import { ServerContext } from 'app/app';
import { RestClient, MsaRestClient } from 'server/rest';
import { HOST_WEBSOCKET } from './websocket';

declare global {
  interface Window { DATATP_CONFIG: any; }
}
export interface ConfigModel {
  environment: 'prod' | 'dev';
  mode: 'host' | 'embedded';
  build: string;
  hosting: { domain: string };
  uiServerUrl: string;
  serverUrl: string;
  restUrl: string;
  apiUrl: string;
  wsUrl: string;
  msaServerUrl: string;
}

class HostAppConfig {
  tenant: string = 'default';
  model: ConfigModel;
  devMode: boolean;
  //theme = 'light';

  constructor(model: ConfigModel) {
    this.model = model;
    this.devMode = model.environment == 'dev';
    let hostname = window.location.hostname;
    if (hostname.endsWith('.' + model.hosting.domain)) {
      this.tenant = hostname.substring(0, hostname.indexOf('.'));
    }
  }

  getModel() { return this.model; }

  getTenant() { return this.tenant; }

  isEmbeddedMode() { return model.mode == 'embedded'; }

  isDevEnvirontment() { return this.getModel().environment === 'dev'; }

  getServerUrl() { return this.model.serverUrl; }

  getApiUrl() { return this.model.apiUrl; }

  getUIServerUrl() { return this.model.uiServerUrl; }

  getWebSocketUrl() { return this.model.wsUrl; }

  createServerLink(path: string) {
    if (this.devMode) return this.model.serverUrl + path;
    return path;
  }

  createServerContext() {
    let rest = new RestClient(this.model.serverUrl, this.model.restUrl, 'datatp')
    HOST_WEBSOCKET.wsUrl = this.model.wsUrl;
    let serverCtx =
      new ServerContext(model.hosting.domain, rest, HOST_WEBSOCKET)
        .withMsaRestClient(new MsaRestClient(this.model.msaServerUrl))
    return serverCtx;
  }

  createRestClient() {
    let rest = new RestClient(this.model.serverUrl, this.model.restUrl, 'datatp')
    return rest;
  }
}

let serverUrl = window.location.origin;
let restUrl = `${serverUrl}/rest/v1.0.0`;
let apiUrl = `${serverUrl}/api`;
let environment: 'dev' | 'prod' = 'prod';
let msaServerUrl = 'https://msa.beelogistics.cloud';

let model: ConfigModel = {
  environment: environment,
  mode: 'host',
  build: 'latest',
  hosting: {
    domain: 'datatp.net'
  },
  uiServerUrl: serverUrl,
  serverUrl: serverUrl,
  restUrl: restUrl,
  apiUrl: apiUrl,
  wsUrl: serverUrl,
  msaServerUrl: msaServerUrl
}

if (window.DATATP_CONFIG) {
  model = { ...model, ...window.DATATP_CONFIG }
  model.wsUrl = `${model.serverUrl}/websocket/channel`;
  model.wsUrl = model.wsUrl.replace('http:', 'ws:');
  model.wsUrl = model.wsUrl.replace('https:', 'wss:');
  console.log(model)
  window.DATATP_CONFIG = null;
}

let CONFIG = new HostAppConfig(model);

export { CONFIG };
