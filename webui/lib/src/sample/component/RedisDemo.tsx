import React from "react";

import * as io from "../../util/io";
import * as bs from "../../wbootstrap/core";
import * as server from "../../server";
import * as app from "../../app";
import { MsaRestClient } from "../../server/rest";

class Log {
  message: string;
  data: any;

  constructor(message: string, data?: any) {
    this.message = message;
    this.data = data;
  }
}

export class RedisDemo extends app.AppComponent {
  logs: Array<Log> = [];
  REGION = 'user:session';

  msaHello = () => {
    let msaClient = new MsaRestClient("https://msa.beelogistics.cloud");
    let rpcPath = '/service/call';
    let userParams = {
      "fname": 'Tuan',
      "lname": 'Nguyen'
    }
    let callParams = { 'service': 'HelloHttpService', 'endpoint': 'hello', 'userParams': userParams }
    let successCB = (resp: server.BackendResponse) => {
      console.log(resp)
    }
    msaClient.post(rpcPath, callParams, successCB);
  }

  hGetAll = () => {
    let { appContext } = this.props;
    let restClient = appContext.getServerContext().getMsaRestClient();
    new server.MsaRedisCall(restClient, 'user')
      .withSuccessData((result: any) => {
        this.logs.push(new Log('Record In Redis', result));
        this.forceUpdate();
      })
      .hGetAll(this.REGION);
  }

  onClear = () => {
    this.logs = [];
    this.forceUpdate();
  }

  hSet = () => {
    let { appContext } = this.props;
    let restClient = appContext.getServerContext().getMsaRestClient();
    let mapping = {
      'firstName': 'Tuan',
      'lastName': 'Nguyen',
    }
    new server.MsaRedisCall(restClient, 'user')
      .withSuccessData((result: any) => {
        this.logs.push(new Log('Init', result));
        this.forceUpdate();
        this.hGetAll();
      })
      .hSet(this.REGION, mapping, 10);
  }

  hSetPartial = () => {
    let { appContext } = this.props;
    let restClient = appContext.getServerContext().getMsaRestClient();
    let mapping = {
      'firstName': 'Tuan - Update',
    }
    new server.MsaRedisCall(restClient, 'user')
      .withSuccessData((result: any) => {
        this.logs.push(new Log('Init', result));
        this.forceUpdate();
        this.hGetAll();
      })
      .hSet(this.REGION, mapping);
  }

  hDel = () => {
    let { appContext } = this.props;
    let restClient = appContext.getServerContext().getMsaRestClient();
    new server.MsaRedisCall(restClient, 'user')
      .withSuccessData((result: any) => {
        this.logs.push(new Log('Delete', result));
        this.forceUpdate();
        this.hGetAll();
      })
      .hDel(this.REGION, ['firstName']);
  }

  doObjectGet(name: string, encryptedKey?: string) {
    let { appContext } = this.props;
    let restClient = appContext.getServerContext().getMsaRestClient();
    new server.MsaRedisCall(restClient, 'user')
      .withSuccessData((objectData: io.ObjectData) => {
        this.logs.push(new Log('Test objectGet', objectData));
        this.logs.push(new Log('ObjectData Content', io.getObjectDataContent(objectData)));
        this.forceUpdate();
      })
      .objectGet('hello-storage', name, encryptedKey);
  }

  objectGet = () => {
    this.doObjectGet('test-file.txt');
  }

  objectSet = () => {
    let { appContext } = this.props;
    let restClient = appContext.getServerContext().getMsaRestClient();
    let data = {
      'firstName': 'Tuan',
      'lastName': 'Nguyen'
    }
    let fileData: io.ObjectData = io.createObjectData('test-file.txt', data);
    new server.MsaRedisCall(restClient, 'user')
      .withSuccessData((result: any) => {
        this.logs.push(new Log('Test objectSet', result));
        this.forceUpdate();
        this.doObjectGet('n/a', result['encryptedKey']);
      })
      .objectSet('hello-storage', fileData, 10);

  }

  renderConsole() {
    let logElements: Array<any> = []
    for (let log of this.logs) {
      logElements.push(
        <div>{log.message}</div>
      );
      if (log.data) {
        logElements.push(
          <div>
            <div>Result:</div>
            <pre>
              {JSON.stringify(log.data, undefined, 2)}
            </pre>
          </div>
        );
      }
    }
    return logElements;
  }

  render() {
    let html = (
      <div className="flex-vbox">
        <bs.Toolbar>
          <bs.Button laf="primary" onClick={this.msaHello}>MSA Hello</bs.Button>
          <div className="mx-2"></div>
          <bs.Button laf="primary" onClick={this.objectGet}>objectGet</bs.Button>
          <bs.Button laf="primary" onClick={this.objectSet}>objectSet</bs.Button>
          <div className="mx-2"></div>
          <bs.Button laf="primary" onClick={this.hGetAll}>hGetAll</bs.Button>
          <bs.Button laf="primary" onClick={this.hSet}>hSet</bs.Button>
          <bs.Button laf="primary" onClick={this.hSetPartial}>hSet Partial</bs.Button>
          <bs.Button laf="primary" onClick={this.hDel}>hDel</bs.Button>
          <bs.Button laf="primary" onClick={this.onClear}>Clear</bs.Button>
        </bs.Toolbar>
        <bs.GreedyScrollable key={this.componentId} className="flex-vbox">
          {this.renderConsole()}
        </bs.GreedyScrollable>
      </div>
    );
    return html;
  }
}