import React from "react";
import * as icon from 'react-feather';
import { app } from '@datatp-ui/lib';
import { UIJobTrackingProjectList, UIJobTrackingProjectListPlugin } from "./config/UIJobTrackingProjectList";
import { UIJobTrackingClaimList, UIJobTrackingClaimListPlugin } from "./claim/UIJobTrackingClaimList";
import space = app.space;

class JobTrackingSpacePlugin extends space.SpacePlugin {
  constructor() {
    super('job-tracking/job-tracking', 'Job Tracking Navigation');
  }

  override createUserScreens(): space.ScreenConfig[] {
    let configs: space.ScreenConfig[] = [
      {
        id: "job-tracking", label: "Job Tracking", icon: icon.Table,
        checkPermission: {
          feature: { module: 'job-tracking', name: 'job-tracking' },
          requiredCapability: app.READ,
        },
        renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (<UIJobTrackingProjectList appContext={appCtx} pageContext={pageCtx}
            plugin={new UIJobTrackingProjectListPlugin()} />);
        },
        screens: [
          {
            id: "job-tracking", label: "Job Tracking", icon: icon.Table,
            checkPermission: {
              feature: { module: 'job-tracking', name: 'job-tracking' },
              requiredCapability: app.READ,
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return <UIJobTrackingClaimList appContext={appCtx} pageContext={pageCtx}
                plugin={new UIJobTrackingClaimListPlugin()} />
            }
          },
          {
            id: "job-tracking-claims", label: "Job Tracking Claims", icon: icon.Table,
            checkPermission: {
              feature: { module: 'job-tracking', name: 'job-tracking' },
              requiredCapability: app.READ,
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return <UIJobTrackingClaimList appContext={appCtx} pageContext={pageCtx}
                plugin={new UIJobTrackingClaimListPlugin()} />
            }
          }
        ]
      },
    ]
    return configs;
  }

  createCompanyScreens(): space.ScreenConfig[] {
    let configs: space.ScreenConfig[] = [
      {
        id: "job-tracking", label: "Job Tracking", icon: icon.Table,
        checkPermission: {
          feature: { module: 'job-tracking', name: 'job-tracking' },
          requiredCapability: app.READ,
        },
        renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (<UIJobTrackingProjectList appContext={appCtx} pageContext={pageCtx}
            plugin={new UIJobTrackingProjectListPlugin()} />);
        },
        screens: [
          {
            id: "job-tracking-claims", label: "Job Tracking Claims", icon: icon.Table,
            checkPermission: {
              feature: { module: 'job-tracking', name: 'job-tracking' },
              requiredCapability: app.READ,
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return <UIJobTrackingClaimList appContext={appCtx} pageContext={pageCtx}
                plugin={new UIJobTrackingClaimListPlugin()} />
            }
          }
        ]
      },
    ]
    return configs;
  }
}

export function init() {
  space.SpacePluginManager.register(new JobTrackingSpacePlugin());
}