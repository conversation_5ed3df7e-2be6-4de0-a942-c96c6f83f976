import React from 'react';
import { grid, entity, input, sql } from '@datatp-ui/lib';
import { T } from '../tms/backend';
import * as FeatherIcon from 'react-feather';
import { BBRefTransporter } from '../tms/transport/BBRefTransporter';
import { BBRefVehicle } from '../tms/vehicle/BBRefVehicle';
import { BBRefVehicleFleet } from '../tms/vehicle/BBRefVehicleFleet';

export class UITMSVendorBillTrackingList extends entity.VGridEntityListEditor {
  componentDidMount() {
    const { plugin } = this.props;
    const records = plugin.getModel().getRecords();
    if (records.length === 0) {
      this.onNewAction();
    }
  }
  createVGridConfig() {
    const { pageContext } = this.props;
    let onInputChange = (fieldCtx: grid.FieldContext, oldVal: any, newVal: any) => {
      let { fieldConfig, displayRecord, gridContext } = fieldCtx
      let event: grid.VGridCellEvent = {
        row: displayRecord.row, field: fieldConfig, event: 'Modified', data: displayRecord
      }
      gridContext.broadcastCellEvent(event);
    };

    let writeCap = pageContext.hasUserWriteCapability();
    let config: grid.VGridConfig = {
      record: {
        editor: {
          supportViewMode: ['table'],
          enable: true
        },
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          {
            name: 'transportType', label: T('Transport Type'), width: 150, state: { showRecordState: true, visible: false },

            editor: {
              type: 'string',
              onInputChange: onInputChange
            },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row === cell.getRow()) {
                  cell.forceUpdate()
                }
              },
            },
          },
          {
            name: 'vehicleType', label: T('Vehicle Type'), state: { visible: false },
            editor: {
              type: 'string',
              onInputChange: onInputChange
            }
          },
          {
            name: 'licensePlate', label: T('License Plate'), width: 150,
            editor: {
              type: 'string',
              onInputChange: onInputChange
            }
          },
          {
            name: 'driverFullName', label: T('Full Name'), width: 150,
            editor: {
              type: 'string',
              onInputChange: onInputChange
            }
          },
          {
            name: 'driverMobile', label: T('Mobile'), width: 150,
            editor: {
              type: 'string',
              onInputChange: onInputChange
            }
          },
          {
            name: 'driverIdentificationNo', label: T('Identification No'), width: 150,
            editor: {
              type: 'string',
              onInputChange: onInputChange
            }
          },
          {
            name: 'fixed', label: T('Fixed'), state: { visible: false },
            editor: {
              type: 'double',
              renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                let { fieldConfig, displayRecord, tabIndex, focus } = ctx;
                let vendorBill = displayRecord.record;
                return (
                  <input.BBCurrencyField
                    bean={vendorBill} field={fieldConfig.name} focus={focus} tabIndex={tabIndex} disable={!writeCap} onInputChange={onInputChange} />
                )
              },
              onInputChange: onInputChange
            }
          },
          {
            name: 'extra', label: T('Extra'), state: { visible: false },
            editor: {
              type: 'double',
              renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                let { fieldConfig, displayRecord, tabIndex, focus } = ctx;
                let vendorBill = displayRecord.record;
                return (
                  <input.BBCurrencyField
                    bean={vendorBill} field={fieldConfig.name} focus={focus} tabIndex={tabIndex} disable={!writeCap} onInputChange={onInputChange} />
                )
              },
              onInputChange: onInputChange
            }
          },
          {
            name: 'cost', label: T('Cost'), state: { visible: false },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow() && (event.field.name === 'fixed' || event.field.name === 'extra')) {
                  let vendorBillTracking = cell.getDisplayRecord().record;
                  vendorBillTracking.cost = vendorBillTracking.fixed + vendorBillTracking.extra;
                  cell.forceUpdate();
                }
              },
            },
          },
          {
            name: 'description', label: T('Tracking Note'), width: 200,
            editor: {
              type: 'text',
              onInputChange: onInputChange
            }
          },
        ],
        fieldGroups: {
          'Driver': {
            label: T('Driver'),
            visible: true,
            fields: [
              'driverFullName', 'driverMobile', 'driverIdentificationNo'
            ]
          },
          'Cost': {
            label: T('Cost'),
            visible: true,
            fields: [
              'fixed', 'extra',
              'cost',
            ]
          },
        },
        computeDataRowHeight: (_ctx: grid.VGridContext, dRec: grid.DisplayRecord) => {
          return 35;
        },
        control: {
          width: 25,
          items: [
            {
              name: 'copy', hint: 'Copy', icon: FeatherIcon.Copy,
              onClick: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
                let vendorBillTracking = dRecord.record;
                vendorBillTracking = { ...vendorBillTracking, id: null };
                ctx.model.insertDisplayRecordAt(dRecord.row, vendorBillTracking);
                grid.getRecordState(vendorBillTracking).markNew(true);
                ctx.getVGrid().forceUpdateView();
              },
            },
          ]
        }
      },
      toolbar: {
        actions: [
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_DELETE(!writeCap, T('Delete')),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_ADD(!writeCap, T('Add')),
        ],
      },
      view: {
        currentViewName: 'grid',
        availables: {
          table: {
            viewMode: 'table'
          },
          grid: {
            viewMode: 'grid',
            rowHeight: 380,
            column: 1,
            renderRecord: (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
              const uiList = _ctx.uiRoot as entity.VGridEntityListEditor;
              const { appContext, pageContext } = uiList.props;
              let trip = dRecord.record;
              const writeCap = pageContext.hasUserWriteCapability();
              return (
                <div className={`flex-grow-0 p-2 ${trip.licensePlate ? 'bg-success' : 'bg-danger'} bg-opacity-10 `}>
                  <BBRefVehicleFleet
                    appContext={appContext} pageContext={pageContext} disable
                    bean={trip} beanIdField={'vendorId'} beanLabelField={'vendorFullName'} label='Fleet' placeholder={'Fleet'}
                  />
                  <BBRefVehicle
                    appContext={appContext} pageContext={pageContext} disable={!writeCap}
                    allowUserInput placeholder='License plate'
                    placement="bottom-start" offset={[0, 5]} minWidth={500}
                    bean={trip} label={T('License plate')} beanIdField={'vehicleId'} beanLabelField={'licensePlate'}
                    loadParams={(searchParams: sql.SqlSearchParams) => {
                      searchParams.params = {
                        ...searchParams.params,
                        // 'vehicleFleetId': trip['fleetId']
                      };
                    }}
                    onPostUpdate={(_inputUI, _bean, selectOpt, _userInput) => {
                      if (selectOpt) {
                        if (selectOpt['vehicleFleetId']) {
                          trip['fleetId'] = selectOpt['vehicleFleetId'];
                          trip['fleetLabel'] = selectOpt['fleetName'];
                        }
                        trip['driverIdentificationNo'] = selectOpt['idCard'];
                        trip['driverMobile'] = selectOpt['mobile'];
                        trip['driverFullName'] = selectOpt['transporterFullName'];
                        trip['driverId'] = selectOpt['transporterId'];
                      }
                      this.forceUpdate();
                    }}
                  />
                  <BBRefTransporter
                    disable={!writeCap}
                    key={`driver-${trip.driverId}`}
                    label={T('Driver')}
                    appContext={appContext} pageContext={pageContext}
                    minWidth={300} allowUserInput
                    placement="left" placeholder="Enter Transporter"
                    bean={trip} beanIdField={'driverId'}
                    beanLabelField={'driverFullName'} refTransporterBy={'id'} onPostUpdate={(inputUI, bean, option, userInput) => {
                      if (option) {
                        bean['driverMobile'] = option['mobile'];
                        bean['driverIdentificationNo'] = option['idCard']
                      }
                      this.forceUpdate();
                    }} />
                  <input.BBStringField bean={trip} label={T('Mobile')} field={'driverMobile'} disable={!writeCap} />
                  <input.BBStringField bean={trip} label={T('ID')} field={'driverIdentificationNo'} disable={!writeCap} />
                  <input.BBTextField bean={trip} field='description' label='Note' disable={!writeCap} />
                </div>
              );
            }
          }
        },

      },

    }
    return config;
  }

  onNewAction() {
    let { plugin } = this.props;
    let newRecord = {};
    let records = plugin.getModel().getRecords();
    records.unshift(newRecord);
    plugin.replaceBeans(records);
    grid.getRecordState(newRecord).markNew();
    this.vgridContext.getVGrid().forceUpdateView();
  }
}

