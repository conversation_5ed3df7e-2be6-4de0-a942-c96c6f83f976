import React, { Component } from 'react';
import { bs, sql, grid, util, app, input, entity } from '@datatp-ui/lib';
import { module } from "@datatp-ui/erp";
import { T } from '../tms/backend';
import * as FeatherIcon from 'react-feather';
import { TMSOperationsStatus, TMSVendorBillStatus, VendorCostStatus } from '../tms/models';
import { UITMSVendorBillTrackingList } from './UITMSVendorBillTracking';
import { TMSBillTransportationModeTools, TMSUtils, UIVendorAttachments } from '../tms/utils';
import { BBRefVehicleFleet } from '../tms/vehicle/BBRefVehicleFleet';
import { UITMSBillUtils } from '../tms/bill/UITMSBillUtils';
import { BBRefVehicle } from '../tms/vehicle/BBRefVehicle';
import { BBRefTransporter } from '../tms/transport/BBRefTransporter';
import { TMSVendorBillExportList } from './TMSVendorBillExport';
import { ManagementRestURL } from '../tms/RestURL';
import { fleet } from '..';
// import { WrapText } from '../tms/vehicle/tracking/UIVehicleTripGoodsTrackingListBase';

const SERVICE_SEARCH = 'TMSVendorBillService';
const METHOD_SEARCH = 'searchTMSVendorBills';
let wrapText = false;


const compactDateTime = (val: string) => {
  if (!val) return;
  let date = util.TimeUtil.parseCompactDateTimeFormat(val);
  return util.text.formater.date(date);
}

interface WrapTextProps extends grid.VGridContextProps {
  checked: boolean;
  onChange(val: any): void;
}
export class WrapText extends Component<WrapTextProps> {
  render(): React.ReactNode {
    let { onChange, checked } = this.props;
    return (
      <div className='flex-hbox mx-1 flex-grow-0 flex-hbox align-items-center'>
        <div className="form-check form-switch py-0 m-0">
          <input className="form-check-input p-0" type="checkbox" style={{ cursor: 'pointer' }}
            role="switch" id="rawDataToggle" checked={checked}
            onChange={(_event: any) => {
              onChange(_event.target.checked);
            }}
          />
        </div>
        <label className="form-check-label fs--1 d-flex align-items-center" htmlFor="rawDataToggle" >
          <span>{T('Wrap Text')}</span>
        </label>
      </div>
    )
  }
}

export class UITMSVendorBillNotLinkTMSBillPlugin extends entity.DbEntityListPlugin {
  constructor() {
    super();
    this.backend = {
      context: 'company',
      service: 'TMSVendorBillService',
      searchMethod: 'searchTMSVendorBillNotLinkTMSBills'
    }

    this.searchParams = {
      params: {},
      filters: [
        ...sql.createSearchFilter()
      ],
      "rangeFilters": [
        {
          "name": "delivery_plan", "label": "Date", "type": "DATE", "required": true,
          "fromValue": null, "toValue": null
        }
      ],
      optionFilters: [
        sql.createStorageStateFilter([entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED])
      ],
      maxReturn: 5000,
    }
  }

  loadData(uiList: entity.DbEntityList<any>) {
    let params = { 'params': this.searchParams }
    this.createBackendSearch(uiList, params).call();
  }

}
export class UITMSVendorBillPlugin extends entity.DbEntityListPlugin {
  constructor() {
    super();

    this.backend = {
      context: 'company',
      service: 'TMSVendorBillService',
      searchMethod: 'searchTMSVendorBills'
    }

    this.searchParams = {
      params: {},
      filters: [
        ...sql.createSearchFilter()
      ],
      "rangeFilters": [
        {
          "name": "deliveryPlan", "label": "Date", "type": "DATE", "required": true,
          "fromValue": null, "toValue": null
        }
      ],
      optionFilters: [
        sql.createStorageStateFilter([entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED])
      ],
      maxReturn: 1000,
    }
  }

  withDataScope = (dataScope?: app.AppDataScope) => {
    this.addSearchParam("dataScope", dataScope?.scope);
    return this;
  }

  withIds(ids: Array<any>) {
    this.addSearchParam("ids", ids);
    return this;
  }

  loadData(uiList: entity.DbEntityList<any>) {
    let params = { 'params': this.searchParams }
    this.createBackendSearch(uiList, params).call();
  }

}

const containerFiles: any[] = ['containerNo', 'sealNo'];
const truckFiles: any[] = ['licensePlate', 'driverFullName', 'driverMobile', 'driverIdentificationNo'];
const costFiles: any[] = ['fixed', 'extra'];

export interface BillInfoSummaryProps extends app.AppComponentProps {
  bill: any
}

class BillInfoSummary extends app.AppComponent<BillInfoSummaryProps> {
  createUIVehicleInfo(coordinator: any, licensePlate: string, driverFullName: string, driverMobile: string, status?: string) {
    return (
      <div>
        <div>
          <div className='flex-hbox'>
            <label className="fw-lighter pe-1">{T('License Plate')}: </label>
            <span className="text-info">{licensePlate ? licensePlate : '...'}</span>
          </div>
        </div>
        <div className='flex-hbox justify-content-between'>
          <div className='flex-hbox'>
            <label className="fw-lighter pe-1">{T(`Driver's Name`)}: </label>
            <span className="text-info">{driverFullName ? driverFullName : '...'}</span>
          </div>
          <div className='flex-hbox'>
            <label className="fw-lighter pe-1">{T(`Driver's Mobile`)}: </label>
            <span className="text-info">{driverMobile ? driverMobile : '...'}</span>
          </div>
        </div>
      </div>
    )
  }

  createVehicleInfo() {
    let vehicleInfo: Array<any> = [];
    let { bill } = this.props;
    let trackings = bill.trackings;
    let vendorBills = bill.vendorBills;
    if (trackings) {
      for (let tracking of trackings) {
        vehicleInfo.push(
          this.createUIVehicleInfo(tracking.coordinatorFullName, tracking.licensePlate, tracking.driverFullName, tracking.driverMobile, tracking.status)
        )
      }
      return vehicleInfo;
    }
    if (vendorBills) {
      for (let vendorBill of vendorBills) {
        if (!vendorBill['licensePlate']) continue;
        this.createUIVehicleInfo(null, vendorBill.licensePlate, vendorBill.driverFullName, vendorBill.driverMobile)
      }
      return vehicleInfo;
    }
  }

  render() {
    let { bill } = this.props;
    let mode = bill.mode;
    let status = bill['status']
    return (
      <div className={'flex-vbox px-1'}>
        <div className='flex-hbox align-items-center'>
          <label className="fw-lighter pe-1">{T('File')}: </label>
          <span className={`px-1 text-info `}>{bill.label}</span>
          |
          <span className={`px-1 ${TMSBillTransportationModeTools.getColor(mode)}`}>
            {TMSBillTransportationModeTools.getLabel(mode)}
          </span>
          <span className="ms-auto">
            {bill.coordinator &&
              <div className='flex-hbox'>
                <label className="fw-lighter pe-1">{T(`Coordinator`)}: </label>
                <span className="text-info">{bill.coordinator}</span>
              </div>
            }
            {status &&
              <div className='flex-hbox'>
                <bs.Badge laf={`${status == 'REJECT' ? 'danger' : 'success'}`}>
                  {status}
                </bs.Badge>
              </div>
            }
          </span>
        </div>
        <div className='flex-hbox'>
          <label className="fw-lighter pe-1">{T('Customer')}: </label>
          <span className="text-info">{bill.customerFullName}</span>
        </div>
        <div className='flex-hbox'>
          <label className="fw-lighter pe-1">{T('House Bill')}: </label>
          <span className="text-info">{bill.hwbNo}</span>
        </div>
        <div className='flex-hbox'>
          <label className="fw-lighter pe-1">{T('Time')}: </label>
          <span className="text-info">
            {util.text.formater.compactDate(bill.deliveryPlan)} {bill.time}
          </span>
        </div>
        <div className='flex-hbox'>
          <label className="fw-lighter pe-1">{T('Container No')}: </label>
          <span className="text-info">
            {bill.tmsContainerNo}
          </span>
        </div>
        <div className='flex-hbox'>
          <label className="fw-lighter pe-1">{T('Seal No')}: </label>
          <span className="text-info">
            {bill.tmsSealNo}
          </span>
        </div>
        <div className='flex-hbox'>
          <label className="fw-lighter pe-1">{T('Booking')}: </label>
          <span className="text-info">
            {bill.bookingCode}
          </span>
        </div>
        <hr className='my-1' />
        <div className="d-flex">
          <label className="fw-lighter pe-1" style={{ whiteSpace: 'nowrap' }}>
            {T('Pickup Address')}:
          </label>
          <span className="text-info text-break">
            {bill.pickupAddress}
          </span>
        </div>
        <div className="d-flex">
          <label className="fw-lighter pe-1" style={{ whiteSpace: 'nowrap' }}>
            {T('Delivery Address')}:
          </label>
          <span className="text-info text-break">
            {bill.deliveryAddress}
          </span>
        </div>
        <div className='flex-hbox'>
          <label className="fw-lighter pe-1">{T('Notes')}: </label>
          <span className="text-info">
            {bill.tmsBillDescription}
          </span>
        </div>
        <hr className='my-1' />
        <div className='flex-hbox'>
          <label className="fw-lighter pe-1">{T('Vendor')}: </label>
          <span className="text-info">{bill.vendorFullName}</span>
        </div>
        {this.createVehicleInfo()}
        <hr className='my-1' />
        <div className='flex-hbox justify-content-between bg-info bg-opacity-25 py-1'>
          <div>
            <FeatherIcon.DollarSign size={18} className='text-danger' />
            <label className="fw-lighter pe-1">{T('Fixed')}: </label>
            <span className="text-info">{util.text.formater.currency(bill.fixed, 0)}</span>
          </div>
          <div>
            <label className="fw-lighter pe-1">{T('Extra')}: </label>
            <span className="text-info">{util.text.formater.currency(bill.extra, 0)}</span>
          </div>
          <div>
            <label className="fw-lighter pe-1">{T('Cost')}: </label>
            <span className="text-info">{util.text.formater.currency(bill.cost, 0)}</span>
          </div>
        </div>
      </div>
    )
  }
}
interface UITMSVendorBillListProps extends entity.DbEntityListProps {
  viewMode?: any;
}
export class UITMSVendorBillList extends entity.DbEntityList<UITMSVendorBillListProps> {
  translate: Record<string, string> = {
    'EXPORT': 'XUẤT',
    'IMPORT': 'NHÂP',
  }
  config: any = {
    containerEmpty: false,
    truckEmpty: false,
    costEmpty: false,
  };

  onInputChange(fieldCtx: grid.FieldContext, oldVal: any, newVal: any) {
    let { displayRecord, fieldConfig, gridContext } = fieldCtx;
    let vendorBill = displayRecord.record;
    let fieldName = fieldConfig.name;
    if (containerFiles.includes(fieldName)) vendorBill['updateContainer'] = true;
    if (truckFiles.includes(fieldName)) vendorBill['updateTruckInfo'] = true;
    if (costFiles.includes(fieldName)) vendorBill['updateCosting'] = true;

    const onCalculateDelayedTime = (vendorBill: any) => {
      let sDeliveryPlan = vendorBill['deliveryPlan'];
      let sEstimateTime = vendorBill['estimateTime'];
      if (sDeliveryPlan && sEstimateTime) {
        let deliveryPlan = util.TimeUtil.parseCompactDateTimeFormat(sDeliveryPlan);
        let estimateDateTime = util.TimeUtil.parseCompactDateTimeFormat(sEstimateTime);
        let timeRange: util.TimeRange = new util.TimeRange();
        timeRange.fromSetDate(deliveryPlan);
        timeRange.toSetDate(estimateDateTime);
        let delayedTime = timeRange.diff("minutes");
        vendorBill['delayedTime'] = delayedTime;
      }
    }

    const onCalculateArrivedOnTime = (vendorBill: any) => {
      if (vendorBill['delayedTime'] === null) return
      if (vendorBill['delayedTime'] <= 0) {
        vendorBill['vehicleArrivedOnTime'] = 2;
      } else if (vendorBill['delayedTime'] <= 30) {
        vendorBill['vehicleArrivedOnTime'] = 1;
      } else {
        vendorBill['vehicleArrivedOnTime'] = 0;
      }
    }

    let event: grid.VGridCellEvent = {
      row: displayRecord.row, field: fieldConfig, event: 'Modified', data: displayRecord
    }

    if (fieldConfig.name === 'estimateTime') {
      vendorBill['updateBillRating'] = true;
      onCalculateDelayedTime(vendorBill);
      onCalculateArrivedOnTime(vendorBill);
    }
    gridContext.broadcastCellEvent(event);
  };

  buildTruckCol(): grid.FieldConfig[] {
    let { appContext, pageContext } = this.props;
    let configs: grid.FieldConfig[] = [
      {
        name: 'licensePlate', label: T('Biển Số'), width: 175,
        customHeaderRender(_ctx, _field, headerEle) {
          return (
            <div className='flex-hbox'>
              <FeatherIcon.Edit size={12} className='text-warning m-1' />
              {headerEle}
            </div>
          )
        },
        editor: {
          type: 'string',
          onInputChange: this.onInputChange,
          renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
            const { displayRecord, fieldConfig, tabIndex, focus } = ctx;
            let record = displayRecord.record;
            if (!record['trackings']) record['trackings'] = [{ 'vendorBillId': record['id'] }];
            let trackings: Array<any> = record['trackings'];
            let btnActions = (tracking: any) => {
              let index = trackings.indexOf(tracking);
              return (
                <div className='flex-hbox'>
                  <bs.Button laf='link' className={`${tracking['editState'] == 'DELETED' ? 'text-danger' : 'text-info'}`}
                    onClick={() => {
                      if (!tracking.id) {
                        trackings.splice(index, 1);
                      } else {
                        if (tracking['editState'] == 'DELETED') {
                          tracking['editState'] = 'MODIFIED'
                        } else {
                          tracking['editState'] = 'DELETED'
                        }
                      }
                      displayRecord.getRecordState().markModified();
                      ctx.gridContext.getVGrid().forceUpdateView();
                    }}>
                    <FeatherIcon.Trash2 size={12} />
                  </bs.Button>
                  {/* <div style={{ width: 15 }}> */}
                  {index == trackings.length - 1 ?
                    <bs.Button laf='link' onClick={() => {
                      trackings.push({
                        'vendorBillId': record['id']
                      });
                      ctx.gridContext.getVGrid().forceUpdateView(true);
                    }}>
                      <FeatherIcon.Plus size={12} />
                    </bs.Button>
                    : null
                  }
                  {/* </div> */}
                </div>
              )
            }
            let contents = trackings.map(sel => {
              return (
                <div className='flex-hbox'>
                  {btnActions(sel)}
                  <BBRefVehicle style={{ height: 40 }} tabIndex={tabIndex} autofocus={focus}
                    appContext={appContext} pageContext={pageContext} placeholder='Vehicle'
                    allowUserInput minWidth={500}
                    bean={sel} beanIdField={'vehicleId'} beanLabelField={fieldConfig.name}
                    onPostUpdate={(_inputUI: React.Component, bean: any, selectOpt: any, userInput: string) => {
                      sel['editState'] = 'MODIFIED';
                      if (selectOpt && selectOpt.id) {
                        sel['driverFullName'] = selectOpt['transporterFullName'];
                        sel['driverId'] = selectOpt['transporterId'];
                        sel['driverMobile'] = selectOpt['mobile'];
                        sel['driverIdentificationNo'] = selectOpt['idCard'];
                      }
                      onInputChange(bean, fieldConfig.name, null, bean[fieldConfig.name])
                    }}
                  />
                </div>
              )
            });
            return (
              <div className='flex-vbox'>
                {contents}
              </div>
            );
          },
        }
      },
      {
        name: 'driverFullName', label: T('Lái Xe'), width: 150,
        customHeaderRender(_ctx, _field, headerEle) {
          return (
            <div className='flex-hbox'>
              <FeatherIcon.Edit size={12} className='text-warning m-1' />
              {headerEle}
            </div>
          )
        },
        listener: {
          onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            if (event.row == cell.getRow() && event.field.name === 'licensePlate') {
              cell.forceUpdate()
            }
          },
        },
        editor: {
          type: 'string',
          onInputChange: this.onInputChange,
          renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
            const { displayRecord, fieldConfig, tabIndex, focus } = ctx;
            let record = displayRecord.record;
            if (!record['trackings']) record['trackings'] = [{}];
            let trackings: Array<any> = record['trackings'];
            let contents = trackings.map(sel => {
              return (
                <BBRefTransporter style={{ height: 40 }}
                  tabIndex={tabIndex} autofocus={focus}
                  allowUserInput
                  placeholder='Enter Transporter' key={`driver${util.IDTracker.next()}`}
                  bean={sel} beanIdField={"driverId"} beanLabelField={fieldConfig.name}
                  appContext={appContext} pageContext={pageContext} refTransporterBy={'id'}
                  onPostUpdate={(_inputUI: React.Component, bean: any, selectOpt: any, userInput: string) => {
                    sel['editState'] = 'MODIFIED';
                    if (selectOpt && selectOpt.id) {
                      sel['driverMobile'] = selectOpt['mobile'];
                      sel['driverIdentificationNo'] = selectOpt['idCard'];
                    }
                    onInputChange(bean, fieldConfig.name, null, bean[fieldConfig.name])
                  }}
                />
              )
            });
            return (
              <div className='flex-vbox'>
                {contents}
              </div>
            );
          },
        }
      },
      {
        name: 'driverMobile', label: T('Mobile'),
        customHeaderRender(_ctx, _field, headerEle) {
          return (
            <div className='flex-hbox'>
              <FeatherIcon.Edit size={12} className='text-warning m-1' />
              {headerEle}
            </div>
          )
        },
        listener: {
          onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            if (event.row == cell.getRow() && event.field.name === 'licensePlate' || event.field.name === 'driverFullName') {
              cell.forceUpdate()
            }
          },
        },
        editor: {
          type: 'string',
          onInputChange: this.onInputChange,
          renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
            const { displayRecord, fieldConfig, tabIndex, focus } = ctx;
            let record = displayRecord.record;
            if (!record['trackings']) record['trackings'] = [{}];
            let trackings: Array<any> = record['trackings'];
            let contents = trackings.map(sel => {
              return (
                <input.BBStringField style={{ height: 40 }} tabIndex={tabIndex} focus={focus}
                  bean={sel} field={fieldConfig.name}
                  onInputChange={(bean: any, field: string, oldVal: any, newVal: any) => {
                    sel['editState'] = 'MODIFIED';
                    onInputChange(bean, field, oldVal, newVal);
                  }} />
              )
            });
            return (
              <div className='flex-vbox'>
                {contents}
              </div>
            );
          },
        }
      },
      {
        name: 'driverIdentificationNo', label: T('CCCD'),
        customHeaderRender(_ctx, _field, headerEle) {
          return (
            <div className='flex-hbox'>
              <FeatherIcon.Edit size={12} className='text-warning m-1' />
              {headerEle}
            </div>
          )
        },
        listener: {
          onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            if (event.row == cell.getRow() && event.field.name === 'licensePlate' || event.field.name === 'driverFullName') {
              cell.forceUpdate()
            }
          },
        },
        editor: {
          type: 'string',
          onInputChange: this.onInputChange,
          renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
            const { displayRecord, fieldConfig, tabIndex, focus } = ctx;
            let record = displayRecord.record;
            if (!record['trackings']) record['trackings'] = [{}];
            let trackings: Array<any> = record['trackings'];
            let contents = trackings.map(sel => {
              return (
                <input.BBStringField style={{ height: 40 }}
                  tabIndex={tabIndex} focus={focus}
                  bean={sel} field={fieldConfig.name}
                  onInputChange={(bean: any, field: string, oldVal: any, newVal: any) => {
                    sel['editState'] = 'MODIFIED';
                    onInputChange(bean, field, oldVal, newVal);
                  }} />
              )
            });
            return (
              <div className='flex-vbox'>
                {contents}
              </div>
            );
          },
        }
      },
    ];
    return configs;
  }

  createVGridConfig() {
    let { plugin, appContext, pageContext, viewMode, type } = this.props;
    const modCap = pageContext.hasUserModeratorCapability();
    let _ = this;
    let addDbSearchFilter = plugin.searchParams ? true : false;
    let writeCap = pageContext.hasUserWriteCapability();
    const hFields: any[] = ['pickupAddress', 'deliveryAddress', 'description', 'tmsBillDescription'];
    const length: number = 26;

    let config: grid.VGridConfig = {
      record: {
        computeDataRowHeight: (_ctx: grid.VGridContext, dRec: grid.DisplayRecord) => {
          let rec = dRec.record;
          const defaultH = 35;
          const maxH = defaultH * 10;
          let autoH = defaultH;
          let trackings: Array<any> = rec['trackings'];
          if (trackings && trackings.length > 1) {
            autoH = defaultH * trackings.length;
          }
          let maxLength = 0;
          for (let field of hFields) {
            let value: string = rec[field];
            if (value && value.length > maxLength) maxLength = value.length;
          }
          // let hf = (maxLength / length) * 16 + 20;
          // if (hf > autoH) {
          //   if (hf < maxH) {
          //     autoH = hf;
          //   } else {
          //     autoH = maxH;
          //   }
          // }
          if (dRec.isDataRecord()) {
            rec['rowHeight'] = autoH;
            return autoH;
          }
          return 20;
        },
        editor: {
          supportViewMode: ['table', 'aggregation'],
          enable: true
        },
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          {
            name: 'fileNo', label: T('Số File'), width: 150, filterableType: 'string', filterable: true,
            state: { showRecordState: true }, container: 'fixed-left',
            computeCssClasses(ctx: grid.VGridContext, dRecord: grid.DisplayRecord) {
              let vendorBill = dRecord.record;
              return vendorBill['tmsBillStorageState'] === entity.StorageState.ARCHIVED ? 'text-danger' : 'text-truncate';
            },
            customRender(ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) {
              let bill = dRecord.record;
              let cssClass = bill['id'] ? 'text-success' : 'text-secondary';
              if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(ctx, dRecord)}`;
              let html = (
                <div className='flex-hbox align-items-center'>
                  < FeatherIcon.Copy className='mx-1 my-2 text-warning' style={{ cursor: 'pointer' }} size={12}
                    onClick={() => {
                      navigator.clipboard.writeText(bill[field.name]);
                      appContext.addOSNotification('success', 'Copy success')
                    }
                    } />
                  <button type="button" className="btn btn-link">
                    <div className={`${cssClass} fw-bold`} onClick={() => {
                      if (bill['id']) {
                        _.onDefaultSelect(dRecord);
                      }
                    }}>{bill[field.name]}</div>
                  </button>
                </div >
              );
              let tooltip = (
                <BillInfoSummary appContext={appContext} pageContext={pageContext} bill={bill} />
              )
              return TMSUtils.renderTooltip(tooltip, html, 500);
            },
          },
          {
            name: 'hwbNo', label: T('House Bill'),
          },
          {
            name: 'customerFullName', label: T('Khách Hàng'),
            width: 170, filterableType: 'Options', filterable: true, container: 'fixed-left',
          },
          {
            name: 'deliveryPlan', label: T('Thời Gian'),
            sortable: true, filterable: true, filterableType: 'date',
            width: 100, format: util.text.formater.compactDate,
            fieldDataGetter(record) {
              return util.text.formater.compactDate(record['deliveryPlan']);
            },
          },
          { name: 'time', label: T('Giờ'), width: 60 },
          {
            name: 'estimateTime', label: T('E.Time'), width: 80, state: { visible: false },
            editor: {
              type: 'date',
              onInputChange: _.onInputChange,
              renderCustom: (fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                let { displayRecord, fieldConfig, tabIndex, focus } = fieldCtx;
                let vendorBill = displayRecord.record;
                return (
                  <input.BBTimeInputMask bean={vendorBill} field={fieldConfig.name} tabIndex={tabIndex} focus={focus}
                    onInputChange={(bean, field, oldVal, newVal) => {
                      if (newVal) {
                        vendorBill['modifiedEstimateTime'] = util.TimeUtil.toCompactDateTimeFormat(new Date());
                      } else {
                        vendorBill['modifiedEstimateTime'] = null;
                      }
                      if (onInputChange) onInputChange(bean, field, oldVal, newVal);
                    }}
                  />
                )
              },
            }
          },
          {
            name: 'mode', label: T('Loại Hàng'), width: 100,
            computeCssClasses(_ctx, dRecord) {
              const mode = dRecord.record['mode'];
              return TMSBillTransportationModeTools.getColor(mode);
            },
            fieldDataGetter(record) {
              const mode: string = record['mode'];
              if (mode) {
                let arrays: any[] = [];
                let splits = mode.split('_');
                splits.forEach(sel => {
                  if (_.translate[sel]) {
                    arrays.push(_.translate[sel])
                  } else {
                    arrays.push(sel)
                  }
                })
                return arrays.join(' ');
              }
              return mode;
            },
          },
          { name: 'truckType', label: T('Loại Xe'), width: 100 },
          {
            name: "responsibleFullName", label: T('PIC.'), width: 120, cssClass: 'pe-1', filterableType: 'options', filterable: true,
            state: { visible: appContext.getUserDataScope().scope == 'Company' },
            customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let uiList = ctx.uiRoot as entity.DbEntityList
              const { appContext, pageContext } = uiList.props;
              return (
                <div className='flex-hbox justify-content-center align-items-center' >
                  <module.account.WAvatars className='px-2'
                    appContext={appContext} pageContext={pageContext} avatarIds={[record['responsibleAccountId']]}
                    avatarIdType='AccountId' width={20} height={20} borderRadius={10} />
                  <bs.Tooltip tooltip={record['responsibleFullName']} className="flex-hbox">
                    {record['userName']}
                  </bs.Tooltip>
                </div>
              )
            }
          },
          {
            name: 'containerNo', label: T('Container No'),
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow() && event.field.name === 'containerNo') {
                  cell.forceUpdate()
                }
              },
            },
            customHeaderRender(_ctx, _field, headerEle) {
              return (
                <div className='flex-hbox'>
                  <FeatherIcon.Edit size={12} className='text-warning m-1' />
                  {headerEle}
                </div>
              )
            },
            editor: {
              type: 'string',
              onInputChange: _.onInputChange,
              renderCustom: (fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                let { displayRecord, fieldConfig, tabIndex, focus } = fieldCtx;
                let bill = displayRecord.record;
                let mode = bill.mode;
                if (TMSBillTransportationModeTools.isExport(mode)) {
                  return (
                    <div className={`flex-hbox`}>
                      <input.BBStringField style={{ height: 40 }} tabIndex={tabIndex} focus={focus} disable={!writeCap}
                        bean={bill} field={fieldConfig.name} onInputChange={onInputChange} />
                      {UITMSBillUtils.containerValidate(bill[fieldConfig.name])}
                    </div>

                  )
                } else {
                  return (
                    <div className={`flex-hbox ${TMSBillTransportationModeTools.getColor(mode)}`}>
                      {bill.tmsContainerNo}
                      {UITMSBillUtils.containerValidate(bill['tmsContainerNo'])}
                    </div>
                  )
                }
              },
            }
          },
          {
            name: 'sealNo', label: T('Seal No'),
            customHeaderRender(_ctx, _field, headerEle) {
              return (
                <div className='flex-hbox'>
                  <FeatherIcon.Edit size={12} className='text-warning m-1' />
                  {headerEle}
                </div>
              )
            },
            editor: {
              type: 'string',
              onInputChange: _.onInputChange,
              renderCustom: (fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                let { displayRecord, fieldConfig, tabIndex, focus } = fieldCtx;
                let bill = displayRecord.record;
                let mode = bill.mode;
                if (TMSBillTransportationModeTools.isExport(mode)) {
                  return (
                    <input.BBStringField style={{ height: 40 }} tabIndex={tabIndex} focus={focus} disable={!writeCap}
                      bean={bill} field={fieldConfig.name} onInputChange={onInputChange} />
                  )
                } else {
                  return (
                    <div className={TMSBillTransportationModeTools.getColor(mode)}>
                      {bill.tmsSealNo}
                    </div>
                  )
                }
              },
            }
          },
          { name: 'bookingCode', label: T('Mã Booking') },
          {
            name: 'pickupAddress', label: T('Địa Chỉ Lấy Hàng'), width: 300, dataTooltip: true,
            cssClass: 'text-warning'
          },
          {
            name: 'stopLocations', label: T('Điểm Trung Gian'), width: 200,
            customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let bill = dRecord.record;
              let stopLocations: Array<any> = bill['stopLocations'];
              let addresses: Array<string> = [];
              if (stopLocations) {
                for (let stopLocation of stopLocations) {
                  addresses.push(stopLocation.address);
                }
              }

              return <bs.CssTooltip >
                <bs.CssTooltipToggle className='flex-vbox justify-content-start'>
                  {
                    addresses.map((address: string, index: number) =>
                      <div style={{
                        borderBottom: index !== addresses.length - 1 ? '1px solid #ccc' : 'none',
                        padding: '2px 0'
                      }}>
                        {address}
                      </div>)
                  }
                </bs.CssTooltipToggle>
                <bs.CssTooltipContent
                  style={{ whiteSpace: 'break-spaces' }}>
                  {
                    addresses ? addresses.map((address) =>
                      <div className='p-2'>
                        {address}
                      </div>
                    ) : <></>
                  }
                </bs.CssTooltipContent>
              </bs.CssTooltip>
            },
          },
          {
            name: 'deliveryAddress', label: T('Địa Chỉ Giao Hàng'), width: 300, dataTooltip: true,
            cssClass: 'text-success',
            computeStyle(_ctx, dRecord) {
              let rec = dRecord.record;
              let rowHeight = rec['rowHeight'];
              return ({ height: rowHeight })
            },
          },
          {
            name: 'quantity', label: T('Số Lượng'), width: 80,
          },
          {
            name: 'quantityUnit', label: T('Đơn Vị'), width: 80,
          },
          {
            name: 'weight', label: T('Khối Lượng'), width: 80,
          },
          {
            name: 'volumeNote', label: T('Thể Tích'), width: 80,
          },
          { name: 'vendorFullName', label: T('Thầu Phụ'), filterable: true, filterableType: 'Options', state: { visible: false } },
          ..._.buildTruckCol(),
          // {
          //   name: 'truckNo', label: T('Truck No'), width: 150,
          //   customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, focus: boolean) => {
          //     let uiList = ctx.uiRoot as UITMSVendorBillList;
          //     let vendorBill = dRecord.record;
          //     let fileWidth = field.width ? field.width : 150;
          //     return (
          //       <div className={'flex-hbox align-items-center'}>
          //         <div style={{ width: fileWidth - 24, overflow: 'hidden' }}>
          //           <bs.Tooltip tooltip={vendorBill[field.name]} >{vendorBill[field.name]}</bs.Tooltip>
          //         </div>
          //         {vendorBill.id ? <FeatherIcon.Edit size={12} className='mx-1 state-modified'
          //           onClick={() =>
          //             UITMSVendorBillList.onShowVendorBillTracking(appContext, pageContext, vendorBill.tmsBillId, (data) => {
          //               TMSUtils.reloadData(uiList, SERVICE_SEARCH, METHOD_SEARCH, [data]);
          //             })
          //           }
          //         />
          //           : <></>}
          //       </div>
          //     );
          //   }
          // },
          {
            name: 'delayedTime', label: T('Delayed Time'), state: { visible: false },
            sortable: true, filterable: true, filterableType: 'date',
            computeCssClasses: (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
              let vendorBill = dRecord.record;
              let oneHourToMinutes = 60;
              if (vendorBill['delayedTime'] < 0 && vendorBill['delayedTime'] < oneHourToMinutes * -1) {
                return 'text-danger';
              }
              if (vendorBill['delayedTime'] > 0 && vendorBill['delayedTime'] > oneHourToMinutes) {
                return 'text-danger';
              }
              return '';
            },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow() && (event.field.name === 'estimateTime')) {
                  cell.forceUpdate();
                }
              },
            },
          },
          {
            name: 'vehicleArrivedOnTime', label: T('Arrived OnTime'), width: 150,
            state: { visible: false },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow() && (event.field.name === 'estimateTime')) {
                  cell.forceUpdate();
                }
              },
            },
          },
          {
            name: 'description', label: T('Ghi Chú'), width: 200,
            customHeaderRender(_ctx, _field, headerEle) {
              return (
                <div className='flex-hbox'>
                  <FeatherIcon.Edit size={12} className='text-warning m-1' />
                  {headerEle}
                </div>
              )
            },
            computeStyle(_ctx, dRecord) {
              let rec = dRecord.record;
              let rowHeight = rec['rowHeight'];
              return ({ height: rowHeight })
            },
            editor: { type: 'text', onInputChange: _.onInputChange }
          },
          {
            name: 'tmsBillDescription', label: T('Ghi Chú PIC'), dataTooltip: true, width: 250,
            computeStyle(_ctx, dRecord) {
              let rec = dRecord.record;
              let rowHeight = rec['rowHeight'];
              return ({ height: rowHeight })
            },
          },
          {
            name: 'fixed', label: T('Cước'),
            customHeaderRender(_ctx, _field, headerEle) {
              return (
                <div className='flex-hbox'>
                  <FeatherIcon.Edit size={12} className='text-warning m-1' />
                  {headerEle}
                </div>
              )
            },

            computeCssClasses: (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
              let bill = dRecord.record;
              let fixed = bill['fixed'];
              let tmsBillFixedPayment = bill['tmsBillFixedPayment'];
              if (fixed != tmsBillFixedPayment) return 'text-danger';
              return '';
            },
            editor: {
              type: 'currency',
              onInputChange: _.onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let { displayRecord, fieldConfig, tabIndex, focus } = fieldCtx;
                let vendorBill = displayRecord.record;
                let cssClass = fieldConfig.cssClass;
                let disable = false;
                if (vendorBill['vendorCostStatus'] !== VendorCostStatus.NEED_CONFIRM && vendorBill['vendorCostStatus'] !== null) {
                  disable = true;
                }
                return (
                  <input.BBCurrencyField disable={disable} style={{ height: 40 }} className={cssClass} tabIndex={tabIndex} focus={focus}
                    bean={vendorBill} field={fieldConfig.name} onInputChange={onInputChange} />
                )
              },
            }
          },
          {
            name: 'extra', label: T('Phát Sinh'),
            customHeaderRender(_ctx, _field, headerEle) {
              return (
                <div className='flex-hbox'>
                  <FeatherIcon.Edit size={12} className='text-warning m-1' />
                  {headerEle}
                </div>
              )
            },
            computeCssClasses: (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
              let bill = dRecord.record;
              let extra = bill['extra'];
              let tmsBillExtraPayment = bill['tmsBillExtraPayment'];
              if (extra != tmsBillExtraPayment) return 'text-danger';
              return '';
            },
            editor: {
              type: 'currency',
              onInputChange: _.onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let { displayRecord, fieldConfig, tabIndex, focus } = fieldCtx;
                let vendorBill = displayRecord.record;
                let cssClass = fieldConfig.cssClass;
                let disable = false;
                if (vendorBill['vendorCostStatus'] !== VendorCostStatus.NEED_CONFIRM && vendorBill['vendorCostStatus'] !== null) {
                  disable = true;
                }
                return (
                  <input.BBCurrencyField style={{ height: 40 }} disable={disable} className={cssClass} tabIndex={tabIndex} focus={focus}
                    bean={vendorBill} field={fieldConfig.name} onInputChange={onInputChange} />
                )
              },
            }
          },
          {
            name: 'cost', label: T('Tổng'),
            customHeaderRender(_ctx, _field, headerEle) {
              return (
                <div className='flex-hbox'>
                  <FeatherIcon.Edit size={12} className='text-warning m-1' />
                  {headerEle}
                </div>
              )
            },
            computeCssClasses: (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
              let bill = dRecord.record;
              let cost = bill['cost'];
              let tmsBillTotalPayment = bill['tmsBillTotalPayment'];
              if (cost != tmsBillTotalPayment) return 'text-danger';
              return '';
            },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow() && (event.field.name === 'fixed' || event.field.name === 'extra')) {
                  let vendorBill = cell.getDisplayRecord().record;
                  vendorBill.cost = vendorBill.fixed + vendorBill.extra;
                  cell.forceUpdate();
                }
              },
            },
          },
          {
            name: 'feedback', label: T('Feedback'), width: 120, dataTooltip: true, state: { visible: modCap },
          },
          {
            name: 'tmsBillFixedPayment', label: T('Bill Fixed'), hint: T('TMSBill Fixed'), state: { visible: modCap },
            computeCssClasses: (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
              let bill = dRecord.record;
              let fixed = bill['fixed'];
              let tmsBillFixedPayment = bill['tmsBillFixedPayment'];
              if (fixed != tmsBillFixedPayment) return 'text-danger';
              return '';
            },
          },
          {
            name: 'tmsBillExtraPayment', label: T('Bill Extra'), hint: T('TMSBill Extra'), state: { visible: modCap },
            computeCssClasses: (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
              let bill = dRecord.record;
              let extra = bill['extra'];
              let tmsBillExtraPayment = bill['tmsBillExtraPayment'];
              if (extra != tmsBillExtraPayment) return 'text-danger';
              return '';
            },
          },
          {
            name: 'tmsBillTotalPayment', label: T('Bill Total'), hint: T('TMSBill Total'), state: { visible: modCap },
            computeCssClasses: (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
              let bill = dRecord.record;
              let cost = bill['cost'];
              let tmsBillTotalPayment = bill['tmsBillTotalPayment'];
              if (cost != tmsBillTotalPayment) return 'text-danger';
              return '';
            },
          },
          {
            name: 'xlsxLastUpdate', label: T('X.Update'), hint: 'xlsxLastUpdate', cssClass: 'flex-vbox align-items-end',
            filterable: true, filterableType: 'options', width: 120, state: { visible: modCap },
          },
          {
            name: 'status', label: T('Vendor Status'), width: 150, state: { visible: false },
            filterableType: 'Options', filterable: true,
            customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let vendorBill = dRecord.record;
              let processStatus = vendorBill['status'];

              if (processStatus == TMSVendorBillStatus.REJECT || processStatus == TMSVendorBillStatus.CONFIRMED) {
                let laf: any = processStatus == TMSVendorBillStatus.REJECT ? 'danger' : 'success'
                return (
                  <bs.Button laf='link' className='w-100'>
                    <bs.Badge className='w-100' laf={laf}>
                      <bs.BadgeLabel>
                        {processStatus}
                      </bs.BadgeLabel>
                    </bs.Badge>
                  </bs.Button>
                )
              } else {
                return (
                  <div className={'flex-hbox flex-grow-0'} >
                    <bs.Button laf='success' className='my-5 p-1' style={{ width: 63, fontSize: 12 }} outline
                      onClick={() => this.onConfirm(vendorBill, TMSVendorBillStatus.CONFIRMED)} >
                      {T('Confirm')} <FeatherIcon.Check size={10} />
                    </bs.Button>
                    <bs.Button laf='danger' className='mx-1 my-5 p-1' style={{ width: 63, fontSize: 12 }} outline
                      onClick={() => this.onConfirm(vendorBill, TMSVendorBillStatus.REJECT)} >
                      {T('Reject')} <FeatherIcon.X size={10} />
                    </bs.Button>
                  </div>
                )
              }
            },
          },
          {
            name: 'vendorCostStatus', label: T('Vendor Cost Status'), container: 'fixed-right', width: 130, hint: 'Status',
            state: { visible: false },
            filterable: true, filterableType: 'options',
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow() && (event.field.name === 'vendorCostStatus')) {
                  cell.forceUpdate();
                }
              },
            },
            customRender: (ctx: grid.VGridContext, fieldConfig: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let vendor = dRecord.record;
              let status = vendor[fieldConfig.name];
              let updateStatus = (status: VendorCostStatus) => {
                appContext.createHttpBackendCall('TMSVendorBillService', 'updateVendorCostStatus', { id: vendor.id, vendorCostStatus: status })
                  .withSuccessData((data: any) => {
                    let vendorBill = data;
                    appContext.addOSNotification("success", T(`Update Success`));
                    vendor[fieldConfig.name] = status;
                    vendor['fixed'] = vendorBill['fixed'];
                    vendor['extra'] = vendorBill['extra'];
                    vendor['cost'] = vendorBill['cost'];
                    this.getVGridContext().getVGrid().forceUpdateView();
                  })
                  .call();
              }
              if (status == null || status === VendorCostStatus.NEED_CONFIRM) {
                return (
                  <div className={'flex-hbox justify-content-center'} >
                    <bs.Button disabled={!writeCap || !vendor.id} laf='warning' className='my-5 p-1' style={{ width: '100%', fontSize: 12 }} outline
                      onClick={() => updateStatus(VendorCostStatus.MANUAL_CONFIRM)} >
                      {T('Confirm')} <FeatherIcon.Check size={10} />
                    </bs.Button>
                  </div>
                )
              }
              let laf: 'primary' | 'secondary' | 'info' | 'success' | 'danger' | 'warning' = 'warning'
              if (status === VendorCostStatus.MANUAL_CONFIRM) {
                laf = 'primary';
              } else if (status === VendorCostStatus.AUTO_CONFIRM) {
                laf = 'success';
              }
              let onBack = () => {
                if (!modCap) return;
                const callbackConfirm = () => {
                  updateStatus(VendorCostStatus.NEED_CONFIRM)
                }
                let message = (<div className="text-danger">Do you want to revert the vendor cost status to 'NEED CONFIRM'?</div>);
                bs.dialogConfirmMessage('Confirm Message', message, callbackConfirm);
              }
              return (
                <bs.Button laf='link' className='w-100' onClick={onBack}>
                  <bs.Badge className='w-100 m-0' style={{ padding: 1 }} laf={laf}>
                    <bs.BadgeLabel>
                      {status}
                    </bs.BadgeLabel>
                  </bs.Badge>
                </bs.Button>

              )
            }
          },
          {
            name: 'commentStatus', label: T('Actions'), width: 80, cssClass: 'p-0', container: 'fixed-right',
            filterable: true, filterableType: 'options',
            customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              return (
                <div className={'flex-hbox'}>
                  {renderBtnTrackingAPI(ctx, record, { cssClass: 'px-0 py-1' })}
                  {this.renderAttachments(record)}
                  {/* <div className='mx-2'>
                    <UIBtnProjectTask ctx={ctx} appContext={appContext} pageContext={pageContext}
                      onPostCommit={(entity, type) => {
                        if (type == 'task') record['taskId'] = entity['id'];
                        if (type == 'comment') record['commentStatus'] = true;
                        ctx.getVGrid().forceUpdateView();
                      }}
                      pluginData={{
                        taskId: record['taskId'],
                        vendorBillId: record['id'],
                        fileNo: record['fileNo'],
                        commentStatus: record['commentStatus']
                      }} />
                  </div> */}
                </div>
              )
            },
          },
          { name: 'tmsBillCode', label: T('B.Code'), width: 50 },
          // { name: 'verify', label: T('Verify') },
          // ...entity.DbEntityListConfigTool.FIELD_ENTITY
        ],
        fieldGroups: {
          'billInfo': {
            label: T('Bill Info'),
            visible: true,
            fields: [
              'label', 'customerFullName'
            ]
          },
          'Vendor': {
            label: T('Vendor'),
            visible: true,
            fields: [
              'vendorFullName', 'truckNo',
              'delayedTime', 'vehicleArrivedOnTime', 'trackingNote'
            ]
          },
          'Ops Info': {
            label: T('Ops Info'),
            visible: false,
            fields: [
              'opsAccountId', 'opsAccountFullName',
              'opsMobile', 'opsIdentificationNo', 'opsNote'
            ]
          },
          'tmsBillCost': {
            label: T('TMSBill Cost'),
            visible: true,
            fields: [
              'tmsBillFixedPayment', 'tmsBillExtraPayment',
              'tmsBillTotalPayment',
            ]
          },
          'vendorCost': {
            label: T('Cước Thầu Phụ'),
            visible: true,
            fields: [
              'fixed', 'extra',
              'cost',
            ]
          }
        }
      },
      toolbar: {
        actions: [
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION(false, {
            name: 'mail', label: T('Mail'), icon: FeatherIcon.Send,
            onClick: (ctx) => {
              let uiRoot = ctx.uiRoot as UITMSVendorBillList;
              let { pageContext } = uiRoot.props;
              let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
                return (
                  <UITMSVendorMail appContext={appCtx} pageContext={pageCtx} observer={new entity.BeanObserver({})} />
                )
              }
              pageContext.createPopupPage('vendor-mail', T('Mail'), createAppPage, { size: 'xl' })
            },
          }),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_DELETE(!writeCap, T('Delete')),
        ],
        filters: entity.DbEntityListConfigTool.TOOLBAR_FILTERS(addDbSearchFilter),
        filterActions: [
          {
            name: 'filter', hint: 'Filter',
            createComponent: function (ctx: grid.VGridContext) {
              return (
                <div className='flex-hbox'>
                  <bs.Button laf='link' onClick={() => {
                    _.config['containerEmpty'] = !_.config['containerEmpty'];
                    if (_.config['containerEmpty']) {
                      plugin.getListModel().getRecordFilter().addFieldValueFilter('containerNo', 'containerNo', '');
                      plugin.getListModel().getRecordFilter().addFieldValueFilter('purpose', 'purpose', 'EXPORT');
                      plugin.getListModel().filter();
                    } else {
                      plugin.getListModel().getRecordFilter().removeFilter('containerNo');
                      plugin.getListModel().getRecordFilter().removeFilter('purpose');
                      plugin.getListModel().filter();
                    }
                    ctx.getVGrid().forceUpdateView();
                  }}>
                    <input.BBCheckboxField bean={_.config} field={'containerEmpty'} label='Cont Empty' value={false} disable />
                  </bs.Button >
                  <bs.Button laf='link' onClick={() => {
                    _.config['truckEmpty'] = !_.config['truckEmpty'];
                    if (_.config['truckEmpty']) {
                      plugin.getListModel().getRecordFilter().addFieldValueFilter('truckEntered', 'truckEntered', 'false');
                      plugin.getListModel().filter();
                    } else {
                      plugin.getListModel().getRecordFilter().removeFilter('truckEntered');
                      plugin.getListModel().filter();
                    }
                    ctx.getVGrid().forceUpdateView();
                  }}>
                    <input.BBCheckboxField bean={_.config} field={'truckEmpty'} label='Truck Empty' value={false} disable />
                  </bs.Button>
                  <bs.Button laf='link' onClick={() => {
                    _.config['costEmpty'] = !_.config['costEmpty'];
                    if (_.config['costEmpty']) {
                      plugin.getListModel().getRecordFilter().addFieldValueFilter('costEntered', 'costEntered', 'false');
                      plugin.getListModel().filter();
                    } else {
                      plugin.getListModel().getRecordFilter().removeFilter('costEntered');
                      plugin.getListModel().filter();
                    }
                    ctx.getVGrid().forceUpdateView();
                  }}>
                    <input.BBCheckboxField bean={_.config} field={'costEmpty'} label='Cost Empty' value={false} disable />
                  </bs.Button>
                </div>
              );
            }
          },
          ...entity.DbEntityListConfigTool.TOOLBAR_AUTO_REFRESH('auto-refresh-bill', T('Refresh')),
        ],
      },
      footer: {
        page: {
          hide: type === 'selector',
          render: (ctx: grid.VGridContext) => {
            let { appContext, pageContext, plugin } = this.props;
            return (
              <bs.Toolbar className='border justify-content-center'>
                <entity.WButtonEntityWrite icon={FeatherIcon.Printer}
                  appContext={appContext} pageContext={pageContext}
                  disable={!pageContext.hasUserReadCapability()} hide={!pageContext.hasUserReadCapability()}
                  label={T('In POD')} onClick={() => {
                    let ids = plugin.getListModel().getSelectedRecords().map(sel => {
                      return sel.tmsBillId;
                    });
                    let newRecords = plugin.getListModel().getRecords().find(sel => grid.getRecordState(sel).isMarkNew());
                    if (newRecords) {
                      bs.notificationShow("danger", "You need to save changes");
                      return;
                    }
                    UITMSBillUtils.onPrintReceiptOfDelivery(pageContext, ids);
                  }} />
                {/* <entity.WButtonEntityWrite
                  appContext={appContext} pageContext={pageContext} icon={FeatherIcon.RefreshCw}
                  label={T("Sync Bill")} onClick={this.updateVendorBill} /> */}
                {/* <entity.WButtonEntityWrite
                  appContext={appContext} pageContext={pageContext} icon={FeatherIcon.List} hide
                  label={T("Vendor Bill Not Match")} onClick={this.showVendorBillNotMatch} /> */}
                <entity.WButtonEntityWrite
                  appContext={appContext} pageContext={pageContext} icon={FeatherIcon.Upload}
                  label={T("Upload")} onClick={() => UITMSBillUtils.showUploadVendorBillXLSXFile(_, () => { _.reloadData() })} />
                <bs.Button laf='primary' outline onClick={() => {
                  let records = plugin.getListModel().getSelectedRecords();
                  if (!records.length) {
                    records = plugin.getListModel().getFilterRecords();
                  }
                  const createContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
                    return (
                      <TMSVendorBillExportList appContext={appCtx} pageContext={pageCtx} plugin={new entity.DbEntityListPlugin(records)} />
                    )
                  }
                  pageContext.createPopupPage('preview-data-export', 'Preview Data Export', createContent, { size: 'xl' })
                }}>
                  <FeatherIcon.Eye size={14} /> {T('Preview Export')}
                </bs.Button>
                {/* <XLSXCustomButton
                  tableName="vendor-bill"
                  appContext={appContext} pageContext={pageContext} context={this.getVGridContext()}
                  options={{ fileName: `subcontractor ${util.TimeUtil.toCompactDateFormat(new Date())}.xlsx`, modelName: 'subcontractor' }}
                  fieldSelect={
                    ['fileNo', 'deliveryPlan', 'time', 'customerFullName', 'containerNo', 'bookingCode', 'pickupAddress', 'deliveryAddress', 'tmsBillDescription',
                      'fixed', 'extra', 'cost', 'description', 'tmsBillCode']
                  }
                /> */}
                <entity.WButtonEntityWrite
                  appContext={appContext} pageContext={pageContext} icon={FeatherIcon.Save}
                  label={T('Lưu')} onClick={this.onSave} />
              </bs.Toolbar>

            );
          }
        },
      },
      view: {
        currentViewName: viewMode ? viewMode : 'aggregation',
        availables: {
          table: {
            viewMode: 'table'
          },
          aggregation: {
            viewMode: 'aggregation',
            treeWidth: 150,
            createAggregationModel(_ctx: grid.VGridContext) {

              let model = new grid.AggregationDisplayModel(T('All'), false);
              model.addAggregation(
                new grid.DateValueAggregation(T("Date"), "deliveryPlan", "YYYY/MM/DD", true)
                  .withSortBucket('desc'));
              model.addAggregation(
                new grid.ValueAggregation(T("Vendor"), "vendorFullName", false)
                  .withSortBucket('asc'));
              return model;
            },
          }
        },
      },

    }
    if (config.record.editor) {
      for (let sel of config.record.fields) {
        if (sel.editor) sel.editor.enable = true;
      }
    }
    return config;
  }

  showVendorBillNotMatch = () => {
    let { pageContext } = this.props;
    let createAppPage = (_appCtx: app.AppContext, _pageCtx: app.PageContext) => {
      return (
        <div className='flex-vbox'>
          <UITMSVendorBillList
            appContext={_appCtx} pageContext={_pageCtx} plugin={new UITMSVendorBillNotLinkTMSBillPlugin()} />
        </div>
      )
    }
    pageContext.createPopupPage('vendor-bill-not-match', T('Vendor Bill Not Match'), createAppPage, { size: 'xl' })
  }


  onDeleteAction() {
    let { plugin, appContext } = this.props;
    let callbackConfirm = () => {
      let ids = plugin.getListModel().getSelectedRecordIds();
      appContext.createHttpBackendCall('TMSVendorBillService', 'deleteVendorBills', { ids: ids })
        .withSuccessData((_data: any) => {
          appContext.addOSNotification("success", T(`Delete Success`));
          plugin.getListModel().removeSelectedDisplayRecords();
          this.getVGridContext().getVGrid().forceUpdateView();
        })
        .call();
    }
    let message = (<div className="text-danger">Do you want to delete these vendor bill?</div>);
    bs.dialogConfirmMessage('Confirm Message', message, callbackConfirm);
  }

  renderAttachments = (vendorBill: any) => {
    let { pageContext } = this.props
    const onShow = () => {
      const createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
        return (
          <module.storage.UIAttachments
            appContext={appCtx} pageContext={pageCtx}
            readOnly={!pageContext.hasUserWriteCapability()}
            commitURL={ManagementRestURL.tmsBill.saveAttachments(vendorBill['tmsBillId'], vendorBill['id'], 'TMSVendorBill')}
            loadURL={ManagementRestURL.tmsBill.loadAttachments(vendorBill['tmsBillId'])}
            onChange={(plugin) => {
              if (vendorBill['vendorAttachFileTotal'] != plugin.getModel().getRecords().length) {
                vendorBill['vendorAttachFileTotal'] = plugin.getModel().getRecords().length;
                this.getVGridContext().getVGrid().forceUpdateView();
              }
            }}
          />
        )
      }
      pageContext.createPopupPage('attachments', T("Attachments"), createAppPage, { size: "lg", backdrop: "static" });
    }
    return TMSUtils.renderFileAttachmentsIcon(vendorBill['vendorAttachFileTotal'], onShow);
  }

  static onShowVendorBillTracking(appContext: app.AppContext, pageContext: app.PageContext, bill: any, onCommit?: (data: any) => void) {
    appContext.createHttpBackendCall('TMSVendorBillService', 'getOrCreateVendorBillByTmsBill', { id: bill['id'] })
      .withSuccessData((data: any) => {
        let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          let observer = new entity.ComplexBeanObserver(data);

          let onSave = () => {
            observer.commitAndGet()
            appContext.createHttpBackendCall('TMSVendorBillService', 'saveVendorBill', { price: observer.commitAndGet() })
              .withSuccessData((data: any) => {
                appCtx.addOSNotification("success", T("Save Success"));
                pageCtx.back();
                if (onCommit) onCommit(data);
              })
              .call();
          }
          let obBillTrackings = observer.createVGridEntityListEditorPlugin('billTrackings', []);
          obBillTrackings.getModel().getRecords().forEach(rec => {
            rec['vendorId'] = bill['vendorId'];
            rec['vendorFullName'] = bill['vendorFullName'];
          });

          return <div className='flex-vbox'>
            <UITMSVendorBillTrackingList style={{ height: 500 }} appContext={appCtx} pageContext={pageCtx}
              plugin={observer.createVGridEntityListEditorPlugin('billTrackings', [])}
              dialogEditor={true} editorTitle={T("Vendor Bill Tracking")}
            />
            <bs.Toolbar className='border'>
              <entity.WButtonEntityWrite
                appContext={appCtx} pageContext={pageCtx} icon={FeatherIcon.Save}
                label={T('Save')} onClick={onSave} />
            </bs.Toolbar>
          </div>
        }
        pageContext.createPopupPage('tms-vendor-bill-tracking', T('Truck'), createAppPage, { size: 'md' })

      })
      .call();
  }

  onConfirm = (vendorBill: any, status: TMSVendorBillStatus) => {
    let { appContext, pageContext } = this.props;
    let params = {
      id: vendorBill.id,
      status: status,
      issue: null
    }
    let onCommit = () => {
      appContext.createHttpBackendCall('TMSVendorBillService', 'updateVendorBillStatus', params)
        .withSuccessData((_data: any) => {
          vendorBill['status'] = status;
          this.getVGridContext().getVGrid().forceUpdateView();
          appContext.addOSNotification("success", T(`Update status success!`));

        })
        .call();
    }

    if (status === TMSVendorBillStatus.CONFIRMED) return onCommit();

    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <div className='flex-vbox'>
          <input.BBTextField bean={params} field={'issue'} label={T('Note')} style={{ height: '10em' }} />
          <bs.Toolbar className='border'>
            <entity.WButtonEntityNew
              label={'Commit'} appContext={appCtx} pageContext={pageCtx}
              onClick={() => {
                onCommit();
                pageCtx.back();
              }} />
          </bs.Toolbar>
        </div>
      )
    }
    pageContext.createPopupPage('issue', "Issue", createAppPage);
  }

  updateVendorBill = () => {
    let { appContext, plugin } = this.props;
    let ids = plugin.getListModel().getSelectedRecordIds();
    if (!ids || ids.length === 0) {
      bs.notificationShow("danger", T("VendorBill is not selected"), T("You should select at least 1 Record!!!"));
      return;
    }

    appContext.createHttpBackendCall('TMSVendorBillService', 'updateVendorBill', { ids: ids })
      .withSuccessData((data: any) => {
        appContext.addOSNotification("success", T("Update Vendor Bill Success"));
        TMSUtils.reloadData(this, SERVICE_SEARCH, METHOD_SEARCH, data);
      })
      .call();
  }

  onDefaultSelect(dRecord: grid.DisplayRecord): void {
    let vendorBill = dRecord.record
    let { appContext, pageContext } = this.props;

    appContext.createHttpBackendCall('TMSVendorBillService', 'getVendorBill', { id: vendorBill.id })
      .withSuccessData((data: any) => {
        data['bill'] = {
          tmsContainerNo: vendorBill['tmsContainerNo'],
          tmsSealNo: vendorBill['tmsSealNo'],
          pickupAddress: vendorBill['pickupAddress'],
          deliveryAddress: vendorBill['deliveryAddress'],
          mode: vendorBill['mode']
        }
        let createPageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (
            <UITMSVendorBill
              vendor={{ vendorId: vendorBill.vendorId, vendorName: vendorBill.vendorFullName }}
              appContext={appCtx} pageContext={pageCtx}
              observer={new entity.ComplexBeanObserver(data)} onPostCommit={() => { TMSUtils.reloadData(this, SERVICE_SEARCH, METHOD_SEARCH, [data]) }} />
          );
        }
        pageContext.createPopupPage('tms-vendor-bill', T(`VendorBill: ${vendorBill.fileNo} -  ${vendorBill.vendorFullName}`), createPageContent, { size: 'lg' })

      })
      .call();
  }

  onSave = () => {
    let { appContext, plugin } = this.props;
    let modifiedRecords = plugin.getListModel().getModifiedRecords();
    for (let rec of modifiedRecords) {
      if (!rec['id']) rec['uikey'] = `bill-${util.IDTracker.next()}`;
    }
    appContext.createHttpBackendCall('TMSVendorBillService', 'saveVendorBillModels', { prices: modifiedRecords })
      .withSuccessData((data: any) => {
        appContext.addOSNotification("success", T("Save Vendor Bill Success"));
        TMSUtils.reloadData(this, SERVICE_SEARCH, METHOD_SEARCH, data);
      })
      .withFailNotification('danger', T('Save Vendor Bill Fail!'))
      .call();
  }
}

class UITMSVendorMail extends entity.AppDbEntity {
  observerMessage = new entity.ComplexBeanObserver({});
  uikey = util.IDTracker.next();

  render(): React.ReactNode {
    let { appContext, pageContext, observer } = this.props;
    let bean = observer.getMutableBean();
    return (
      <bs.VSplit>
        <bs.VSplitPane className='flex-vbox' width={300}>
          <div className='flex-vbox'>
            <BBRefVehicleFleet minWidth={400}
              appContext={appContext} pageContext={pageContext}
              bean={bean} beanIdField={'vendorId'} beanLabelField={'vendorFullName'} placeholder={'Vendor'}
            />
            <bs.Row>
              <bs.Col span={6}>
                <input.BBDateInputMask
                  bean={bean} field={'fromDate'} format='DD/MM/YYYY' label='From' onInputChange={() => this.forceUpdate()} />
              </bs.Col>
              <bs.Col span={6}>
                <input.BBDateInputMask
                  bean={bean} field={'toDate'} format='DD/MM/YYYY' label='To' onInputChange={() => this.forceUpdate()} />
              </bs.Col>
            </bs.Row>
          </div>
          <bs.Toolbar>
            <entity.WButtonEntityRead appContext={appContext} pageContext={pageContext}
              icon={FeatherIcon.RotateCw} label={'Load'}
              onClick={() => {
                appContext.createHttpBackendCall('TMSPrintService', 'createVendorAccountingEmail', { params: observer.getMutableBean() })
                  .withSuccessData((data: any) => {
                    this.observerMessage = new entity.ComplexBeanObserver(data);
                    this.uikey = util.IDTracker.next();
                    this.forceUpdate();
                  })
                  .call();
              }} />
          </bs.Toolbar>
        </bs.VSplitPane>
        <bs.VSplitPane>
          <module.communication.message.UIMessageEditor key={`${this.uikey}-vendor-mail`}
            appContext={appContext} pageContext={pageContext} onPostCommit={() => {
              pageContext.back();
            }}
            observer={this.observerMessage} />
        </bs.VSplitPane>
      </bs.VSplit>
    )
  }
}

export function renderBtnTrackingAPI(
  ctx: grid.VGridContext, vendorBill: any, config?: { btnShowBorder?: boolean, iconSize?: number, cssClass?: any }) {
  let uiRoot = ctx.uiRoot as entity.DbEntityList;
  let { pageContext } = uiRoot.props;
  if (!vendorBill.vendorAuthorization) return;
  let smallScreen = bs.ScreenUtil.isSmallScreen();
  const onShow = () => {
    let createPageContent = (_appCtx: app.AppContext, _pageCtx: app.PageContext) => {
      const CONFIG = app.host.CONFIG
      let serverUrl = CONFIG.getUIServerUrl();
      let url = `${serverUrl}/api/ui/${vendorBill.vendorAuthorization}`
      return (<object className="flex-vbox" data={url}></object>)
    }
    pageContext.createPopupPage('uiapi', "Api UI", createPageContent, { size: smallScreen ? 'xl' : 'lg', backdrop: 'static' });
  }
  let iconSize = 12;
  let cssClass = '';
  let btnShowBorder: boolean = false;
  if (config) {
    if (config.iconSize) iconSize = config.iconSize;
    cssClass = config.cssClass;
    btnShowBorder = config.btnShowBorder ? true : false;
  }
  return (
    <bs.Button laf={btnShowBorder ? 'info' : 'link'} outline className={`text-primary ${cssClass}`} onClick={onShow}>
      <FeatherIcon.Link size={iconSize} />
    </bs.Button>
  )
}

export function renderVendorBillAttachmentsByToken(authorizationToken: any): React.JSX.Element {
  if (!authorizationToken) return <h4>{T('Authorization Token Is Empty, Contact admin for support!')}</h4>;
  const CONFIG = app.host.CONFIG
  let serverUrl = CONFIG.getUIServerUrl();
  let url = `${serverUrl}/api/ui/${authorizationToken}`
  return (
    <object className="flex-vbox" data={url}></object>
  )
}
interface UITMSVendorBillProps extends entity.AppComplexEntityEditorProps {
  vendor?: { vendorId: number, vendorName: any };
}
class UITMSVendorBill extends entity.AppDbComplexEntityEditor<UITMSVendorBillProps> {
  onShow = (vendorBill: any) => {
    let { pageContext } = this.props;
    const createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UIVendorAttachments readOnly={!pageContext.hasUserWriteCapability()}
          appContext={appCtx} pageContext={pageCtx}
          loadMethod={{ component: 'TMSVendorBillService', method: 'findTMSVendorAttachments', param: { id: vendorBill['id'] } }}
          commitMethod={{ component: 'TMSVendorBillService', method: 'saveTMSVendorAttachments', param: { id: vendorBill['id'] } }}
        />)
    }
    pageContext.createPopupPage('attachments', T("Attachments"), createAppPage, { size: "lg", backdrop: "static" });
  }

  onSave = () => {
    let { observer, appContext, pageContext, onPostCommit } = this.props;
    observer.commitAndGet()
    let vendorBill = observer.getMutableBean();

    appContext.createHttpBackendCall('TMSVendorBillService', 'saveVendorBill', { price: vendorBill })
      .withSuccessData((data: any) => {
        appContext.addOSNotification("success", T("Save Success"));
        observer.replaceWithUpdate(data);
        let plugin = observer.observers['billTrackings'] as entity.VGridEntityListEditorPlugin;
        let records = plugin.getModel().getRecords();
        for (let row = 0; row < records.length; row++) {
          let record = records[row];
          grid.initRecordState(record, row);
        }
        observer.commitAndGet();
        if (onPostCommit) {
          onPostCommit(vendorBill, this);
        }
        if (pageContext.breadcumbs) pageContext.breadcumbs.forceUpdatePreviousPage();
        this.forceUpdate();
      })
      .call();

  }

  render() {
    let { appContext, pageContext, observer, vendor } = this.props;
    let writeCap = pageContext.hasUserWriteCapability();
    let vendorBill = observer.getMutableBean();
    let billTrackings = observer.createVGridEntityListEditorPlugin('billTrackings', []);
    if (billTrackings.getModel().getRecords().length == 0) {
      billTrackings.addRecord({});
    }
    billTrackings.getModel().getRecords().forEach(rec => {
      if (!rec['vendorId']) {
        rec['vendorId'] = vendor?.vendorId;
        rec['vendorFullName'] = vendor?.vendorName
      }
    });
    let tmsBillOb = observer.createObserver('bill', {});
    let bill = tmsBillOb.getMutableBean();

    return (
      <div className='flex-vbox'>
        <bs.VSplit updateOnResize smallScreenView="tabs">
          <bs.VSplitPane className='pr-1' width={'50%'} title="Info">
            <div className="flex-vbox">
              <bs.ScrollableCards className='flex-vbox'>
                <bs.Card header="Vendor Info">
                  <div className='p-1'>
                    <bs.Row>
                      <bs.Col span={6}>
                        <input.BBStringField bean={vendorBill} label='File No.' field={'fileNo'} disable={true} />
                      </bs.Col>
                      <bs.Col span={6}>
                        <input.BBStringField bean={vendorBill} label='Customer' field={'customerFullName'} disable={true} />
                      </bs.Col>
                    </bs.Row>
                    <bs.Row>
                      <bs.Col span={6}>
                        <input.BBDateTimeField bean={vendorBill} label='Delivery Plan' field='deliveryPlan' disable={true} dateFormat={"DD/MM/YYYY"} timeFormat={"HH:mm:ss"} />
                      </bs.Col>
                      <bs.Col span={6}>
                        <bs.FormLabel>E.Time</bs.FormLabel>
                        <input.BBTimeInputMask bean={vendorBill} field={'estimateTime'}
                          onInputChange={(bean, field, oldVal, newVal) => {
                            if (newVal) {
                              vendorBill['modifiedEstimateTime'] = util.TimeUtil.toCompactDateTimeFormat(new Date());
                            } else {
                              vendorBill['modifiedEstimateTime'] = null;
                            }
                          }}
                        />
                      </bs.Col>
                    </bs.Row>
                    <bs.Row>
                      <bs.Col span={6}>
                        <input.BBStringField bean={vendorBill} label='PIC.' field={'responsibleFullName'} disable={true} />
                      </bs.Col>
                      <bs.Col span={6}>
                        <input.BBStringField bean={vendorBill} label='Booking Code' field={'bookingCode'} disable={true} />
                      </bs.Col>
                    </bs.Row>
                    {/* <bs.Row>
                      <bs.Col span={6}>
                        <input.BBStringField bean={vendorBill} label='Delayed Time' field={'delayedTime'} disable={true} />
                      </bs.Col>
                      <bs.Col span={6}>
                        <input.BBStringField bean={vendorBill} label='Vendor' field={'vendorFullName'} disable={true} />
                      </bs.Col>
                    </bs.Row> */}
                    <bs.Row>
                      <bs.Col span={6}>
                        <input.BBTextField style={{ height: 100 }} bean={bill} label='Delivery Address' field={'deliveryAddress'} disable={true} />
                      </bs.Col>
                      <bs.Col span={6}>
                        <input.BBTextField style={{ height: 100 }} bean={bill} label='Pickup Address' field={'pickupAddress'} disable={true} />
                      </bs.Col>
                    </bs.Row>
                    {/* <bs.Row>
                      <bs.Col span={6}>
                        <input.BBStringField bean={vendorBill} label='Booking Code' field={'bookingCode'} disable={true} />
                      </bs.Col>
                      <bs.Col span={6}>
                        <input.BBSelectField bean={vendorBill} label='Status' field={'status'}
                          options={[TMSVendorBillStatus.NEED_CONFIRM, TMSVendorBillStatus.CONFIRMED, TMSVendorBillStatus.REJECT]}
                        />
                      </bs.Col>
                    </bs.Row> */}

                    <bs.Row>
                      <bs.Col span={12}>
                        <input.BBTextField bean={vendorBill} label='Description' field={'description'} />
                      </bs.Col>
                    </bs.Row>
                  </div>
                </bs.Card>
                {TMSBillTransportationModeTools.isExport(bill.mode) ?
                  <bs.Card header="Container Info" className={`${vendorBill.containerNo ? 'bg-success' : 'bg-danger'} bg-opacity-10`}>
                    <bs.Row>
                      <bs.Col span={6}>
                        <input.BBStringField bean={vendorBill} label='Container No' field={'containerNo'} />
                      </bs.Col>
                      <bs.Col span={6}>
                        <input.BBStringField bean={vendorBill} label='Seal No' field={'sealNo'} />
                      </bs.Col>
                    </bs.Row>
                  </bs.Card>
                  :
                  <bs.Card header="Container Info" >
                    <bs.Row>
                      <bs.Col span={6}>
                        <input.BBStringField bean={bill} label='Container No' field={'tmsContainerNo'} disable />
                      </bs.Col>
                      <bs.Col span={6}>
                        <input.BBStringField bean={bill} label='Seal No' field={'tmsSealNo'} disable />
                      </bs.Col>
                    </bs.Row>
                  </bs.Card>
                }
                <bs.Card header="Cost" className={`${vendorBill.cost ? 'bg-success' : 'bg-danger'} bg-opacity-10`}>
                  <bs.Row>
                    <bs.Col span={4}>
                      <input.BBCurrencyField bean={vendorBill} label='Fixed' field={'fixed'}
                        onInputChange={(bean, field, oldVal, newVal) => {
                          if (oldVal !== newVal) {
                            bean['cost'] = bean['extra'] + newVal
                          }
                          this.forceUpdate();
                        }} />
                    </bs.Col>
                    <bs.Col span={4}>
                      <input.BBCurrencyField bean={vendorBill} label='Extra' field={'extra'}
                        onInputChange={(bean, field, oldVal, newVal) => {
                          if (oldVal !== newVal) {
                            bean['cost'] = bean['fixed'] + newVal
                          }
                          this.forceUpdate();
                        }} />
                    </bs.Col>
                    <bs.Col span={4}>
                      <input.BBCurrencyField bean={vendorBill} label='Cost' field={'cost'} disable />
                    </bs.Col>
                  </bs.Row>
                </bs.Card>
                <bs.Card header="Ops Info" collapse>
                  <bs.Row>
                    <bs.Col span={6}>
                      <module.company.hr.BBRefEmployee
                        appContext={appContext} pageContext={pageContext} bean={vendorBill}
                        disable={!writeCap} selectedId='accountId' department={'ops'}
                        beanIdField='opsAccountId' beanLabelField='opsAccountFullName'
                        label='OPS' placeholder='OPS'
                        onPostUpdate={(_inputUI, _bean, selectOpt, _userInput) => {
                          if (selectOpt) {
                            vendorBill['opsMobile'] = selectOpt['mobile'];
                            vendorBill['opsIdentificationNo'] = selectOpt['identificationNo'];
                          }
                          this.forceUpdate();
                        }}
                      />
                    </bs.Col>
                    <bs.Col>
                      <input.BBStringField bean={vendorBill} label='Mobile' field={'opsMobile'} />
                    </bs.Col>
                  </bs.Row>
                  <bs.Row>
                    <bs.Col span={6}>
                      <input.BBStringField bean={vendorBill} label='Identification No' field={'opsIdentificationNo'} />
                    </bs.Col>
                    <bs.Col span={6}>
                      <input.BBSelectField bean={vendorBill} field={'opsStatus'} label='Status'
                        options={[TMSOperationsStatus.NEED_CONFIRM, TMSOperationsStatus.PROCESSING, TMSOperationsStatus.DONE]}
                      />
                    </bs.Col>
                  </bs.Row>
                  <bs.Row>
                    <bs.Col span={12}>
                      <input.BBTextField bean={vendorBill} label='Note' field={'opsNote'} />
                    </bs.Col>
                  </bs.Row>
                </bs.Card>
              </bs.ScrollableCards>
            </div>
          </bs.VSplitPane>
          <bs.VSplitPane title="Bill Trackings">
            <UITMSVendorBillTrackingList appContext={appContext} pageContext={pageContext}
              plugin={billTrackings}
              dialogEditor={true} editorTitle={T("Vendor Bill Tracking")} />
          </bs.VSplitPane>
        </bs.VSplit>
        <bs.Toolbar hide={!writeCap}>
          {/* <bs.Button laf={'primary'} onClick={() => this.onShow(vendorBill)}>
            <FeatherIcon.FileText className='me-1' size={12} />
            {T('File Attachment')}
          </bs.Button> */}
          <entity.WButtonEntityWrite
            icon={FeatherIcon.Save}
            label={T('Save')}
            appContext={appContext} pageContext={pageContext}
            onClick={this.onSave}
          />
          {/* <entity.WButtonEntityReset
            appContext={appContext} pageContext={pageContext} observer={observer}
            onPostRollback={this.onPostRollback} /> */}
        </bs.Toolbar>
      </div>
    )
  }
}
interface UITMSVendorBillUploadListProps extends entity.DbEntityListProps {
  context: grid.VGridContext;
}
export class UITMSVendorBillUploadList extends entity.DbEntityList<UITMSVendorBillUploadListProps> {

  createVGridConfig() {
    let { appContext, pageContext } = this.props;
    let config: grid.VGridConfig = {
      record: {
        editor: {
          enable: true,
          supportViewMode: ['table']
        },
        computeDataRowHeight: (_ctx: grid.VGridContext, dRec: grid.DisplayRecord) => {
          return 30;
        },
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          {
            name: 'fileNo', label: T('File No.'),
            width: 150, filterableType: 'string', filterable: true, container: 'fixed-left'
          },
          { name: 'code', label: T('Code'), width: 155, state: { visible: false } },
          {
            name: 'customerFullName', label: T('Customer'),
            width: 170, filterableType: 'Options', filterable: true, container: 'fixed-left'
          },
          { name: 'deliveryPlan', label: T('Delivery Plan'), filterableType: 'date', filterable: true, width: 200, format: compactDateTime },
          { name: 'containerNo', label: T('Container No') },
          { name: 'bookingCode', label: T('Booking/Bill') },
          {
            name: 'pickupAddress', label: T('Pickup Address'), width: 200, dataTooltip: true, cssClass: 'text-warning'
          },
          {
            name: 'deliveryAddress', label: T('Delivery Address'), width: 200, dataTooltip: true, cssClass: 'text-success'
          },
          {
            name: 'vendorFullName', label: T('Vendor'), width: 200, dataTooltip: true, cssClass: 'text-success',
            editor: {
              type: 'string',
              enable: true,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let { displayRecord, fieldConfig, tabIndex, focus } = fieldCtx;
                let bill = displayRecord.record;
                let cssClass = fieldConfig.cssClass;
                const oldVal = bill[fieldConfig.name];
                return (
                  <BBRefVehicleFleet minWidth={400}
                    className={cssClass} appContext={appContext} pageContext={pageContext} tabIndex={tabIndex} autofocus={focus}
                    bean={bill} beanIdField={'vendorId'} beanLabelField={'vendorFullName'} placeholder={'Vendor'}
                    onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                      onInputChange(bean, fieldConfig.name, oldVal, bean[fieldConfig.name])
                    }}
                  />
                )
              },
            }
          },
          {
            name: 'fixed', label: T('Fixed'), format: util.text.formater.currency,
          },
          {
            name: 'extra', label: T('Extra'), format: util.text.formater.currency,
          },
          {
            name: 'cost', label: T('Cost'), format: util.text.formater.currency,
          },
          {
            name: 'description', label: T('Note')
          },
          {
            name: 'xlsxStatus', label: T('Verify'), container: 'fixed-right', filterable: true, filterableType: 'options',
            customRender: (_ctx: grid.VGridContext, _fieldConfig: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let xlsxStatus = record['xlsxStatus'];
              let laf: any = 'success';
              if (xlsxStatus === 'WARNING') laf = 'warning';
              if (xlsxStatus === 'NOT_MATCH') laf = 'danger';
              return <bs.Badge className='w-100 m-0' style={{ padding: 1 }} laf={laf}>
                <bs.BadgeLabel>
                  {xlsxStatus}
                </bs.BadgeLabel>
              </bs.Badge>
            }
          }

        ]
      },
      toolbar: {
      },
      footer: {
        page: {
          render: (ctx: grid.VGridContext) => {
            let { appContext, pageContext } = this.props;
            return (
              <bs.Toolbar className='border'>
                <entity.WButtonEntityWrite
                  appContext={appContext} pageContext={pageContext} icon={FeatherIcon.Save}
                  label={T('Confirm')} onClick={this.onConfirm} />
              </bs.Toolbar>

            );
          }
        },
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          },
        },
      },

    }
    return config;
  }

  onConfirm = () => {
    let { appContext, pageContext, plugin, context } = this.props;
    let uiRoot = context.uiRoot as UITMSVendorBillList;
    let vendorBills = plugin.getListModel().getRecords();

    appContext.createHttpBackendCall('TMSVendorBillService', 'saveVendorBills', { vendorBills: vendorBills })
      .withSuccessData((_data: any) => {
        appContext.addOSNotification("success", T("Save Vendor Bill Success"));
        uiRoot.reloadData();
        pageContext.back()
      })
      .withFailNotification('danger', T('Save Vendor Bill Fail!'))
      .call();
  }
}

interface UIBtnRequestAdjustCostProps extends app.AppComponentProps {
  ctx: grid.VGridContext;
  pluginData: any;
  onPostCommit?(entity: any, type: 'task' | 'comment'): void;
}
export class UIBtnProjectTask extends app.AppComponent<UIBtnRequestAdjustCostProps> {
  onUpdateVendorBillTasKId = (task: any) => {
    const { appContext, pluginData, onPostCommit } = this.props;
    if (pluginData['taskId']) return;

    let params = {
      vendorBillId: pluginData['vendorBillId'],
      taskId: task['id']
    }
    appContext.createHttpBackendCall('TMSVendorBillService', 'updateTaskId', params)
      .withSuccessData((_data: any) => {
        if (onPostCommit) onPostCommit(task, 'task');
        appContext.addOSNotification('success', 'Update Vendor Bill TaskId Success!!!');
      })
      .call();
  }

  onUpdateLastTaskCommenter = (comment: any) => {
    const { appContext, onPostCommit } = this.props;

    appContext.createHttpBackendCall('TMSVendorBillService', 'updateLastTaskCommenter', { comment: comment })
      .withSuccessData((_data: any) => {
        if (onPostCommit) onPostCommit(comment, 'comment');
        appContext.addOSNotification('success', 'Update commenter success!!!');
      })
      .call();
  }

  onLoadProject = () => {
    const { appContext, pageContext, ctx, pluginData } = this.props;
    let taskId = pluginData['taskId'];
    let task = {
      id: taskId,
      pluginData: pluginData,
    }

    appContext.createHttpBackendCall('TMSVendorBillService', 'getTMSVendorBillProjectTask', { params: task })
      .withSuccessData((data: any) => {
        let model = data;
        let projectContext = new module.project.ProjectContext(model.project);
        const createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (
            <module.project.task.UILoadableTask appContext={appCtx} pageContext={pageCtx} uiContext={ctx}
              projectContext={projectContext} task={model.task}
              onPostCommit={this.onUpdateVendorBillTasKId}
              onCommentPostCommit={this.onUpdateLastTaskCommenter}
            />
          )
        }
        pageContext.createPopupPage('request-adjust-cost', T('Request Adjust Cost'), createAppPage, { size: 'xl' });
      })
      .call();
  }

  render(): React.ReactNode {
    const { pluginData } = this.props;
    let vendorBillId = pluginData['vendorBillId'];
    if (!vendorBillId) return;
    let taskId = pluginData['taskId'];
    let cssClass = 'text-secondary';
    if (taskId) cssClass = 'text-danger';
    let countIcon = <></>;
    if (pluginData['commentStatus'] === 'NOT_REPLIED' && taskId) {
      countIcon = (
        <div className={'align-items-center justify-content-center text-center'}
          style={{
            height: 12, width: 12, borderRadius: 50, fontSize: 12,
            backgroundColor: 'red', color: 'white',
            marginTop: -20, marginLeft: 5
          }}>
          {'!'}
        </div>
      );
    }
    return (
      <bs.Button laf='link' className={cssClass} onClick={this.onLoadProject}>
        <FeatherIcon.MessageSquare size={14} />
        {countIcon}
      </bs.Button>
    )
  }
}

