import React from "react";
import * as FeatherIcon from "react-feather";

import { T } from "../tms/backend";
import { entity, input, bs, util, grid } from "@datatp-ui/lib";
import { TMSBillTransportationModeTools } from "../tms/utils";
import { XLSXCustomButton } from "../tms/XLSXButton";

export class TMSVendorBillExportList extends entity.DbEntityList {
  translate: Record<string, string> = {
    'EXPORT': 'XUẤT',
    'IMPORT': 'NHÂP',
  }
  createVGridConfig() {
    let { appContext, pageContext, plugin } = this.props;
    let _ = this;
    let config: grid.VGridConfig = {
      title: T("TMS Report Bill"),
      record: {
        fields: [
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          { name: 'tmsBillCode', label: T('Code') },
          { name: 'label', label: T('File No.') },
          {
            name: 'hwbNo', label: T('HBL No'),
          },
          {
            name: 'deliveryPlan', label: T('Thời Gian'),
            sortable: true, filterable: true, filterableType: 'date',
            width: 150, format: util.text.formater.compactDate,
            fieldDataGetter(record) {
              return util.text.formater.compactDate(record['deliveryPlan']);
            },
          },
          {
            name: "responsibleFullName", label: T('Người chịu trách nhiệm'), width: 200, cssClass: 'pe-1', filterableType: 'options', filterable: true,
          },
          {
            name: 'office', label: T('Văn phòng'), width: 120, hint: 'Office', filterableType: 'Options', filterable: true,
          },
          {
            name: 'customerFullName', label: T('Khách hàng'), width: 170, cssClass: 'px-1', sortable: true,
            dataTooltip: true, filterableType: 'Options', filterable: true,
          },
          {
            name: 'mode', label: T('Loại'), width: 100, filterableType: 'options', filterable: true, sortable: true,
            fieldDataGetter(record) {
              const mode: string = record['mode'];
              if (mode) {
                let arrays: any[] = [];
                let splits = mode.split('_');
                splits.forEach(sel => {
                  if (_.translate[sel]) {
                    arrays.push(_.translate[sel])
                  } else {
                    arrays.push(sel)
                  }
                })
                return arrays.join(' ');
              }
              return mode;
            },
          },
          { name: 'bookingCode', label: T('Booking/Bill'), width: 150 },
          {
            name: 'truckType', label: T('Loại xe'), cssClass: 'justify-content-center ',
            filterableType: 'options', filterable: true,
          },
          {
            name: 'containerNo', label: T('Container No'),
            fieldDataGetter(bill) {
              let mode = bill['mode'];
              if (TMSBillTransportationModeTools.isExport(mode)) {
                return bill['containerNo'];
              } else {
                return bill['tmsContainerNo'];
              }
            },
          },
          {
            name: 'sealNo', label: T('Seal No'),
            fieldDataGetter(bill) {
              let mode = bill['mode'];
              if (TMSBillTransportationModeTools.isExport(mode)) {
                return bill['sealNo'];
              } else {
                return bill['tmsSealNo'];
              }
            },
          },
          {
            name: 'address', label: T('Địa chỉ'), width: 250, dataTooltip: true,
            fieldDataGetter(record) {
              let mode = record['mode'];
              if (TMSBillTransportationModeTools.isExport(mode)) {
                return record['pickupAddress'];
              }
              if (TMSBillTransportationModeTools.isImport(mode)) {
                return record['deliveryAddress'];
              }
              return `${record['pickupAddress']} - ${record['deliveryAddress']}`;
            },
          },
          {
            name: 'round', label: T('Tuyến đường'), width: 400, dataTooltip: true,
            fieldDataGetter(record) {
              let mode = record['mode'];
              let whLabel;
              let senderLabel = record['pickupInvAddress'];
              let receiverLabel = record['deliveryInvAddress'];
              if (TMSBillTransportationModeTools.isExport(mode)) {
                whLabel = record['deliveryAddress'];
                receiverLabel = `${whLabel}, ${receiverLabel}`;
              }
              if (TMSBillTransportationModeTools.isImport(mode)) {
                whLabel = record['pickupAddress'];
                senderLabel = `${whLabel}, ${senderLabel}`;
              }

              let stopLocations: any[] = record['stopLocations'];
              let locationLabels: any[] = [];
              if (stopLocations && stopLocations.length > 0) {
                for (let stopLocation of stopLocations) {
                  const locationLabel = stopLocation['locationLabel'];
                  if (locationLabel) {
                    let split: any[] = locationLabel.split(',');
                    if (split.length > 3) {
                      let first = split[0];
                      let end = split[split.length - 2];
                      locationLabels.push(`${first.trim()}, ${end.trim()}`);
                    }
                  }
                }
              }

              let label = `${senderLabel} - ${receiverLabel}`;
              if (locationLabels.length > 0) {
                label = `${senderLabel} - ${locationLabels.join(' - ')} - ${receiverLabel}`;
              }
              label = label.replaceAll('Xã', '').replaceAll('Phường', '').replaceAll('Thị trấn', '').trim();
              label = label.replaceAll('Tỉnh', '').replaceAll('Thành phố', '').trim();
              label = label.replaceAll("  ", " ");
              return label;
            },
          },
          {
            name: 'licensePlate', label: T('Biển số xe'), width: 120,
            filterableType: 'options', filterable: true,
            fieldDataGetter: (record) => {
              let trackings: Array<any> = record['trackings'];
              if (trackings) {
                return trackings.map(vendorBill => vendorBill.licensePlate).join(', ');
              }
              return null;
            },
          },
          {
            name: 'driver', label: T('Lái xe'), width: 120,
            filterableType: 'options', filterable: true,
            fieldDataGetter: (record) => {
              let trackings: Array<any> = record['trackings'];
              if (trackings) {
                return trackings.map(vendorBill => vendorBill.driverFullName).join(', ');
              }
              return null;
            },
          },
          {
            name: 'mobile', label: T('Mobile'), width: 120,
            filterableType: 'options', filterable: true,
            fieldDataGetter: (record) => {
              let trackings: Array<any> = record['trackings'];
              if (trackings) {
                return trackings.map(vendorBill => vendorBill.driverMobile).join(', ');
              }
              return null;
            },
          },
          {
            name: 'driverIdentificationNo', label: T('ID'), width: 120,
            filterableType: 'options', filterable: true,
            fieldDataGetter: (record) => {
              let trackings: Array<any> = record['trackings'];
              if (trackings) {
                return trackings.map(vendorBill => vendorBill.driverIdentificationNo).join(', ');
              }
              return null;
            },
          },
          {
            name: 'feedback', label: T(`Phản hồi`), width: 200
          },
          {
            name: 'tmsBillFixedPayment', label: T(`Cước`), dataType: 'double'
          },
          {
            name: 'tmsBillExtraPayment', label: T(`Cước phát sinh`), dataType: 'double'
          },
          {
            name: 'tmsBillTotalPayment', label: T('Tổng'), dataType: 'double'
          },
          {
            name: 'fixed', label: T('Cước (Thầu phụ)'), dataType: 'double'
          },
          {
            name: 'extra', label: T('Cước phát sinh (Thầu phụ)'), width: 200, dataType: 'double'
          },
          {
            name: 'cost', label: T('Tổng (Thầu phụ)'), dataType: 'double'
          },
        ],

      },

      toolbar: {
        actions: [
        ],
        filters: entity.DbEntityListConfigTool.TOOLBAR_FILTERS(!!plugin.searchParams),
      },
      footer: {
        page: {
          render: (ctx: grid.VGridContext) => {
            return (
              <bs.Toolbar className='border' >
                <XLSXCustomButton className="p-2 my-1" tableName="vendor-bill"
                  context={ctx} name={T('Export')}
                  options={{ fileName: `Exported ${util.TimeUtil.toCompactDateFormat(new Date())}.xlsx`, modelName: 'vendor-bill' }}
                  appContext={appContext} pageContext={pageContext} fieldSelect={'all'} selectMode="all" />
              </bs.Toolbar>
            )
          }
        },
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          },
        }
      }
    }
    return config;
  }
}