import React, { Component } from 'react';
import * as FeatherIcon from 'react-feather'
import { app, server, grid, util, input, entity, bs, sql } from "@datatp-ui/lib";
import { module } from "@datatp-ui/erp";

import { T } from './backend';
import { TMSBillProcessStatus, TMSRoundUsedStatus, VehicleTripStatus } from "./models"
export class TMSBillTransportationModeTools {
  static isExport = (mode: string) => {
    if (mode && mode.toUpperCase().includes('EXPORT')) return true;
    return false;
  }

  static isImport = (mode: string) => {
    if (mode && mode.toUpperCase().includes('IMPORT')) return true;
    return false;
  }

  static isLCL = (mode: string) => {
    if (mode && (mode.toUpperCase().includes('LCL') || mode.toUpperCase().includes('AIR'))) return true;
    return false;
  }
  static isFCL = (mode: string) => {
    if (mode && (mode.toUpperCase().includes('FCL'))) return true;
    return false;
  }

  static isDomestic = (mode: string) => {
    if (mode && (mode.toUpperCase().includes('DOMESTIC') || mode.toUpperCase().includes('CBT'))) return true;
    return false;
  }

  static group = (mode: string): string => {
    if (this.isFCL(mode)) return '1.FCL';
    if (this.isLCL(mode)) return '2.LCL';
    return '3.DOM';
  }

  static type = (mode: string): string => {
    if (this.isExport(mode)) return 'Export';
    if (this.isImport(mode)) return 'Import';
    return 'Domestic';
  }

  static getLabel = (mode: string) => {
    if (!mode) return '';
    return mode
      .toLowerCase()
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }
  static getLabelVn = (mode: string) => {
    if (!mode) return '';
    if (mode.toUpperCase().startsWith('EXPORT')) return 'XUẤT';
    if (mode.toUpperCase().startsWith('IMPORT')) return 'NHẬP';
    return 'CK';
  }

  static getColor = (mode: string) => {
    if (this.isImport(mode)) return 'text-success'
    if (this.isExport(mode)) return 'text-warning'
    if (this.isDomestic(mode)) return 'text-info'
    return "";
  }
}

export class TMSBillProcessStatusTools {
  static getLabel = (mode: TMSBillProcessStatus) => {
    switch (mode) {
      case TMSBillProcessStatus.FOLLOWING: return 'Following';
      case TMSBillProcessStatus.PENDING: return 'Pending';
      case TMSBillProcessStatus.PROCESSING: return 'Processing';
      case TMSBillProcessStatus.DONE: return 'Done';
      case TMSBillProcessStatus.CANCELED: return 'Canceled';
      default: return '';
    }
  }

  static getTextColor = (mode: TMSBillProcessStatus) => {
    return `text-${this.getColor(mode)}`
  }

  static getColor = (mode: TMSBillProcessStatus) => {
    switch (mode) {
      case TMSBillProcessStatus.FOLLOWING: return 'info';
      case TMSBillProcessStatus.PENDING: return 'warning';
      case TMSBillProcessStatus.PROCESSING: return 'primary';
      case TMSBillProcessStatus.DONE: return 'success';
      case TMSBillProcessStatus.CANCELED: return 'danger';
      default: return 'info';
    }
  }

  static getIcon = (status: TMSBillProcessStatus) => {
    switch (status) {
      case TMSBillProcessStatus.FOLLOWING:
        return <FeatherIcon.FileText size={12} className={this.getColor(status)} />;
      case TMSBillProcessStatus.PENDING:
        return <FeatherIcon.AlertTriangle size={12} className={this.getColor(status)} />;
      case TMSBillProcessStatus.PROCESSING:
        return <FeatherIcon.Loader size={12} className={this.getColor(status)} />;
      case TMSBillProcessStatus.DONE:
        return <FeatherIcon.CheckCircle size={12} className={this.getColor(status)} />;
      case TMSBillProcessStatus.CANCELED:
        return <FeatherIcon.X size={12} className={this.getColor(status)} />;
      default:
        break;
    }
  }
}

export class TMSRoundUsedStatusTools {
  static getBgColor = (status: TMSRoundUsedStatusTools) => {
    return `bg-${this.getColor(status)}`
  }

  static getTextColor = (status: TMSRoundUsedStatusTools) => {
    return `text-${this.getColor(status)}`
  }

  static getColor = (status: TMSRoundUsedStatusTools) => {
    switch (status) {
      case TMSRoundUsedStatus.SendingFromBill: return `warning`
      case TMSRoundUsedStatus.SendingFromRu: return `warning`
      case TMSRoundUsedStatus.Processing: return 'primary';
      case TMSRoundUsedStatus.Done: return 'success';
      case TMSRoundUsedStatus.Cancel: return 'danger';
      default: return 'info';
    }
  }
}

export class VehicleTripStatusTools {
  static getLabel = (status: VehicleTripStatus) => {
    switch (status) {
      case VehicleTripStatus.PLAN: return 'Kế Hoạch';
      case VehicleTripStatus.SUBMITTED_PLAN: return 'Chờ';
      case VehicleTripStatus.TRANSPORTING: return 'Chạy';
      case VehicleTripStatus.DONE: return 'Hoàn Thành';
      default: return '';
    }
  }

  static getIcon = (status: VehicleTripStatus, size: number = 50) => {
    switch (status) {
      case VehicleTripStatus.PLAN: return <FeatherIcon.FileText className={this.getColor(status)} size={size} />;
      case VehicleTripStatus.TRANSPORTING: return <FeatherIcon.Truck className={this.getColor(status)} size={size} />;
      case VehicleTripStatus.DONE:
        return (
          <div className={`${this.getColor(status)} flex-vbox justify-content-center align-items-center`}
            style={{
              width: size + 40, height: size + 40, border: '5px double green', borderRadius: 50, transform: 'rotate(25deg)'
            }}>
            DONE
          </div>
        );
      default: return '';
    }
  }

  static getColor = (status: VehicleTripStatus) => {
    switch (status) {
      case VehicleTripStatus.PLAN: return 'text-warning';
      case VehicleTripStatus.TRANSPORTING: return 'text-primary';
      case VehicleTripStatus.DONE: return 'text-success';
      default: return '';
    }
  }

  static getButtonColor = (status: VehicleTripStatus) => {
    switch (status) {
      case VehicleTripStatus.PLAN: return 'info';
      case VehicleTripStatus.TRANSPORTING: return 'primary';
      case VehicleTripStatus.DONE: return 'success';
      default: return 'info';
    }
  }
}

export class TMSUtils {
  static isDecimal = (str: string): boolean => {
    return str.match(/^[-+]?[0-9]+(\.[0-9]+)?$/) != null;
  }
  static renderFileAttachmentsIcon = (attachmentCount: number, onShowAttachList: () => void) => {
    let countIcon = (
      <div className={'align-items-center justify-content-center text-center'}
        style={{
          height: 12, width: 12,
          marginTop: -20, marginLeft: 5
        }}>
      </div>
    );
    if (attachmentCount) {
      countIcon = (
        <div className={'align-items-center justify-content-center text-center'}
          style={{
            height: 12, width: 12, borderRadius: 50, fontSize: 12,
            backgroundColor: 'red', color: 'white',
            marginTop: -20, marginLeft: 5
          }}>
          {attachmentCount}
        </div>
      );
    }
    return (
      <bs.Button className='flex-hbox flex-grow-0 text-warning align-items-left'
        style={{ width: 12, fontSize: 12, marginLeft: 5, marginBottom: 8 }}
        onClick={onShowAttachList} laf='link'>
        <FeatherIcon.FileText size={12} />
        {countIcon}
      </bs.Button>
    )
  }

  static reloadData(vgrid: entity.DbEntityList<any>, component: string, method: string, recordsResponse: Array<any>) {
    let { appContext, plugin } = vgrid.props;
    let records = plugin.getListModel().getModifiedRecords();
    let searchParams = JSON.parse(JSON.stringify(plugin.getSearchParams()));
    let ids: Array<any> = [];
    recordsResponse.forEach((record) => ids.push(record.id));
    searchParams.params['ids'] = ids;
    let params = { 'params': searchParams };

    appContext.createHttpBackendCall(component, method, params)
      .withSuccessData((data: any) => {
        let updatedRecords = data;
        grid.initRecordStates(updatedRecords);

        for (let record of records) {
          if (record.id) continue;
          for (let recordRes of recordsResponse) {
            if (record.uikey === recordRes.uikey) {
              record.id = recordRes.id;
            }
          }

        }
        for (let record of records) {
          for (let updatedRecord of updatedRecords) {
            if (record.id === updatedRecord.id) {
              for (let property in record) {
                if (updatedRecord[property] === undefined) delete record[property];
              }
              for (let property in updatedRecord) {
                record[property] = updatedRecord[property];
              }
            }
          }
        }
        vgrid.getVGridContext().getVGrid().forceUpdateView();
      })
      .call();
  }

  static renderTMSGridTooltip(element: any, content: any, width: number = 120, row: number = 2, cssConfig: { className?: any } = {}) {
    return (
      <bs.CssTooltip style={{ width: width }}>
        <bs.CssTooltipToggle className={'flex-hbox justify-content-start'}>
          {element}
        </bs.CssTooltipToggle>
        <bs.CssTooltipContent
          className={`align-items-end text-secondary`}
          style={{ whiteSpace: 'break-spaces' }}>
          <div className='p-2'>
            {content}
          </div>
        </bs.CssTooltipContent>
      </bs.CssTooltip>
    )
  }

  static renderTooltip = (content: any, html: any, width: number = 150) => {
    return (
      <bs.CssTooltip width={width} position='bottom-left'>
        <bs.CssTooltipToggle className='flex-hbox justify-content-start'>
          {html}
        </bs.CssTooltipToggle>
        <bs.CssTooltipContent style={{ whiteSpace: 'break-spaces' }}>
          {content}
        </bs.CssTooltipContent>
      </bs.CssTooltip>
    )
  }

}

export class TMSVGridConfigTool {
  static ENTITY_COLUMNS: Array<grid.FieldConfig> = [
    { name: 'modifiedBy', label: 'Modified By', state: { visible: false }, width: 130 },
    {
      name: 'modifiedTime', label: 'Modified Time', state: { visible: false },
      width: 150, cssClass: 'text-right', format: util.text.formater.compactDateTime
    },
    { name: 'storageState', label: 'State', width: 100, state: { visible: false } },
    {
      name: 'companyId', label: 'Company Id', state: { visible: false }, width: 100
    },
    { name: 'createdBy', label: 'Created By', state: { visible: false }, width: 100 },
    {
      name: 'createdTime', label: 'Created Time', state: { visible: false },
      width: 150, cssClass: 'text-right', format: util.text.formater.compactDateTime
    },
    { name: 'editState', label: 'EditState', state: { visible: false }, width: 100 },
  ];
}

export interface HeaderCellProps extends grid.VGridContextProps {
  className?: string;
  style?: any;
  field: grid.FieldConfig;
  onSelect?(): void;
}
export class CustomSelectorHeaderCell extends Component<HeaderCellProps> {
  toggleSelectAllRow = (checked: boolean) => {
    let { context, onSelect } = this.props;
    let state = grid.VGridConfigUtil.getRecordConfigState(context.config);
    state.selectAll = !state.selectAll;
    context.model.getDisplayRecordList().markSelectAllDisplayRecords(checked);
    context.getVGrid().forceUpdateView();
    if (onSelect) onSelect();
    this.forceUpdate();
  }

  render() {
    let { context } = this.props;
    let state = grid.VGridConfigUtil.getRecordConfigState(context.config);
    let cellUI = (
      <div className='flex-hbox justify-content-center'>
        <input.WCheckboxInput className='m-0 p-0' name='selectAll' checked={state.selectAll} onInputChange={this.toggleSelectAllRow} />
      </div>
    );
    return cellUI;
  }
}

export type RestCall = {
  component: string;
  method: string;
  param?: any;
}
class AttachmentPlugin {
  renderCustomFields(attachment: any): any { return null; }
}
interface UIAttachmentsProps extends app.AppComponentProps {
  // syncWithOriginAuthorization?: string;
  attachments?: Array<any>;
  commitMethod?: RestCall;
  loadMethod?: RestCall;
  hide?: boolean;
  onChange?: (plugin: entity.VGridEntityListEditorPlugin) => void
}
export class UIVendorAttachments extends app.AppComponent<UIAttachmentsProps> {
  plugin: entity.VGridEntityListEditorPlugin;

  constructor(props: UIAttachmentsProps) {
    super(props);
    let { attachments } = props;
    this.plugin = new entity.VGridEntityListEditorPlugin([]);
    if (attachments) {
      this.plugin.getModel().update(attachments);
    } else {
      this.loadData(true);
      this.markLoading(true);
    }
  }

  loadData(_forceReload: boolean = true) {
    let { appContext, loadMethod, onChange } = this.props;
    if (!loadMethod) throw new Error('Load Method Is Not Null!!!');
    appContext.createHttpBackendCall(loadMethod.component, loadMethod.method, loadMethod.param)
      .withSuccessData((data: any) => {
        let attachments = data;
        this.plugin.replaceBeans(attachments);
        if (onChange) onChange(this.plugin);
        this.markLoading(false).forceUpdate();
      })
      .call();
  }

  saveAttachments() {
    let { appContext, commitMethod, onChange } = this.props;
    if (!commitMethod) throw new Error('Commit Method Is Not Null!!!');
    let attachments: Array<any> = this.plugin.commitAndGet();
    appContext
      .createBackendCall(commitMethod.component, commitMethod.method, { ...commitMethod.param, "attachments": attachments })
      .withSuccessData((savedAttachments: Array<any>) => {
        this.plugin.replaceWithUpdate(savedAttachments);
        if (onChange) onChange(this.plugin);
        this.forceUpdate();
      })
      .withSuccessNotification("success", T(`Add Attachment Success`))
      .call();
  }

  onUploadSuccess = () => {
    this.saveAttachments();
  }

  render() {
    if (this.isLoading()) return this.renderLoading();
    let { appContext, pageContext, readOnly } = this.props;
    let attachmentPlugin = new AttachmentPlugin();
    let html = (
      <div className='flex-vbox'>
        <UIVendorPublicAttachmentListEditor
          plugin={this.plugin} appContext={appContext} pageContext={pageContext} readOnly={readOnly}
          attachmentPlugin={attachmentPlugin}
          onUploadSuccess={this.onUploadSuccess}
          dialogEditor={true} editorTitle={'Upload'} />
        <bs.Toolbar className='border' hide={readOnly}>
          <entity.WButtonEntityWrite
            appContext={appContext} pageContext={pageContext}
            icon={FeatherIcon.Upload} label={'Save'} onClick={() => this.saveAttachments()} />
        </bs.Toolbar>
      </div>
    );
    return html;
  }
}

class UIVendorPublicAttachmentListEditor extends module.storage.UIAttachmentListEditor {
  createVGridConfig() {
    let { readOnly } = this.props;
    if (readOnly === undefined) readOnly = true;
    let smallScreen = bs.ScreenUtil.isSmallScreen();
    let config: grid.VGridConfig = {
      title: T("Attachments"),
      record: {
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          entity.DbEntityListConfigTool.FIELD_ON_SELECT('label', T('Label'), 200),

          { name: 'name', label: 'Name', width: 200 },
          { name: 'logicalStoragePath', label: "Logical Storage", width: 300 },
          { name: 'size', label: 'Size', width: 100 },
          { name: 'description', label: 'Description', width: 300 },
          ...entity.DbEntityListConfigTool.FIELD_ENTITY
        ]
      },
      toolbar: {
        actions: [
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_ADD(readOnly, "Add"),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_DELETE(readOnly, "Del"),
        ]
      },
      view: {
        currentViewName: 'grid',
        availables: {
          table: {
            viewMode: 'table'
          },
          grid: {
            viewMode: 'grid',
            column: smallScreen ? 2 : 4,
            rowHeight: 150,
            renderRecord: (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
              let uiList = _ctx.uiRoot as UIVendorPublicAttachmentListEditor;
              let { appContext, pageContext, plugin } = uiList.props;
              const attachment = dRecord.record;
              let onRemove = () => {
                plugin.getModel().removeRecord(attachment);
                _ctx.getVGrid().forceUpdateView();
              }
              let resourceUri: string = attachment.resourceUri;
              return (
                <WPublicPreviewThumbnail appContext={appContext} pageContext={pageContext}
                  label={attachment.label} url={resourceUri} onRemove={onRemove} />
              );
            }
          },
        }
      }
    }
    return config;
  }
}

type ThumbnailCreator = (ctx: app.AppContext, label: string, ext: string, url: string) => any;

let DefaultThumbnailCreator = (_ctx: app.AppContext, _label: string, ext: string, _url: string) => {
  let text = ext.toUpperCase();
  let html = (
    <div className='flex-vbox align-middle text-center pt-2'
      style={{ fontSize: '2em', textShadow: '2px 2px 4px #000' }}>
      {text}
    </div>
  );
  return html;
}

let ImageThumbnailCreator = (ctx: app.AppContext, _label: string, _ext: string, url: string) => {
  url = ctx.getServerContext().createServerURL(`/storage${url}`, {});
  let html = (
    <div className='flex-hbox justify-content-center'>
      <img src={url} height="70" />
    </div>
  );
  return html;
}
class ThumbnailFactory {
  thumbnails: Record<string, ThumbnailCreator> = {};

  constructor() {
    this.thumbnails['default'] = DefaultThumbnailCreator;
    this.thumbnails['png'] = ImageThumbnailCreator;
    this.thumbnails['jpg'] = ImageThumbnailCreator;
  }

  create(ctx: app.AppContext, label: string, url: string) {
    if (!url) url = label;
    let idx = url.lastIndexOf('.');
    let ext = 'N/A';
    if (idx > 0) {
      ext = url.substr(idx + 1);
    }
    if (ext === 'N/A') {
      idx = label.lastIndexOf('.');
      ext = label.slice(idx + 1, label.length);
    }
    let creator = this.thumbnails[ext];
    if (!creator) creator = this.thumbnails['default'];
    return creator(ctx, label, ext, url);
  }
}

const FACTORY = new ThumbnailFactory();


class WPublicPreviewThumbnail extends module.storage.WPreviewThumbnail {
  onPreview = () => {
    let { url, params } = this.props;
    module.storage.popupUIPublicStoragePreview(this.props, url, params);
  }

  render() {
    let { appContext, label, url, onRemove } = this.props;
    let downloadUrl = appContext.getServerContext().createServerURL(`/storage${url}`, { download: true });
    let html = (
      <div className='flex-vbox border m-1'>
        <div className='flex-hbox'>
          <div className='flex-hbox btn' onClick={this.onPreview} style={{ height: 80 }}>
            {FACTORY.create(appContext, label, url)}
          </div>
          <bs.Button laf='secondary' className='p-1 m-1' style={{ height: 30, width: 30 }} onClick={onRemove}>
            <FeatherIcon.X size={12} />
          </bs.Button>
        </div>
        <div className='flex-vbox-grow-0 justify-content-center border-top'>
          <a href={downloadUrl} target={'_blank'} download>
            <FeatherIcon.Download size={12} />
            {label}
          </a>
        </div>
      </div>
    );
    return html;
  }
}

interface WButtonListenDataChangeProps {
  icon?: FeatherIcon.Icon;
  label: string;
  autoLoadPeriod?: number;
  ctx: grid.VGridContext;
  severCallComponent: string;
  severCallMethod: string;
  searchParams: sql.SqlSearchParams;
  recordFilter?: entity.RecordFilter;
  onPostLoad?: (records: Array<any>) => Array<any>;
  onClick: (uiSource: WButtonListenDataChange) => void;
  renderNumber?: (filteredRecords: Array<any>) => void;
}
export class WButtonListenDataChange extends Component<WButtonListenDataChangeProps> {
  timerId: any;
  loadPeriod: number = 30;
  filteredRecords: Array<any> = [];
  loadFail: boolean = false;
  loading: boolean = false;
  notifiedRecords: Array<any> = [];
  poller: util.common.Poller;

  constructor(props: WButtonListenDataChangeProps) {
    super(props);
    let { autoLoadPeriod } = props;
    if (autoLoadPeriod) this.loadPeriod = autoLoadPeriod;
  }

  componentDidMount(): void {
    this.poller = new util.common.Poller(this.doLoadData);
    this.poller.start(this.loadPeriod);
  }

  componentWillUnmount(): void {
    this.poller.stop();
  }

  toastShow = (filteredRecords: Array<any>) => {
    let filters: Array<any> = filteredRecords;
    if (this.notifiedRecords.length > 0) {
      let recordIdFilter = new entity.ExcludeRecordIdFilter().addRecords(this.notifiedRecords);
      filters = recordIdFilter.filter(filteredRecords);
    }
    if (filters.length == 0) return;
    let html = (
      <div className="flex-vbox">
        ({filters.length}) {T('New Information')}
      </div>
    )
    bs.toastShow(
      html,
      { type: 'info', delay: 2000 },
      { position: 'absolute', top: 10, right: 50, zIndex: 2000, opacity: 0.9 });
    this.notifiedRecords.push(...filters);
  }

  onChange = (_updateRecords: Array<any>) => { }

  doLoadData = (callSourceType: any = 'UserCron', callBack?: () => void) => {
    let { ctx, severCallComponent, severCallMethod, recordFilter, searchParams, onPostLoad } = this.props;
    let uiRoot = ctx.uiRoot as entity.DbEntityList;
    let { appContext } = uiRoot.props;
    let params = {
      'sourceType': callSourceType,
      'params': searchParams
    };
    this.loading = true;
    this.forceUpdate();
    appContext.createHttpBackendCall(severCallComponent, severCallMethod, params)
      .withSuccessData((data: any) => {
        let records = data;
        if (onPostLoad) records = onPostLoad(records);
        if (recordFilter) {
          this.filteredRecords = recordFilter.filter(records);
        } else {
          this.filteredRecords = records;
        }
        if (callBack) {
          callBack();
        }
        this.loading = false;
        this.onChange(this.filteredRecords);
        this.forceUpdate();
      })
      .withFail((_response: server.BackendResponse) => {
        if (this.poller) {
          this.poller.stop();
        }
        this.loadFail = true;
        this.loading = false;
        this.forceUpdate();
      })
      .call();
  }

  renderNumberRec = () => {
    if (this.filteredRecords.length == 0) return;
    return (
      <div className="px-1"
        style={{
          height: 18, width: 25, borderRadius: 10,
          backgroundColor: 'red',
          color: 'white',
          marginTop: -15,
          marginRight: -10
        }}>
        {this.filteredRecords.length}
      </div>
    )
  }

  render() {
    let { label, icon, onClick, renderNumber } = this.props;
    let renderNumberRec: any = this.renderNumberRec();
    if (renderNumber) {
      renderNumberRec = renderNumber(this.filteredRecords);
    }
    return (
      <bs.Button laf='warning' onClick={() => {
        this.doLoadData('User', () => {
          this.poller.stop();
          this.poller.start(this.loadPeriod);
          onClick(this);
        });
      }}
        style={{ marginRight: 0 }} outline={this.filteredRecords.length > 0 ? false : true}>
        {this.loadFail ?
          <div className="flex-hbox">
            <div>
              <FeatherIcon.AlertCircle size={15} className="mx-1 text-danger" />{`${label}`}
            </div>
            {renderNumberRec}
          </div>
          : this.loading ?
            <div className="flex-hbox">
              <div>
                <FeatherIcon.Loader size={15} className="mx-1" style={{ animation: '0.75s linear infinite spinner-border' }} />
                {`${label}`}
              </div>
              {renderNumberRec}
            </div>
            :
            <div className="flex-hbox">
              <div>
                {icon ? bs.renderIcon(icon) : <FeatherIcon.MessageCircle size={15} className="mx-1" />}{`${label}`}
              </div>
              {renderNumberRec}
            </div>
        }
      </bs.Button>
    );
  }
}

export interface EditEmailsProps extends entity.AppDbEntityEditorProps {
  commitConfig: entity.CommitConfig;
}

export class EditEmails extends entity.AppDbEntityEditor<EditEmailsProps> {
  static onShowPopUpProcessEmails(uiRoot: entity.DbEntityEditor) {
    let { pageContext, observer } = uiRoot.props;
    const writeCap = pageContext.hasUserWriteCapability();
    let mutableBean = observer.getMutableBean();
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      let bean = {
        emails: ''
      }
      let onProcess = () => {
        let emails: string = bean.emails;
        if (!emails) {
          bs.notificationShow("danger", "Emails must not be null!");
          return;
        }
        emails = emails.replace(/,/g, ';');
        emails = emails.replace(/[\t\n]+/g, ';');
        emails = emails.replace(/\s/g, '');
        emails = emails.replace(/;{2,}/g, ';');
        if (mutableBean.emails) {
          mutableBean.emails.push(...emails.split(';'));
        } else {
          mutableBean.emails = emails.split(';');
        }
        pageCtx.back();
        uiRoot.nextViewId();
        uiRoot.forceUpdate();
      }
      return <div className='flex-vbox'>
        <input.BBTextField bean={bean} field={'emails'} label={'Emails'} style={{ height: '8em' }} disable={!writeCap} />
        <bs.Toolbar>
          <entity.WButtonEntityWrite
            icon={FeatherIcon.Edit}
            appContext={appCtx} pageContext={pageCtx} hide={!writeCap}
            label={T('Process')} onClick={() => onProcess()} />
        </bs.Toolbar>
      </div>
    }
    pageContext.createPopupPage('process-emails', `Process Emails`, createAppPage, { backdrop: 'static' });
  }
  render() {
    let { appContext, pageContext, observer, commitConfig } = this.props;
    const writeCap = pageContext.hasUserWriteCapability();
    let mutableBean = observer.getMutableBean();
    return (
      <div className='flex-vbox'>
        <bs.GreedyScrollable className='flex-vbox' style={{ height: 300 }}>
          <div className='my-2'>
            <div className='flex-hbox'>
              <bs.FormLabel>{T('Email')}</bs.FormLabel>
              <FeatherIcon.Edit className='mx-2 text-warning' style={{ cursor: 'pointer' }} size={12} onClick={() => EditEmails.onShowPopUpProcessEmails(this)} />
            </div>
            <input.BBStringArrayField key={this.viewId}
              bean={mutableBean} field={'emails'} disable={!writeCap}
              validators={[util.validator.EMAIL_VALIDATOR]} onInputChange={(bean, field, oldVal, newVal) => {
                if (newVal.length == 0) bean[field] = null;
              }} />
          </div>
        </bs.GreedyScrollable>
        <bs.Toolbar className='border' hide={!writeCap}>
          <entity.ButtonEntityCommit
            appContext={appContext} pageContext={pageContext} observer={observer}
            commit={commitConfig}
            onPostCommit={
              (entity) => {
                this.onPostCommit(entity);
                pageContext.back();
              }
            } />
        </bs.Toolbar>
      </div>
    )
  }
}