import React from "react";
import * as icon from 'react-feather';
import { app, bs, util } from '@datatp-ui/lib';
import { module } from "@datatp-ui/erp";

import { T } from './backend';
import space = app.space;

import { UIJobProjectTMSBillList, UITMSBillList, UITMSBillListPlugin } from './bill/UITMSBillList';
import { UITMSBillPrintApiPlugin } from "./api/TMSBillPrintPlugin";
import { UITMSBillReport } from "./bill/UITMSBillReport";
import { UITMSPartnerTab } from "./partner/UITMSPartnerTab";
import { UITMSLOLOInvoiceReconcileList, UIInvoiceReconcileListPlugin } from "./document/UIInvoiceReconcile";
import { UIInvoiceReconcileReport } from "./document/InvoiceReconcileReportList";
import { UITMSHouse<PERSON>illList, UITMSHouseBillListPlugin } from "./housebill/TMSHouseBillGeneralList";
import { UITMSBillReportRoute } from "./chart/RouteChartReport";

import UIApiPluginManager = module.security.UIApiPluginManager;
import { SystemTMSBillList, SystemTMSBillReport } from "./bill/UISystemTMSBillReport";
import { UITMSBillReportByVendorApiPlugin } from "./chart/TMSBillVendorReport";
class TMSSpacePlugin extends space.SpacePlugin {
  constructor() {
    super('tms/tms-app', 'TMS Navigation');
  }

  override createUserComponents(): space.ComponentConfig[] {
    const widgets: space.ComponentConfig[] = [
      {
        id: "system:UserReportWidget",
        tags: new Set(["user", "tms"]),
        renderUI: (accountAcl: app.host.AccountAcl, appCtx: app.AppContext, pageCtx: app.PageContext) => {
          let html = (
            <div>TODO: Create TMS Widget</div>
          );
          return html;
        }
      }
    ]
    return widgets;
  }

  override createUserScreens(): space.ScreenConfig[] {
    return [
      {
        id: "tms", label: "TMS", icon: icon.FileText,
        checkPermission: {
          feature: { module: 'tms', name: 'user-tms-app' },
          requiredCapability: app.WRITE,
        },
        renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (
            <bs.TabPane>
              <bs.Tab name="tms-bill-report" label={'Bill Report'}>
                <UITMSBillReport appContext={appCtx} pageContext={pageCtx} />
              </bs.Tab>
              <bs.Tab name="route-report" label={'Route Report'} active>
                <UITMSBillReportRoute appContext={appCtx} pageContext={pageCtx} />
              </bs.Tab>
              <bs.Tab name="fcl-reconcile-report" key="fcl-reconcile-report" label={'FCL Reconcile Report'}>
                <UIInvoiceReconcileReport appContext={appCtx} pageContext={pageCtx} invoice_type={'FCL'} />
              </bs.Tab>
              <bs.Tab name="lcl-reconcile-report" key="lcl-reconcile-report" label={'LCL Reconcile Report'}>
                <UIInvoiceReconcileReport appContext={appCtx} pageContext={pageCtx} invoice_type={'LCL'} />
              </bs.Tab>
            </bs.TabPane>
          );
        },
        screens: [
          {
            id: "tms-bill", label: T("TMS Bill"),
            checkPermission: {
              feature: { module: 'tms', name: 'user-tms-app' },
              requiredCapability: app.WRITE,
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return <TMSBillScreenTabs appContext={appCtx} pageContext={pageCtx} space="user" />
            }
          },
          {
            id: "tms-house-bill", label: T("House Bill"),
            checkPermission: {
              feature: { module: 'tms', name: 'user-tms-app' },
              requiredCapability: app.WRITE,
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return (
                <UITMSHouseBillList
                  appContext={appCtx} pageContext={pageCtx} plugin={new UITMSHouseBillListPlugin().withDataScope(pageCtx.getUserDataScope())} type="page" />
              );
            }
          },
          {
            id: "tms-lolo", label: T("TMS Reconcile"),
            checkPermission: {
              feature: { module: 'tms', name: 'user-tms-app' },
              requiredCapability: app.WRITE,
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return (
                <bs.TabPane>
                  <bs.Tab key='fcl-invoice-reconcile' name="fcl-invoice-reconcile" label={'FCL Invoice Reconcile'} active>
                    <UITMSLOLOInvoiceReconcileList appContext={appCtx} pageContext={pageCtx} invoice_type={'FCL'}
                      plugin={new UIInvoiceReconcileListPlugin('FCL').withDataScope(app.AppDataScope.OWNER)} />
                  </bs.Tab>
                  <bs.Tab key='lcl-invoice-reconcile' name="lcl-invoice-reconcile" label={'LCL Invoice Reconcile'}>
                    <UITMSLOLOInvoiceReconcileList appContext={appCtx} pageContext={pageCtx} invoice_type={'LCL'}
                      plugin={new UIInvoiceReconcileListPlugin('LCL').withDataScope(app.AppDataScope.OWNER)} />
                  </bs.Tab>
                </bs.TabPane>
              )
            }
          },
          {
            id: "tms-partner", label: T("TMS Partners"),
            checkPermission: {
              feature: { module: 'tms', name: 'user-tms-app' },
              requiredCapability: app.WRITE,
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return (<UITMSPartnerTab appContext={appCtx} pageContext={pageCtx} />);
            }
          },
        ]
      }
    ];
  }

  override createCompanyScreens(): space.ScreenConfig[] {
    return [
      {
        id: "tms", label: "TMS", icon: icon.FileText,
        checkPermission: {
          feature: { module: 'tms', name: 'user-tms-app' },
          requiredCapability: app.WRITE,
        },
        renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (
            <bs.TabPane>
              <bs.Tab name="tms-bill-report" label={'Bill Report'} active>
                <UITMSBillReport appContext={appCtx} pageContext={pageCtx} />
              </bs.Tab>
              <bs.Tab name="fcl-reconcile-report" key="fcl-reconcile-report" label={'FCL Reconcile Report'}>
                <UIInvoiceReconcileReport appContext={appCtx} pageContext={pageCtx} invoice_type={'FCL'} />
              </bs.Tab>
              <bs.Tab name="lcl-reconcile-report" key="lcl-reconcile-report" label={'LCL Reconcile Report'}>
                <UIInvoiceReconcileReport appContext={appCtx} pageContext={pageCtx} invoice_type={'LCL'} />
              </bs.Tab>
            </bs.TabPane>
          );
        },
        screens: [
          {
            id: "tms-bill", label: T("TMS Bill"),
            checkPermission: {
              feature: { module: 'tms', name: 'user-tms-app' },
              requiredCapability: app.WRITE,
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return <TMSBillScreenTabs appContext={appCtx} pageContext={pageCtx} space="company" />
            }
          },
          {
            id: "tms-house-bill", label: T("House Bill"),
            checkPermission: {
              feature: { module: 'tms', name: 'user-tms-app' },
              requiredCapability: app.WRITE,
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return (
                <UITMSHouseBillList
                  appContext={appCtx} pageContext={pageCtx} plugin={new UITMSHouseBillListPlugin()} type="page" />
              );
            }
          },
          {
            id: "tms-reconcile", label: T("TMS Reconcile"),
            checkPermission: {
              feature: { module: 'tms', name: 'user-tms-app' },
              requiredCapability: app.WRITE,
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return (
                <bs.TabPane>
                  <bs.Tab key='fcl-invoice-reconcile' name="fcl-invoice-reconcile" label={'FCL Invoice Reconcile'} active>
                    <UITMSLOLOInvoiceReconcileList appContext={appCtx} pageContext={pageCtx} invoice_type={'FCL'}
                      plugin={new UIInvoiceReconcileListPlugin('FCL')} />
                  </bs.Tab>
                  <bs.Tab key='lcl-invoice-reconcile' name="lcl-invoice-reconcile" label={'LCL Invoice Reconcile'}>
                    <UITMSLOLOInvoiceReconcileList appContext={appCtx} pageContext={pageCtx} invoice_type={'LCL'}
                      plugin={new UIInvoiceReconcileListPlugin('LCL')} />
                  </bs.Tab>
                </bs.TabPane>
              )
            }
          },
          {
            id: "tms-partner", label: T("TMS Partners"),
            checkPermission: {
              feature: { module: 'tms', name: 'user-tms-app' },
              requiredCapability: app.WRITE,
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return (<UITMSPartnerTab appContext={appCtx} pageContext={pageCtx} />);
            }
          }
        ]
      }
    ];
  }
}
class TMSBillCompanySpacePlugin extends space.SpacePlugin {
  constructor() {
    super('tms/tms-bill-company', 'TMS Navigation');
  }

  override createUserScreens(): space.ScreenConfig[] {
    return [
      {
        id: "tms-bill-company", label: "TMS View", icon: icon.Eye,
        checkPermission: {
          feature: { module: 'tms', name: 'tms-bill-company' },
          requiredCapability: app.READ,
        },
        renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          let dateRange = new util.TimeRange();
          dateRange.fromSetDate(new Date());
          dateRange.fromSubtract(2, 'month');
          dateRange.fromStartOf('month');
          dateRange.toEndOf('month');
          return (
            <UITMSBillList type='page'
              plugin={new UITMSBillListPlugin().withDataScope(pageCtx.getUserDataScope()).withCustomerPermission()}
              appContext={appCtx} pageContext={pageCtx} readOnly />
          )
        },
        screens: [
          {
            id: "tms-reconcile", label: T("TMS Reconcile"),
            checkPermission: {
              feature: { module: 'tms', name: 'tms-bill-company' },
              requiredCapability: app.READ,
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return (
                <bs.TabPane>
                  <bs.Tab key='fcl-invoice-reconcile' name="fcl-invoice-reconcile" label={'FCL Invoice Reconcile'} active>
                    <UITMSLOLOInvoiceReconcileList appContext={appCtx} pageContext={pageCtx} invoice_type={'FCL'}
                      plugin={new UIInvoiceReconcileListPlugin('FCL')} />
                  </bs.Tab>
                  <bs.Tab key='lcl-invoice-reconcile' name="lcl-invoice-reconcile" label={'LCL Invoice Reconcile'}>
                    <UITMSLOLOInvoiceReconcileList appContext={appCtx} pageContext={pageCtx} invoice_type={'LCL'}
                      plugin={new UIInvoiceReconcileListPlugin('LCL')} />
                  </bs.Tab>
                </bs.TabPane>
              )
            }
          },
        ]
      }
    ];
  }


  createSystemScreens(): app.space.ScreenConfig[] {
    let configs: space.ScreenConfig[] = [
      {
        id: "tms-bill-report", label: "TMS Report", icon: icon.FileText,
        checkPermission: {
          feature: { module: 'tms', name: 'tms-bill-company' },
          requiredCapability: app.READ,
        },
        renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (
            <SystemTMSBillReport appContext={appCtx} pageContext={pageCtx} />
          );
        },
        screens: [
          {
            id: 'tms-bill-company', label: 'TMS Bill', icon: icon.FileText,
            checkPermission: {
              feature: { module: 'tms', name: 'tms-bill-company' },
              requiredCapability: app.READ,
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return <SystemTMSBillList appContext={appCtx} pageContext={pageCtx} readOnly
              />
            }
          },
        ]
      }
    ]
    return configs;
  }
}

class TMSReconcileSpacePlugin extends space.SpacePlugin {
  constructor() {
    super('tms/tms-reconcile', 'TMS Reconcile');
  }


  override createUserScreens(): space.ScreenConfig[] {
    return [
      {
        id: "tms-lolo-reconcile", label: "TMS Reconcile", icon: icon.File,
        checkPermission: {
          feature: { module: 'tms', name: 'tms-reconcile' },
          requiredCapability: app.READ,
        },
        renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (
            <bs.TabPane>
              <bs.Tab key='fcl-invoice-reconcile' name="fcl-invoice-reconcile" label={'FCL Invoice Reconcile'} active>
                <UITMSLOLOInvoiceReconcileList appContext={appCtx} pageContext={pageCtx} invoice_type={'FCL'}
                  plugin={new UIInvoiceReconcileListPlugin('FCL').withDataScope(pageCtx.getUserDataScope())} />
              </bs.Tab>
              <bs.Tab key='lcl-invoice-reconcile' name="lcl-invoice-reconcile" label={'LCL Invoice Reconcile'}>
                <UITMSLOLOInvoiceReconcileList appContext={appCtx} pageContext={pageCtx} invoice_type={'LCL'}
                  plugin={new UIInvoiceReconcileListPlugin('LCL').withDataScope(pageCtx.getUserDataScope())} />
              </bs.Tab>
            </bs.TabPane>
          );
        }
      }
    ];
  }

  override createCompanyScreens(): space.ScreenConfig[] {
    return [
      {
        id: "tms-lolo-reconcile", label: "TMS Reconcile", icon: icon.File,
        checkPermission: {
          feature: { module: 'tms', name: 'tms-reconcile' },
          requiredCapability: app.READ,
        },
        renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (
            <bs.TabPane>
              <bs.Tab key='fcl-invoice-reconcile' name="fcl-invoice-reconcile" label={'FCL Invoice Reconcile'} active>
                <UITMSLOLOInvoiceReconcileList appContext={appCtx} pageContext={pageCtx} invoice_type={'FCL'}
                  plugin={new UIInvoiceReconcileListPlugin('FCL')} />
              </bs.Tab>
              <bs.Tab key='lcl-invoice-reconcile' name="lcl-invoice-reconcile" label={'LCL Invoice Reconcile'}>
                <UITMSLOLOInvoiceReconcileList appContext={appCtx} pageContext={pageCtx} invoice_type={'LCL'}
                  plugin={new UIInvoiceReconcileListPlugin('LCL')} />
              </bs.Tab>
            </bs.TabPane>
          );
        }
      }
    ];
  }

}

interface TMSBillScreenTabsTabs extends app.AppComponentProps {
  space: 'company' | 'user'
}

export class TMSBillScreenTabs extends app.AppComponent<TMSBillScreenTabsTabs> {

  createDateRange(type: 'COSTING' | 'TRUCK_INFO') {
    let dateRange: util.TimeRange = new util.TimeRange();
    let fromDate = new Date();
    fromDate.setHours(0, 0, 0, 0);
    let toDate = new Date();
    if (type == 'COSTING') {
      fromDate.setDate(fromDate.getDate() - 13);
      toDate.setDate(new Date().getDate() - 4);
    } else if (type == 'TRUCK_INFO') {
      fromDate.setDate(fromDate.getDate() - 10);
      toDate.setDate(new Date().getDate() - 1)
    }
    toDate.setHours(23, 59, 59, 999);
    dateRange.fromSetDate(fromDate)
    dateRange.toSetDate(toDate);
    return dateRange;
  }

  render() {
    let { appContext, pageContext, space } = this.props;
    let dataScope = pageContext.getUserDataScope();
    if (space == 'company') {
      dataScope = app.AppDataScope.COMPANY
    }
    let config: bs.TabPaneConfig = {
      tabs: [
        {
          name: 'all', label: 'All', active: true,
          renderContent: (_ctx: bs.UIContext) => {
            return (
              <UIJobProjectTMSBillList
                appContext={appContext} pageContext={pageContext}
                listPlugin={
                  new UITMSBillListPlugin('INTERNAL_BILL')
                    .withDataScope(dataScope)
                    .hasModeratorPermission(pageContext.hasUserModeratorCapability())
                }
              />
            );
          }
        },
        {
          name: 'costing', label: 'Costing',
          renderContent: (_ctx: bs.UIContext) => {
            return (
              <UITMSBillList key={util.IDTracker.next()} type='page' view='COSTING'
                plugin={new UITMSBillListPlugin('INTERNAL_BILL')
                  .withDataScope(dataScope)
                  .hasModeratorPermission(pageContext.hasUserModeratorCapability()).withFilterByCosting()
                  .withDateTime(this.createDateRange('COSTING'), false, false).withPushBfsOneCost(false)}
                appContext={appContext} pageContext={pageContext} readOnly={!pageContext.hasUserWriteCapability()} />
            );
          }
        },
        {
          name: 'truck-info', label: 'Truck Info',
          renderContent: (_ctx: bs.UIContext) => {
            return (
              <UITMSBillList key={util.IDTracker.next()} type='page' view='TRUCK_INFO'
                plugin={new UITMSBillListPlugin('INTERNAL_BILL')
                  .withDataScope(dataScope)
                  .hasModeratorPermission(pageContext.hasUserModeratorCapability()).withFilterByTruckInfo()
                  .withDateTime(this.createDateRange('TRUCK_INFO'), false, false).withPushBfsOneVehicleInfo(false)
                }
                appContext={appContext} pageContext={pageContext} readOnly={!pageContext.hasUserWriteCapability()} />
            );
          }
        },
      ]
    };
    return (
      <bs.DefaultTabPane className="flex-vbox" config={config} />
    )
  }
}

export function init() {
  space.SpacePluginManager.register(new TMSSpacePlugin());
  space.SpacePluginManager.register(new TMSBillCompanySpacePlugin());
  space.SpacePluginManager.register(new TMSReconcileSpacePlugin());
  UIApiPluginManager.register(new UITMSBillPrintApiPlugin());
  UIApiPluginManager.register(new UITMSBillReportByVendorApiPlugin());
}