import React from 'react';
import {
  <PERSON>, <PERSON>A<PERSON>s, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, Composed<PERSON><PERSON>, <PERSON><PERSON><PERSON>
} from 'recharts';

import * as FeatherIcon from 'react-feather';
import { app, chart, grid, util, entity, sql, bs, input } from "@datatp-ui/lib";
import { TMSBillTransportationModeTools } from '../utils';
import { WBtnRouteConfig } from 'app/logistics/vendor/UITMSVendorRouteTargetList';
import { T } from '../backend';
import { UITMSBillList, UITMSBillListPlugin } from '../bill/UITMSBillList';
import { VendorReportCssToolTip } from './TMSBillVendorReport';
import { XLSXCustomButton } from '../XLSXButton';

const COLOR = [
  '#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#0000ff'
]

class ReportTMSBillListPlugin extends entity.DbEntityListPlugin {
  constructor(dateRange: util.TimeRange, targetBy: 'day' | 'month' = 'month') {
    super([]);
    this.backend = {
      context: 'company',
      service: 'TMSRestCallService',
      searchMethod: 'searchTMSBills'
    }
    this.searchParams = {
      "params": { targetBy: targetBy },
      "optionFilters": [
        sql.createStorageStateFilter([entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED])
      ],
      "rangeFilters": [
        ...sql.createDateTimeFilter("deliveryPlan", "Date", dateRange),
      ],
      maxReturn: 10000,
    }
  }

  withCompanyCode(companyCode: any) {
    this.addSearchParam("companyCode", companyCode);
    return this;
  }
}

interface ReportRouteBarChartProps {
  width?: number;
  height?: number;
  data: any[];
  label: any;
  barNameOption: any;
  layout?: 'vertical' | 'horizontal'
}
export class ReportRouteBarChart extends React.Component<ReportRouteBarChartProps> {
  chartData: any[] = [];
  barName: Set<String> = new Set<String>();

  buildVerticalChart() {
    let { width, height, data, barNameOption } = this.props;
    if (!width) width = 900;
    if (!height) height = 35 * data.length;
    let bars: any[] = [];
    let barNames: any[] = [];
    if (barNameOption) {
      for (let propertyName in barNameOption) {
        if (barNameOption[propertyName]) barNames.push(propertyName);
      }
      barNames.forEach((key, index) => {
        if (index == barNames.length - 1) {
          bars.push(
            <Bar
              dataKey={`${key}`} stackId={'actual'} barSize={20} fill={COLOR[index]} label={{ position: 'right' }} yAxisId="one" />
          )
        } else {
          bars.push(
            <Bar
              dataKey={`${key}`} stackId={'actual'} barSize={20} fill={COLOR[index]} yAxisId="one" />
          )
        }
      })
    }
    return (
      <div className='d-flex justify-content-start'>
        <ComposedChart
          layout="vertical"
          width={width}
          height={height}
          data={data}
          margin={{
            right: 20,
            bottom: 20,
          }}
        >
          <CartesianGrid stroke="#f5f5f5" />
          <XAxis type="number" />
          <YAxis dataKey="name" type="category" width={120} fontSize={11} yAxisId="one" />
          <YAxis hide dataKey="name" type="category" width={120} fontSize={11} yAxisId="two" />
          <Tooltip />
          <Legend />
          <Bar dataKey={'target'} stackId={'target'} barSize={50} fill={'#C0C0C0'} yAxisId="two" />
          {bars}
        </ComposedChart>
      </div>
    )
  }

  buildHorizontalChart() {
    let { width, height, data, barNameOption, label } = this.props;
    let rWidth = data ? data.length * 35 : 0;
    if (rWidth < window.innerWidth / 3) rWidth = window.innerWidth / 3.5;
    if (!width) width = rWidth;
    if (!height) height = window.innerHeight / 4;
    let bars: any[] = [];
    let barNames: any[] = [];
    if (barNameOption) {
      for (let propertyName in barNameOption) {
        if (barNameOption[propertyName]) barNames.push(propertyName);
      }
      barNames.forEach((key, index) => {
        if (index == barNames.length - 1) {
          bars.push(<Bar dataKey={`${key}`} stackId={'actual'} barSize={20} fill={COLOR[index]} label={{ position: 'top' }} xAxisId="one" />)
        } else {
          bars.push(<Bar dataKey={`${key}`} stackId={'actual'} barSize={20} fill={COLOR[index]} xAxisId="one" />)
        }
      })
    }
    return (
      <div className='flex-vbox justify-content-start text-center'>
        <h4>{label}</h4>
        <BarChart
          layout="horizontal"
          width={width}
          height={height}
          data={data}
          margin={{
            right: 20,
            bottom: 20,
          }}
        >
          <CartesianGrid stroke="#f5f5f5" />
          <XAxis dataKey="name" type="category" height={60} fontSize={11} xAxisId="one" angle={-90}
            tick={({ x, y, stroke, payload }) => {
              return (
                <g transform={`translate(${x},${y})`}>
                  <text textAnchor="end" fontSize={11} fill="#666" transform="rotate(-45)">
                    {payload.value}
                  </text>
                </g>
              );
            }}
          />
          <XAxis hide dataKey="name" type="category" width={120} fontSize={11} xAxisId="two" />
          <YAxis type="number" />
          <Tooltip />
          <Legend verticalAlign='top' height={60} />
          <Bar dataKey={'Target'} stackId={'target'} barSize={30} fill={'#C0C0C0'} xAxisId="two" />
          {bars}
        </BarChart>
      </div>
    )
  }

  render() {
    let { layout } = this.props;
    if (layout == 'vertical') return this.buildVerticalChart();
    return this.buildHorizontalChart();
  }
}

interface UITMSBillReportRouteProps extends app.AppComponentProps {
  companyCode?: any;
}
export class UITMSBillReportRoute extends app.AppComponent<UITMSBillReportRouteProps> {
  chartData: Record<string, any[]> = {
    'fcl': [],
    'lcl': [],
    'other': [],
  };
  bean: any = {
    reportBy: 'month',
  };
  rawData: any[];
  label: string = 'Loading...';
  barNameOption: any = {};
  routeTargets: any[] = [];
  dateRange = new util.TimeRange(new Date());
  componentDidMount(): void {
    this.loadDefault();
  }

  loadDefault() {
    this.dateRange = new util.TimeRange(new Date());
    this.dateRange.fromStartOf('month');
    this.dateRange.toEndOf('month');
    this.reloadData(this.dateRange);
  }

  reloadData = (dateRange: util.TimeRange) => {
    let { appContext, companyCode } = this.props;
    let plugin = new ReportTMSBillListPlugin(dateRange, this.bean.reportBy).withCompanyCode(companyCode);
    this.label = `${util.text.formater.compactDate(dateRange.fromFormat())} - ${util.text.formater.compactDate(dateRange.toFormat())}`;
    appContext.createHttpBackendCall('TMSReportService', 'routeDataAnalysis', { params: plugin.searchParams })
      .withSuccessData((data: any) => {
        this.routeTargets = data.routeTargets;
        this.rawData = data.bills;
        this.initData(this.rawData);
      })
      .call();
  }

  onLoadTarget = (reportBy: any) => {
    let { appContext, companyCode } = this.props;
    if (reportBy == 'month') {
      this.loadDefault();
    } else {
      appContext.createHttpBackendCall('TMSVendorRouteTargetService', 'findTargetActiveByTarget', { 'reportBy': reportBy, 'companyCode': companyCode })
        .withSuccessData((data: any) => {
          this.routeTargets = data;
          this.forceUpdate();
        })
        .call();
    }
  }

  initData = (rawData: any[], filterByRoute?: boolean) => {
    let data = [...rawData];
    if (filterByRoute) {
      let selectRoutes: any[] = [];
      for (let propertyName in this.barNameOption) {
        if (this.barNameOption[propertyName]) selectRoutes.push(propertyName)
      }
      data = data.filter(sel => {
        let route = sel['route'] ? sel['route'] : 'unknown';
        if (selectRoutes.includes(route)) return true;
      });
    }
    let barNames: Set<String> = new Set<String>();
    barNames.add('unknown');
    data.forEach(sel => {
      if (!sel['vendorFullName']) return;
      let mode = sel['mode'];
      if (sel['route']) {
        barNames.add(sel['route']);
      }
      if (TMSBillTransportationModeTools.isFCL(mode)) {
        this.chartData['fcl'].push(sel);
      } else if (TMSBillTransportationModeTools.isLCL(mode)) {
        this.chartData['lcl'].push(sel);
      } else {
        this.chartData['other'].push(sel);
      }
    });
    let barNameArray: any[] = [...barNames];
    barNameArray.sort((a: string, b: string) => a.localeCompare(b));
    barNameArray.forEach(name => {
      if (this.barNameOption[name] == undefined) this.barNameOption[name] = true;
    });
    this.forceUpdate();
  }

  buildReportRouteBarChart = () => {
    let charts: any[] = [];
    for (let key in this.chartData) {
      let records = this.chartData[key];
      let barChartData = new TMSBillReportComputeData(records).computeBarChartData(true);
      charts.push(
        <ReportRouteBarChart key={`${util.IDTracker.next()}-bar-chart`}
          label={key.toUpperCase()}
          barNameOption={this.barNameOption}
          data={barChartData} />
      )
    }
    return charts;
  }

  buildReportRouteBarChartByDay = () => {
    let chartMap: Record<string, any[]> = {};
    for (let type in this.chartData) {
      let records = this.chartData[type];
      let allReportModel = new chart.ReportModel("all", "All", records);
      let dayModelMap = allReportModel.groupByDate('deliveryPlan', 'month', 'YYYY-MM-DD', 'desc');
      for (let day in dayModelMap) {
        let dayReportMode = dayModelMap[day];
        let barChartData = new TMSBillReportComputeData(dayReportMode.records).computeBarChartData(true);
        const html = (
          <ReportRouteBarChart key={`${util.IDTracker.next()}-bar-chart`}
            label={type.toUpperCase()}
            barNameOption={this.barNameOption}
            data={barChartData} />
        );
        if (chartMap[day]) {
          chartMap[day].push(html);
        } else {
          chartMap[day] = [html];
        }
      }
    }
    let charts: any[] = [];
    for (let day in chartMap) {
      charts.push(
        <div className='text-start'>
          <h4>{day}</h4>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: 5 }}>
            {chartMap[day]}
          </div>
        </div>
      )
    }
    return charts;
  }

  mergerTarget = (barChartData: any[], routeTargetMap: Record<string, any>, type: any, multiplyBy: number = 1) => {
    if (!routeTargetMap) return;
    barChartData.forEach(rec => {
      let selectRoutes: any[] = [];
      for (let propertyName in this.barNameOption) {
        if (this.barNameOption[propertyName]) selectRoutes.push(propertyName.toLowerCase());
      }
      let target = routeTargetMap[rec.name];
      if (!target) return;
      let totalTripTarget = 0;
      if (target) {
        let items: any[] = target.items;
        items.forEach(item => {
          let route: string = item.route;
          route = route.toLowerCase();
          if (selectRoutes.includes(route)) {
            if (type && item.type == type) {
              totalTripTarget += item.totalTripTarget;
            } else if (!type) {
              totalTripTarget += item.totalTripTarget;
            }
          }
        })
      }
      rec['Target'] = totalTripTarget * multiplyBy;
    })
  }

  buildBarOption = () => {
    let options: any[] = [];
    let routes: any[] = [];
    for (let propertyName in this.barNameOption) {
      routes.push(propertyName);
    }
    routes.forEach((route, index) => {
      options.push(
        <input.BBCheckboxField style={{ fontSize: '1.2em', height: '1.2em', color: `${COLOR[index]}` }} key={route}
          bean={this.barNameOption} field={route} label={route} value={false}
          onInputChange={() => this.initData(this.rawData, false)}
        />
      )
    })
    return (
      <div className='flex-hbox gap-2 my-1'>
        {options}
      </div>
    );
  }

  buildRange = () => {
    let btns: any[] = [];
    if (this.bean.reportBy == 'month') {
      let curTimeRange = new util.TimeRange(new Date());
      for (let i = 0; i < 3; i++) {
        if (i > 0) curTimeRange.fromAdd(-1, 'month');
        let fromDate = curTimeRange.fromAsDate();
        let outline = true;
        if (this.dateRange.fromAsDate().getMonth() == fromDate.getMonth()) outline = false;
        btns.unshift(
          <bs.Button key={i} outline={outline} className='m-1 p-1' laf='info'
            onClick={() => {
              this.dateRange = new util.TimeRange(fromDate);
              this.dateRange.fromStartOf('month');
              this.dateRange.toEndOf('month');
              this.reloadData(this.dateRange);
            }}>
            {util.TimeUtil.format(fromDate, 'MM/YYYY')}
          </bs.Button>
        )
      }
    }
    return (
      <div className='flex-hbox' style={{ width: 300 }}>
        {btns}
        <input.BBDateTimeField
          bean={{ from: this.dateRange.fromFormat() }} field='from' timeFormat={false}
          onInputChange={(bean: any, field: string, oldVal: any, newVal: any) => {
            this.dateRange.fromSetDate(util.TimeUtil.parseCompactDateTimeFormat(newVal));
            if (this.bean.reportBy == 'month') {
              this.dateRange.toSetDate(util.TimeUtil.parseCompactDateTimeFormat(newVal));
              this.dateRange.toEndOf('month');
              this.forceUpdate();
            }
          }} />
        <input.BBDateTimeField className='mx-2' bean={{ to: this.dateRange.toFormat() }} field='to' timeFormat={false}
          onInputChange={(bean: any, field: string, oldVal: any, newVal: any) => {
            this.dateRange.toSetDate(util.TimeUtil.parseCompactDateTimeFormat(newVal));
          }} />
        <bs.Button className='m-1 p-1' laf='link' onClick={() => this.reloadData(this.dateRange)}>
          <FeatherIcon.Search size={16} />
        </bs.Button>
      </div>
    )
  }


  render() {
    let { appContext, pageContext } = this.props;
    if (!this.rawData) return (
      <div className="flex-vbox">
        <h4>{'Loading...'}</h4>
      </div>
    )

    let reportBy = this.bean.reportBy;
    let recordFCLs = this.rawData.filter((record: any) => TMSBillTransportationModeTools.isFCL(record['mode']));
    let computeFclRecs = new TMSBillReportComputeData(recordFCLs).computeData('fcl', reportBy, this.routeTargets);

    let recordLCLs = this.rawData.filter((record: any) => TMSBillTransportationModeTools.isLCL(record['mode']));
    let computeLclRecs = new TMSBillReportComputeData(recordLCLs).computeData('lcl', reportBy, this.routeTargets);

    let recordOthers = this.rawData.filter((record: any) => TMSBillTransportationModeTools.isDomestic(record['mode']));
    let computeOtherRecs = new TMSBillReportComputeData(recordOthers).computeData('other', reportBy, this.routeTargets);

    return (
      <div className='flex-vbox'>
        <div className='flex-grow-0 mx-1'>
          <div className='flex-hbox' style={{ width: 600 }}>
            <WBtnRouteConfig appContext={appContext} pageContext={pageContext} />
            <input.BBRadioInputField className='flex-hbox' bean={this.bean} field='reportBy'
              options={['day', 'month']} optionLabels={['DAY', 'MONTH']}
              onInputChange={(_bean: any, _field: string, _oldVal: any, newVal: any) => {
                this.onLoadTarget(newVal);
              }} />
            {this.buildRange()}
          </div>
        </div>
        <bs.TabPane>
          <bs.Tab name='chart' label='Chart'>
            <div className='flex-vbox'>
              <div className='flex-hbox flex-grow-0'>
                {this.buildBarOption()}
              </div>
              <bs.ScrollableCards className='flex-vbox m-2' key={`bar-chart-${util.IDTracker.next()}`}>
                <div className='flex-vbox text-center'>
                  <h3 className='text-primary my-1'>{T(`Report ${this.label}`)}</h3>
                  <div style={{
                    display: 'flex',
                    flexWrap: 'wrap'
                  }}>
                    {this.buildReportRouteBarChart()}
                  </div>
                </div>
                <div className='flex-vbox text-center'>
                  <h3 className='text-primary my-1'>{T('Report By Day')}</h3>
                  {this.buildReportRouteBarChartByDay()}
                </div>
              </bs.ScrollableCards>
            </div>
          </bs.Tab>
          <bs.Tab name='fcl' label='FCL data' active>
            <UITMSBillReportRouteList key={`fcl-${util.IDTracker.next()}`}
              viewReportBy={reportBy}
              appContext={appContext} pageContext={pageContext}
              roundCols={computeFclRecs.roundCols}
              plugin={new entity.DbEntityListPlugin(computeFclRecs.data)} />
          </bs.Tab>
          <bs.Tab name='lcl' label='LCL data'>
            <UITMSBillReportRouteList key={`lcl-${util.IDTracker.next()}`}
              viewReportBy={reportBy}
              appContext={appContext} pageContext={pageContext}
              roundCols={computeLclRecs.roundCols}
              plugin={new entity.DbEntityListPlugin(computeLclRecs.data)} />
          </bs.Tab>
          <bs.Tab name='other' label='Other data'>
            <UITMSBillReportRouteList key={`other-${util.IDTracker.next()}`}
              viewReportBy={reportBy}
              appContext={appContext} pageContext={pageContext}
              roundCols={computeOtherRecs.roundCols}
              plugin={new entity.DbEntityListPlugin(computeOtherRecs.data)} />
          </bs.Tab>

        </bs.TabPane>
      </div>
    )
  }
}

interface UITMSBillReportRouteListProps extends entity.DbEntityListProps {
  roundCols?: Record<string, string>;
  viewReportBy: 'day' | 'month';
}
export class UITMSBillReportRouteList extends entity.DbEntityList<UITMSBillReportRouteListProps> {
  buildRoundCols(): grid.FieldConfig[] {
    let { pageContext, roundCols } = this.props;
    let fields: grid.FieldConfig[] = [];
    if (!roundCols) return fields;
    for (let key in roundCols) {
      fields.push({
        label: roundCols[key],
        name: key,
        cssClass: 'flex-vbox text-end',
        dataType: 'double',
        fieldDataGetter(record) {
          let rounds: any[] = record['rounds'];
          if (!rounds) return null;
          let round = rounds.find(sel => sel.name === key);
          if (!round) return null;
          return round.totalFile;
        },
        onClick(_ctx, dRecord) {
          let rounds: any[] = dRecord.record['rounds'];
          if (!rounds) return null;
          let round = rounds.find(sel => sel.name === key);
          if (!round) return null;
          let billIds = round['billIds'];
          let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
            return <UITMSBillList appContext={appCtx} pageContext={pageCtx} readOnly plugin={new UITMSBillListPlugin().withIds(billIds)} />
          }
          pageContext.createPopupPage('tms-bill', 'TMS Bills', createAppPage, { size: 'xl' });
        },
      });
    };
    return fields;
  }

  buildFields(): grid.FieldConfig[] {
    let { appContext, pageContext, viewReportBy } = this.props;
    if (viewReportBy == 'day') {
      return [
        {
          label: T("Ngày"), name: "label", width: 120, sortable: true, filterable: true, filterableType: 'options', container: 'fixed-left',
          fieldDataGetter(record) {
            if (record.type === 'day') {
              return record['label'];
            }
            return null;
          },
        },
        {
          label: T("Thầu Phụ"), name: "vendorName", width: 250, sortable: true, filterable: true, filterableType: 'options',
          container: 'fixed-left',
          fieldDataGetter(record) {
            if (record.type != 'day') {
              return record['label'];
            }
            return null;
          },
          customRender(ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, focus: boolean) {
            const record = dRecord.record;
            let displayValue = record['label'];
            if (!displayValue) return;
            if (record.type === 'day') {
              displayValue = null;
            }
            let cssClass = field.computeCssClasses ? field.computeCssClasses(ctx, dRecord) : '';
            if (!record['rounds']) {
              return (
                <div className={cssClass}>
                  {displayValue}
                </div>
              );
            }
            return (
              <VendorReportCssToolTip className={cssClass}
                appContext={appContext} pageContext={pageContext} reportData={record} label={displayValue} />
            )
          }
        },
      ]
    } else {
      return [
        {
          label: T("Thầu Phụ"), name: "label", width: 250, sortable: true, filterable: true, filterableType: 'options',
          container: 'fixed-left',
          customRender(ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, focus: boolean) {
            const record = dRecord.record;
            let displayValue = record[field.name];
            if (!displayValue) return;
            if (record.type === 'day') {
              displayValue = null;
            }
            let cssClass = field.computeCssClasses ? field.computeCssClasses(ctx, dRecord) : '';
            if (!record['rounds']) {
              return (
                <div className={cssClass}>
                  {displayValue}
                </div>
              );
            }
            return (
              <VendorReportCssToolTip className={cssClass}
                appContext={appContext} pageContext={pageContext} reportData={record} label={displayValue} />
            )
          }
        },
      ]
    }
  }

  createVGridConfig() {
    let { appContext, } = this.props;
    let treePlugin = new grid.TreeDisplayModelPlugin();
    let CONFIG: grid.VGridConfig = {
      title: 'Data',
      record: {
        fields: [
          grid.createIndex("", 40, false),
          ...this.buildFields(),
          { label: T("Tuyến đường"), name: "routeName", width: 120 },
          { label: T("Sản lượng cam kết"), name: "totalTarget", width: 200, dataType: 'double' },
          ...this.buildRoundCols(),
          { label: T("Tổng sản lượng thực tế"), name: "totalFile", width: 200, dataType: 'double' },
          { label: T("Chênh lệch"), name: "difference", width: 120, dataType: 'double' },
          {
            label: T("%Đạt"), name: "complete", width: 120, dataType: 'percent',
            customRender(ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, focus: boolean) {
              const record = dRecord.record;
              let displayValue = record[field.name];
              if (!displayValue) return;
              let cssClass = field.computeCssClasses ? field.computeCssClasses(ctx, dRecord) : '';
              return (
                <div className={`flex-hbox justify-content-end ${cssClass}`}>
                  {displayValue}%
                </div>
              )
            }
          },
          { label: T("Note"), name: "note", width: 250 },
          {
            label: T("Link"), name: "_link", cssClass: 'text-danger',
            fieldDataGetter(record) {
              if (record['type'] != 'day') return 'Copy Link';
              return null;
            },
            onClick(ctx, dRecord) {
              const record = dRecord.record;
              let rounds: any[] = record['rounds'];
              let billIds: any[] = [];
              for (let round of rounds) {
                billIds.push(...round['billIds']);
              }
              let params = {
                tmsBillIds: billIds,
                reportBy: record['reportBy'],
                type: record['reportType'],
              };
              appContext.createHttpBackendCall('TMSReportService', 'createAccessToken', { params: params })
                .withSuccessData(data => {
                  const CONFIG = app.host.CONFIG
                  let serverUrl = CONFIG.getUIServerUrl();
                  let url = `${serverUrl}/api/ui/${data.encrypt}`;
                  appContext.addOSNotification('success', 'Copy success');
                  navigator.clipboard.writeText(url);
                })
                .call();
            },
          },
        ]
      },

      toolbar: {
        actions: [
          {
            name: "export-xlsx", label: 'Export Xlsx',
            createComponent: function (ctx: grid.VGridContext) {
              let uiRoot = ctx.uiRoot as any;
              const { appContext, pageContext } = uiRoot.props;
              return (<XLSXCustomButton tableName='vendor-bill-report' fieldSelect={'all'}
                appContext={appContext} pageContext={pageContext} context={ctx} selectMode='all'
                options={{ fileName: `Route Report ${util.TimeUtil.toCompactDateFormat(new Date())}.xlsx`, modelName: 'report' }}
              />)
            }
          },
        ],
        filterActions: [
          ...entity.DbEntityListConfigTool.TOOLBAR_AUTO_REFRESH('refresh', T('Refresh')),
        ],
      },

      view: {
        currentViewName: 'tree',
        availables: {
          tree: {
            viewMode: 'tree',
            label: 'Report',
            treeField: 'label',
            plugin: treePlugin
          },
        }
      }
    }
    let fields = CONFIG.record.fields;
    for (let sel of fields) {
      if (sel.name.startsWith("_")) continue;
      sel.computeCssClasses = (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
        let record = dRecord.record;
        if (record.type === 'day') return 'text-warning fw-bold';
        if (record.type === 'vendor') {
          let difference = record['difference'];
          if (difference < 0) return 'bg-danger bg-opacity-25 text-secondary fw-bold';
          return 'text-info fw-bold';
        }
        return ''
      }
    }
    return CONFIG;
  }
}


export class TMSBillReportComputeData {
  records: Array<any>;
  constructor(records: Array<any>) {
    this.records = records;
  }

  computeData(type: 'fcl' | 'lcl' | 'other', reportBy: 'day' | 'month', targets: any[], groupByDate: boolean = true) {
    let allReportModel = new chart.ReportModel("all", "All", this.records);

    let filterTargets = targets.filter(sel => sel.type == type);
    let targetMap: Record<string, any> = {};
    filterTargets.forEach(sel => {
      let vendorId = sel.vendorId;
      targetMap[vendorId] = sel;
    });

    let roundSet = new Set<string>();

    const calculate = (reportModel: chart.ReportModel) => {
      let vendorModelMap = reportModel.groupBy("vendorId", "vendor");
      for (const vendorId in vendorModelMap) {
        let vendorModel: chart.ReportModel = vendorModelMap[vendorId];
        let vendorReportModel = new chart.ReportModel("all", "All", vendorModel.records);
        const roundModelMap = vendorReportModel.groupBy("route", "route");
        const totalFile = vendorModel.records.length;
        vendorModel.setReportRecord('totalFile', totalFile);
        vendorModel.setReportRecord('label', vendorModel.records[0].vendorFullName);
        vendorModel.setReportRecord('reportBy', reportBy);
        vendorModel.setReportRecord('reportType', type);
        if (targetMap) {
          const target = targetMap[vendorId];
          if (target) {
            vendorModel.setReportRecord('routeName', target['label']);
            if (target.unlimited) {
              vendorModel.setReportRecord('totalTarget', 'Không Giới Hạn');
            } else {
              const totalTarget = target.totalTripTarget;
              vendorModel.setReportRecord('totalTarget', totalTarget);
              const difference = totalFile - totalTarget;
              vendorModel.setReportRecord('difference', difference);
              const complete = Math.round((totalFile / totalTarget) * 100);
              vendorModel.setReportRecord('complete', complete);
            }
          }
        }
        let rounds: any[] = [];
        for (const round in roundModelMap) {
          let roundModel: chart.ReportModel = roundModelMap[round];
          rounds.push({
            'label': round,
            'totalFile': roundModel.records.length,
            'billIds': roundModel.records.map(sel => sel.id),
            'bills': roundModel.records,
          });
          roundSet.add(round);
        }
        vendorModel.setReportRecord('rounds', rounds);
      }
    }
    if (reportBy === 'day' && groupByDate) {
      const dayModelMap = allReportModel.groupByDate('deliveryPlan', 'day', 'YYYY/MM/DD', 'desc');
      for (const day in dayModelMap) {
        let dayModel: chart.ReportModel = dayModelMap[day];
        calculate(dayModel);
      }
    } else {
      calculate(allReportModel);
    }

    let results = allReportModel.collectReportRecords(true);
    results.sort((a: any, b: any) => b.totalFile - a.totalFile);
    let roundArray: any[] = [...roundSet];
    roundArray.sort((a: string, b: string) => b.localeCompare(a));

    let roundCols: Record<string, string> = {};
    let roundNameMap: Record<string, string> = {};
    roundArray.forEach((round, idx) => {
      roundCols[`round${idx}`] = round;
      roundNameMap[round] = `round${idx}`;
    });
    results.forEach((rec: any) => {
      let rounds: any[] = rec['rounds'];
      if (rounds) {
        rounds.forEach((round: any) => {
          round['name'] = roundNameMap[round.label];
        });
      }
    });
    return { data: results, roundCols: roundCols };
  }

  computeBarChartData(flat?: boolean) {
    let allReportModel = new chart.ReportModel("all", "All", this.records);
    const vendorModelMap = allReportModel.groupBy('vendorFullName', 'vendor');
    for (const vendorLabel in vendorModelMap) {
      let vendorModel: chart.ReportModel = vendorModelMap[vendorLabel];
      vendorModel.groupBy('route', 'route');
    }

    let results: any[] = [];
    let childrenMap: Record<string, chart.ReportModel> = allReportModel.childrenMap;
    for (let name in childrenMap) {
      let vendorModel = childrenMap[name];
      let routes: Array<any> = [];
      let vendorChildrenMap: Record<string, chart.ReportModel> = vendorModel.childrenMap;
      for (let route in vendorChildrenMap) {
        let routeModel = vendorChildrenMap[route];
        let _routeChartData = {
          name: route,
          actual: routeModel.records.length,
          ids: routeModel.records.map(sel => sel.id)
        }
        routes.push(_routeChartData);
      }

      let routeListModel: grid.ListModel = new grid.ListModel(routes);
      routeListModel.sort('name');
      let chartData = {
        name: name,
        actual: vendorModel.records.length,
        routes: routeListModel.getRecords(),
        ids: vendorModel.records.map(sel => sel.id)
      }
      results.push(chartData);
    }
    let listModel: grid.ListModel = new grid.ListModel(results);
    listModel.sort('actual');
    if (flat) {
      let records: any[] = [];
      listModel.getRecords().forEach(rec => {
        let routes: any[] = rec['routes'];
        routes.forEach(route => {
          rec[route.name] = route.actual;
        });
        records.push(rec);
      });
      return records;
    } else {
      return listModel.getRecords();
    }
  }
}

