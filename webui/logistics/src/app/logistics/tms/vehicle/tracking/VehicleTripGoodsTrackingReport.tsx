import React from 'react';
import * as FeatherIcon from "react-feather";

import { grid, chart, app, util, entity, sql, bs } from '@datatp-ui/lib';
import { T } from '../../backend';
import { TMSBillTransportationModeTools } from '../../utils';
import { UITMSBillLeaderboard } from '../../bill/UITMSBillReport';
import { <PERSON>lt<PERSON>, Legend, PieChart, Pie, Cell } from 'recharts';


export class ReportVehicleTripGoodsTracking extends app.AppComponent {

  computeReportPlugin = () => {
    let plugin = new ReportVehicleTripGoodsTrackingListPlugin('INTERNAL_COMPANY');
    plugin.update = (data: Array<any>) => {
      plugin.rawData = data;
      let records = new VehicleTripGoodsTrackingReportComputeData(data).computeData();
      plugin.listModel.update(records);
      plugin.getListModel().sort('totalFile', true);
      return plugin;
    }
    return plugin;
  }

  computeReportByVendorPlugin = () => {
    let plugin = new ReportVehicleTripGoodsTrackingListPlugin();
    plugin.update = (data: Array<any>) => {
      plugin.rawData = data;
      let records = new VehicleTripGoodsTrackingReportComputeData(data).computeVendorData();
      plugin.listModel.update(records);
      plugin.getListModel().sort('totalFile', true);
      return plugin;
    }
    return plugin;
  }

  render() {
    const { appContext, pageContext } = this.props
    return (
      <bs.ScrollableCards className="flex-vbox">
        <ReportVehicleTripGoodsTrackingList reportFor='driver'
          className='flex-vbox' key={'driver-report'} style={{ height: window.innerHeight - window.innerHeight * 0.3 }}
          appContext={appContext} pageContext={pageContext} plugin={this.computeReportPlugin()} />
        <ReportVehicleTripGoodsTrackingList reportFor='vendor'
          className='flex-vbox' key={'vendor-report'} style={{ height: window.innerHeight - window.innerHeight * 0.3 }}
          appContext={appContext} pageContext={pageContext} plugin={this.computeReportByVendorPlugin()} />
      </bs.ScrollableCards>
    )
  }
}

export class VehicleTripGoodsTrackingReportComputeData {
  records: Array<any>;
  constructor(records: Array<any>) {
    this.records = records;
  }

  computeData() {
    let allReportModel = new chart.ReportModel("all", "All", this.records);
    const mothModelMap = allReportModel.groupByDate('deliveryPlan', 'month', 'YYYY-MM', 'desc');
    for (const monthLabel in mothModelMap) {
      let monthModel: chart.ReportModel = mothModelMap[monthLabel];
      this.computeReportRecord(monthModel);
      const driverModelMap = monthModel.groupBy('driverFullName', 'driver');
      for (const driverLabel in driverModelMap) {
        let driverModel: chart.ReportModel = driverModelMap[driverLabel];
        driverModel.setReportRecord('driverId', driverModel.records[0]['driverId']);
        this.computeReportRecord(driverModel);
      }
    }
    return allReportModel.collectReportRecords(true);
  }

  computeVendorData() {
    let allReportModel = new chart.ReportModel("all", "All", this.records);
    const mothModelMap = allReportModel.groupByDate('deliveryPlan', 'month', 'YYYY-MM', 'desc');
    for (const monthLabel in mothModelMap) {
      let monthModel: chart.ReportModel = mothModelMap[monthLabel];
      this.computeReportRecord(monthModel);
      const driverModelMap = monthModel.groupBy('fleetLabel', 'vendor');
      for (const driverLabel in driverModelMap) {
        let driverModel: chart.ReportModel = driverModelMap[driverLabel];
        driverModel.setReportRecord('fleetId', driverModel.records[0]['fleetId']);
        this.computeReportRecord(driverModel);
      }
    }
    return allReportModel.collectReportRecords(true);
  }


  private computeReportRecord(model: chart.ReportModel) {
    const { records } = model;
    let totalFileImport = 0;
    let totalFileExport = 0;
    let totalFileDomestic = 0;
    let totalFileShortRoute = 0;
    let totalFileLongRoute = 0;
    let totalFileUnknownRoute = 0;
    let totalFileCont40 = 0;
    let totalFileCont20 = 0;
    let totalFileUnknownCont = 0;
    let totalFullTrip = 0;
    let totalCombineTrip = 0;
    let totalPairingTrip = 0;
    let totalRuTrip = 0;
    let totalOtherTrip = 0;

    for (let record of records) {
      if (TMSBillTransportationModeTools.isExport(record['mode'])) {
        totalFileExport++;
      } else if (TMSBillTransportationModeTools.isImport(record['mode'])) {
        totalFileImport++;
      } else {
        totalFileDomestic++;
      }

      if (record['routeType'] == 'long') {
        totalFileLongRoute++;
      } else if (record['routeType'] == 'short') {
        totalFileShortRoute++;
      } else {
        totalFileUnknownRoute++;
      }
      let vehicleType: string = record['vehicleType'];

      if (!vehicleType) {
        totalFileUnknownCont++;
      } else if (vehicleType.includes('20')) {
        totalFileCont20++;
      } else if (vehicleType.includes('40')) {
        totalFileCont40++;
      } else {
        totalFileUnknownCont++;
      }

      let transportType = record['transportType'];
      if (transportType == 'Nguyên chuyến') {
        totalFullTrip++;
      } else if (transportType == 'Kết hợp') {
        totalCombineTrip++;
      } else if (transportType == 'Ghép') {
        totalPairingTrip++;
      } else if (transportType == 'RU') {
        totalRuTrip++;
      } else {
        totalOtherTrip++;
      }
    }
    model.setReportRecord('totalFile', records.length);
    model.setReportRecord('totalFileExport', totalFileExport);
    model.setReportRecord('totalFileImport', totalFileImport);
    model.setReportRecord('totalFileDomestic', totalFileDomestic);
    model.setReportRecord('totalFileShortRoute', totalFileShortRoute);
    model.setReportRecord('totalFileLongRoute', totalFileLongRoute);
    model.setReportRecord('totalFileUnknownRoute', totalFileUnknownRoute);
    model.setReportRecord('totalFileCont40', totalFileCont40);
    model.setReportRecord('totalFileCont20', totalFileCont20);
    model.setReportRecord('totalFileUnknownCont', totalFileUnknownCont);

    model.setReportRecord('totalFullTrip', totalFullTrip);
    model.setReportRecord('totalCombineTrip', totalCombineTrip);
    model.setReportRecord('totalPairingTrip', totalPairingTrip);
    model.setReportRecord('totalRuTrip', totalRuTrip);
    model.setReportRecord('totalOtherTrip', totalOtherTrip);
  }
}

export class ReportVehicleTripGoodsTrackingListPlugin extends entity.DbEntityListPlugin {
  rawData: Array<any>;

  constructor(fleetResource: 'INTERNAL_COMPANY' | '' = '') {
    let dateRange: util.TimeRange = new util.TimeRange(new Date());
    dateRange.fromStartOf('M');
    dateRange.toEndOf('M');
    super([]);
    this.backend = {
      context: 'company',
      service: 'VehicleService',
      searchMethod: 'reportVehicleTripGoodsTracking'
    }
    this.searchParams = {
      "params": {},
      "filters": [
        ...sql.createSearchFilter(),
      ],
      "optionFilters": [
        {
          name: 'fleetResource', label: 'Fleet Resource', type: 'STRING', required: true, multiple: true,
          options: ['INTERNAL_COMPANY'],
          optionLabels: ['INTERNAL'],
          selectOptions: [fleetResource],
        },
        sql.createStorageStateFilter([entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED]),
      ],
      "rangeFilters": [
        ...sql.createDateTimeFilter("deliveryPlan", "Date", dateRange),
      ],
      'maxReturn': 5000,
    }
  }

  loadData(uiList: entity.DbEntityList<any>) {
    let params = { 'params': this.searchParams }
    this.createBackendSearch(uiList, params).call();
  }
}

interface ReportVehicleTripGoodsTrackingListProps extends entity.DbEntityListProps {
  reportFor: 'vendor' | 'driver';
}
export class ReportVehicleTripGoodsTrackingList extends entity.DbEntityList<ReportVehicleTripGoodsTrackingListProps> {
  createVGridConfig() {
    let treePlugin = new grid.TreeDisplayModelPlugin();
    let CONFIG: grid.VGridConfig = {
      title: 'Data',
      record: {
        fields: [
          grid.createIndex("", 40, false),
          {
            label: T("Category"), name: "label", width: 250, sortable: true, filterable: true, filterableType: 'options',
            container: 'fixed-left',
          },
          { label: T("Nhập"), name: "totalFileImport", width: 100, sortable: true, },
          { label: T("Xuất"), name: "totalFileExport", width: 100, sortable: true, },
          { label: T("Domestic"), name: "totalFileDomestic", width: 100, sortable: true, },
          { label: T("Đường Ngắn"), name: "totalFileShortRoute", width: 100, sortable: true, },
          { label: T("Đường Dài"), name: "totalFileLongRoute", width: 100, sortable: true, },
          { label: T("Đường Chưa Xác Định"), name: "totalFileUnknownRoute", hint: T("Đường Chưa Xác Định"), width: 200, sortable: true, },
          { label: T("Cont 40"), name: "totalFileCont40", width: 100, sortable: true, },
          { label: T("Cont 20"), name: "totalFileCont20", width: 100, sortable: true, },
          { label: T("Cont Chưa Xác Định"), name: "totalFileUnknownCont", hint: T("Cont Chưa Xác Định"), width: 200, sortable: true, },

          { label: T("Nguyên chuyến"), name: "totalFullTrip", width: 120, sortable: true, },
          { label: T("Kết hợp"), name: "totalCombineTrip", width: 100, sortable: true, },
          { label: T("Ghép"), name: "totalPairingTrip", width: 100, sortable: true, },
          { label: T("RU"), name: "totalRuTrip", width: 100, sortable: true, },
          { label: T("Other"), name: "totalOtherTrip", width: 100, sortable: true, },
          { label: T("Tồng File"), name: "totalFile", width: 100, sortable: true, container: 'fixed-right' },
        ]
      },

      toolbar: {
        actions: [
          {
            name: "export-xlsx", label: 'Export Xlsx',
            createComponent: function (ctx: grid.VGridContext) {
              let uiRoot = ctx.uiRoot as any;
              const { appContext, pageContext } = uiRoot.props;
              return (<entity.XlsxExportButton appContext={appContext} pageContext={pageContext} context={ctx}
                options={{ fileName: `Tracking Report ${util.TimeUtil.toCompactDateFormat(new Date())}.xlsx`, modelName: 'report' }}
              />)
            }
          },
        ],
        filters: entity.DbEntityListConfigTool.TOOLBAR_FILTERS(true, 'search'),
      },

      view: {
        currentViewName: 'tree',
        availables: {
          tree: {
            viewMode: 'tree',
            label: 'Report',
            treeField: 'label',
            plugin: treePlugin
          },
        }
      }
    }
    let fields = CONFIG.record.fields;
    for (let sel of fields) {
      if (sel.name.startsWith("_")) continue;
      sel.computeCssClasses = (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
        let record = dRecord.record;
        if (record.type === 'month') return 'text-warning fw-bold';
        if (record.type === this.props.reportFor) return 'text-info';
        return ''
      }
    }
    return CONFIG;
  }

  buildLeaderboard = () => {
    let { appContext, pageContext, plugin, reportFor } = this.props;
    let recMap: Record<string, any[]> = {};
    let labelMap: Record<string, string> = {};
    for (let rec of plugin.getRecords()) {
      let type = rec['type'];
      if (type === 'month') {
        let id = rec['id'];
        recMap[id] = [];
        labelMap[id] = rec['label'];
      }
      if (type === reportFor) {
        let parentId = rec['parentId'];
        recMap[parentId].push(rec);
      }
    }

    let leaderboardHtml: any[] = [];
    for (let key in recMap) {
      let recs = recMap[key];
      let label = labelMap[key];
      leaderboardHtml.push(
        <UITMSBillLeaderboard
          className="flex-grow-1 border border-300" key={`${key}-${reportFor}-leaderboard`}
          field="totalFile" title={{ label: `TOP FILES [${reportFor.toUpperCase()}]`, dateRange: label }}
          appContext={appContext} pageContext={pageContext} readOnly
          itemIcon={FeatherIcon.FileText}
          backgroundIcon={<FeatherIcon.FileText className="text-warning" size={50} />}
          records={recs}
        />
      )
    }
    if (leaderboardHtml.length == 0) {
      leaderboardHtml.push(
        <UITMSBillLeaderboard
          className="flex-grow-1 border border-300" key={`${reportFor}-leaderboard`}
          field="totalFile" title={{ label: `TOP FILES [${reportFor.toUpperCase()}]`, dateRange: 'Chưa có dữ liệu' }}
          appContext={appContext} pageContext={pageContext} readOnly
          itemIcon={FeatherIcon.FileText}
          backgroundIcon={<FeatherIcon.FileText className="text-warning" size={50} />}
          records={[]}
        />
      )
    }
    return leaderboardHtml;
  }

  buildChart = (data: any[]) => {
    const renderCustomizedLabel = (data: any) => {
      return `${(data.percent * 100).toFixed(1)}%`;
    };
    return (
      <PieChart width={310} height={330}>
        <Pie dataKey="value" isAnimationActive={false} data={data} cx={180} cy={150} outerRadius={65} fill="#8884d8" label={renderCustomizedLabel}>
          {
            data.map((entry, _index) => <Cell key={`${entry.name}`} fill={entry.color} />)
          }
        </Pie>
        <Tooltip />
        <Legend />
      </PieChart>
    )
  }

  buildCharts = () => {
    let { plugin } = this.props;
    let records: any[] = plugin.getRecords();

    let totalLongRoute = 0;
    let totalShortRoute = 0;
    let totalUnkRoute = 0;

    let totalImport = 0;
    let totalExportRoute = 0;
    let totalDomRoute = 0;

    let totalCont20 = 0;
    let totalCont40 = 0;
    let totalContUnk = 0;

    let totalFullTrip = 0;
    let totalCombineTrip = 0;
    let totalPairingTrip = 0;
    let totalRuTrip = 0;
    let totalOtherTrip = 0;

    for (let rec of records) {
      let type = rec['type'];
      if (type === 'month') {
        totalLongRoute += rec['totalFileLongRoute'] ? rec['totalFileLongRoute'] : 0;
        totalShortRoute += rec['totalFileShortRoute'] ? rec['totalFileShortRoute'] : 0;
        totalUnkRoute += rec['totalFileUnknownRoute'] ? rec['totalFileUnknownRoute'] : 0;

        totalImport += rec['totalFileImport'] ? rec['totalFileImport'] : 0;
        totalExportRoute += rec['totalFileExport'] ? rec['totalFileExport'] : 0;
        totalDomRoute += rec['totalFileDomestic'] ? rec['totalFileDomestic'] : 0;

        totalCont20 += rec['totalFileCont20'] ? rec['totalFileCont20'] : 0;
        totalCont40 += rec['totalFileCont40'] ? rec['totalFileCont40'] : 0;
        totalContUnk += rec['totalFileUnknownCont'] ? rec['totalFileUnknownCont'] : 0;

        totalFullTrip += rec['totalFullTrip'] ? rec['totalFullTrip'] : 0;
        totalCombineTrip += rec['totalCombineTrip'] ? rec['totalCombineTrip'] : 0;
        totalPairingTrip += rec['totalPairingTrip'] ? rec['totalPairingTrip'] : 0;
        totalRuTrip += rec['totalRuTrip'] ? rec['totalRuTrip'] : 0;
        totalOtherTrip += rec['totalOtherTrip'] ? rec['totalOtherTrip'] : 0;
      }
    }
    const dataRoute = [
      { name: 'Đ.Ngắn', value: totalShortRoute, color: '#0088FE' },
      { name: 'Đ.Dài', value: totalLongRoute, color: '#00C49F' },
      { name: 'Chưa Xác Định', value: totalUnkRoute, color: '#FFBB28' },
    ];

    const dataMode = [
      { name: 'Import', value: totalImport, color: '#0088FE' },
      { name: 'Export', value: totalExportRoute, color: '#00C49F' },
      { name: 'Domestic', value: totalDomRoute, color: '#FFBB28' },
    ];

    const dataCont = [
      { name: 'Cont 20', value: totalCont20, color: '#0088FE' },
      { name: 'Cont 40', value: totalCont40, color: '#00C49F' },
      { name: 'Cont Unknown', value: totalContUnk, color: '#FFBB28' },
    ];

    const dataTransport = [
      { name: 'Nguyên chuyến', value: totalFullTrip, color: '#0088FE' },
      { name: 'Kết hợp', value: totalCombineTrip, color: '#00C49F' },
      { name: 'Ghép', value: totalPairingTrip, color: '#FFBB28' },
      { name: 'RU', value: totalRuTrip, color: '#db6e1bff' },
      { name: 'Khác', value: totalOtherTrip, color: '#073d5cff' },
    ];

    let charts: any[] = [
      this.buildChart(dataRoute),
      this.buildChart(dataMode),
      this.buildChart(dataCont),
      this.buildChart(dataTransport),
    ];
    return (
      <div style={{
        display: 'flex',
        flexWrap: 'wrap'
      }}>
        {charts}
      </div>
    );
  }

  render() {
    if (this.isLoading()) return this.renderLoading();
    return (
      <div className="flex-vbox">
        <div className="flex-hbox flex-grow-0">
          {this.buildLeaderboard()}
        </div>
        <div className='flex-hbox'>
          <div className='w-70'>
            {this.renderUIGrid()}
          </div>
          <div className='w-30'>
            {this.buildCharts()}
          </div>
        </div>
      </div>
    )
  }
}