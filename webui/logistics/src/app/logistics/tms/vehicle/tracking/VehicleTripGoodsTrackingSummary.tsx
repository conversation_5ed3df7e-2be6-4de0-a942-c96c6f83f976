import React, { Component } from 'react';
import * as FeatherIcon from 'react-feather';
import { grid, util, input, bs, entity, app, storage } from '@datatp-ui/lib';
import { module } from "@datatp-ui/erp";

import { T } from '../../backend';
import { TMSBillTransportationModeTools } from '../../utils';
import { TMSBillTransportationMode, VehicleTripStatus } from '../../models';

const TOKEN_ID = 35484;
export class UIVehicleTripGoodsTrackingSummary extends Component<grid.VGridContextProps> {
  selectBean: any = {
    selectFiled: 'export',
    export: [
      'billLabel', 'deliveryPlan', 'time', 'customerFullName', 'senderContact', 'senderAddress', 'receiverContact',
      'mode', 'tmsBillQuantity', 'tmsBillWeight', 'tmsBillVolumeAsText', 'warehouseLabel', 'etaCutOffTime',
      'tmsBillDescription', 'description'
    ],
    import: [
      'billLabel', 'deliveryPlan', 'time', 'customerFullName', 'senderContact', 'receiverContact', 'receiverAddress',
      'mode', 'tmsBillQuantity', 'tmsBillWeight', 'tmsBillVolumeAsText', 'tmsBillDescription', 'description', 'warehouseLabel'
    ],
    cus: [
      'billLabel', 'deliveryPlan', 'time', 'customerFullName', 'senderContact', 'receiverContact', 'tmsBillDescription', 'description',
      'mode', 'tmsBillQuantity', 'tmsBillWeight', 'tmsBillVolumeAsText', 'warehouseLabel', 'etaCutOffTime', 'transportType', 'vehicleType', 'vehicleLabel', 'driverFullName', 'mobile', 'identificationNo'
    ],
  };
  bean: any = {};

  breakLine: any = {
    export: [
      'deliveryPlan', 'senderAddress', 'receiverAddress', 'senderContact', 'receiverContact',
      'warehouseLabel', 'tmsBillQuantity', 'tmsBillDescription', 'description'
    ],
    import: [
      'deliveryPlan', 'senderAddress', 'receiverAddress', 'senderContact', 'receiverContact', 'warehouseLabel',
      'etaCutOffTime', 'tmsBillQuantity', 'transportType', 'tmsBillDescription', 'description'
    ],
    cus: [
      'deliveryPlan', 'senderAddress', 'receiverAddress', 'senderContact', 'receiverContact', 'warehouseLabel',
      'tmsBillQuantity', 'transportType', 'tmsBillDescription', 'description'
    ],
  };
  showFileName = ['identificationNo', 'mobile', 'etaCutOffTime'];
  appendLabel: any = {
    warehouseLabel: 'Kho: ',
    tmsBillDescription: 'Ghi chú: ',
    description: 'Ghi chú: ',
  }

  tokens: Record<string, string> = {};

  constructor(props: grid.VGridContextProps) {
    super(props);
    let { context } = this.props;
    let uiRoot = context.uiRoot as entity.DbEntityList;;
    let { plugin } = uiRoot.props;
    this._onInit();
    let records: Array<any> = plugin.getListModel().getSelectedRecords();
    let mode = records.length ? records[0]['mode'] : TMSBillTransportationMode.ExportAir;
    if (TMSBillTransportationModeTools.isExport(mode)) {
      this.selectBean['selectFiled'] = 'export';
    } else {
      this.selectBean['selectFiled'] = 'import';
    }
    for (let propertyName of this.selectBean[this.selectBean.selectFiled]) {
      this.bean[propertyName] = true;
    };
  }

  componentDidMount(): void {
    let { context } = this.props;
    let uiRoot = context.uiRoot as entity.DbEntityList;;
    let { appContext, plugin } = uiRoot.props;
    let records: Array<any> = plugin.getListModel().getSelectedRecords();
    const allowedResourceIds: Set<number> = new Set();
    records.forEach(sel => {
      const vehicleTripId = sel.vehicleTripId;
      if (vehicleTripId) allowedResourceIds.add(vehicleTripId);
    });

    const params = {
      tokenId: TOKEN_ID,
      ids: Array.from(allowedResourceIds)
    }
    appContext.createHttpBackendCall('VehicleService', 'createAccessTokenByTrip', params)
      .withSuccessData((data: any) => {
        this.tokens = data;
        this.forceUpdate();
      })
      .call();

  }

  _onInit = () => {
    this.bean = {
      billLabel: false,
      office: false,
      mode: false,
      responsibleFullName: false,
      customerFullName: false,
      deliveryPlan: false,
      time: false,
      pickupDeliveryAddress: false,
      warehouseLabel: false,
      etaCutOffTime: false,
      tmsBillQuantity: false,
      tmsBillWeight: false,
      tmsBillVolumeAsText: false,
      senderContact: false,
      senderAddress: false,
      pickupLocation: false,
      receiverContact: false,
      receiverAddress: false,
      deliveryLocation: false,
      transportType: false,
      vehicleType: false,
      vehicleLabel: false,
      driverFullName: false,
      identificationNo: false,
      mobile: false,
    };
  }

  onRenderSelectBean = () => {
    let { context } = this.props;
    let contents: Array<any> = [];
    let fieldConfigs: Array<grid.FieldConfig> = context.getVGridConfigModel().getRecordConfigModel().allFields;
    for (let fieldConfig of fieldConfigs) {
      if (fieldConfig.name.startsWith("_")) continue;
      if (fieldConfig.state?.visible === false) continue;
      contents.push(
        <input.BBCheckboxField key={fieldConfig.name} bean={this.bean} field={fieldConfig.name}
          label={T(`${fieldConfig.hint ? fieldConfig.hint : fieldConfig.label}`)} value={false}
          onInputChange={(bean, field, oldVal, newVal) => {
            if (field === 'tmsBillQuantity') {
              bean['tmsBillVolumeAsText'] = newVal;
              bean['tmsBillWeight'] = newVal;
            }
            this.forceUpdate();
          }} />)
    }
    return (
      <bs.GreedyScrollable className='flex-vbox'>
        {...contents}
      </bs.GreedyScrollable>)
  }

  onRenderReport = () => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as entity.DbEntityList;;
    let { appContext, plugin } = uiRoot.props;
    let records: Array<any> = plugin.getListModel().getSelectedRecords();
    let contents: Array<any> = [];

    for (let vehicleTripId of Object.keys(this.tokens)) {
      let find = records.findLast((record) => record.vehicleTripId == vehicleTripId);
      const token = this.tokens[vehicleTripId];
      if (token && find) {
        const CONFIG = app.host.CONFIG
        let serverUrl = `${CONFIG.getUIServerUrl()}/api/ui/${token}`;
        find.url = serverUrl;
      }
    }

    for (let record of records) {
      let element: Array<any> = [];
      for (let propertyName in this.bean) {
        if (!this.bean[propertyName]) continue;
        let fieldConfig = context.getVGridConfigModel().getRecordConfigModel().getFieldConfig(propertyName);
        if (record[propertyName] || (fieldConfig.fieldDataGetter && fieldConfig.fieldDataGetter(record))) {
          if (this.breakLine[this.selectBean['selectFiled']].includes(propertyName)) element.push('\n- ');
          if (this.appendLabel[propertyName]) element.push(this.appendLabel[propertyName]);
        }
        let mode = record['mode'];
        if (propertyName === 'deliveryPlan') {
          if (TMSBillTransportationModeTools.isImport(mode)) {
            element.push('Thời gian giao hàng: ');
          } else {
            element.push('Thời gian lấy hàng: ');
          }
        }
        if (propertyName === 'senderAddress') {
          element.push('Điểm lấy hàng: ');
        }
        if (propertyName === 'receiverAddress') {
          element.push('Điểm giao hàng: ');
        }

        if (TMSBillTransportationModeTools.isDomestic(mode) && (propertyName === 'warehouseLabel' || propertyName === 'etaCutOffTime')) continue;
        if (propertyName == 'tmsBillQuantity') {
          if (record[propertyName]) element.push("Khối lượng:" + record[propertyName] + ` (${record['quantityUnit']}) `);
          continue;
        }
        if (propertyName == 'tmsBillWeight') {
          if (record[propertyName]) element.push(record[propertyName] + ` (KG) `);
          continue;
        }
        if (propertyName == 'tmsBillVolumeAsText') {
          if (record[propertyName]) element.push(record[propertyName] + ` (CBM) `);
          continue;
        }
        let fieldLabel = '';
        if (this.showFileName.includes(propertyName)) fieldLabel = `${fieldConfig.label}:`;
        if (propertyName == 'mode') {
          let value = fieldConfig.fieldDataGetter ? fieldConfig.fieldDataGetter(record) : record[propertyName];
          value = value.replace('Export', 'Xuất');
          value = value.replace('Import', 'Nhập');
          element.push(
            fieldLabel + value + " "
          );
          continue;
        }
        if (fieldConfig.fieldDataGetter) {
          if (!fieldConfig.fieldDataGetter(record)) continue;
          element.push(
            fieldLabel + fieldConfig.fieldDataGetter(record)
          )
        } else {
          if (!record[propertyName]) continue;
          if (fieldConfig.format) {
            let sDate: string = fieldConfig.format(util.TimeUtil.parseCompactDateTimeFormat(record[propertyName]));
            sDate = sDate.slice(0, 5);
            element.push(
              fieldLabel + sDate
            )
          } else {
            element.push(
              fieldLabel + record[propertyName]
            )
          }
        }
        element.push(" ");
      }

      let url = record['url'];
      let tripInfoUrl = url ? (
        <a href={url} target="_blank">
          Nhấn vào đây để truy cập lô hàng
          <div className='text-truncate w-50'>{url}</div>
        </a>
      ) : null;
      contents.push(
        <div className='card my-1 p-1' style={{ whiteSpace: 'pre-line' }}>
          {...element}
          {tripInfoUrl}
          <div>{'\n'}</div>
        </div>
      )
    }
    return (
      <bs.GreedyScrollable>
        <div className='flex-hbox' style={{ width: 600 }}>
          <input.BBRadioInputField bean={this.selectBean} field='selectFiled' options={['export', 'import', 'cus']}
            optionLabels={['HÀNG XUẤT', 'HÀNG NHẬP', 'CUS, KHÁCH HÀNG']} onInputChange={(bean, field, oldVal, newVal) => {
              this._onInit();
              for (let propertyName of this.selectBean[newVal]) {
                this.bean[propertyName] = true;
              };
              this.forceUpdate();
            }}
          />
          <FeatherIcon.Copy className='mx-1 my-2 text-warning' style={{ cursor: 'pointer' }} size={15}
            onClick={() => {
              let element = document.getElementById('content');
              if (element === null) return;
              navigator.clipboard.writeText(element.innerText);
              appContext.addOSNotification('success', 'Copy success')
            }} />
        </div>
        <div id='content'>
          {...contents}
        </div>
      </bs.GreedyScrollable>
    )
  }

  render(): React.ReactNode {
    return (
      <bs.VSplit>
        <bs.VSplitPane width={300}>
          {this.onRenderSelectBean()}
        </bs.VSplitPane>
        <bs.VSplitPane>
          {this.onRenderReport()}
        </bs.VSplitPane>
      </bs.VSplit>
    )
  }
}

interface VehicleTripGoodsTrackingMessageProps extends app.AppComponentProps {
  trackings: Array<any>;
  color?: string;
  onPostCommit?: (entity: any) => void;
}

export class VehicleTripGoodsTrackingSendMessageIcon extends app.AppComponent<VehicleTripGoodsTrackingMessageProps> {
  showMessageBox = () => {
    let { pageContext, trackings, onPostCommit } = this.props;
    let createContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <VehicleTripGoodsTrackingMessage appContext={appCtx} pageContext={pageCtx} trackings={trackings} onPostCommit={onPostCommit} />
      )
    }
    pageContext.createPopupPage('send-message', T('Send Message'), createContent, { size: 'lg' });

  }
  render(): React.ReactNode {
    let { color } = this.props;
    return (
      <bs.Button laf='link' className='p-0' color={color} onClick={this.showMessageBox}>
        <FeatherIcon.Mail size={15} color={color} />
      </bs.Button>
    )
  }
}

function getMessageConfig() {
  return storage.diskGet('vehicle-message-config', {});
}

function setMessageConfig(bean: any) {
  return storage.diskPut('vehicle-message-config', bean);
}
class VehicleTripGoodsTrackingMessage extends app.AppComponent<VehicleTripGoodsTrackingMessageProps> {
  qrUrl: string;
  tripInfoUrl: string;
  bean: any = getMessageConfig();

  componentDidMount(): void {
    let { trackings } = this.props;
    const allowedResourceIds: Set<number> = new Set();
    trackings.forEach(sel => {
      const vehicleTripId = sel.vehicleTripId;
      if (vehicleTripId) allowedResourceIds.add(vehicleTripId);
    });
    // this.createVehicleTripTrackingAuthorizedToken(Array.from(allowedResourceIds), this.genQrCode);
  }

  createVehicleTripTrackingAuthorizedToken = (allowedResourceIds: Array<any>, callback: (data: any) => void) => {
    if (!allowedResourceIds || allowedResourceIds.length == 0) return;
    const { appContext } = this.props;
    const resourceHandler = "resource:tms-vehicle-trip-tracking";
    let params = {
      tokenId: TOKEN_ID,
      token: {
        resourceHandler: resourceHandler,
        allowedResourceIds: {
          tripId: Array.from(allowedResourceIds)[0]
        }
      }
    };

    appContext
      .createHttpBackendCall('ApiAuthorizationService', 'createAuthorizedTokenObject', params)
      .withSuccessData(callback)
      .call()
  }


  // genQrCode = (token: any) => {
  //   const { appContext } = this.props;
  //   const CONFIG = app.host.CONFIG
  //   let serverUrl = CONFIG.getUIServerUrl();
  //   this.tripInfoUrl = `${serverUrl}/api/ui/${token['encrypt']}`;
  //   appContext.createHttpBackendCall("GPSTrackingServer", "genQrCode", { data: this.tripInfoUrl })
  //     .withSuccessData(data => {
  //       this.qrUrl = app.host.CONFIG.createServerLink(`/get/private/store/${data.storeId}`);
  //       this.forceUpdate();
  //     })
  //     .call();
  // }

  buildContactMessage = (tracking: any) => {
    let lines: any[] = [];
    let mode = tracking.mode;
    let pickupContainer = this.append('Lấy cont:', tracking.pickupContainerLocation);
    let returnContainer = this.append('\nHạ cont:', tracking.returnContainerLocation);
    const stopLocations: Array<any> = tracking.stopLocations ? tracking.stopLocations : [];
    const stopLocationLabels: Array<any> = stopLocations.map((stop: any) => { return `Điểm trung gian ${stopLocations.indexOf(stop) + 1}: ${stop.address}` });
    if (TMSBillTransportationModeTools.isExport(mode)) {
      let warehouseLocation = pickupContainer + returnContainer;
      if (TMSBillTransportationModeTools.isLCL(mode))
        warehouseLocation = this.append('Kho: ', tracking.receiverLocationShortLabel ? tracking.receiverLocationShortLabel : tracking.receiverAddress);
      lines.push(this.append('Liên hệ: ', tracking.senderContact));
      lines.push(this.append('Địa chỉ: ', tracking.senderAddress));
      lines.push(stopLocationLabels.join(''));
      lines.push(warehouseLocation);
      lines.push(this.append('Liên hệ kho: ', tracking.receiverContact));
    }
    if (TMSBillTransportationModeTools.isImport(mode)) {
      let warehouseLocation = pickupContainer + returnContainer;
      if (TMSBillTransportationModeTools.isLCL(mode))
        warehouseLocation = this.append('Kho: ', tracking.senderLocationShortLabel ? tracking.senderLocationShortLabel : tracking.senderAddress);
      lines.push(this.append('Liên hệ: ', tracking.receiverContact));
      lines.push(this.append('Địa chỉ: ', tracking.receiverAddress));
      lines.push(stopLocationLabels.join(''));
      lines.push(warehouseLocation);
      lines.push(this.append('Liên hệ kho: ', tracking.senderContact));
    }
    if (TMSBillTransportationModeTools.isDomestic(mode)) {
      let warehouseLocation = pickupContainer + returnContainer;
      lines.push(this.append('Người gửi: ', tracking.senderContact));
      lines.push(this.append('Địa chỉ: ', tracking.senderAddress));
      lines.push(stopLocationLabels.join(''));
      lines.push(this.append('Người nhận: ', tracking.receiverContact));
      lines.push(this.append('Địa chỉ: ', tracking.receiverAddress));
      if (tracking['typeOfTransport'] == 'typeOfTransport') lines.push(warehouseLocation);
    }
    return lines;
  }

  append(s1: string, s2: string) {
    if (s1 && s2) {
      return s1 + s2;
    }
    if (s1) return s1;
    return '';
  }

  buildTrackingMeasurements = (tracking: any) => {
    let mode = tracking.mode;
    let quantity = tracking.tmsBillQuantity;
    let weight = tracking.tmsBillWeight;
    let volume = tracking.tmsBillVolumeAsText;
    let quantityContent = quantity && `${quantity} ${tracking.tmsBillQuantityUnit ? tracking.tmsBillQuantityUnit : 'PKG'} `;
    let weightContent = weight && `${weight} KG`;
    let volumeContent = volume && `${volume} M3`;
    if (TMSBillTransportationModeTools.isFCL(mode)) {
      return (
        <div>
          {`Khối lượng: ${weightContent} `}
        </div>
      )
    }
    return (
      <div>
        {`Khối lượng: ${quantityContent} ${weightContent} ${volumeContent} `}
      </div>
    )
  }

  copyInnerText = (id: string) => {
    let div = document.getElementById(id);
    if (div === null) return;
    let elementsToRemove = div.querySelectorAll('[id^="remove"]');
    elementsToRemove.forEach(element => element.remove());
    const htmlContent = div.innerText;
    navigator.clipboard.writeText(htmlContent);
    this.props.appContext.addOSNotification('success', 'Copy success');
    this.forceUpdate();
    setMessageConfig(this.bean);
  }

  onUpdateStatus = (status: VehicleTripStatus) => {
    let { appContext, trackings, onPostCommit } = this.props;
    let ids: Set<number> = new Set();
    for (let tracking of trackings) {
      if (tracking.vehicleTripId) {
        ids.add(tracking.vehicleTripId);
      }
    }
    appContext.createHttpBackendCall('VehicleService', 'updateStatusVehicleTrips', { status: status, ids: Array.from(ids) })
      .withSuccessData((_data: any) => {
        appContext.addOSNotification("success", T('Update Status Success'));
        trackings.forEach((tracking: any) => {
          tracking['taskStatus'] = status;
          if (onPostCommit) onPostCommit(trackings);
        });
      })
      .call();
  }

  buildContent = (idx: any, values: any[]) => {
    return values.map((val, index) => {
      let field = `line-${idx}-${index}`;
      if (this.bean[field] === undefined) this.bean[field] = true;
      return (
        <div id={`${this.bean[field] ? 'add' : 'remove'}-${idx}-${index}`} className={`flex-hbox ${!this.bean[field] ? 'text-danger text-decoration-line-through' : ''}`}>
          <input.BBCheckboxField className='mx-2' bean={this.bean} field={field} value={true} onInputChange={() => {
            this.forceUpdate();
          }} />
          {val}
        </div>
      )
    })
  }

  buildVendorMessage = () => {
    let { trackings } = this.props;
    let billInfos: Array<any> = [];

    for (let tracking of trackings) {
      let i = trackings.indexOf(tracking);
      let truckInfo = `Container: ${tracking.containerNo} / ${tracking.vehicleType}\n`;
      if (TMSBillTransportationModeTools.isLCL(tracking.mode)) truckInfo = '';
      let lines: any[] = [
        `(${i + 1}) Mã đơn: ${tracking.billLabel} ${TMSBillTransportationModeTools.getLabelVn(tracking.mode)} `,
        `Thời gian: ${util.text.formater.compactDate(tracking.deliveryPlan)} ${tracking.time} `,
        `Khách hàng: ${tracking.office ? tracking.office + " / " : ""} ${tracking.customerFullName}`,
        ...this.buildContactMessage(tracking),
        this.buildTrackingMeasurements(tracking),
        truckInfo,
        `Booking: ${tracking.bookingCode}`,
        this.append('Ghi chú:', tracking.tmsBillDescription),
        this.append('Ghi chú điều vận:', tracking.description),
        tracking.twoWay ? `\n-> Chạy 2 Chiều` : '',
      ];
      billInfos.push(...this.buildContent('truck', lines.filter(l => !!l)));
      billInfos.push(<br />);
    }

    return (
      <div className='flex-vbox'>
        <div id='bill-info' className='flex-vbox' key={`bill-info-${util.IDTracker.next()}`}>
          <div className='fw-bold'>
            THÔNG BÁO VẬN CHUYỂN
          </div>
          🇻🇳 🇻🇳 🇻🇳
          <div className='fw-bold'>
            THÔNG TIN LÔ HÀNG:
          </div>
          <div id='info' style={{ whiteSpace: 'pre-line' }}>
            {billInfos}
          </div>
        </div>
        <bs.Toolbar>
          <bs.Button laf='info' outline onClick={() => this.copyInnerText('bill-info')}>
            {T('Copy')}
          </bs.Button>
          <bs.Button laf='info' outline onClick={() => {
            this.copyInnerText('bill-info');
            this.onUpdateStatus(VehicleTripStatus.SUBMITTED_PLAN);
          }}>
            {T('Copy And Submit Plan')}
          </bs.Button>
        </bs.Toolbar>
      </div>
    )
  }

  buildVehicleTripMessage = () => {
    let { trackings } = this.props;
    if (trackings.length === 0) return [];
    let tracking = trackings[0];
    if (!tracking.vehicleTripId) return [];
    let tripInfos = [
      `Xe: ${tracking.vehicleLabel} ${tracking.trailerNumber ? `/ ${tracking.trailerNumber}` : ''}`,
      `Tài xế: ${tracking.driverFullName} ${tracking.mobile}`,
      `CMND: ${tracking.identificationNo}`
    ]
    return tripInfos;
  }

  buildCustomerMessage = () => {
    let { appContext, pageContext, trackings } = this.props;
    let billInfos: Array<any> = [];
    for (let tracking of trackings) {
      let i = trackings.indexOf(tracking);
      let truckInfo = `Container: ${tracking.vehicleType}`;
      if (TMSBillTransportationModeTools.isLCL(tracking.mode)) truckInfo = `Xe: ${tracking.transportType} ${tracking.vehicleType && tracking.vehicleType}`;
      let lines: any[] = [
        `(${i + 1}) Mã đơn: ${tracking.billLabel} ${TMSBillTransportationModeTools.getLabelVn(tracking.mode)} `,
        `Thời gian: ${util.text.formater.compactDate(tracking.deliveryPlan)} ${tracking.time} `,
        `Khách hàng: ${tracking.office ? tracking.office + " / " : ""} ${tracking.customerFullName}`,
        ...this.buildContactMessage(tracking),
        this.buildTrackingMeasurements(tracking),
        truckInfo,
        `Booking: ${tracking.bookingCode}`,
      ]
      billInfos.push(...this.buildContent('customer', lines.filter(l => !!l)));
      billInfos.push(<br />);
    }
    return (
      <div className='flex-vbox'>
        <div id='trip-info' className='flex-vbox' key={`trip-info-${util.IDTracker.next()}`}>
          <div className='fw-bold'>
            THÔNG TIN LÔ HÀNG:
          </div>
          <div style={{ whiteSpace: 'pre-line' }}>
            {billInfos}
          </div>
          <div className='fw-bold'>
            VẬN CHUYỂN:
          </div>
          <div style={{ whiteSpace: 'pre-line' }}>
            {...this.buildContent('truck-info', this.buildVehicleTripMessage())}
          </div>
        </div>
        <bs.Toolbar>
          <bs.Button laf='info' outline onClick={() => this.copyInnerText('trip-info')}>
            {T('Copy')}
          </bs.Button>
          <bs.Button laf='info' outline onClick={() => {
            let tmsBillIds = trackings.map(tracking => tracking.tmsBillId);
            appContext.createHttpBackendCall("TMSPrintService", 'createCustomerTripInfoEmail', { tmsBillIds: tmsBillIds })
              .withSuccessData((data: any) => {
                pageContext.createPopupPage('send-email', T('Send Mail'), (appCtx, pageCtx) => (
                  <module.communication.message.UIMessageEditor key={`mail`}
                    appContext={appContext} pageContext={pageContext}
                    onPostCommit={() => {
                      pageContext.back();
                    }}
                    observer={new entity.ComplexBeanObserver(data)} />
                ), { size: 'lg' });
              })
              .call();

          }}>
            {T('Send Mail')}
          </bs.Button>
        </bs.Toolbar>
      </div>
    )
  }

  render(): React.ReactNode {
    return (
      <bs.TabPane>
        <bs.Tab name='vendor-notification' label='Thông Báo Lái Xe' active={true}>
          {this.buildVendorMessage()}
        </bs.Tab>
        <bs.Tab name='customer-notification' label='Thông Báo Khách Hàng'>
          {this.buildCustomerMessage()}
        </bs.Tab>
      </bs.TabPane>
    )
  }
}