import React, { Component } from 'react';
import * as FeatherIcon from 'react-feather'
import { app, input, grid, sql, bs, entity, util, server } from '@datatp-ui/lib';
import { module } from '@datatp-ui/erp';

import { T } from '../backend';

import { VehicleFleetURL } from '../RestURL';
import { UIVehicleUtils, VehicleUtils } from './VehicleUtils';
import { UIMaplibre } from '../map/UIMaplibreMarkers';
import { GPSConfigList, GPSConfigListPlugin } from './gps/GPSConfigList';
import { XLSXCustomButton } from '../XLSXButton';
import { Location, MaplibreTracking, MaplibreTrackingUtil } from '../map/UIMaplibreTracking';
import { BBRefTransporter } from '../transport/BBRefTransporter';
import { BBRefVehicleFleet } from './BBRefVehicleFleet';

export class UIVehicleListPlugin extends entity.DbEntityListPlugin {
  vehicleFleetId: number | null;
  constructor() {
    super([]);
    this.backend = {
      context: 'company',
      service: 'VehicleService',
      searchMethod: 'searchVehicles'
    }
    this.searchParams = {
      filters: [...sql.createSearchFilter()],
      optionFilters: [sql.createStorageStateFilter([entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED])],
      orderBy: {
        fields: ['label', 'modifiedTime'],
        fieldLabels: ['Label', 'Modified Time'],
        selectFields: ['modifiedTime'],
        sort: 'DESC',
      },
      maxReturn: 1000,
    };
  }

  loadData(uiList: entity.DbEntityList<any>): void {
    this.createBackendSearch(uiList, { params: this.searchParams }).call();
  }

  withVehicleFleet(code: any | null) {
    if (code != null) this.addSearchParam('vehicleFleetCode', code);
    return this;
  }

  withVehicleFleetId(id: number | null) {
    this.addSearchParam('vehicleFleetId', id);
    this.vehicleFleetId = id;
    return this;
  }

  withGPSConfigId(gpsConfigId: number) {
    this.addSearchParam('gpsConfigId', gpsConfigId);
    return this;
  }

  isFleet() {
    return this.vehicleFleetId ? true : false;
  }
}
interface UIVehicleListProps extends entity.DbEntityListProps {
  vehicleFleet?: any
  screen?: 'general' | 'membership';
}

export class UIVehicleList extends entity.DbEntityList<UIVehicleListProps> {
  createVGridConfig() {
    let { type, screen, plugin, appContext, pageContext } = this.props;
    let thisUI = this;
    let pluginVehicle = plugin as UIVehicleListPlugin;
    const writeCap = pageContext.hasUserWriteCapability();
    let modCap = pageContext.hasUserModeratorCapability();
    let queryAccount: entity.EntityQuery = {
      entityId: 'account_account',
      labelField: 'fullName',
      loadFields: ['id', 'fullName', 'loginId', 'email', 'mobile'],
      domain:
        '@fullName ILIKE :pattern OR @loginId ILIKE :pattern OR @email ILIKE :pattern OR @mobile ILIKE :pattern'
    }
    let onInputChange = (fieldCtx: grid.FieldContext, oldVal: any, newVal: any) => {
      let record = fieldCtx.displayRecord;
      let field = fieldCtx.fieldConfig;
      let ctx = fieldCtx.gridContext;
      let event: grid.VGridCellEvent = {
        row: record.row,
        field: field,
        event: 'Modified'
      }
      ctx.broadcastCellEvent(event);
    }
    let colLicensePlate = entity.DbEntityListConfigTool.FIELD_ON_SELECT('licensePlate', T('License Plate'), 120);
    colLicensePlate.state = { showRecordState: true };
    colLicensePlate.listener = {
      onDataCellEvent(cell, event) {
        if (event.row === cell.getRow()) {
          cell.forceUpdate();
        }
      },
    }
    colLicensePlate.editor!.onInputChange = onInputChange
    let config: grid.VGridConfig = {
      title: 'Vehicles',
      record: {
        editor: {
          supportViewMode: ['table', 'aggregation'],
          enable: true
        },
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          colLicensePlate,
          { name: 'label', label: T('Label'), width: 150, editor: { type: 'string', onInputChange: onInputChange } },
          {
            name: 'fleetName', label: T('Vehicle Fleet'), width: 150,
            editor: {
              type: 'string',
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let vehicle = dRecord.record;
                const oldVehicleFleetId = vehicle['vehicleFleetId'];
                return <BBRefVehicleFleet
                  bean={vehicle} beanIdField={"vehicleFleetId"} beanLabelField={"fleetName"} 
                  tabIndex={tabIndex} autofocus={focus}
                  appContext={appContext} pageContext={pageContext}
                  onPostUpdate={(inputUI, bean, selectOpt, userInput) => {
                    if(!vehicle['oldVehicleFleetId']) vehicle['oldVehicleFleetId'] = oldVehicleFleetId;
                    onInputChange(bean, field.name, '', bean[field.name]);
                  }} placeholder={'Enter Vehicle Fleet'}/>
              },
              onInputChange: onInputChange
            }
          },
          { name: 'trailerNumber', label: T('Ro Mooc') },
          { name: 'code', label: T('Code'), state: { visible: false }, width: 150 },
          {
            name: 'ownerAccountFullName', label: T('Owner'), width: 150,
            editor: {
              type: 'string',
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let vehicle = dRecord.record;
                return <entity.BBModelAutoComplete
                  appContext={appContext} pageContext={pageContext}
                  tabIndex={tabIndex} autofocus={focus}
                  query={queryAccount} bean={vehicle} field={'ownerAccountId'} labelField={'ownerAccountFullName'} onPostSelect={(bean, newVal) => {
                    onInputChange(bean, field.name, bean[field.name], newVal)
                  }}></entity.BBModelAutoComplete>
              },
              onInputChange: onInputChange
            }
          },
          {
            name: 'transporterFullName', label: T('Transporter'), width: 150,
            editor: {
              type: 'string',
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let record = dRecord.record;
                return <BBRefTransporter minWidth={500}
                  tabIndex={tabIndex} autofocus={focus} allowUserInput
                  appContext={appContext} pageContext={pageContext} placeholder='Transporter' bean={record}
                  beanIdField='transporterId' beanLabelField={field.name} 
                  onPostCommit={(Transporter: any) => {
                      record["fullName"] = Transporter['fullName'];
                  }}
                  onPostUpdate={(inputUI, bean, selectOpt, userInput) => {
                    bean["fullName"] = selectOpt['fullName'];
                    thisUI.viewId = util.IDTracker.next();
                    onInputChange(bean, field.name,'', bean[field.name]);
                  }}
                  refTransporterBy={'id'}  />
              },
              onInputChange: onInputChange
            }
          },
          { name: 'vehicleType', label: T('Type'), width: 80, editor: { type: 'string', onInputChange: onInputChange } },

          { name: 'dimensionX', label: T('X'), dataType: 'double', width: 80, editor: { type: 'double', onInputChange: onInputChange } },
          { name: 'dimensionY', label: T('Y'), dataType: 'double', width: 80, editor: { type: 'double', onInputChange: onInputChange } },
          { name: 'dimensionZ', label: T('Z'), dataType: 'double', width: 80, editor: { type: 'double', onInputChange: onInputChange } },
          {
            name: 'dimensionUnit', label: T('Unit'), width: 80, cssClass: 'px-1', editor: {
              type: 'string',
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let vehicle = dRecord.record;
                return (
                  <module.settings.BBRefUnit
                    appContext={appContext} pageContext={pageContext}
                    tabIndex={tabIndex} autofocus={focus}
                    minWidth={300}
                    placement="left"
                    placeholder="Enter Unit"
                    groupNames={['length']}
                    bean={vehicle} beanIdField={'dimensionUnit'}
                    onPostUpdate={(inputUI, bean, selectOpt, userInput) => onInputChange(bean, field.name, userInput, bean[field.name])} />
                )

              },
              onInputChange: onInputChange
            }
          },

          { name: 'volume', label: T('Volume'), dataType: 'double', width: 80, editor: { type: 'double', onInputChange: onInputChange }, },
          {
            name: 'volumeUnit', label: T('Unit'), cssClass: 'px-1', width: 80, editor: {
              type: 'string',
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let vehicle = dRecord.record;
                return (
                  <module.settings.BBRefUnit
                    appContext={appContext} pageContext={pageContext}
                    tabIndex={tabIndex} autofocus={focus}
                    minWidth={300}
                    placement="left"
                    placeholder="Enter Unit"
                    groupNames={['volume']}
                    bean={vehicle} beanIdField={'volumeUnit'}
                    onPostUpdate={(inputUI, bean, selectOpt, userInput) => onInputChange(bean, field.name, userInput, bean[field.name])} />
                )
              },
              onInputChange: onInputChange
            }
          },

          { name: 'grossWeigh', label: T('Gross Weigh'), dataType: 'double', editor: { type: 'double', onInputChange: onInputChange } },
          {
            name: 'grossWeighUnit', label: T('Unit'), cssClass: 'px-1', width: 80, editor: {
              type: 'string',
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let vehicle = dRecord.record;
                return (
                  <module.settings.BBRefUnit
                    appContext={appContext} pageContext={pageContext}
                    tabIndex={tabIndex} autofocus={focus}
                    minWidth={300}
                    placement="left"
                    placeholder="Enter Unit"
                    groupNames={['weight']}
                    bean={vehicle} beanIdField={'grossWeighUnit'}
                    onPostUpdate={(inputUI, bean, selectOpt, userInput) => onInputChange(bean, field.name, '', bean[field.name])} />
                )
              },
              onInputChange: onInputChange
            }
          },
          { name: 'oilConsumption', label: T('Oil Consumption'), dataType: 'double', width: 150, editor: { type: 'double', onInputChange: onInputChange } },
          { name: 'gpsPluginName', label: T('GPS'), container: 'fixed-right' },
          ...entity.DbEntityListConfigTool.FIELD_ENTITY,
        ],
        fieldGroups: {
          dimension: {
            label: T("Dimension"),
            fields: ['dimensionX', 'dimensionY', 'dimensionZ', 'dimensionUnit']
          },
          volume: {
            label: T("Volume"),
            fields: ['volume', 'volumeUnit']
          },
          grossWeigh: {
            label: T("Gross Weigh"),
            fields: ['grossWeigh', 'grossWeighUnit']
          }
        },
        summary: {
          dataCellHeight: 40,
          render: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord, _viewMode: grid.ViewMode) => {
            const infoField = ['ownerAccountFullName', 'licensePlate'];
            let html = (
              <grid.SummaryCell className="flex-hbox" context={ctx} record={dRecord}>
                <grid.SummaryInfo context={ctx} record={dRecord} labelField="label" infoField={infoField} descField="code" />
              </grid.SummaryCell>
            );
            return html;
          },
        },
      },
      toolbar: {
        actions: [
          ...entity.DbEntityListConfigTool.TOOLBAR_CHANGE_STORAGE_STATES(
            VehicleFleetURL.vehicle.saveStates,
            [entity.StorageState.ARCHIVED, entity.StorageState.ACTIVE],
            !modCap
          ),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION(!writeCap || !pluginVehicle.isFleet() || screen === 'general', {
            name: 'select-vehicle',
            label: T('Select'),
            hint: T('Select Vehicle'),
            icon: FeatherIcon.List,
            onClick: (ctx: grid.VGridContext) => {
              this.onAction();
            },
          }),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION(!writeCap, {
            name: 'add-row', label: T('Add Row'), icon: FeatherIcon.Plus,
            onClick: VehicleUtils.onAddRow
          }),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_ADD(!writeCap || !pluginVehicle.isFleet(), T('Add')),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_DELETE(!writeCap || screen === 'general' || !pluginVehicle.isFleet(), T('Remove')),
        ],
        filters: entity.DbEntityListConfigTool.TOOLBAR_FILTERS(true),
      },

      footer: {
        page: {
          hide: type !== 'page',
          render: (ctx: grid.VGridContext) => {
            return <UIVehicleListPageControl context={ctx} />;
          },
        },
        selector: entity.DbEntityListConfigTool.FOOTER_MULTI_SELECT(T('Select'), type),
      },

      view: {
        currentViewName: 'aggregation',
        availables: {
          table: {
            viewMode: 'table',
          },
        aggregation: {
          viewMode: 'aggregation',
          createAggregationModel(_ctx: grid.VGridContext) {
            let model = new grid.AggregationDisplayModel('All', false);
            model.addAggregation(
              new grid.ValueAggregation(T("Vehicle Fleet"), "fleetName", true)
                .withSortBucket('asc')
            );
            return model;
          }
        }
        },
      },
    };
    return config;
  }

  onNewAction() {
    let { plugin } = this.props;
    let newRecord = {}
    plugin.getListModel().addRecord(newRecord);
    let state = grid.getRecordState(newRecord);
    state.markNew();
    this.getVGridContext().getVGrid().forceUpdateView();
  }

  onDefaultSelect(dRecord: grid.DisplayRecord) {
    let vehicle = dRecord.record;
    UIVehicleUtils.showVehicleByCode({ uiSource: this, code: vehicle.code });
  }

  onVehicleMultiSelect = (appContext: app.AppContext, pageContext: app.PageContext, vehicles: Array<any>) => {
    let { plugin, vehicleFleet } = this.props;
    if (!vehicleFleet) return;

    let memberships: Array<any> = [];
    for (let vehicle of vehicles) {
      let membership = {
        vehicleFleetId: vehicleFleet.id,
        vehicleFleetCode: vehicleFleet.code,
        vehicleCode: vehicle.code,
        vehicleId: vehicle.id,
      };
      memberships.push(membership);
    }

    appContext.createHttpBackendCall('VehicleFleetService', 'addVehicleFleetMembershipList', { memberships: memberships })
      .withSuccessData((_data: any) => {
        appContext.addOSNotification('success', T('Add Success'));
        for (let vehicle of vehicles) {
          let state = grid.getRecordState(vehicle);
          state.selected = false;
        }
        plugin.getListModel().addRecords(vehicles);
        this.forceUpdate();
        pageContext.back();
      })
      .withFailNotification('danger', T('Add Fail!'))
      .call();
  };

  onVehicleSelect = (appContext: app.AppContext, pageContext: app.PageContext, vehicle: any) => {
    let { plugin, vehicleFleet, onModifyBean } = this.props;
    if (!vehicleFleet) return;

    let membership = {
      vehicleFleetId: vehicleFleet.id,
      vehicleId: vehicle.id,
    };

    appContext.createHttpBackendCall('VehicleFleetService', 'addVehicleFleetMembership', { membership: membership })
      .withSuccessData((_data: any) => {
        appContext.addOSNotification('success', T('Add Success'));
        let state = grid.getRecordState(vehicle);
        state.selected = false;
        plugin.getListModel().addRecord(vehicle);
        this.forceUpdate();
        pageContext.back();
      })
      .withFailNotification('danger', T('Add Fail'))
      .call();
  };

  onAction() {
    let { pageContext, plugin } = this.props;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return <UIVehicleList
        appContext={appCtx}
        pageContext={pageCtx}
        plugin={new UIVehicleListPlugin().withRecordFilter(new entity.ExcludeRecordFilter(plugin.getListModel().getRecords(), 'code'))}
        onMultiSelect={(appCtx, pageCtx, transporter) => this.onVehicleMultiSelect(appCtx, pageCtx, transporter)
        }
        onSelect={this.onVehicleSelect}
        type={'selector'}
      />
    }
    pageContext.createPopupPage('onActionVehicle', T('Vehicle'), createAppPage, { size: 'lg', backdrop: 'static' });
  }

  onDeleteAction() {
    let { plugin, appContext, vehicleFleet } = this.props;
    let vehicles: Array<any> = plugin.getListModel().getSelectedRecords();
    if (vehicles.length === 0) {
      appContext.addOSNotification('warning', T('No Vehicle were Selected'));
    } else {
      let memberships: Array<any> = [];
      vehicles.forEach((vehicle) => {
        let membership = {
          vehicleId: vehicle.id,
          vehicleFleetId: vehicleFleet.id,
        };
        memberships.push(membership);
      });

      appContext.createHttpBackendCall('VehicleFleetService', 'removeVehicleFleetMemberships', { memberships: memberships })
        .withSuccessData((_data: any) => {
          appContext.addOSNotification('success', T('Remove Success'));
          plugin.getListModel().removeSelectedDisplayRecords();
          this.forceUpdate();
        })
        .withFailNotification('danger', T('Remove Fail!'))
        .call();
    }
  }
}

export class UIVehicleListPageControl extends Component<grid.VGridContextProps> {
  onNew() {
    let { context } = this.props;
    let uiRoot = context.uiRoot as entity.DbEntityList<UIVehicleListProps>;
    let { appContext, vehicleFleet, onModifyBean } = uiRoot.props;

    let onPostCommit = (vehicle: any, _uiEditor?: app.AppComponent) => {
      const membership = {
        vehicleFleetId: vehicleFleet.id,
        vehicleId: vehicle.id,
      };

      appContext.createHttpBackendCall('VehicleFleetService', 'addVehicleFleetMembership', { membership: membership })
        .withSuccessData((_data: any) => {
          appContext.addOSNotification('success', T('Add Vehicle Membership Success'));
          if (onModifyBean) {
            onModifyBean(null, undefined);
          } else this.forceUpdate();
        })
        .call();
    };

    const vehicle = VehicleUtils.initVehicle();
    UIVehicleUtils.onNewVehicle({
      uiSource: uiRoot,
      vehicle: vehicle,
      onPostCommit,
    });
  }

  onSave = () => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as UIVehicleList;
    let { appContext, pageContext, plugin, vehicleFleet } = uiRoot.props;
    let modifiedRecords = plugin.getListModel().getMarkModifiedRecords();
    let records: Array<any> = [];
    for (let i = 0; i < modifiedRecords.length; i++) {
      let record = modifiedRecords[i];
      let recordState = grid.getRecordState(record);
      if (recordState.isMarkNew()) {
        record.editState = 'NEW';
      } else if (recordState.isMarkModified()) {
        record.editState = 'MODIFIED';
      }
      records.push(record);
    }
    appContext.createHttpBackendCall('VehicleService', 'saveVehicles', { vehicles: records })
    .withEntityOpNotification('commit', 'Vehicle')
    .call();
  }

  vehicleConnectGPS = (gpsConfig: any) => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as UIVehicleList;
    let { appContext, pageContext, plugin } = uiRoot.props;
    appContext.createHttpBackendCall('VehicleService', 'connectGPS', { gpsConfig: gpsConfig })
      .withSuccessData((vehicles) => {
        let filterPlugin = new entity.ExcludeRecordIdFilter(vehicles);
        uiRoot.onVehicleMultiSelect(appContext, pageContext, filterPlugin.filter(plugin.getRecords()));
      })
      .call();
  }

  onShowGPSConfig = () => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as entity.DbEntityList;
    let { pageContext, plugin } = uiRoot.props;
    let vehicleIds = plugin.getListModel().getSelectedRecordIds();
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <GPSConfigList type='selector'
          appContext={appCtx} pageContext={pageCtx} plugin={new GPSConfigListPlugin()}
          onSelect={(_appCtx: app.AppContext, _pageCtx: app.PageContext, config: any) => this.addGPSConfig(_appCtx, _pageCtx, config, vehicleIds)} />
      )
    }
    pageContext.createPopupPage('gps-config-list', 'GPS Configs', createAppPage, { size: 'lg' })
  }

  vehicleTracking = () => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as entity.DbEntityList;
    let { appContext, pageContext, plugin } = uiRoot.props;
    let vehiclePlates: Array<any> = [];
    plugin.getListModel().getRecords().forEach(v => v.gpsPluginName && vehiclePlates.push(v['licensePlate']));
    this.findVehicleLocationByPlates(appContext, vehiclePlates, (locations: Array<any>) => {
      let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
        return (
          <VehicleTracking appContext={appCtx} pageContext={pageCtx} locations={locations} />
        )
      }
      pageContext.createPopupPage('vehicle-tracking', 'Tracking', createAppPage, { size: 'lg' });
    });
  }

  addGPSConfig = (appCtx: app.AppContext, pageCtx: app.PageContext, config: any, vehicleIds: Array<any>) => {
    appCtx
      .createHttpBackendCall('VehicleService', 'addGPSConfigToVehicles', { config: config, vehicleIds: vehicleIds })
      .withSuccessData(_data => {
        pageCtx.back();
        this.forceUpdate();
      })
      .call();

  }

  onGPSApi = () => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as entity.DbEntityList;
    let { pageContext } = uiRoot.props;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <GPSApi appContext={appCtx} pageContext={pageCtx} observer={new entity.BeanObserver({})} />
      )
    }
    pageContext.createPopupPage('gpsApi', T('GPS Api'), createAppPage, { size: 'lg', backdrop: 'static' });
  }

  onOpenMap = () => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as entity.DbEntityList;
    let { appContext, pageContext, plugin } = uiRoot.props;
    let vehiclePlates: Array<any> = [];
    let selectedRecs = plugin.getListModel().getSelectedRecords();
    if (selectedRecs.length > 0) {
      selectedRecs.forEach(v => vehiclePlates.push(v['licensePlate']));
    } else {
      plugin.getListModel().getRecords().forEach(v => vehiclePlates.push(v['licensePlate']));
    }

    this.findVehicleLocationByPlates(appContext, vehiclePlates, (locations) => {
      let findErrors: Array<any> = locations.filter(sel => !!sel.error);
      let plates = findErrors.map(sel => sel['numberPlate']);
      console.log(plates);
      let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
        if (locations.length > 0) {
          let maplibreLocations: Array<any> = [];
          for (let location of locations) {
            maplibreLocations.push({
              lat: location.latitude,
              lng: location.longtitude,
              rotation: location.rotation,
              label: `${location.gpsProvider} : ${location.numberPlate}`
            })
          }
          return (
            <UIMaplibre appContext={appCtx} pageContext={pageCtx} locations={maplibreLocations} />
          )
        } else {
          return (
            <div>
              {'No GPS Connection'}
            </div>
          )
        }
      }
      pageContext.createPopupPage('map', T('MAPS'), createAppPage, { size: 'lg', backdrop: 'static' });
    });
  }

  findVehicleLocationByPlates(appContext: app.AppContext, vehiclePlates: Array<string>, callback: (locations: Array<any>) => void) {
    appContext
      .createHttpBackendCall('VehicleService', 'findVehicleLocationByPlates', { vehiclePlates: vehiclePlates })
      .withSuccessData(callback)
      .call();
  }

  render() {
    let { context } = this.props;
    let uiRoot = context.uiRoot as app.AppComponent;
    let { appContext, pageContext } = uiRoot.props;
    let writeCap = pageContext.hasUserWriteCapability();
    return (
      <bs.Toolbar className='border' hide={!writeCap}>
        <entity.WButtonEntityWrite icon={FeatherIcon.Activity} appContext={appContext} pageContext={pageContext} label={T('Test Map')} onClick={this.vehicleTracking} />
        <entity.WButtonEntityWrite icon={FeatherIcon.Activity} appContext={appContext} pageContext={pageContext} label={T('Add GPS Config')} onClick={this.onShowGPSConfig} />
        <entity.WButtonEntityWrite icon={FeatherIcon.Plus} appContext={appContext} pageContext={pageContext} label={T('GPS API KEY')} onClick={this.onGPSApi} />
        <entity.WButtonEntityWrite icon={FeatherIcon.Map} appContext={appContext} pageContext={pageContext} label={T('MAPS')} onClick={this.onOpenMap} />
        <entity.WButtonEntityNew appContext={appContext} pageContext={pageContext} label={T('Vehicle')} onClick={() => this.onNew()} />
        <XLSXCustomButton
          tableName="vehicles"
          context={context} appContext={appContext} pageContext={pageContext}
          options={{ fileName: `vehicle ${util.TimeUtil.toCompactDateFormat(new Date())}.xlsx`, modelName: 'vehicles' }} />
        <entity.WButtonEntityWrite appContext={appContext} pageContext={pageContext} icon={FeatherIcon.Save}
          label={T('Save Rows')} onClick={this.onSave} />
      </bs.Toolbar>
    );
  }
}

class GPSApi extends entity.AppDbEntityEditor {
  onCreateToken = () => {
    let { appContext, observer } = this.props;
    let bean = observer.getMutableBean();
    let params = {
      tokenId: bean.tokenId,
      partnerName: bean.partnerName
    };
    appContext
      .createHttpBackendCall('VehicleService', 'createAccessToken', params)
      .withSuccessData((data: string) => {
        bean.authorization = data;
        this.forceUpdate();
      })
      .call()
  }

  onMap = () => {
    const { pageContext, observer } = this.props;
    let authorization = observer.getBeanProperty('authorization');
    const CONFIG = app.host.CONFIG;
    let restClient = CONFIG.createRestClient();
    let callback = (resp: server.BackendResponse) => {
      let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
        return (
          <GPSInfoList appContext={appCtx} pageContext={pageCtx} plugin={new entity.DbEntityListPlugin(resp['data']['result'])} />
        )
      }
      pageContext.createPopupPage('gpsApi', T('GPS Api'), createAppPage, { size: 'lg', backdrop: 'static' });
    }
    let params: any = {
      request: 'get-location',
      vehicles: ['29K03120']
    };
    let header = {
      'DataTP-Authorization': authorization
    }
    restClient.postWithHeader(`/resource`, header, params, callback)
  }

  render(): React.ReactNode {
    let { appContext, pageContext, observer } = this.props;
    let bean = observer.getMutableBean();
    const SESSION = app.host.DATATP_HOST.session;
    return (
      <div className='flex-vbox'>
        <div className='flex-grow-1'>
          <module.security.BBRefAccessToken
            appContext={appContext} pageContext={pageContext}
            label="Use Token" placeholder='Select a token' accountId={SESSION.getAccountId()}
            placement="bottom-start" offset={[0, 5]} minWidth={300}
            bean={bean} beanIdField="tokenId" beanLabelField="tokenLabel" />
          <input.BBStringField label='Partner Name' bean={bean} field='partnerName' />
          <input.BBInfo
            key={`${util.IDTracker.next()}`} style={{ height: '15em' }}
            bean={bean} field='authorization' textArea={true} />
        </div>
        <bs.Toolbar>
          <bs.Button laf="primary" onClick={this.onCreateToken}>
            <FeatherIcon.Upload size={12} />Create
          </bs.Button>
          <bs.Button laf="primary" onClick={this.onMap}>
            <FeatherIcon.Send size={12} />Test
          </bs.Button>
        </bs.Toolbar>
      </div>
    )
  }
}

interface GPSInfoListProps extends entity.DbEntityListProps {
  viewName?: 'table' | 'grid';
}
class GPSInfoList extends entity.DbEntityList<GPSInfoListProps> {
  createVGridConfig(): grid.VGridConfig {
    let { pageContext, type, viewName } = this.props;
    const writeCap = pageContext.hasUserWriteCapability();
    let config: grid.VGridConfig = {
      title: T("Vessels"),
      record: {
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          entity.DbEntityListConfigTool.FIELD_ON_SELECT('gpsProvider', T('GPS Provider'), 200),
          { name: 'numberPlate', label: T('Number Plate') },
          { name: 'latitude', label: T('Lat'), },
          { name: 'longtitude', label: T('Lng') },
          { name: 'locationAddress', label: T('Address'), width: 200 },
          { name: 'speed', label: T('Speed') },
          { name: 'rotation', label: T('Rotation') },
          { name: 'driverFullName', label: T('Driver') },
          { name: 'driverLicense', label: T('Driver License') },
          { name: 'driverPhoneNumber', label: T('Driver Phone') },
        ]
      },

      toolbar: {
        actions: [],
        filters: entity.DbEntityListConfigTool.TOOLBAR_FILTERS(false)
      },

      footer: {
        page: {
          hide: type !== 'page',
          render: (ctx: grid.VGridContext) => {
            return (<></>);
          }
        },
      },
      view: {
        currentViewName: viewName ? viewName : 'table',
        availables: {
          table: {
            viewMode: 'table'
          },
          grid: {
            label: "Vehicle Location",
            column: 1,
            viewMode: 'grid',
            rowHeight: 150,
            renderRecord: (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
              let uiRoot = _ctx.uiRoot as entity.DbEntityList;
              let { appContext, pageContext, onSelect } = uiRoot.props;
              let location = dRecord.record;
              let html = (
                <div className='flex-vbox border-top p-1' style={{ fontSize: 15, cursor: 'pointer' }}
                  onClick={() => { if (onSelect) onSelect(appContext, pageContext, location) }}>
                  <bs.Button laf='link' className='p-0'>
                    {location['numberPlate']}
                  </bs.Button>
                  <ul>
                    <li className="page-item">{location['locationAddress']}</li>
                    <li className="page-item">{location['speed']} KM/H</li>
                  </ul>
                </div>
              )
              return html;
            },
          }
        }
      },
    };
    return config;
  }
}
interface VehicleTrackingProps extends app.AppComponentProps {
  locations: Array<any>;
}
class VehicleTracking extends app.AppComponent<VehicleTrackingProps> {
  focusVehicleLocation: Location = {
    coordinate: [106.6297, 20.8561],
    label: 'Me',
  }
  numberPlate: string;


  constructor(props: VehicleTrackingProps) {
    super(props);
    let { locations } = props;
    let firstVehicleLocation = locations[0];
    this.focusVehicleLocation = this.toLocation(firstVehicleLocation);
  }

  toLocation = (dbLocation: any): Location => {
    let focusVehicleLocation = MaplibreTrackingUtil.gpsInfoToLocation(dbLocation);
    this.numberPlate = dbLocation.numberPlate;
    return focusVehicleLocation;
  }

  loadVehicleLocation(appContext: app.AppContext, callback: (location: any) => void) {
    appContext
      .createHttpBackendCall('VehicleService', 'getVehicleLocation', { numberPlate: this.numberPlate })
      .withSuccessData((notification) => callback(notification.attrs.data))
      .call();
  }


  render(): React.ReactNode {
    let { appContext, pageContext, locations } = this.props;
    return (
      <bs.VSplit>
        <bs.VSplitPane width={450}>
          <GPSInfoList appContext={appContext} pageContext={pageContext} viewName='grid' plugin={new entity.DbEntityListPlugin(locations)}
            onSelect={(_appCtx, _pageCtx, location) => {
              this.focusVehicleLocation = this.toLocation(location);
              this.forceUpdate();
            }} />
        </bs.VSplitPane>
        <bs.VSplitPane>
          <MaplibreTracking key={`maplibre-tracking-${util.IDTracker.next()}`} locations={[this.focusVehicleLocation]}
            appContext={appContext} pageContext={pageContext}
            doLoadData={(_callBack) => {
              this.loadVehicleLocation(appContext, (location) => {
                _callBack(this.toLocation(location));
              });
            }} />
        </bs.VSplitPane>
      </bs.VSplit>
    )
  }
}
