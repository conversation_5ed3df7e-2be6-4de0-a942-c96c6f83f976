import { entity, sql, util, app } from "@datatp-ui/lib";
import { T } from "app/logistics/fleet/Dependency";
import { UITransporterEditor } from "./UITransporter";
import React from "react";

interface BBRefTransporterProps extends entity.BBRefEntityProps {
  beanIdField: string;
  beanLabelField: string;
  refTransporterBy: 'id' | 'code';
  vehicleFleetId?: number;
  readOnly?: boolean;
  code?: any;
  onPostCommit?: (entity: any) => void;
  isNewRecord?: boolean;
}

export class BBRefTransporter extends entity.BBRefEntity<BBRefTransporterProps> {
  createPlugin() {
    let { beanIdField, beanLabelField, refTransporterBy, vehicleFleetId, onPostCommit, pageContext } = this.props;
    let loadMethod = 'loadTransporterById';
    if (refTransporterBy == 'code') {
      loadMethod = 'loadTransporterByCode';
    }
    let config: entity.BBRefEntityPluginConfig = {
      backend: {
        context: 'company',
        service: 'VehicleFleetService',
        searchMethod: 'searchTransporters',
        loadMethod: loadMethod,
        createSearchParams(origParams: sql.SqlSearchParams, userInput: string) {
          if (vehicleFleetId) {
            origParams.params = { vehicleFleetId: vehicleFleetId };
          }
          return origParams;
        }
      },
      bean: {
        idField: beanIdField,
        labelField: beanLabelField,
        mapSelect: (ui: entity.BBRefEntity, bean: any, selectOpt: any, idValue: any, labelValue: any): any => {
          if (selectOpt) {
            bean[beanIdField] = idValue;
            bean[beanLabelField] = labelValue;
            return 'success';
          } else {
            bean[beanIdField] = null;
            bean[beanLabelField] = null;
            const { allowUserInput, isNewRecord } = ui.props as BBRefTransporterProps;

            if (allowUserInput || isNewRecord == true) {
              bean[beanLabelField] = labelValue;
              const transporter: any = { fullName: labelValue, code: `transporter-${util.TimeUtil.toDateTimeIdFormat(new Date())}` };
              let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
                return <UITransporterEditor appContext={appCtx} pageContext={pageCtx} observer={new entity.ComplexBeanObserver(transporter)} onPostCommit={(transporter: any) => {
                  bean[beanIdField] = transporter['transporterId'];
                  bean[beanLabelField] = transporter['fullName'];
                  if (onPostCommit) {
                    onPostCommit(transporter);
                  }
                  pageCtx.back();
                }} />
              }
              pageContext.createPopupPage('transporter', 'Save Transporter', createAppPage, { size: 'lg' })
              return 'success';
            } else {
              return 'fail';
            }
          }
        }
      },
      refEntity: {
        idField: refTransporterBy,
        labelField: 'fullName',
        vgridRecordConfig: {
          fields: [
            entity.DbEntityListConfigTool.FIELD_INDEX(),
            entity.DbEntityListConfigTool.FIELD_ON_SELECT('fullName', T('Full Name'), 250),
            { name: 'mobile', label: T('Mobile'), width: 200 },
            { name: 'idCard', label: T('Id Card'), width: 200 },
          ]
        }
      },
    };

    return new entity.BBRefEntityPlugin(config);
  }
}
