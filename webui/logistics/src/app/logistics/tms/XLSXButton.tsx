import React from "react";
import * as FeatherIcon from 'react-feather'
import { bs, grid, entity, storage } from "@datatp-ui/lib";

import { T } from "./backend";
// const cacheDataSelectModelMap = new Map<String, grid.DataSelectModel>();

function getDataSelectModel(tableName: string) {
  let config = storage.diskGet(`export-config-${tableName}`);
  return config;
}

function setDataSelectModel(tableName: string, data: any) {
  storage.diskPut(`export-config-${tableName}`, data);
}

interface XLSXCustomButtonProps extends entity.XlsxExportButtonProps {
  name?: string;
  tableName: string;
  selectMode?: 'all' | 'selected';
  fieldSelect?: Array<any> | 'all';
  onExported?: (data: any) => void;
}
export class XLSXCustomButton extends entity.XlsxExportButton<XLSXCustomButtonProps> {
  override initDataSelectModel = (model: grid.DataSelectModel) => {
    let { context, selectMode, fieldSelect, tableName } = this.props;
    let cacheDataSelectModel = getDataSelectModel(tableName);
    if (cacheDataSelectModel) {
      model.fields = cacheDataSelectModel.fields;
      model.groupFields = cacheDataSelectModel.groupFields;
      model.selectMode = cacheDataSelectModel.selectMode;
      return model;
    } else {
      let fields: Array<grid.FieldConfig> = context.getVGridConfigModel().getRecordConfigModel().allFields;
      let holder: Array<grid.SelectField> = [];
      let lastElementArray: Array<grid.SelectField> = [];
      let firstElementArray: Array<grid.SelectField> = [];
      for (let sel of fields) {
        if (sel.name.startsWith('_') || sel.name === 'selected' || !sel.label) continue;
        if (sel.state?.visible != false || fieldSelect?.includes(sel.name)) {
          let selectField: grid.SelectField = {
            label: sel.label,
            name: sel.name,
            select: false,
            dataType: sel.dataType ? sel.dataType : sel.editor?.type,
          }
          if (sel.container == 'fixed-left') {
            firstElementArray.push(selectField);
          } else if (sel.container == 'fixed-right') {
            lastElementArray.push(selectField);
          } else {
            holder.push(selectField);
          }
        }
      }
      model.fields = [...firstElementArray, ...holder, ...lastElementArray];
      for (let group of model.groupFields) {
        group.select = false;
        let holderFields = [];
        for (let fileName of group.fieldNames) {
          let find = model.fields.find(sel => sel.name === fileName);
          if (find) holderFields.push(fileName);
        }
        group.fieldNames = holderFields;
      }
      model.selectMode = 'selected';
      if (selectMode) model.selectMode = selectMode;
      if (fieldSelect) {
        model.setFieldSelect(true, fieldSelect);
      }
    }
  }

  override customDataListExportModel = (model: entity.DataListExportModel) => {
    model.fieldGroups = [];
    return model;
  }

  _onCacheSelectedFields = (model: grid.DataSelectModel) => {
    let { tableName } = this.props;
    setDataSelectModel(tableName, model);
  }

  override onExportCustomization = () => {
    let { context, onExported } = this.props;
    let dialogUI = (
      <entity.VGridDataSelectorListPlugin
        context={context} selectLabel='Export'
        onSelect={(model: grid.DataSelectModel) => {
          this._onCacheSelectedFields(model);
          this.onSelect(model);
          if (onExported) onExported(model);
        }}
        initDataSelectModel={this.initDataSelectModel} customDataSelectorActions={this.customDataSelectorActions} />
    );
    bs.dialogShow(T('XLSX Export'), dialogUI, { dialogId: this.dialogId, size: 'lg', backdrop: 'static' });
  }

  override render() {
    let { name } = this.props;
    return (
      <bs.Button laf="primary" className="p-1" onClick={() => this.onExportCustomization()}>
        <FeatherIcon.Download size={12} /> {name ? name : T(' XLSX Export')}
      </bs.Button>
    )
  }
}