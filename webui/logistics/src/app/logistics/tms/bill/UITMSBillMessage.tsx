import { bs, input, app, server, grid, util, entity } from '@datatp-ui/lib';
import { module } from "@datatp-ui/erp";
import * as FeatherIcon from 'react-feather'
import React, { Component } from 'react';

import { T } from '../backend';
import { TMSBillTransportationModeTools } from '../utils';
import { UITMSBillAttachmentList } from './UITMSBillAttachments';
import { updateTMSBillData } from './UITMSBillListPageControl';
import { BBRefVehicleFleet } from '../vehicle/BBRefVehicleFleet';
import { TMSRequest } from '../common/TMSRequestUtils';
interface UITMSBillMessageProps extends app.AppComponentProps {
  observer: entity.ComplexBeanObserver,
  plugin: entity.DbEntityListPlugin,
  context: grid.VGridContext,
  ownerAccount?: any;
  vendor?: any
  onSendMessageSuccessCallBack?: (message: any, allowCreateVendorBill: boolean) => void;
}
interface WBtnMailProps extends grid.VGridContextProps {
  bill: any;
}
export class WBtnMail extends Component<WBtnMailProps> {
  render(): React.ReactNode {
    let { context, bill } = this.props;
    let uiRoot = context.uiRoot as entity.DbEntityList;
    let { appContext, pageContext } = uiRoot.props;
    let cssColor = 'text-warning';
    if (bill.messageStatus === 'DeliveredWithError') cssColor = 'text-danger';
    if (bill.messageStatus === 'Delivered') cssColor = 'text-success';

    const showMessage = () => {
      if (!bill.messageId) {
        bs.notificationShow('warning', 'Message Id Is Null!');
        return;
      }
      const successCb = (data: any) => {
        const createContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (
            <module.communication.message.UIMessageEditor
              appContext={appCtx} pageContext={pageCtx} observer={new entity.ComplexBeanObserver(data)}
              onPostCommit={(message) => {
                let status = message['status'];
                if (status != 'Delivered') {
                  let detail = (
                    <div className='flex-vbox'>
                      <bs.Badge laf='danger'>
                        {status.replace(/([A-Z])/g, ' $1')}
                      </bs.Badge>
                      {message['error']}
                    </div>
                  )
                  bs.dialogMessage('Error Sending Email!!!', 'warning', detail);
                } else {
                  let detail = (
                    <div className='flex-vbox'>
                      <bs.Badge laf='success'>
                        {status}
                      </bs.Badge>
                    </div>
                  )
                  bs.dialogMessage('Email Sent Successfully!!!', 'info', detail);
                  pageCtx.back();
                }
                bill['messageStatus'] = status;
                bill['message'] = message;
                context.getVGrid().forceUpdateView();
              }}
            />
          )
        }
        pageContext.createPopupPage('email', T('Email'), createContent, { size: 'lg' })
      }
      appContext.createHttpBackendCall('CommunicationMessageService', 'getMessageById', { id: bill.messageId })
        .withSuccessData(successCb)
        .call();
    }

    if (bill['vendorBillId']) {
      return (
        <bs.Button style={{ width: 22, marginLeft: 4 }} className={cssColor}
          onClick={showMessage} laf='link'>
          <FeatherIcon.Mail size={15} />
        </bs.Button>
      );
    } else {
      return (
        <bs.Button style={{ width: 22, marginLeft: 4 }} className={'text-info'}
          onClick={() => {
            TMSRequest.requestVendorBillWithTMSBillIds(appContext, [bill['id']], ((data: any[]) => {
              let find = data.find(sel => sel['tmsBillId'] == bill['id']);
              if (find) {
                bill['vendorBillId'] = find['id'];
                context.getVGrid().forceUpdateView();
              }
            }));
          }} laf='link'>
          <FeatherIcon.PhoneForwarded size={15} />
        </bs.Button>
      )
    }
  }
}
export class UITMSBillMessage extends app.AppComponent<UITMSBillMessageProps> {
  contactListKey: any = util.IDTracker.next();
  contentInvalidData: Array<any> = [];
  isValidData: boolean = false;

  bean: any = {
    createTMSOps: false,
    createVehicleTripGoodsTracking: true,
    eSignature: false,
    signatureContent: "",
    processVendorId: null,
    processVendorName: null,
    allowRequestApp: false,
    combineTrip: false,
  }

  constructor(props: UITMSBillMessageProps) {
    super(props);
    const { vendor, plugin } = this.props;
    if (vendor) {
      this.bean['processVendorId'] = vendor['id'];
      this.bean['processVendorName'] = vendor['label'];
      this.bean['allowRequestApp'] = vendor['allowRequestTrackingApp'];
      this.bean['containerShippingSupport'] = vendor['containerShippingSupport'];
      this.bean['truckShippingSupport'] = vendor['truckShippingSupport'];
      this.forceUpdate();
    }

    let records = plugin.getListModel().getSelectedRecords();
    this.initContentInvalidData(records);
    // if (this.isValidData) this.bean['createVehicleTripGoodsTracking'] = this.bean['allowRequestApp'];
  }

  initContentInvalidData = (records: Array<any>) => {
    let { context } = this.props;
    let contentInvalidData = [];
    let fields = ['deliveryPlan', 'dateTime', 'time', 'customerFullName', 'office'];
    for (let record of records) {
      let checkFields = [...fields];
      if (TMSBillTransportationModeTools.isImport(record.mode)) {
        checkFields.push('receiverAddress');
        checkFields.push('receiverContact');
        checkFields.push('senderLocationId');
      } else if (TMSBillTransportationModeTools.isExport(record.mode)) {
        checkFields.push('senderAddress');
        checkFields.push('senderContact');
        checkFields.push('receiverLocationId');
      } else if (TMSBillTransportationModeTools.isDomestic(record.mode)) {
        checkFields.push('receiverAddress');
        checkFields.push('receiverContact');
        checkFields.push('senderAddress');
        checkFields.push('senderContact');
        checkFields.push('truckType');
      }

      let fieldsIsNullValue = [];
      for (let fieldName of checkFields) {
        if (!record[fieldName] || record[fieldName] == 0) {
          try {
            let fieldConfig: grid.FieldConfig = context.getVGridConfigModel().getRecordConfigModel().getFieldConfig(fieldName);
            fieldsIsNullValue.push(fieldConfig.label);
          } catch { }
        }
      }

      if (fieldsIsNullValue.length > 0) {
        let html = (
          <div key={`${record['id']}-verify-field`} className='flex-hbox'>
            <p className={'text-info'}>
              {record.label}
            </p> : {fieldsIsNullValue.join(', ')}
          </div>
        )
        contentInvalidData.push(html);
      }
    }
    this.contentInvalidData = contentInvalidData;
    this.isValidData = contentInvalidData.length === 0;
  }

  onShowUILoadableTMSBillAttachmentList = (uiEditor: bs.BaseComponent, attachments: Array<any>, tmsBillIds: Array<number>) => {
    let uiMessage = uiEditor as module.communication.message.UIMessageForm;
    let { appContext, pageContext } = this.props;
    let pushAttachments = (entity: any) => {
      let attachment = {
        "storeId": entity['storeInfo'],
        "name": entity['name'],
        "size": entity['size'],
        "resourceUri": appContext.getServerContext().createServerURL(`/storage/company/private/${entity.storeInfo}`),
        "tmsBillSourceId": entity['tmsBillId'],
        "sourceId": entity['id'],
      }
      uiMessage.pushAttachmentFile(attachment);
    }
    let onSelect = (_appContext: app.AppContext, pageContext: app.PageContext, entity: any) => {
      pushAttachments(entity);
      pageContext.back();
      uiEditor.forceUpdate();
    }

    let onMultiSelect = (_appContext: app.AppContext, pageContext: app.PageContext, entities: Array<any>) => {
      entities.forEach(entity => pushAttachments(entity));
      pageContext.back();
      uiEditor.forceUpdate();
    }

    let callBack = (data: any) => {
      let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
        return (
          <UITMSBillAttachmentList style={{ minHeight: 400 }} type='selector' appContext={appCtx} pageContext={pageCtx}
            plugin={
              new entity.DbEntityListPlugin(data.data)
                .withRecordFilter(new entity.ExcludeRecordFilter(attachments, 'sourceId', 'id'))}
            onSelect={onSelect} onMultiSelect={onMultiSelect} />
        )
      }
      pageContext.createPopupPage('attachments', T('Attachments'), createAppPage, { size: 'md' });
    }
    appContext.createHttpBackendCall('TMSRestCallService', 'findTMSBillAttachmentByTMSBillIds', { tmsBillIds: tmsBillIds }).withSuccess(callBack).call();
  }
  customAttachActions = (uiEditor: bs.BaseComponent, attachments: Array<any>) => {
    let { appContext, pageContext, plugin, observer } = this.props;
    let uiMessage = uiEditor as module.communication.message.UIMessageForm;
    this.bean.signatureContent = observer.getBeanProperty("esignature");
    if (!this.bean.eSignature) {
      observer.replaceBeanProperty('esignature', '');
    }
    let billIds: Array<any> = [];
    let bills = plugin.getListModel().getSelectedRecords();
    let totalFileAttach = 0;
    for (let bill of bills) {
      billIds.push(bill['id']);
      totalFileAttach += bill['billAttachmentCount'];
    }
    let attachPODPdf = (attachPod: any) => {
      if (attachPod) {
        const successCB = (data: any) => {
          let storeInfo = data;
          let attachment = {
            "storeId": storeInfo['storeId'],
            "name": "POD.pdf",
            "size": storeInfo['size'],
            "resourceUri": appContext.getServerContext().createServerURL(`/get/private/store/${storeInfo.storeId}`),
            "attachPod": true,
            "resourceScheme": 'store'
          }
          uiMessage.pushAttachmentFile(attachment);
          uiMessage.forceUpdate();
        }
        appContext.createHttpBackendCall('TMSPrintService', 'createReceiptOfDeliveryPrint', { format: 'pdf', ids: billIds }).withSuccessData(successCB).call();
      } else {
        for (let i = 0; i < attachments.length; i++) {
          let att = attachments[i];
          if (att.attachPod) {
            attachments.splice(i, 1);
            uiEditor.forceUpdate();
            return;
          }
        }
      }
    }

    let registerSignature = (register: boolean) => {
      if (register) {
        observer.replaceBeanProperty('esignature', this.bean.signatureContent);
      } else {
        observer.replaceBeanProperty('esignature', '');
      }
    }

    let pod = {
      attachPod: false
    }
    let attachmentPod = attachments.find(sel => sel['attachPod']);
    if (attachmentPod) {
      pod.attachPod = true;
    }
    return (
      <>
        <input.BBCheckboxField className='px-1' bean={pod} field={'attachPod'} value={false} label={T('Attach POD')}
          onInputChange={(_bean, _field, _oldVal, _newVal) => {
            attachPODPdf(_newVal);
          }} />
        <div className='flex-hbox-grow-0 align-items-center '>
          <input.BBCheckboxField className='px-1' bean={this.bean} field={'eSignature'} value={false}
            onInputChange={(_bean, _field, _oldVal, _newVal) => {
              registerSignature(_newVal);
            }} />
          <bs.Button className='px-1' laf='link' onClick={() => {
            let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return (
                <div dangerouslySetInnerHTML={{ __html: this.bean.signatureContent }}></div>
              )
            }
            pageContext.createPopupPage('preview-e-signature', T('Preview'), createAppPage);
          }}>{T('E-Signature')}</bs.Button>
        </div>
      </>
    )
  }

  onRequest = (appCtx: app.AppContext, pageCtx: app.PageContext, ids: Array<any>, sendMessage: boolean = false) => {
    let { context, plugin, observer } = this.props;
    if (!this.bean.processVendorId) {
      bs.dialogMessage('warning', 'error', 'Enter Vendor Field And Try Again!');
      return;
    }
    const successCB = (_response: server.BackendResponse) => {
      let records = plugin.getListModel().getSelectedRecords();
      updateTMSBillData(context, records, () => {
        pageCtx.back();
        context.getVGrid().forceUpdateView();
      });
      if (sendMessage) {
        this.onSendMessage(appCtx, pageCtx, observer.getMutableBean());
      }
    }

    let attachments: Array<any> = observer.getComplexBeanProperty('attachments', []);
    attachments.forEach(sel => sel['resourceUrl'] = encodeURI(sel['resourceUri']));
    observer.commitAndGet();
    let params = {
      request: {
        tmsBillIds: ids,
        typeOfTransport: this.bean.shippingType,
        createTMSOps: this.bean.createTMSOps,
        combineTrip: this.bean.combineTrip,
        markColor: this.bean.markColor,
        createVehicleTripGoodsTracking: this.bean.createVehicleTripGoodsTracking,
        processVendorId: this.bean.processVendorId,
        message: observer.getMutableBean(),
      }
    }
    appCtx
      .createHttpBackendCall('TMSRestCallService', 'processingRequestTMSBills', params)
      .withSuccess(successCB)
      .call();
  }

  onSendMessage = (appCtx: app.AppContext, pageCtx: app.PageContext, message: any) => {
    let { onSendMessageSuccessCallBack } = this.props;
    const successCB = (message: any) => {
      if (onSendMessageSuccessCallBack) onSendMessageSuccessCallBack(message, false);
    }
    appCtx
      .createHttpBackendCall('CommunicationMessageService', 'sendMessage', { message: message })
      .withSuccessData(successCB)
      .call();
  }

  onUpdateVendor = (id: number) => {
    let { appContext, observer } = this.props;
    let targetRecipients: Array<any> = [];
    if (!id) {
      this.bean['allowRequestApp'] = false;
      // this.bean['createVehicleTripGoodsTracking'] = false;
      observer.replaceBeanProperty('recipients', targetRecipients);
      this.forceUpdate();
      return;
    }
    appContext
      .createHttpBackendCall('VehicleFleetService', 'getVehicleFleetById', { id: id })
      .withSuccessData((data: any) => {
        this.bean['allowRequestApp'] = data['allowRequestTrackingApp'];
        this.bean['containerShippingSupport'] = data['containerShippingSupport'];
        this.bean['truckShippingSupport'] = data['truckShippingSupport'];
        let emails = data['emails'];
        if (emails) this.onAddEmails(emails);
        // if (this.isValidData) {
        //   this.bean['createVehicleTripGoodsTracking'] = this.bean['allowRequestApp'];
        // } else {
        //   this.bean['createVehicleTripGoodsTracking'] = false;
        // }
      }).call();
  }

  onAddEmails = (emails: Array<any>) => {
    let { observer } = this.props;
    observer.commitAndGet();
    let targetRecipients: Array<any> = observer.getBeanProperty('recipients');
    for (let email of emails) {
      if (!email) continue;
      let containEmail = targetRecipients.find(sel => sel.recipientEmail === email);
      if (containEmail) continue;
      let targetRecipient = {
        deliverType: 'Email',
        recipientEmail: email,
        recipientDisplayName: email
      }
      targetRecipients.push(targetRecipient);
    }
    observer.replaceBeanProperty('recipients', targetRecipients);
    this.forceUpdate();
  }

  buildChooseShippingType = () => {
    let options = [];
    let optionLabels = [];
    if (this.bean['containerShippingSupport']) {
      options.push('container');
      optionLabels.push('CONTAINER');
    }
    if (this.bean['truckShippingSupport']) {
      options.push('truck');
      optionLabels.push('TRUCK');
    }
    if (options.length == 0) return;
    if (!this.bean['shippingType']) this.bean['shippingType'] = options[0];
    return (
      <input.BBRadioInputField bean={this.bean} field={'shippingType'} options={options} optionLabels={optionLabels} />
    )
  }

  buildMark = () => {
    const bgColors = ['', 'bg-success', 'bg-warning', 'bg-danger'];
    let btns: any[] = [];
    bgColors.forEach(c => {
      btns.push(
        <bs.Button key={c} laf='link' className={`m-1 p-3 ${c} border border-300`}
          onClick={() => {
            this.bean['markColor'] = c;
            this.forceUpdate();
          }}>
        </bs.Button>
      )
    });
    return (
      <bs.Popover className="flex-grow-0" title={T('Mark')} closeOnTrigger='.btn'>
        <bs.PopoverToggle className={`border border-300 h-100 ${this.bean['markColor'] ? this.bean['markColor'] : 'bg-white text-secondary'}`}>
          {T('Mark')}
        </bs.PopoverToggle>
        <bs.PopoverContent>
          <div className='flex-vbox gap-2' style={{ width: '200px' }}>
            {btns}
          </div>
        </bs.PopoverContent>
      </bs.Popover>

    );
  }

  render() {
    let { appContext, pageContext, observer, plugin, context, ownerAccount, onSendMessageSuccessCallBack } = this.props;
    let message = observer.getMutableBean();
    let ids = plugin.getListModel().getSelectedRecordIds();
    let textSendGT = '(Requested Vender App)';
    let onInputChange = (bean: any, field: string, oldVal: any, newVal: any) => {
      if (!this.isValidData && !oldVal) {
        bean[field] = false;
        bs.dialogConfirmMessage('Do you want to send to Goods Tracking ?', this.contentInvalidData, () => {
          bean[field] = true;
          if (!message.subject) {
            message.subject = textSendGT
          }
          if (!message.subject.includes(textSendGT)) {
            message.subject += ' ' + textSendGT;
          }
          this.forceUpdate();
        })
      }
      if (!newVal && message.subject.includes('(Requested Vender App)')) {
        message.subject = message.subject.replace(' ' + textSendGT, '');
      } else if (newVal && !message.subject.includes('(Requested Vender App)')) {
        message.subject += ' ' + textSendGT;
      }
      if (newVal == true) {
        this.bean['combineTrip'] = true;
      }
      this.forceUpdate();
    }
    let newContact = {
      category: 'Partner',
      tagNames: ['APP:TMS']
    };
    let disabledRequest = !this.bean.allowRequestApp && !this.bean.createTMSOps;
    return (
      <bs.VSplit updateOnResize smallScreenView='tabs'>
        <bs.VSplitPane width={450} className='flex-vbox' title='Request'>
          <div className='flex-vbox'>
            <div className='flex-grow-0 flex-hbox'>
              <BBRefVehicleFleet className='flex-grow-1' minWidth={400} required label='Vendor'
                appContext={appContext} pageContext={pageContext} placeholder={'Vendor'}
                bean={this.bean} beanIdField={'processVendorId'} beanLabelField={'processVendorName'}
                onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                  this.onUpdateVendor(bean['processVendorId'])
                }}
              />
              {this.buildMark()}
            </div>
            <input.BBCheckboxField style={{ fontSize: 16 }}
              label={T("Request OPS")} bean={this.bean} field={'createTMSOps'} value={true}
              disable={!pageContext.hasUserWriteCapability()} onInputChange={() => this.forceUpdate()} />
            <div className='flex-hbox-grow-0 '>
              <input.BBCheckboxField style={{ fontSize: 16 }}
                bean={this.bean} label={T("Request Vendor App")} field={'createVehicleTripGoodsTracking'} value={true} onInputChange={onInputChange} />
              {plugin.getListModel().getSelectedRecords().length > 1 && this.bean['createVehicleTripGoodsTracking'] ?
                <input.BBCheckboxField style={{ fontSize: 16 }} className='ms-1'
                  label={T("Combine Trip (Gộp chuyến)")} bean={this.bean} field={'combineTrip'} value={true}
                  disable={!pageContext.hasUserWriteCapability()} /> : <></>
              }
            </div>
            {this.buildChooseShippingType()}
            {!this.isValidData ?
              <div className='border border-100 px-1 py-2'>
                <bs.Badge laf='warning' className='flex-hbox' >
                  <FeatherIcon.AlertTriangle className='p-0' size={12} />
                  {'Warning!'}
                </bs.Badge>
                <div style={{ fontSize: '0.9rem' }}>
                  {this.contentInvalidData}
                </div>
              </div>
              // <bs.Button className='p-1 text-warning' laf='link' onClick={() => {
              //   bs.dialogMessage('Information is incomplete', 'warning', this.contentInvalidData);
              // }}>
              //   <FeatherIcon.AlertTriangle className='p-0' size={12} />
              // </bs.Button>
              :
              <></>
            }
            <hr />
            {ownerAccount ?
              <module.resource.UIContactList key={`contact-list-${this.contactListKey}`}
                appContext={appContext} pageContext={pageContext} type='selector' summaryMode template={newContact}
                onSelect={(_appCtx: app.AppContext, _pageCtx: app.PageContext, rec: any) => this.onAddEmails(rec['email'])}
                onModifyBean={(_bean) => {
                  this.contactListKey = util.IDTracker.next();
                  this.forceUpdate();
                }}
                plugin={
                  new module.resource.UIContactListPlugin('Account', ownerAccount['id'])
                    .withCategory('Partner')
                    .withTags(['APP:TMS'])}
              />
              : null
            }
          </div>
          <bs.Toolbar >
            <bs.Button disabled={disabledRequest} laf='primary'
              onClick={() => this.onRequest(appContext, pageContext, ids)}>
              <FeatherIcon.Save className='mx-1' size={12} />{T('Request App')}
            </bs.Button>
            <bs.Button disabled={disabledRequest} laf='primary'
              onClick={() => this.onRequest(appContext, pageContext, ids, true)}>
              <FeatherIcon.Save className='mx-1' size={12} />{T('Request & Send Message')}
            </bs.Button>
            <bs.Button laf='primary'
              onClick={() => {
                let content = (
                  <div>Request App chỉ sử dụng với các một số đơn vị thầu phụ nhất định có đăng kí sử dụng TMS APP!</div>
                );
                bs.dialogShow('Info', content)
              }}>
              <FeatherIcon.Info className='mx-1' size={12} />
            </bs.Button>
          </bs.Toolbar>
        </bs.VSplitPane>
        <bs.VSplitPane title='Email'>
          <bs.AvailableSize className='flex-vbox'>
            <module.communication.message.UIMessageEditor
              key={`message-${util.IDTracker.next()}`}
              customAttachActions={this.customAttachActions}
              appContext={appContext} pageContext={pageContext} onPostCommit={(message) => {
                if (onSendMessageSuccessCallBack) onSendMessageSuccessCallBack(message, true);
                pageContext.back();
                context.getVGrid().forceUpdateView();
              }}
              observer={observer} />
          </bs.AvailableSize>
        </bs.VSplitPane>
      </bs.VSplit>
    )
  }
}

interface NotificationMessageProps extends grid.VGridContextProps {
  fieldName: string;
}
let billDeliveredFails: Array<any> = [];
export class NotificationMessage extends Component<NotificationMessageProps> {
  timerId: any;
  loadPeriod: number = 60;
  loadFail: boolean = false;
  loading: boolean = false;
  poller: util.common.Poller;

  componentDidMount(): void {
    this.poller = new util.common.Poller(this.doLoadData);
    this.poller.start(this.loadPeriod);
  }

  componentWillUnmount(): void {
    this.poller.stop();
  }

  onUpdateCel = (dRecord: grid.DisplayRecord) => {
    let { context, fieldName } = this.props;
    for (let [key, cell] of Object.entries(context.cells)) {
      if (cell.getRow() == dRecord.row && key.startsWith(fieldName)) {
        cell.forceUpdate();
        break;
      }
    }
  }

  doLoadData = (callBack?: () => void) => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as entity.DbEntityList;
    let { appContext, plugin } = uiRoot.props;
    let messageIds: Set<number> = new Set<number>();
    let checkBills: Array<any> = [];
    for (let rec of plugin.getRecords()) {
      if (rec.messageId && rec.messageStatus != 'Delivered' && rec.messageStatus != 'Draft') {
        messageIds.add(rec.messageId);
        checkBills.push(rec);
      }
    }

    if (messageIds.size === 0) {
      billDeliveredFails = [];
      return;
    }
    let params = {
      'messageIds': [...messageIds]
    };

    this.loading = true;
    this.forceUpdate();
    appContext.createHttpBackendCall('CommunicationMessageService', 'findMessageByIds', params)
      .withSuccessData((messages: Array<any>) => {
        let newBillDeliveredFailsRecords: Array<any> = [];
        for (let message of messages) {
          let messageId = message.id;
          let messageStatus = message.status;
          for (let bill of checkBills) {
            if (bill.messageId === messageId && bill.messageStatus != messageStatus) {
              let dRec = plugin.getListModel().getDisplayRecordList().getDisplayRecords().find(dRec => dRec.record.id == bill.id);
              bill.messageStatus = messageStatus;
              if (dRec) this.onUpdateCel(dRec);
            }
            bill['message'] = message;
          }
        }
        for (let bill of checkBills) {
          let messageStatus = bill.messageStatus;
          if (messageStatus == 'DeliveredWithError') newBillDeliveredFailsRecords.push(bill);
        }
        this.loading = false;
        this.forceUpdate();
        if (callBack) {
          callBack();
        } else {
          this.toastShow(billDeliveredFails, newBillDeliveredFailsRecords);
        }
        billDeliveredFails = newBillDeliveredFailsRecords;
      })
      .withFail((_response: server.BackendResponse) => {
        if (this.poller) {
          this.poller.stop();
        }
        this.loadFail = true;
        this.loading = false;
        this.forceUpdate();
      })
      .call();
  }

  renderNumberRec() {
    if (billDeliveredFails.length == 0) return;
    return (
      <div className="px-1"
        style={{
          height: 15,
          width: 15,
          borderRadius: 10,
          color: 'red',
          marginLeft: -8,
          fontSize: 11
        }}>
        {billDeliveredFails.length}
      </div>
    )
  }

  onShowRecDeliveredFails = () => {
    let { context } = this.props;
    const uiRoot = context.uiRoot as entity.DbEntityList;
    const { pageContext } = uiRoot.props;
    const createContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <TMSBillDetailList style={{ minHeight: 550 }}
          appContext={appCtx} pageContext={pageCtx} plugin={new entity.DbEntityListPlugin(billDeliveredFails)} />
      )
    }
    pageContext.createPopupPage('tms-bill-detail', T('TMS Bill Delivered Fails'), createContent, { size: 'lg' });
  }

  toastShow = (oldRecords: Array<any>, newRecords: Array<any>) => {
    if (newRecords.length == 0) return;
    let oldIds: Array<any> = oldRecords.map(sel => { return sel.id });
    let contents: Array<any> = [];
    for (let nRec of newRecords) {
      let newRecId = nRec.id;
      if (oldIds.includes(newRecId)) continue;
      contents.push(
        <div>
          {`${nRec.label} ${nRec.customerFullName}`}
        </div>
      );
    }
    if (contents.length == 0) return;
    let html = (
      <div className="flex-vbox">
        {contents}
      </div>
    )
    bs.toastShow(
      html,
      { type: 'danger', delay: 5000, header: 'Sending Email Failed!!!' },
      { position: 'absolute', top: 10, right: 50, zIndex: 1000, boxShadow: '5px 5px 10px rgba(0, 0, 0, 0.3)' });
  }

  render() {
    let renderNumberRec: any = this.renderNumberRec();
    if (!renderNumberRec) return;
    let cssColor = billDeliveredFails.length === 0 ? '' : 'text-danger';
    return (
      <bs.Button laf='link' onClick={() => {
        this.doLoadData(() => {
          this.poller.stop();
          this.poller.start(this.loadPeriod);
          this.onShowRecDeliveredFails();
        });
      }}
        style={{ marginRight: 0 }}>
        {this.loadFail ?
          <div className="flex-hbox">
            <div>
              <FeatherIcon.AlertTriangle size={20} className="mx-1 text-danger" />
            </div>
          </div>
          : this.loading ?
            <div className="flex-hbox">
              <div>
                <FeatherIcon.Loader size={20} className="mx-1" style={{ animation: '0.75s linear infinite spinner-border' }} />
              </div>
            </div>
            :
            <div className="flex-hbox">
              <div>
                <FeatherIcon.MessageCircle size={20} className={`mx-1 ${cssColor}`} />
              </div>
              {renderNumberRec}
            </div>
        }
      </bs.Button>
    );
  }
}

class TMSBillDetailList extends entity.DbEntityList {
  createVGridConfig(): grid.VGridConfig {
    let _ = this;
    let config: grid.VGridConfig = {
      title: T("TMS Bill"),
      record: {
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          { name: 'label', label: T('File No.'), width: 150 },
          { name: 'hwbNo', label: T('HWB No') },
          { name: 'responsibleFullName', label: T('PIC'), width: 170 },
          { name: 'customerFullName', label: T('Customer'), width: 170 },
          { name: 'dateTime', label: T('Date'), format: util.text.formater.compactDate },
          { name: 'bookingCode', label: T('Booking/Bill'), width: 150 },
          { name: 'code', label: T('Bill Code'), width: 150 },
          {
            name: 'errorMessage', label: T('Error Message'), container: 'fixed-right', width: 400,
            dataTooltip: true,
            fieldDataGetter(record) {
              let message = record['message'];
              if (message) return message['error'];
              return null;
            },
          },
          {
            name: 'emails', label: T('Email'), container: 'fixed-right', width: 80,
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              return <WBtnMail context={ctx} bill={dRecord.record} />
            }
          },
        ]
      },

      toolbar: {
        actions: [
        ],
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          },
        }
      },
    };
    return config;
  }
  onResendMail = (ctx: grid.VGridContext) => {
    let uiRoot = ctx.uiRoot as entity.DbEntityList;
    let { appContext, plugin } = uiRoot.props;
    let messageIds: Set<number> = new Set<number>();
    plugin.getRecords().forEach(rec => {
      let messageId = rec['messageId'];
      if (messageId) messageIds.add(messageId);
    });

    const successCB = (messages: Array<any>) => {
      for (let message of messages) {
        let messageId = message['id'];
        plugin.getRecords().forEach(rec => {
          if (messageId === rec['messageId']) {
            rec['messageStatus'] = message['status'];
            rec['message'] = message;
          }
        })
      };
      ctx.getVGrid().forceUpdateView();
    }
    appContext
      .createHttpBackendCall('CommunicationMessageService', 'resendMessageByIds', { messageIds: messageIds })
      .withSuccessData(successCB)
      .call();

  }
}