import React, { Component } from 'react';
import { util, app, bs, grid, input, entity, component } from '@datatp-ui/lib';
import * as FeatherIcon from 'react-feather'

import { T } from '../backend';
import { OdooStatus, ResponsibilityType, TMSBillProcessStatus, TMSBillTransportationMode, TMSBillType } from '../models';
import { Verified } from '../models';
import { UITMSBillList } from './UITMSBillList';
import { UITMSReceiptOfDeliveryPrint, UITMSSubcontractorReportingPrint } from './UITMSBillPrint';
import { TMSBillTransportationModeTools } from '../utils';
const { CONTAINER_NUMBER_VALIDATOR } = util.validator;

export class UITMSBillUtils {
  static containerValidate = (val: any) => {
    try {
      CONTAINER_NUMBER_VALIDATOR.validate(val);
      return null;
    } catch (error) {
      return (
        <div className='flex-grow-0' >
          <FeatherIcon.XCircle size={12} color='red' />
        </div>
      );
    }
  }

  static copyToClipboard = (appCtx: app.AppContext, tmsBill: any) => {
    let mode = tmsBill['mode'];
    let truckNo = tmsBill.truckNo ? tmsBill.truckNo : '' + tmsBill.tmsTrackingTruckNo ? tmsBill.tmsTrackingTruckNo : ''
    let shipmentInfos: Array<string> = [];
    shipmentInfos.push(tmsBill.truckType ? tmsBill.truckType : '');
    shipmentInfos.push(tmsBill.containerNo ? tmsBill.containerNo : '');
    shipmentInfos.push(tmsBill.sealNo ? tmsBill.sealNo : '');
    shipmentInfos.push(tmsBill.quantity ? tmsBill.quantity + `(${tmsBill.quantityUnit})` : '');
    shipmentInfos.push(tmsBill.weight ? tmsBill.weight + '(KG)' : '');
    if (!TMSBillTransportationModeTools.isFCL(mode) && !tmsBill.containerNo) {
      shipmentInfos.push(tmsBill.volumeAsText ? tmsBill.volumeAsText + '(CBM)' : '');
    }
    let clipboards: Array<any> = [];
    clipboards.push(`File No.: ${tmsBill.label}`);
    clipboards.push(`Customer: ${tmsBill.customerFullName} ${TMSBillTransportationModeTools.getLabel(mode)}`);
    if (TMSBillTransportationModeTools.isExport(mode)) {
      clipboards.push(`Address: ${tmsBill.senderAddress}`);
    } else if (TMSBillTransportationModeTools.isImport(mode)) {
      clipboards.push(`Address: ${tmsBill.receiverAddress}`);
    } else {
      clipboards.push(`Sender Address: ${tmsBill.senderAddress}`);
      clipboards.push(`Receiver Address: ${tmsBill.receiverAddress}`);
    }
    clipboards.push(`Shipment Info: ${shipmentInfos.join(' ')}`);
    clipboards.push(`Truck No: ${truckNo}`);
    clipboards.push(`Date: ${util.text.formater.compactDate(tmsBill.dateTime)} - Time: ${tmsBill.time ?? ''}`);
    clipboards.push(`E.Time (Thời gian dự kiến xe đến): ${tmsBill.estimateTime ?? ''}`);

    navigator.clipboard.writeText(clipboards.join('\n'));
    appCtx.addOSNotification('success', T('Copy success'));
  }

  static createResponsibility = (bill: any, currentState: any, type: ResponsibilityType) => {
    let nextTime = currentState.toTime ? currentState.toTime : currentState.fromTime;
    let state = {
      code: `responsibility-${util.TimeUtil.toDateTimeIdFormat(new Date())}`,
      label: type,
      active: true,
      driverLoginId: '',
      fleetCode: '',
      tmsBillCode: currentState.tmsBillCode,
      type: type,
      fromTime: nextTime,
    }

    if (type === ResponsibilityType.Delivering) {
      state.driverLoginId = bill.deliveryDriverLoginId;
      state.fleetCode = bill.deliveryFleetCode;
    } else if (type === ResponsibilityType.PickingUp) {
      state.driverLoginId = bill.pickupDriverLoginId;
      state.fleetCode = bill.pickupFleetCode;
    }
    return state;
  }

  static createTMSBillActivity = (responsibility: any): any => {
    let activity = {
      label: '',
      code: `activity-${util.TimeUtil.toDateTimeIdFormat(new Date())}`,
      tmsBillCode: responsibility.tmsBillCode,
      tmsBillResponsibilityCode: responsibility.code,
      description: '',
    }
    let type: ResponsibilityType = responsibility.type;
    switch (type) {
      case ResponsibilityType.Sender:
        activity.label = 'Order has been created';
        activity.description = activity.label;
        break;
      case ResponsibilityType.PickingUp:
        activity.label = 'The driver is picking up your bill';
        activity.description = activity.label;
        break;
      case ResponsibilityType.TransportHub:
        activity.label = 'Your bill being transport hub';
        activity.description = activity.label;
        break;
      case ResponsibilityType.Transport:
        activity.label = 'Your bill is being transported';
        activity.description = activity.label;
        break;
      case ResponsibilityType.Delivering:
        activity.label = 'Your bill is being delivered by transporter';
        activity.description = activity.label;
        break;
      case ResponsibilityType.Receiver:
        activity.label = 'Your bill has been delivered to the recipient';
        activity.description = activity.label;
        break;
      default:
        break;
    }
    return activity;
  }

  static renderVerifiedField = (field: grid.FieldConfig, value: any) => {
    if (field.width) {
      value = util.text.formater.uiTruncate(value, field.width, true);
    }
    switch (value) {
      case Verified.AUTO:
        return (
          <div title={value} className="flex-hbox" style={{ color: '#0dcaf0', justifyContent: "center", fontWeight: "bold" }}>
            {value}
          </div>);
      case Verified.MANUAL:
        return (
          <div title={value} className="flex-hbox" style={{ color: 'green', justifyContent: "center", fontWeight: "bold" }}>
            {value}
          </div>);
      case Verified.NEED_VERIFY:
        return (
          <div title={value} className="flex-hbox" style={{ color: 'red', justifyContent: "center", fontWeight: "bold" }}>
            {value.replace(/([A-Z])/g, ' $1')}
          </div>);
      default: null;
    }
  }

  static renderOdooStatusField = (field: grid.FieldConfig, value: any) => {
    if (field.width) {
      value = util.text.formater.uiTruncate(value, field.width, true);
    }
    switch (value) {
      case OdooStatus.Draft:
        return (
          <div title={value} className="flex-hbox text-info"
            style={{ justifyContent: "center", fontWeight: "bold" }}>
            {value}
          </div>);
      case OdooStatus.Posted:
        return (
          <div title={value} className="flex-hbox text-success"
            style={{ justifyContent: "center", fontWeight: "bold" }}>
            {value}
          </div>);
      case OdooStatus.Cancelled:
        return (
          <div title={value} className="flex-hbox text-secondary"
            style={{ justifyContent: "center", fontWeight: "bold" }}>
            {value}
          </div>);
      default: null;
    }
  }

  static onAddRow = (ctx: grid.VGridContext, hbl?: any) => {
    let uiList = ctx.uiRoot as UITMSBillList;
    let { plugin } = uiList.props;
    let company = app.host.DATATP_HOST.session.getAccountAcl().getCompanyAcl();
    let companyCode = company.companyCode;
    let bill: any = {
      'uikey': `new/${uiList.idTrackerNext()}`,
      'label': '/',
      'office': companyCode.toUpperCase(),
      'openedDate': util.TimeUtil.toCompactDateTimeFormat(new Date),
      'type': TMSBillType.FORWARDER,
      'mode': TMSBillTransportationMode.Domestic,
      'processStatus': TMSBillProcessStatus.FOLLOWING,
      'quantityUnit': 'PKG',
      'weightUnit': 'KGM',
      'volumeUnit': 'CBM',
      'cod': 0,
      'goodsInsurance': 0,
      'shipmentCharge': 0,
      'extraShipmentCharge': 0,
      'totalTax': 0,
      'totalShipmentCharge': 0,
      'finalShipmentCharge': 0,
      'finalCharge': 0,
      'adjustGoodsInsurance': 0,
      'adjustExtraShipmentCharge': 0,
      'adjustShipmentCharge': 0,
      'adjustTotalTax': 0,
      'adjustTotalShipmentCharge': 0,
      'adjustFinalShipmentCharge': 0,
      'adjustFinalCharge': 0,
      'totalPaymentTax': 0,
      'finalPayment': 0,
      'profit': 0,

      'driverRating': '2',
      'vehicleArrivedOnTime': '2',
      'updateInfo': '2',
      'updateVehicleLocation': '2',
      'vendorRating': '1',
    }
    if (hbl) bill = this.updateBillWithHbl(hbl, bill);

    plugin.getRecords().unshift(bill);
    grid.initRecordStates([bill]);
    plugin.getListModel().filter();

    let state = grid.getRecordState(bill);
    state.markModified();
    ctx.getVGrid().forceUpdateView();
  }

  static updateBillWithHbl(hbl: any, bill: any) {
    let mode: any = `${hbl['typeOfTransportation']}_${hbl['purpose']}_${hbl['method']}`;
    mode = mode.toUpperCase();
    const importAndExport = hbl['importAndExport'];
    bill['hblId'] = hbl['id'];
    bill['label'] = hbl['fileNo'];
    bill['hwbNo'] = hbl['hblNo'];
    bill['verifyHblNo'] = hbl['verifyHblNo'];
    bill['customerId'] = hbl['customerId'];
    bill['customerFullName'] = hbl['customerFullName'];
    bill['office'] = hbl['root'];
    bill['mode'] = mode;
    bill['bookingCode'] = hbl['bookingCode'];
    bill['declarationNumber'] = hbl['declarationNumber'];
    bill['etaCutOffTime'] = importAndExport['etaCutOffTimeNote'];
    bill['carrierFullName'] = importAndExport['carrierFullName'];
    bill['carrierId'] = importAndExport['carrierId'];
    if (TMSBillTransportationModeTools.isImport(mode)) {
      bill['senderContact'] = importAndExport['warehouseContact'];
      bill['senderAddress'] = importAndExport['warehouseLocationLabel'];
      bill['senderLocationId'] = importAndExport['warehouseLocationId'];
    }
    if (TMSBillTransportationModeTools.isExport(mode)) {
      bill['receiverContact'] = importAndExport['warehouseContact'];
      bill['receiverAddress'] = importAndExport['warehouseLocationLabel'];
      bill['receiverLocationId'] = importAndExport['warehouseLocationId'];
    }
    return bill;
  }

  static cloneBill = (bill: any): any => {
    let clone = entity.EntityUtil.clone(bill, true);
    delete clone.code;
    delete clone.collect;
    delete clone.delayedTime;
    delete clone.estimateTime;
    delete clone.modifiedEstimateTime;
    delete clone.jobTrackingId;
    delete clone.jobOtherInfo;
    delete clone['_state'];
    delete clone['licensePlate'];
    delete clone['truckNo'];
    delete clone['vendorBills'];
    delete clone['trackings'];
    delete clone['messageId'];
    delete clone['messageStatus'];
    delete clone['vendorBillId'];
    delete clone['hblId'];
    delete clone['roundUsedStatus'];
    delete clone['roundUsedId'];
    delete clone['verifyHblNo'];
    delete clone['verifyVehicleInfo'];
    delete clone['verifyVehicleInfoNote'];
    delete clone['verifyPaymentInfo'];
    delete clone['verifyPaymentNote'];
    delete clone['closePayment'];
    delete clone['vehicleInfoLockDate'];
    delete clone['isCombine'];
    clone['uikey'] = `new/${util.IDTracker.next()}`;
    clone['dateTime'] = null;
    clone['planDeliverySuccessTime'] = null;
    clone['planPickupSuccessTime'] = null;
    clone['time'] = null;
    clone['processStatus'] = TMSBillProcessStatus.FOLLOWING;
    clone['cod'] = 0;
    clone['goodsInsurance'] = 0;
    clone['shipmentCharge'] = 0;
    clone['extraShipmentCharge'] = 0;
    clone['totalTax'] = 0;
    clone['totalShipmentCharge'] = 0;
    clone['finalShipmentCharge'] = 0;
    clone['finalCharge'] = 0;
    clone['adjustGoodsInsurance'] = 0;
    clone['adjustExtraShipmentCharge'] = 0;
    clone['adjustShipmentCharge'] = 0;
    clone['adjustTotalTax'] = 0;
    clone['adjustTotalShipmentCharge'] = 0;
    clone['adjustFinalShipmentCharge'] = 0;
    clone['adjustFinalCharge'] = 0;
    clone['totalPaymentTax'] = 0;
    clone['fixedPayment'] = 0;
    clone['extraPayment'] = 0;
    clone['totalPayment'] = 0;
    clone['finalPayment'] = 0;
    clone['profit'] = 0;
    if (clone.receiverLocationId == null || clone.receiverLocationStorageState != 'ACTIVE') {
      clone['receiverLocationAddress'] = null;
      clone['receiverAddress'] = null;
    }
    if (clone.senderLocationId == null || clone.senderLocationStorageState != 'ACTIVE') {
      clone['senderLocationAddress'] = null;
      clone['senderAddress'] = null;
    }

    let company = app.host.DATATP_HOST.session.getAccountAcl().getCompanyAcl();
    let companyCode = company.companyCode;
    if (!clone['office']) clone['office'] = companyCode.toUpperCase();
    return clone;
  }

  static onPrintReceiptOfDelivery = (pageContext: app.PageContext, ids: any[]) => {
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UITMSReceiptOfDeliveryPrint key={`print-${util.IDTracker.next()}`}
          appContext={appCtx} pageContext={pageCtx} billIds={ids} />
      )
    }
    pageContext.createPopupPage('receipt-of-delivery', T('Receipt Of Delivery'), createAppPage, { size: 'xl' });
  }

  static onPrintSubcontractorReporting = (ctx: grid.VGridContext) => {
    let uiList = ctx.uiRoot as UITMSBillList;
    let { pageContext, plugin } = uiList.props;
    let bills = plugin.getListModel().getSelectedRecordIds();
    let newRecords = plugin.getListModel().getRecords().find(sel => grid.getRecordState(sel).isMarkNew());
    if (newRecords) {
      bs.notificationShow("danger", "You need to save changes");
      return;
    }

    let onSubmit = (comment: string) => {
      let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
        let params = {
          tmsBillIds: bills,
          comment: comment,
        }
        return (
          <UITMSSubcontractorReportingPrint key={`print-${util.IDTracker.next()}`}
            appContext={appCtx} pageContext={pageCtx} params={params} />
        )
      }
      pageContext.createPopupPage('subcontractor-reporting', T('Subcontractor Reporting'), createAppPage, { size: 'xl' });
    }
    let createPageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      let bean = { comment: '' };
      return (
        <div className="flex-vbox">
          <div className="p-1">
            <input.BBTextField bean={bean} style={{ minHeight: '10em' }} label='Comment' field={'comment'} />
          </div>
          <bs.Toolbar>
            <bs.Button laf='secondary' onClick={() => { onSubmit(bean.comment) }}>
              <FeatherIcon.Command size={12} /> Submit
            </bs.Button>
          </bs.Toolbar>
        </div>
      )
    };
    pageContext.createPopupPage("print-subcontractor-reporting-comment", T('Comment'), createPageContent, { size: 'md', backdrop: "static" });
  }

  static renderButtonInfo = (fieldName: string, value: any) => {
    let showInfo = () => {
      bs.dialogShow(fieldName, (<div>{value}</div>),)
    }

    let btnContent = (
      <bs.Tooltip tooltip={value}>
        <FeatherIcon.Info size={12} />
      </bs.Tooltip>
    )

    if (!value) return null;
    return (
      <bs.Button className='flex-grow-0' laf='link' onClick={() => showInfo()}>
        {btnContent}
      </bs.Button>
    )
  }

  static renderTooltip = (title: any, content: any) => {
    return (
      <bs.CssTooltip position='top-right'>
        <bs.CssTooltipToggle className='flex-hbox'>
          {content}
        </bs.CssTooltipToggle>
        <bs.CssTooltipContent className="d-flex flex-column rounded">
          {title}
        </bs.CssTooltipContent>
      </bs.CssTooltip>
    )
  }

  static onRenderCellHeaderMultiplier = (field: grid.FieldConfig, multiplierBean: any, callback: (multiplier: number) => void) => {
    let multiplierLabel = multiplierBean['val'] > 1 ? `(x${multiplierBean['val']})` : '';
    return (
      <bs.Popover flex-hbox-grow-0 closeOnTrigger=".btn" placement='top'>
        <bs.PopoverToggle className={`p-0 ms-1 `} laf='link'>
          {`${field.label} ${multiplierLabel}`}
        </bs.PopoverToggle>
        <bs.PopoverContent>
          <div className='flex-hbox align-items-center'>
            <input.BBRadioInputField
              options={[1, 10, 100, 1000]} optionLabels={['x1', 'x10', 'x100', 'x1000']} bean={multiplierBean} field={'val'}
              onInputChange={(bean, field, oldVal, newVal) => {
                let multiplier = bean[field];
                callback(multiplier);
              }} />
          </div>
        </bs.PopoverContent>
      </bs.Popover >
    )
  }

  static showUploadVendorBillXLSXFile = (uiRoot: entity.DbEntityList, onPostUpload: () => void) => {
    let { appContext, pageContext } = uiRoot.props;
    let msaRestClient = appContext.getServerContext().getMsaRestClient();

    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      let config: component.XLSXProcessorConfig = {
        label: 'XLSX Files',
        processLabel: 'Process XLSX Files',
        msaRestClient: msaRestClient,
        grid: {
          actions: [
            {
              name: 'upload', label: 'Upload', icon: FeatherIcon.Upload,
              onClick: (ctx: grid.VGridContext) => {
                let uiRoot = ctx.uiRoot as component.XLSXProcessor;
                let { uiContext } = uiRoot.props;
                let records = ctx.model.getRecords();
                let appCtx: app.AppContext = uiContext?.getAppContext();
                let pageCtx: app.PageContext = uiContext?.getPageContext();
                appCtx.createHttpBackendCall('TMSVendorBillService', 'saveTMSVendorBillMapObjects', { records: records })
                  .withSuccessData(
                    (data: any) => {
                      pageCtx.back();
                      onPostUpload();
                    })
                  .withSuccessNotification('success', T('Upload success'))
                  .call()
              }
            }
          ]
        },
        state: new component.XLSXProcessorState()
      };
      let uiCtx: bs.UIContext = new bs.UIContext();
      uiCtx.withAppEnv(appCtx, pageCtx)
      return <component.XLSXProcessor config={config} uiContext={uiCtx} />;
    }
    pageContext.createPopupPage('excel-upload', T('Excel Upload'), createAppPage, { size: 'xl' })
  }
}

interface LoadDataProps extends app.AppComponentProps {
  label: string
}
export class LoadData extends app.AppComponent<LoadDataProps> {
  render() {
    let msg: bs.NotificationMessage =
      { type: 'info', label: this.props.label, detail: this.renderLoading() }
    return (
      <div className='flex-vbox'>
        <bs.UINotificationMessage msg={msg} />
      </div>
    )
  }
}

export interface BBTimeInputMaskProps extends bs.ELEProps {
  field: string;
  bean: any;
  label?: string;
  tabIndex?: number;
  focus?: boolean;
  disabled?: boolean;
  onInputChange?: (bean: any, field: string, oldVal: any, newVal: any) => void;
}

export class BBTimeInputMask extends Component<BBTimeInputMaskProps> {
  onInputChange = (timeValue: any) => {
    let { bean, field, onInputChange } = this.props;
    let oldVal = bean[field];
    if (timeValue) {
      if (!bean[field]) bean[field] = util.TimeUtil.toCompactDateTimeFormat(new Date());
      let date = util.TimeUtil.parseCompactDateTimeFormat(bean[field]);
      let sDate = util.TimeUtil.toCompactDateTimeFormat(date);
      let onlyDateValue = sDate.substring(0, sDate.indexOf('@') + 1);
      let timeZone = sDate.substring(sDate.indexOf('+'), sDate.length);
      bean[field] = `${onlyDateValue}${timeValue}:00${timeZone}`;
    } else {
      bean[field] = timeValue;
    }
    if (onInputChange) {
      onInputChange(bean, field, oldVal, timeValue);
    }
  }

  render() {
    let { bean, field, tabIndex, disabled, className, label, focus } = this.props;
    let value = bean[field];
    let date: Date = util.TimeUtil.parseCompactDateTimeFormat(value);
    let time = `${date.getHours().toString().padStart(2, "0")}:${date.getMinutes().toString().padStart(2, "0")}`;
    let inputHtml = (
      <input.BBTimeInputMask
        className={className} bean={{ 'time': time }} field={'time'} tabIndex={tabIndex} focus={focus} disabled={disabled}
        onInputChange={(_bean: any, _field: string, _oldVal: any, newVal: any) => this.onInputChange(newVal)}
      />
    );

    if (label) {
      return (
        <div className='bb-field'>
          <label className="form-label">{label}</label>
          {inputHtml}
        </div>
      )
    }
    return inputHtml;
  }
}

export interface UITMSCopyPasteProps extends bs.ELEProps {
  stopField?: string,
  ctx: grid.VGridContext,
  onCommit(): void,
}
export class UITMSCopyPaste extends Component<UITMSCopyPasteProps> {
  bean: any = {
    select: 'label',
    mode: 'Paste',
  }

  onRenderInputField = (dRecord: grid.DisplayRecord) => {
    this.bean['val'] = dRecord.getValue(this.bean['select']);
    let { ctx } = this.props;
    let fieldName = this.bean.select;
    let fieldConfig = ctx.getVGridConfigModel().getRecordConfigModel().getFieldConfig(fieldName);
    let onChange = (_bean: any, _fieldName: string, _oldVal: any, newVal: any) => {
      this.bean['val'] = newVal;
    }
    let fieldCtx: grid.FieldContext = {
      gridContext: ctx,
      displayRecord: dRecord,
      fieldConfig: fieldConfig,
      tabIndex: 0,
      focus: true
    }
    if (!fieldConfig.editor) return;
    if (fieldConfig.editor.renderCustom) {
      return fieldConfig.editor.renderCustom(fieldCtx, onChange);
    } else {
      if (fieldConfig.editor.type === 'string' || fieldConfig.editor.type === 'String') {
        return (
          <input.BBStringField bean={this.bean} field={'val'} onInputChange={onChange} />
        )
      }

      if (fieldConfig.editor.type === 'double' || fieldConfig.editor.type === 'Double') {
        return (
          <input.BBDoubleField bean={this.bean} field={'val'} onInputChange={onChange} />
        )
      }

      if (fieldConfig.editor.type === 'date' || fieldConfig.editor.type === 'Date') {
        return (
          <input.BBDateTimeField timeFormat={true} bean={this.bean} field={'val'} onInputChange={onChange} />
        )
      }
    }
  }

  onRenderText = (dRecords: Array<grid.DisplayRecord>) => {
    let content = '';
    let { ctx } = this.props;
    let fieldName = this.bean.select;
    let fieldConfig = ctx.getVGridConfigModel().getRecordConfigModel().getFieldConfig(fieldName);
    for (let dRec of dRecords) {
      let rec = dRec.record;
      if (fieldConfig.fieldDataGetter) {
        content += fieldConfig.fieldDataGetter(rec) ? fieldConfig.fieldDataGetter(rec) + " " : "";
      } else {
        content += rec[fieldConfig.name] ? rec[fieldConfig.name] + " " : "";
      }
    }
    this.bean['content'] = content;
    return (
      <input.BBTextField bean={this.bean} field='content' style={{ height: 280 }} />
    )
  }

  render(): React.ReactNode {
    let { ctx, stopField, onCommit } = this.props;
    let uiRoot = ctx.uiRoot as entity.DbEntityList;
    let { plugin, appContext, pageContext } = uiRoot.props;
    let dRecords: Array<grid.DisplayRecord> = plugin.getListModel().getSelectedDisplayRecords();
    let configFields: Array<grid.FieldConfig> = uiRoot.getVGridContext().getVGridConfigModel().getRecordConfigModel().allFields;
    let options = [];
    let optionsLabels = [];
    for (let sel of configFields) {
      if (!sel.state?.visible || !sel.editor) continue;
      if (sel.name === stopField) break;
      options.push(sel.name);
      optionsLabels.push(sel.label);
    }
    let dRecFirst = dRecords[0];
    return (
      <div className='flex-vbox' style={{ height: 350 }}>
        <input.BBRadioInputField bean={this.bean} field={'mode'} options={['Copy', 'Paste']} onInputChange={() => this.forceUpdate()} />
        {this.bean['mode'] === 'Paste' ?
          <div className='flex-vbox'>
            <div className='flex-grow-1'>
              <bs.Row>
                <bs.Col span={6}>
                  <input.BBSelectField bean={this.bean} field={'select'} options={options} optionLabels={optionsLabels}
                    onInputChange={() => this.forceUpdate()} />
                </bs.Col>
                <bs.Col span={6}>
                  {this.onRenderInputField(dRecords[0])}
                </bs.Col>
              </bs.Row>
            </div>
            <bs.Toolbar>
              <entity.WButtonEntityWrite appContext={appContext} pageContext={pageContext} outline label={T('Confirm')} icon={FeatherIcon.Copy}
                onClick={() => {
                  for (let dRec of dRecords) {
                    let record = dRec.record;
                    let mode = dRec.getValue('mode');
                    let selectField = this.bean.select;
                    if ((selectField === 'senderAddress' && TMSBillTransportationModeTools.isImport(mode)) ||
                      (selectField === 'receiverAddress' && TMSBillTransportationModeTools.isExport(mode))) {
                      record['warehouseLabel'] = dRecFirst.getValue('warehouseLabel');
                      record['warehouseId'] = dRecFirst.getValue('warehouseId');
                    }
                    if (selectField === 'carrierFullName') {
                      record['carrierFullName'] = dRecFirst.getValue('carrierFullName');
                      record['carrierId'] = dRecFirst.getValue('carrierId');
                    }
                    if (selectField === 'vendorFullName') {
                      record['vendorFullName'] = dRecFirst.getValue('vendorFullName');
                      record['vendorId'] = dRecFirst.getValue('vendorId');
                    }
                    if (selectField === 'customerFullName') {
                      record['customerFullName'] = dRecFirst.getValue('customerFullName');
                      record['customerId'] = dRecFirst.getValue('customerId');
                    }
                    let state = dRec.getRecordState();
                    state.markModified();
                    record[this.bean['select']] = this.bean['val'];
                  }
                  ctx.getVGrid().forceUpdateView();
                  onCommit();
                }} />
            </bs.Toolbar>
          </div>
          :
          <div className='flex-vbox'>
            <input.BBSelectField bean={this.bean} field={'select'} options={options} optionLabels={optionsLabels}
              onInputChange={() => this.forceUpdate()} />
            {this.onRenderText(dRecords)}
          </div>
        }
      </div>
    )
  }
}

export function TMSJoinFields(record: any, showLabel: any, ...fields: any): string {
  let contents = [];
  for (let fileName of fields) {
    if (!record[fileName]) continue;
    if (showLabel[fileName]) contents.push(`${showLabel[fileName]}:`);
    contents.push(record[fileName]);
  }
  return contents.join(' ');
}

export function TMSCssTooltip(value: string, style?: React.CSSProperties) {
  return (
    <bs.CssTooltip style={style}>
      <bs.CssTooltipToggle className='flex-hbox justify-content-start'>
        {value}
      </bs.CssTooltipToggle>
      <bs.CssTooltipContent
        className={`d-flex align-items-end text-secondary text-start`}>
        <div className='p-2'>
          {value}
        </div>
      </bs.CssTooltipContent>
    </bs.CssTooltip>
  )
}


