import React, { Component } from 'react';
import * as FeatherIcon from 'react-feather'
import { server, util, bs, grid, input, sql, app, entity, tree } from '@datatp-ui/lib';
import { module } from "@datatp-ui/erp";

import { T } from '../backend';
import { ManagementRestURL } from '../RestURL';

import { SyncCostType, TMSBillProcessStatus } from '../models';
import { UITMSBillListPageControl, updateTMSBillData } from './UITMSBillListPageControl';
import { UITMSBillFeeEditor } from './UITMSBillFee';
import { UIBFSOneTransactionList, UIBFSOneTransactionListPlugin } from 'app/logistics/bfsone/UIBFSOneTransactionList';
import { UITMSBillUtils, UITMSCopyPaste } from './UITMSBillUtils';
import { UITMSBillMessage } from './UITMSBillMessage';
import { createTMSBillRecordConfig } from './TMSBillRecordConfig';
import { TMS_BILL_JOB_TRACKING_COMPANY_CONFIG_CODE } from './UITMSJobTracking';
import { TMSBillEditor } from './UITMSBillEditor';
import { UITMSBillSummary } from './TMSBillSummary';
import { BBRefTMSPartnerAddress } from '../partner/BBRefTMSPartnerAddress';
import { TMSBillProcessStatusTools, TMSBillTransportationModeTools } from '../utils';

import BBRefLocation = module.settings.BBRefLocation;
export class UITMSBillListPlugin extends entity.DbEntityListPlugin {
  constructor(source?: 'INTERNAL_BILL') {
    super([]);
    this.backend = {
      context: 'company',
      service: 'TMSRestCallService',
      searchMethod: 'searchTMSBills'
    }
    this.searchParams = {
      "params": {},
      "filters": [
        ...sql.createSearchFilter(),
        {
          fields: ['searchHblNo'], "name": "searchHblNo", label: 'Hbl No', "type": "STRING_LIKE", "required": true, "filterValue": ''
        },
        {
          fields: ['searchFileNo'], "name": "searchFileNo", label: 'File No', "type": "STRING_LIKE", "required": true, "filterValue": ''
        },
      ],
      "optionFilters": [
        {
          name: 'source', label: 'Source', type: 'STRING', required: true, multiple: true,
          options: ['INTERNAL_BILL'],
          optionLabels: ['INTERNAL BILL'],
          selectOptions: [source ? source : ''],
        },
        sql.createStorageStateFilter([entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED])
      ],
      "rangeFilters": [
        // ...sql.createDateTimeFilter("date_time", "Date", dateRange),
      ],
      'maxReturn': 3000,
    }
  }

  loadData(uiList: entity.DbEntityList<any>) {
    let params = { 'params': this.searchParams }
    this.createBackendSearch(uiList, params).call();
  }

  withDateTime(dateRange: util.TimeRange, dateEmpty: boolean = true, cleanTo: boolean = true) {
    if (!this.searchParams) return this;
    this.addSearchParam("dateEmpty", dateEmpty);
    let fromValue = dateRange.fromFormat();
    let toValue = cleanTo ? null : dateRange.toFormat();
    this.searchParams.rangeFilters = [
      {
        "name": "date_time", "label": "Date", "type": "DATE", "required": true,
        "fromValue": fromValue, "toValue": toValue,
      }
    ];
    return this;
  }

  withMaxReturn(maxReturn: number) {
    if (this.searchParams) this.searchParams.maxReturn = maxReturn;
    return this;
  }

  withIds(ids: Array<any>) {
    this.addSearchParam("ids", ids);
    return this;
  }

  withDataScope = (dataScope?: app.AppDataScope) => {
    this.addSearchParam("dataScope", dataScope?.scope);
    return this;
  }

  hasModeratorPermission = (per: boolean) => {
    this.addSearchParam("moderatorPermission", per);
    return this;
  }

  withCustomerPermission() {
    this.addSearchParam('withCustomerPermission', true);
    return this;
  }

  withFilterByCompany(companyCode: string) {
    this.addSearchParam('companyCode', companyCode);
    return this;
  }

  withFilterByCosting() {
    this.addSearchParam('filterByCosting', true);
    return this;
  }

  withFilterByTruckInfo() {
    this.addSearchParam('filterByTruckInfo', true);
    return this;
  }

  withTMSPartnerId(fileName: 'customerId' | 'vendorId' | 'carrierId', id: number) {
    this.addSearchParam(fileName, id);
    return this;
  }

  withTMSPartnerTypeId(name: 'customerId' | 'vendorId' | 'carrierId', id: number) {
    this.addSearchParam(name, id);
    return this;
  }

  withExcludeRoundUsed() {
    this.addSearchParam("excludeRoundUsed", true);
    return this;
  }

  withProcessStatus(processStatus: TMSBillProcessStatus | null) {
    this.addSearchParam("processStatus", processStatus);
    return this;
  }

  withPushBfsOneCost(value: boolean) {
    this.searchParams!.params['pushBfsOneCost'] = value;
    return this;
  }

  withPushBfsOneVehicleInfo(value: boolean) {
    this.searchParams!.params['pushBfsOneVehicleInfo'] = value;
    return this;
  }
}

interface UITMSBillListProps extends entity.DbEntityListProps {
  jobTrackingProject?: any,
  view?: 'COSTING' | 'TRUCK_INFO'
}
export class UITMSBillList extends entity.DbEntityList<UITMSBillListProps> {
  idTracker: number = 0;
  idTrackerNext() { return ++this.idTracker; }
  multiplierBean: any = { val: 1000 };
  initConfig: any = { state: 'waiting' }
  status: string = "All";

  processingGoodsRequest = () => {
    let { appContext, pageContext, plugin } = this.props;
    let pluginImpl = plugin as UITMSBillListPlugin;
    let ids = pluginImpl.getListModel().getSelectedRecordIds();
    if (!ids || ids.length === 0) {
      bs.notificationShow("danger", T("Bill is not selected"), T("You should select at least 1 Record!!!"));
      return;
    }
    let newRecords = plugin.getListModel().getRecords().find(sel => grid.getRecordState(sel).isMarkNew());
    if (newRecords) {
      bs.notificationShow("danger", "You need to save changes");
      return;
    }

    let callback = (data: any) => {
      let createPopup = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
        return (<UITMSBillMessage appContext={appCtx} pageContext={pageCtx} context={this.getVGridContext()}
          plugin={plugin} observer={new entity.ComplexBeanObserver(data)} />)
      }
      pageContext.createPopupPage('processing-goods-request', T('Processing Goods Request'), createPopup, { size: 'lg' });
    }
    appContext.createHttpBackendCall('TMSPrintService', 'createCustomerServiceEmail', { ids: ids }).withSuccessData(callback).call();
  }

  _updateMessage = (message: any, allowCreateVendorBill: boolean) => {
    let { appContext, plugin } = this.props;
    let selected = plugin.getListModel().getSelectedRecords();
    let billIds = plugin.getListModel().getSelectedRecordIds();
    let successCb = (_data: any) => {
      if (allowCreateVendorBill) {
        this._requestVendorBill(billIds);
      } else {
        selected.forEach(sel => {
          sel['messageId'] = message.id;
          sel['messageStatus'] = message.status;
          this.getVGridContext().getVGrid().forceUpdateView();
        })
      }
    }
    const params = {
      messageId: message.id,
      billIds: billIds
    }
    appContext
      .createHttpBackendCall('TMSBillService', 'updateMessageId', params)
      .withSuccessData(successCb)
      .call();
  }

  _requestVendorBill = (ids: Array<number>) => {
    let { appContext, plugin } = this.props;
    let onRequestSuccess = (_data: any) => {
      let records = plugin.getListModel().getSelectedRecords();
      updateTMSBillData(this.getVGridContext(), records, () => {
        this.getVGridContext().getVGrid().forceUpdateView();
      });
    }
    appContext
      .createHttpBackendCall('TMSVendorBillService', 'requestVendorBill', { ids: ids })
      .withSuccessData(onRequestSuccess)
      .call();
  }

  processingGoodsRequestv2 = () => {
    let { appContext, pageContext, plugin } = this.props;
    let pluginImpl = plugin as UITMSBillListPlugin;
    let ids = pluginImpl.getListModel().getSelectedRecordIds();
    if (!ids || ids.length === 0) {
      bs.notificationShow("danger", T("Bill is not selected"), T("You should select at least 1 Record!!!"));
      return;
    }
    let newRecords = plugin.getListModel().getRecords().find(sel => grid.getRecordState(sel).isMarkNew());
    if (newRecords) {
      bs.notificationShow("danger", "You need to save changes");
      return;
    }

    let callback = (data: any) => {
      let message = data.message;
      let vendor = data.vendor;
      let ownerAccount = data.owner;
      let createPopup = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
        return (<UITMSBillMessage appContext={appCtx} pageContext={pageCtx} context={this.getVGridContext()}
          plugin={plugin} observer={new entity.ComplexBeanObserver(message)} vendor={vendor} ownerAccount={ownerAccount}
          onSendMessageSuccessCallBack={this._updateMessage} />)
      }
      pageContext.createPopupPage('processing-goods-request', T('Processing Goods Request'), createPopup, { size: 'xl' });
    }
    appContext.createHttpBackendCall('TMSPrintService', 'createCustomerServiceEmailv2', { ids: ids }).withSuccessData(callback).call();
  }

  onShowUIMessageCustomerFCL = () => {
    let { appContext, pageContext, plugin } = this.props;
    let pluginImpl = plugin as UITMSBillListPlugin;
    let ids = pluginImpl.getListModel().getSelectedRecordIds();
    if (!ids || ids.length === 0) {
      bs.notificationShow("danger", T("Bill is not selected"), T("You should select at least 1 Record!!!"));
      return;
    }
    let newRecords = plugin.getListModel().getRecords().find(sel => grid.getRecordState(sel).isMarkNew());
    if (newRecords) {
      bs.notificationShow("danger", "You need to save changes");
      return;
    }

    appContext.createHttpBackendCall('TMSPrintService', 'createCustomerFCLService', { ids })
      .withSuccessData((data: any) => {
        let createPopup = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (
            <module.communication.message.UIMessageEditor key={`message-${util.IDTracker.next()}`}
              appContext={appCtx} pageContext={pageCtx} onPostCommit={() => {
                pageContext.back();

              }}
              observer={new entity.ComplexBeanObserver(data)} />
          )
        }
        pageContext.createPopupPage('send-customer-fcl', T('Send Customer(FCL)'), createPopup, { size: 'xl' });
      })
      .call();
  }

  onShowSyncCost = (bill: any) => {
    let { appContext, pageContext } = this.props;
    let bean: any = {
      selectOps: SyncCostType.TMSBill,
    }
    if (!bill['totalPayment']) bean['selectOps'] = SyncCostType.VendorBill;

    let trackings = bill['trackings'];
    if (trackings) {
      if (bill['totalPayment'] == bill['tmsTrackingTotalCharge'] || bill['totalPayment'] == 0) {
        appContext.createHttpBackendCall('TMSRestCallService', 'syncCost', { id: bill.id, syncCostType: SyncCostType.VendorBill })
          .withSuccessData((_data: any) => {
            updateTMSBillData(this.getVGridContext(), [bill], (_records) => {
              appContext.addOSNotification("success", T(`Success`));
              this.getVGridContext().getVGrid().forceUpdateView();
            });
          })
          .call();
        return;
      } else {
        let confirm = () => {
          appContext.createHttpBackendCall('TMSRestCallService', 'syncCost', { id: bill.id, syncCostType: SyncCostType.VendorBill })
            .withSuccessData((_data: any) => {
              updateTMSBillData(this.getVGridContext(), [bill], (_records) => {
                appContext.addOSNotification("success", T(`Success`));
                this.getVGridContext().getVGrid().forceUpdateView();
              });
            })
            .call();
        }
        bs.dialogConfirmMessage('warning', 'Cost not balance!', confirm);
        return;
      }
    }

    if (bill['totalPayment'] == bill['vendorCost'] || bill['vendorCost'] == 0 || bill['totalPayment'] == 0) {
      let syncCostType = bill['totalPayment'] == 0 ? SyncCostType.VendorBill : SyncCostType.TMSBill;
      appContext.createHttpBackendCall('TMSRestCallService', 'syncCost', { id: bill.id, syncCostType: syncCostType })
        .withSuccessData((_data: any) => {
          updateTMSBillData(this.getVGridContext(), [bill], (_records) => {
            appContext.addOSNotification("success", T(`Success`));
            this.getVGridContext().getVGrid().forceUpdateView();
          });
        })
        .call();
      return;
    }

    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      let confirm = () => {
        appContext.createHttpBackendCall('TMSRestCallService', 'syncCost', { id: bill.id, syncCostType: bean.selectOps })
          .withSuccessData((_data: any) => {
            updateTMSBillData(this.getVGridContext(), [bill], (_records) => {
              appContext.addOSNotification("success", T(`Success`));
              this.getVGridContext().getVGrid().forceUpdateView();
              pageCtx.back();
            });
          })
          .call();
      }
      let options: Array<SyncCostType> = [SyncCostType.TMSBill, SyncCostType.VendorBill];
      return (
        <div>
          <input.BBRadioInputField field="selectOps" label="Choice" bean={bean}
            options={options} />
          <bs.Toolbar className='border' >
            <entity.WButtonEntityWrite
              appContext={appCtx} pageContext={pageCtx} icon={FeatherIcon.Save}
              label={T('Confirm')} onClick={confirm} />
          </bs.Toolbar>
        </div>
      )
    }
    pageContext.createPopupPage("sync-cost", T("Sync Cost"), createAppPage, { size: 'sm', backdrop: 'static' });
  }

  onShowTMSBillFee = (bill: any) => {
    let { appContext, pageContext } = this.props;
    let onPreCommit = (observer: entity.ComplexBeanObserver) => {
      onUpdateCostItems(observer);
    };

    let onUpdateCostItems = (observer: entity.ComplexBeanObserver) => {
      const items: Array<any> = [];
      const tmsBillFee = observer.createComplexBeanObserver('tmsBillFee', {});
      tmsBillFee.commitAndGet();
      items.push(...tmsBillFee.getBeanProperty('revenueItems', []));
      items.push(...tmsBillFee.getBeanProperty('costItems', []));
      tmsBillFee.replaceBeanProperty('items', items);
      delete tmsBillFee.getMutableBean().revenueItems;
      delete tmsBillFee.getMutableBean().costItems;
      observer.commitAndGet();
    }

    let callBack = (data: any) => {
      let observer = new entity.ComplexBeanObserver(data);
      let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {

        let onPostCommit = (bean: any) => {
          let tmsBillFee = bean['tmsBillFee'];
          let updateFields = [
            "fixedPayment", "extraPayment", "totalPayment", "finalPayment", 'profit'
          ];
          for (let property of updateFields) {
            bill[property] = tmsBillFee[property];
          }
          pageCtx.back()
          this.getVGridContext().getVGrid().forceUpdateView();
        }
        let tmsBillFee = observer.getBeanProperty('tmsBillFee', {});
        let forwarder = observer.getBeanProperty('tmsBillForwarderTransport', {});
        let closePayment = tmsBillFee['closePayment'];
        return (
          <div className='flex-vbox'>
            <UITMSBillFeeEditor type={'cost'}
              templateData={{
                'quantity': 1, 'unit': forwarder['truckType'],
                tmsPartnerName: forwarder['vendorFullName'], tmsPartnerId: forwarder['vendorId']
              }}
              appContext={appCtx} pageContext={pageCtx} observer={observer.createComplexBeanObserver('tmsBillFee', {})}
              readOnly={!pageContext.hasUserWriteCapability() || closePayment} />
            <bs.Toolbar className='border' hide={!pageContext.hasUserWriteCapability() || closePayment}>
              <entity.WButtonEntityCommit appContext={appContext} pageContext={pageContext} observer={observer} onPreCommit={onPreCommit}
                label={T("TMS Bill")} commitURL={ManagementRestURL.tmsBill.save} onPostCommit={onPostCommit} />
            </bs.Toolbar>
          </div>
        )
      }
      pageContext.createPopupPage('tms-bill-fee', T('TMS Bill Fee'), createAppPage, { size: 'lg' })
    }
    if (bill['totalPayment'] > 0) {
      appContext.createHttpBackendCall('TMSBillService', 'getTMSBillById', { id: bill.id })
        .withSuccessData(callBack)
        .call();
    } else {
      appContext.createHttpBackendCall('TMSBillService', 'copyToTmsBillCost', { id: bill.id })
        .withSuccessData((data) => {
          let updateFields = [
            "fixedPayment", "extraPayment", "totalPayment", "finalPayment", 'profit'
          ];
          if (data.tmsBillFee) {
            for (let property of updateFields) {
              bill[property] = data.tmsBillFee[property];
            }
            this.getVGridContext().getVGrid().forceUpdateView();
          }
          callBack(data)
        })
        .call();
    }
  }

  onShowSummary(_ctx: grid.VGridContext) {
    let uiRoot = _ctx.uiRoot as entity.DbEntityList;
    let { pageContext } = uiRoot.props;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UITMSBillSummary appContext={appCtx} pageContext={pageCtx} context={_ctx} />
      );
    }

    pageContext.createPopupPage('summary', T('TMS Bill Summary'), createAppPage, { size: 'xl' })
  }

  createVGridConfig() {
    let { type, pageContext, readOnly, plugin, view } = this.props;
    readOnly = readOnly ?? false;
    let pluginImpl = plugin as UITMSBillListPlugin;
    const writeCap = pageContext.hasUserWriteCapability();
    let thisUI = this;
    let configId = `tms-bill-list`;

    let actions = [
      ...entity.DbEntityListConfigTool.TOOLBAR_ACTION(!writeCap || type !== 'page', {
        name: 'summary', label: T('Summary'), hint: T('Summary'),
        icon: FeatherIcon.Activity,
        onClick: this.onShowSummary
      }),
      ...entity.DbEntityListConfigTool.TOOLBAR_ACTION(!writeCap || type !== 'page' || readOnly, {
        name: 'list-actions', label: T('Actions'), icon: FeatherIcon.List,
        createComponent: function (ctx: grid.VGridContext) {
          return <TMSBillListActions context={ctx} />
        }
      }),
      ...entity.DbEntityListConfigTool.TOOLBAR_ACTION(!writeCap || type !== 'page' || readOnly, {
        name: 'add-row', label: T('Add Row'), icon: FeatherIcon.Plus,
        onClick: UITMSBillUtils.onAddRow
      }),
      ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_DELETE(!pageContext.hasUserModeratorCapability() || type !== 'page' || readOnly, T('Del')),

    ];
    if (plugin.searchParams) {
      actions.push(
        module.settings.createVGridFavButton(configId)
      )
    }
    let config: grid.VGridConfig = {
      id: configId,
      title: `TMS Bills`,
      control: {
        state: {
          width: 120,
          collapse: false
        },
        label: 'Date',
        render: (ctx: grid.VGridContext) => {
          let { appContext, pageContext } = this.props;
          if (view == 'COSTING' || view == 'TRUCK_INFO') {
            return (
              <TMSBillTreeExplorer appContext={appContext} pageContext={pageContext} context={ctx} />
            );
          }
          return <></>
        }
      },
      record: createTMSBillRecordConfig(this, !writeCap || this.needSelector()),
      toolbar: {
        actions: actions,
        filters: entity.DbEntityListConfigTool.TOOLBAR_FILTERS(!!plugin.searchParams, 'filter'),
        filterActions: [
          ...entity.DbEntityListConfigTool.TOOLBAR_AUTO_REFRESH('auto-refresh-bill', T('Refresh')),
          {
            name: 'status', label: 'Status',
            createComponent(ctx) {
              const allValues: Array<any> = Object.values(TMSBillProcessStatus);
              return (
                <bs.Popover style={{ width: 66 }} className="d-flex flex-center w-100"
                  title={T('Status')} closeOnTrigger=".btn" >
                  <bs.Button laf='info' style={{ height: "100%" }}>
                    <FeatherIcon.Search size={12} /> {thisUI.status}
                  </bs.Button>
                  <bs.PopoverContent>
                    <div className='flex-vbox gap-2' style={{ width: '200px' }}>
                      <bs.Button laf={'secondary'} onClick={() => {
                        pluginImpl.withProcessStatus(null);
                        thisUI.status = 'All';
                        thisUI.reloadData();
                      }}>
                        All
                      </bs.Button>
                      {allValues.map((value: TMSBillProcessStatus) => {
                        return (
                          <bs.Button laf={TMSBillProcessStatusTools.getColor(value)} onClick={() => {
                            pluginImpl.withProcessStatus(value);
                            thisUI.status = TMSBillProcessStatusTools.getLabel(value)
                            thisUI.reloadData();
                          }}>
                            {TMSBillProcessStatusTools.getIcon(value)} {TMSBillProcessStatusTools.getLabel(value)}
                          </bs.Button>
                        )
                      })}
                    </div>
                  </bs.PopoverContent>
                </bs.Popover>

              )
            }
          },
          {
            name: 'pending-plans', label: 'Pending Plans',
            createComponent(ctx) {
              let l = plugin.getRecords().filter(sel => !sel['vendorBillId']).length;
              let pendingPlan = thisUI.initConfig['pendingPlan'];
              return (
                <bs.Button className={pendingPlan ? 'bg-primary bg-opacity-75' : ''} laf='primary' onClick={() => {
                  thisUI.initConfig['pendingPlan'] = !pendingPlan;
                  ctx.getVGrid().forceUpdateView();
                }}>
                  <FeatherIcon.Truck size={12} /> {T('Chua xu ly')} {l}
                </bs.Button>
              )
            }
          },
          {
            name: 'push', label: 'PUSH',
            createComponent(ctx) {
              if (view === 'COSTING' || view === 'TRUCK_INFO') {
                return (
                  <input.BBRadioInputField
                    bean={thisUI.initConfig} field={'state'}
                    options={['all', 'pushed', 'waiting']}
                    optionLabels={['All', 'Pushed', 'Waiting']} onInputChange={(bean: any, field: string, oldVal: any, newVal: any) => {
                      if (oldVal !== newVal) {
                        let params = pluginImpl.searchParams!.params;
                        if (view == 'COSTING') {
                          if (newVal == 'pushed') {
                            params['pushBfsOneCost'] = true;
                          } else if (newVal == 'waiting') {
                            params['pushBfsOneCost'] = false;
                          } else {
                            delete params['pushBfsOneCost']
                          }
                        }
                        if (view == 'TRUCK_INFO') {
                          if (newVal == 'pushed') {
                            params['pushBfsOneVehicleInfo'] = true;
                          } else if (newVal == 'waiting') {
                            params['pushBfsOneVehicleInfo'] = false;
                          } else {
                            delete params['pushBfsOneVehicleInfo']
                          }
                        }

                        thisUI.reloadData();
                        thisUI.getVGridContext().getVGrid().forceUpdateView();
                        bean.state = newVal;
                      }
                    }} />
                )
              }
              return <></>
            },
          }
        ],
      },
      footer: {
        selector: entity.DbEntityListConfigTool.FOOTER_MULTI_SELECT("Select", type),
        page: {
          hide: type == 'selector',
          render: (ctx: grid.VGridContext) => {
            return <UITMSBillListPageControl context={ctx} />
          }
        },
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table',
            footer: {
              createRecords: (ctx: grid.VGridContext) => this.createRecords(ctx)
            }
          },
          aggregation: {
            viewMode: 'aggregation',
            treeWidth: 150,
            createAggregationModel(_ctx: grid.VGridContext) {
              let sumAggregation = (label: string, enable: boolean = false) => {
                return new grid.SumAggregationFunction(
                  label,
                  ['quantity', 'weight', 'fixedPayment', 'extraPayment', 'totalPayment',
                    'vendorFixed', 'vendorExtra', 'vendorCost', 'profit'],
                  enable
                );
              }
              let model = new grid.AggregationDisplayModel(T('All'), false);
              model.addAggregation(
                new grid.DateValueAggregation(T("Date"), "dateTime", "YYYY/MM/DD", true)
                  .withSortBucket('desc')
                  .withAggFunction(sumAggregation('Total', true)));
              model.addAggregation(
                new grid.ValueAggregation(T("Customer"), "customerFullName", false)
                  .withSortBucket('asc')
                  .withAggFunction(sumAggregation('Total')));
              model.addAggregation(
                new grid.ValueAggregation(T("Verify Hbl No"), "verifyHblNo", false));
              return model;
            },
            footer: {
              createRecords: (ctx: grid.VGridContext) => this.createRecords(ctx)
            }
          }
        }
      }
    }

    let fields = config.record.fields;
    let computeCssClassesModifyBean = (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
      if (dRecord.isDataRecord()) {
        let state = dRecord.getRecordState();
        if (state.isMarkModified()) return 'fw-bolder fst-italic';
      }
      return '';
    }
    for (let sel of fields) {
      if (sel.name.startsWith("_")) continue;
      if (sel.listener) {
        if (!sel.listener.onDataCellEvent) {
          sel.listener.onDataCellEvent = (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            if (event.row == cell.getRow()) {
              cell.forceUpdate()
            }
          }
        }
      } else {
        sel.listener = {
          onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            if (event.row == cell.getRow()) {
              cell.forceUpdate()
            }
          },
        }
      }

      if (sel.computeCssClasses) {
        let cssClass = sel.computeCssClasses;
        sel.computeCssClasses = (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
          return `${cssClass(ctx, dRecord)} ${computeCssClassesModifyBean(ctx, dRecord)}`;
        }
      } else {
        sel.computeCssClasses = (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
          return computeCssClassesModifyBean(ctx, dRecord);
        }
      }
    }
    return config;
  }

  createRecords(_ctx: grid.VGridContext) {
    let { plugin } = this.props;
    let records = plugin.getListModel().getFilterRecords();
    let footerRecords = new Array<any>();
    let actives = util.CollectionMath.sum(
      records, [
      'quantity', 'weight', 'fixedPayment', 'extraPayment', 'totalPayment', 'finalPayment', 'profit'
    ], { label: 'Total Selected' });
    footerRecords.push(actives);
    return footerRecords;
  }

  onProcessTMSJobTrackingRule(context: grid.VGridContext, models: Array<any>) {
    let uiRoot = context.uiRoot as entity.DbEntityList;
    let { appContext } = uiRoot.props;
    models.forEach(sel => sel['jobTrackingUpdating'] = true)
    const successCB = (records: Array<any>) => {
      for (let model of models) {
        let findTracking = records.find(rec => rec['id'] === model['id']);
        if (findTracking) {
          model['jobTrackingId'] = findTracking['jobTrackingId'];
          model['stepDoneCount'] = findTracking['stepDoneCount'];
          model['currentStep'] = findTracking['currentStep'];
        }
        delete model['jobTrackingUpdating'];
      }
      context.getVGrid().forceUpdateView();
    }

    appContext
      .createHttpBackendCall('TMSRestCallService', 'autoBotTMSBillJobTracking', { models: models })
      .withSuccessData(successCB)
      .call();
  }

  onPostCommit = (context: grid.VGridContext, record: any) => {
    let { appContext } = this.props;
    updateTMSBillData(this.getVGridContext(), [record], (records) => {
      this.onProcessTMSJobTrackingRule(context, records);
      appContext.addOSNotification("success", T("Save Bills Success"));
      context.getVGrid().forceUpdateView();
    });
  }

  onDefaultSelect(dRecord: grid.DisplayRecord) {
    let { appContext, pageContext } = this.props;
    let bill = dRecord.record;
    const closePayment = bill['closePayment'];
    appContext.createHttpBackendCall('TMSBillService', 'getTMSBillById', { id: bill.id })
      .withSuccessData((data: any) => {
        let createUI = () => {
          return (
            <TMSBillEditor appContext={appContext} pageContext={pageContext} readOnly={!pageContext.hasUserWriteCapability() || closePayment}
              observer={new entity.ComplexBeanObserver(data)}
              onPostCommit={() => this.onPostCommit(this.getVGridContext(), bill)} />
          );
        }
        this.getVGridContext().getVGrid().addTab(bill.code, createUI);
        this.getVGridContext().getVGrid().forceUpdateView();
      })
      .call();
  }

  onDeleteAction(): void {
    let { appContext, plugin } = this.props;
    let tmsBillIds = plugin.getListModel().getSelectedRecordIds();
    let callbackConfirm = () => {
      appContext.createHttpBackendCall('TMSBillService', 'deleteTMSBills', { model: tmsBillIds })
        .withSuccessData((_data: any) => {
          appContext.addOSNotification("success", T('Delete TMS Bill Success'));
          plugin.getListModel().getSelectedRecords().forEach(sel => plugin.getListModel().removeRecord(sel));
          this.getVGridContext().getVGrid().forceUpdateView();
        })
        .withFailNotification('danger', T('Delete TMS Bill Fail!'))
        .call();
    }

    bs.dialogConfirmMessage('Confirm Message', T('Confirm Delete TMS Bill'), callbackConfirm);
  }

  onShowStopLocations(bill: any) {
    let { appContext, pageContext } = this.props

    appContext.createHttpBackendCall('TMSRestCallService', 'getTMSBillById', { id: bill.id })
      .withSuccessData((data: any) => {
        let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          let observer = new entity.ComplexBeanObserver(data);
          return (
            <div className='flex-vbox'>
              <TMSBillStopLocationList appContext={appCtx} pageContext={pageCtx} style={{ height: 350 }} customerId={bill['customerId']}
                partnerId={bill['customerPartnerId']}
                purposeType={TMSBillTransportationModeTools.isImport(bill['mode']) ? 'ReturnGoods' : 'CollectGoods'}
                plugin={observer.createVGridEntityListEditorPlugin('stopLocations', [])}
                dialogEditor={true} editorTitle={T("Stop Locations")}
              />
              <bs.Toolbar className='border'>
                <entity.ButtonEntityCommit
                  appContext={appCtx} pageContext={pageCtx} observer={observer}
                  onPostCommit={() => {
                    bill['stopLocations'] = observer.getComplexArrayProperty('stopLocations', []);
                    this.nextViewId();
                    this.forceUpdate();
                    pageCtx.back()
                  }}
                  commit={{ service: 'TMSRestCallService', commitMethod: 'saveTMSBill', entityLabel: "TMS Bill" }} />
              </bs.Toolbar>
            </div>
          )

        }
        pageContext.createPopupPage('stop-locations', T('Stop Locations'), createAppPage, { size: 'lg', backdrop: 'static' })
      })
      .call();
  }

  onClearPaymentStatus(bill: any) {
    let { appContext } = this.props;
    appContext
      .createHttpBackendCall('TMSRestCallService', 'updatePaymentStatus', { billFeeId: bill['billFeeId'], paymentStatus: null })
      .withSuccessData((data: any) => {
        bill['paymentStatus'] = null;
        this.getVGridContext().getVGrid().forceUpdateView();
      })
      .withSuccessNotification('success', T('Clear Payment Status Success'))
      .call();
  }
}

class TMSBillListActions extends Component<grid.VGridContextProps> {
  onLoadBFSOneData = () => {
    let { context } = this.props;
    let uiList = context.uiRoot as UITMSBillList;
    let { appContext, pageContext } = uiList.props;

    let onMultiSelect = (_appCtx: app.AppContext, pageCtx: app.PageContext, records: Array<any>) => {
      let successCB: server.SuccessCallback = (_result: any) => {
        appContext.addOSNotification("success", T('Convert BFSOne Transaction Data Success'));
        pageCtx.back();
        uiList.reloadData();
      }
      let params = {
        bfsOneRecords: records
      };
      appContext.createHttpBackendCall('TMSRestCallService', 'convertBFSOneDataToTMSBills', params).withSuccess(successCB).call();
    }

    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UIBFSOneTransactionList
          appContext={appCtx} pageContext={pageCtx} plugin={new UIBFSOneTransactionListPlugin()} type='selector'
          onSelect={(_appCtx: app.AppContext, _pageCtx: app.PageContext, bean: any) => onMultiSelect(_appCtx, _pageCtx, [bean])}
          onMultiSelect={onMultiSelect} />
      )
    }
    pageContext.createPopupPage('bfsone-data-list', T('BFSOne Data'), createAppPage, { size: 'xl', backdrop: 'static' });
  }

  onProcessRequest = () => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as UITMSBillList;
    uiRoot.processingGoodsRequest();
  }

  onShowTMSCopyPaste = () => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as entity.DbEntityList;
    let { pageContext, plugin } = uiRoot.props;

    let dRecords: Array<grid.DisplayRecord> = plugin.getListModel().getSelectedDisplayRecords();
    if (dRecords.length == 0) {
      bs.notificationShow('danger', 'You need to select at least 1 record!!!');
      return;
    }

    let createAppPage = (_appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UITMSCopyPaste ctx={context} stopField={'truckNo'}
          onCommit={() => pageCtx.back()} />
      )
    }
    pageContext.createPopupPage('copy-paste', T('Copy/Paste'), createAppPage);
  }

  render() {
    let { context } = this.props;
    const buttonStyle = "mb-1 w-150 text-start";
    return (
      <bs.Popover flex-hbox-grow-0 title={'Actions'} closeOnTrigger=".btn">
        <bs.PopoverToggle laf='primary' outline>
          <FeatherIcon.List size={12} /> {'Actions'}
        </bs.PopoverToggle>
        <bs.PopoverContent>
          <div className='flex-vbox'>
            <bs.Button laf='secondary' outline className={buttonStyle} onClick={this.onShowTMSCopyPaste}>
              <FeatherIcon.List size={12} /> {T('Copy/Paste')}
            </bs.Button>
            <bs.Button laf='secondary' outline className={buttonStyle} onClick={() => UITMSBillUtils.onPrintSubcontractorReporting(context)}>
              <FeatherIcon.List size={12} /> {T('Subcontractor Reporting')}
            </bs.Button>
            <bs.Button laf='secondary' outline className={buttonStyle} onClick={this.onLoadBFSOneData}>
              <FeatherIcon.List size={12} /> {T('BFSOne Data')}
            </bs.Button>
            <bs.Button laf='secondary' outline className={buttonStyle} onClick={this.onProcessRequest}>
              <FeatherIcon.List size={12} /> {T('Process Request')}
            </bs.Button>
          </div>
        </bs.PopoverContent>
      </bs.Popover>
    );
  }
}

interface UIJobProjectTMSBillListProps extends app.AppComponentProps {
  listPlugin: UITMSBillListPlugin;
}
export class UIJobProjectTMSBillList extends app.AppComponent<UIJobProjectTMSBillListProps> {
  jobTrackingProject: any;
  constructor(props: UIJobProjectTMSBillListProps) {
    super(props);
    let { appContext } = this.props;
    this.markLoading(true);
    let params = {
      companyConfigCode: TMS_BILL_JOB_TRACKING_COMPANY_CONFIG_CODE
    }
    appContext.createHttpBackendCall('JobTrackingService', 'getJobTrackingProjectByCompanyConfigCode', params)
      .withSuccessData((data: any) => {
        this.jobTrackingProject = data;
        this.markLoading(false);
        this.forceUpdate();
      })
      .call();
  }

  render(): React.ReactNode {
    if (this.isLoading()) return;
    let dateRange = new util.TimeRange();
    dateRange.fromSetDate(new Date());
    dateRange.fromSubtract(2, 'month');
    dateRange.fromStartOf('month');
    dateRange.toEndOf('month');

    let { appContext, pageContext, listPlugin } = this.props;

    return (
      <UITMSBillList key={util.IDTracker.next()} type='page' jobTrackingProject={this.jobTrackingProject}
        plugin={listPlugin.withDateTime(dateRange)}
        appContext={appContext} pageContext={pageContext} readOnly={!pageContext.hasUserWriteCapability()} />
    )
  }
}

export interface TMSBillStopLocationListProps extends entity.VGridEntityListEditorProps {
  partnerId: number;
  customerId: number;
  purposeType: 'CollectGoods' | 'ReturnGoods';
}

export class TMSBillStopLocationList extends entity.VGridEntityListEditor<TMSBillStopLocationListProps> {
  viewId = util.IDTracker.next();

  createVGridConfig() {
    let { appContext, pageContext, customerId, partnerId } = this.props;
    let thisUI = this;
    let onInputChange = (fieldCtx: grid.FieldContext, oldVal: any, newVal: any) => {
      let dRecord = fieldCtx.displayRecord;
      let field = fieldCtx.fieldConfig;
      let ctx = fieldCtx.gridContext;
      let event: grid.VGridCellEvent = {
        row: dRecord.row, field: field, event: 'Modified', data: dRecord
      }
      ctx.broadcastCellEvent(event);
    };


    let writeCap = pageContext.hasUserWriteCapability();
    let config: grid.VGridConfig = {
      record: {
        editor: {
          supportViewMode: ['table'],
          enable: true
        },
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          {
            name: 'contact', label: T('Contact'), width: 250,
            state: { showRecordState: true },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row === cell.getRow()) {
                  cell.forceUpdate()
                }
              },
            },
            editor: { type: 'string', onInputChange: onInputChange }
          },
          {
            name: 'address', label: T('Street Name'), width: 250,
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let stopLocation = dRecord.record;
                const oldVal = stopLocation[field.name];
                return (
                  <BBRefTMSPartnerAddress minWidth={500}
                    autofocus={focus} tabIndex={tabIndex} allowUserInput
                    appContext={appContext} pageContext={pageContext} placeholder='Address' bean={stopLocation} customerId={customerId}
                    beanIdField='locationId' beanLabelField={field.name} types={['None']} partnerId={partnerId}
                    onPostCommit={(partnerAddress: any) => {
                      stopLocation["locationLabel"] = partnerAddress['locationLabel'];
                    }}
                    onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                      bean["locationLabel"] = _selectOpt['locationLabel'];
                      thisUI.viewId = util.IDTracker.next();
                      onInputChange(bean, field.name, oldVal, bean[field.name]);
                    }}
                  />
                )
              },
            }
          },
          {
            name: 'locationId', label: T('Location'), width: 250,
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let stopLocation = dRecord.record;
                const oldVal = stopLocation[field.name];
                return (
                  <BBRefLocation key={thisUI.viewId} locationTypes={['Subdistrict', 'District']} tabIndex={tabIndex} autofocus={focus} required minWidth={500}
                    appContext={appContext} pageContext={pageContext} bean={stopLocation} disable={!writeCap} refLocationBy='id'
                    beanIdField={'locationId'} beanLabelField={'locationLabel'} beanRefLabelField='label' placeholder='Location'
                    onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                      onInputChange(bean, field.name, oldVal, bean[field.name]);
                    }} />
                )
              },
            },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row === cell.getRow() && event.field.name === 'address') {
                  cell.forceUpdate()
                }
              },
            },
          },
          {
            name: 'purposeType', label: T('Purpose Type'),
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let stopLocation = dRecord.record;
                return (
                  <input.BBSelectField tabIndex={tabIndex} focus={focus}
                    bean={stopLocation} field={'purposeType'} options={['CollectGoods', 'ReturnGoods']}
                    onInputChange={onInputChange} disable={!writeCap
                    }
                  />
                )
              },
            }
          },
          { name: 'note', label: T('Note'), width: 250, editor: { type: 'text', onInputChange: onInputChange } },
        ],
        computeDataRowHeight: (_ctx: grid.VGridContext, dRec: grid.DisplayRecord) => {
          return 35;
        },
        control: {
          width: 25,
          items: [
            {
              name: 'copy', hint: 'Copy', icon: FeatherIcon.Copy,
              onClick: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
                let stopLocation = dRecord.record;
                stopLocation = { ...stopLocation, id: null };
                ctx.model.insertDisplayRecordAt(dRecord.row, stopLocation);
                grid.getRecordState(stopLocation).markNew(true);
                ctx.getVGrid().forceUpdateView();
              },
            },
          ]
        }
      },
      toolbar: {
        actions: [
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_ADD(!writeCap, T('Add')),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_DELETE(!writeCap, T('Delete'))
        ],
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          }
        },
      },

    }
    return config;
  }

  onNewAction() {
    let { plugin, purposeType } = this.props;
    let newRecord = { purposeType: purposeType };
    plugin.getModel().addRecord(newRecord)
    grid.getRecordState(newRecord).markModified();
    this.vgridContext.getVGrid().forceUpdateView();
  }
}

export class TMSBillTreeExplorer<T extends entity.VGridComponentProps> extends entity.VGridExplorer<T> {
  createConfig() {
    let explorerConfig: entity.ExplorerConfig = { actions: [] };
    return this.createTreeConfig(explorerConfig);
  }

  createTreeModel(): tree.TreeModel {
    let { context } = this.props;
    let uiList = context.uiRoot as UITMSBillList;

    return new TMSBillTreeModel(this.props.appContext, true, uiList);
  }

  onSelectNode = (node: tree.TreeNode) => {
    let { context } = this.props;
    let uiList = context.uiRoot as entity.DbEntityList;
    let { plugin } = uiList.props;
    let pluginImp = plugin as UITMSBillListPlugin;
    let dateRange = new util.TimeRange();
    let date: Date = node.userData;

    if (node.label === 'All') {
      let fromDate = new Date();
      fromDate.setHours(0, 0, 0, 0);
      let toDate = new Date();
      if (pluginImp.getSearchParams().params['filterByCosting']) {
        fromDate.setDate(fromDate.getDate() - 13);
        toDate.setDate(new Date().getDate() - 4);
      } else if (pluginImp.getSearchParams().params['filterByTruckInfo']) {
        fromDate.setDate(fromDate.getDate() - 10);
        toDate.setDate(new Date().getDate() - 1)
      } else {
        fromDate.setDate(fromDate.getDate() - 9);
        toDate = new Date();
      }
      toDate.setHours(23, 59, 59, 999);
      dateRange.fromSetDate(fromDate)
      dateRange.toSetDate(toDate);
    } else {
      let fromDate: Date = new Date(date.getTime())
      dateRange.fromSetDate(fromDate);
      let toDate = new Date(fromDate.getTime());
      toDate.setHours(23, 59, 59, 999);
      dateRange.toSetDate(toDate);
    }

    pluginImp.withDateTime(dateRange, false, false);
    uiList.reloadData();
    this.forceUpdate();
  }
}

class TMSBillTreeModel extends tree.TreeModel {
  appContext: app.AppContext;
  uiList: UITMSBillList;
  constructor(appContext: app.AppContext, showRoot: boolean = true, uiList: UITMSBillList) {
    super(showRoot);
    this.appContext = appContext;
    let root = new tree.TreeNode(null, "", "", "All", null, false);
    this.setRoot(root);
    this.uiList = uiList;
  }

  loadChildren(node: tree.TreeNode, postLoadCallback?: (node: tree.TreeNode) => void): any {
    let { view } = this.uiList.props;
    const last10Dates = Array.from({ length: 10 }, (_, i) => {
      const d = new Date();
      d.setHours(0, 0, 0, 0);
      if (view === 'COSTING') {
        d.setDate(d.getDate() - (i + 4));
      } else if (view === 'TRUCK_INFO') {
        d.setDate(d.getDate() - (i + 1));
      }
      return d;
    });
    for (let date of last10Dates) {
      let dateStr = util.TimeUtil.format(date, 'DD/MM/YYYY');
      let childNode = this.addChild(node, dateStr, dateStr, date);
      childNode.setLoadedChildren();
    }
    node.setLoadedChildren();
    if (postLoadCallback) postLoadCallback(node);
  }
}

