import React, { Component } from 'react';
import { bs, grid, app, entity, sql } from '@datatp-ui/lib';
import * as FeatherIcon from 'react-feather'

import { T } from '../backend';
import { UITMSBillList, UITMSBillListPlugin } from './UITMSBillList';
import { TMSUtils, WButtonListenDataChange } from '../utils';


interface WBtnVendorBillChangeLogProps extends app.AppComponentProps {
  records?: any[];
  context: grid.VGridContext;
}
export class WBtnTMSBillChangeLog extends app.AppComponent<WBtnVendorBillChangeLogProps> {
  records: any[];
  constructor(props: WBtnVendorBillChangeLogProps) {
    super(props);
    this.records = props.records ? [...props.records] : [];
  }
  searchAndShowBill = (ids: any[]) => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as UITMSBillList;
    let { appContext, pageContext, plugin } = uiRoot.props;
    const popupTMSBillChangeLog = (records: any[]) => {
      this.records = records;
      this.forceUpdate();
      let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
        return (
          <TMSBillChangeLog context={context} appContext={appCtx} pageContext={pageCtx} records={records} />
        )
      }
      pageContext.createPopupPage('check-log', 'Log', createAppPage, { size: 'xl' });
    }
    if (ids.length == 0) {
      popupTMSBillChangeLog([]);
    } else {
      let sqlParams: sql.SqlSearchParams = entity.EntityUtil.clone(plugin.getSearchParams(), true);
      if (!sqlParams.params) sqlParams.params = {};
      sqlParams.params['ids'] = ids;

      let backend = plugin.backend;
      if (backend && backend.searchMethod) {
        appContext.createHttpBackendCall(backend.service, backend.searchMethod, { 'params': sqlParams })
          .withSuccessData((data) => {
            popupTMSBillChangeLog(data);
          })
          .call();
      }
    }
  }

  onShowRecords = () => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as UITMSBillList;
    let { appContext } = uiRoot.props;
    appContext.createHttpBackendCall("TMSVendorBillService", "findTMSBillIdsChanged")
      .withSuccessData((ids) => {
        this.searchAndShowBill(ids);
      })
      .call();
  }

  render(): React.ReactNode {
    let findContainerNos = this.records.filter(sel => sel['updateContainerNo']);
    let findCostings = this.records.filter(sel => sel['updateCosting']);
    return (
      <bs.Button laf='warning' outline={!(findContainerNos.length > 0 || findCostings.length > 0)}
        onClick={() => this.onShowRecords()}>
        <FeatherIcon.AlertTriangle size={14} /> {'Check Log'}
      </bs.Button>

    )
  }
}
class TMSBillChangeLog extends app.AppComponent<WBtnVendorBillChangeLogProps> {
  componentWillUnmount(): void {
    let { context } = this.props;
    let uiRoot = context.uiRoot as UITMSBillList;
    uiRoot.reloadData();
  }
  render(): React.ReactNode {
    let { appContext, pageContext, records } = this.props;
    if (!records) records = [];
    let findContainerNos = records.filter(sel => sel['updateContainerNo']);
    let findCostings = records.filter(sel => sel['updateCosting']);
    return (
      <bs.TabPane >
        <bs.Tab name='container-no' label={`Container No (${findContainerNos.length})`} active>
          <UITMSBillList key={'container-no'} type='selector' appContext={appContext} pageContext={pageContext} plugin={new entity.DbEntityListPlugin(findContainerNos)} />
        </bs.Tab>
        <bs.Tab name='costing' label={`Costing (${findCostings.length})`}>
          <UITMSBillList key={'costing'} type='selector' appContext={appContext} pageContext={pageContext} plugin={new entity.DbEntityListPlugin(findCostings)} />
        </bs.Tab>
      </bs.TabPane>
    )
  }
}


interface ConfirmContainerNoProps extends app.AppComponentProps {
  context: grid.VGridContext;
  bean: any;
}
export class ConfirmContainerNo extends app.AppComponent<ConfirmContainerNoProps> {
  onConfirmContainerNo() {
    let { context, appContext, bean } = this.props;
    let request = {
      id: bean['id'],
      containerNo: bean['vendorBillContainerNo'],
      sealNo: bean['vendorBillSealNo'],
    }
    appContext.createHttpBackendCall("TMSVendorBillService", "confirmUpdateContainerNo", { request })
      .withSuccessData((_data) => {
        bean['containerNo'] = bean['vendorBillContainerNo'];
        bean['sealNo'] = bean['vendorBillSealNo'];
        bean['updateContainerNo'] = false;
        context.getVGrid().forceUpdateView();
      })
      .withEntityOpNotification('commit', 'Update Container No')
      .call();
  }

  render(): React.ReactNode {
    let { bean } = this.props;
    if (!bean['updateContainerNo']) return;
    let html = (
      <bs.Button className='m-1' laf='link'
        onClick={() => this.onConfirmContainerNo()}>
        <FeatherIcon.Check className='text-danger' size={14} />
      </bs.Button>
    );
    let content = (
      <div>
        {'Container No: ' + bean['vendorBillContainerNo']}
        {'SealNo: ' + bean['sealNo']}
        {'Updated: ' + bean['vendorBillModifiedContainerNo']}
      </div>
    )
    return TMSUtils.renderTooltip(html, content);
  }
}
