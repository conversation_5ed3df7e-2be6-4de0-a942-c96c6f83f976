export const ManagementRestURL = {
  ops: {
    saveAttachments: (opsId: number) => { return `/logistics/tms/ops/${opsId}/attachments`; },
    loadAttachments: (opsId: number) => { return `/logistics/tms/ops/${opsId}/attachments`; },
  },

  tmsBill: {
    save: '/logistics/tms/bill',
    search: '/logistics/tms/bill/search',
    verified: '/logistics/tms/bill/verified',
    searchTmsBillReport: '/logistics/tms/bill/search-tms-bill-report',
    saveState: '/logistics/tms/bill/storage-state',
    matchZone: '/logistics/tms/bill/match-zone',
    matchPlace: '/logistics/tms/bill/match-place',
    syncTMSGoodItems: '/logistics/tms/bill/sync/good-items',
    calculateTransportReport: '/logistics/tms/transport-report/calculate',
    convertSpreadsheetTracking: '/logistics/tms/bill/spreadsheet-tracking/convert',
    printPODURL: (format: string) => {
      return `/logistics/tms/bill/print/receipt-of-delivery/${format}`;
    },
    printSubcontractorReportingURL: (format: string) => {
      return `/logistics/tms/bill/print/subcontractor-reporting/${format}`;
    },
    saveAttachments: (billId: number, entityId?: number, entityName?: string) => {
      if (!entityId && !entityName) return `/logistics/tms/bill/${billId}/attachments`;
      return `/logistics/tms/bill/${billId}/${entityId}/${entityName}/attachments`;
    },
    loadAttachments: (billId: number) => { return `/logistics/tms/bill/${billId}/attachments`; },
  },

  tmsJournalEntry: {
    search: '/logistics/tms/odoo/journal-entry/search',
    load: (code: string) => { return `logistics/tms/odoo/journal-entry/${code}` },
  },

  tmsAccountPayment: {
    search: '/logistics/tms/odoo/account-payment/search',
    load: (code: string) => { return `logistics/tms/odoo/account-payment/${code}` },
  },

  // Customer Contract
  tmsCustomerContractCharge: {
    match: '/logistics/tms/sale/tms-cus-contract-charge/match',
  },
  tmsBillActivity: {
    save: '/logistics/tms/bill-activity',
  },

  partner: {
    save: 'logistics/tms/sale/tms-partner',
    search: 'logistics/tms/sale/tms-partner/search',
    saveState: 'logistics/tms/sale/tms-partner/storage-state',
  },

  odooResourceMapping: {
    load: (type: String, resourceId: String) => { return `odoo/resource-mapping/${type}/${resourceId}` },
    save: 'odoo/resource-mapping',
    search: 'odoo/resource-mapping/search',
    saveState: 'odoo/resource-mapping/storage-state',
  },

  odooAccounting: {
    createJournalEntryAnalyzer: '/logistics/tms/accounting/odoo/journal-entry',
    saveGeneralJournalEntry: '/logistics/tms/accounting/odoo/journal-entry/general',
  },

  transporterReport: {
    save: `logistics/tms/transporter/report`,
  },

  transporterKPIConfig: {
    active: `logistics/tms/transporter/config/active`,
    create: `logistics/tms/transporter/config/create`,
  },

  transporterReportItem: {
    load(code: string) { return `logistics/tms/report/transport/item/${code}` },
  },

  customerReport: {
    load(code: string) { return `logistics/tms/customer/report/${code}` },
    loadById(id: number) { return `logistics/tms/customer/report/id/${id}` },
    search: 'logistics/tms/customer/report/search',
    calculateCustomerReport: 'logistics/tms/customer/report/calculate',
  },

  partnerTMSBill: {
    load(tmsBillId: number) { return `logistics/tms/bill/partner-tms/${tmsBillId}` },
    search: '/logistics/tms/bill/partner-tms/search',
    setStorageState: 'bill/partner-tms/storage-state',
  },

  partnerTMSBillData: {
    load(tmsBillId: number) { return `logistics/tms/bill/partner-tms-data/${tmsBillId}` }
  },
}

export const VehicleFleetURL = {
  vehicleTripGoodsTracking: {
    load(id: string) { return `/logistics/fleet/vehicle/vehicle-trip-goods-tracking/${id}` },
    save: '/logistics/fleet/vehicle/vehicle-trip-goods-tracking',
    search: '/logistics/fleet/vehicle/vehicle-trip-goods-tracking/search',
    split: '/logistics/fleet/vehicle/vehicle-trip-goods-tracking/split',
    distanceInKm(id: number) { return `/logistics/fleet/vehicle/vehicle-trip-goods-tracking/distance-in-km/bill/${id}` },
    clone(id: number) { return `/logistics/fleet/vehicle/vehicle-trip-goods-tracking/clone/${id}` },
    removeVehicleTrip(id: number) { return `/logistics/fleet/vehicle/vehicle-trip-goods-tracking/${id}/remove-vehicle-trip` },
  },

  vehicle: {
    loadById(id: number) { return `logistics/fleet/vehicle/id/${id}` },
    save: '/logistics/fleet/vehicle',
    saveStates: '/logistics/fleet/vehicle/storage-state',
  },

  vehicleRefuel: {
    save: '/logistics/fleet/vehicle/vehicle-refuel',
  },

  vehicleTrip: {
    save: '/logistics/fleet/vehicle/vehicle-trip',
    saveList: '/logistics/fleet/vehicle/vehicle-trips',
    create: '/logistics/fleet/vehicle/vehicle-trip/create',
    saveAttachments: (tripId: number) => { return `/logistics/fleet/vehicle/vehicle-trip/${tripId}/attachments`; },
    loadAttachments: (tripId: number) => { return `/logistics/fleet/vehicle/vehicle-trip/${tripId}/attachments`; },
  },


};