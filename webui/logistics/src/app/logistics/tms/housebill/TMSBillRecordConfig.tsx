import React from 'react';
import * as FeatherIcon from 'react-feather'
import { util, bs, grid, input, app, entity } from '@datatp-ui/lib';
import { module } from "@datatp-ui/erp";

import { T } from '../backend';

import {
  TMSBillProcessStatus,
  TMSRoundUsedStatus,
  VendorCostStatus
} from '../models';
import {
  TMSBillProcessStatusTools,
  TMSBillTransportationModeTools,
  TMSRoundUsedStatusTools,
  TMSUtils, TMSVGridConfigTool
} from '../utils';
import {
  renderBtnTrackingAPI,
  UITMSVendorBillList,
} from '../../vendor/UITMSVendorBillList';

import { updateTMSBillData } from '../bill/UITMSBillListPageControl';
import { TMSCssTooltip, TMSJoinFields, UITMSBillUtils } from '../bill/UITMSBillUtils';
import { UITMSGoodsTracking, UIVendorBillInfo } from '../../vendor/api/TMSTrackingPlugin';
import { renderSummary } from '../bill/TMSBillRecordSummaryConfig';
import { BBOptionAddress, BBRefTMSCustomer } from '../partner/BBRefTMSCustomer';
import { BBRefTMSCarrier } from '../partner/BBRefTMSCarrier';
import { NotificationMessage, WBtnMail } from '../bill/UITMSBillMessage';

import { BBRefTMSPartnerAddress } from '../partner/BBRefTMSPartnerAddress';
import { BBRefVehicleFleet } from '../vehicle/BBRefVehicleFleet';
import { UITMSHouseBillList } from './TMSHouseBillGeneralList';
import { Method, MethodTools, Purpose, PurposeTools, TypeOfTransportation } from './models';
import { showTMSHouseBillEditor, TMSHouseBillGeneralEditor } from './UITMSHouseBillEditor';
import { WBtnBFSOneTemplate, WBtnBFSOneTemplateCost } from './BFSOneApi';
import BBRefLocation = module.settings.BBRefLocation;

const formatCurrency = (val: any) => { return util.text.formater.currency(val, 0) };
const isValidTimeFormat = (input: string) => {
  const regex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
  return regex.test(input);
}

export class WTMSGridInsertRow extends grid.WGridInsertRow {
  override onInsert = () => {
    let { context, row, createInsertRecord, allowInsert } = this.props;
    let dRecord = context.model.getDisplayRecordList().getDisplayRecordAt(row);

    let insert = true;
    if (allowInsert) insert = allowInsert(context, dRecord);

    if (insert) {
      let newRecord = createInsertRecord(context, dRecord)
      context.model.insertDisplayRecordAt(row, newRecord);
      let state = grid.getRecordState(newRecord);
      state.markModified();
      context.getVGrid().forceUpdateView(true);
    }
  }
}

function RenderGoodsTrackingButton(
  ctx: grid.VGridContext, bill: any, config?: { btnSize: number, iconSize: number, cssClass?: any }) {
  let trackings: Array<any> = bill.trackings;
  let totalTrucking = 0;
  let truckInfos = [];
  for (let tracking of trackings) {
    if (tracking['vehicleLabel']) {
      totalTrucking++;
      truckInfos.push(TMSJoinFields(
        tracking,
        { 'mobile': '\nMobile', 'identificationNo': '\nID' },
        'vehicleType', 'vehicleLabel', 'driverFullName', 'mobile', 'identificationNo'
      ));
    }
  }
  let countIcon;
  countIcon = (
    <div className={'align-items-center justify-content-center text-center'}
      style={{
        height: 12, width: 12, borderRadius: 50, fontSize: 12,
        backgroundColor: 'red', color: 'white',
        marginTop: -6, marginLeft: -4
      }}>
      {totalTrucking}
    </div>
  );
  let tooltip = truckInfos.join(', \n');
  if (totalTrucking == 0) tooltip = T('No Vehicle Information Yet!!!');

  let btnSize = 22;
  let iconSize = 12
  let cssClass = '';
  if (config) {
    iconSize = config.iconSize;
    btnSize = config.btnSize;
    cssClass = config.cssClass;
  }
  return (
    <bs.Button style={{ width: btnSize, marginLeft: 4 }} className={`text-primary ${cssClass}`}
      onClick={() => OnShowTrackings(ctx, bill)} laf='link'>
      <bs.Tooltip tooltip={tooltip} className='flex-hbox'>
        <FeatherIcon.Truck size={iconSize} />
        {countIcon}
      </bs.Tooltip>
    </bs.Button>
  );
}

function OnShowTrackings(ctx: grid.VGridContext, bill: any) {
  let uiRoot = ctx.uiRoot as entity.DbEntityList;
  let smallScreen = bs.ScreenUtil.isSmallScreen();
  let { appContext, pageContext } = uiRoot.props;
  if (bill['trackings'] == null) return;
  let successCB = (data: any) => {
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UITMSGoodsTracking appContext={appCtx} pageContext={pageCtx} data={data} />
      )
    }
    pageContext.createPopupPage('vehicle-trip-goods-trackings', T('Trackings'), createAppPage, { size: smallScreen ? 'xl' : 'lg' })
  }
  appContext
    .createHttpBackendCall('VehicleRestCallService', 'findTrackingInfoByTMSBillId', { tmsBillId: bill['id'] })
    .withSuccessData(successCB)
    .call();
}
function formatName(name: string) {
  if(name && name.indexOf('-') !== -1){
    name = name.split('-')[0].trim();
  }
  return name;
}

function RenderVendorTrackingButton(ctx: grid.VGridContext, bill: any, config?: { btnSize: number, iconSize: number, cssClass?: any }) {
  let vendorBills = bill['vendorBills'];
  let vendorBillInfo = T('No Vehicle Information Yet!!!');
  let totalTruck = 0;
  let infos = [];
  if (vendorBills) {
    for (let vendorBill of vendorBills) {
      if (vendorBill['licensePlate']) {
        totalTruck++;
        infos.push(
          TMSJoinFields(
            vendorBill,
            { 'driverMobile': '\nMobile', 'driverIdentificationNo': '\nID' },
            'vehicleType', 'licensePlate', 'driverFullName', 'driverMobile', 'driverIdentificationNo'
          ));
      }
    }
  }
  if (infos.length > 0) vendorBillInfo = infos.join(", \n");
  if (totalTruck == 0 && !bill.vendorBillSendVendor) {
    return <WBtnMail context={ctx} bill={bill} />
  };
  let countIcon = (
    <div className={'align-items-center justify-content-center text-center'}
      style={{
        height: 12, width: 12, borderRadius: 50, fontSize: 12,
        backgroundColor: 'red', color: 'white',
        marginTop: -6, marginLeft: -4
      }}>
      {totalTruck}
    </div>
  );

  let btnSize = 22;
  let iconSize = 12
  let cssClass = '';
  if (config) {
    iconSize = config.iconSize;
    btnSize = config.btnSize;
    cssClass = config.cssClass;
  }
  return (
    <bs.Button style={{ width: btnSize, marginLeft: 4 }}
      className={`text-secondary ${cssClass}`} onClick={() => OnShowVendorBill(ctx, bill)} laf='link'>
      <bs.Tooltip tooltip={vendorBillInfo} className='flex-hbox'>
        <FeatherIcon.Truck size={iconSize} />
        {countIcon}
      </bs.Tooltip>
    </bs.Button>
  );
}

function OnShowVendorBill(ctx: grid.VGridContext, bill: any) {
  let uiRoot = ctx.uiRoot as entity.DbEntityList;
  let { appContext, pageContext } = uiRoot.props;
  if (bill.vendorBillId == null) return;
  const successCB = (vendorBill: any) => {
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UIVendorBillInfo appContext={appCtx} pageContext={pageCtx} vendorBill={vendorBill} />
      )
    }
    pageContext.createPopupPage('vendor-bill', T('Vendor Bill'), createAppPage, { size: 'xl' });
  }
  appContext
    .createHttpBackendCall('TMSVendorBillService', 'loadVendorBillInfo', { vendorBillId: bill.vendorBillId })
    .withSuccessData(successCB)
    .call();

}

function RenderTrackingButton(
  _ctx: grid.VGridContext, bill: any, config?: { btnSize: number, iconSize: number, cssClass?: any }) {
  if (bill.roundUsedStatus) return;
  let vendorBillId = bill.vendorBillId;
  if (bill['trackings']) return RenderGoodsTrackingButton(_ctx, bill, config);
  if (vendorBillId) return RenderVendorTrackingButton(_ctx, bill, config);
  return;
}

function renderRoundUsedStatus(ctx: grid.VGridContext, bill: any, config?: { iconSize: number, fontSize: number, cssClass?: any }) {
  let uiRoot = ctx.uiRoot as entity.DbEntityList;
  let roundUsedStatus: TMSRoundUsedStatus = bill.roundUsedStatus;
  if (!roundUsedStatus) return;
  let onUpdateStatus = (newStatus: TMSRoundUsedStatus) => {
    let { appContext } = uiRoot.props;
    appContext.createHttpBackendCall('TMSRoundUsedService', 'updateTMSRoundUsedStatus', { id: bill.roundUsedId, status: newStatus })
      .withSuccessData((_data: any) => {
        bill.roundUsedStatus = newStatus;
        appContext.addOSNotification("success", T(`${newStatus} Round Used Success`));
        ctx.getVGrid().forceUpdateView();
      })
      .call();
  }
  let btnHeight = 15;
  let btnWidth = 18;
  let fontSize = 12;
  let cssClass = '';
  if (config) {
    btnHeight = config.iconSize;
    btnWidth = config.iconSize;
    fontSize = config.fontSize;
    cssClass = config.cssClass;
  }

  let bgCssClass = TMSRoundUsedStatusTools.getBgColor(roundUsedStatus);
  if (roundUsedStatus === TMSRoundUsedStatus.SendingFromRu) {
    return (
      <bs.Popover flex-hbox-grow-0 closeOnTrigger=".btn" placement='left' className='flex-hbox-grow-1'>
        <bs.PopoverToggle className={`p-0 ${bgCssClass}`} style={{ height: 15, width: 18, fontSize: 11, borderRadius: 3 }}>
          <bs.Tooltip tooltip={`Round Used: ${roundUsedStatus}`} className='text-white fw-bold text-center'>
            {'RU'}
          </bs.Tooltip>
        </bs.PopoverToggle>
        <bs.PopoverContent>
          <div className={`flex-hbox align-items-center ${cssClass}`}>
            <bs.Button laf='success' style={{ height: btnHeight, width: btnWidth, fontSize: fontSize, borderRadius: 5 }}
              className='p-2 mx-1' onClick={() => onUpdateStatus(TMSRoundUsedStatus.Processing)}>
              {'Confirm'}
            </bs.Button>
            <bs.Button laf='danger' style={{ height: btnHeight, width: btnWidth, fontSize: fontSize, borderRadius: 5 }}
              className='p-2 mx-1' onClick={() => onUpdateStatus(TMSRoundUsedStatus.Cancel)}>
              {'Reject'}
            </bs.Button>
          </div>
        </bs.PopoverContent>
      </bs.Popover >
    )
  }
  return (
    <div className={`flex-grow-0 flex-hbox align-items-center ${cssClass}`}>
      <bs.Button laf='primary' style={{ height: btnHeight, width: btnWidth, fontSize: fontSize, borderRadius: 3 }}
        className={`p-0 ${bgCssClass}`}>
        <bs.Tooltip tooltip={`Round Used: ${roundUsedStatus}`} className='text-white fw-bold text-center'>
          {'RU'}
        </bs.Tooltip>
      </bs.Button>
    </div>
  );
}

export const WHIcon = () => {
  return (
    <div className='text-center border-right text-primary fw-bold text-decoration-underline'
      style={{ fontSize: '0.4rem' }}>
      {'WH'}
    </div>
  )
}

export function createTMSBillRecordConfig(uiRoot: UITMSHouseBillList, allowSelector: boolean): grid.RecordConfig {
  let { appContext, pageContext, plugin, type, readOnly, hbl } = uiRoot.props;
  const writeCap = pageContext.hasUserWriteCapability();
  const modCap = pageContext.hasUserModeratorCapability();

  let _aggSumCell = (cell: grid.VGridCell, event: grid.VGridCellEvent, field: any) => {
    let bucketId = cell.getDisplayRecord().bucketId;
    let dRecord: grid.DisplayRecord = event.data;
    if (!bucketId || bucketId != dRecord.bucketId) return;
    let aggModel = plugin.getListModel().getDisplayRecordList() as grid.AggregationDisplayModel;
    let bucket = aggModel.getRootBucket().findBucketById(bucketId);
    if (bucket) {
      let record = cell.getDisplayRecord().record;
      let records = bucket.records;
      let aggSum = util.CollectionMath.sum(records, [field]);
      record[field] = aggSum[field];
      cell.forceUpdate();
    }
  }

  let _sumFooter = (record: any) => {
    let fields = [
      'quantity', 'weight', 'fixedPayment', 'extraPayment', 'totalPayment',
      'vendorFixed', 'vendorExtra', 'vendorCost', 'profit'
    ];
    let records = plugin.getListModel().getSelectedRecords();
    let sum = util.CollectionMath.sum(records, fields);
    let volume = 0;
    for (let rec of records) {
      let volumeAsText = rec['volumeAsText'];
      if (!volumeAsText) continue;
      if (TMSUtils.isDecimal(volumeAsText)) volume += Number(volumeAsText)
    }
    record['volumeAsText'] = volume;
    for (let fieldName of fields) {
      record[fieldName] = sum[fieldName];
    }
    record['label'] = `Total Selected (${records.length})`;
    uiRoot.getVGridContext().getVGrid().forceUpdateView();
  }

  let _onDeliveryPlanCellEvent = (field: grid.FieldConfig, cell: grid.VGridCell) => {
    let deliveryPlan = cell.getDisplayRecord().getValue('deliveryPlan');
    if (deliveryPlan && field.name == 'deliveryPlan') {
      let dateTime: Date = util.TimeUtil.parseCompactDateTimeFormat(deliveryPlan);
      let dateNow: Date = new Date();
      if ((dateTime.getFullYear() < dateNow.getFullYear()) || (dateTime.getFullYear() > dateNow.getFullYear() + 1)) {
        let content = (
          <div>
            <div>
              <span>You are creating a different year file</span>
              (<span className='fw-bold'>{dateTime.getFullYear()}</span>).
            </div>
            <div>
              <span>
                You want to update to
              </span>
              <span className='fw-bold px-1'>
                {dateNow.getFullYear()}
              </span>
            </div>
          </div>
        )
        bs.dialogConfirmMessage('Confirm delivery plan', content, () => {
          dateTime.setFullYear(dateNow.getFullYear());
          cell.getDisplayRecord().record['deliveryPlan'] = util.TimeUtil.javaCompactDateTimeFormat(dateTime);
          cell.forceUpdate();
        });
      }
    }
  }

  const _onChangeCost = (field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
    let billCostFields = ['fixedPayment', 'extraPayment'];
    if (billCostFields.includes(field.name)) {
      dRecord.record[field.name] = dRecord.record[field.name] * uiRoot.multiplierBean['val'];
    }
  }
  const _onChangeTime = (field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
    const onCalculateDelayedTime = (bill: any) => {
      let sDateTime = bill['deliveryPlan'];
      let ddmmyy = util.text.formater.compactDate(sDateTime);
      let estimateTime = bill['estimateTime'];
      let time = bill['time'];
      if (sDateTime && isValidTimeFormat(estimateTime) && isValidTimeFormat(time)) {
        let dateTime = util.TimeUtil.parseCompactDateTimeFormat(`${ddmmyy}@${time}:00+0700`);
        let estimateDateTime = util.TimeUtil.parseCompactDateTimeFormat(`${ddmmyy}@${estimateTime}:00+0700`);
        let estimateDeliveryMinutes = estimateDateTime.getHours() * 60 + estimateDateTime.getMinutes();
        let planDeliveryMinutes = dateTime.getHours() * 60 + dateTime.getMinutes();
        let delayedTime = estimateDeliveryMinutes - planDeliveryMinutes;
        bill['delayedTime'] = delayedTime;
      }
    }
    const onCalculateArrivedOnTime = (bill: any) => {
      if (bill['delayedTime'] <= 0) {
        bill['vehicleArrivedOnTime'] = 2;
      } else if (bill['delayedTime'] <= 30) {
        bill['vehicleArrivedOnTime'] = 1;
      } else {
        bill['vehicleArrivedOnTime'] = 0;
      }
    }
    if (field.name === 'time' || field.name === 'estimateTime') {
      onCalculateDelayedTime(dRecord.record);
      onCalculateArrivedOnTime(dRecord.record);
    }
  }

  const _updateSenderAddress = (bill: any, address: any) => {
    const locStorageState = address['locStorageState'];
    if (locStorageState && locStorageState != 'ACTIVE') {
      bs.dialogShow('Inactive Address', <h4 className='text-danger'>{'Địa chỉ đã dừng hoạt động!!!'}</h4>);
    }
    bill['senderAddress'] = address['address'];
    bill['senderLocationId'] = address['locationId'];
    bill["senderInvAddress"] = address['invAddress'];
    bill["senderPartnerAddressId"] = address['id'];
  }

  const _updateReceiverAddress = (bill: any, address: any) => {
    const locStorageState = address['locStorageState'];
    if (locStorageState && locStorageState != 'ACTIVE') {
      bs.dialogShow('Inactive Address', <h4 className='text-danger'>{'Địa chỉ đã dừng hoạt động!!!'}</h4>);
    }
    bill['receiverAddress'] = address['address'];
    bill['receiverLocationId'] = address['locationId'];
    bill["receiverInvAddress"] = address['invAddress'];
    bill["receiverPartnerAddressId"] = address['id'];
  }

  const _onLoadCustomerAddress = (ctx: grid.VGridContext, bill: any, overrideAddress: boolean = false) => {
    if (!bill['customerId']) return;
    let purpose = bill['purpose'];
    let sqlParams = {
      params: {
        'customerId': bill['customerId'],
        'locationStorageState': 'ACTIVE',
      }
    }
    appContext.createHttpBackendCall('TMSPartnerService', 'searchPartnerAddresses', { params: sqlParams })
      .withSuccessData((addresses: Array<any>) => {
        if (addresses.length == 0) return;
        const pickupAddresses: Array<any> = addresses.filter(sel => sel['type'] == 'Export' || sel['type'] == 'None');
        const deliveryAddresses: Array<any> = addresses.filter(sel => sel['type'] == 'Import' || sel['type'] == 'None');
        if (PurposeTools.isExport(purpose)) {
          if (bill['senderLocationId'] && !overrideAddress) return;
          if (pickupAddresses.length == 1) {
            let first = pickupAddresses[0];
            _updateSenderAddress(bill, first);
          } else {
            const createContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return (<BBOptionAddress
                appContext={appCtx} pageContext={pageCtx} addresses={pickupAddresses} onSelectOptions={(address) => {
                  _updateSenderAddress(bill, address);
                  pageCtx.back();
                  ctx.getVGrid().forceUpdateView();
                }}
              />)
            }
            pageContext.createPopupPage('', 'Choose Address', createContent);
          }
          return;
        }
        if (PurposeTools.isImport(purpose)) {
          if (bill['receiverLocationId'] && !overrideAddress) return;
          if (deliveryAddresses.length == 1) {
            let first = deliveryAddresses[0];
            _updateReceiverAddress(bill, first);
          } else {
            const createContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return (<BBOptionAddress
                appContext={appCtx} pageContext={pageCtx} addresses={deliveryAddresses} onSelectOptions={(address) => {
                  _updateReceiverAddress(bill, address);
                  pageCtx.back();
                  ctx.getVGrid().forceUpdateView();
                }}
              />)
            }
            pageContext.createPopupPage('', 'Choose Address', createContent)
          }
          return;
        }

        if (pickupAddresses.length == 1 && overrideAddress) {
          let first = pickupAddresses[0];
          _updateSenderAddress(bill, first);
        }
        if (deliveryAddresses.length == 1 && overrideAddress) {
          let first = deliveryAddresses[0];
          _updateReceiverAddress(bill, first);
        }
      })
      .call();
  }

  const renderMeasurement = (hblValue: any, billContent: any) => {
    if (hbl) return billContent;
    return (
      <div className='flex-hbox'>
        <div className={'flex-vbox text-primary fw-bold w-50 opacity-50 justify-content-center pt-1 ps-1'}
          style={{ fontSize: 12.5 }}>
          {hblValue}
        </div>
        <div className='flex-grow-1 text-end'>
          {billContent}
        </div>
      </div>
    )
  }

  const verifyHblNo = (hblNo: any, callBack: (verifyHblNo: boolean) => void) => {
    appContext.createHttpBackendCall('TMSHouseBillService', 'verifyHblNo', { hblNo: hblNo })
      .withSuccessData((data) => {
        callBack(data);
      })
      .withFail(() => {
        callBack(false);
      })
      .call();
  }

  const onInputChange = (fieldCtx: grid.FieldContext, oldVal: any, newVal: any) => {
    let dRecord = fieldCtx.displayRecord;
    let field = fieldCtx.fieldConfig;
    let ctx = fieldCtx.gridContext;
    let event: grid.VGridCellEvent = {
      row: dRecord.row, field: field, event: 'Modified', data: dRecord
    }
    let record = dRecord.record;
    _onChangeTime(field, dRecord);
    _onChangeCost(field, dRecord);
    if (field.name === 'customerFullName') _onLoadCustomerAddress(ctx, record, true);
    if (field.name === 'purpose') {
      if (oldVal != newVal) {
        _onLoadCustomerAddress(ctx, record, true);
      } else {
        _onLoadCustomerAddress(ctx, record);
      }
    }
    if (field.name === 'truckNo' || field.name === 'licensePlate') record['updateVehicleInfo'] = true;
    let hblFields: any[] = [
      ...hblGroupFields,
      ...measurementFields
    ];
    let purpose = record['purpose'];
    if (PurposeTools.isExport(purpose)) {
      hblFields.push('receiverAddress');
    }
    if (PurposeTools.isImport(purpose)) {
      hblFields.push('senderAddress');
    }
    hblFields.push('deliveryPlan');
    if (hblFields.includes(field.name)) record['updateHouseBill'] = true;
    ctx.broadcastCellEvent(event);
  };

  const hblGroupFields: any[] = [
    'fileNo', 'customerFullName', 'hblNo', 'root', 'purpose', 'method', 'typeOfTransportation',
    'carrierFullName', 'bookingCode', 'etaCutOffTimeNote', 'declarationNumber'
  ];
  const measurementFields: any[] = [
    'quantity', 'quantityUnit', 'weight', 'volume', 'volumeAsText'
  ];
  const controlWidth = bs.ScreenUtil.isSmallScreen() ? 25 : 45;
  //RecordConfig
  let records: grid.RecordConfig = {
    computeDataRowHeight: (_ctx: grid.VGridContext, dRec: grid.DisplayRecord) => {
      let rec = dRec.record;
      if (dRec.isDataRecord()) {
        let stopLocations = rec['stopLocations'];
        if (stopLocations && stopLocations.length > 1) {
          let stopCount = stopLocations.length;
          let height = stopCount * 25;
          return height;
        }
        return 35;
      }
      return 20;
    },
    control: {
      width: controlWidth,
      items: [
        {
          name: 'copy', hint: 'Copy', icon: FeatherIcon.Plus,
          customRender: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
            let create = (_ctx: grid.VGridContext, houseBill: any, atRecord: grid.DisplayRecord) => {
              let bill = UITMSBillUtils.cloneBill(atRecord.record);
              let mode: any = `${houseBill['purpose']}_${houseBill['method']}_${houseBill['typeOfTransportation']}`;
              mode = mode.toUpperCase();
              const importAndExport = houseBill['importAndExport'];
              bill = {
                ...bill,
                hblId: houseBill['id'],
                label: houseBill['fileNo'],
                hwbNo: houseBill['hblNo'],
                verifyHblNo: houseBill['verifyHblNo'],
                customerId: houseBill['customerId'],
                customerFullName: houseBill['customerFullName'],
                office: houseBill['root'],
                mode: mode,
                bookingCode: houseBill['bookingCode'],
                declarationNumber: houseBill['declarationNumber'],
                etaCutOffTime: importAndExport['etaCutOffTimeNote'],
                carrierFullName: importAndExport['carrierFullName'],
                carrierId: importAndExport['carrierId'],
              };
              if (TMSBillTransportationModeTools.isImport(mode)) {
                bill['senderContact'] = importAndExport['warehouseContact'];
                bill['senderAddress'] = importAndExport['warehouseLocationLabel'];
                bill['senderLocationId'] = importAndExport['warehouseLocationId'];
              }
              if (TMSBillTransportationModeTools.isExport(mode)) {
                bill['receiverContact'] = importAndExport['warehouseContact'];
                bill['receiverAddress'] = importAndExport['warehouseLocationLabel'];
                bill['receiverLocationId'] = importAndExport['warehouseLocationId'];
              }
              return bill;
            }
            if (!hbl) {
              return (
                <bs.Button laf='link' onClick={() => {
                  if (MethodTools.isFcl(dRecord.record.method)) {
                    appContext.createHttpBackendCall('TMSHouseBillService', 'getTMSHouseBillById', { 'hblId': dRecord.record.hblId })
                      .withSuccessData((data: any) => {
                        let cloneHbl = entity.EntityUtil.clone(data, true);
                        let bill = create(ctx, cloneHbl, dRecord);
                        let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
                          return (
                            <TMSHouseBillGeneralEditor
                              appContext={appCtx} pageContext={pageCtx}
                              observer={new entity.ComplexBeanObserver(cloneHbl)}
                              houseBillPlugin={new entity.DbEntityListPlugin([bill])}
                              onPostCommit={() => uiRoot.reloadData()}
                            />
                          )
                        }
                        pageContext.createPopupPage('clone-tms-house-bill', 'New House Bill', createAppPage, { size: 'xl' });
                      })
                      .call();
                  } else {
                    let bill = UITMSBillUtils.cloneBill(dRecord.record);
                    ctx.model.insertDisplayRecordAt(dRecord.row, bill);
                    let state = grid.getRecordState(bill);
                    state.markModified();
                    ctx.getVGrid().forceUpdateView(true);
                  }
                }}>
                  <FeatherIcon.PlusSquare size={14} />
                </bs.Button>
              )
            };

            let allowInsert = (_ctx: grid.VGridContext, atRecord: grid.DisplayRecord) => {
              let recState = atRecord.getRecordState(false);
              if (!recState) return false;
              if (recState.isMarkDeleted()) {
                return false;
              }
              return true;
            }
            return (<WTMSGridInsertRow style={{ marginRight: 5 }} key={'add'} color='link' context={ctx} row={dRecord.row}
              createInsertRecord={(ctx: grid.VGridContext, atRecord: grid.DisplayRecord) => create(ctx, hbl, atRecord)}
              allowInsert={allowInsert} />);
          },
        },
        {
          name: 'del', hint: 'Delete', icon: FeatherIcon.Trash2,
          customRender(ctx, dRecord) {
            let onClick = (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
              let bill = dRecord.record;
              const onConfirm = () => {
                if (!bill.id) {
                  ctx.model.removeRecord(bill);
                  ctx.getVGrid().forceUpdateView();
                  return;
                }
                ctx.model.getDisplayRecordList().toggleDeletedDisplayRecord(dRecord.row);
                if (dRecord.getRecordState().isMarkDeleted()) {
                  ctx.model.getDisplayRecordList().markSelectDisplayRecord(dRecord.row);
                  bill['editState'] = 'DELETED';
                } else {
                  dRecord.getRecordState().selected = false;
                  delete bill['editState'];
                }
                ctx.getVGrid().forceUpdateView();
              }
              bs.dialogConfirmMessage('Delete Confirm', 'Are you sure delete???', onConfirm);
            }
            return (
              <bs.Button className='mx-1 text-danger' laf='link' onClick={() => onClick(ctx, dRecord)}>
                <FeatherIcon.Trash2 size={14} />
              </bs.Button>
            )
          },
        },
      ],
    },
    editor: {
      supportViewMode: ['aggregation', 'table'],
      enable: true || type === 'page'
    },
    fields: [
      ...entity.DbEntityListConfigTool.FIELD_SELECTOR(),
      {
        ...entity.DbEntityListConfigTool.FIELD_INDEX(), width: 50,
        computeCssClasses: (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
          if (!dRecord.isDataRecord()) return '';
          if (!dRecord.getValue('id')) {
            return 'bg-success bg-opacity-25'
          }
          return '';
        },
        customRender: (_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
          if (!dRecord.isDataRecord()) return;
          let _state = dRecord.getRecordState();
          let icon;
          if (_state.isMarkDeleted()) {
            icon = <FeatherIcon.Trash2 size={14} className='text-danger mx-1' />
          } else if (_state.isMarkModified()) {
            icon = <FeatherIcon.Edit size={14} className='text-warning mx-1' />
          }
          return (
            <div className='flex-hbox align-items-center'>
              {icon}
              <div>
                {dRecord.getDisplayRow()}
              </div>
            </div>
          )
        },
        listener: {
          onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            let dRec = cell.getDisplayRecord();
            if (event.row == cell.getRow()) {
              dRec.getRecordState().markModified();
              cell.forceUpdate()
            }
          },
        },
      },
      /** @TMSBill_Info */
      {
        name: 'fileNo', label: T('File No.'), width: 150, filterableType: 'string', filterable: true, container: 'fixed-left',
        listener: {
          onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            if (event.row == cell.getRow()) {
              cell.forceUpdate()
            }
          },
        },
        editor: {
          type: 'string', onInputChange: onInputChange
        },
        customRender(ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) {
          if (!dRecord.isDataRecord()) {
            if (dRecord.type == 'footer') {
              return (
                <bs.Button laf='link' onClick={() => _sumFooter(dRecord.record)}>
                  {dRecord.record[field.name]}
                </bs.Button>
              )
            }
            return;
          }
          let bill = dRecord.record;
          let cssClass = field.cssClass;
          if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(ctx, dRecord)}`;
          return <div className='flex-hbox align-items-center'>
            <FeatherIcon.Copy className='mx-1 my-2 text-warning' style={{ cursor: 'pointer' }} size={12}
              onClick={() => {
                navigator.clipboard.writeText(bill[field.name]);
                appContext.addOSNotification('success', 'Copy success')
              }
              } />
            <button type="button" className="btn btn-link">
              <div className={cssClass} onClick={() => { uiRoot.onSelect(dRecord); }} > {bill[field.name]}</div >
            </button>
          </div >
        },
      },
      {
        name: 'hblNo', label: T('Hbl No'), container: 'fixed-left',
        fieldDataGetter(record) {
          return record['hblNo'] ? record['hblNo'] : '/';
        },
        listener: {
          onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            if (event.row == cell.getRow() && event.field.name === 'hblNo') {
              cell.forceUpdate()
            }
          },
        },
        customRender: (_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
          let bill = dRecord.record;
          let verifyHblNoIcon = null;
          let verifyHblNo = bill['verifyHblNo'];
          if (bill['verifyHblNo']) {
            verifyHblNoIcon = (
              <FeatherIcon.CheckCircle className='text-success mx-1' size={14} />
            )
          } else if (verifyHblNo === false) {
            verifyHblNoIcon = (
              <FeatherIcon.XCircle className='text-danger mx-1' size={14} />
            )
          }
          let width = _field.width ? _field.width : 120;
          return (
            <div className='flex-hbox justify-content-start '>
              <div className='100vh'>
                {verifyHblNoIcon}
              </div>
              <bs.Button laf='link' style={{ width: width - 20 }}
                onClick={() => showTMSHouseBillEditor(appContext, pageContext, bill['hblId'], () => uiRoot.reloadData())}>
                {bill[_field.name] ? bill[_field.name] : '/'}
              </bs.Button>
            </div>
          )
        },
        editor: {
          type: 'string',
          onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let { fieldConfig, displayRecord } = fieldCtx;
            let bill = displayRecord.record;
            let color = bill['verifyHblNo'] ? 'text-success' : 'text-danger';
            return (
              <div className={`flex-hbox justify-content-start`}>
                <bs.Button laf='link'
                  onClick={() => showTMSHouseBillEditor(appContext, pageContext, bill['hblId'], () => uiRoot.reloadData())}>
                  <FeatherIcon.Edit size={12} />
                </bs.Button>
                <input.BBStringField className={color} bean={bill} field={fieldConfig.name}
                  onInputChange={(bean: any, field: string, oldVal: any, newVal: any) => {
                    verifyHblNo(bill[fieldConfig.name], (verifyHblNo: boolean) => {
                      bill['verifyHblNo'] = verifyHblNo;
                      onInputChange(bean, field, oldVal, newVal);
                    });
                  }} />
              </div>
            )
          },
        },
      },
      {
        name: 'customerFullName', label: T('Customer'), width: 170, cssClass: 'px-1', sortable: true, container: 'fixed-left',
        dataTooltip: true, filterableType: 'Options', filterable: true,
        computeCssClasses: (_ctx, dRecord) => {
          return dRecord.record['customerId'] == null ? 'text-warning' : '';
        },
        listener: {
          onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            if (event.row == cell.getRow() && event.field.name === 'customerFullName') {
              cell.forceUpdate()
            }
          },
        },
        editor: {
          type: 'string',
          onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let field = fieldCtx.fieldConfig;
            let dRecord = fieldCtx.displayRecord;
            let tabIndex = fieldCtx.tabIndex;
            let focus = fieldCtx.focus;
            let bill = dRecord.record;
            const oldVal = `${bill[field.name]}-${bill['customerId']}`;
            let cssClass = field.cssClass;
            if (field.computeCssClasses) cssClass = field.computeCssClasses(uiRoot.getVGridContext(), dRecord);
            return (
              <BBRefTMSCustomer minWidth={400}
                className={cssClass} appContext={appContext} pageContext={pageContext} tabIndex={tabIndex} autofocus={focus}
                bean={bill} beanIdField={'customerId'} beanLabelField={'customerFullName'} placeholder={'Customer'}
                onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                  if (_selectOpt) {
                    bill['payOnBehalfCustomerId'] = _selectOpt['id'];
                    bill['payOnBehalfCustomerName'] = _selectOpt['shortName'];
                  }
                  if (bill['senderLocationId']) {
                    bill['senderAddress'] = null;
                    bill['senderLocationId'] = null;
                    bill['senderLocationAddress'] = null;
                  }
                  if (bill['receiverLocationId']) {
                    bill['receiverAddress'] = null;
                    bill['receiverLocationId'] = null;
                    bill['receiverLocationAddress'] = null;
                  }
                  onInputChange(bean, field.name, oldVal, `${bill[field.name]}-${bill['customerId']}`);
                }}
              />
            )
          },
        },
      },
      {
        name: 'deliveryPlan', label: T('Date'), width: 100, cssClass: 'flex-grow-1 text-end', container: 'fixed-left',
        sortable: true, filterableType: 'date', filterable: true, format: util.text.formater.compactDate,
        listener: {
          onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            if (event.row == cell.getRow() && event.field.name === 'deliveryPlan') {
              _onDeliveryPlanCellEvent(event.field, cell);
              cell.forceUpdate();
            }
          },
        },
        editor: {
          type: 'date',
          onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let field = fieldCtx.fieldConfig;
            let dRecord = fieldCtx.displayRecord;
            let tabIndex = fieldCtx.tabIndex;
            let focus = fieldCtx.focus;
            let bill = dRecord.record;
            let cssClass = field.cssClass;
            if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
            return (
              <input.BBDateInputMask className={cssClass}
                bean={bill} field={field.name} tabIndex={tabIndex} focus={focus} format={"DD/MM/YYYY"}
                onInputChange={onInputChange} />
            );
          },
        }
      },
      {
        name: 'time', label: T('Time'), width: 60, container: 'fixed-left',
        filterableType: 'options', filterable: true,
        editor: {
          type: 'string',
          onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let field = fieldCtx.fieldConfig;
            let dRecord = fieldCtx.displayRecord;
            let tabIndex = fieldCtx.tabIndex;
            let focus = fieldCtx.focus;
            let bill = dRecord.record;
            let cssClass = field.cssClass;
            if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
            return (
              <input.BBTimeInputMask
                className={cssClass} bean={bill} field={field.name} tabIndex={tabIndex} focus={focus}
                onInputChange={onInputChange} />
            );
          },
        }
      },
      {
        name: 'estimateTime', label: T('E.Time'), hint: T('Estimate Time'), width: 80, container: 'fixed-left',
        filterableType: 'options', filterable: true,
        editor: {
          type: 'string',
          onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let field = fieldCtx.fieldConfig;
            let dRecord = fieldCtx.displayRecord;
            let tabIndex = fieldCtx.tabIndex;
            let focus = fieldCtx.focus;
            let bill = dRecord.record;
            let vendorEstimateTime = bill['vendorEstimateTime'];
            let time = bill['time'];
            let sDateTime = bill['deliveryPlan'];
            let readOnly = false;
            if (!isValidTimeFormat(time)) time = '00:00';
            if (sDateTime) {
              sDateTime = sDateTime.replace(/\d{2}:\d{2}:\d{2}/, `${time}:00`)
              let dateTime = util.TimeUtil.parseCompactDateTimeFormat(sDateTime);
              let dateRange = new util.TimeRange();
              dateRange.fromSetDate(dateTime);
              dateRange.toSetDate(new Date());
              let diffM = dateRange.diff('minutes');
              if (diffM > 15) readOnly = true;
            }
            if (vendorEstimateTime) {
              let date: Date = util.TimeUtil.parseCompactDateTimeFormat(vendorEstimateTime);
              let time = `${date.getHours().toString().padStart(2, "0")}:${date.getMinutes().toString().padStart(2, "0")}`
              return (
                <div className='text-success'>{time}</div>
              )
            }
            let cssClass = field.cssClass;
            if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
            return (
              <input.BBTimeInputMask className={cssClass} bean={bill} field={field.name} tabIndex={tabIndex} focus={focus}
                onInputChange={onInputChange} disabled={readOnly && !pageContext.hasUserModeratorCapability()}
              />
            );
          },
        }
      },
      {
        name: 'payOnBehalfCustomer', label: T('To Inv Company'), state: { visible: false },
        width: 150, cssClass: 'px-1', dataTooltip: true, filterableType: 'Options', filterable: true,
        listener: {
          onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            if (event.row == cell.getRow() && event.field.name === 'customerFullName') {
              cell.forceUpdate()
            }
          },
        },
        editor: {
          type: 'string',
          onInputChange: onInputChange,
          renderCustom(ctx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let bill = ctx.displayRecord.record;
            const oldVal = `${bill['payOnBehalfCustomerId']}-${bill['payOnBehalfCustomerFullName']}`;
            return (
              <BBRefTMSCustomer key={`pay-on-behalf-${bill['payOnBehalfCustomerId']}`}
                minWidth={650} placeholder={'To Invoice Company'} showTaxCode
                appContext={appContext} pageContext={pageContext} tabIndex={ctx.tabIndex} autofocus={ctx.focus}
                bean={bill} beanIdField={'payOnBehalfCustomerId'} beanLabelField={'payOnBehalfCustomerName'}
                onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                  onInputChange(bean, ctx.fieldConfig.name, oldVal, `${bill[ctx.fieldConfig.name]}-${bill['customerId']}`);
                }}
              />
            )
          },
        },
      },
      {
        name: 'modifiedEstimateTime', label: T('Modified E.Time'), width: 155, cssClass: 'flex-grow-1 text-end',
        format: (val) => util.text.formater.dateTime(util.TimeUtil.parseCompactDateTimeFormat(val)),
        state: { visible: false },
        fieldDataGetter: (record) => {
          return record['vendorModifiedEstimateTime'] ? record['vendorModifiedEstimateTime'] : record['modifiedEstimateTime'];
        },
        computeCssClasses(_ctx, dRecord) {
          let bill = dRecord.record;
          let sDateTime = bill['deliveryPlan'];
          let ddmmyy = util.text.formater.compactDate(sDateTime);
          let time = bill['time'];
          if (!sDateTime || !isValidTimeFormat(time)) return "text-danger";
          let dateTime = util.TimeUtil.parseCompactDateTimeFormat(`${ddmmyy}@${time}:00+0700`);
          let modifiedEstimateTime = util.TimeUtil.parseCompactDateTimeFormat(bill['modifiedEstimateTime']);
          if (bill['vendorModifiedEstimateTime']) modifiedEstimateTime = util.TimeUtil.parseCompactDateTimeFormat(bill['vendorModifiedEstimateTime']);
          let checkTime = dateTime.getTime() < modifiedEstimateTime.getTime();
          if (checkTime) return "text-warning";
          return "";
        },
      },
      {
        name: 'delayedTime', label: T('Delayed'), hint: T('Delayed Time'), state: { visible: false },
        cssClass: 'flex-grow-1 text-end', width: 100, sortable: true,
        customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
          let record = dRecord.record;
          let delayedTime = record['vendorDelayedTime'] ? record['vendorDelayedTime'] : record[field.name];
          if (!delayedTime) return;
          let hours = ~~(delayedTime / 60);
          let minutes = delayedTime % 60;
          let html;
          if (delayedTime > 0) {
            html = (
              <div className='text-warning'>{`${hours > 0 ? hours + 'h' : ''}${minutes ? String(minutes).padStart(2, '0') + '\'' : ''} late`}</div>
            )
          } else {
            hours = hours * -1;
            minutes = minutes * -1;
            html = (
              <div>{`${hours > 0 ? hours + 'h' : ''}${minutes ? String(minutes).padStart(2, '0') + '\'' : ''} early`}</div>
            )
          }
          return (
            <div className={field.cssClass} >
              {html}
            </div>
          )
        },
        listener: {
          onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            if (event.row == cell.getRow() && (event.field.name === 'time' || event.field.name === 'estimateTime')) {
              cell.forceUpdate()
            }
          },
        },
      },
      {
        name: "responsibleFullName", label: T('PIC.'), width: 120, cssClass: 'pe-1', filterableType: 'options', filterable: true,
        fieldDataGetter(record){
         return formatName(record['responsibleFullName'])
        },
        state: { visible: !pageContext.hasUserModeratorCapability() },
        customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
          let record = dRecord.record;
          let uiList = ctx.uiRoot as entity.DbEntityList
          const { appContext, pageContext } = uiList.props;
          return (
            <div className='flex-hbox justify-content-center align-items-center' >
              <module.account.WAvatars className='px-2'
                appContext={appContext} pageContext={pageContext} avatarIds={[record['responsibleAccountId']]}
                avatarIdType='AccountId' width={20} height={20} borderRadius={10} />
              <bs.Tooltip tooltip={formatName(record['responsibleFullName'])} className="flex-hbox">
                {record['userName'] ? formatName(record['userName']) : formatName(record['responsibleFullName'])}
              </bs.Tooltip>
            </div>
          )
        }
      },
      {
        name: 'root', label: T('Root'), width: 80, hint: 'Root', filterableType: 'Options', filterable: true,
        editor: {
          type: 'string', onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let field = fieldCtx.fieldConfig;
            let dRecord = fieldCtx.displayRecord;
            let tabIndex = fieldCtx.tabIndex;
            let focus = fieldCtx.focus;
            let forwarder = dRecord.record;
            let cssClass = field.cssClass;
            if (field.computeCssClasses) cssClass = field.computeCssClasses(uiRoot.getVGridContext(), dRecord);
            return (
              < input.BBOptionAutoComplete tabIndex={tabIndex} autofocus={focus} className={cssClass} allowUserInput
                bean={forwarder} field={field.name}
                options={['BEEHPH', 'BEEHAN', 'BEEHCM', 'BEEDAD', 'BEELS', 'BEEND', 'BEETH', 'BEEHNA', 'BEENA', 'BEEHD', 'MARINE']}
                onInputChange={onInputChange} />
            )
          },
        }
      },
      {
        name: 'purpose', label: T('Purpose'), width: 80, filterableType: 'options', filterable: true,
        computeCssClasses(_ctx, dRecord) {
          return PurposeTools.getColor(dRecord.record.purpose);
        },
        listener: {
          onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            if (event.row == cell.getRow() && event.field.name === 'purpose') {
              cell.forceUpdate()
            }
          },
        },
        editor: {
          type: 'string',
          onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            const { gridContext, displayRecord, fieldConfig, tabIndex, focus } = fieldCtx;
            let cssClass = fieldConfig.cssClass;
            if (fieldConfig.computeCssClasses) cssClass = fieldConfig.computeCssClasses(gridContext, displayRecord);
            let houseBill = displayRecord.record;
            return (
              <input.BBSelectField className={cssClass}
                bean={houseBill} field={fieldConfig.name} tabIndex={tabIndex} focus={focus}
                options={[Purpose.Export, Purpose.Import, Purpose.Domestic, Purpose.PortTransfer, Purpose.Cbt]}
                onInputChange={onInputChange} />
            )
          },
        }
      },
      {
        name: 'method', label: T('Method'), width: 80, filterableType: 'options', filterable: true,
        computeCssClasses(_ctx, dRecord) {
          return PurposeTools.getColor(dRecord.record.purpose);
        },
        listener: {
          onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            if (event.row == cell.getRow() && event.field.name === 'purpose') {
              cell.forceUpdate()
            }
          },
        },
        editor: {
          type: 'string',
          onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            const { gridContext, displayRecord, fieldConfig, tabIndex, focus } = fieldCtx;
            let cssClass = fieldConfig.cssClass;
            if (fieldConfig.computeCssClasses) cssClass = fieldConfig.computeCssClasses(gridContext, displayRecord);
            let houseBill = displayRecord.record;
            return (
              <input.BBSelectField className={cssClass}
                bean={houseBill} field={fieldConfig.name} tabIndex={tabIndex} focus={focus}
                options={[Method.Fcl, Method.Lcl]}
                onInputChange={onInputChange} />
            )
          },
        }
      },
      {
        name: 'typeOfTransportation', label: T('Type Of Trans'), width: 80, hint: T('Type Of Transportation'), filterableType: 'options', filterable: true,
        computeCssClasses(_ctx, dRecord) {
          return PurposeTools.getColor(dRecord.record.purpose);
        },
        listener: {
          onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            if (event.row == cell.getRow() && event.field.name === 'purpose') {
              cell.forceUpdate()
            }
          },
        },
        editor: {
          type: 'string',
          onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            const { gridContext, displayRecord, fieldConfig, tabIndex, focus } = fieldCtx;
            let cssClass = fieldConfig.cssClass;
            if (fieldConfig.computeCssClasses) cssClass = fieldConfig.computeCssClasses(gridContext, displayRecord);
            let houseBill = displayRecord.record;
            return (
              <input.BBSelectField className={cssClass}
                bean={houseBill} field={fieldConfig.name} tabIndex={tabIndex} focus={focus}
                options={[
                  TypeOfTransportation.Sea, TypeOfTransportation.Air,
                  TypeOfTransportation.Road, TypeOfTransportation.Rail, TypeOfTransportation.Other
                ]}
                onInputChange={onInputChange} />
            )
          },
        }
      },
      {
        name: 'carrierFullName', label: T('Carrier'), width: 150,
        editor: {
          type: 'string',
          onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let field = fieldCtx.fieldConfig;
            let dRecord = fieldCtx.displayRecord;
            let tabIndex = fieldCtx.tabIndex;
            let focus = fieldCtx.focus;
            let bill = dRecord.record;
            const oldVal = bill[field.name];
            let cssClass = field.cssClass;
            if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
            return (
              <BBRefTMSCarrier minWidth={400}
                className={cssClass} appContext={appContext} pageContext={pageContext} tabIndex={tabIndex} autofocus={focus}
                bean={bill} beanIdField={'carrierId'} beanLabelField={'carrierFullName'} placeholder={'Carrier'}
                onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                  onInputChange(bean, field.name, oldVal, bean[field.name])
                }}
              />
            )
          },
        }
      },
      { name: 'bookingCode', label: T('Booking/Bill'), width: 150, editor: { type: 'string', onInputChange: onInputChange, } },
      {
        name: 'etaCutOffTimeNote', label: T('COT/ETA'), width: 130, dataType: 'string',
        editor: {
          type: 'string',
          onInputChange: onInputChange,
        }
      },
      {
        name: 'declarationNumber', label: T('CDS'), hint: T('Declaration Number'), width: 150,
        editor: { type: 'string', onInputChange: onInputChange, }
      },
      {
        name: 'truckType', label: T('Truck Type'), cssClass: 'justify-content-center ',
        filterableType: 'options', filterable: true,
        editor: {
          type: 'string',
          onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let field = fieldCtx.fieldConfig;
            let dRecord = fieldCtx.displayRecord;
            let tabIndex = fieldCtx.tabIndex;
            let focus = fieldCtx.focus;
            let forwarder = dRecord.record;
            const oldVal = forwarder[field.name];
            let cssClass = field.cssClass;
            if (field.computeCssClasses) cssClass = field.computeCssClasses(uiRoot.getVGridContext(), dRecord);
            return (
              <module.settings.BBRefUnit
                className={cssClass}
                appContext={appContext} pageContext={pageContext}
                minWidth={300}
                placement="left"
                placeholder="Enter Unit"
                groupNames={['truck']}
                bean={forwarder} beanIdField={field.name}
                tabIndex={tabIndex} autofocus={focus}
                onPostUpdate={(_inputUI, bean, _selectOpt, _userInput) => onInputChange(bean, field.name, oldVal, bean[field.name])} />
            )
          },
        }
      },
      {
        name: 'containerNo', label: T('Container No'),
        customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
          let record = dRecord.record;
          let containerNo = record[field.name];
          let width = field.width ? field.width : 120;
          let cssClass = field.cssClass;
          if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(ctx, dRecord)}`;
          return (
            <div className={`flex-hbox`}>
              <bs.Tooltip className={`flex-grow-1 text-truncate ${cssClass}`} style={{ width: width - 25 }} tooltip={containerNo}>
                {containerNo}
              </bs.Tooltip>
              {UITMSBillUtils.containerValidate(containerNo)}
            </div>
          )
        },
        editor: {
          type: 'string', onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let field = fieldCtx.fieldConfig;
            let dRecord = fieldCtx.displayRecord;
            let tabIndex = fieldCtx.tabIndex;
            let focus = fieldCtx.focus;
            let bill = dRecord.record;
            let containerNo = bill[field.name];
            let cssClass = field.cssClass;
            if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
            return (
              <div className={`flex-hbox`}>
                <input.BBStringField className={cssClass}
                  bean={bill} field={field.name} tabIndex={tabIndex} focus={focus} onInputChange={onInputChange} />
                {UITMSBillUtils.containerValidate(containerNo)}
              </div>
            )
          },
        }
      },
      { name: 'sealNo', label: T('Seal No'), editor: { type: 'string', onInputChange: onInputChange, } },
      {
        name: 'quantity', label: T('Quantity'),
        listener: {
          onAggregationDataCellEvent(cell: grid.VGridCell, event: grid.VGridCellEvent) {
            if (event.field.name === 'quantity') {
              _aggSumCell(cell, event, 'quantity');
            }
          },
        },
        editor: {
          type: 'double', onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let { fieldConfig, displayRecord, focus, tabIndex, gridContext } = fieldCtx;
            let bill = displayRecord.record;
            let content = (
              <input.BBDoubleField bean={bill} field={fieldConfig.name} focus={focus} tabIndex={tabIndex}
                onInputChange={onInputChange} />
            )
            return renderMeasurement(bill['hblQuantity'], content);
          },
        }
      },
      {
        name: 'quantityUnit', label: T('Q.Unit'), width: 130,
        editor: {
          type: 'string',
          onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let field = fieldCtx.fieldConfig;
            let dRecord = fieldCtx.displayRecord;
            let tabIndex = fieldCtx.tabIndex;
            let focus = fieldCtx.focus;
            let forwarder = dRecord.record;
            const oldVal = forwarder[field.name];
            return (
              <module.settings.BBRefUnit
                appContext={appContext} pageContext={pageContext}
                minWidth={400}
                placement="left"
                placeholder="Enter Unit"
                bean={forwarder} beanIdField={field.name}
                tabIndex={tabIndex} autofocus={focus}
                onPostUpdate={(_inputUI, bean, _selectOpt, _userInput) => onInputChange(bean, field.name, oldVal, bean[field.name])} />
            )
          },
        }
      },
      {
        name: 'weight', label: T('Weight'),
        listener: {
          onAggregationDataCellEvent(cell: grid.VGridCell, event: grid.VGridCellEvent) {
            if (event.field.name === 'weight') {
              _aggSumCell(cell, event, 'weight');
            }
          },
        },
        editor: {
          type: 'double', onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let { fieldConfig, displayRecord, focus, tabIndex, gridContext } = fieldCtx;
            let bill = displayRecord.record;
            let content = (
              <input.BBDoubleField bean={bill} field={fieldConfig.name} focus={focus} tabIndex={tabIndex}
                onInputChange={onInputChange} />
            )
            return renderMeasurement(bill['hblWeight'], content);
          },

        }
      },
      { name: 'chargeableWeight', label: T('C.Weight'), state: { visible: false }, width: 80, editor: { type: 'double', onInputChange: onInputChange, } },
      {
        name: 'weightUnit', label: T('W.Unit'), state: { visible: false },
        editor: {
          type: 'string',
          onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let field = fieldCtx.fieldConfig;
            let dRecord = fieldCtx.displayRecord;
            let tabIndex = fieldCtx.tabIndex;
            let focus = fieldCtx.focus;
            let forwarder = dRecord.record;
            const oldVal = forwarder[field.name];
            let cssClass = field.cssClass;
            if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
            return (
              <module.settings.BBRefUnit
                className={cssClass}
                appContext={appContext} pageContext={pageContext}
                minWidth={300}
                placement="left"
                placeholder="Enter Unit"
                groupNames={['weight']}
                bean={forwarder} beanIdField={field.name}
                tabIndex={tabIndex} autofocus={focus}
                onPostUpdate={(_inputUI, bean, _selectOpt, _userInput) => onInputChange(bean, field.name, oldVal, bean[field.name])} />
            )
          },
        }
      },
      {
        name: 'volume', label: T('Volume'),
        editor: {
          type: 'double', onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let { fieldConfig, displayRecord, focus, tabIndex, gridContext } = fieldCtx;
            let bill = displayRecord.record;
            let content = (
              <input.BBDoubleField bean={bill} field={fieldConfig.name} focus={focus} tabIndex={tabIndex}
                onInputChange={onInputChange} />
            )
            return renderMeasurement(bill['hblVolume'], content);
          },
        }
      },
      {
        name: 'volumeAsText', label: T('Volume Note'), width: 100, cssClass: 'flex-grow-1 text-end',
        listener: {
          onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            if (event.row == cell.getRow() && (event.field.name === 'volumeAsText')) {
              let bill = cell.getDisplayRecord().record;
              let volumeAsText = bill['volumeAsText'];
              if (TMSUtils.isDecimal(volumeAsText)) {
                bill['volume'] = Number(volumeAsText);
              } else {
                bill['volume'] = 0;
              }
            }
          },
        },
        editor: {
          type: 'string', onInputChange: onInputChange,
        }
      },
      {
        name: 'chargeableVolume', label: T('C.Volume'), width: 80,
        state: { visible: false }, editor: { type: 'double', onInputChange: onInputChange }
      },
      {
        name: 'volumeUnit', label: T('V.Unit'), state: { visible: false },
        editor: {
          type: 'string',
          onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let field = fieldCtx.fieldConfig;
            let dRecord = fieldCtx.displayRecord;
            let tabIndex = fieldCtx.tabIndex;
            let focus = fieldCtx.focus;
            let forwarder = dRecord.record;
            const oldVal = forwarder[field.name];
            let cssClass = field.cssClass;
            if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
            return (
              <module.settings.BBRefUnit
                className={cssClass}
                appContext={appContext} pageContext={pageContext}
                minWidth={300}
                placement="left"
                placeholder="Enter Unit"
                groupNames={['volume']}
                bean={forwarder} beanIdField={field.name}
                tabIndex={tabIndex} autofocus={focus}
                onPostUpdate={(_inputUI, bean, _selectOpt, _userInput) => onInputChange(bean, field.name, oldVal, bean[field.name])} />
            )
          },
        }
      },
      //Sender
      {
        name: 'senderContact', label: T('S/WH.Contact'), hint: T('Sender/WH Contact'), cssClass: 'px-1', width: 155, dataTooltip: true,
        sortable: true,
        listener: {
          onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            if (event.row == cell.getRow() && event.field.name === 'purpose') {
              cell.forceUpdate()
            }
          },
        },
        customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
          let bill = dRecord.record;
          let cssClass = field.cssClass;
          if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(ctx, dRecord)}`;
          return TMSUtils.renderTMSGridTooltip(bill[field.name], bill[field.name], field.width, dRecord.row);
        },
        editor: {
          type: 'string',
          onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            const { fieldConfig, displayRecord, tabIndex, } = fieldCtx;
            let bill = displayRecord.record;
            let cssClass = fieldConfig.cssClass;
            let purpose = bill.purpose;
            let placeholder = PurposeTools.isImport(purpose) || PurposeTools.isPortTransfer(purpose) ? 'WH Contact' : 'Contact';
            let html = (
              <input.BBStringField className={cssClass} placeholder={placeholder}
                tabIndex={tabIndex} focus={fieldCtx.focus} bean={bill} field={fieldConfig.name} onInputChange={onInputChange} />
            )
            return UITMSBillUtils.renderTooltip(bill[fieldConfig.name], html);;
          },
        },
      },
      {
        name: 'senderAddress', label: T('S.Address/Full Return WH'), hint: T('Sender Address'), cssClass: 'text-warning', width: 250, dataTooltip: true,
        computeCssClasses(_ctx, dRecord) {
          return dRecord.record['senderLocationId'] ? 'text-success' : 'text-warning';
        },
        listener: {
          onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            if (event.row == cell.getRow() && event.field.name === 'purpose' || event.field.name === 'customerFullName') {
              cell.forceUpdate()
            }
          },
        },
        customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
          let bill = dRecord.record;
          let cssClass = `flex-hbox justify-content-end px-2 ${field.computeCssClasses && field.computeCssClasses(ctx, dRecord)} `;
          let value = field.fieldDataGetter ? field.fieldDataGetter(bill) : bill[field.name];
          const content = (
            <div className={cssClass} style={{ width: field.width }}>
              {value}
            </div>
          );
          const title = (
            <ul>
              <li>{value}</li>
              <li><FeatherIcon.MapPin size={12} className='text-success me-1' />{bill['senderLocationAddress']}</li>
              <li>{bill['senderInvAddress']}</li>
            </ul>
          );
          return UITMSBillUtils.renderTooltip(title, content);
        },
        editor: {
          type: 'string',
          onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            const { fieldConfig, displayRecord, tabIndex, focus, gridContext } = fieldCtx;
            let bill = displayRecord.record;
            const senderLocationId = bill['senderLocationId'];
            const purpose = bill.purpose;
            const cssClass = `${fieldConfig.computeCssClasses && fieldConfig.computeCssClasses(gridContext, displayRecord)}`;
            let value = fieldConfig.fieldDataGetter ? fieldConfig.fieldDataGetter(bill) : bill[fieldConfig.name];
            const title = (
              <ul>
                <li>{value}</li>
                <li><FeatherIcon.MapPin size={12} className='text-success me-1' />{bill['senderLocationAddress']}</li>
                <li>{bill['senderInvAddress']}</li>
              </ul>
            );
            const html = PurposeTools.isExport(purpose) || PurposeTools.isDomestic(purpose) || PurposeTools.isCbt(purpose) ?
              <BBRefTMSPartnerAddress key={`sender-${bill['senderLocationId']}-${purpose}`}
                minWidth={500} allowUserInput
                className={`flex-grow-1 ${cssClass}`} autofocus={fieldCtx.focus} tabIndex={tabIndex}
                appContext={appContext} pageContext={pageContext} placeholder='Address' bean={bill} customerId={bill['customerId']}
                partnerId={bill['customerPartnerId']}
                beanIdField='senderPartnerAddressId' beanLabelField={fieldConfig.name} types={['Export', 'None']}
                onPostCommit={(partnerAddress: any) => {
                  _updateSenderAddress(bill, partnerAddress);
                  onInputChange(bill, fieldConfig.name, senderLocationId, bill['senderLocationId']);
                }}
                onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                  if (_selectOpt && _selectOpt.id) _updateSenderAddress(bean, _selectOpt);
                  onInputChange(bean, fieldConfig.name, senderLocationId, bean['senderLocationId']);
                }} />
              :
              <div className='flex-hbox'>
                {WHIcon()}
                <BBRefLocation
                  className={`flex-grow-1 ${cssClass}`} autofocus={focus} tabIndex={tabIndex} locationTags={['app:tms']} minWidth={500}
                  appContext={appContext} pageContext={pageContext} bean={bill} disable={!writeCap} refLocationBy='id'
                  beanIdField={'senderLocationId'} beanLabelField={fieldConfig.name} placeholder='Full Return WH'
                  onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                    bean["senderLocationAddress"] = _selectOpt['address'];
                    bean["senderInvAddress"] = `${_selectOpt['shortLabel']}, ${_selectOpt['subdistrictLabel']}, ${_selectOpt['stateLabel']}`;
                    onInputChange(bean, fieldConfig.name, senderLocationId, bean['senderLocationId']);
                  }} />
              </div>
            return UITMSBillUtils.renderTooltip(title, html);
          },
        },
      },
      {
        name: 'senderLocationAddress', label: T('S.Location'), hint: T('Sender Location'), width: 250, state: { visible: false },
        customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
          let bill = dRecord.record;
          let address = bill[field.name];
          return TMSUtils.renderTMSGridTooltip(address, address, field.width, dRecord.row, { className: 'text-warning flex-hbox justify-content-end px-2' });
        },
        editor: {
          type: 'string',
          onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let field = fieldCtx.fieldConfig;
            let dRecord = fieldCtx.displayRecord;
            let bill = dRecord.record;
            const oldVal = bill[field.name];
            let className = 'flex-hbox align-items-center justify-content-end text-warning h-100'
            return (
              <BBRefLocation
                className={`flex-grow-1 ${className}`} tabIndex={fieldCtx.tabIndex} autofocus={fieldCtx.focus} required locationTags={['app:tms']} minWidth={500}
                appContext={appContext} pageContext={pageContext} bean={bill} disable={!writeCap} refLocationBy='id'
                beanIdField={'senderLocationId'} beanLabelField={'senderLocationAddress'} beanRefLabelField='address' placeholder='Location'
                onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                  onInputChange(bean, field.name, oldVal, bean[field.name]);
                }}
              />
            )
          },
        }
      },
      {
        name: 'stopLocations', label: T('Stop Location'), width: 250,
        customRender: (_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
          let bill = dRecord.record;
          let stopLocations: Array<any> = bill['stopLocations'];
          let addresses: Array<string> = [];
          if (stopLocations) {
            for (let stopLocation of stopLocations) {
              addresses.push(stopLocation.address);
            }
          }
          return <bs.CssTooltip >
            <bs.CssTooltipToggle className='flex-vbox'>
              {
                addresses.map((address: string, index: number) =>
                  <div style={{
                    borderBottom: index !== addresses.length - 1 ? '1px solid #ccc' : 'none',
                    padding: '2px 0'
                  }}>
                    {address}
                  </div>)
              }
            </bs.CssTooltipToggle>
            <bs.CssTooltipContent
              className={`d-flex align-items-end text-secondary`}
              style={{ whiteSpace: 'break-spaces', transform: 'translate(-5px, -75px)' }}>
              {
                addresses ? addresses.map((address) =>
                  <div className='p-2'>
                    {address}
                  </div>
                ) : <></>
              }
            </bs.CssTooltipContent>
          </bs.CssTooltip>
        },
      },
      //Receiver
      {
        name: 'receiverContact', label: T('R/WH.Contact'), hint: T('Receiver/WH Contact'), width: 155, dataTooltip: true,
        sortable: true,
        listener: {
          onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            if (event.row == cell.getRow() && (event.field.name == 'purpose' || event.field.name == 'receiverContact')) {
              cell.forceUpdate()
            }
          },
        },
        customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
          let bill = dRecord.record;
          return TMSUtils.renderTMSGridTooltip(bill[field.name], bill[field.name], field.width, dRecord.row);
        },
        editor: {
          type: 'string',
          onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            const { displayRecord, fieldConfig, tabIndex, focus } = fieldCtx;
            let bill = displayRecord.record;
            let cssClass = fieldConfig.cssClass;
            let purpose = bill.purpose;
            let placeholder = PurposeTools.isExport(purpose) || PurposeTools.isPortTransfer(purpose) ? 'WH Contact' : 'Contact';
            let html = (
              <input.BBStringField className={cssClass}
                focus={focus} tabIndex={tabIndex} bean={bill} field={fieldConfig.name} onInputChange={onInputChange} placeholder={placeholder} />
            )
            return UITMSBillUtils.renderTooltip(bill[fieldConfig.name], html);
          },
        },
      },
      {
        name: 'receiverAddress', label: T('R.Address/Full Return WH'), hint: T('Receiver Address'), width: 250, dataTooltip: true,
        computeCssClasses(_ctx, dRecord) {
          return dRecord.record['receiverLocationId'] ? 'text-success' : 'text-warning';
        },
        listener: {
          onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            if (event.row == cell.getRow() && event.field.name === 'purpose' || event.field.name === 'receiverAddress') {
              cell.forceUpdate()
            }
          },
        },
        customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
          let bill = dRecord.record;
          let cssClass = 'flex-hbox justify-content-end px-2';
          let value = field.fieldDataGetter ? field.fieldDataGetter(bill) : bill[field.name];
          const content = (
            <div className={`${cssClass} ${field.computeCssClasses && field.computeCssClasses(ctx, dRecord)}`}
              style={{ width: field.width }}>
              {value}
            </div>
          );
          const title = (
            <ul>
              <li>{value}</li>
              <li><FeatherIcon.MapPin size={14} className='text-success me-1' />{bill['receiverLocationAddress']}</li>
              <li>{bill['receiverInvAddress']}</li>
            </ul>
          );
          return UITMSBillUtils.renderTooltip(title, content);
        },
        editor: {
          type: 'string',
          onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            const { fieldConfig, displayRecord, tabIndex, focus, gridContext } = fieldCtx;
            let bill = displayRecord.record;
            const receiverLocationId = bill['receiverLocationId'];
            let purpose = bill.purpose;
            let cssClass = `${fieldConfig.cssClass} ${fieldConfig.computeCssClasses && fieldConfig.computeCssClasses(gridContext, displayRecord)}`;
            let value = fieldConfig.fieldDataGetter ? fieldConfig.fieldDataGetter(bill) : bill[fieldConfig.name];
            let title = (
              <ul key={util.IDTracker.next()}>
                <li>{value}</li>
                <li><FeatherIcon.MapPin size={12} className='text-success me-1' />{bill['receiverLocationAddress']}</li>
                <li>{bill['receiverInvAddress']}</li>
              </ul>
            );
            let html = PurposeTools.isImport(purpose) || PurposeTools.isDomestic(purpose) ?
              <BBRefTMSPartnerAddress key={`receiver-${bill['receiverLocationId']}-${purpose}`}
                minWidth={500}
                className={`flex-grow-1 ${cssClass}`} autofocus={fieldCtx.focus} tabIndex={tabIndex} allowUserInput
                appContext={appContext} pageContext={pageContext} placeholder='Address' bean={bill} customerId={bill['customerId']}
                beanIdField='receiverPartnerAddressId' beanLabelField={fieldConfig.name} types={['Import', 'None']}
                partnerId={bill['customerPartnerId']}
                onPostCommit={(partnerAddress: any) => {
                  _updateReceiverAddress(bill, partnerAddress);
                  onInputChange(bill, fieldConfig.name, receiverLocationId, bill['receiverLocationId']);
                }}
                onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                  if (_selectOpt && _selectOpt.id) _updateReceiverAddress(bean, _selectOpt);
                  onInputChange(bean, fieldConfig.name, receiverLocationId, bean['receiverLocationId']);
                }}
              />
              :
              <div className='flex-hbox'>
                {WHIcon()}
                <BBRefLocation
                  className={`flex-grow-1 ${cssClass}`} tabIndex={tabIndex} autofocus={focus} required locationTags={['app:tms']} minWidth={500}
                  appContext={appContext} pageContext={pageContext} bean={bill} disable={!writeCap} refLocationBy='id'
                  beanIdField={'receiverLocationId'} beanLabelField={fieldConfig.name} placeholder='Full Return WH'
                  onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                    bean["receiverLocationAddress"] = _selectOpt['address'];
                    bean["receiverInvAddress"] = `${_selectOpt['shortLabel']}, ${_selectOpt['subdistrictLabel']}, ${_selectOpt['stateLabel']}`;
                    onInputChange(bean, fieldConfig.name, receiverLocationId, bean['receiverLocationId']);
                  }} />
              </div>
            return UITMSBillUtils.renderTooltip(title, html);
          },
        },
      },
      {
        name: 'receiverLocationAddress', label: T('R.Location'), hint: T('Receiver Location'), width: 250, state: { visible: false },
        customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
          let bill = dRecord.record;
          let address = bill[field.name];
          return TMSUtils.renderTMSGridTooltip(address, address, field.width, dRecord.row, { className: 'text-success flex-hbox justify-content-end px-2' });
        },
        editor: {
          type: 'string',
          onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let field = fieldCtx.fieldConfig;
            let dRecord = fieldCtx.displayRecord;
            let bill = dRecord.record;
            const oldVal = bill[field.name];
            let className = 'flex-hbox align-items-center justify-content-end text-success h-100'
            return (
              <BBRefLocation
                className={`flex-grow-1 ${className}`} autofocus={fieldCtx.focus} tabIndex={fieldCtx.tabIndex} required locationTags={['app:tms']} minWidth={500}
                appContext={appContext} pageContext={pageContext} bean={bill} disable={!writeCap} refLocationBy='id'
                beanIdField={'receiverLocationId'} beanLabelField={'receiverLocationAddress'} beanRefLabelField='address' placeholder='Location'
                onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                  if (!bean["receiverAddress"]) bean["receiverAddress"] = _selectOpt['address'];
                  onInputChange(bean, field.name, oldVal, bean[field.name]);
                }}
              />
            )
          },
        }
      },
      {
        name: 'description', label: T('Notes'), width: 200, dataTooltip: true, cssClass: 'px-1 text-danger',
        customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
          let bill = dRecord.record;
          let html = (
            <bs.CssTooltip style={{ width: field.width }}>
              <bs.CssTooltipToggle style={{ textOverflow: 'ellipsis', overflow: 'hidden' }}>
                {bill[field.name]}
              </bs.CssTooltipToggle>
              <bs.CssTooltipContent
                className={`d-flex align-items-end text-secondary`}
                style={{ whiteSpace: 'break-spaces', transform: 'translate(-5px, -75px)' }}>
                <div className='p-2'>
                  {bill[field.name]}
                </div>
              </bs.CssTooltipContent>
            </bs.CssTooltip>
          )
          return html;
        },
        editor: {
          type: 'string', onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let field = fieldCtx.fieldConfig;
            let dRecord = fieldCtx.displayRecord;
            let tabIndex = fieldCtx.tabIndex;
            let bill = dRecord.record;
            let cssClass = field.cssClass;
            if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
            return (
              <div className='flex-hbox'>
                <input.BBStringField className={cssClass} tabIndex={tabIndex} focus={fieldCtx.focus} bean={bill} field={field.name} onInputChange={onInputChange} />
                {UITMSBillUtils.renderButtonInfo('Note', bill[field.name])}
              </div>
            );
          },
        }
      },
      {
        name: 'tmsTrackingDescription', label: T('Tracking Note'), width: 200, dataTooltip: true,
        hint: 'Ghi Chú Điều Vận', cssClass: 'text-warning',
        fieldDataGetter: (record) => {
          let trackings: Array<any> = record['trackings'];
          let vendorBills: Array<any> = record['vendorBills'];
          if (trackings) {
            return trackings.map(
              tracking => {
                let vehicleLabel = tracking.vehicleLabel ? tracking.vehicleLabel : 'No Vehicle';
                let description = tracking.description;
                if (!description) return;
                return trackings.length > 1 ? `${vehicleLabel} : ${description}` : description;
              }
            ).join('\n');
          }
          if (vendorBills) {
            return vendorBills.map(
              vendorBill => {
                let vehicleLabel = vendorBill.licensePlate ? vendorBill.licensePlate : 'No Vehicle';
                let description = vendorBill.description;
                if (!description) return;
                return vendorBills.length > 1 ? `${vehicleLabel} : ${description}` : description;
              }
            ).join(', ');
          }
          return "";
        },
      },

      {
        name: 'vendorFullName', label: T('Vendor'), width: 150, cssClass: 'px-1', filterableType: 'options', filterable: true,
        computeCssClasses(_ctx, dRecord) {
          let vendorId = dRecord.getValue('vendorId');
          if (!vendorId) return 'text-warning'
          return '';
        },
        editor: {
          type: 'string',
          onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let field = fieldCtx.fieldConfig;
            let dRecord = fieldCtx.displayRecord;
            let tabIndex = fieldCtx.tabIndex;
            let focus = fieldCtx.focus;
            let bill = dRecord.record;
            let cssClass = field.cssClass;
            const oldVal = bill[field.name];
            if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
            return (
              <BBRefVehicleFleet minWidth={400}
                className={cssClass} appContext={appContext} pageContext={pageContext} tabIndex={tabIndex} autofocus={focus}
                bean={bill} beanIdField={'vendorId'} beanLabelField={'vendorFullName'} placeholder={'Vendor'}
                onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                  onInputChange(bean, field.name, oldVal, bean[field.name])
                }}
              />
            )
          },
        }
      },
      {
        name: 'tmsTrackingFleetLabel', label: T('Ref. Vendor'), width: 120, cssClass: 'text-warning',
        filterableType: 'options', filterable: true,
        fieldDataGetter: (record) => {
          let trackings: Array<any> = record['trackings'];
          if (trackings) {
            return trackings.map(tracking => tracking.fleetLabel).join(', ');
          }
          return "";
        },
      },
      {
        name: 'licensePlate', label: T('License Plate'), width: 120,
        filterableType: 'options', filterable: true,
        fieldDataGetter: (record) => {
          let vendorBills: Array<any> = record['vendorBills'];
          if (vendorBills) {
            return vendorBills.map(vendorBill => vendorBill.licensePlate).join(', ');
          }
          return null;
        },
        editor: {
          type: 'string', onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let field = fieldCtx.fieldConfig;
            let dRecord = fieldCtx.displayRecord;
            let tabIndex = fieldCtx.tabIndex;
            let focus = fieldCtx.focus;
            let bill = dRecord.record;
            let licensePlate = bill['licensePlate'];
            if (!licensePlate) bill['licensePlate'] = field.fieldDataGetter ? field.fieldDataGetter(bill) : licensePlate;
            let cssClass = field.cssClass;
            if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
            return (
              <input.BBStringField disable={!writeCap || bill['trackings']}
                className={cssClass} tabIndex={tabIndex} focus={focus} bean={bill} field={field.name}
                onInputChange={(bean: any, field: string, oldVal: any, newVal: any) => {
                  let vendorBills: Array<any> = bean['vendorBills'];
                  if (vendorBills) {
                    let find = vendorBills.find(vendorBill => vendorBill['licensePlate'] === oldVal);
                    if (find) {
                      find['licensePlate'] = newVal;
                    }
                  }
                  onInputChange(bean, field, oldVal, newVal);
                }} />
            );
          },
        }
      },
      {
        name: 'truckNo', label: T('Truck Info'), dataTooltip: true,
        fieldDataGetter(record: any) {
          let trackings: Array<any> = record['trackings'];
          let vendorBills: Array<any> = record['vendorBills'];
          let info = [];
          if (trackings) {
            for (let tracking of trackings) {
              if (!tracking['vehicleLabel']) continue;
              info.push(
                TMSJoinFields(
                  tracking,
                  { 'mobile': 'Mobile', 'identificationNo': 'ID' },
                  'vehicleType', 'vehicleLabel', 'driverFullName', 'mobile', 'identificationNo'
                ));
            }
            return info.join(', \n');
          }
          let truckNo = record['truckNo'] ? record['truckNo'] : '';
          if (vendorBills) {
            for (let vendorBill of vendorBills) {
              if (!vendorBill['licensePlate']) continue;
              info.push(
                TMSJoinFields(
                  vendorBill,
                  { 'driverMobile': 'Mobile', 'driverIdentificationNo': 'ID' },
                  'licensePlate', 'driverFullName', 'driverMobile', 'driverIdentificationNo'
                ));
            }
            return truckNo + "\n" + info.join(', \n');
          }
          return truckNo;
        },
        editor: {
          type: 'string', onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let field = fieldCtx.fieldConfig;
            let dRecord = fieldCtx.displayRecord;
            let tabIndex = fieldCtx.tabIndex;
            let focus = fieldCtx.focus;
            let cssClass = field.cssClass;
            if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
            let bill = dRecord.record;
            if (bill['trackings'] && field.fieldDataGetter) {
              return TMSCssTooltip(field.fieldDataGetter(bill), { width: field.width, opacity: 0.87 });
            }
            return (
              <div className='flex-hbox'>
                <input.BBStringField
                  className={cssClass} tabIndex={tabIndex} focus={focus} bean={bill} field={field.name}
                  onInputChange={(bean: any, field: string, oldVal: any, newVal: any) => {
                    let vendorBills: Array<any> = bean['vendorBills'];
                    if (vendorBills) {
                      let find = vendorBills.find(vendorBill => vendorBill['licensePlate'] === bill['licensePlate']);
                      if (find) {
                        find['note'] = newVal;
                      }
                    }
                    onInputChange(bean, field, oldVal, newVal);
                  }}
                />
                <bs.Button laf='link' onClick={() => {
                  UITMSVendorBillList.onShowVendorBillTracking(appContext, pageContext, bill, (data) => {
                    bill['vendorBills'] = data['billTrackings'];
                    fieldCtx.gridContext.getVGrid().forceUpdateView();
                  })
                }}>
                  <FeatherIcon.Edit size={12} />
                </bs.Button>
              </div>
            )
          },
        }
      },
      {
        name: 'fixedPayment', label: T(`Fixed`), format: formatCurrency, dataType: 'double',
        customHeaderRender(ctx, field, _headerEle) {
          return UITMSBillUtils.onRenderCellHeaderMultiplier(field, uiRoot.multiplierBean, (val) => {
            let selectRecs: Array<any> = plugin.getListModel().getSelectedRecords();
            selectRecs.forEach(sel => {
              sel['fixedPayment'] = sel['fixedPayment'] * val;
              sel['totalPayment'] = sel['fixedPayment'] + sel['extraPayment'];
              let state = grid.getRecordState(sel);
              state.markModified(true);
            });
            ctx.getVGrid().forceUpdateView();
          });
        },
        listener: {
          onAggregationDataCellEvent(cell: grid.VGridCell, event: grid.VGridCellEvent) {
            if (event.field.name === 'fixedPayment') {
              _aggSumCell(cell, event, 'fixedPayment');
            }
          },
        },
        computeCssClasses: (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
          let bill = dRecord.record;
          if (bill['trackings']) return 'text-info';
          let fixedPayment = bill['fixedPayment'];
          let tmsTrackingFixedCharge = bill['tmsTrackingFixedCharge'] ? bill['tmsTrackingFixedCharge'] : bill['vendorFixed'];
          tmsTrackingFixedCharge = tmsTrackingFixedCharge ? tmsTrackingFixedCharge : 0;
          if (fixedPayment != tmsTrackingFixedCharge) return 'text-danger';
          return '';
        },
        editor: {
          type: 'currency',
          onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let field = fieldCtx.fieldConfig;
            let dRecord = fieldCtx.displayRecord;
            let tabIndex = fieldCtx.tabIndex;
            let focus = fieldCtx.focus;
            let bill = dRecord.record;
            let trackings = bill['trackings'];
            let _writeCap = writeCap && !trackings;
            let cssClass = field.cssClass;
            if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
            let isPaid = bill['paymentStatus'] == 'Paid';
            return (
              <input.BBCurrencyField disable={!_writeCap || isPaid} className={cssClass} tabIndex={tabIndex} focus={focus}
                bean={bill} field={field.name} onInputChange={onInputChange} />
            )
          },
        }
      },
      {
        name: 'extraPayment', label: T('Extra'), format: formatCurrency, dataType: 'double',
        computeCssClasses: (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
          let bill = dRecord.record;
          if (bill['trackings']) return 'text-info';
          let extraPayment = bill['extraPayment'];
          let tmsTrackingExtraCharge = bill['tmsTrackingExtraCharge'] ? bill['tmsTrackingExtraCharge'] : bill['vendorExtra'];
          tmsTrackingExtraCharge = tmsTrackingExtraCharge ? tmsTrackingExtraCharge : 0;
          if (extraPayment != tmsTrackingExtraCharge) return 'text-danger';
          return '';
        },
        listener: {
          onAggregationDataCellEvent(cell: grid.VGridCell, event: grid.VGridCellEvent) {
            if (event.field.name === 'extraPayment') {
              _aggSumCell(cell, event, 'extraPayment');
            }
          },
        },
        editor: {
          type: 'currency',
          onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let field = fieldCtx.fieldConfig;
            let dRecord = fieldCtx.displayRecord;
            let tabIndex = fieldCtx.tabIndex;
            let focus = fieldCtx.focus;
            let bill = dRecord.record;
            let trackings = bill['trackings'];
            let _writeCap = writeCap && !trackings;
            let cssClass = field.cssClass;
            if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
            let isPaid = bill['paymentStatus'] == 'Paid';
            return (
              <input.BBCurrencyField disable={!_writeCap || isPaid} className={cssClass} tabIndex={tabIndex} focus={focus}
                bean={bill} field={field.name} onInputChange={onInputChange} />
            )
          },
        }
      },
      {
        name: 'totalPayment', label: T('Total'), hint: T('Total Payment'), dataType: 'double', format: formatCurrency,
        computeCssClasses: (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
          let bill = dRecord.record;
          if (bill['trackings']) return 'text-info';
          let totalPayment = bill['totalPayment'];
          let tmsTrackingTotalCharge = bill['tmsTrackingTotalCharge'] ? bill['tmsTrackingTotalCharge'] : bill['vendorCost'];
          tmsTrackingTotalCharge = tmsTrackingTotalCharge ? tmsTrackingTotalCharge : 0;
          if (totalPayment != tmsTrackingTotalCharge) return 'text-danger';
          return '';
        },
        listener: {
          onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            if (event.row == cell.getRow() && (event.field.name === 'fixedPayment' || event.field.name === 'extraPayment')) {
              let bill = cell.getDisplayRecord().record;
              bill.totalPayment = bill.fixedPayment + bill.extraPayment;
              cell.forceUpdate();
            }
          },
          onAggregationDataCellEvent(cell: grid.VGridCell, event: grid.VGridCellEvent) {
            if (event.field.name === 'fixedPayment' || event.field.name === 'extraPayment') {
              _aggSumCell(cell, event, 'totalPayment');
            }
          },
        },
      },
      {
        name: '_editCost', label: '', hint: T('Edit Cost'), filterable: true, filterableType: 'options', width: 60,
        customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
          let uiList = ctx.uiRoot as UITMSHouseBillList;
          let bill = dRecord.record;
          let editCost = (
            <bs.Button style={{ paddingBottom: 5 }} className='me-1' laf='link' onClick={() => uiList.onShowTMSBillFee(dRecord.record)}>
              <FeatherIcon.Edit className={'text-warning'} size={12} />
            </bs.Button>
          )
          if (bill.id == null || !dRecord.isDataRecord()) return;
          if (!bill['trackings']) return editCost;

          let cssClass = field.cssClass;
          if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(ctx, dRecord)}`;
          let color = 'green';
          let vendorCostStatus = bill['vendorCostStatus'];
          let closePayment = bill['closePayment'];
          let uiCheckVendorCost = (
            <div style={{ width: 15 }}>
              <div className={`flex-grow-0 text-end`}>
                <bs.Button style={{ paddingBottom: 5 }} laf='link' onClick={
                  () => {
                    if (uiList.props.pageContext.hasUserModeratorCapability() && vendorCostStatus != VendorCostStatus.NEED_CONFIRM && !vendorCostStatus) {
                      const callbackConfirm = () => {
                        appContext.createHttpBackendCall('TMSVendorBillService', 'updateVendorCostStatus', { id: bill['vendorBillId'], vendorCostStatus: VendorCostStatus.NEED_CONFIRM })
                          .withSuccessData((_data: any) => {
                            updateTMSBillData(uiList.getVGridContext(), [bill], (_records) => {
                              appContext.addOSNotification("success", T(`Success`));
                              uiList.getVGridContext().getVGrid().forceUpdateView();
                            });
                          })
                          .call();
                      }
                      let message = (<div className="text-danger">Do you want to revert the vendor cost status to 'NEED CONFIRM'?</div>);
                      bs.dialogConfirmMessage('Confirm Message', message, callbackConfirm);
                    } else {
                      uiList.onShowSyncCost(bill);
                    }
                  }} >
                  <FeatherIcon.Repeat className='fw-bold' color={color} size={15} />
                </bs.Button>
              </div>
            </div>
          );
          if (closePayment) uiCheckVendorCost = <></>;
          return (
            <div className={`flex-hbox`}>
              {editCost}
              {uiCheckVendorCost}
            </div>
          );
        }
      },
      { name: 'finalPayment', label: T('Final Payment'), format: formatCurrency, dataType: 'double', state: { visible: false } },
      {
        name: 'paymentNote', label: T('Payment Note'), width: 200,
        editor: {
          type: 'string',
          onInputChange: onInputChange
        }
      },
      {
        name: 'vendorFixed', label: T('Vendor Fixed'), format: formatCurrency, dataType: 'double',
        fieldDataGetter(record) {
          if (record['tmsTrackingFixedCharge']) return record['tmsTrackingFixedCharge'];
          if (record['vendorFixed']) return record['vendorFixed'];
          return 0;
        },
        computeCssClasses: (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
          let bill = dRecord.record;
          let fixedPayment = bill['fixedPayment'];
          let tmsTrackingFixedCharge = bill['tmsTrackingFixedCharge'] ? bill['tmsTrackingFixedCharge'] : bill['vendorFixed'];
          tmsTrackingFixedCharge = tmsTrackingFixedCharge ? tmsTrackingFixedCharge : 0;
          if (fixedPayment != tmsTrackingFixedCharge) return 'text-danger';
          return '';
        },
        customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
          let value = field.fieldDataGetter ? field.fieldDataGetter(dRecord.record) : 0;
          let cssClass = field.computeCssClasses ? field.computeCssClasses(ctx, dRecord) : "";
          return (
            <div className={`flex-vbox ${cssClass} cell-number`}>
              {formatCurrency(value)}
            </div>
          )
        }
      },
      {
        name: 'vendorExtra', label: T('Vendor Extra'), format: formatCurrency, dataType: 'double',
        fieldDataGetter(record) {
          if (record['tmsTrackingExtraCharge']) return record['tmsTrackingExtraCharge'];
          if (record['vendorExtra']) return record['vendorExtra'];
          return 0;
        },
        computeCssClasses: (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
          let bill = dRecord.record;
          let extraPayment = bill['extraPayment'];
          let tmsTrackingExtraCharge = bill['tmsTrackingExtraCharge'] ? bill['tmsTrackingExtraCharge'] : bill['vendorExtra'];
          tmsTrackingExtraCharge = tmsTrackingExtraCharge ? tmsTrackingExtraCharge : 0;
          if (extraPayment != tmsTrackingExtraCharge) return 'text-danger';
          return '';
        },
        customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
          let value = field.fieldDataGetter ? field.fieldDataGetter(dRecord.record) : 0;
          let cssClass = field.computeCssClasses ? field.computeCssClasses(ctx, dRecord) : "";
          return (
            <div className={`flex-vbox ${cssClass} cell-number`}>
              {formatCurrency(value)}
            </div>
          )
        }
      },
      {
        name: 'vendorCost', label: T('Vendor Total Cost'), format: formatCurrency, dataType: 'double',
        fieldDataGetter(record) {
          if (record['tmsTrackingTotalCharge']) return record['tmsTrackingTotalCharge'];
          if (record['vendorCost']) return record['vendorCost'];
          return 0;
        },
        computeCssClasses: (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
          let bill = dRecord.record;
          let totalPayment = bill['totalPayment'];
          let tmsTrackingTotalCharge = bill['tmsTrackingTotalCharge'] ? bill['tmsTrackingTotalCharge'] : bill['vendorCost'];
          tmsTrackingTotalCharge = tmsTrackingTotalCharge ? tmsTrackingTotalCharge : 0;
          if (totalPayment != tmsTrackingTotalCharge) return 'text-danger';
          return '';
        },
        customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
          let value = field.fieldDataGetter ? field.fieldDataGetter(dRecord.record) : 0;
          let cssClass = field.computeCssClasses ? field.computeCssClasses(ctx, dRecord) : "";
          return (
            <div className={`flex-vbox ${cssClass} cell-number`}>
              {formatCurrency(value)}
            </div>
          )
        }
      },

      //
      {
        name: '_verifyPaymentInfo', label: 'V.Pay', hint: T('Verify Payment Info'),
        filterable: true, filterableType: 'options', width: 60,
        fieldDataGetter(record) {
          let value = 'N/A';
          let verifyPaymentInfo = record['verifyPaymentInfo'];
          if (verifyPaymentInfo === true || verifyPaymentInfo === false) return verifyPaymentInfo;
          return value;
        },
        customRender: (_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, focus: boolean) => {
          let bill = dRecord.record;
          let verifyPaymentInfo = bill['verifyPaymentInfo'];
          let verifyPaymentNote = bill['verifyPaymentNote'];
          const createContent = (content: any) => {
            if (!verifyPaymentNote) return (
              <div className='flex-vbox align-items-center' >
                {content}
              </div>
            );
            return (
              <bs.CssTooltip >
                <bs.CssTooltipToggle className='flex-vbox'>
                  {content}
                </bs.CssTooltipToggle>
                <bs.CssTooltipContent>
                  <div style={{ whiteSpace: 'pre-line' }}>
                    {verifyPaymentNote}
                  </div>
                </bs.CssTooltipContent>
              </bs.CssTooltip>
            )
          }
          if (verifyPaymentInfo == true) {
            return createContent(<FeatherIcon.CheckCircle className='text-success' size={16} />);
          }
          if (verifyPaymentInfo == false) {
            return createContent(<FeatherIcon.XCircle className='text-danger' size={16} />);
          }
          return null;
        }
      },
      { name: 'verifyPaymentNote', label: T('Verify Payment Note'), width: 50 },
      {
        name: '_closePayment', label: '', hint: T('Close Payment'), filterable: true, filterableType: 'options', width: 40,
        listener: {
          onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            if (event.row == cell.getRow() && event.field.name === '_closePayment') {
              cell.forceUpdate()
            }
          },
        },
        fieldDataGetter(record) {
          let closePayment = record['closePayment'];
          if (closePayment) return 'CLOSE PAYMENT';
          return 'OPEN PAYMENT';
        },
        customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
          let bill = dRecord.record;
          const onChange = (date: any) => {
            if (!modCap && !date) return;
            bill['closePayment'] = date;
            let event: grid.VGridCellEvent = {
              row: dRecord.row, field: field, event: 'Modified', data: dRecord
            }
            ctx.broadcastCellEvent(event);
          }
          let closePayment = bill['closePayment'];
          if (!closePayment) return (
            <bs.Button laf='link' className='flex-vbox text-center'
              onClick={() => onChange(util.TimeUtil.javaCompactDateTimeFormat(new Date()))}>
              <FeatherIcon.Unlock size={16} className='text-success' />
            </bs.Button>
          );
          return (
            <bs.CssTooltip >
              <bs.CssTooltipToggle className='flex-vbox'>
                <bs.Button laf='link' className='flex-vbox text-center' onClick={() => onChange(null)}>
                  <FeatherIcon.Lock size={16} className='text-danger' />
                </bs.Button>
              </bs.CssTooltipToggle>
              <bs.CssTooltipContent>
                <div style={{ whiteSpace: 'pre-line' }}>
                  {closePayment}
                </div>
              </bs.CssTooltipContent>
            </bs.CssTooltip>
          )
        }
      },
      {
        name: '_verifyVehicleInfo', label: 'V.Vehicle', hint: T('Verify Vehicle Info'), filterable: true, filterableType: 'options', width: 60,
        fieldDataGetter(record) {
          let value = 'N/A';
          let verifyVehicleInfo = record['verifyVehicleInfo'];
          if (verifyVehicleInfo === true || verifyVehicleInfo === false) return verifyVehicleInfo;
          return value;
        },
        customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, focus: boolean) => {
          let bill = dRecord.record;
          let verifyPaymentInfo = bill['verifyVehicleInfo'];
          let verifyPaymentNote = bill['verifyVehicleInfoNote'];
          const createContent = (content: any) => {
            if (!verifyPaymentNote) return (
              <div className='flex-vbox align-items-center' >
                {content}
              </div>
            );
            return (
              <bs.CssTooltip >
                <bs.CssTooltipToggle className='flex-vbox'>
                  {content}
                </bs.CssTooltipToggle>
                <bs.CssTooltipContent>
                  <div style={{ whiteSpace: 'pre-line' }}>
                    {verifyPaymentNote}
                  </div>
                </bs.CssTooltipContent>
              </bs.CssTooltip>
            )
          }
          if (verifyPaymentInfo == true) {
            return createContent(<FeatherIcon.CheckCircle className='text-success' size={16} />);
          }
          if (verifyPaymentInfo == false) {
            return createContent(<FeatherIcon.XCircle className='text-danger' size={16} />);
          }
          return null;
        }
      },
      { name: 'verifyVehicleInfoNote', label: T('Verify Note'), width: 50 },
      {
        name: '_vehicleInfoLockDate', label: '', hint: T('Vehicle Info Lock Date'), filterable: true, filterableType: 'options', width: 40,
        fieldDataGetter(record) {
          let vehicleInfoLockDate = record['vehicleInfoLockDate'];
          if (vehicleInfoLockDate) return 'LOCK VEHICLE INFO';
          return 'OPEN VEHICLE INFO';
        },
        customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, focus: boolean) => {
          let bill = dRecord.record;
          let vehicleInfoLockDate = bill['vehicleInfoLockDate'];
          if (!vehicleInfoLockDate) return;
          return (
            <bs.CssTooltip >
              <bs.CssTooltipToggle className='flex-vbox'>
                <FeatherIcon.Lock size={16} className='text-danger' />
              </bs.CssTooltipToggle>
              <bs.CssTooltipContent>
                <div style={{ whiteSpace: 'pre-line' }}>
                  {vehicleInfoLockDate}
                </div>
              </bs.CssTooltipContent>
            </bs.CssTooltip>
          )
        }
      },
      {
        name: 'collect', label: T('Collect'),
      },
      {
        name: 'isCombine', label: T('Ghép'), width: 40, cssClass: 'flex-vbox justify-content-center', container: 'fixed-right',
        editor: {
          type: 'boolean',
          onInputChange: onInputChange
        }
      },
      {
        name: 'uploadError', label: T('Upload Error'), dataTooltip: true, filterable: true, filterableType: 'options'
      },
      //
      { name: 'code', label: T('Code'), state: { visible: false } },
      {
        name: '_storageState', label: T('Storage State'), state: { visible: false },
        editor: {
          type: 'string',
          onInputChange: onInputChange,
          renderCustom(ctx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let bill = ctx.displayRecord.record;
            return (
              <input.BBSelectField
                bean={bill} field={'storageState'} options={['ACTIVE', 'ARCHIVED']} tabIndex={ctx.tabIndex} focus={ctx.focus}
                onInputChange={onInputChange} disable={!pageContext.hasUserWriteCapability()} />
            )
          },
        }
      },
      {
        name: '_actions', label: T('Acs'), container: 'fixed-right', width: 80, cssClass: 'p-0',
        customHeaderRender(ctx, field, _headerEle) {
          return (
            <div className='flex-hbox'>
              <div className='flex-grow-1'>
                <bs.Badge laf='primary' className='flex-vbox p-1 w-100 align-items-center justify-content-center'>
                  <FeatherIcon.BookOpen size={15} />
                </bs.Badge>
              </div>
              <NotificationMessage context={ctx} fieldName={field.name} />
            </div>
          )
        },
        listener: {
          onDataCellEvent(cell, event) {
            if (event.field.name === '_actions' && cell.getRow() === event.row) {
              cell.forceUpdate();
            }
          },
        },
        customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
          if (!dRecord.isDataRecord()) return;
          let record = dRecord.record;
          let issueTemplate = {
            'refEntityId': record['id'],
            'refEntity': 'lgc_tms_bill',
            'label': record['label'],
            'label2': record['customerFullName'],
            'resolverAccountId': record['responsibleAccountId'],
            'resolverAccountFullName': record['responsibleFullName'],
            'status': 'CONFIRMED',
          }
          if (record['jobTrackingId']) {
            issueTemplate['refEntity'] = 'lgc_job_tracking';
            issueTemplate['refEntityId'] = record['jobTrackingId'];
          } record
          return (
            <div className={'flex-hbox'}>
              {<WBtnBFSOneTemplate appContext={appContext} pageContext={pageContext} tmsBillIds={[record['id']]}
                onPushSuccessCb={(data) => {
                  if (!data['Error']) {
                    record['pushBfsOneVehicleInfo'] = true;
                    record['vehicleInfoLockDate'] = util.TimeUtil.javaCompactDateTimeFormat(new Date());
                  }
                  uiRoot.getVGridContext().getVGrid().forceUpdateView();
                }}
              />}
              {<WBtnBFSOneTemplateCost appContext={appContext} pageContext={pageContext} tmsBillIds={[record['id']]}
                onPushSuccessCb={(data) => {
                  if (!data['Error']) {
                    record['pushBfsOneCost'] = true;
                    record['closePayment'] = util.TimeUtil.javaCompactDateTimeFormat(new Date());
                  }
                  uiRoot.getVGridContext().getVGrid().forceUpdateView();
                }}
              />}
              {renderRoundUsedStatus(ctx, record)}
              {RenderTrackingButton(ctx, record)}
              {renderBtnTrackingAPI(ctx, record, { cssClass: 'px-0 py-1' })}
            </div>
          )
        },
      },
      // {
      //   name: 'jobTracking', label: '', container: 'fixed-right', width: 40, state: { visible: !!jobTrackingProject },
      //   customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
      //     let record = dRecord.record;
      //     return (
      //       <WBtnTMSBillJobTracking context={ctx} bill={record} jobTrackingProject={jobTrackingProject} />
      //     )
      //   },
      // },
      {
        name: 'processStatus', label: T('Status'), sortable: true, filterableType: 'options', filterable: true,
        container: 'fixed-right', width: 105,
        listener: {
          onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            if (event.row == cell.getRow() && event.field.name === 'processStatus') {
              cell.forceUpdate()
            }
          },
        },
        customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
          if (!dRecord.isDataRecord()) return;
          let bill = dRecord.record;
          let status = bill['processStatus'];
          let color = TMSBillProcessStatusTools.getColor(status);
          const allValues: Array<any> = Object.values(TMSBillProcessStatus);
          return (
            <bs.Popover style={{ width: 66 }} className="d-flex flex-center w-100"
              title={T('Status')} closeOnTrigger=".btn" >
              <bs.PopoverToggle
                className={`flex-hbox flex-center p-1 rounded-2 bg-${color}-subtle text-${color} w-100 `}>
                <span>{TMSBillProcessStatusTools.getIcon(status)} {TMSBillProcessStatusTools.getLabel(status)}</span>
              </bs.PopoverToggle>
              <bs.PopoverContent>
                <div className='flex-vbox gap-2' style={{ width: '200px' }}>
                  {allValues.map((value: TMSBillProcessStatus) => {
                    return (
                      <bs.Button laf={TMSBillProcessStatusTools.getColor(value)} onClick={() => {
                        bill['processStatus'] = value;
                        let event: grid.VGridCellEvent = {
                          row: dRecord.row, field: field, event: 'Modified', data: dRecord
                        }
                        ctx.broadcastCellEvent(event);
                      }}>
                        {TMSBillProcessStatusTools.getIcon(value)} {TMSBillProcessStatusTools.getLabel(value)}
                      </bs.Button>
                    )
                  })}
                </div>
              </bs.PopoverContent>
            </bs.Popover>
          )
        },
      },
      ...TMSVGridConfigTool.ENTITY_COLUMNS
    ],
    fieldGroups: {
      "houseBillInfo": {
        label: T('House Bill'),
        visible: true,
        fields: hblGroupFields
      },
      "measurementFields": {
        label: T('Measurement'),
        visible: true,
        fields: measurementFields
      },
      "deliveryPlan": {
        label: T('Delivery Plan'),
        visible: true,
        fields: [
          'time', 'estimateTime', 'deliveryPlan',
        ]
      },
      "trackingInfo": {
        label: T('Tracking Info'),
        visible: true,
        fields: [
          'description', 'tmsTrackingDescription', 'licensePlate', 'truckNo', 'vendorFullName', 'reportBFSOne',
          'tmsTrackingFleetLabel'
        ]
      },

      'pickupDeliveryInfo': {
        label: T('Pickup/Delivery Info'),
        visible: true,
        fields: [
          'senderFullName',
          'senderAddress', 'senderContact', 'senderLocationAddress',
          'receiverFullName',
          'receiverAddress', 'receiverContact', 'receiverLocationAddress',
          'stopLocations'
        ]
      },
      'cost': {
        label: T('Cost'),
        visible: true,
        fields: [
          'payOnBehalfCustomerName', 'fixedPayment', 'extraPayment', 'matchPrice',
          'totalPayment', 'paymentStatus', 'totalPaymentTax', 'finalPayment', 'paymentNote', '_editCost'
        ]
      },
      'vendorCost': {
        label: T('Cước Thầu Phụ'),
        visible: true,
        fields: [
          'vendorFixed', 'vendorExtra', 'vendorCost'
        ]
      },
    },

    summary: {
      dataCellHeight: 210,
      render: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord, _viewMode: grid.ViewMode) => {
        return renderSummary(ctx, dRecord);
      }
    },
  }

  if (!pageContext.hasUserWriteCapability() || readOnly) delete records.editor;
  if (records.editor) {
    for (let sel of records.fields) {
      if (sel.editor) sel.editor.enable = records.editor.enable;
    }
  }

  if (uiRoot.props.hbl) {
    for (let sel of records.fields) {
      if (hblGroupFields.includes(sel.name)) {
        if (sel.state) {
          sel.state.visible = false;
        } else {
          sel.state = {
            visible: false
          }
        }
      }
    }
  }

  if (bs.ScreenUtil.isSmallScreen()) delete records.control;
  return records;
}