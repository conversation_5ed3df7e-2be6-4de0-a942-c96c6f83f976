import React from 'react';
import { bs, entity, app, grid, component } from '@datatp-ui/lib';
import * as FeatherIcon from 'react-feather'

import { T } from '../backend';
import { TMSUtils } from '../utils';

interface BFSOneTemplateListProps extends entity.DbEntityListProps {
  onPushSuccessCb?: (result: any) => void;
}
export class BFSOneTruckTrackingTemplateList extends entity.DbEntityList<BFSOneTemplateListProps> {
  constructor(props: entity.DbEntityListProps) {
    super(props);
    const { plugin } = props;
    plugin.getListModel().sort('verifyTruckInfo');
    plugin.getListModel().getRecords().forEach(sel => {
      if (sel['verifyTruckInfo']) grid.getRecordState(sel).selected = true;
    });
    this.forceUpdate();
  }
  createVGridConfig() {
    let config: grid.VGridConfig = {
      title: T('BFSOne Transactions'),
      record: {
        editor: {
          supportViewMode: ['table'],
          enable: false,
        },
        fields: [
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(true),
          {
            name: 'HWBNO', label: T('Hbl No'),
            computeCssClasses(_ctx, dRecord) {
              let rec = dRecord.record;
              if (rec['verifyHblNo']) return 'text-success';
              return 'text-danger';
            },
          },
          { name: 'TruckingFrom', label: T('Trucking From') },
          { name: 'TruckingTo', label: T('Trucking To') },
          { name: 'TruckNo', label: T('Truck No') },
          { name: 'TruckType', label: T('Truck Type') },
          { name: 'DriverName', label: T('Driver Name') },
          { name: 'DriverTel', label: T('Driver Tel') },
          { name: 'ContNo', label: T('Cont No') },
          { name: 'ContType', label: T('Cont Type') },
          { name: 'DeliveryDate', label: T('Delivery Date') },
          { name: 'Creator', label: T('Creator') },
          { name: 'bfsOneCode', label: T('UserId') },
          { name: 'bfsOneUsername', label: T('Username') },
          {
            name: 'verifyTruckInfo', label: T('Verify'), width: 60, container: 'fixed-right',
            customRender(_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, focus: boolean) {
              const record = dRecord.record;
              if (record['verifyTruckInfo']) return <FeatherIcon.CheckCircle className='text-success' size={14} />;
              let html = <FeatherIcon.XCircle className='text-danger' size={14} />;
              return TMSUtils.renderTooltip(record['missingFields'].join(',\n'), html);
            }
          },
        ],
      },
      toolbar: {
        actions: [],
      },
      footer: {
        page: {
          render: (ctx: grid.VGridContext) => {
            let uiRoot = ctx.uiRoot as BFSOneTruckTrackingTemplateList;
            return (
              <bs.Toolbar>
                <bs.Button laf='info' onClick={() => {
                  let { appContext, plugin, onPushSuccessCb } = uiRoot.props;
                  let recs = plugin.getListModel().getSelectedRecords();
                  const callBack = (data: any) => {
                    let label = 'Thành Công';
                    let cssClass = 'bg-success bg-opacity-25';
                    if (data.Error) {
                      label = 'Thất Bại';
                      cssClass = 'bg-danger bg-opacity-25';
                    }
                    let ui = (
                      <div className={cssClass}>
                        <h4 className='mx-1'>{label}</h4>
                        <component.LazyJsonView className={cssClass} object={data} />
                      </div>
                    )
                    bs.dialogShow(null, ui, { size: 'md' });
                  }
                  appContext.createBackendCall("BFSOneApiService", "updateTruckTracking", { records: recs })
                    .withSuccessData((data) => {
                      if (onPushSuccessCb) onPushSuccessCb(data);
                      callBack(data);
                      this.markLoading(false);
                      this.forceUpdate();
                    })
                    .withFail(() => {
                      this.markLoading(false);
                      this.forceUpdate();
                    })
                    .withEntityOpNotification('commit', 'Push Truck Tracking')
                    .call();
                  this.markLoading(true);
                  this.forceUpdate();
                }}>
                  <FeatherIcon.UploadCloud size={12} /> {T("Push Data")}
                </bs.Button>
              </bs.Toolbar>
            );
          }
        },
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          }
        }
      }
    }
    return config;
  }
  render() {
    let { plugin } = this.props;
    if (this.isLoading()) return this.renderLoading();
    let verifyTruckInfoRecs = plugin.getRecords().filter(rec => rec['verifyTruckInfo']);
    let countRecVerify = verifyTruckInfoRecs ? verifyTruckInfoRecs.length : 0;
    let countRecVerifyFail = plugin.getRecords().length - countRecVerify;
    return (
      <div className="flex-vbox">
        <div className="flex-grow-0">
          <bs.Badge laf='success'>
            <FeatherIcon.CheckCircle className='mx-1' size={12} /> {`Complete : ${countRecVerify}`}
          </bs.Badge>
          <bs.Badge laf='danger' className='mx-2'>
            <FeatherIcon.XCircle className='mx-1' size={12} />{`Fail : ${countRecVerifyFail}`}
          </bs.Badge>
          <bs.Badge laf='info'>
            <FeatherIcon.File className='mx-1' size={12} />{`Total : ${plugin.getRecords().length}`}
          </bs.Badge>
        </div>
        {this.renderUIGrid()}
      </div>
    )
  }
}

function calculateFeeSeq(bfsData: any[]) {
  let hblMap: Record<string, any[]> = {};
  for (let i = 0; i < bfsData.length; i++) {
    let rec = bfsData[i];
    rec['index'] = i + 1;
    let hwbNo = rec['HWBNO'];
    if (hwbNo) {
      if (!hblMap[hwbNo]) {
        hblMap[hwbNo] = [];
      }
      hblMap[hwbNo].push(rec);
    }
  }
  for (let hwbNo in hblMap) {
    let recs: Array<any> = hblMap[hwbNo];
    if (recs.length > 0) {
      for (let i = 0; i < recs.length; i++) {
        if (i == 0) continue;
        let rec = recs[i];
        if (!rec['FeeName']) continue;
        rec['seq'] = i;
        rec['FeeName'] = `${rec['FeeName']}(${i})`;
      }
    }
  }
}
export class BFSOneCostTemplateList extends entity.DbEntityList<BFSOneTemplateListProps> {
  merge: boolean = false;
  originRecs: Array<any> = [];
  constructor(props: entity.DbEntityListProps) {
    super(props);
    const { plugin } = props;
    this.originRecs = [...props.plugin.getListModel().getRecords()];
    plugin.getListModel().sort('verifyPaymentInfo', true);
    plugin.getListModel().getRecords().forEach(sel => {
      if (sel['verifyPaymentInfo']) grid.getRecordState(sel).selected = true;
    });
    this.forceUpdate();
  }

  mergeHblCost = () => {
    let { plugin } = this.props;
    if (!this.merge) {
      let results: any[] = [];
      let mapRec: Record<string, any[]> = {};
      for (let rec of this.originRecs) {
        let hwbNo = rec['HWBNO'];
        let unit = rec['Unit'];
        let price = rec['UnitPrice'];
        let vendorBfsOneCode = rec['PartnerID'];
        if (hwbNo && rec['verifyPaymentInfo']) {
          let key = `${vendorBfsOneCode}-${hwbNo}-${unit}-${price}`;
          if (!mapRec[key]) {
            mapRec[key] = [];
          }
          mapRec[key].push(rec);
        } else {
          results.push(rec);
        }
      }

      for (let key in mapRec) {
        let recs = mapRec[key];
        if (recs.length > 0) {
          let mergedRec: any = { ...recs[0] };
          if (recs.length == 1) {
            results.push(mergedRec);
            continue;
          }
          let quantity = 0;
          let mergedItems: any[] = [];
          recs.forEach((rec: any) => {
            quantity += rec['Quantity'];
            mergedItems.push(rec['tmsBillId']);
          });
          mergedRec['mergedItems'] = mergedItems;
          mergedRec['Quantity'] = quantity;
          let totalUnitPrice = mergedRec['UnitPrice'] * mergedRec['Quantity'];
          mergedRec['Total'] = totalUnitPrice + ((mergedRec['VAT'] / 100) * totalUnitPrice);
          results.push(mergedRec);
        }
      }
      plugin.getListModel().replaceWith(results);
    } else {
      plugin.getListModel().replaceWith(this.originRecs);
    }
    calculateFeeSeq(plugin.getListModel().getRecords());
    plugin.getListModel().sort('verifyPaymentInfo', true);
    this.merge = !this.merge;
    this.forceUpdate();
  }

  createVGridConfig() {
    let config: grid.VGridConfig = {
      record: {
        editor: {
          supportViewMode: ['table'],
          enable: false,
        },
        fields: [
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(true),
          {
            name: 'HWBNO', label: T('Hbl No'),
            computeCssClasses(_ctx, dRecord) {
              let rec = dRecord.record;
              if (rec['verifyHblNo']) return 'text-success';
              return 'text-danger';
            },
          },
          { name: 'PartnerID', label: T('Partner ID') },
          { name: 'PartnerName', label: T('Partner Name') },
          { name: 'Office', label: T('Office'), filterable: true, filterableType: 'options' },
          {
            name: 'FeeName', label: 'FeeName'
          },
          { name: 'FeeCode', label: T('FeeCode') },
          { name: 'Unit', label: T('Unit') },
          { name: 'Quantity', label: T('Quantity') },
          { name: 'UnitPrice', label: T('Unit Price') },
          { name: 'Currency', label: T('Currency') },
          { name: 'ExchangeRate', label: T('Exchange Rate') },
          { name: 'VAT', label: T('VAT') },
          { name: 'Total', label: T('Total') },
          { name: 'Notes', label: T('Notes') },
          { name: 'Creator', label: T('Creator') },
          { name: 'bfsOneCode', label: T('Userid') },
          { name: 'bfsOneUsername', label: T('Username') },
          {
            name: 'verifyPaymentInfo', label: T('Verify'), width: 60, container: 'fixed-right',
            customRender(_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, focus: boolean) {
              const record = dRecord.record;
              if (record['verifyPaymentInfo']) return <FeatherIcon.CheckCircle className='text-success' size={14} />;
              let html = <FeatherIcon.XCircle className='text-danger' size={14} />;
              return TMSUtils.renderTooltip(record['missingFields'].join(',\n'), html);
            }
          },
        ],
      },
      toolbar: {
        actions: [],
      },
      footer: {
        page: {
          render: (ctx: grid.VGridContext) => {
            let uiRoot = ctx.uiRoot as BFSOneCostTemplateList;
            return (
              <bs.Toolbar>
                <div className='flex-grow-0 flex-hbox align-items-center mt-1 mx-1' style={{ height: 15 }}>
                  <div className="form-check form-switch py-0 m-0">
                    <input className="form-check-input p-0" type="checkbox" style={{ cursor: 'pointer' }}
                      role="switch" id="rawDataToggle" checked={this.merge}
                      onChange={this.mergeHblCost}
                    />
                  </div>
                  <label className="form-check-label fs--1 d-flex align-items-center" htmlFor="rawDataToggle" >
                    <span>{T('Merge Row')}</span>
                  </label>
                </div>
                <bs.Button laf='info' onClick={() => {
                  let { appContext, plugin, onPushSuccessCb } = uiRoot.props;
                  let recs = plugin.getListModel().getSelectedRecords();
                  const callBack = (data: any) => {
                    let label = 'Thành Công';
                    let cssClass = 'bg-success bg-opacity-25';
                    if (data.Error) {
                      label = 'Thất Bại';
                      cssClass = 'bg-danger bg-opacity-25';
                    }
                    let ui = (
                      <div className={cssClass}>
                        <h4 className='mx-1'>{label}</h4>
                        <component.LazyJsonView className={cssClass} object={data} />
                      </div>
                    )
                    bs.dialogShow(null, ui, { size: 'md' });
                  }

                  appContext.createBackendCall("BFSOneApiService", "updateTruckCosting", { records: recs })
                    .withSuccessData((data) => {
                      if (onPushSuccessCb) onPushSuccessCb(data);
                      callBack(data);
                      this.markLoading(false);
                      this.forceUpdate();
                    })
                    .withFail(() => {
                      this.markLoading(false);
                      this.forceUpdate();
                    })
                    .withEntityOpNotification('commit', 'Push Cost')
                    .call();
                  this.markLoading(true);
                  this.forceUpdate();
                }}>
                  <FeatherIcon.UploadCloud size={12} /> {T("Push Data")}
                </bs.Button>
              </bs.Toolbar>
            );
          }
        },
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          }
        }
      }
    }
    return config;
  }

  render() {
    let { plugin } = this.props;
    if (this.isLoading()) return this.renderLoading();
    let verifyPaymentInfoRecs = plugin.getRecords().filter(rec => rec['verifyPaymentInfo']);
    let countRecVerify = verifyPaymentInfoRecs ? verifyPaymentInfoRecs.length : 0;
    let countRecVerifyFail = plugin.getRecords().length - countRecVerify;
    return (
      <div className="flex-vbox">
        <div className="flex-grow-0">
          <bs.Badge laf='success'>
            <FeatherIcon.CheckCircle className='mx-1' size={12} /> {`Complete : ${countRecVerify}`}
          </bs.Badge>
          <bs.Badge laf='danger' className='mx-2'>
            <FeatherIcon.XCircle className='mx-1' size={12} />{`Fail : ${countRecVerifyFail}`}
          </bs.Badge>
          <bs.Badge laf='info'>
            <FeatherIcon.File className='mx-1' size={12} />{`Total : ${plugin.getRecords().length}`}
          </bs.Badge>
        </div>
        {this.renderUIGrid()}
      </div>
    )
  }
}

interface WBtnBFSOneTemplateProps extends app.AppComponentProps {
  color?: string;
  tmsBillIds: Array<any>;
  label?: any;
  laf?: bs.ButtonLaf;
  outline?: boolean;
  onPushSuccessCb?: (result: any) => void;
}

class BFSOneTruckTemplateLoadable extends app.AppComponent<WBtnBFSOneTemplateProps> {
  records: any[] = [];
  componentDidMount(): void {
    const { appContext, tmsBillIds } = this.props;
    this.markLoading(true);
    this.forceUpdate();
    appContext.createHttpBackendCall('BFSOneApiService', 'createBFSTruckTrackingTemplateByTMSBillIds', { ids: tmsBillIds })
      .withSuccessData((recs: Array<any>) => {
        this.records = recs;
        this.markLoading(false);
        this.forceUpdate();
      })
      .call();
  }

  render(): React.ReactNode {
    if (this.isLoading()) return this.renderLoading();
    const { appContext, pageContext, onPushSuccessCb } = this.props;
    return (
      <BFSOneTruckTrackingTemplateList
        appContext={appContext} pageContext={pageContext}
        plugin={new entity.DbEntityListPlugin(this.records)} onPushSuccessCb={onPushSuccessCb} />
    )
  }
}

export class WBtnBFSOneTemplate extends app.AppComponent<WBtnBFSOneTemplateProps> {
  onCreateBFSOneData = () => {
    const { appContext, pageContext, tmsBillIds, onPushSuccessCb } = this.props;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <BFSOneTruckTemplateLoadable
          appContext={appCtx} pageContext={pageCtx} tmsBillIds={tmsBillIds} onPushSuccessCb={onPushSuccessCb} />
      )
    }
    pageContext.createPopupPage('bfs-one-template', T('BFS Truck Template'), createAppPage, { size: 'lg', backdrop: 'static' });
  }

  render(): React.ReactNode {
    const { label, laf, className, outline } = this.props;
    return (
      <bs.Button outline={outline} className={className} laf={laf ? laf : 'link'} onClick={this.onCreateBFSOneData}>
        <FeatherIcon.UploadCloud size={14} />{label}
      </bs.Button>
    )
  }
}


class BFSOneCostTemplateLoadable extends app.AppComponent<WBtnBFSOneTemplateProps> {
  records: any[] = [];
  componentDidMount(): void {
    const { appContext, tmsBillIds } = this.props;
    this.markLoading(true);
    this.forceUpdate();
    appContext.createHttpBackendCall('BFSOneApiService', 'createBFSOneTemplateCostByTMSBillIds', { ids: tmsBillIds })
      .withSuccessData((recs: Array<any>) => {
        this.records = recs;
        this.markLoading(false);
        this.forceUpdate();
      })
      .call();
  }

  render(): React.ReactNode {
    if (this.isLoading()) return this.renderLoading();
    const { appContext, pageContext, onPushSuccessCb } = this.props;
    return (
      <BFSOneCostTemplateList
        appContext={appContext} pageContext={pageContext}
        plugin={new entity.DbEntityListPlugin(this.records)} onPushSuccessCb={onPushSuccessCb} />
    )
  }
}
export class WBtnBFSOneTemplateCost extends app.AppComponent<WBtnBFSOneTemplateProps> {
  onCreateBFSOneData = () => {
    const { pageContext, tmsBillIds, onPushSuccessCb } = this.props;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <BFSOneCostTemplateLoadable
          appContext={appCtx} pageContext={pageCtx} tmsBillIds={tmsBillIds} onPushSuccessCb={onPushSuccessCb} />
      )
    }
    pageContext.createPopupPage('bfs-one-template', T('BFS Costing Template'), createAppPage, { size: 'lg', backdrop: 'static' });
  }

  render(): React.ReactNode {
    const { label, laf, color, className, outline } = this.props;
    return (
      <bs.Button outline={outline} className={className} laf={laf ? laf : 'link'} onClick={this.onCreateBFSOneData}>
        <FeatherIcon.DollarSign className={color ? color : 'text-danger'} size={14} />{label}
      </bs.Button>
    )
  }
}

