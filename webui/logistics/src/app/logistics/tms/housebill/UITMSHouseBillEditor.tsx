import React from 'react';
import { bs, entity, input, app, util, grid } from '@datatp-ui/lib';
import { module } from "@datatp-ui/erp";
import * as FeatherIcon from 'react-feather'

import { T } from '../backend';
import { Method, Purpose, TypeOfTransportation } from './models';
import { BBRefTMSCustomer } from '../partner/BBRefTMSCustomer';
import { BBRefTMSCarrier } from '../partner/BBRefTMSCarrier';
import { UITMSHouseBillList, UITMSHouseBillListPlugin } from './TMSHouseBillGeneralList';
import { UITMSBillUtils } from '../bill/UITMSBillUtils';
import BBRefLocation = module.settings.BBRefLocation;

export function showTMSHouseBillEditor(appCtx: app.AppContext, pageCtx: app.PageContext, hblId: any, onPostCommit?: (bean: any) => void) {
  const successData = (data: any) => {
    if (!data) {
      data = {
        'purpose': Purpose.Domestic,
        'method': Method.Lcl,
        'typeOfTransportation': TypeOfTransportation.Road
      };
    }
    const createAppPage = (_appCtx: app.AppContext, _pageCtx: app.PageContext) => {
      return (
        <TMSHouseBillGeneralEditor appContext={_appCtx} pageContext={_pageCtx} observer={new entity.ComplexBeanObserver(data)}
          onPostCommit={onPostCommit} />
      )
    }
    pageCtx.createPopupPage('tms-house-bill-editor', T('House Bill'), createAppPage, { size: 'xl' });
  }
  appCtx
    .createHttpBackendCall('TMSHouseBillService', 'getTMSHouseBillById', { 'hblId': hblId })
    .withSuccessData(successData)
    .call();

}

interface TMSHouseBillGeneralEditorProps extends entity.AppComplexEntityEditorProps {
  houseBillPlugin?: entity.DbEntityListPlugin;
}
export class TMSHouseBillGeneralEditor extends entity.AppDbComplexEntityEditor<TMSHouseBillGeneralEditorProps> {
  plugin: entity.DbEntityListPlugin;

  constructor(props: TMSHouseBillGeneralEditorProps) {
    super(props);
    const { observer, houseBillPlugin } = this.props;
    this.plugin = new entity.DbEntityListPlugin([]);
    if (houseBillPlugin) {
      this.plugin = houseBillPlugin;
      this.plugin.getRecords().forEach((record: any) => {
        grid.getRecordState(record).markModified();
      });
    } else if (!observer.isNewBean()) {
      this.plugin = new UITMSHouseBillListPlugin().withHouseBillId(observer.getBeanProperty('id'));
    }
  }

  fetchHouseBill = (hblId: any) => {
    let { appContext, observer } = this.props;
    appContext
      .createHttpBackendCall('TMSHouseBillService', 'getTMSHouseBillById', { 'hblId': hblId })
      .withSuccessData((data: any) => {
        observer.replaceWithUpdate(data);
        this.plugin = new UITMSHouseBillListPlugin().withHouseBillId(observer.getBeanProperty('id'));
        this.nextViewId();
        this.forceUpdate();
      })
      .call();
  }

  onSaveBills = (hbl: any, records: Array<any>) => {
    let { appContext, observer, onPostCommit } = this.props;
    if (records.length === 0) {
      if (onPostCommit) onPostCommit(hbl);
      observer.replaceWithUpdate(hbl);
      this.plugin = new UITMSHouseBillListPlugin().withHouseBillId(observer.getBeanProperty('id'));
      this.nextViewId();
      this.forceUpdate();
    } else {
      appContext
        .createHttpBackendCall('TMSRestCallService', 'saveTMSBillModels', { records: records })
        .withSuccessData(() => {
          this.fetchHouseBill(hbl['id']);
          if (onPostCommit) onPostCommit(hbl);
        })
        .call();
    }
  }

  render(): React.ReactNode {
    let { appContext, pageContext, observer, houseBillPlugin, onPostCommit } = this.props;
    return (
      <bs.VSplit >
        <bs.VSplitPane width={500}>
          <UITMSHouseBillEditor appContext={appContext} pageContext={pageContext} observer={observer}
            onConvertWithBFSOneDataCb={(data: any) => {
              observer.replaceWithUpdate(data.houseBill);
              this.plugin = new entity.DbEntityListPlugin(data.bills);
              this.plugin.getRecords().forEach((record: any) => {
                grid.getRecordState(record).markModified();
              });
              this.nextViewId();
              this.forceUpdate();
            }}
            onPostCommit={(hbl) => {
              this.plugin.getRecords().forEach((record: any) => {
                record = UITMSBillUtils.updateBillWithHbl(hbl, record);
              });
              this.onSaveBills(hbl, this.plugin.getListModel().getModifiedRecords())
            }} />
        </bs.VSplitPane>
        <bs.VSplitPane>
          <UITMSHouseBillList key={`hbl-${this.viewId}`} type='page'
            appContext={appContext} pageContext={pageContext}
            plugin={this.plugin} hbl={observer.getMutableBean()} readOnly={observer.isNewBean()}
            onModifyBean={() => {
              houseBillPlugin = undefined;
              this.plugin = new UITMSHouseBillListPlugin().withHouseBillId(observer.getBeanProperty('id'));
              if (onPostCommit) onPostCommit(observer.getMutableBean());
              this.forceUpdate();
            }} />
        </bs.VSplitPane>
      </bs.VSplit>
    )
  }
}

interface UITMSHouseBillEditorProps extends entity.AppComplexEntityEditorProps {
  onConvertWithBFSOneDataCb?: (data: any) => void;
}
export class UITMSHouseBillEditor extends entity.AppDbComplexEntityEditor<UITMSHouseBillEditorProps> {
  componentWillUnmount(): void {
    let { pageContext, observer } = this.props;
    let countInputChanges = observer.countInputChanges();
    if (countInputChanges > 0) {
      let createAppPage = (_appCtx: app.AppContext, pageCtx: app.PageContext) => {
        return (
          <div className='flex-vbox'>
            <h4>{T('You want to save House Bill!')}</h4>
            <bs.Toolbar>
              {this.btnSave(() => pageCtx.back())}
            </bs.Toolbar>
          </div>
        )
      }
      pageContext.createPopupPage('', '', createAppPage);
    }
  }

  btnSave = (onCommit?: (bean: any) => void) => {
    let { appContext, pageContext, observer } = this.props;
    return (
      <entity.ButtonEntityCommit
        appContext={appContext} pageContext={pageContext} hide={!pageContext.hasUserWriteCapability()} observer={observer}
        commit={{
          entityLabel: T('TMS House Bill'), context: 'company',
          service: 'TMSHouseBillService', commitMethod: 'saveTMSHouseBill'
        }}
        onPostCommit={(bean) => {
          if (onCommit) onCommit(bean);
          this.onPostCommitCallback(bean);
          observer.clearInputChanges();
        }}
      />
    )
  }

  doCommit() {
    let { appContext, pageContext, observer, onPostCommit } = this.props;
    let commitedEntityCallback = (entity: any) => {
      observer.replaceWithUpdate(entity);
      observer.commitAndGet();
      observer.clearInputChanges();
      if (onPostCommit) onPostCommit(entity);
      if (pageContext.breadcumbs) pageContext.breadcumbs.forceUpdatePreviousPage();
    };
    let entity = observer.commitAndGet();
    let params = { 'entity': entity };
    appContext
      .createHttpBackendCall('TMSHouseBillService', 'saveTMSHouseBill', params)
      .withSuccessData(commitedEntityCallback)
      .withEntityOpNotification('commit', T('TMS House Bill'))
      .call()
  }

  createTMSHouseBillWithBFSOneData = (hblNo: string) => {
    let { appContext, onConvertWithBFSOneDataCb } = this.props;
    this.markLoading(true);
    this.forceUpdate();
    appContext
      .createHttpBackendCall('TMSHouseBillService', 'createTMSHouseBillWithBFSOneData', { hblNo: hblNo })
      .withSuccessData((data: any) => {
        this.markLoading(false);
        if (onConvertWithBFSOneDataCb) onConvertWithBFSOneDataCb(data);
      })
      .call();
  }

  verifyHblNo = (hblNo: any) => {
    let { appContext, observer } = this.props;
    appContext.createHttpBackendCall('TMSHouseBillService', 'verifyHblNo', { hblNo: hblNo })
      .withSuccessData((data) => {
        observer.replaceBeanProperty('verifyHblNo', data);
        this.forceUpdate();
      })
      .call();
  }

  render() {
    if (this.isLoading()) return (
      <div className='flex-hbox justify-content-center align-items-center'>
        <div className='flex-vbox text-center text-warning'>
          <h4>{T('Hệ thống đang tải dữ liệu...')}</h4>
          {this.renderLoading()}
        </div>
      </div>
    )
    let { appContext, pageContext, observer } = this.props;
    let hbl = observer.getMutableBean();
    let importAndExportOb = observer.createObserver('importAndExport', {});
    let importAndExport = importAndExportOb.getMutableBean();
    let houseBillGoodsOb = observer.createComplexBeanObserver('houseBillGoods', {});
    let houseBillGoods = houseBillGoodsOb.getMutableBean();
    let writeCap = pageContext.hasUserWriteCapability();
    let hblColor = hbl.verifyHblNo ? 'text-success' : 'text-danger';
    return (
      <div className='flex-vbox' onKeyDown={(evt) => {
        if (evt.ctrlKey && evt.key === 's') {
          evt.preventDefault();
          this.doCommit();
        }
      }}>
        <div className='flex-grow-1'>
          <bs.Card header='House Bill'>
            <bs.Row>
              <bs.Col span={6}>
                <input.BBStringField bean={hbl} label='File No' field='fileNo' disable={!writeCap} />
              </bs.Col>
              <bs.Col span={6} >
                <bs.FormLabel className='flex-hbox'>
                  {T('Hbl No')}
                  <div className='mx-1'>
                    {hbl.verifyHblNo ? <FeatherIcon.CheckCircle className={hblColor} size={12} /> : <FeatherIcon.XCircle className={hblColor} size={12} />}
                  </div>
                </bs.FormLabel>
                <input.BBStringField className={hblColor} onKeyDown={(_winput, event, _currInput) => {
                  if (event.key === 'Enter') {
                    this.createTMSHouseBillWithBFSOneData(_currInput);
                  }
                }}
                  bean={hbl} inputObserver={observer} field='hblNo' disable={!writeCap}
                  onInputChange={(_bean: any, _field: string, _oldVal: any, newVal: any) => {
                    if (_oldVal != newVal && !_bean['verifyHblNo']) this.verifyHblNo(newVal);
                  }} />
              </bs.Col>
            </bs.Row>
            <input.BBDateInputMask key={`last-shipping-date-${util.IDTracker.next()}`}
              bean={hbl} inputObserver={observer} label='Last Shipping Date'
              field='lastShippingDate' format='DD/MM/YYYY' disabled />
            <bs.Row>
              <bs.Col span={6}>
                <BBRefTMSCustomer key={`customer-${hbl['customerId']}`} disable={!writeCap}
                  minWidth={650} placeholder={'Customer'} showTaxCode
                  appContext={appContext} pageContext={pageContext}
                  bean={hbl} inputObserver={observer} beanIdField={'customerId'} beanLabelField={'customerFullName'} label='Customer'
                />
              </bs.Col>
              <bs.Col span={6}>
                < input.BBOptionAutoComplete allowUserInput disable={!writeCap} key={`root-${hbl['root']}`}
                  bean={hbl} field={'root'} label={T('Root')}
                  options={['BEEHPH', 'BEEHAN', 'BEEHCM', 'BEEDAD', 'BEELS', 'BEEND', 'BEETH', 'BEEHNA', 'BEENA', 'BEEHD', 'MARINE']}
                />
              </bs.Col>
            </bs.Row>
            <bs.Row>
              <bs.Col span={3}>
                <input.BBSelectField disable={!writeCap}
                  bean={hbl} inputObserver={observer} field={'purpose'} label={T('Purpose')}
                  options={[Purpose.Export, Purpose.Import, Purpose.Domestic, Purpose.PortTransfer, Purpose.Cbt]} />
              </bs.Col>
              <bs.Col span={3}>
                <input.BBSelectField disable={!writeCap}
                  bean={hbl} inputObserver={observer} field={'method'} label={T('Method')}
                  options={[Method.Fcl, Method.Lcl]} />
              </bs.Col>
              <bs.Col span={6}>
                <input.BBSelectField disable={!writeCap}
                  bean={hbl} inputObserver={observer} field={'typeOfTransportation'} label={T('Type Of Trans')}
                  options={[
                    TypeOfTransportation.Sea, TypeOfTransportation.Air,
                    TypeOfTransportation.Road, TypeOfTransportation.Rail, TypeOfTransportation.Other
                  ]} />
              </bs.Col>
            </bs.Row>
            <bs.Row>
              <bs.Col span={6}>
                <input.BBStringField bean={hbl} inputObserver={observer} label='Booking/Bill' field='bookingCode' disable={!writeCap} />
              </bs.Col>
              <bs.Col span={6}>
                <input.BBStringField bean={hbl} inputObserver={observer} label='CDS' field='declarationNumber' disable={!writeCap} />
              </bs.Col>
            </bs.Row>
            <input.BBTextField bean={hbl} inputObserver={observer} label='Error' field='error' disable={!writeCap} />
          </bs.Card>
          <bs.Card header='Import/Export'>
            <bs.Row>
              <bs.Col span={6}>
                <input.BBStringField bean={importAndExport} inputObserver={importAndExportOb} label='WH Contact' field='warehouseContact' disable={!writeCap} />
              </bs.Col>
              <bs.Col span={6}>
                <BBRefLocation
                  locationTags={['app:tms']} minWidth={500} label='Warehouse'
                  appContext={appContext} pageContext={pageContext}
                  bean={importAndExport} inputObserver={importAndExportOb}
                  disable={!writeCap} refLocationBy='id'
                  beanIdField={'warehouseLocationId'} beanLabelField={'warehouseLocationLabel'} placeholder='Full Return WH' />
              </bs.Col>
            </bs.Row>
            <bs.Row>
              <bs.Col span={6}>
                <BBRefTMSCarrier minWidth={400} label='Carrier'
                  appContext={appContext} pageContext={pageContext} disable={!writeCap}
                  bean={importAndExport} inputObserver={importAndExportOb}
                  beanIdField={'carrierId'} beanLabelField={'carrierFullName'} placeholder={'Carrier'}
                />
              </bs.Col>
              <bs.Col span={6}>
                <input.BBStringField bean={importAndExport} inputObserver={importAndExportOb} label='COT/ETA Note' field='etaCutOffTimeNote' disable={!writeCap} />
              </bs.Col>
            </bs.Row>
          </bs.Card>
          <bs.Card header='Goods'>
            <input.BBStringField bean={houseBillGoods} inputObserver={houseBillGoodsOb} label='Goods' field='goods' disable />
            <bs.Row>
              <bs.Col span={6}>
                <input.BBDoubleField bean={houseBillGoods} inputObserver={houseBillGoodsOb} label='Quantity' field='quantity' disable />
              </bs.Col>
              <bs.Col span={6}>
                <module.settings.BBRefUnit key={`unit-${houseBillGoods['quantityUnit']}`} minWidth={400} disable
                  appContext={appContext} pageContext={pageContext}
                  placement="left" placeholder="Enter Unit"
                  bean={houseBillGoods} inputObserver={houseBillGoodsOb} beanIdField={'quantityUnit'} label='Unit' />
              </bs.Col>
            </bs.Row>
            <bs.Row>
              <bs.Col span={6}>
                <input.BBDoubleField bean={houseBillGoods} inputObserver={houseBillGoodsOb} label='Weight' field='weight' disable />
              </bs.Col>
              <bs.Col span={3}>
                <input.BBDoubleField bean={houseBillGoods} inputObserver={houseBillGoodsOb} label='Volume' field='volume' disable />
              </bs.Col>
              <bs.Col span={3}>
                <input.BBStringField bean={houseBillGoods} inputObserver={houseBillGoodsOb} label='Volume Note' field='volumeNote' disable />
              </bs.Col>
            </bs.Row>
            <input.BBStringField bean={houseBillGoods} inputObserver={houseBillGoodsOb} label='Goods Description' field='goodsDescription' disable />
          </bs.Card>
          ...Items
        </div>
        <bs.Toolbar>
          {observer.isNewBean() &&
            <bs.Button laf='primary' onClick={() => this.createTMSHouseBillWithBFSOneData(hbl['hblNo'])}>
              <FeatherIcon.ArrowDownCircle size={12} />
              {'Convert With BFSOne Data'}
            </bs.Button>
          }
          {this.btnSave()}
        </bs.Toolbar>
      </div>
    )
  }
}


