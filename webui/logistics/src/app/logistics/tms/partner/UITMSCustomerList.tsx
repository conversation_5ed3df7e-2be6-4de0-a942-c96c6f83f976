import React from 'react';
import { app, server, grid, bs, entity, sql, util } from '@datatp-ui/lib';
import * as FeatherIcon from 'react-feather'
import { T } from '../backend';
import {
  UITMSPartnerBaseList, UITMSPartnerBaseListPageControl,
  UIPartnerEditor, ConvertBFSOnePartnerBaseEditor, UIPartner,
  UIPartnerProps
} from './UITMSPartnerBaseList';
import { UIJobTrackingProjectRuleConfig } from 'app/logistics/jobtracking/rule/UIJobTrackingProjectRuleConfig';
import { RuleConfigStatus } from 'app/logistics/jobtracking/models';
import { UIMergeTMSPartners, UITMSPartnerAddressList, UITMSPartnerAddressListPlugin, UITMSPartnerList } from './UITMSPartnerList';
import { BBRefJobTrackingProjectRule } from 'app/logistics/jobtracking';
import { TMS_BILL_JOB_TRACKING_COMPANY_CONFIG_CODE } from '../bill/UITMSJobTracking';
import { Role } from '../models';
export class UITMSCustomerListPlugin extends entity.DbEntityListPlugin {
  constructor() {
    super([]);
    this.backend = {
      context: 'company',
      service: 'TMSCustomerService',
      searchMethod: 'searchTMSCustomers',
      changeStorageStateMethod: 'changeTMSCustomerStorageState',
      deleteMethod: 'deleteTMSCustomers'
    }
    this.searchParams = {
      "params": {},
      "filters": [
        ...sql.createSearchFilter()
      ],
      "optionFilters": [
        {
          name: 'dataScope', label: 'Data Scope', type: 'STRING', required: true,
          options: ['Owner', 'Company'],
          optionLabels: [T('Owner'), T('Company')],
          selectOption: 'Company'
        },
        {
          name: 'jobTrackingRule', label: 'Job Tracking Rule', type: 'STRING', required: true, multiple: true,
          options: ['default-rule', 'custom-rule', 'no-rule'],
          optionLabels: [T('Default'), T('Custom'), T('No Rule')],
          selectOptions: ['default-rule', 'custom-rule', 'no-rule'],
        },
        sql.createStorageStateFilter([entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED])
      ],
      maxReturn: 3000,
    }
  }
  loadData(uiList: entity.DbEntityList<any>) {
    this.createBackendSearch(uiList, { 'params': this.searchParams }).call();
  }
}

export class UITMSCustomerListPageControl extends UITMSPartnerBaseListPageControl {

  mergeTMSCustomers() {
    let { context } = this.props;
    let uiRoot = context.uiRoot as entity.DbEntityList;
    const { pageContext } = uiRoot.props;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return <UIMergeTMSCustomers context={context} appContext={appCtx} pageContext={pageCtx} />
    }
    pageContext.createPopupPage('merger-tms-customer', T("Merge"), createAppPage, { size: 'md' })
  }

  syncBFSSales = () => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as entity.DbEntityList;
    const { appContext, plugin } = uiRoot.props;
    let selectRecs = plugin.getListModel().getSelectedRecords();
    let partnerIds = selectRecs.map((rec: any) => rec.tmsPartnerId);
    appContext.createHttpBackendCall('TMSPartnerService', 'syncPartnerPermissionSaleman', { partnerIds: partnerIds })
      .withSuccessData((_data: any) => {
        uiRoot.reloadData();
      })
      .call();
  }


  addButtonToolBars(): Array<any> {
    let { context } = this.props;
    let uiRoot = context.uiRoot as entity.DbEntityList;
    const { appContext, pageContext } = uiRoot.props;
    return [
      <entity.WButtonEntityWrite
        appContext={appContext} pageContext={pageContext} icon={FeatherIcon.GitMerge}
        label={T('Merge')} onClick={() => this.mergeTMSCustomers()} />,
      <bs.Button laf='primary'
        onClick={this.syncBFSSales} key='add-button'>
        <FeatherIcon.RotateCw size={12} />
        {T('Sync BFS Sale Man')}
      </bs.Button>
    ]
  }

  onAdd(): void {
    let { context } = this.props;
    let uiRoot = context.uiRoot as UITMSCustomerList;
    const { pageContext } = uiRoot.props;
    let createContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <ConvertBFSOneCustomerEditor
          appContext={appCtx} pageContext={pageCtx} observer={new entity.ComplexBeanObserver({})}
          onPostCommit={(_bean) => {
            uiRoot.reloadData();
          }} />
      )
    }
    pageContext.createPopupPage('customer', 'New Customer', createContent, { size: 'xl' });
  }

  createHttpBackEndCallSave(records: Array<any>): server.HttpBackendCall {
    let { context } = this.props;
    let uiRoot = context.uiRoot as UITMSCustomerList;
    let { appContext } = uiRoot.props;
    return appContext.createHttpBackendCall('TMSCustomerService', 'saveTMSCustomers', { models: records });
  }
}

export class UITMSCustomerList extends UITMSPartnerBaseList {

  renderListPageControl(context: grid.VGridContext): React.ReactElement {
    return <UITMSCustomerListPageControl context={context} />
  }

  createHTTPBackEndCallFollow(record: any): server.HttpBackendCall | null {
    let { appContext } = this.props;
    return appContext.createHttpBackendCall('TMSPartnerPermissionService', 'addPartnerPermission', { tmsPartnerId: record.tmsPartnerId });
  }


  onLoadPartnerAddress = (customer: any) => {
    let { pageContext } = this.props;
    const createContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UITMSPartnerAddressList style={{ minHeight: 600 }}
          appContext={appCtx} pageContext={pageCtx}
          plugin={new UITMSPartnerAddressListPlugin(customer.tmsPartnerId)} />
      )
    }
    pageContext.createPopupPage('partner-address', T('Partner Address'), createContent, { size: 'flex-lg' })
  }

  addFieldConfigs(): Array<grid.FieldConfig> {
    let thisUI = this;
    let { pageContext } = thisUI.props;
    return [
      {
        name: 'totalAddress', label: T('Address'), width: 80, container: 'fixed-left',
        filterable: true, filterableType: 'string',
        editor: {
          type: 'string',
          onInputChange: this.onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let dRecord = fieldCtx.displayRecord;
            let customer = dRecord.record;
            return (
              <div className={`flex-hbox align-items-center`}>
                {customer.id && pageContext.hasUserWriteCapability() ?
                  <div className='flex-hbox'>
                    <div className='ms-1 me-3' style={{ width: 25 }}>
                      {customer.totalAddress ? customer.totalAddress : ''}
                    </div>
                    <bs.Button laf='link' className='p-0 mx-1' onClick={() => thisUI.onLoadPartnerAddress(customer)}>
                      <FeatherIcon.Edit size={12} className='mx-1 state-modified' />
                    </bs.Button>
                  </div>
                  : <></>
                }
              </div>
            )
          },
        }
      },
      {
        name: 'tmsJobTrackingRuleLabel', label: T('Rule Name'), dataTooltip: true, filterable: true, filterableType: 'options'
      },
      {
        name: 'numberOfBill', label: T('Bill'), container: 'fixed-right', filterable: true, filterableType: 'options', width: 80
      },
      {
        name: 'jobTrackingRule', label: 'Rule', width: 100,
        container: 'fixed-right',
        customRender(_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) {
          let customer = dRecord.record;
          let configStatus = customer['projectRuleConfigStatus'];
          let jobTrackingRuleId = customer['tmsJobTrackingRuleId']
          let onClick = () => {
            if (!jobTrackingRuleId) {
              thisUI.onCreateTMSCustomerRuleFromDefault(customer);
            } else {
              thisUI.onPopupTMSCustomerJobTrackingRule(customer);
            }
          }
          let label = 'View';
          let className = '';
          if (!jobTrackingRuleId) {
            className = 'text-success'
            label = 'Create'
          } else if (!configStatus || configStatus === RuleConfigStatus.NEED_REVIEW) {
            className = 'text-warning'
            label = 'Need Review'
          }
          return (
            <div className='flex-hbox justify-content-center'>
              <bs.Button laf='link' onClick={onClick} className={className}>
                {label}
              </bs.Button>
            </div>
          )
        }
      },
      ...this.createFollowFieldConfigs()
    ]
  }

  onPostCloneJTProjectRule = (customer: any, newJTProjectRule: any, uiSource?: app.AppComponent) => {
    const { appContext } = this.props;
    let params = { customerId: customer.id, jobTrackingProjectRuleId: newJTProjectRule.id }
    appContext.createHttpBackendCall('TMSCustomerService', 'updateTMSCustomerJobTrackingRuleRef', params)
      .withSuccessData((updatedCustomer: any) => {
        uiSource?.props.pageContext.back();
        customer['projectRuleConfigStatus'] = RuleConfigStatus.NEED_REVIEW;
        customer['tmsJobTrackingRuleId'] = updatedCustomer['tmsJobTrackingRuleId'];
        this.forceUpdate();
        this.onPopupTMSCustomerJobTrackingRule(updatedCustomer);
      })
      .withSuccessNotification('success', 'Success')
      .withFailNotification("danger", "Fail!")
      .call();
  }

  onPopupTMSCustomerJobTrackingRule = (customer: any) => {
    let { pageContext } = this.props;
    let jobTrackingRuleId = customer['tmsJobTrackingRuleId']
    let createPopupContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UIJobTrackingProjectRuleConfig appContext={appCtx} pageContext={pageCtx}
          jobTrackingRuleId={jobTrackingRuleId} customJobTrackingLabel={customer.shortName}
          customJobTrackingId={customer.id} customJobTrackingType='tms-bill'
          onPostCloneJTProjectRule={(newJTProjectRule: any, uiSource?: app.AppComponent) => {
            this.onPostCloneJTProjectRule(customer, newJTProjectRule, uiSource);
          }}
          onPostConfirm={() => {
            customer['projectRuleConfigStatus'] = RuleConfigStatus.REVIEWED;
            this.forceUpdate();
          }}
        />
      )
    }
    pageContext.createPopupPage("job-tracking-project-rule-config", T('Project Rule Config'), createPopupContent, { size: 'lg', backdrop: "static" });
  }

  onCreateTMSCustomerRuleFromDefault = (customer: any) => {
    let { appContext } = this.props;
    appContext.createHttpBackendCall('TMSCustomerService', 'createTMSCustomerRuleFromDefault', { customerId: customer.id })
      .withSuccessData((updatedCustomer: any) => {
        customer['tmsJobTrackingRuleId'] = updatedCustomer['tmsJobTrackingRuleId'];
        customer['tmsJobTrackingRuleLabel'] = updatedCustomer['tmsJobTrackingRuleLabel'];
        customer['projectRuleConfigStatus'] = RuleConfigStatus.NEED_REVIEW;
        this.forceUpdate();
        this.onPopupTMSCustomerJobTrackingRule(updatedCustomer);
      })
      .withFailNotification("danger", "Fail!")
      .withSuccessNotification('success', 'Success')
      .call();
  }

  onDefaultSelect(dRecord: grid.DisplayRecord) {
    let { appContext, pageContext } = this.props;
    let customer = dRecord.record;
    appContext.createHttpBackendCall('TMSPartnerService', 'getTMSPartnerById', { id: customer.tmsPartnerId })
      .withSuccessData((data: any) => {
        let createUI = () => {
          return (
            <UITMSCustomer appContext={appContext} pageContext={pageContext}
              observer={new entity.ComplexBeanObserver(data)}
              role={Role.MANAGEMENT_CUSTOMER} refId={customer.id}
              jobTrackingRuleId={customer.tmsJobTrackingRuleId}
              onPostCommit={() => { this.reloadData() }
              }
            />
          );
        }
        this.getVGridContext().getVGrid().addTab(customer.shortName, createUI);
        this.getVGridContext().getVGrid().forceUpdateView();
      })
      .call();
  }
}
export interface UITMSCustomerFormProps extends app.AppComponentProps {
  tmsPartnerId: number,
  onPostCommit: (entity: any) => void;
}
class UITMSCustomerForm extends app.AppComponent<UITMSCustomerFormProps> {
  jobTrackingProject: any;
  observer: entity.BeanObserver = new entity.BeanObserver({});
  viewId = `view-${util.IDTracker.next()}`;

  nextViewId() { this.viewId = `view-${util.IDTracker.next()}`; }

  constructor(props: UITMSCustomerFormProps) {
    super(props);
    let { appContext, tmsPartnerId } = this.props;
    this.markLoading(true);
    let params = {
      companyConfigCode: TMS_BILL_JOB_TRACKING_COMPANY_CONFIG_CODE
    }
    appContext.createHttpBackendCall('TMSCustomerService', 'loadTMSCustomerByPartnerId', { tmsPartnerId: tmsPartnerId })
      .withSuccessData((data: any) => {
        this.observer.setMutableBean(data);
        appContext.createHttpBackendCall('JobTrackingService', 'getJobTrackingProjectByCompanyConfigCode', params)
          .withSuccessData((data: any) => {
            this.jobTrackingProject = data;
            this.markLoading(false);
            this.nextViewId()
            this.forceUpdate();
          })
          .call();
      })
      .call();

  }


  render() {
    const { appContext, pageContext, readOnly, onPostCommit } = this.props;
    const customer = this.observer.getMutableBean();
    const writeCap = pageContext.hasUserWriteCapability() && !readOnly;

    return (
      <div className='flex-vbox'>
        <bs.GreedyScrollable className='flex-vbox'>
          <BBRefJobTrackingProjectRule key={`ref-job-tracking-rule-${this.viewId}`} label={T('Ref Job Tracking Rule')}
            appContext={appContext} pageContext={pageContext} disable={!writeCap && !!this.jobTrackingProject}
            placeholder='Enter Job Tracking Rule' projectId={this.jobTrackingProject ? this.jobTrackingProject?.id : null} customJobTrackingType={'tms-bill'}
            bean={customer} idField={'tmsJobTrackingRuleId'} labelField={'tmsJobTrackingRuleLabel'} />
        </bs.GreedyScrollable>
        <bs.Toolbar className='border' hide={!writeCap || readOnly}>
          <entity.ButtonEntityCommit
            appContext={appContext} pageContext={pageContext} hide={!writeCap || readOnly} observer={this.observer}
            commit={{
              entityLabel: T(`TMS Customer`), context: 'company',
              service: 'TMSCustomerService', commitMethod: 'saveTMSCustomer'
            }}
            onPostCommit={(entity: any) => {
              this.forceUpdate();
              onPostCommit(entity);
            }}
          />
        </bs.Toolbar>
      </div>
    )
  }

}

export class UITMSCustomerEditor extends UIPartnerEditor {
  addTabs(): Array<any> {
    let { appContext, pageContext } = this.props;
    if (!this.tmsPartnerId) {
      return [];
    }
    return [
      <bs.Tab name='customer' label='Customer'>
        <UITMSCustomerForm appContext={appContext} pageContext={pageContext} tmsPartnerId={this.tmsPartnerId} onPostCommit={this.onPostCommit} />
      </bs.Tab>,
    ]
  }
  createPartnerCommit(): entity.CommitConfig {
    return {
      entityLabel: T(`TMS Partner`), context: 'company',
      service: 'TMSCustomerService', commitMethod: 'saveTMSPartner'
    }
  }
}

export class ConvertBFSOneCustomerEditor extends ConvertBFSOnePartnerBaseEditor {
  readOnly: boolean = false;

  createHttpBackEndCallPreview(params: any): server.HttpBackendCall {
    let { appContext } = this.props;
    return appContext.createHttpBackendCall('TMSCustomerService', 'createMapObjectPreview', params)
  }

  onPostPreviewCallBack(data: any): void {
    let { observer } = this.props;
    observer.replaceBeanProperty('partners', data['tmsPartners']);
    observer.replaceBeanProperty('customers', data['tmsCustomers']);
    let partnes: Array<any> = data['tmsPartners'];
    let customers: Array<any> = data['tmsCustomers']
    if (partnes.length > 0 || customers.length > 0) {
      this.readOnly = true;
    }
  }

  createListDuplicated(): JSX.Element {
    let { observer, appContext, pageContext } = this.props;
    let customers: Array<any> = observer.getComplexArrayProperty('customers', []);
    let partners: Array<any> = observer.getComplexArrayProperty('partners', []);
    if (customers.length > 0) {
      return (
        <div >
          <hr className='my-4 text-primary' />
          <h4 className='text-danger'>TMSCustomer Duplicated...</h4>
          <UITMSCustomerList style={{ minHeight: 400 }} appContext={appContext} pageContext={pageContext}
            onPostCommit={this.onPostCommit}
            plugin={new entity.DbEntityListPlugin(customers)} readOnly />
        </div>
      )
    } else if (partners.length > 0) {
      return (
        <div >
          <hr className='my-4 text-primary' />
          <h4 className='text-danger'>TMSPartner Duplicated...</h4>
          <UITMSPartnerList style={{ minHeight: 400 }} appContext={appContext} pageContext={pageContext}
            onPostCommit={this.onPostCommit}
            plugin={new entity.DbEntityListPlugin(partners)} readOnly typePartner="Customer" />
        </div>
      )
    }
    return <></>
  }

  renderEditor(): JSX.Element {
    let { appContext, pageContext, observer } = this.props;
    return (
      <UITMSCustomerEditor key={util.IDTracker.next()}
        appContext={appContext} pageContext={pageContext} observer={observer.createComplexBeanObserver('partner', {})}
        readOnly={this.readOnly}
        onPostCommit={(entity: any, _uiEditor?: bs.BaseComponent) => {
          this.onPostCommit(entity);
        }} />
    )
  }
}

export interface UITMSCustomerProps extends UIPartnerProps {
  jobTrackingRuleId: number
}

export class UITMSCustomer extends UIPartner<UITMSCustomerProps> {
  addTabs(): Array<any> {
    const { appContext, pageContext, observer, jobTrackingRuleId, refId } = this.props;
    const partner = observer.getMutableBean();
    let tabs = [
      <bs.Tab name='address' label='Address'>
        <UITMSPartnerAddressList
          appContext={appContext} pageContext={pageContext}
          plugin={new UITMSPartnerAddressListPlugin(partner.id)} />
      </bs.Tab>
    ];
    if (jobTrackingRuleId) {
      tabs.push(
        <bs.Tab name={'tms-job-tracking-project-rule'} label={T('Job Tracking Rule Config')}>
          <UIJobTrackingProjectRuleConfig appContext={appContext} pageContext={pageContext}
            jobTrackingRuleId={jobTrackingRuleId} customJobTrackingLabel={partner.shortName}
            customJobTrackingId={refId} customJobTrackingType='tms-bill'
            onPostCloneJTProjectRule={this.onPostCloneJTProjectRule} />
        </bs.Tab>
      );
    }
    return tabs;
  }

  createEditor(): JSX.Element {
    const { appContext, pageContext, observer, role, refId } = this.props;
    const partner = observer.getMutableBean();
    const writeCap = pageContext.hasUserWriteCapability();
    return (
      <UITMSCustomerEditor key={`info-${this.viewId}`}
        appContext={appContext} pageContext={pageContext} observer={observer} readOnly={!writeCap}
        onPostCommit={() => {
          this.onPostCommit(partner);
        }} />
    )
  }
}

class UIMergeTMSCustomers extends UIMergeTMSPartners {
  createMergeHttpBackendCall(): server.HttpBackendCall {
    let { appContext, context } = this.props;
    let uiList = context.uiRoot as entity.DbEntityList;;
    const { plugin } = uiList.props;
    let records = plugin.getListModel().getSelectedRecords();
    let deletePartnerIds = [];
    let deleteIds = [];
    let selectRecordId = this.bean['selectOpt']['id'];
    let selectRecordPartnerId = this.bean['selectOpt']['tmsPartnerId'];
    for (let record of records) {
      if (record['id'] !== selectRecordId) {
        if (record['tmsPartnerId'] !== selectRecordPartnerId) {
          deletePartnerIds.push(record['tmsPartnerId']);
        }
        deleteIds.push(record['id']);
      }
    }
    let params = {
      targetId: this.bean['selectOpt'].id,
      customerDelIds: deleteIds,
    }
    return appContext.createHttpBackendCall("TMSCustomerService", "mergeTMSCustomers", params);
  }
}