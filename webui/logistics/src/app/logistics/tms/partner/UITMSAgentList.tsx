import React from 'react';
import { app, server, grid, bs, entity, sql } from '@datatp-ui/lib';
import { T } from '../backend';
import {
  UITMSPartnerBaseList, UITMSPartnerBaseListPageControl,
  UIPartnerEditor, ConvertBFSOnePartnerBaseEditor, UIPartner,
} from './UITMSPartnerBaseList';
import { UITMSPartnerList } from './UITMSPartnerList';
import { Role } from '../models';


export class UITMSAgentListPlugin extends entity.DbEntityListPlugin {
  constructor() {
    super([]);
    this.backend = {
      context: 'company',
      service: 'TMSAgentService',
      searchMethod: 'searchTMSAgents',
      changeStorageStateMethod: 'changeTMSAgentStorageState',
      deleteMethod: 'deleteTMSAgents'
    }
    this.searchParams = {
      "params": {},
      "filters": [
        ...sql.createSearchFilter()
      ],
      "optionFilters": [
        {
          name: 'dataScope', label: 'Data Scope', type: 'STRING', required: true,
          options: ['Owner', 'Company'],
          optionLabels: [T('Owner'), T('Company')],
          selectOption: 'Company'
        },
        sql.createStorageStateFilter([entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED])
      ],
      maxReturn: 3000,
    }
  }
  loadData(uiList: entity.DbEntityList<any>) {
    this.createBackendSearch(uiList, { 'params': this.searchParams }).call();
  }
}

export class UITMSAgentListPageControl extends UITMSPartnerBaseListPageControl {

  onAdd(): void {
    let { context } = this.props;
    let uiRoot = context.uiRoot as UITMSAgentList;
    const { pageContext } = uiRoot.props;
    let createContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <ConvertBFSOneAgentEditor
          appContext={appCtx} pageContext={pageCtx} observer={new entity.ComplexBeanObserver({})}
          onPostCommit={(_bean) => {
            uiRoot.reloadData();
          }} />
      )
    }
    pageContext.createPopupPage('agent', 'New Agent', createContent, { size: 'xl' });
  }

  createHttpBackEndCallSave(records: Array<any>): server.HttpBackendCall {
    let { context } = this.props;
    let uiRoot = context.uiRoot as UITMSAgentList;
    let { appContext } = uiRoot.props;
    return appContext.createHttpBackendCall('TMSAgentService', 'saveTMSAgentModels', { models: records });
  }
}

export class UITMSAgentList extends UITMSPartnerBaseList {

  renderListPageControl(context: grid.VGridContext): React.ReactElement {
    return <UITMSAgentListPageControl context={context} />
  }

  createHTTPBackEndCallFollow(record: any): server.HttpBackendCall | null {
    let { appContext } = this.props;
    return appContext.createHttpBackendCall('TMSPartnerPermissionService', 'addPartnerPermission', { tmsPartnerId: record.tmsPartnerId });
  }

  addFieldConfigs(): Array<grid.FieldConfig> {
    return [
      ...this.createFollowFieldConfigs()
    ]
  }

  onDefaultSelect(dRecord: grid.DisplayRecord) {
    let { appContext, pageContext } = this.props;
    let agent = dRecord.record;
    appContext.createHttpBackendCall('TMSPartnerService', 'getTMSPartnerById', { id: agent.tmsPartnerId })
      .withSuccessData((data: any) => {
        let createUI = () => {
          return (
            <UITMSAgent appContext={appContext} pageContext={pageContext}
              observer={new entity.ComplexBeanObserver(data)}
              role={Role.MANAGEMENT_AGENT} refId={agent.id}
              onPostCommit={() => { this.reloadData() }
              }
            />
          );
        }
        this.getVGridContext().getVGrid().addTab(agent.shortName, createUI);
        this.getVGridContext().getVGrid().forceUpdateView();
      })
      .call();
  }
}
export interface UITMSAgentFormProps extends app.AppComponentProps {
  tmsPartnerId: number,
  onPostCommit: (entity: any) => void;
}

export class UITMSAgentEditor extends UIPartnerEditor {
  createPartnerCommit(): entity.CommitConfig {
    return {
      entityLabel: T(`TMS Partner`), context: 'company',
      service: 'TMSAgentService', commitMethod: 'saveTMSPartner'
    }
  }
}

export class ConvertBFSOneAgentEditor extends ConvertBFSOnePartnerBaseEditor {
  readOnly: boolean = false;

  createHttpBackEndCallPreview(params: any): server.HttpBackendCall {
    let { appContext } = this.props;
    return appContext.createHttpBackendCall('TMSAgentService', 'createMapObjectPreview', params)
  }

  onPostPreviewCallBack(data: any): void {
    let { observer } = this.props;
    observer.replaceBeanProperty('partners', data['tmsPartners']);
    observer.replaceBeanProperty('agents', data['tmsAgents']);
    let partnes: Array<any> = data['tmsPartners'];
    let agents: Array<any> = data['tmsAgents']
    if (partnes.length > 0 || agents.length > 0) {
      this.readOnly = true;
    }
  }

  createListDuplicated(): JSX.Element {
    let { observer, appContext, pageContext } = this.props;
    let agents: Array<any> = observer.getComplexArrayProperty('agents', []);
    let partners: Array<any> = observer.getComplexArrayProperty('partners', []);
    if (agents.length > 0) {
      return (
        <div >
          <hr className='my-4 text-primary' />
          <h4 className='text-danger'>TMSAgent Duplicated...</h4>
          <UITMSAgentList style={{ minHeight: 400 }} appContext={appContext} pageContext={pageContext}
            onPostCommit={this.onPostCommit}
            plugin={new entity.DbEntityListPlugin(agents)} readOnly />
        </div>
      )
    } else if (partners.length > 0) {
      return (
        <div >
          <hr className='my-4 text-primary' />
          <h4 className='text-danger'>TMSPartner Duplicated...</h4>
          <UITMSPartnerList style={{ minHeight: 400 }} appContext={appContext} pageContext={pageContext}
            onPostCommit={this.onPostCommit}
            plugin={new entity.DbEntityListPlugin(partners)} readOnly typePartner="Agent" />
        </div>
      )
    }
    return <></>
  }

  renderEditor(): JSX.Element {
    let { appContext, pageContext, observer } = this.props;
    return (
      <UITMSAgentEditor
        appContext={appContext} pageContext={pageContext} observer={observer.createComplexBeanObserver('partner', {})}
        readOnly={this.readOnly}
        onPostCommit={(_entity: any, _uiEditor?: bs.BaseComponent) => {
          this.onPostCommit(_entity);
        }} />
    )
  }
}

export class UITMSAgent extends UIPartner {

  hasRenderTMSBillList(): boolean {
    return false;
  }

  createEditor(): JSX.Element {
    const { appContext, pageContext, observer, role, refId } = this.props;
    const partner = observer.getMutableBean();
    const writeCap = pageContext.hasUserWriteCapability();
    return (
      <UITMSAgentEditor key={`info-${this.viewId}`}
        appContext={appContext} pageContext={pageContext} observer={observer} readOnly={!writeCap}
        onPostCommit={() => {
          this.onPostCommit(partner);
        }} />
    )
  }
}