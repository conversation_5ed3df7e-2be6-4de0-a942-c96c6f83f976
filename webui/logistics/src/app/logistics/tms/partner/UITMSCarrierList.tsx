import React from 'react';
import { app, server, grid, bs, entity, sql } from '@datatp-ui/lib';
import { T } from '../backend';
import { ConvertBFSOnePartnerBaseEditor, UIPartner, UIPartnerEditor, UITMSPartnerBaseList, UITMSPartnerBaseListPageControl } from './UITMSPartnerBaseList';
import { UITMSPartnerList } from './UITMSPartnerList';
export class UITMSCarrierListPlugin extends entity.DbEntityListPlugin {
  constructor() {
    super([]);
    this.backend = {
      context: 'company',
      service: 'TMSCarrierService',
      searchMethod: 'searchTMSCarriers',
      changeStorageStateMethod: 'changeTMSCarrierStorageState',
      deleteMethod: 'deleteTMSCarriers'
    }
    this.searchParams = {
      "params": {},
      "filters": [
        ...sql.createSearchFilter()
      ],
      "optionFilters": [
        sql.createStorageStateFilter([entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED])
      ],
      maxReturn: 3000,
    }
  }
  loadData(uiList: entity.DbEntityList<any>) {
    this.createBackendSearch(uiList, { 'params': this.searchParams }).call();
  }
}

export class UITMSCarrierListPageControl extends UITMSPartnerBaseListPageControl {
  onAdd(): void {
    let { context } = this.props;
    let uiRoot = context.uiRoot as UITMSCarrierList;
    const { pageContext } = uiRoot.props;
    let createContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <ConvertBFSOneCarrierEditor
          appContext={appCtx} pageContext={pageCtx} observer={new entity.ComplexBeanObserver({})}
          onPostCommit={(bean) => {
            uiRoot.reloadData();
          }} />
      )
    }
    pageContext.createPopupPage('carrier', 'New Carrier', createContent, { size: 'xl' });
  }

  createHttpBackEndCallSave(record: Array<any>): server.HttpBackendCall {
    let { context } = this.props;
    let uiRoot = context.uiRoot as UITMSCarrierList;
    let { appContext } = uiRoot.props;
    return appContext.createHttpBackendCall('TMSCarrierService', 'saveTMSCarrierModels', { models: record });
  }
}

export class UITMSCarrierList extends UITMSPartnerBaseList {
  renderListPageControl(context: grid.VGridContext): React.ReactElement {
    return <UITMSCarrierListPageControl context={context} />
  }

  onDefaultSelect(dRecord: grid.DisplayRecord) {
    let { appContext, pageContext } = this.props;
    let vendor = dRecord.record;
    appContext.createHttpBackendCall('TMSPartnerService', 'getTMSPartnerById', { id: vendor.tmsPartnerId })
      .withSuccessData((data: any) => {
        let createUI = () => {
          return (
            <UITMSCarrier appContext={appContext} pageContext={pageContext}
              refId={vendor.id}
              observer={new entity.ComplexBeanObserver(data)}
              onPostCommit={() => { this.reloadData() }}
            />
          );
        }
        this.getVGridContext().getVGrid().addTab(vendor.shortName, createUI);
        this.getVGridContext().getVGrid().forceUpdateView();
      })
      .call();
  }

  addFieldConfigs(): Array<grid.FieldConfig> {
    return [
      {
        name: 'numberOfBill', label: T('Bill'), container: 'fixed-right', filterable: true, filterableType: 'options'
      },
    ]
  }
}

export class ConvertBFSOneCarrierEditor extends ConvertBFSOnePartnerBaseEditor {
  readOnly: boolean = false;

  createHttpBackEndCallPreview(params: any): server.HttpBackendCall {
    let { appContext } = this.props;
    return appContext.createHttpBackendCall('TMSCarrierService', 'createMapObjectPreview', params)
  }

  onPostPreviewCallBack(data: any): void {
    let { observer } = this.props;
    observer.replaceBeanProperty('partners', data['tmsPartners']);
    observer.replaceBeanProperty('carriers', data['tmsCarriers']);
    let partnes: Array<any> = data['tmsPartners'];
    let carriers: Array<any> = data['tmsCarriers']
    if (partnes.length > 0 || carriers.length > 0) {
      this.readOnly = true;
    }
  }

  createListDuplicated(): JSX.Element {
    let { observer, appContext, pageContext } = this.props;
    let carriers: Array<any> = observer.getComplexArrayProperty('carriers', []);
    let partners: Array<any> = observer.getComplexArrayProperty('partners', []);
    if (carriers.length > 0) {
      return (
        <div >
          <hr className='my-4 text-primary' />
          <h4 className='text-danger'>TMSCarrier Duplicated...</h4>
          <UITMSCarrierList style={{ minHeight: 400 }} appContext={appContext} pageContext={pageContext}
            onPostCommit={this.onPostCommit}
            plugin={new entity.DbEntityListPlugin(carriers)} readOnly />
        </div>
      )
    } else if (partners.length > 0) {
      return (
        <div >
          <hr className='my-4 text-primary' />
          <h4 className='text-danger'>TMSPartner Duplicated...</h4>
          <UITMSPartnerList style={{ minHeight: 400 }} appContext={appContext} pageContext={pageContext}
            onPostCommit={this.onPostCommit}
            plugin={new entity.DbEntityListPlugin(partners)} readOnly typePartner="Carrier" />
        </div>
      )
    }
    return <></>
  }

  renderEditor(): JSX.Element {
    let { appContext, pageContext, observer } = this.props;
    return (
      <UITMSCarrierEditor
        appContext={appContext} pageContext={pageContext} observer={observer.createComplexBeanObserver('partner', {})}
        readOnly={this.readOnly}
        onPostCommit={(_entity: any, _uiEditor?: bs.BaseComponent) => {
          this.onPostCommit(_entity);
        }} />
    )
  }
}

export class UITMSCarrierEditor extends UIPartnerEditor {

  createPartnerCommit(): entity.CommitConfig {
    return {
      entityLabel: T(`TMS Partner`), context: 'company',
      service: 'TMSCarrierService', commitMethod: 'saveTMSPartner'
    }
  }
}

export class UITMSCarrier extends UIPartner {

  createTMSBillFilterName(): 'customerId' | 'carrierId' {
    return 'carrierId';
  }

  createEditor(): JSX.Element {
    const { appContext, pageContext, observer } = this.props;
    const partner = observer.getMutableBean();
    const writeCap = pageContext.hasUserWriteCapability();
    return (
      <UITMSCarrierEditor key={`info-${this.viewId}`}
        appContext={appContext} pageContext={pageContext} observer={observer} readOnly={!writeCap}
        onPostCommit={() => {
          this.onPostCommit(partner);
        }} />
    )
  }

}