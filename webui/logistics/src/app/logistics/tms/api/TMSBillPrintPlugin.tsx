import React, { Component } from 'react';
import { app, bs, component, util, input, server } from '@datatp-ui/lib';
import { module } from '@datatp-ui/erp';

import * as FeatherIcon from 'react-feather';

import { T } from '../backend';

import { UITMSReceiptOfDeliveryPrint } from '../bill/UITMSBillPrint';

import UIApiPlugin = module.security.UIApiPlugin;
import ApiResponse = module.security.ApiResponse;

const SESSION = app.host.DATATP_HOST.session;
let breakPOD = false;

interface WButtonTMSBillPODPrintProps extends app.AppComponentProps {
	initData(): Array<any>;
	dataType: 'id' | 'bill',
	size?: number;
	label?: string;
	laf?: bs.ButtonLaf;
	color?: string;
}
export class WButtonTMSBillPODPrint extends Component<WButtonTMSBillPODPrintProps> {
	onPrintPOD = () => {
		let { appContext, pageContext, initData, dataType } = this.props;
		let data: Array<any> = initData();
		if (dataType == 'bill') {
			let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
				return (
					<TMSBillPODPrintTab appContext={appCtx} pageContext={pageCtx} tmsBills={data} />
				)
			}
			pageContext.createPopupPage('receipt-of-delivery', T(`Receipt Of Delivery (${data.length})`), createAppPage, { size: 'xl' });
		} else {
			const successData = (bills: Array<any>) => {
				let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
					return (
						<TMSBillPODPrintTab appContext={appCtx} pageContext={pageCtx} tmsBills={bills} />
					)
				}
				pageContext.createPopupPage('receipt-of-delivery', T(`Receipt Of Delivery (${data.length})`), createAppPage, { size: 'xl' });
			}

			appContext
				.createHttpBackendCall('TMSBillService', 'findTMSBillByIds', { 'ids': data })
				.withSuccessData(successData)
				.call();
		}
	}

	render(): React.ReactNode {
		let { size, label, laf, color } = this.props;
		return (
			<bs.Button laf={laf ? laf : 'info'} outline className='p-1' onClick={this.onPrintPOD}>
				<FeatherIcon.Printer className={color} size={size ? size : 12} /> {label}
			</bs.Button>
		)
	}
}
interface TMSBillPODPrintTabProps extends app.AppComponentProps {
	tmsBills: Array<any>;
}export class TMSBillPODPrintTab extends Component<TMSBillPODPrintTabProps> {
	bean: any = {
		'breakPOD': breakPOD
	}

	render(): React.ReactNode {
		const CONFIG = app.host.CONFIG
		const serverUrl = CONFIG.getUIServerUrl();
		const url = `${serverUrl}/api/ui`;
		let companyContext = SESSION.getCurrentCompanyContext();
		let companyCode: string = companyContext['companyCode'];

		let { appContext, pageContext, tmsBills } = this.props;
		let ids: Array<any> = [];
		let internalBills: Array<any> = [];
		let externalBills: Array<any> = [];
		let panelConfigs: Array<bs.PanelConfig> = [];
		for (let tmsBill of tmsBills) {
			let tmsBillPrintAuthorizationToken = tmsBill['tmsBillPrintAuthorizationToken'];
			let syncVendorBillAuthorizationToken = tmsBill['syncVendorBillAuthorizationToken'];
			if (tmsBillPrintAuthorizationToken || syncVendorBillAuthorizationToken) {
				externalBills.push(tmsBill);
			} else {
				ids.push(tmsBill['id']);
				internalBills.push(tmsBill);
			}
		}

		if (ids.length == 0 && externalBills.length == 0) {
			return (
				<h4 className='flex-hbox text-center'>
					{T('No printed information available!')}
				</h4>
			)
		}

		if (this.bean['breakPOD']) {
			for (let i = 0; i < internalBills.length; i++) {
				let bill = internalBills[i];
				panelConfigs.push({
					name: `internal-bill-print-pod-${i}`,
					label: `${companyCode.toUpperCase()} ${util.text.formater.truncate(bill.label, 20, true)}`,
					active: i == 0,
					Icon: FeatherIcon.File,
					renderContent: (_ctx: bs.UIContext) => {
						return (
							<UITMSReceiptOfDeliveryPrint key={`internal-bill-print-pod-${i}`}
								appContext={appContext} pageContext={pageContext} billIds={[bill['id']]} />
						)
					}
				})
			}
		} else if (ids.length > 0) {
			panelConfigs.push({
				name: `tms-bill-print-pod`,
				label: `${companyCode.toUpperCase()} Print POD`,
				active: true,
				Icon: FeatherIcon.FileText,
				renderContent: (_ctx: bs.UIContext) => {
					return (
						<UITMSReceiptOfDeliveryPrint
							appContext={appContext} pageContext={pageContext} billIds={ids} />
					)
				}
			})
		}

		let tokenMap: Record<string, any[]> = {};
		for (let i = 0; i < externalBills.length; i++) {
			let bill = externalBills[i];
			let token = bill['tmsBillPrintAuthorizationToken'];
			if (tokenMap[`${token}`]) {
				tokenMap[`${token}`].push(bill);
			} else {
				tokenMap[`${token}`] = [bill];
			}
		}
		let i = 0;
		for (let [token, bills] of Object.entries(tokenMap)) {
			let label: any;
			let refIds: any[] = [];
			for (let bill of bills) {
				let office = bill['office'] ? bill['office'] : '';
				if (!label) {
					label = `${office.toUpperCase()} ${util.text.formater.truncate(bill.label, 20, true)}`;
				} else {
					label += `${util.text.formater.truncate(bill.label, 20, true)}`;
				}
				refIds.push(bill['refId']);
			}
			panelConfigs.push({
				'name': `external-bill-print-pod-${i}`,
				'label': label,
				'active': panelConfigs.length == 0,
				'Icon': FeatherIcon.Link2,
				renderContent: (_ctx: bs.UIContext) => {
					return (
						<PrintPodResourceApi key={`pod-${util.IDTracker.next()}`} appContext={appContext} pageContext={pageContext} token={token} ids={refIds} />
					)
				}
			});
			i++;
		}

		return (
			<div className='flex-vbox' key={`tms-bill-print-pod-${util.IDTracker.next()}`}>
				<bs.DefaultTabPane
					config={{
						tabs: panelConfigs
					}}
				/>
				<bs.Toolbar hide={ids.length <= 1}>
					<input.BBCheckboxField label={T('Break POD')} bean={this.bean} field='breakPOD' value={false}
						onInputChange={(_bean: any, _field: string, _oldVal: any, newVal: any) => {
							breakPOD = newVal;
							this.forceUpdate()
						}} />
				</bs.Toolbar>
			</div>
		)
	}
}

interface TMSBillPODPrintProps extends module.misc.UIPrintViewProps {
	storeId: any;
}
class TMSBillPODPrint extends module.misc.UIPrintView<TMSBillPODPrintProps> {
	createPrintData() { return null; }

	createLoadPrintUrl(_config: component.PrintConfig, format: string) { return ''; }

	createPrintConfigs(): component.PrintConfig[] {
		let reports: Array<component.PrintConfig> = [
			{ name: 'receipt-of-delivery', label: 'Receipt Of Delivery' },
		];
		return reports;
	}

	loadPrint = (_ctx: bs.WidgetContext, _config: component.PrintConfig, _format: string, reportCallback: component.PrintCallback) => {
		let { storeId } = this.props;
		let viewReportUrl = app.host.CONFIG.createServerLink(`/get/public/store/${storeId}`);
		reportCallback('success', viewReportUrl, "Load Report Success");
	}

	render() {
		if (!this.state.init) {
			return (<div className='flex-vbox' ref={(ele) => { this.divElement = ele }}>Loading...</div>)
		}
		const { width } = this.state;
		let html = (
			<div className='view view-report flex-vbox' style={{ overflow: 'auto', width: width, height: '100vh' }}>
				<component.UIPrint
					context={this.widgetContext}
					reports={this.createPrintConfigs()} defaultPrint={''}
					loadPrint={this.loadPrint} />
			</div>
		)
		return html;
	}
}

export class UITMSBillPrintApiPlugin extends UIApiPlugin {
	constructor() {
		super('resource:tms-bill-print', 'TMS Bill Print Api Plugin');
	}

	render(appContext: app.AppContext, pageContext: app.PageContext, resp: ApiResponse) {
		let html = <TMSBillPODPrint appContext={appContext} pageContext={pageContext} storeId={resp.result.storeId} />
		return html;
	}
}

interface PrintPodResourceApiProps extends app.AppComponentProps {
	token: any;
	ids: any[];
}
class PrintPodResourceApi extends app.AppComponent<PrintPodResourceApiProps> {
	storeId: any;
	constructor(props: PrintPodResourceApiProps) {
		super(props);
		this.markLoading(true);
		this.forceUpdate();
	}
	componentDidMount(): void {
		const { token, ids } = this.props;
		const CONFIG = app.host.CONFIG
		let restClient = new server.rest.RestClient(CONFIG.getServerUrl(), CONFIG.getApiUrl(), 'datatp');
		let callback = (resp: server.BackendResponse) => {
			this.storeId = resp.data.result.storeId;
			this.markLoading(false);
			this.forceUpdate();
		}
		let params = {
			'tmsBillIds': ids
		}
		let header = {
			'DataTP-Authorization': token
		}
		restClient.postWithHeader(`/resource`, header, params, callback, () => {
			this.markLoading(false);
			this.forceUpdate();
		})
	}
	render(): React.ReactNode {
		const { appContext, pageContext } = this.props;
		if (this.isLoading()) return <div>Loading...</div>
		return <TMSBillPODPrint appContext={appContext} pageContext={pageContext} storeId={this.storeId} />
	}
}
