import React from 'react';
import * as FeatherIcon from 'react-feather';
import { calendar as cal, bs, calendar, app, sql, util, entity } from '@datatp-ui/lib';
import { DbEntityTaskAssetListPlugin } from './AssetTaskCalendarPlugin';
import { T } from '../Dependency';
import { UITaskableAssetEditor } from './TaskableAsset';
import { AssetTaskStatusUtils, AssetTaskTypeUtils } from '../models';

import Config = calendar.CalendarConfig
import Context = calendar.CalendarContext

const SESSION = app.host.DATATP_HOST.session;

type OfficeBranch = {
  code: string;
  label: string;
  id?: number
}

export const OFFICE_BRANCH: Record<string, OfficeBranch> = {
  beehcm: { code: 'beehcm', label: 'BEE HCM', id: 17 },
  beedad: { code: 'beedad', label: 'BEE DAD', id: 18 },
  beehph: { code: 'beehph', label: 'BEE HPH', id: 8 },
  beehan: { code: 'beehan', label: 'BEE HAN', id: 16 },
}

export interface WCompanySelectorProps extends app.AppComponentProps {
  onModify?: entity.EntityOnModify;
  initCompany?: { code: string, label: string };
}

interface WCompanySelectorState {
  company: { code: string, label: string };
}
export class WCompanySelector extends app.AppComponent<WCompanySelectorProps, WCompanySelectorState> {

  constructor(props: WCompanySelectorProps) {
    super(props);
    const { initCompany } = this.props;
    if (initCompany) {
      this.state = { company: initCompany }
    } else {
      let companyContext = SESSION.getCurrentCompanyContext();
      this.state = {
        company: {
          code: companyContext['companyCode'],
          label: companyContext['companyLabel']
        }
      }
    }
  }

  onInputChange = (companyCode: string, _companyLabel: string) => {
    const { onModify } = this.props;
    const company: any = {
      code: companyCode,
      label: _companyLabel
    }
    this.setState({ company: company })
    if (onModify) onModify(company, 'companyCode', null, companyCode);
  }

  render(): React.ReactNode {
    const { company } = this.state;

    let companyButtons: any[] = [];
    for (let selKey in OFFICE_BRANCH) {
      let ctx = OFFICE_BRANCH[selKey];

      companyButtons.push(
        <bs.Button laf="secondary" outline size="sm" className="me-1 py-2 w-100 text-start"
          onClick={() => this.onInputChange(ctx.code, ctx.label)}>
          <FeatherIcon.Home size={14} className="me-1" />
          {ctx.label}
        </bs.Button>
      )
    }

    return (
      <bs.Popover className="flex-vbox flex-grow-0" placement="bottom-start" closeOnTrigger=".btn" >
        <bs.PopoverToggle laf="secondary" outline className="me-1 p-1" style={{ color: '#6c757d' }}>
          <FeatherIcon.Home size={14} className="me-1" />
          {`${company.label} Office`}
          <FeatherIcon.ChevronDown size={14} className="ms-1" />
        </bs.PopoverToggle>
        <bs.PopoverContent>
          <div className='flex-vbox align-items-center gap-1' style={{ width: '180px' }}>
            {companyButtons}
          </div>
        </bs.PopoverContent>
      </bs.Popover>
    )
  }
}

interface CalendarWeekCellProps extends app.AppComponentProps {
  context: cal.CalendarContext;
  config: cal.CalendarConfig;
  date: Date;
}

class CalendarWeekCell extends app.AppComponent<CalendarWeekCellProps> {

  render(): React.ReactElement {
    const { context, date } = this.props;
    let dayRecords = context.getDateRecordMap().getByDay(date);
    if (!dayRecords) return this.renderCell();
    let records = dayRecords.getAllRecords();
    if (records.length == 0) return this.renderCell();
    // Sort records by dueDate
    records.sort((a: any, b: any) => {
      const dueDateA = a.dueDate ? new Date(a.dueDate).getTime() : 0;
      const dueDateB = b.dueDate ? new Date(b.dueDate).getTime() : 0;
      return dueDateA - dueDateB;
    });

    const events = records.map((record: any) => {
      return this.renderWeekCellTask(context, record);
    });

    return this.renderCell(events);
  }

  onViewTask = (context: calendar.CalendarContext, task: any) => {
    let uiRoot = context.uiRoot as UIAssetTaskCalendar;
    let { appContext, pageContext } = uiRoot.props;
    let { entityRefId } = task;

    appContext.createHttpBackendCall('AssetService', 'getTaskableAsset', { id: entityRefId })
      .withSuccessData((data: any) => {
        let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          let observer = new entity.BeanObserver(data);
          const onPostCommit = (_entity: any, _uiEditor?: bs.BaseComponent) => {
            pageCtx.back();
            uiRoot.onLoadData();
          }
          return (
            <UITaskableAssetEditor
              appContext={appCtx} pageContext={pageCtx} observer={observer} onPostCommit={onPostCommit} assetCompanyCode={uiRoot.filterParams.company.code} />
          );
        }
        const taskTypeInfo = AssetTaskTypeUtils.getTypeInfo(data['taskType']);
        pageContext.createPopupPage(`asset-task-${util.IDTracker.next()}`, `${T(`${data['taskType'] ? taskTypeInfo.label : 'Task'}`)}: ${task.label}`, createAppPage, { size: 'flex-lg', backdrop: 'static' });
      })
      .call();
  }

  formatDateTime(dateStr: string) {
    let date = 'N/A';
    let time = 'N/A';
    if (dateStr && dateStr.length > 1) {
      date = dateStr.substring(0, 10);
      time = dateStr.substring(11, 16);
    }
    return {
      date: date,
      time: time
    };
  }

  renderWeekCellTask = (context: calendar.CalendarContext, task: any): any => {
    let entityRefData: any = {
      taskType: 'OTHER',
      status: 'IN_PROGRESS',
      referenceId: null,
      referenceType: null
    }
    if (task.entityRefData != null) {
      entityRefData = JSON.parse(task.entityRefData)
    }

    const taskTypeInfo = AssetTaskTypeUtils.getTypeInfo(entityRefData['taskType']);
    const Icon = taskTypeInfo.icon;
    const statusInfo = AssetTaskStatusUtils.getStatusInfo(entityRefData['status']);
    const StatusIcon = statusInfo.icon;

    let html = (
      <div className='flex-vbox rounded-2 bg-white border p-1 mb-1' style={{ cursor: 'pointer', transition: 'all 0.2s ease-in-out' }}
        onClick={(e) => {
          e.stopPropagation();
          this.onViewTask(context, task);
        }}>
        <div className={`ps-2 py-1 align-items-center text-center fw-bold border-bottom`} style={{ fontSize: '0.9rem' }}>
          <span className='fw-bold text-secondary'>{task.label}</span>
        </div>

        <div className="d-flex align-items-start p-1 cursor-pointer">
          <div className="flex-grow-1">
            <div className={`fw-bold mb-1 text-center text-${taskTypeInfo.color}`} style={{ fontSize: '0.9rem' }}>
              <Icon size={16} className={`me-2`} /> {entityRefData.assetLabel ? entityRefData.assetLabel : 'N/A'}
            </div>

            {taskTypeInfo.value == 'Car' &&
              <div className="d-flex align-items-center justify-content-center small fw-bold text-gray-600 mt-1">
                <span>{entityRefData.startPlace} - {entityRefData.endPlace}</span>
              </div>
            }

            <div className="d-flex align-items-center justify-content-center small text-danger fw-bold">
              <FeatherIcon.Clock size={12} className="me-1" />
              <span>{this.formatDateTime(entityRefData.fromTime).time}</span>
              <span className='m-1'>-</span>
              <span>{this.formatDateTime(entityRefData.toTime).time}</span>

              <span className={`ms-2 px-2 py-1 align-items-center bg-${statusInfo.color}-subtle text-${statusInfo.color}`}
                style={{ fontSize: '0.75rem', fontWeight: 500, borderRadius: '50%' }} title={statusInfo.label}>
                <StatusIcon size={12} />
              </span>
            </div>

            <div className="d-flex align-items-center justify-content-center small fw-bold text-muted mt-1">
              <span>{entityRefData.picLabel}</span>
            </div>
          </div>
        </div>
      </div>
    );
    return html;
  }

  renderCell = (events?: any): React.ReactElement => {
    let { date } = this.props;
    const selectedDate = date;
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return (
      <div className="flex-vbox justify-content-between p-1 h-100" style={{
        overflow: 'hidden' // Prevent content from overflowing
      }}>
        <div className='flex-vbox'>
          <bs.GreedyScrollable className='flex-vbox flex-grow-1'>
            {events ? events : null}
          </bs.GreedyScrollable>
        </div>
        {
          selectedDate >= today ?
            <div className='flex-vbox flex-grow-0 justify-content-end align-items-end mt-2'>
              {this.renderAddButton(date)}
            </div>
            : null
        }
      </div>
    );
  }

  onAddTask = (context: calendar.CalendarContext, date: Date) => {
    let uiRoot = context.uiRoot as UIAssetTaskCalendar;
    let { appContext, pageContext } = uiRoot.props;
    let company: OfficeBranch = uiRoot.filterParams.company;

    let currentDate = new Date(date);
    currentDate.setHours(currentDate.getHours() + 1);
    currentDate.setMinutes(0, 0, 0);
    let fromTime = util.TimeUtil.javaCompactDateTimeFormat(currentDate);
    currentDate.setHours(currentDate.getHours() + 1);
    let toTime = util.TimeUtil.javaCompactDateTimeFormat(currentDate);

    let tAsset = {
      usingDate: util.TimeUtil.javaCompactDateTimeFormat(new Date(date)),
      fromTime: fromTime,
      toTime: toTime,
    }

    appContext.createHttpBackendCall('AssetService', 'initTaskableAsset', { tAsset: tAsset })
      .withSuccessData((data: any) => {
        let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          let observer = new entity.BeanObserver(data);

          const onPostCommit = (_entity: any, _uiEditor?: bs.BaseComponent) => {
            pageCtx.back();
            uiRoot.onLoadData();
          }
          return (
            <UITaskableAssetEditor assetCompanyCode={company.code}
              appContext={appCtx} pageContext={pageCtx} observer={observer} onPostCommit={onPostCommit} />
          )
        }
        pageContext.createPopupPage("new-asset", T("New Car/ Meeting Room"), createAppPage, { size: 'flex-lg', backdrop: 'static' });
      })
      .call();
  }


  renderAddButton = (date: Date): React.ReactElement => {
    const { context } = this.props;
    return (
      <bs.Button laf="primary" outline className="px-2 py-1"
        onClick={() => { this.onAddTask(context, date); }} >
        <FeatherIcon.Plus size={14} />
        <span className="ms-1 d-none d-md-inline">Car/ Meeting Room</span>
      </bs.Button>
    );
  }
}


export class UIAssetTaskCalendarManager extends calendar.UICalendarManager {
  render() {
    return (
      <div className="ui-calendar-manager flex-vbox">
        {this.renderCalendarView()}
      </div>
    );
  }
}

interface FilterParams {
  taskType?: string | null;
  company: { code: string, label: string };
}

export interface UIAssetTaskCalendarProps extends app.AppComponentProps { }
export class UIAssetTaskCalendar extends app.AppComponent<UIAssetTaskCalendarProps> {
  filterParams: FilterParams;
  config: Config;
  records: Array<any> = [];

  constructor(props: any) {
    super(props);

    let companyContext = SESSION.getCurrentCompanyContext();
    let companyCode: string = companyContext['companyCode'];
    let companyLabel: string = companyContext['companyLabel'];
    let currentOffice: OfficeBranch = { code: companyCode, label: companyLabel };

    if (OFFICE_BRANCH[companyCode]) {
      this.filterParams = {
        company: currentOffice
      }
    } else {
      let office: OfficeBranch = OFFICE_BRANCH['beehph'];
      this.filterParams = {
        company: { code: office.code, label: office.label }
      }
    }

    this.config = {
      view: calendar.CalendarViewType.Week,
      cellAction: calendar.CellAction.None,
      cellMode: calendar.HourCellMode.DataOnly,
      selectedDate: new Date(),
      year: {},
      record: { dateField: 'dueDate', dateFieldLabel: T('Due Date'), dateFieldSelect: true },

      month: {},

      week: {
        renderCell(ctx: cal.CalendarContext, config: cal.CalendarConfig, date: Date) {
          let uiList = ctx.uiRoot as UIAssetTaskCalendar;
          const { appContext, pageContext } = uiList.props;
          return (<CalendarWeekCell appContext={appContext} pageContext={pageContext} context={ctx} config={config} date={date} />);
        }
      },

      day: {},
    }
  }

  componentDidMount(): void {
    this.onLoadData();
  }

  onLoadData() {
    this.markLoading(true);
    const { appContext } = this.props;
    let plugin = new DbEntityTaskAssetListPlugin();
    let searchParams: sql.SqlSearchParams = plugin.getSearchParams();
    searchParams.params = {
      ...searchParams.params || {},
      taskType: this.filterParams.taskType,
      companyCode: this.filterParams.company.code,
    }

    let userParams = { params: searchParams }
    appContext.createHttpBackendCall('AssetService', 'searchAssetTasks', userParams)
      .withSuccessData((data: any) => {
        this.records = data;
        this.markLoading(false);
        this.forceUpdate();
      })
      .call();
  }


  onChangeMonth(direction: 'prev' | 'next') {
    const currentDate = this.config.selectedDate;
    const newDate = new Date(currentDate);

    if (direction === 'prev') {
      newDate.setMonth(currentDate.getMonth() - 1);
    } else {
      newDate.setMonth(currentDate.getMonth() + 1);
    }

    this.config.selectedDate = newDate;
    this.onLoadData();
    // this.forceUpdate();
  }

  onChangePeriod(direction: 'prev' | 'next') {
    const currentDate = this.config.selectedDate;
    const newDate = new Date(currentDate);
    if (direction === 'prev') {
      newDate.setDate(currentDate.getDate() - 7);
    } else {
      newDate.setDate(currentDate.getDate() + 7);
    }
    this.config.selectedDate = newDate;
    this.onLoadData();
  }

  onToday() {
    this.config.selectedDate = new Date();
    this.onLoadData();
    // this.forceUpdate()
  }

  renderToolbar() {
    const { appContext, pageContext } = this.props;
    const currentDate = this.config.selectedDate;
    const monthYear = currentDate.toLocaleString('default', { day: '2-digit', month: 'long', year: 'numeric' });

    let popoverLaf: 'primary' | 'secondary' | 'warning' | 'success' = 'secondary';
    if (this.filterParams.taskType === 'Car') popoverLaf = 'primary';
    if (this.filterParams.taskType === 'MeetingRoom') popoverLaf = 'success';
    if (this.filterParams.taskType === 'Other') popoverLaf = 'warning';

    let todayLaf: 'primary' | 'secondary' = 'secondary';
    if (this.config.selectedDate.getDate() === new Date().getDate()) todayLaf = 'primary';

    return (
      <div className="flex-hbox flex-grow-0 align-items-center px-2 py-1" >
        {/* Left section - Today button */}
        <div className="d-flex align-items-center">
          <bs.Button laf={todayLaf} outline className="px-2 py-1"
            onClick={() => this.onToday()} title="Go to Today">
            Today
          </bs.Button>

          <bs.Popover placement="bottom" offset={[0, 5]} minWidth={200}>
            <bs.PopoverToggle laf={popoverLaf}
              className='mx-1 p-1 flex-hbox' outline>
              <FeatherIcon.Filter size={12} /> Filter
            </bs.PopoverToggle>
            <bs.PopoverContent className="flex-vbox-grow-0">
              <UICalendarFilter appContext={appContext} pageContext={pageContext} uiRoot={this} />
            </bs.PopoverContent>
          </bs.Popover>
        </div>

        {/* Center section - Month navigation and display */}
        <div className="flex-grow-1 d-flex align-items-center justify-content-center">
          <div className="d-flex align-items-center gap-2">
            <bs.Button
              laf="secondary"
              className="bg-white rounded-circle d-flex align-items-center justify-content-center p-1 text-secondary"
              style={{ width: '24px', height: '24px' }}
              hint={`Previous Week`}
              onClick={() => this.onChangePeriod('prev')}>
              <FeatherIcon.ChevronLeft size={24} />
            </bs.Button>

            <h5 className="mb-0 fw-semibold px-2" style={{ minWidth: '150px', textAlign: 'center' }}>
              {monthYear}
            </h5>

            <bs.Button
              laf="secondary"
              className="bg-white rounded-circle d-flex align-items-center justify-content-center p-1 text-secondary"
              style={{ width: '24px', height: '24px' }}
              hint={"Next Week"}
              onClick={() => this.onChangePeriod('next')}>
              <FeatherIcon.ChevronRight size={24} />
            </bs.Button>
          </div>
        </div>

        {/* Right section - View options */}
        <div className="d-flex align-items-center">
          <WCompanySelector appContext={appContext} pageContext={pageContext}
            initCompany={this.filterParams.company}
            onModify={(bean: any, _field: string, _oldVal: any, _newVal: any) => {
              this.filterParams.company = bean;
              this.onLoadData();
            }} />
        </div>
      </div>
    );
  }

  render() {
    const { } = this.props;
    let dayRecordMap: calendar.DateRecordMap = new calendar.DateRecordMap(this.records);
    dayRecordMap.dateRecordMap = {};

    for (let rec of this.records) {
      if (!rec['entityRefData']) continue;
      let entityRefData = JSON.parse(rec.entityRefData)
      if (!entityRefData['usingDate']) continue;
      let usingDate: Date = util.TimeUtil.parseCompactDateTimeFormat(entityRefData['usingDate']);
      dayRecordMap._addRecord(rec, usingDate);
    }

    let context = new Context(this, dayRecordMap);
    return (
      <div className="flex-vbox bg-white">
        {this.renderToolbar()}
        <div className='flex-vbox' key={util.IDTracker.next()}>
          <UIAssetTaskCalendarManager context={context} config={this.config} />
        </div>
      </div>
    )
  }
}

interface UICalendarFilterProps extends app.AppComponentProps {
  uiRoot: UIAssetTaskCalendar;
}

export class UICalendarFilter extends app.AppComponent<UICalendarFilterProps> {
  popoverId: string = 'calendar-filter-' + util.IDTracker.next();
  isFiltering: boolean = false;

  render() {
    let { uiRoot } = this.props;

    return (
      <div className="flex-vbox">
        {/* All Tasks Checkbox */}
        <div className="form-check d-flex align-items-center" style={{ minWidth: '140px' }}
          onClick={() => {
            uiRoot.filterParams.taskType = null;
            uiRoot.onLoadData();
          }}>
          <input className="form-check-input me-2" type="checkbox"
            checked={!uiRoot.filterParams.taskType}
            style={{
              width: '16px',
              height: '16px',
              cursor: 'pointer',
              backgroundColor: !uiRoot.filterParams.taskType ? '#525b75' : '#fff',
              borderColor: '#525b75'
            }}
          />
          <label className="form-check-label" style={{ cursor: 'pointer' }}>
            All Tasks
          </label>
        </div>

        {/* Car Tasks Checkbox */}
        <div className="form-check d-flex align-items-center" style={{ minWidth: '140px' }}
          onClick={() => {
            if (uiRoot.filterParams.taskType === 'Car') uiRoot.filterParams.taskType = null;
            else uiRoot.filterParams.taskType = 'Car';
            uiRoot.onLoadData();
          }}>
          <input
            className="form-check-input me-2"
            type="checkbox"
            checked={uiRoot.filterParams.taskType === 'Car'}
            style={{
              width: '16px',
              height: '16px',
              cursor: 'pointer',
              backgroundColor: uiRoot.filterParams.taskType === 'Car' ? '#3874ff' : '#fff',
              borderColor: '#3874ff'
            }}
          />
          <label className="form-check-label" style={{ cursor: 'pointer' }}>
            Car Schedule
          </label>
        </div>

        {/* Meeting/Training Tasks Checkbox */}
        <div className="form-check d-flex align-items-center" style={{ minWidth: '140px' }}
          onClick={() => {
            if (uiRoot.filterParams.taskType === 'MeetingRoom') uiRoot.filterParams.taskType = null;
            else uiRoot.filterParams.taskType = 'MeetingRoom';
            uiRoot.onLoadData();
          }}>
          <input className="form-check-input me-2" type="checkbox"
            checked={uiRoot.filterParams.taskType === 'MeetingRoom'}
            style={{
              width: '16px',
              height: '16px',
              cursor: 'pointer',
              backgroundColor: uiRoot.filterParams.taskType === 'MeetingRoom' ? '#25b003' : '#fff',
              borderColor: '#25b003'
            }}
          />
          <label className="form-check-label" style={{ cursor: 'pointer' }}>
            Meeting/ Training
          </label>
        </div>

        {/* Other Tasks Checkbox */}
        <div className="form-check d-flex align-items-center" style={{ minWidth: '140px' }}
          onClick={() => {
            if (uiRoot.filterParams.taskType === 'Other') uiRoot.filterParams.taskType = null;
            else uiRoot.filterParams.taskType = 'Other';
            uiRoot.onLoadData();
          }}
        >
          <input className="form-check-input me-2" type="checkbox"
            checked={uiRoot.filterParams.taskType === 'Other'}
            style={{
              width: '16px',
              height: '16px',
              cursor: 'pointer',
              backgroundColor: uiRoot.filterParams.taskType === 'Other' ? '#f4b400' : '#fff',
              borderColor: '#f4b400'
            }}
          />
          <label className="form-check-label" style={{ cursor: 'pointer' }}>
            Other
          </label>
        </div>
      </div>
    );
  }
}
