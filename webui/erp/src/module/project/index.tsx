import { ProjectPluginManager } from './ExternalApi'
import { TodoProjectPlugin } from './plugin'

export * as plugin from './plugin'
export * as task from './task'
export * as workflow from './workflow'

export * from './plugin/ProjectPlugin'
export * from './ExternalApi'
export * from './ProjectContext'
export * from './UIProject'
export * from './UIProjectList'

export { UIProjectListPage } from './UIProjectList'

export { init } from './init'

ProjectPluginManager.register(new TodoProjectPlugin());
