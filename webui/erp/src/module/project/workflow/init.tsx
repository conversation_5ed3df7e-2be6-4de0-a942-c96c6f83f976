import React from "react";
import * as icon from 'react-feather';
import { app } from '@datatp-ui/lib';

import space = app.space;
import { UITaskRequestList, UITaskRequestListPlugin } from "./UITaskRequestList";
import { UIApiPluginManager } from "module/security";
import { UIApiPluginTaskRequest } from "./TaskRequestApiPlugin";


const SESSION = app.host.DATATP_HOST.session;

class WorkflowSpacePlugin extends space.SpacePlugin {
  constructor() {
    super('company/workflow', 'Workflow Navigation');
  }

  override createCompanyScreens(): space.ScreenConfig[] {
    return [
      {
        id: "company-workflow-task-requests", label: "Project/ Task list", icon: icon.Trello,
        checkPermission: {
          feature: { module: 'project', name: 'project' },
          requiredCapability: app.READ,
        },
        renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (<UITaskRequestList appContext={appCtx} pageContext={pageCtx} plugin={new UITaskRequestListPlugin('Company')} />)
        }
      }
    ];
  }

  override createUserScreens(): space.ScreenConfig[] {
    const permisson: any = SESSION.getAccountAcl().getAppPermission('project', 'project');
    let space: 'User' | 'Company' | 'System' = 'User'
    if (permisson) {
      if (permisson.dataScope === 'Company') {
        space = 'Company';
      } else if (permisson.dataScope === 'All') {
        space = 'System';
      }
    }

    return [
      {
        id: "user-workflow-task-requests", label: "Project/ Task list", icon: icon.Trello,
        checkPermission: {
          feature: { module: 'project', name: 'project' },
          requiredCapability: app.READ,
        },
        renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (<UITaskRequestList appContext={appCtx} pageContext={pageCtx} plugin={new UITaskRequestListPlugin(space)} />)
        },
        screens: []
      }
    ];
  }
}

export function init() {
  space.SpacePluginManager.register(new WorkflowSpacePlugin());
  UIApiPluginManager.register(new UIApiPluginTaskRequest());
}

