import { entity } from '@datatp-ui/lib';

function createConfig(comapnyIdField: string, companyLabelField: string, beanRefLabelField?: 'code' | 'label') {

  let config: entity.BBRefEntityPluginConfig = {
    backend: {
      context: 'company',
      service: 'CompanyService',
      searchMethod: 'searchCompanies',
      loadMethod: 'getCompany',
    },
    bean: {
      idField: comapnyIdField,
      labelField: companyLabelField
    },
    refEntity: {
      idField: 'id',
      labelField: beanRefLabelField ? beanRefLabelField : 'label',
      labelFunc: (opt: any) => {
        return `${opt['label']}(${opt['code']})`
      },

      vgridRecordConfig: {
        fields: [
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          entity.DbEntityListConfigTool.FIELD_ON_SELECT('label', 'Label', 350),
          { name: 'code', label: 'Code', width: 150 },
          { name: 'fullName', label: 'Full Name', width: 350 },
        ]
      }
    },
  };
  return config;
}
interface BBRefCompanyProps extends entity.BBRefEntityProps {
  companyIdField: string;
  companyLabelField: string;
  beanRefLabelField?: 'code' | 'label';
}
export class BBRefCompany extends entity.BBRefEntity<BBRefCompanyProps> {
  createPlugin() {
    let { companyIdField, companyLabelField, beanRefLabelField } = this.props
    let config = createConfig(companyIdField, companyLabelField, beanRefLabelField);
    return new entity.BBRefEntityPlugin(config);
  }
}
