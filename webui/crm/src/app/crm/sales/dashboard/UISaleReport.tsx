import React from "react";
import * as FeatherIcon from 'react-feather'
import { module } from '@datatp-ui/erp';
import { util, grid, entity, sql } from '@datatp-ui/lib';
import { T } from "../backend";

export class UISaleReportPlugin extends entity.DbEntityListPlugin {
  constructor(space: 'User' | 'Company' | 'System') {
    super([]);

    this.backend = {
      context: 'company',
      service: 'PerformanceReportService',
      searchMethod: 'saleAccountReport',
    }

    const today = new Date();
    let issuedDateRange = new util.TimeRange();
    issuedDateRange.fromSetDate(today).fromSubtract(0, "month").fromStartOf("month"); // -3 months
    issuedDateRange.toSetDate(today).fromSubtract(0, "month").toEndOf("month");

    this.searchParams = {
      params: {
        "space": space,
      },
      filters: [
        ...sql.createSearchFilter()
      ],
      rangeFilters: [
        ...sql.createDateTimeFilter("reportDate", T("Report Date"), issuedDateRange),
      ],
      optionFilters: [
        {
          "name": "purpose", "label": "Imp/ Exp", "type": "STRING", "required": true,
          "options": ["", "IMPORT", "EXPORT"],
          "optionLabels": ["All", "Import", "Export"],
          "selectOption": ""
        },
        {
          "name": "companyCode", "label": "Company", "type": "STRING", "required": true,
          "options": ["", "beehph", "beehan", "beehcm", 'beedad', "bee"],
          "optionLabels": ["All", "Bee HPH", "Bee HAN", "Bee HCM", "Bee DAD", "Bee Corp"],
          multiple: true,
          selectOptions: ["beehph"]
        },
      ],
      maxReturn: 5000
    }
  }

  loadData(uiList: entity.DbEntityList<any>): void {
    this.createBackendSearch(uiList, { params: this.searchParams }).call();
  }

}


export interface UISaleReportProps extends entity.DbEntityListProps { }
export class UISaleReport extends entity.DbEntityList<UISaleReportProps> {

  createVGridConfig(): grid.VGridConfig {
    const { } = this.props;

    let config: grid.VGridConfig = {
      record: {
        dataCellHeight: 30,
        fields: [
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          { name: 'companyCode', label: T(`Company`), width: 100 },
          {
            name: "saleManLabel", label: T('Saleman.'), width: 250, cssClass: 'pe-1', filterable: true,
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let uiList = ctx.uiRoot as UISaleReport
              const { appContext, pageContext } = uiList.props;
              let val = record[_field.name] || 'N/A';
              return (
                <div className='flex-hbox justify-content-center align-items-center' >
                  <module.account.WAvatars className='px-2'
                    appContext={appContext} pageContext={pageContext} avatarIds={[record['saleAccountId']]}
                    avatarIdType='AccountId' width={20} height={20} borderRadius={10} />
                  <div className="flex-hbox">{val}</div>
                </div>
              )
            }
          },
          {
            name: 'inquiryCount', label: T(`Total Inquiry`), width: 150, sortable: true,
            editor: { type: 'number' }
          },
          {
            name: 'quotationCount', label: T(`Total Quotation`), width: 150, sortable: true,
            editor: { type: 'number' }
          },
          {
            name: 'bookingCount', label: T(`Total Booking`), width: 150, sortable: true,
            editor: { type: 'number' }
          },
          {
            name: 'totalSearchCount', label: T(`Total Search Count`), width: 180, sortable: true,
            editor: { type: 'number' }
          },
          { name: 'searchSource', label: T(`From Location`), width: 250, filterable: true },
          { name: 'searchDestination', label: T(`To Location`), width: 250, filterable: true },
          {
            name: 'topRouteSearchCount', label: T(`Top Route Search Count`), width: 200,
            editor: { type: 'number' }
          },
        ],
        fieldGroups: {
          'most-searched-route': {
            label: "Most Searched Route",
            fields: ['searchSource', 'searchDestination', 'topRouteSearchCount']
          }
        }
      },
      toolbar: {
        filters: entity.DbEntityListConfigTool.TOOLBAR_FILTERS(true),
        actions: [
          {
            name: "export-xlsx", label: 'Export Xlsx',
            createComponent: function (ctx: grid.VGridContext) {
              let uiRoot = ctx.uiRoot as any;
              const { appContext, pageContext } = uiRoot.props;
              return (<entity.XlsxExportButton appContext={appContext} pageContext={pageContext} context={ctx}
                options={{ fileName: `sale_analysis.xlsx`, modelName: 'report' }}
              />)
            }
          },
        ]
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          }
        }
      },
    }
    return config;
  }




}

