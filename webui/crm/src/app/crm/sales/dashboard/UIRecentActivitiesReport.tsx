import React from "react"; import * as FeatherIcon from 'react-feather';
import { bs, app, util, server } from '@datatp-ui/lib';
import { ActivityItem } from "../report/UIRecentActivities";
import { UISaleDailyTaskListPlugin } from "../report/UITaskCalendarList";

export interface WSaleActivitiesReportProps extends app.AppComponentProps {
  space: 'User' | 'Company' | 'System';
}
export class WSaleActivitiesReport extends app.AppComponent<WSaleActivitiesReportProps> {
  activeFilter: string | undefined;
  records: Array<any> = []

  constructor(props: WSaleActivitiesReportProps) {
    super(props);
  }

  componentDidMount(): void {
    this.markLoading(true);
    const { space } = this.props;
    let plugin = new UISaleDailyTaskListPlugin(space, new Date());
    plugin.loadDataCalendar(this, (response: server.BackendResponse) => {
      const records: Array<any> = response.data || [];
      this.records = records
        .filter(record => ['COMPLETED', 'BLOCKED'].includes(record.status))
        .sort((a, b) => {
          const timeA = util.TimeUtil.parseCompactDateTimeFormat(a.dueDate).getTime();
          const timeB = util.TimeUtil.parseCompactDateTimeFormat(b.dueDate).getTime();
          return timeB - timeA; // Sắp xếp giảm dần (mới nhất lên đầu)
        })
        .slice(0, 100);
      this.markLoading(false);
      this.forceUpdate();
    });
  }

  onViewAllTasks = () => {

  }

  render(): React.ReactNode {
    const { appContext, pageContext } = this.props;

    return (
      <div className="flex-vbox mx-2 p-2 bg-white rounded-md w-100 h-100">

        <div className="flex-hbox flex-grow-0 align-items-center justify-content-between px-2 py-1 border-bottom">
          <h5 style={{ color: '#6c757d' }}><FeatherIcon.Activity className="me-2" size={18} />Recent Activities</h5>

          <div className="flex-hbox justify-content-end align-items-center flex-grow-1">

            <bs.Button laf="secondary" outline size="sm" className="me-1 p-1" style={{ color: '#6c757d' }}
              onClick={() => { }}>
              <FeatherIcon.Download size={14} className="me-1" />
              Export
            </bs.Button>

            <bs.Button laf="secondary" outline size="sm" className="p-1" style={{ color: '#6c757d' }}
              onClick={() => { }}>
              View all
              <FeatherIcon.ExternalLink size={12} className="ms-2" />
            </bs.Button>
          </div>

        </div>

        <div className="d-flex flex-grow-0 flex-wrap gap-1 mb-1 border-bottom py-2">
          {/* All Tasks Checkbox */}
          <div className="form-check d-flex align-items-center" style={{ minWidth: '110px' }}>
            <input className="form-check-input me-2" type="checkbox" checked={false}
              onChange={() => { }}
              style={{
                width: '16px',
                height: '16px',
                cursor: 'pointer',
                backgroundColor: !this.activeFilter ? '#4285f4' : '#fff',
                borderColor: '#4285f4'
              }}
            />
            <label className="form-check-label" style={{ cursor: 'pointer' }}>
              All Tasks
            </label>
          </div>

          {/* Inquiry Requests Checkbox */}
          <div className="form-check d-flex align-items-center" style={{ minWidth: '110px' }}>
            <input className="form-check-input me-2" type="checkbox" checked={this.activeFilter === 'MEETING'}
              onChange={() => {
                this.activeFilter = 'MEETING'
              }}
              style={{
                width: '16px',
                height: '16px',
                cursor: 'pointer',
                backgroundColor: this.activeFilter === 'MEETING' ? '#0f9d58' : '#fff',
                borderColor: '#0f9d58'
              }}
            />
            <label className="form-check-label" style={{ cursor: 'pointer' }}>
              Meeting Note
            </label>
          </div>

          {/* Call Tasks Checkbox */}
          <div className="form-check d-flex align-items-center" style={{ minWidth: '110px' }}>
            <input
              className="form-check-input me-2"
              type="checkbox"
              checked={this.activeFilter === 'LEADS'}
              onChange={() => {
                this.activeFilter = 'LEADS'
              }}
              style={{
                width: '16px',
                height: '16px',
                cursor: 'pointer',
                backgroundColor: this.activeFilter === 'LEADS' ? '#db4437' : '#fff',
                borderColor: '#db4437'
              }}
            />
            <label className="form-check-label" style={{ cursor: 'pointer' }}>
              Leads
            </label>
          </div>

          {/* Meet Customer Tasks Checkbox */}
          <div className="form-check d-flex align-items-center" style={{ minWidth: '110px' }}>
            <input className="form-check-input me-2" type="checkbox" checked={this.activeFilter === 'CUSTOMERS'}
              onChange={() => {
                this.activeFilter = 'CUSTOMERS'
              }}
              style={{
                width: '16px',
                height: '16px',
                cursor: 'pointer',
                backgroundColor: this.activeFilter === 'CUSTOMERS' ? '#f4b400' : '#fff',
                borderColor: '#f4b400'
              }}
            />
            <label className="form-check-label" style={{ cursor: 'pointer' }}>
              Customers
            </label>
          </div>
        </div>

        <div className="flex-vbox" key={util.IDTracker.next()}>
          <bs.GreedyScrollable>
            {this.records.map((task, index) => (
              <ActivityItem appContext={appContext} pageContext={pageContext} key={index} task={task} />
            ))}
          </bs.GreedyScrollable>
        </div>

      </div>
    )
  }
}