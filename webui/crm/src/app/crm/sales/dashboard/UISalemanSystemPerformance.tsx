import React from "react";
import * as FeatherIcon from 'react-feather';
import { bs, app, util, sql, entity } from '@datatp-ui/lib';
import { GridConfig, ResponsiveGrid, GridColumn } from "../../common/ResponsiveGrid";
import { WQuickTimeRangeSelector } from "../../common/UIDashboardUtility";
import { UISaleDailyTaskList, UISaleDailyTaskListPlugin } from "../report/UITaskCalendarList";
import { SaleTaskType } from "../backend";
import { UICustomerLeadList, UICustomerLeadPlugin } from "../leads/UICustomerLeadList";
import { LeadStatus } from "../leads/LeadUtils";
import { PricingRequestStatus, UIInquiryRequestList, UIInquiryRequestReportPlugin } from "../../price";

const SESSION = app.host.DATATP_HOST.session;

interface ReportFilter {
  dateFilter: { fromValue: string, toValue: string, label: string };
  company: { code: string, label: string };
}

export interface UISalemanSystemPerformanceProps extends app.AppComponentProps {
  space: 'User' | 'Company' | 'System';
}
export class UISalemanSystemPerformance extends app.AppComponent<UISalemanSystemPerformanceProps> {
  reportFilter: ReportFilter;
  records: Array<any> = [];

  constructor(props: UISalemanSystemPerformanceProps) {
    super(props);

    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    let dateFilter = new util.TimeRange();
    dateFilter.fromSetDate(firstDayOfMonth);
    dateFilter.toSetDate(lastDayOfMonth);

    let companyContext = SESSION.getCurrentCompanyContext();
    let companyCode: string = companyContext['companyCode'];
    let companyLabel: string = companyContext['companyLabel'];

    this.reportFilter = {
      company: { code: companyCode, label: companyLabel },
      dateFilter: { fromValue: dateFilter.fromFormat(), toValue: dateFilter.toFormat(), label: 'This Month' },
    }
    this.loadData();
  }

  componentDidMount(): void {
    this.loadData();
  }

  loadData(): void {
    const { appContext } = this.props;
    const { dateFilter } = this.reportFilter;

    if ((dateFilter.fromValue || '').trim()) {
    }

    if ((dateFilter.toValue || '').trim()) {
    }

    let fromDate: Date = util.TimeUtil.parseCompactDateTimeFormat(dateFilter.fromValue);
    let toDate: Date = util.TimeUtil.parseCompactDateTimeFormat(dateFilter.toValue);

    let searchParams: sql.SqlSearchParams = {
      params: {},
      filters: [...sql.createSearchFilter()],
      rangeFilters: [
        ...sql.createDateTimeFilter("reportDate", ("Report Date"), new util.TimeRange(fromDate, toDate)),
      ],
      maxReturn: 100000
    }

    this.markLoading(true);
    appContext.createHttpBackendCall('PerformanceReportService', 'salemanSystemPerformanceReport', { params: searchParams })
      .withSuccessData((records: Array<any>) => {
        for (let rec of records) {
          rec['employeeLabel'] = util.text.formater.uiTruncate(rec.employeeLabel || 'N/A', 280, true);
        }
        this.records = records;
        this.markLoading(false);
        this.forceUpdate();
      })
      .call()
  }

  onModify = (bean: any, _field: string, _oldVal: any, newVal: any) => {
    // this.viewId = util.IDTracker.next();
    bean[_field] = newVal;
    this.forceUpdate();
    this.loadData();
  }

  onExport = (records: Array<any>, grid: GridConfig, fileName: string) => {
    const { appContext } = this.props;
    const currentDate = util.TimeUtil.toCompactDateFormat(new Date());
    let fields: entity.Field[] = grid.columns.map(column => ({
      name: column.field,
      label: column.label
    }));
    const exportModel: entity.DataListExportModel = {
      modelName: fileName,
      fileName: `${fileName}_${currentDate}.xlsx`,
      fields: fields,
      fieldGroups: [],
      records: records,
    };

    appContext.createHttpBackendCall('DataMappingService', 'xlsxPrivateExport', { model: exportModel })
      .withSuccessData((response: any) => {
        let storeInfo = response;
        if (storeInfo.type == 'ir.actions.act_url') {
          entity.StoreInfo.download(storeInfo.url);
        } else if (storeInfo.privateMode) {
          entity.StoreInfo.privateDownload(storeInfo);
        } else {
          entity.StoreInfo.download(storeInfo.url);
        }
      })
      .call();
  }

  onViewSalesDailyActivity(salemanAccountId: number, employeeLabel: string) {
    let { pageContext, space } = this.props;
    let { dateFilter } = this.reportFilter
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      let plugin = new UISaleDailyTaskListPlugin(space);
      plugin.withSalemanAccountId(salemanAccountId);
      plugin.withDueDate(dateFilter.fromValue, dateFilter.toValue);
      return (
        <UISaleDailyTaskList appContext={appCtx} pageContext={pageCtx} plugin={plugin} space={space} />
      );
    }
    let popupId = `view-sale-daily-${util.IDTracker.next()}`;
    let pupupLabel: string = `Sales Daily Activities - ${employeeLabel}`;
    pageContext.createPopupPage(popupId, pupupLabel, createAppPage, { size: 'xl', backdrop: 'static' });
  }

  onViewMeetCustomerSalesDaily(salemanAccountId: number, employeeLabel: string) {
    let { pageContext, space } = this.props;
    let { dateFilter } = this.reportFilter;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      let plugin = new UISaleDailyTaskListPlugin(space);
      plugin.withSalemanAccountId(salemanAccountId);
      plugin.withDueDate(dateFilter.fromValue, dateFilter.toValue);
      plugin.withTaskType(SaleTaskType.MEET_CUSTOMER);
      return (
        <UISaleDailyTaskList appContext={appCtx} pageContext={pageCtx} plugin={plugin} space={space} />
      );
    }
    let popupId = `view-meet-sale-daily-${util.IDTracker.next()}`;
    let pupupLabel: string = `Sales Daily Activities - ${employeeLabel}`;
    pageContext.createPopupPage(popupId, pupupLabel, createAppPage, { size: 'xl', backdrop: 'static' });
  }

  onViewLeads(salemanAccountId: number, employeeLabel: string) {
    let { pageContext, space } = this.props;
    let { dateFilter } = this.reportFilter;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      let plugin = new UICustomerLeadPlugin(space);
      plugin.withSalemanAccountId(salemanAccountId);
      plugin.withDateFilter('date', dateFilter.fromValue, dateFilter.toValue);
      return (
        <UICustomerLeadList appContext={appCtx} pageContext={pageCtx} plugin={plugin} />
      );
    }
    let popupId = `view-leads-${util.IDTracker.next()}`;
    let pupupLabel: string = `Leads - ${employeeLabel}`;
    pageContext.createPopupPage(popupId, pupupLabel, createAppPage, { size: 'xl', backdrop: 'static' });
  }

  onViewCustomer(salemanAccountId: number, employeeLabel: string) {
    let { pageContext, space } = this.props;
    let { dateFilter } = this.reportFilter;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      let plugin = new UICustomerLeadPlugin(space);
      plugin.withSalemanAccountId(salemanAccountId);
      plugin.withStatus(LeadStatus.CONVERTED);
      plugin.withDateFilter('date', dateFilter.fromValue, dateFilter.toValue);
      return (
        <UICustomerLeadList appContext={appCtx} pageContext={pageCtx} plugin={plugin} />
      );
    }
    let popupId = `view-customer-${util.IDTracker.next()}`;
    let pupupLabel: string = `Customer - ${employeeLabel}`;
    pageContext.createPopupPage(popupId, pupupLabel, createAppPage, { size: 'xl', backdrop: 'static' });
  }

  onExpand = () => {
    let { pageContext } = this.props;
    let createAppPage = (_appCtx: app.AppContext, _pageCtx: app.PageContext) => {
      return (<ResponsiveGrid config={this.buildGridConfig()} data={this.records} />)
    }
    let popupId = `view-expand-saleman-performance-${util.IDTracker.next()}`;
    let pupupLabel: string = `Salesman Activity Tracker`;
    pageContext.createPopupPage(popupId, pupupLabel, createAppPage, { size: 'xl', backdrop: 'static' });
  }

  onViewRequestsPricing(salemanAccountId: number, employeeLabel: string) {
    let { pageContext, space } = this.props;
    let { dateFilter } = this.reportFilter;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      let plugin = new UIInquiryRequestReportPlugin('Company');
      plugin.withSalemanAccountId(salemanAccountId);
      plugin.withDateFilter('requestDate', dateFilter.fromValue, dateFilter.toValue);
      return (
        <UIInquiryRequestList appContext={appCtx} pageContext={pageCtx} plugin={plugin} space={space} />
      );
    }
    let popupId = `inquiry-requests-${util.IDTracker.next()}`;
    let pupupLabel: string = `Inquiry Requests - ${employeeLabel}`;
    pageContext.createPopupPage(popupId, pupupLabel, createAppPage, { size: 'xl', backdrop: 'static' });
  }

  onViewNoResponseRequests(salemanAccountId: number, employeeLabel: string) {
    let { pageContext } = this.props;
    let { dateFilter } = this.reportFilter;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      let plugin = new UIInquiryRequestReportPlugin('Company');
      plugin.withSalemanAccountId(salemanAccountId);
      plugin.withDateFilter('requestDate', dateFilter.fromValue, dateFilter.toValue);
      plugin.withStatus([PricingRequestStatus.NO_RESPONSE, PricingRequestStatus.DONE])
      return (
        <UIInquiryRequestList appContext={appCtx} pageContext={pageCtx} plugin={plugin} space="Company" />
      );
    }
    let popupId = `no-response-requests-${util.IDTracker.next()}`;
    let pupupLabel: string = `No Response Requests - ${employeeLabel}`;
    pageContext.createPopupPage(popupId, pupupLabel, createAppPage, { size: 'xl', backdrop: 'static' });
  }

  buildGridConfig() {
    let thisUI = this;

    const renderButtonEventHighlight = (record: any, label: string, onClick: () => void) => {
      const isEmpty = !label || label === '0';
      const borderClass = isEmpty ? 'border-warning' : 'border-light';
      const textClass = isEmpty ? 'text-warning fw-bold' : '';

      const inProgressTasks = record['inProgressTasks'] || 0;

      let displayVal = `${inProgressTasks}/ ${label}`;

      return (
        <div className={`flex-hbox align-items-center justify-content-start border ${borderClass} mx-0 text-start`} style={{ width: 95 }}>
          <bs.Button laf='secondary' outline className={`border-0 px-2 w-100 m-0 text-start fw-normal rounded-0 fs-9 ${textClass}`}
            onClick={onClick} >
            {isEmpty ? '0' : displayVal}
          </bs.Button>
        </div>
      )
    }

    const renderButtonEvent = (column: GridColumn, label: string, onClick: () => void) => {
      const width = column.width || 100;
      return (
        <div className='flex-hbox align-items-center justify-content-start border border-light mx-0 text-start' style={{ width: width }}>
          <bs.Button laf='secondary' outline className='border-0 px-2 w-100 m-0 text-start fw-normal rounded-0 fs-9' onClick={onClick} >
            {label}
          </bs.Button>
        </div>
      )
    }

    const config: GridConfig = {
      header: {
        height: 20,
        cssClass: 'flex-hbox-grow-0 border-bottom bg-body-highlight py-1 px-1 fw-bold fs-9 me-1'
      },
      row: {
        height: 30,
      },
      showHeader: true,
      showBorder: true,
      columns: [
        { field: 'employeeLabel', label: 'Saleman' },
        {
          field: 'companyBranch', label: 'Branch',
          cellRenderer(value: any, _record: any, _column: GridColumn) {
            return renderButtonEvent(_column, value, () => { })
          },
        },
        {
          field: 'totalTasks', label: 'Total Tasks',
          cellRenderer(value: any, record: any, _column: GridColumn) {
            return renderButtonEventHighlight(record, value, () => thisUI.onViewSalesDailyActivity(record['employeeAccountId'], record['employeeLabel']))
          },
        },
        {
          field: 'meetCustomerTasks', label: 'Meet Customer',
          cellRenderer(value: any, record: any, column: GridColumn) {
            return renderButtonEvent(column, value, () => thisUI.onViewMeetCustomerSalesDaily(record['employeeAccountId'], record['employeeLabel']))
          },
        },
        {
          field: 'newLeadCount', label: 'New Leads',
          cellRenderer(value: any, record: any, column: GridColumn) {
            return renderButtonEvent(column, value, () => thisUI.onViewLeads(record['employeeAccountId'], record['employeeLabel']))
          },
        },
        {
          field: 'newCustomerCount', label: 'New Customer',
          cellRenderer(value: any, record: any, column: GridColumn) {
            return renderButtonEvent(column, value, () => thisUI.onViewCustomer(record['employeeAccountId'], record['employeeLabel']))
          },
        },
        {
          field: 'totalRequestsPricing', label: 'Inquiry',
          cellRenderer(value: any, record: any, column: GridColumn) {
            return renderButtonEvent(column, value, () => thisUI.onViewRequestsPricing(record['employeeAccountId'], record['employeeLabel']))
          },
        },
        {
          field: 'quotationCount', label: 'Quotation',
          cellRenderer(value: any, _record: any, _column: GridColumn) {
            return renderButtonEvent(_column, value, () => { })
          },
        },
        {
          field: 'bookingCount', label: 'Booking',
          cellRenderer(value: any, _record: any, _column: GridColumn) {
            return renderButtonEvent(_column, value, () => { })
          },
        },
        {
          field: 'noResponseRequests', label: 'No Resp Req.',
          cellRenderer(value: any, record: any, column: GridColumn) {
            return renderButtonEvent(column, value, () => thisUI.onViewNoResponseRequests(record['employeeAccountId'], record['employeeLabel']))
          },
        },
        {
          field: 'overdueRequestCount', label: 'Overdue Req.',
          cellRenderer(value: any, _record: any, _column: GridColumn) {
            return renderButtonEvent(_column, value, () => { })
          },
        },
      ],
      widthConfig: {
        totalWidth: 1500,
        minColumnWidth: 100,
        ratios: [2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
      }
    };

    return config;
  }

  render(): React.ReactNode {
    const { appContext, pageContext } = this.props;
    const { dateFilter } = this.reportFilter;

    const lightColor = '#D6EFD8';
    const borderColor = '#80AF81';

    const hoverStyle: any = {
      boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
      transform: 'translateY(-2px)',
      backgroundColor: lightColor
    };

    return (
      <div className="flex-vbox mx-2 py-2 bg-white rounded-md w-100 h-100 mb-1">

        <div className="d-flex flex-column flex-md-row gap-2 p-1">

          <div className="bg-white border rounded-3 flex-vbox justify-content-center align-items-center"
            style={{
              minWidth: 900,
              borderColor: borderColor,
              transition: 'all 0.3s ease',
              marginBottom: '10px'
            }}
            onMouseEnter={(e) => Object.assign(e.currentTarget.style, hoverStyle)}
            onMouseLeave={(e) => {
              e.currentTarget.style.boxShadow = 'none';
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.backgroundColor = '#fff';
            }}>
            <div className="flex-vbox mx-2 p-2 bg-white rounded-md w-100 h-100">
              <div className="flex-hbox flex-grow-0 align-items-center justify-content-between px-2 py-1">
                <h5 style={{ color: '#6c757d' }}><FeatherIcon.TrendingUp className="me-2" size={18} />Salesman Activity Tracker</h5>

                <div className="flex-hbox justify-content-end align-items-center flex-grow-1">

                  <WQuickTimeRangeSelector appContext={appContext} pageContext={pageContext}
                    initBean={dateFilter}
                    onModify={(bean: any, _field: string, _oldVal: any, _newVal: any) => {
                      this.reportFilter.dateFilter = bean;
                      this.loadData();
                    }} />

                  <bs.Button laf="secondary" outline size="sm" className="me-1 p-1" style={{ color: '#6c757d' }}
                    onClick={() => { this.onExport(this.records, this.buildGridConfig(), 'Salesman_Activity_Tracker') }}>
                    <FeatherIcon.Download size={14} className="me-1" />
                    Export
                  </bs.Button>

                  <bs.Button laf="secondary" outline size="sm" className="me-1 p-1" style={{ color: '#6c757d' }}
                    onClick={this.onExpand}>
                    <FeatherIcon.Maximize2 size={14} className="me-1" />
                    Expand
                  </bs.Button>

                </div>
              </div>

              <div className="flex-vbox" style={{ maxHeight: 400, minHeight: 400 }}>
                <ResponsiveGrid config={this.buildGridConfig()} data={this.records} />
              </div>

            </div>
          </div>
        </div>

      </div>
    )

  }
}
