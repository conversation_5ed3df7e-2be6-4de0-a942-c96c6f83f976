import React from "react";

import * as FeatherIcon from 'react-feather';
import { bs, app, util, entity, server, sql } from '@datatp-ui/lib';
import { WKeyAccountReportTypeSelector, WQuickTimeRangeSelector } from "../../common/UIDashboardUtility";
import {
  SalemanReportBean,
  UIPerformanceGridReport,
  UIKeyAccountFormReport
} from "../partners/report/UISalePerformanceFormReport";
import { UIVolumeSalemanKeyAccountReportList, UIVolumeSalemanKeyAccountReportPlugin } from "../partners/report/UIVolumeSalemanKeyAccountReportList";

const SESSION = app.host.DATATP_HOST.session;

const USD_CURR_FORMAT = (val: any) => util.text.formater.currency(val, 2);

interface ReportFilter {
  dateFilter: { fromValue: string, toValue: string, label: string };
  company: { code: string, label: string };
}

export interface UIKeyAccountPerformanceProps extends app.AppComponentProps {
  space: 'User' | 'Company' | 'System';
  hiddenHeader?: boolean;
  reportBean?: SalemanReportBean;
}
export class UIKeyAccountPerformance extends app.AppComponent<UIKeyAccountPerformanceProps> {
  reportFilter: ReportFilter;
  viewId: number = util.IDTracker.next();
  rawData: Array<any> = [];
  volumePerformance: any = {};

  constructor(props: UIKeyAccountPerformanceProps) {
    super(props);
    const { reportBean } = this.props;

    let companyContext = SESSION.getCurrentCompanyContext();
    let companyCode: string = companyContext['companyCode'];
    let companyLabel: string = companyContext['companyLabel'];

    if (reportBean && Object.keys(reportBean).length > 0) {
      this.volumePerformance = reportBean.volumePerformance || {};
      this.reportFilter = {
        company: { code: companyCode, label: companyLabel },
        dateFilter: { fromValue: reportBean.reportedDateFrom, toValue: reportBean.reportedDateTo, label: 'Custom' },
      }
    } else {
      const today = new Date();
      const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
      const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

      let dateFilter = new util.TimeRange();
      dateFilter.fromSetDate(firstDayOfMonth);
      dateFilter.toSetDate(lastDayOfMonth);
      this.reportFilter = {
        company: { code: companyCode, label: companyLabel },
        dateFilter: { fromValue: dateFilter.fromFormat(), toValue: dateFilter.toFormat(), label: 'This Month' },
      }
    }
  }

  componentDidMount(): void {
    if (!this.volumePerformance || Object.keys(this.volumePerformance).length === 0) {
      this.loadData();
    }
  }

  rawDataProcessor(type: 'BD' | 'SALES', records: Array<any> = []): any {

    const airImpHolder: Array<any> = []
    const airExpHolder: Array<any> = []
    const fclImpHolder: Array<any> = []
    const fclExpHolder: Array<any> = []
    const lclImpHolder: Array<any> = []
    const lclExpHolder: Array<any> = []
    const otherHolder: Array<any> = []

    for (let rec of records) {
      const mapRec: any = { ...rec };

      if (rec.shipmentType === 'NOMINATED' && type === 'BD') {
        mapRec.keyAccountCode = rec.agentCode;
        mapRec.keyAccountName = (rec.agentName || 'N/A');
      } else {
        mapRec.keyAccountCode = rec.customerCode;
        mapRec.keyAccountName = (rec.customerName || 'N/A')
      }

      if (mapRec['typeOfService'] === 'AirExpTransactions') {
        mapRec.volume = mapRec.hawbCw;
        airExpHolder.push(mapRec);
      } else if (mapRec['typeOfService'] === 'AirImpTransactions') {
        mapRec.volume = mapRec.hawbCw;
        airImpHolder.push(mapRec);
      } else if (mapRec['typeOfService'] === 'SeaExpTransactions_FCL') {
        // Count TEUs based on containerSize
        const containerSize = mapRec.containerSize || '';
        // Updated regex to capture all container groups globally
        const teusMatches = containerSize.matchAll(/(\d+)[xX](\d+)/g);
        let totalTeus = 0;

        for (const match of teusMatches) {
          const containers = parseInt(match[1]);
          const size = parseInt(match[2]);
          totalTeus += containers * (size === 20 ? 1 : 2); // 20' = 1 TEU, 40' = 2 TEU
        }

        if (totalTeus > 0) {
          mapRec.volume = totalTeus;
        }
        fclExpHolder.push(mapRec)
      } else if (mapRec['typeOfService'] === 'SeaImpTransactions_FCL') {
        // Count TEUs based on containerSize
        const containerSize = mapRec.containerSize || '';
        const teusMatch = containerSize.match(/(\d+)[xX](\d+)/);
        if (teusMatch) {
          const containers = parseInt(teusMatch[1]);
          const size = parseInt(teusMatch[2]);
          mapRec.volume = containers * (size === 20 ? 1 : 2); // 20' = 1 TEU, 40' = 2 TEU
        }
        fclImpHolder.push(mapRec)
      } else if (mapRec['typeOfService'] === 'SeaExpTransactions_CSL' || mapRec['typeOfService'] === 'SeaExpTransactions_LCL') {
        mapRec.volume = mapRec.hawbCbm;
        lclExpHolder.push(mapRec)
      } else if (mapRec['typeOfService'] === 'SeaImpTransactions_CSL' || mapRec['typeOfService'] === 'SeaImpTransactions_LCL') {
        mapRec.volume = mapRec.hawbCbm;
        lclImpHolder.push(mapRec)
      } else {
        mapRec.volume = mapRec.customsNumberCount || 0
        otherHolder.push(mapRec)
      }
    }

    function computeShipmentVolume(records: Array<any>) {
      // Bước 1: Group theo transactionId
      const groupedByTransaction = records.reduce((acc, record) => {
        const transactionId = record.transactionId;

        if (!acc[transactionId]) {
          acc[transactionId] = {
            transactionId,
            keyAccountName: record.keyAccountName, // Lấy giá trị đầu tiên
            volume: 0,
            revenue: 0,
            cost: 0
          };
        }

        // Sum các giá trị số
        const revenue = (record.subtotalSellingVnd + record.subtotalOtherDebitVnd) / (record.exchangeRateUsd || 1) || 0;
        const cost = (record.subtotalBuyingVnd + record.subtotalOtherCreditVnd) / (record.exchangeRateUsd || 1) || 0;

        acc[transactionId].revenue += (revenue || 0);
        acc[transactionId].cost += (cost || 0);
        acc[transactionId].volume += (record.volume || 0);

        return acc;
      }, {});

      // Bước 2: Group theo keyAccountName từ kết quả đã group theo transactionId
      const groupedByKeyAccount = Object.values(groupedByTransaction).reduce<Record<string, {
        keyAccountName: string;
        shipmentCount: number;
        volume: number;
        revenue: number;
        cost: number;
      }>>((acc, transaction: {
        keyAccountName: string;
        volume: number;
        revenue: number;
        cost: number;
      }) => {
        const keyAccountName = transaction.keyAccountName;

        if (!acc[keyAccountName]) {
          acc[keyAccountName] = {
            keyAccountName,
            shipmentCount: 0,
            volume: 0,
            revenue: 0,
            cost: 0
          };
        }

        // Tăng shipmentCount lên 1 cho mỗi transaction
        acc[keyAccountName].shipmentCount += 1;
        acc[keyAccountName].revenue += transaction.revenue;
        acc[keyAccountName].cost += transaction.cost;
        acc[keyAccountName].volume += transaction.volume;

        return acc;
      }, {});

      let holder: Array<any> = Object.values(groupedByKeyAccount).map((group: any) => ({
        keyAccountName: group.keyAccountName,
        shipmentCount: group.shipmentCount,
        volume: group.volume,
        profit: group.revenue - group.cost,
        revenue: group.revenue
      })).sort((a, b) => b.profit - a.profit);

      if (holder.length === 0) return holder;

      let sumAll: { shipmentCount: number, volume: number, profit: number; revenue: number } = holder.reduce(
        (acc, curr) => {
          acc.shipmentCount += curr.shipmentCount;
          acc.volume += curr.volume;
          acc.profit += curr.profit;
          acc.revenue += curr.revenue;
          return acc;
        },
        { shipmentCount: 0, volume: 0, profit: 0, revenue: 0 }
      );

      holder.push({
        keyAccountName: 'Total',
        shipmentCount: sumAll.shipmentCount,
        volume: sumAll.volume,
        profit: sumAll.profit,
        revenue: sumAll.revenue
      })

      for (let rec of holder) {
        rec['volume'] = USD_CURR_FORMAT(rec.volume)
        rec['profit'] = USD_CURR_FORMAT(rec.profit)
        rec['revenue'] = USD_CURR_FORMAT(rec.revenue)
      }
      return holder;
    }

    let volumePerformance: any = {
      airImp: computeShipmentVolume(airImpHolder),
      airExp: computeShipmentVolume(airExpHolder),
      fclImp: computeShipmentVolume(fclImpHolder),
      fclExp: computeShipmentVolume(fclExpHolder),
      lclExp: computeShipmentVolume(lclExpHolder),
      lclImp: computeShipmentVolume(lclImpHolder),
      otherService: computeShipmentVolume(otherHolder),
      totalProfit: 0,
      totalRevenue: 0
    }

    const airImp: Array<any> = volumePerformance['airImp'] || []
    const airExp: Array<any> = volumePerformance['airExp'] || []
    const fclExp: Array<any> = volumePerformance['fclExp'] || []
    const fclImp: Array<any> = volumePerformance['fclImp'] || []
    const lclExp: Array<any> = volumePerformance['lclExp'] || []
    const lclImp: Array<any> = volumePerformance['lclImp'] || []
    const otherService: Array<any> = volumePerformance['otherService'] || []

    // Calculate total profit and revenue from all segments
    const allSegments = [...airImp, ...airExp, ...fclExp, ...fclImp, ...lclExp, ...lclImp, ...otherService];
    volumePerformance.totalProfit = allSegments
      .filter(record => record.keyAccountName === 'Total')
      .reduce((sum, record) => {
        // Remove currency formatting and convert to number
        const profit = typeof record.profit === 'string'
          ? Number(record.profit.replace(/[^0-9.-]+/g, ''))
          : (record.profit || 0);
        return sum + profit;
      }, 0);

    volumePerformance.totalRevenue = allSegments
      .filter(record => record.keyAccountName === 'Total')
      .reduce((sum, record) => {
        // Remove currency formatting and convert to number
        const revenue = typeof record.revenue === 'string'
          ? Number(record.revenue.replace(/[^0-9.-]+/g, ''))
          : (record.revenue || 0);
        return sum + revenue;
      }, 0);
    this.volumePerformance = volumePerformance;
    return volumePerformance;
  }

  loadData() {
    const { appContext } = this.props;
    this.markLoading(true);
    const { dateFilter } = this.reportFilter;
    if (!dateFilter || !dateFilter.fromValue || !dateFilter.toValue) return;

    const params: any = {
      params: {
        fromDate: dateFilter.fromValue,
        toDate: dateFilter.toValue,
      }
    };

    appContext.createHttpBackendCall('PartnerReportService', 'searchVolumeSalemanKeyAccountReport', { params })
      .withSuccessData((records: Array<any>) => {
        this.rawData = records;
        this.rawDataProcessor('SALES', records);
        this.markLoading(false);
        this.viewId = util.IDTracker.next();
        this.forceUpdate();
      })
      .withFail((response: server.BackendResponse) => {
        let messageError: string = response.error['message'] || 'An unexpected error occurred. Please try again later.';
        bs.dialogShow('Error',
          <div className="text-danger fw-bold text-center py-3 border-bottom">
            <FeatherIcon.AlertCircle className="mx-2" />{messageError}
          </div>,
          { backdrop: 'static', size: 'md' }
        );
        return;
      })
      .call();
    this.forceUpdate();
  }

  renderUIPerformance(type: 'FCL' | 'AIR' | 'LCL' | 'OTHER', title: string, records: Array<any> = []) {
    if (records.length === 0) return <></>
    const { appContext, pageContext } = this.props;
    return (
      <UIPerformanceGridReport type={type} title={title}
        appContext={appContext} pageContext={pageContext} rawRecords={records} />
    )
  }

  onMakeNewReport = (volumePerformance: any, reportType: 'SALES' | 'BD') => {
    const { appContext, pageContext } = this.props;
    const { fromValue, toValue } = this.reportFilter.dateFilter;

    let reportBean: SalemanReportBean = {
      id: undefined,
      code: '',
      type: reportType,
      reportedDateFrom: fromValue,
      reportedDateTo: toValue,
      salemanAccountId: SESSION.getAccountId(),
      salemanLabel: SESSION.getAccountAcl().getFullName(),
      volumePerformance: volumePerformance,
      profit: 0,
      volume: 0,
      revenue: 0,
      highlights: {
        newCustomers: [],
        expectedLeadsToWin: [],
        marketInformation: '',
        reportedIssues: ''
      },
      forecast: {},
      suggestionOrRequest: '',
    }

    if (reportType === 'BD') {

      const dateFilter = new util.TimeRange();
      dateFilter.fromSetDate(util.TimeUtil.parseCompactDateTimeFormat(fromValue));
      dateFilter.toSetDate(util.TimeUtil.parseCompactDateTimeFormat(toValue));
      let searchParams: sql.SqlSearchParams = {};
      searchParams.params = {
        type: ['AGENTS_APPROACHED', 'AGENTS'],
      };

      searchParams.rangeFilters = [
        sql.createDateTimeFilterNew("createdTime", "Created Time", dateFilter)
      ];

      appContext.createHttpBackendCall('CustomerLeadsService', 'searchUserCustomers', { params: searchParams })
        .withSuccessData((data: any) => {
          let signedAaContracts: Array<any> = data.filter((item: any) => item.type === 'AGENTS');
          let newAgentsApproachedIn2Weeks: Array<any> = data.filter((item: any) => item.type === 'AGENTS_APPROACHED');
          let performance: any = this.rawData.length > 0 ? this.rawDataProcessor('BD', this.rawData) : this.volumePerformance;
          reportBean['volumePerformance'] = performance;
          reportBean['highlights'] = {
            signedAaContracts: signedAaContracts,
            meetingIn2Weeks: [],
            newAgentsApproachedIn2Weeks: newAgentsApproachedIn2Weeks,
            otherHighlights: '',
            lowlights: ''
          };
          reportBean['forecast'] = {
            airVolume: { kgs: '' },
            seaVolume: { teus: '', cbm: '' },
            estimate: { gp: '', revenue: '' }
          };

          const createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
            return (<UIKeyAccountFormReport appContext={appCtx} pageContext={pageCtx} initReportBean={reportBean} />)
          }

          let popupId = `make-key-account-report-${util.IDTracker.next()}`;
          let popupLabel: string = `Key Account Report - ${SESSION.getAccountAcl().getFullName()}`;
          pageContext.createPopupPage(popupId, popupLabel, createAppPage, { size: 'xl', backdrop: 'static' });

        }).call();
    } else {
      const createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
        return (<UIKeyAccountFormReport appContext={appCtx} pageContext={pageCtx} initReportBean={reportBean} />)
      }

      let popupId = `make-key-account-report-${util.IDTracker.next()}`;
      let popupLabel: string = `Key Account Report - ${SESSION.getAccountAcl().getFullName()}`;
      pageContext.createPopupPage(popupId, popupLabel, createAppPage, { size: 'xl', backdrop: 'static' });
    }
  }

  onRefresh = () => {
    this.markLoading(true);
    this.forceUpdate();
    const { appContext } = this.props;

    const { dateFilter } = this.reportFilter;
    if (!dateFilter || !dateFilter.fromValue || !dateFilter.toValue) return;

    let fromDate: Date = util.TimeUtil.parseCompactDateTimeFormat(dateFilter.fromValue);
    let toDate: Date = util.TimeUtil.parseCompactDateTimeFormat(dateFilter.toValue);

    // Format dates to YYYY-MM-DD format for API params
    const formatDateToString = (date: Date): string => {
      return date.toISOString().split('T')[0];
    };

    appContext.createHttpBackendCall('MSAIntegrationService', 'syncHouseBillWithSaleman', {
      params: {
        'from_date': '2025-05-01',
        'to_date': '2025-08-02'
      }
    })
      .withSuccessData((_data: any) => {
        this.markLoading(false);
        this.forceUpdate();
      })
      .call();
  }

  onViewAll = () => {
    const { pageContext } = this.props;
    const { dateFilter } = this.reportFilter;
    if (!dateFilter || !dateFilter.fromValue || !dateFilter.toValue) return;

    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UIVolumeSalemanKeyAccountReportList appContext={appCtx} pageContext={pageCtx}
          plugin={new UIVolumeSalemanKeyAccountReportPlugin().withReportedDate(dateFilter.fromValue, dateFilter.toValue)} />
      );
    }
    pageContext.createPopupPage(`view-all-${util.IDTracker.next()}`, "Shipment Details", createAppPage, { size: 'xl', backdrop: 'static' });
  }

  renderHeader(): React.ReactNode {
    const { appContext, pageContext, hiddenHeader } = this.props;
    if (hiddenHeader) return <></>

    return (
      <div className="flex-hbox flex-grow-0 align-items-center justify-content-between border-bottom px-2 py-1">
        <h5 style={{ color: '#6c757d', fontSize: '0.9rem' }}>
          <FeatherIcon.TrendingUp className="me-2" size={18} />{`Key Account Performance`}
        </h5>

        <div className="flex-hbox justify-content-end align-items-center flex-grow-1">

          <bs.Button laf="secondary" outline size="sm" className="me-1 p-1" style={{ color: '#6c757d' }}
            onClick={this.onViewAll}>
            View all
            <FeatherIcon.ChevronRight size={14} className="ms-2" />
          </bs.Button>

          <WKeyAccountReportTypeSelector appContext={appContext} pageContext={pageContext}
            readOnly={bs.ScreenUtil.isMobileScreen()}
            onModify={(_bean: any, _field: string, _oldVal: any, _newVal: any) => {
              this.onMakeNewReport(this.volumePerformance, _newVal);
            }} />

          <WQuickTimeRangeSelector appContext={appContext} pageContext={pageContext}
            initBean={this.reportFilter.dateFilter}
            onModify={(bean: any, _field: string, _oldVal: any, _newVal: any) => {
              let dateFilter: any = {
                fromValue: bean.fromValue,
                toValue: bean.toValue,
                label: bean.label
              };
              this.reportFilter.dateFilter = dateFilter;
              this.loadData();
            }} />

          <bs.Button laf="secondary" outline size="sm" className="me-1 p-1" style={{ color: '#6c757d' }}
            onClick={this.onRefresh}>
            <bs.CssTooltip width={300} position="bottom-left" offset={{ x: -200, y: 0 }}>
              <bs.CssTooltipToggle >
                <FeatherIcon.RefreshCcw size={14} className="me-1" />
                Refresh
              </bs.CssTooltipToggle>
              <bs.CssTooltipContent>
                <div className="tooltip-header">
                  <span className="tooltip-title">Refresh data from Bee Legacy (BFSOne).</span>
                </div>
              </bs.CssTooltipContent>
            </bs.CssTooltip>
          </bs.Button>

          <bs.Button laf="secondary" outline size="sm" className="me-1 p-1" style={{ color: '#6c757d' }}
            onClick={() => { }}>
            <FeatherIcon.Maximize2 size={14} className="me-1" />
          </bs.Button>

        </div>
      </div>
    )
  }

  render(): React.ReactNode {
    const lightColor = '#D6EFD8';
    const borderColor = '#80AF81';

    const hoverStyle: any = {
      boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
      transform: 'translateY(-2px)',
      backgroundColor: lightColor
    };

    const airImp: Array<any> = this.volumePerformance['airImp'] || []
    const airExp: Array<any> = this.volumePerformance['airExp'] || []
    const fclExp: Array<any> = this.volumePerformance['fclExp'] || []
    const fclImp: Array<any> = this.volumePerformance['fclImp'] || []
    const lclExp: Array<any> = this.volumePerformance['lclExp'] || []
    const lclImp: Array<any> = this.volumePerformance['lclImp'] || []
    const otherService: Array<any> = this.volumePerformance['otherService'] || []

    let totalProfit: number = this.volumePerformance['totalProfit'];
    let totalRevenue: number = this.volumePerformance['totalRevenue'];
    let profitRate: number = totalProfit / totalRevenue || 0;
    let margin = USD_CURR_FORMAT(profitRate * 100)

    {/* ------------------- Key Account Performance ----------------------- */ }
    return (
      <div className="flex-vbox" >
        <div className="bg-white border rounded-3 flex-vbox justify-content-center align-items-center"
          style={{
            borderColor: borderColor,
            transition: 'all 0.3s ease',
            marginBottom: '2px'
          }}
          onMouseEnter={(e) => Object.assign(e.currentTarget.style, hoverStyle)}
          onMouseLeave={(e) => {
            e.currentTarget.style.boxShadow = 'none';
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.backgroundColor = '#fff';
          }}>
          <div className="flex-vbox mx-2 p-1 bg-white rounded-md w-100" key={this.viewId}>
            {this.renderHeader()}
            <div className="flex-vbox" style={{ minHeight: 400, maxHeight: 400 }}>
              {
                this.isLoading() ?
                  this.renderLoading()
                  :
                  <bs.GreedyScrollable className='p-1 flex-vbox flex-grow-1' style={{ width: '100%', height: '100%' }}>
                    {this.renderUIPerformance('AIR', 'Air Export', airExp)}
                    {this.renderUIPerformance('AIR', 'Air Import', airImp)}
                    {this.renderUIPerformance('FCL', 'FCL Export', fclExp)}
                    {this.renderUIPerformance('FCL', 'FCL Import', fclImp)}
                    {this.renderUIPerformance('LCL', 'LCL Export', lclExp)}
                    {this.renderUIPerformance('LCL', 'LCL Import', lclImp)}
                    {this.renderUIPerformance('OTHER', 'Other Service', otherService)}
                  </bs.GreedyScrollable>
              }
            </div>

            <div className="flex-grow-0 flex-shrink-0 p-2 border-top flex-hbox justify-content-between align-items-center">

              <div className='flex-hbox flex-grow-0 flex-shrink-0 align-items-center justify-content-start mx-2 py-1' style={{ maxWidth: 250 }}>
                <h6 className="fw-bold text-danger"> <FeatherIcon.Package className="me-2" size={16} />TOTAL PERFORMANCE</h6>
              </div>

              <div className='flex-hbox align-items-center justify-content-end mx-2 gap-2'>

                <div className="d-flex justify-content-between align-items-center flex-grow-0 flex-shrink-0 mb-1 pe-2 fs-9 border-end">
                  <span className="fw-bold me-2">GP:</span>
                  <span className="fw-bold">{`${USD_CURR_FORMAT(totalProfit)} USD`}</span>
                </div>

                <div className="d-flex justify-content-between align-items-center flex-grow-0 flex-shrink-0 mb-1 pe-2 fs-9 border-end">
                  <span className="fw-bold me-2">REV:</span>
                  <span className="fw-bold">{`${USD_CURR_FORMAT(totalRevenue)} USD`}</span>
                </div>

                <div className="d-flex justify-content-between align-items-center flex-grow-0 flex-shrink-0 mb-1 pe-2 fs-9 text-primary">
                  <span className="fw-bold me-2">Margin (GP/ REV):</span>
                  <span className="fw-bold">{`${margin}  %`}</span>
                </div>

              </div>
            </div>

          </div>
        </div>
      </div>
    )
  }

}