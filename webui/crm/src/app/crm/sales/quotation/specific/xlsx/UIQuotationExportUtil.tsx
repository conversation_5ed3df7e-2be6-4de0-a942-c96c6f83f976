import React from 'react';
import * as FeatherIcon from 'react-feather';

import { entity, bs } from '@datatp-ui/lib';

import { AdditionalChargeTarget, TransportPurpose } from 'app/crm/price';
import { CELL_STYLES } from './StyleHelper';
import { createHeaderRow, createIntroRow, createTitleRow, formatDate, } from './XLSXUtils';
import { GeneralInformation, IGeneralInformation } from './models/GeneralInformation';
import { ContainerType, ContainerTypeUnit } from 'app/crm/common/ContainerTypeUtil';

import { TransportationMode, TransportationTool, getAbbreviation } from 'app/crm/common';

export const MAX_COLUMN: number = 10;

export type Header = {
  name: string;
  colIdx: number;
  mappingTo: string;
  dataType?: 'currency' | 'date';
}

export type QuoteExportRequest = {
  issuedDate: string;
  quotationNo: string;
  inquiry: any;
  quoteSections: any[];
  maxColumnIdx: number;
}

export class SQuotationExportProcessor {
  isMultipleRouting: boolean = false;
  quoteType: 'CUSTOMER' | 'AGENT' | 'CUSTOM';
  rows: entity.XLSXRow[] = [];
  inquiry: any = {};
  request: QuoteExportRequest;

  containerTypes: ContainerType[] = []
  containerQtyMap: Record<string, number> = {}
  totalQuoteCurrMap: Record<string, number> = {}

  indexMap: any = { 1: 'I', 2: 'II', 3: 'III', 4: 'IV', 5: 'V', 6: 'VI', 7: 'VII' }
  currentIdx: number = 1;
  maxColumn: number = MAX_COLUMN;

  constructor(quotation: any, isMultipleRouting: boolean = false, quoteType: 'CUSTOMER' | 'AGENT' | 'CUSTOM' = 'CUSTOMER') {
    this.isMultipleRouting = isMultipleRouting;
    this.quoteType = quoteType;

    this.inquiry = quotation['inquiry'] || {};
    this.containerQtyMap = {}
    this.totalQuoteCurrMap = {};


    let containers = quotation['inquiry']['containers'] || [];
    for (let cont of containers) {
      let containerType: string = (cont['containerType'] || '').trim();
      let type: ContainerType | undefined = ContainerTypeUnit.match(containerType);
      if (type) {
        this.containerTypes.push(type)
        this.containerQtyMap[type.name] = cont['quantity'] || 0;
      }
    }

    this.currentIdx = 1;
    let computeMaxCol = this.containerTypes.length + 6;
    if (isMultipleRouting) computeMaxCol += 3; // for POL, POD, Final Dest column
    this.maxColumn = Math.max(MAX_COLUMN, computeMaxCol);

    const mode: TransportationMode = this.inquiry.mode;
    let quotationTitle = 'QUOTATION';
    if (TransportationTool.isSea(mode) || TransportationTool.isAir(mode)) {
      quotationTitle = getAbbreviation(mode) + ' ' + this.inquiry.purpose + ' ' + 'QUOTATION';
    }

    let pickupAddress = this.inquiry.pickupAddress;
    let deliveryAddress = this.inquiry.deliveryAddress;

    const referenceNo = this.inquiry.referenceCode || '';

    const generalInfoData: IGeneralInformation = {
      header: {
        title: quotationTitle,
        quotationNo: referenceNo,
        date: new Date()
      },
      contactInfo: {
        company: this.inquiry.clientLabel,
        attention: this.inquiry.attention
      },
      shipmentLocation: {
        fromLocation: this.inquiry.fromLocationLabel,
        toLocation: this.inquiry.finalDestination || this.inquiry.toLocationLabel,
        pickupAddress,
        deliveryAddress
      },
      cargo: {
        commodity: this.inquiry.descOfGoods,
        cargoReadyDate: this.inquiry.cargoReadyDate,
        grossWeightKg: this.inquiry.grossWeightKg,
        volumeInfo: this.inquiry.containerTypes,
      },
      transportationMode: this.inquiry.mode,
      incoterms: this.inquiry.incoterms,
      truckDetails: TransportationTool.isTruck(this.inquiry.mode) ? {
        deliveryLocation: deliveryAddress,
        pickupLocation: pickupAddress,
        commodity: this.inquiry.commodity
      } : undefined
    };

    const generalInfo = new GeneralInformation(generalInfoData, this.maxColumn, this.getIndex());
    this.rows.push(...generalInfo.generateRows());

    const displayType = quoteType === 'AGENT' ? 'partner' : 'customer';

    let intro = `On behalf of Bee Logistics, we would like to extend our sincere thanks to you, our valued ${displayType}. We are pleased to present our rates and services as detailed below:`
    let copyrightNotice = 'Copyright by BEE LOGISTICS CORPORATION. All Rights Reserved';
    this.rows.push(createIntroRow(intro, this.maxColumn));
    this.request = this.processQuotation(quotation);
    this.rows.push(createIntroRow(copyrightNotice, this.maxColumn));
  }

  private getIndex(): string {
    let value = this.indexMap[this.currentIdx];
    this.currentIdx++;
    return value;
  }

  private processQuotation(quotation: any): QuoteExportRequest {
    const { inquiry, code: quotationNo, issuedDate } = quotation;
    // Process local handling charges
    let quoteList: any[] = quotation['quoteListSelector'] || [];
    this.processQuoteList(inquiry, quoteList, this.rows);

    // Process local handling charges
    const localHandlingChanges = quotation['localHandlingCharges'] || [];
    this.processLocalCharges(localHandlingChanges, this.rows)

    let totalRow: entity.XLSXRow = {
      dataType: 'complex',
      rowHeight: 2,
      cells: [
        {
          value: `TOTAL CHARGES OF SHIMENT (II to ${this.indexMap[this.currentIdx - 1]})`,
          colStart: 0,
          style: CELL_STYLES.BOLD_RED
        },
        {
          value: 'USD',
          colStart: 1,
          style: CELL_STYLES.BOLD_RED_CENTER
        },

        {
          value: '',
          colStart: 2,
          colEnd: 4,
          style: CELL_STYLES.CENTER
        },
        {
          value: this.totalQuoteCurrMap['USD'] || '',
          colStart: 5,
          dataType: 'currency',
          style: CELL_STYLES.BOLD_RED_CENTER
        },
        {
          value: '',
          colStart: 6,
          colEnd: this.maxColumn,
          style: {
            border: true,
          },
        },
      ]
    }

    if (this.containerTypes.length === 0) {
      this.rows.push({
        dataType: 'complex', rowHeight: 2,
        cells: [
          {
            value: ``,
            colStart: 0,
            colEnd: this.maxColumn,
            style: CELL_STYLES.BASE
          }
        ]
      })
      this.rows.push(totalRow)
    }

    this.createLocalTariff(this.rows);
    let indexValue = this.getIndex();
    this.rows.push(createTitleRow(`${indexValue}. Note`, this.maxColumn));
    this.rows.push({
      dataType: 'complex',
      rowHeight: 6,
      cells: [{
        value: inquiry['signatureNote'] || '',
        colEnd: this.maxColumn,
        style: CELL_STYLES.WRAP_TEXT,
      }]
    });

    return {
      quotationNo,
      issuedDate,
      inquiry,
      quoteSections: this.rows,
      maxColumnIdx: this.maxColumn,
    };
  }

  private createLocalTariff(rows: entity.XLSXRow[]) {
    const referenceNo = this.inquiry.referenceCode || '';
    if (referenceNo || this.quoteType === 'AGENT') return;

    let indexValue = this.getIndex();
    this.rows.push(createTitleRow(`${indexValue}.Local tariff`, this.maxColumn));
    let headerRow: entity.XLSXRow = {
      dataType: 'complex',
      rowspan: 1,
      rowHeight: 2,
      cells: [
        {
          value: `DESCRIPTION`,
          colStart: 0,
          rowspan: 1,
          style: { ...CELL_STYLES.BOLD, bgColor: 'LIGHT_ORANGE' }
        },
        {
          value: 'THC (per CTNR)',
          colStart: 1,
          colEnd: 4,
          style: CELL_STYLES.BOLD_CENTER_LIGHT_ORANGE
        },
        {
          value: `20'DC`,
          colStart: 1,
          style: CELL_STYLES.BOLD_CENTER_LIGHT_ORANGE
        },
        {
          value: `40'DC/HC`,
          colStart: 2,
          style: CELL_STYLES.BOLD_CENTER_LIGHT_ORANGE
        },
        {
          value: `20'RF`,
          colStart: 3,
          style: CELL_STYLES.BOLD_CENTER_LIGHT_ORANGE
        },
        {
          value: `40'RF`,
          colStart: 4,
          style: CELL_STYLES.BOLD_CENTER_LIGHT_ORANGE
        },

        {
          value: 'BILL \n(per BL)',
          rowspan: 1,
          colStart: 5,
          style: CELL_STYLES.BOLD_CENTER_LIGHT_ORANGE
        },
        {
          value: 'SEAL \n(per PCS)',
          rowspan: 1,
          colStart: 6,
          style: CELL_STYLES.BOLD_CENTER_LIGHT_ORANGE
        },
        {
          value: 'TELEX/SURRENDERED \n(per BL)',
          rowspan: 1,
          colStart: 7,
          style: CELL_STYLES.BOLD_CENTER_LIGHT_ORANGE
        },
        {
          value: 'AFS/AFR/ENS/AMS/ACI \n(For CN/JP/EU/US/CA routings per BL)',
          rowspan: 1,
          colStart: 8,
          style: CELL_STYLES.BOLD_CENTER_LIGHT_ORANGE
        },
        {
          value: 'HANDLING FEE \n(SHIPMENT)',
          rowspan: 1,
          colStart: 9,
          colEnd: this.maxColumn,
          style: CELL_STYLES.BOLD_CENTER_LIGHT_ORANGE
        },
      ]
    }

    this.rows.push(headerRow);
    let row: entity.XLSXRow = {
      dataType: 'complex',
      rowHeight: 2,
      cells: [
        {
          value: 'ALL LINES (Except SEALAND to CN/JP)',
          colStart: 0,
          style: CELL_STYLES.BASE
        },
        {
          value: '',
          colStart: 1,
          style: CELL_STYLES.BASE
        },
        {
          value: '',
          colStart: 2,
          style: CELL_STYLES.BASE
        },
        {
          value: '',
          colStart: 3,
          style: CELL_STYLES.BASE
        },
        {
          value: '',
          colStart: 4,
          style: CELL_STYLES.BASE
        },
        {
          value: '',
          colStart: 5,
          style: CELL_STYLES.BASE
        },
        {
          value: '',
          colStart: 6,
          style: CELL_STYLES.BASE
        },
        {
          value: '',
          colStart: 7,
          style: CELL_STYLES.BASE
        },
        {
          value: '',
          colStart: 8,
          style: CELL_STYLES.BASE
        },
        {
          value: '',
          colStart: 9,
          colEnd: this.maxColumn,
          style: CELL_STYLES.BASE
        },
      ]
    }
    this.rows.push(row);
    this.rows.push(this.createEmptyCellRow());
    this.rows.push(this.createEmptyCellRow());
  }

  private createEmptyCellRow(): entity.XLSXRow {
    let row: entity.XLSXRow = {
      dataType: 'complex',
      rowHeight: 2,
      cells: [
        {
          value: '',
          colStart: 0,
          style: CELL_STYLES.BASE
        },
        {
          value: '',
          colStart: 1,
          style: CELL_STYLES.BASE
        },
        {
          value: '',
          colStart: 2,
          style: CELL_STYLES.BASE
        },
        {
          value: '',
          colStart: 3,
          style: CELL_STYLES.BASE
        },
        {
          value: '',
          colStart: 4,
          style: CELL_STYLES.BASE
        },
        {
          value: '',
          colStart: 5,
          style: CELL_STYLES.BASE
        },
        {
          value: '',
          colStart: 6,
          style: CELL_STYLES.BASE
        },
        {
          value: '',
          colStart: 7,
          style: CELL_STYLES.BASE
        },
        {
          value: '',
          colStart: 8,
          style: CELL_STYLES.BASE
        },
        {
          value: '',
          colStart: 9,
          colEnd: this.maxColumn,
          style: CELL_STYLES.BASE
        },
      ]
    }
    return row;
  }

  private createAirQuoteRow(quote: any): entity.XLSXRow {
    let validity: string = '';
    if (quote['validity']) {
      validity = formatDate(quote['validity']);
    }
    let effectiveDate = '';
    if (quote['effectiveDate']) {
      effectiveDate = formatDate(quote['effectiveDate']);
    }

    let transitTime = quote['transitTime'];
    let transitPort = quote['transitPort'];
    let transitDisplay = '';
    if (transitTime && transitPort) {
      transitDisplay = `${transitTime} / ${transitPort}`;
    } else {
      transitDisplay = transitTime || transitPort || '';
    }

    let qty = this.inquiry['chargeableWeight'] || this.inquiry['grossWeightKg'] || 0;
    let price = (quote['priceGroup']['selectedPrice'] || 0)

    let dataRow: entity.XLSXRow = {
      dataType: 'complex',
      rowHeight: 2,
      cells: [
        {
          value: `${quote['carrierLabel'] || ''}`,
          colStart: 0,
          style: CELL_STYLES.BASE
        },
        {
          value: quote['currency'],
          colStart: 1,
          style: CELL_STYLES.CENTER,
        },
        {
          value: 'KGS',
          colStart: 2,
          style: CELL_STYLES.CENTER,
        },
        {
          value: price,
          colStart: 3,
          dataType: 'currency',
          style: CELL_STYLES.CENTER,
        },
        {
          value: qty,
          colStart: 4,
          dataType: 'currency',
          style: CELL_STYLES.CENTER,
        },
        {
          value: price,
          colStart: 5,
          dataType: 'currency',
          style: CELL_STYLES.CENTER,
        },
        {
          value: transitDisplay,
          colStart: 6,
          style: CELL_STYLES.WRAP_TEXT,
        },
        {
          value: effectiveDate,
          dataType: 'date',
          colStart: 7,
          style: CELL_STYLES.CENTER,
        },
        {
          value: validity,
          dataType: 'date',
          colStart: 8,
          style: CELL_STYLES.CENTER,
        },
        {
          value: quote['note'] || '',
          colStart: 9,
          colEnd: this.maxColumn,
          style: CELL_STYLES.WRAP_TEXT,
        },
      ]
    }
    return dataRow;
  }

  private createLCLQuoteRow(quote: any): entity.XLSXRow {
    let validity: string = '';
    if (quote['validity']) {
      validity = formatDate(quote['validity']);
    }
    let effectiveDate = '';
    if (quote['effectiveDate']) {
      effectiveDate = formatDate(quote['effectiveDate']);
    }

    let qty = this.inquiry['chargeableVolume'] || this.inquiry['volumeCbm'] || 0;
    qty = Math.round(qty * 1000) / 1000;

    let transitTime = quote['transitTime'];
    let transitPort = quote['transitPort'];
    let transitDisplay = '';
    if (transitTime && transitPort) {
      transitDisplay = `${transitTime} / ${transitPort}`;
    } else {
      transitDisplay = transitTime || transitPort || '';
    }

    let dataRow: entity.XLSXRow = {
      dataType: 'complex',
      rowHeight: 2,
      cells: [
        {
          value: quote['carrierLabel'] || '',
          colStart: 0,
          style: CELL_STYLES.BASE
        },
        {
          value: quote['currency'],
          colStart: 1,
          style: CELL_STYLES.CENTER,
        },
        {
          value: 'CBM',
          colStart: 2,
          style: CELL_STYLES.CENTER,
        },
        {
          value: quote['priceGroup']['selectedPrice'] || 0,
          colStart: 3,
          dataType: 'currency',
          style: CELL_STYLES.CENTER,
        },
        {
          value: qty,
          colStart: 4,
          dataType: 'currency',
          style: CELL_STYLES.CENTER,
        },
        {
          value: (quote['priceGroup']['selectedPrice'] || 0) * qty,
          colStart: 5,
          dataType: 'currency',
          style: CELL_STYLES.CENTER,
        },
        {
          value: transitDisplay,
          colStart: 6,
          style: CELL_STYLES.WRAP_TEXT,
        },
        {
          value: effectiveDate,
          dataType: 'date',
          colStart: 7,
          style: CELL_STYLES.CENTER,
        },
        {
          value: validity,
          colStart: 8,
          dataType: 'date',
          style: CELL_STYLES.CENTER,
        },
        {
          value: quote['note'] || '',
          colStart: 9,
          colEnd: this.maxColumn,
          style: CELL_STYLES.WRAP_TEXT,
        },
      ]
    }
    return dataRow;
  }

  private processQuoteList(inquiry: any, quoteList: any[], rows: entity.XLSXRow[]): void {
    if (!quoteList || quoteList.length === 0) {
      return;
    }

    // Check if all quotes have the same transportation mode
    let mode: TransportationMode = inquiry.mode;
    let purpose = inquiry.purpose;
    const allSameMode = quoteList.every((quote: any) => quote.mode === mode);
    if (!allSameMode) {
      bs.dialogShow('Error',
        <div className="text-warning fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />{'Please ensure all quotes have the same transportation mode.'}
        </div>,
        { backdrop: 'static', size: 'md' }
      );
      throw new Error('All quotes must have the same transportation mode.');
    }


    let indexValue = this.getIndex();
    let route = (inquiry.fromLocationLabel && (inquiry.finalDestination || inquiry.toLocationLabel)) ? ` (${inquiry.fromLocationLabel} - ${inquiry.finalDestination || inquiry.toLocationLabel})` : '';
    const hasFinalDestination = !!inquiry.finalDestination

    let title = ``
    let headers: string[] = [];
    let totalFreight: number = 0;


    if (TransportationTool.isAir(mode)) {
      title = `${indexValue}.Air Freight ${route}`;
      headers = ['AirLine', 'Currency', 'Unit', 'Unit Price', 'Quantity', 'TOTAL', 'T/T (days)', 'Effective Date', 'Validity', 'Remarks'];
    } else if (TransportationTool.isSeaLCL(mode)) {
      title = `${indexValue}.Ocean Freight ${route}`;
      headers = ['Carrier', 'Currency', 'Unit', 'Unit Price', 'Quantity', 'TOTAL', 'T/T (days)', 'Effective Date', 'Validity', 'Remarks'];
    } else if (TransportationTool.isSeaFCL(mode)) {
      title = `${indexValue}.Ocean Freight ${route}`;
      const containerHeaders: string[] = this.containerTypes.map(sel => sel.label);
      headers = [
        'Carrier', ...(this.isMultipleRouting ? ['POL', 'POD', ...(hasFinalDestination ? ['Final Dest'] : [])] : []),
        'Currency', ...containerHeaders, 'T/T (days)', ...(purpose === 'EXPORT' && TransportationTool.isSeaFCL(mode) ? ['Free time'] : []),
        'Effective Date', 'Validity', 'Remarks'
      ];
    } else {

    }

    rows.push(createTitleRow(title, this.maxColumn));
    rows.push(createHeaderRow(headers, this.maxColumn));

    quoteList.forEach((quote: any) => {
      if (TransportationTool.isSeaLCL(mode)) {
        let volume: number = this.inquiry['chargeableVolume'] || this.inquiry['volumeCbm'] || 0;
        totalFreight += (quote['priceGroup']['selectedPrice'] || 0) * volume;
        rows.push(this.createLCLQuoteRow(quote));
      } else if (TransportationTool.isAir(mode)) {
        let qty = this.inquiry['chargeableWeight'] || this.inquiry['grossWeightKg'] || 0;
        totalFreight += (quote['priceGroup']['selectedPrice'] || 0) * qty
        rows.push(this.createAirQuoteRow(quote));
      } else if (TransportationTool.isSeaFCL(mode)) {
        let indexOfTotal = 5;
        let totalEachFreight: number = 0;
        let priceGroup: any = quote['priceGroup'] || {};

        let containerCells: any[] = [];
        for (let i = 0; i < this.containerTypes.length; i++) {
          let cont = this.containerTypes[i];
          let fclLevel: string | undefined = cont.toFCLPriceLevel();
          if (fclLevel) {
            let priceVal = priceGroup[fclLevel] || 0
            totalEachFreight += priceVal * this.containerQtyMap[cont.name]
            containerCells.push({
              value: priceVal,
              colStart: i + 2,
              dataType: 'currency',
              style: CELL_STYLES.CENTER,
            })
          }
        }

        totalFreight += totalEachFreight;

        let validity: string = '';
        if (quote['validity']) {
          validity = formatDate(quote['validity']);
        }

        let effectiveDate = '';
        if (quote['effectiveDate']) {
          effectiveDate = formatDate(quote['effectiveDate']);
        }

        indexOfTotal = (this.isMultipleRouting ? (hasFinalDestination ? 5 : 4) : 2) + containerCells.length;

        let transitTime = quote['transitTime'];
        let transitPort = quote['transitPort'];
        let transitDisplay = '';
        if (transitTime && transitPort) {
          transitDisplay = `${transitTime} / ${transitPort}`;
        } else {
          transitDisplay = transitTime || transitPort || '';
        }

        let dataRow: entity.XLSXRow = {
          dataType: 'complex',
          rowHeight: 2,
          cells: [
            {
              value: quote['carrierLabel'],
              colStart: 0,
              style: CELL_STYLES.BASE
            },
            ...(this.isMultipleRouting ? [
              {
                value: quote['fromLocationLabel'] || '',
                colStart: 1,
                style: CELL_STYLES.BASE,
              },
              {
                value: quote['toLocationLabel'] || '',
                colStart: 2,
                style: CELL_STYLES.BASE,
              },
              ...(hasFinalDestination ? [{
                value: quote['finalDestination'] || '',
                colStart: 3,
                style: CELL_STYLES.BASE,
              }] : [])
            ] : []),
            {
              value: quote['currency'],
              colStart: this.isMultipleRouting ? (hasFinalDestination ? 4 : 3) : 1,
              style: CELL_STYLES.CENTER,
            },
            ...containerCells.map((cell, index) => ({
              ...cell,
              colStart: (this.isMultipleRouting ? (hasFinalDestination ? 5 : 4) : 2) + index
            })),
            {
              value: transitDisplay,
              colStart: (this.isMultipleRouting ? (hasFinalDestination ? 5 : 4) : 2) + containerCells.length,
              style: CELL_STYLES.WRAP_TEXT,
            },
            ...(purpose === 'EXPORT' && TransportationTool.isSeaFCL(mode) ? [
              {
                value: quote['freeTime'] || '',
                colStart: indexOfTotal + 1,
                style: CELL_STYLES.CENTER,
              },
              {
                value: effectiveDate,
                dataType: 'date',
                colStart: indexOfTotal + 2,
                style: CELL_STYLES.CENTER,
              },
              {
                value: validity,
                dataType: 'date',
                colStart: indexOfTotal + 3,
                style: CELL_STYLES.CENTER,
              },
              {
                value: quote['note'] || '',
                colStart: indexOfTotal + 4,
                colEnd: this.maxColumn,
                style: CELL_STYLES.WRAP_TEXT,
              }
            ] : [
              {
                value: effectiveDate,
                dataType: 'date',
                colStart: indexOfTotal + (this.isMultipleRouting ? (hasFinalDestination ? 3 : 2) : 1),
                style: CELL_STYLES.CENTER,
              },
              {
                value: validity,
                dataType: 'date',
                colStart: indexOfTotal + (this.isMultipleRouting ? (hasFinalDestination ? 4 : 3) : 2),
                style: CELL_STYLES.CENTER,
              },
              {
                value: quote['note'] || '',
                colStart: indexOfTotal + (this.isMultipleRouting ? (hasFinalDestination ? 5 : 4) : 3),
                colEnd: this.maxColumn,
                style: CELL_STYLES.WRAP_TEXT,
              }
            ])
          ]
        }
        rows.push(dataRow);

        this.totalQuoteCurrMap['USD'] = (this.totalQuoteCurrMap['USD'] || 0) + totalFreight;
      }
    });

    if (quoteList.length === 1 && !TransportationTool.isSeaFCL(mode)) {
      rows.push(this.createTotalRow(`TOTAL (${indexValue})`, totalFreight));
    }

  }

  private processLocalCharges(charges: any[], rows: entity.XLSXRow[]): void {
    const originLocalCharges: any[] = [];
    const destLocalCharges: any[] = [];
    const originDomesticCharges: any[] = [];
    const destDomesticCharges: any[] = [];

    for (let charge of charges) {
      if (!charge['mode']) charge['mode'] = this.inquiry['mode'];
      let target = charge['target'] || AdditionalChargeTarget.ORIGIN;
      let mode = charge['mode'];

      if (TransportationTool.isAir(mode) || TransportationTool.isSea(mode)) {
        if (target === AdditionalChargeTarget.ORIGIN) {
          originLocalCharges.push(charge);
        } else {
          destLocalCharges.push(charge);
        }
      } else if (TransportationTool.isTruck(mode) || TransportationTool.isUnknown(mode)) {
        if (target === AdditionalChargeTarget.ORIGIN) {
          originDomesticCharges.push(charge);
        } else {
          destDomesticCharges.push(charge);
        }
      }
    }

    if (originLocalCharges.length > 0) {
      let indexValue = this.getIndex();
      rows.push(createTitleRow(`${indexValue}.Local charge at POL / Origin`, this.maxColumn));
      if (this.quoteType === 'CUSTOMER') {
        if (this.containerTypes.length > 0) {
          const containerHeaders: string[] = this.containerTypes.map(sel => sel.label);
          const headers = ['COST', 'Currency', ...containerHeaders, 'B/L', 'Remarks']
          rows.push(createHeaderRow(headers, this.maxColumn));
          this.addLocalChargeRow(rows, originLocalCharges, indexValue);
        } else {
          rows.push(this.createLocalChargeHeaderRow());
          this.addLocalChargeRow(rows, originLocalCharges, indexValue);
        }
      } else {
        rows.push(this.createCustomLocalChargeHeaderRow());
        this.addCustomLocalChargeRows(rows, originLocalCharges);
      }
    }

    if (destLocalCharges.length > 0) {
      let indexValue = this.getIndex();
      rows.push(createTitleRow(`${indexValue}.Local charge at POD / Destination`, this.maxColumn));
      if (this.quoteType === 'CUSTOMER') {
        if (this.containerTypes.length > 0) {
          const containerHeaders: string[] = this.containerTypes.map(sel => sel.label);
          const headers = ['COST', 'Currency', ...containerHeaders, 'B/L', 'Remarks']
          rows.push(createHeaderRow(headers, this.maxColumn));
          this.addLocalChargeRow(rows, destLocalCharges, indexValue);
        } else {
          rows.push(this.createLocalChargeHeaderRow());
          this.addLocalChargeRow(rows, destLocalCharges, indexValue);
        }
      } else {
        rows.push(this.createCustomLocalChargeHeaderRow());
        this.addCustomLocalChargeRows(rows, destLocalCharges);
      }
    }

    if (originDomesticCharges.length > 0) {
      let indexValue = this.getIndex();
      rows.push(createTitleRow(`${indexValue}. Domestics charge in origin`, this.maxColumn));

      if (this.containerTypes.length > 0) {
        const containerHeaders: string[] = this.containerTypes.map(sel => sel.label);
        const headers = ['COST', 'Currency', ...containerHeaders, 'B/L', 'Remarks']
        rows.push(createHeaderRow(headers, this.maxColumn));
        this.addLocalChargeRow(rows, originDomesticCharges, indexValue);
      } else {
        rows.push(this.createLocalChargeHeaderRow());
        this.addLocalChargeRow(rows, originDomesticCharges, indexValue);
      }
    }

    if (destDomesticCharges.length > 0) {
      let indexValue = this.getIndex();
      rows.push(createTitleRow(`${indexValue}. Domestics charge in destination`, this.maxColumn));
      if (this.containerTypes.length > 0) {
        const containerHeaders: string[] = this.containerTypes.map(sel => sel.label);
        const headers = ['COST', 'Currency', ...containerHeaders, 'B/L', 'Remarks']
        rows.push(createHeaderRow(headers, this.maxColumn));
        this.addLocalChargeRow(rows, destDomesticCharges, indexValue);
      } else {
        rows.push(this.createLocalChargeHeaderRow());
        this.addLocalChargeRow(rows, destDomesticCharges, indexValue);
      }
    }

  }

  private createCustomLocalChargeHeaderRow(): entity.XLSXRow {
    const headers: string[] = ['COST', 'Currency', 'Unit Price', 'Unit', 'VAT', 'Remarks'];
    let headerRow: entity.XLSXRow = {
      dataType: 'complex',
      rowHeight: 2,
      cells: []
    }
    for (let i = 0; i < headers.length; i++) {
      let header: any = headers[i];
      let cell: any = {
        value: header,
        colStart: i,
        colEnd: i === headers.length - 1 ? this.maxColumn : i,
        style: i !== 0 ? { ...CELL_STYLES.BOLD_CENTER, bgColor: 'LIGHT_ORANGE' } : { ...CELL_STYLES.BOLD, bgColor: 'LIGHT_ORANGE' },
      }
      headerRow.cells.push(cell);
    }
    return headerRow;
  }

  private createLocalChargeHeaderRow(): entity.XLSXRow {
    const headers: string[] = ['COST', 'Currency', 'Unit', 'Unit Price', 'Quantity', 'TOTAL', 'Remarks'];
    let headerRow: entity.XLSXRow = {
      dataType: 'complex',
      rowHeight: 2,
      cells: []
    }
    for (let i = 0; i < headers.length; i++) {
      let header: any = headers[i];
      let cell: any = {
        value: header,
        colStart: i,
        colEnd: i === headers.length - 1 ? this.maxColumn : 0,
        style: i !== 0 ? { ...CELL_STYLES.BOLD_CENTER, bgColor: 'LIGHT_ORANGE' } : { ...CELL_STYLES.BOLD, bgColor: 'LIGHT_ORANGE' },
      }
      headerRow.cells.push(cell);
    }
    return headerRow;
  }

  private addCustomLocalChargeRows(rows: entity.XLSXRow[], charges: any[]): void {
    for (let sel of charges) {
      let unitPrice = sel['unitPrice'] || 0;
      let quoteRate = sel['quoteRate'] || {};
      let vat = sel['taxRate'] * 100 || 0;

      if (this.containerTypes.length > 0) {
        for (let i = 0; i < this.containerTypes.length; i++) {
          let cont = this.containerTypes[i];
          let amount = quoteRate[cont.label] || quoteRate[cont.name] || 0;

          if (amount !== 0) {
            let dataRow: entity.XLSXRow = {
              dataType: 'complex',
              rowHeight: 2,
              cells: [
                {
                  value: sel['name'],
                  colStart: 0,
                  style: CELL_STYLES.BASE
                },
                {
                  value: sel['currency'],
                  colStart: 1,
                  style: CELL_STYLES.CENTER
                },
                {
                  value: amount,
                  colStart: 2,
                  dataType: 'currency',
                  style: CELL_STYLES.CENTER
                },
                {
                  value: cont.label,
                  colStart: 3,
                  style: CELL_STYLES.CENTER
                },
                {
                  value: vat === 0 ? 'x' : vat,
                  colStart: 4,
                  dataType: vat === 0 ? 'string' : 'currency',
                  style: CELL_STYLES.CENTER
                },
                {
                  value: sel['note'] || '',
                  colStart: 5,
                  colEnd: this.maxColumn,
                  style: CELL_STYLES.WRAP_TEXT
                }
              ]
            };
            rows.push(dataRow);
          }
        }
      }

      if (unitPrice !== 0) {
        let dataRow: entity.XLSXRow = {
          dataType: 'complex',
          rowHeight: 2,
          cells: [
            {
              value: sel['name'],
              colStart: 0,
              style: CELL_STYLES.BASE
            },
            {
              value: sel['currency'],
              colStart: 1,
              style: CELL_STYLES.CENTER
            },
            {
              value: unitPrice,
              colStart: 2,
              dataType: 'currency',
              style: CELL_STYLES.CENTER
            },
            {
              value: sel['unit'],
              colStart: 3,
              style: CELL_STYLES.CENTER
            },
            {
              value: vat === 0 ? 'x' : vat,
              colStart: 4,
              dataType: vat === 0 ? 'string' : 'currency',
              style: CELL_STYLES.CENTER
            },
            {
              value: sel['note'] || '',
              colStart: 5,
              colEnd: this.maxColumn,
              style: CELL_STYLES.WRAP_TEXT
            }
          ]
        };
        rows.push(dataRow);
      }
    }
  }

  private addLocalChargeRow(rows: entity.XLSXRow[], charges: any[], indexValue: string): void {

    let total = 0;
    if (this.containerTypes.length > 0) {
      for (let sel of charges) {
        let totalItem = 0;
        let unitPrice = sel['unitPrice'] || 0;
        // totalItem += unitPrice * (sel['quantity'] || 1)
        totalItem += unitPrice

        let containerCells: any[] = [];
        let quoteRate = sel['quoteRate'] || {};

        for (let i = 0; i < this.containerTypes.length; i++) {
          let cont = this.containerTypes[i];
          let amount = quoteRate[cont.label] || quoteRate[cont.name] || 0;
          totalItem += amount * this.containerQtyMap[cont.name];

          let cell = {
            value: amount === 0 ? 'x' : amount,
            colStart: i + 2,
            dataType: amount === 0 ? 'string' : 'currency',
            style: CELL_STYLES.CENTER
          }
          containerCells.push(cell)
        }

        total += totalItem

        let indexOfTotal = containerCells.length + 2;

        let dataRow: entity.XLSXRow = {
          dataType: 'complex',
          rowHeight: 2,
          cells: [
            {
              value: `${sel['name'] || ''}`,
              colStart: 0,
              style: CELL_STYLES.BASE
            },
            {
              value: sel['currency'],
              colStart: 1,
              style: CELL_STYLES.CENTER
            },
            ...containerCells,
            {
              value: unitPrice === 0 ? 'x' : unitPrice,
              dataType: unitPrice === 0 ? 'string' : 'currency',
              colStart: indexOfTotal,
              style: CELL_STYLES.CENTER
            },
            {
              value: sel['note'] || '',
              colStart: indexOfTotal + 1,
              colEnd: this.maxColumn,
              style: CELL_STYLES.WRAP_TEXT
            },
          ]
        }
        rows.push(dataRow)
      }
    } else {
      for (let sel of charges) {
        let unitPrice = sel['unitPrice'] || 0;
        // if (unitPrice === 0) continue;
        total += unitPrice;
        let dataRow: entity.XLSXRow = {
          dataType: 'complex',
          rowHeight: 2,
          cells: [
            {
              value: `${sel['name'] || ''}`,
              colStart: 0,
              style: CELL_STYLES.BASE
            },
            {
              value: sel['currency'],
              colStart: 1,
              style: CELL_STYLES.CENTER
            },
            {
              value: sel['unit'],
              colStart: 2,
              style: CELL_STYLES.CENTER
            },
            {
              value: unitPrice,
              colStart: 3,
              dataType: 'currency',
              style: CELL_STYLES.CENTER
            },
            {
              value: sel['quantity'] || 0,
              colStart: 4,
              dataType: 'currency',
              style: CELL_STYLES.CENTER
            },
            {
              value: (sel['quantity'] || 0) * unitPrice,
              colStart: 5,
              dataType: 'currency',
              style: CELL_STYLES.CENTER
            },
            {
              value: sel['note'] || '',
              colStart: 6,
              colEnd: this.maxColumn,
              style: CELL_STYLES.WRAP_TEXT
            },
          ]
        }
        rows.push(dataRow)
      }
    }

    let isSumTotal = charges.every((charge: any) => TransportationTool.isSeaLCL(charge['mode']));
    if (isSumTotal) {
      rows.push(this.createTotalRow(`SUB TOTAL (${indexValue})`, total));
      rows.push(this.createTaxRateRow(`TAX RATE`));
    }
    if (this.containerTypes.length === 0) {
      rows.push(this.createTotalRow(`TOTAL (${indexValue})`, total));
    }
  }

  private createTaxRateRow(title: string): entity.XLSXRow {
    let taxRateRow: entity.XLSXRow = {
      dataType: 'complex',
      rowHeight: 2,
      cells: [
        {
          value: title,
          colStart: 0,
          style: CELL_STYLES.BOLD
        },
        {
          value: '%',
          colStart: 1,
          style: CELL_STYLES.CENTER
        },
        {
          value: '',
          colStart: 2,
          colEnd: 4,
          style: CELL_STYLES.CENTER
        },
        {
          value: '',
          dataType: 'currency',
          colStart: 5,
          style: CELL_STYLES.CENTER
        },
        {
          value: '',
          colStart: 6,
          colEnd: this.maxColumn,
          style: {
            border: true,
          },
        },
      ]
    }
    return taxRateRow;
  }

  private createTotalRow(title: string, value: number): entity.XLSXRow {
    this.totalQuoteCurrMap['USD'] = (this.totalQuoteCurrMap['USD'] || 0) + value;

    let totalRow: entity.XLSXRow = {
      dataType: 'complex',
      rowHeight: 2,
      cells: [
        {
          value: title,
          colStart: 0,
          style: CELL_STYLES.BOLD
        },
        {
          value: 'USD',
          colStart: 1,
          style: CELL_STYLES.CENTER
        },
        {
          value: '',
          colStart: 2,
          colEnd: 4,
          style: CELL_STYLES.CENTER
        },
        {
          value: value || 0,
          dataType: 'currency',
          colStart: 5,
          style: CELL_STYLES.BOLD_CENTER
        },
        {
          value: '',
          colStart: 6,
          colEnd: this.maxColumn,
          style: {
            border: true,
          },
        }
      ]
    }
    return totalRow;
  }

}
