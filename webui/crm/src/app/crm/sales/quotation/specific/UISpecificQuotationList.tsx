import React from 'react';
import * as FeatherIcon from "react-feather";

import { util, grid, sql, bs, app, entity, input } from '@datatp-ui/lib';

import { T } from '../../backend'
import { SQuotationCreation, UIQuotationUtils } from '../QuotationUtils';
import {
  buildTooltipValues,
  onShowUIRequestPricing,
  PricingRequestStatusUtils
} from 'app/crm/price';
import {
  BULK_CARGO_INQUIRY_ENTITY_NAME,
  UIMailBulkCargoRequestPricing
} from 'app/crm/price/request/UIBulkCargoInquiryRequest';
import { UIBookingUtils } from '../../booking/BookingUtils';
import { responsiveGridConfig } from '../../common';
import {
  TransportationMode,
  TransportationTool,
  mapToTypeOfShipment
} from 'app/crm/common';
import { UIBooking } from '../../booking/UIBooking';

const SESSION = app.host.DATATP_SESSION;

type RecordType = 'INQUIRY_REQUEST' | 'BULK_CARGO_INQUIRY_REQUEST';

export class UISpecificQuotationPlugin extends entity.DbEntityListPlugin {

  constructor(space: 'User' | 'Company' | 'System') {
    super([]);

    this.backend = {
      context: 'company',
      service: 'QuotationService',
      searchMethod: 'searchSpecificQuotations',
    }

    const today = new Date();
    let requestDateRange = new util.TimeRange();
    requestDateRange.fromSetDate(today).fromSubtract(2, "month").fromStartOf("month"); // -3 months
    requestDateRange.toSetDate(today).toEndOf("month");

    this.searchParams = {
      params: { "space": space, },
      filters: [
        ...sql.createSearchFilter()
      ],
      optionFilters: [
        sql.createStorageStateFilter([
          entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED
        ]),
      ],
      rangeFilters: [
        ...sql.createDateTimeFilter("requestDate", T("Request Date"), requestDateRange),
      ],
      maxReturn: 1000
    }

  }

  withPinned(pinned: boolean = true) {
    if (pinned) this.addSearchParam('isFavorite', pinned);
    else this.addSearchParam('isFavorite', undefined);
    return this
  }


  withSalemanAccountId(accountId: number) {
    this.addSearchParam('salemanAccountId', accountId);
    return this;
  }

  withRequestDate(fromValue: string, toValue: string) {
    let startOfFromDate: Date | null = null;
    let endOfToDate: Date | null = null;

    if ((fromValue || '').trim()) {
      let fromDate: Date = util.TimeUtil.parseCompactDateTimeFormat(fromValue);
      startOfFromDate = new Date(fromDate.getFullYear(), fromDate.getMonth(), fromDate.getDate());
    }

    if ((toValue || '').trim()) {
      let toDate: Date = util.TimeUtil.parseCompactDateTimeFormat(toValue);
      endOfToDate = new Date(toDate.getFullYear(), toDate.getMonth(), toDate.getDate(), 23, 59, 59, 999);
    }

    if (this.searchParams) {
      let rangeFilters = this.searchParams.rangeFilters;
      if (rangeFilters) {
        for (let i = 0; i < rangeFilters.length; i++) {
          let filter = rangeFilters[i];
          if (filter.name === 'requestDate') {
            filter.fromValue = startOfFromDate ? util.TimeUtil.javaCompactDateTimeFormat(startOfFromDate) : fromValue;
            filter.toValue = endOfToDate ? util.TimeUtil.javaCompactDateTimeFormat(endOfToDate) : toValue;
            break;
          }
        }
      }
      this.searchParams.rangeFilters = rangeFilters;
    }
    return this;
  }

  loadData(uiList: entity.DbEntityList<any>) {
    this.createBackendSearch(uiList, { params: this.searchParams }).call();
  }
}

interface UISpecificQuotationListProps extends entity.DbEntityListProps {
  space: 'User' | 'Company' | 'System'
}
export class UISpecificQuotationList extends entity.DbEntityList<UISpecificQuotationListProps> {

  createVGridConfig(): grid.VGridConfig {
    let { pageContext, space } = this.props;

    let writeCap = pageContext.hasUserWriteCapability();
    let moderatorCap = pageContext.hasUserModeratorCapability();

    const copyAction = {
      name: 'resend-request', hint: T('Resend Request'), icon: FeatherIcon.ExternalLink,
      customRender: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
        let uiList = ctx.uiRoot as UISpecificQuotationList;
        let type: RecordType = dRecord.record['type'];
        if (type == 'BULK_CARGO_INQUIRY_REQUEST') return null;
        return (
          <bs.Button laf='link' className={`px-1`} onClick={() => uiList.onResendRequest(dRecord)}>
            <bs.Tooltip tooltip='Resend Request'>
              <FeatherIcon.ExternalLink size={12} />
            </bs.Tooltip>
          </bs.Button>
        );
      },
    }

    const tooltipFields = [
      { key: 'note', label: 'Note' },
      { key: 'feedback', label: 'Feedback' },
      { key: 'pricingNote', label: 'Pricing Note' },
    ];

    const renderTooltipAdvanced = (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
      const record = dRecord.record;
      const val = record[field.name] || 'N/A';

      let mailTo = (record['mailTo'] || '').split(",").map((item: string) => item.trim()).join("\n");
      record['mailTo'] = mailTo

      const { htmlFormat, textFormat } = buildTooltipValues(record, tooltipFields)

      const handleClick = () => {
        navigator.clipboard.writeText(textFormat);
        bs.toastShow('Copied to clipboard!', { type: 'success' });
      };

      return (
        <bs.CssTooltip width={400} position='bottom-right' offset={{ x: 380, y: 0 }}>
          <bs.CssTooltipToggle>
            <div className='flex-hbox' onClick={handleClick}>
              {field.fieldDataGetter ? field.fieldDataGetter(record) : field.format ? field.format(val) : val}
            </div>
          </bs.CssTooltipToggle>
          <bs.CssTooltipContent>
            {htmlFormat}
          </bs.CssTooltipContent>
        </bs.CssTooltip>
      );
    }

    let config: grid.VGridConfig = {
      record: {
        dataCellHeight: 40,
        control: {
          width: 30,
          items: writeCap && space === 'User' ? [copyAction] : [],
        },
        fields: [
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          { name: 'code', label: T(`Ref`), width: 120, container: 'fixed-left', filterable: true },
          {
            name: 'clientLabel', label: T('Customer/ Lead'), width: 220, filterable: true,
            fieldDataGetter(record) {
              let val = record['clientLabel'] || 'N/A';
              return util.text.formater.uiTruncate(val, 200, true);
            },
            customRender: renderTooltipAdvanced
          },
          {
            name: "salemanBranchName", label: 'Saleman Branch.', width: 140,
            filterable: true, state: { visible: space !== 'User' },
            customRender: renderTooltipAdvanced
          },
          {
            name: "salemanLabel", label: 'Saleman.', width: 220, filterable: true, state: { visible: space !== 'User' },
            fieldDataGetter(record) {
              let val = record['salemanLabel'] || 'N/A';
              return util.text.formater.uiTruncate(val, 200, true);
            },
            customRender: renderTooltipAdvanced
          },
          {
            name: 'typeOfShipment', label: T('Type of shpt'), width: 200, filterable: true,
            fieldDataGetter(record: any) {
              if (record['typeOfShipment'] === '-') return record['typeOfShipment'];
              return mapToTypeOfShipment(record['purpose'], record['mode'])
            },
            customRender: renderTooltipAdvanced
          },
          {
            name: 'requestDate', label: T(`Req. Date`), width: 110, format: util.text.formater.compactDate,
            filterable: true, filterableType: 'date',
            fieldDataGetter(record: any) {
              let val: string = record['requestDate']
              return `${val ? util.text.formater.compactDate(val) : ''}`
            },
            customRender: renderTooltipAdvanced
          },
          {
            name: 'cargoReadyDate', label: T(`CRD.`), width: 110, format: util.text.formater.compactDate,
            filterable: true, filterableType: 'date',
            fieldDataGetter(record: any) {
              let val: string = record['cargoReadyDate']
              return `${val ? util.text.formater.compactDate(val) : ''}`
            },
            customRender: renderTooltipAdvanced
          },
          {
            name: 'termOfService', label: T(`Term.`), width: 90, filterable: true, filterableType: 'string',
            customRender: renderTooltipAdvanced
          },
          {
            name: 'fromLocationLabel', label: T('From Location'), width: 200, filterable: true, filterableType: 'string',
            fieldDataGetter(record) {
              let val = record['fromLocationLabel'] || 'N/A';
              return util.text.formater.uiTruncate(val, 200, true);
            },
            customRender: renderTooltipAdvanced
          },
          {
            name: 'toLocationLabel', label: T('To Location'), width: 200, filterable: true, filterableType: 'string',
            fieldDataGetter(record) {
              let val = record['toLocationLabel'] || 'N/A';
              return util.text.formater.uiTruncate(val, 200, true);
            },
            customRender: renderTooltipAdvanced
          },
          {
            name: 'finalDestination', label: T('Final Dest'), width: 200, filterable: true, filterableType: 'string',
            fieldDataGetter(record) {
              let val = record['finalDestination'] || '';
              return util.text.formater.uiTruncate(val, 200, true);
            },
            customRender: renderTooltipAdvanced
          },
          {
            name: 'volumeInfo', label: T(`Vol`), width: 140,
            fieldDataGetter(record: any) {
              let val: string = record['volumeInfo']
              if (val) return val;
              let mode: TransportationMode = record['mode']
              if (TransportationTool.isAir(mode)) return record['grossWeightKg'] + ' KGS'
              if (TransportationTool.isSeaLCL(mode)) return record['volumeCbm'] + ' CBM'
            },
            customRender: renderTooltipAdvanced
          },
          {
            name: 'feedback', label: T(`Feedback`), width: 270,
            style: { height: 40, fontSize: '0.9rem', }, cssClass: 'fw-bold text-warning',
            editor: {
              type: 'string',
              enable: (space === 'User' && writeCap) || moderatorCap,
              onInputChange: (ctx: grid.FieldContext, oldVal: any, newVal: any) => {
                let record: any = ctx.displayRecord.record;
                if (newVal !== oldVal) {
                  let event: grid.VGridCellEvent = {
                    row: record.row,
                    field: ctx.fieldConfig,
                    event: 'Modified'
                  }
                  this.vgridContext.broadcastCellEvent(event);

                  let modified = {
                    id: record['id'],
                    feedback: newVal,
                    status: record['status'],
                    pricingAccountId: record['pricingAccountId'],
                    salemanAccountId: record['salemanAccountId'],
                    entityName: record['entityName']
                  };
                  this.handleOnUpdateFeedback(modified);
                }
              },
            }
          },
          { name: 'pricingNote', label: T(`Pricing Note`), width: 270 },
          {
            name: 'modifiedTime', label: T('Modified Time'), width: 170,
            filterableType: 'Date', filterable: true,
            fieldDataGetter: (record: any) => {
              if (record['modifiedTime'] === '-') return record['modifiedTime'];
              return util.text.formater.compactDateTime(record['modifiedTime']);
            },
          },
          {
            name: 'action', label: T(`Actions`), width: 100, container: 'fixed-right',
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              const quotationId = record['quotationId'];
              const bookingId = record['bookingId'];
              return (
                <div className='flex-hbox justify-content-between align-items-center flex-grow-0 gap-1 pe-1'>

                  {/* Quotation Actions */}
                  <bs.Button laf={quotationId ? 'info' : 'secondary'} outline className='border-0 py-1 px-1'
                    title={T('Edit Quotation')}
                    onClick={() => this.onViewQuotation(dRecord)}>
                    <bs.CssTooltip width={150} position='top-left' offset={{ x: -150, y: 0 }}>
                      <bs.CssTooltipToggle className="w-100">
                        <FeatherIcon.Edit size={15} className="me-1" />
                      </bs.CssTooltipToggle>
                      <bs.CssTooltipContent className="rounded shadow-sm">
                        <div className="p-1" style={{ maxHeight: '250px' }}>
                          {`${quotationId ? T('Edit Quotation') : T('Create Quotation')}`}
                        </div>
                      </bs.CssTooltipContent>
                    </bs.CssTooltip>
                  </bs.Button>

                  <bs.Button laf={quotationId ? 'info' : 'secondary'} outline className='border-0 py-1 px-1'
                    title={T('Copy Quotation')}
                    onClick={() => this.onCopyQuotation(dRecord)}>
                    <bs.CssTooltip width={200} position='top-left' offset={{ x: -150, y: 0 }}>
                      <bs.CssTooltipToggle className="w-100">
                        <FeatherIcon.Copy size={15} className="me-1" />
                      </bs.CssTooltipToggle>
                      <bs.CssTooltipContent className="rounded shadow-sm">
                        <div className="p-1" style={{ maxHeight: '250px' }}>
                          {`Save as Quotation`}
                        </div>
                      </bs.CssTooltipContent>
                    </bs.CssTooltip>
                  </bs.Button>

                  {/* Booking Actions */}
                  <bs.Button laf={bookingId ? 'info' : 'secondary'} outline className='border-0 py-1 px-1'
                    title={bookingId ? T('View IB') : T('Create IB')}
                    onClick={() => this.onViewBooking(dRecord)}>
                    <bs.CssTooltip width={170} position='top-left' offset={{ x: -150, y: 0 }}>
                      <bs.CssTooltipToggle className="w-100">
                        <FeatherIcon.ShoppingBag size={15} className="me-1" />
                      </bs.CssTooltipToggle>
                      <bs.CssTooltipContent className="rounded shadow-sm">
                        <div className="p-1" style={{ maxHeight: '250px' }}>
                          {`${bookingId ? T('View IB') : T('Create IB')}`}
                        </div>
                      </bs.CssTooltipContent>
                    </bs.CssTooltip>

                  </bs.Button>

                </div >
              );
            }
          },
          {
            name: 'status', label: T('Status'), width: 120,
            filterable: true, container: 'fixed-right',
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                let _rowRecord = cell.getDisplayRecord().record;
                let fieldName: string = event.field.name;
                if (fieldName === 'feedback') {
                  cell.forceUpdate()
                }
              },
            },
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;

              let currentStatus = PricingRequestStatusUtils.getStatusInfo(record['status']);
              let StatusIcon = currentStatus.icon;
              let label = currentStatus.label;
              let color = currentStatus.color;

              const statusList = PricingRequestStatusUtils.getPricingRequestStatusList();
              const statusRemaining = statusList.filter(status =>
                status.value !== record['status'] &&
                status.value !== 'IN_PROGRESS' &&
                status.value !== 'DONE'
              );

              return (
                <bs.Popover className="d-flex flex-center w-100 h-100" key={util.IDTracker.next()}
                  title={T('Status')} closeOnTrigger=".btn" >
                  <bs.PopoverToggle
                    className={`flex-hbox flex-center px-2 py-2 rounded-2 bg-${color}-subtle text-${color} w-100 `}>
                    <StatusIcon size={14} className="me-1" />
                    <span>{label}</span>
                  </bs.PopoverToggle>
                  <bs.PopoverContent>
                    <div className='flex-vbox gap-2' style={{ width: '200px' }}>
                      {statusRemaining.map(opt => {
                        let OptIcon = opt.icon;
                        return (
                          <div key={opt.value}
                            className={`d-flex flex-center px-2 py-1 rounded-2 bg-${opt.color}-subtle text-${opt.color} w-100 cursor-pointer`}
                            onClick={() => this.handleOnChangeStatus({
                              id: record['id'],
                              status: opt.value,
                              feedback: record['feedback'],
                              pricingAccountId: record['pricingAccountId'],
                              salemanAccountId: record['salemanAccountId']
                            })}>
                            <OptIcon size={14} className="me-1" />
                            <span>{opt.label}</span>
                          </div>
                        );
                      })}
                    </div>
                  </bs.PopoverContent>
                </bs.Popover>
              );
            }
          },

        ],
      },
      toolbar: {
        hide: true,
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: { viewMode: 'table' },
        }
      },
    };
    return responsiveGridConfig(config);
  }


  private checkRecordsPermission(record: any): boolean {
    const { pageContext } = this.props;

    const moderatorCap = pageContext.hasUserModeratorCapability();
    const writeCap = pageContext.hasUserWriteCapability();

    const salemanAccountId = record['salemanAccountId'];
    const hasWriteAccess = moderatorCap || (writeCap && (SESSION.getAccountId() === salemanAccountId));
    if (!hasWriteAccess) {
      bs.dialogShow(
        T("Access Denied"),
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />
          {T("You can only perform action on your own quotation.")}
        </div>,
        { backdrop: 'static', size: 'md' }
      );
      return false;
    }
    return true;
  }

  saveChangeRequest = (modified: any) => {
    let { appContext } = this.props;
    if (!this.checkRecordsPermission(modified)) return;

    this.onAddOrModifyDBRecordCallback(modified);

    const backendCall = (component: string, endpoint: string, requests: Array<any>) => {
      appContext.createHttpBackendCall(component, endpoint, { records: requests })
        .withSuccessData((_data: any) => {
          appContext.addOSNotification("success", T(`Auto save modified records success!!!`));
          this.vgridContext.getVGrid().forceUpdateView();
        })
        .call();
    }

    if (modified['entityName'] === BULK_CARGO_INQUIRY_ENTITY_NAME) {
      backendCall('TransportPriceMiscService', 'saveBulkCargoInquiryRequestRecords', [modified]);
    } else {
      backendCall('TransportPriceMiscService', 'saveInquiryRequestRecords', [modified]);
    }
  }

  handleOnUpdateFeedback(request: any) {
    const { pageContext } = this.props;

    let status = request['status'];
    if (status === 'PRICE_MISMATCH' || status === 'RESPONDED' || status === 'SUCCESS' || status === 'REJECTED') {
      this.saveChangeRequest(request);
      return;
    }

    const statusList = PricingRequestStatusUtils.getPricingRequestStatusList();
    const statusRemaining = statusList.filter(status =>
      ['PRICE_MISMATCH', 'RESPONDED', 'SUCCESS', 'REJECTED',].includes(status.value)
    );

    let createAppPage = (_appCtx: app.AppContext, pageCtx: app.PageContext) => {

      return (
        <div className='flex-vbox' style={{ minHeight: '100px', maxHeight: '200px' }}>
          <input.BBTextField bean={request} field={'feedback'} style={{ height: '8em' }} />
          <div className='flex-hbox justify-content-between align-items-center flex-grow-0 gap-2 border-top p-1'>
            {statusRemaining.map(opt => {
              let OptIcon = opt.icon;
              return (
                <bs.Button laf='warning' className={`border-0 py-2 px-1 w-100 bg-${opt.color}-subtle text-${opt.color}`}
                  onClick={() => {
                    pageCtx.back();
                    let modified: any = { ...request, status: opt.value }
                    this.handleOnChangeStatus(modified)
                  }}>
                  <OptIcon size={14} className="me-1" />
                  <span>{opt.label}</span>
                </bs.Button>
              );
            })}
          </div>
        </div>
      );
    }
    pageContext.createPopupPage('update-feedback', T('Update Feedback'), createAppPage, { size: 'md' });
  }

  handleOnChangeStatus(request: any) {
    if (!request) return;
    let status = request['status'];

    const { pageContext } = this.props;

    this.saveChangeRequest(request);
    // Check if feedback is required for the new status
    if (status !== 'SUCCESS' && status !== 'NO_RESPONSE' && status !== 'DONE') {
      if (!request.feedback) {

        let createAppPage = (_appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (
            <div className='flex-vbox' style={{ minHeight: '100px', maxHeight: '200px' }}>
              <input.BBTextField bean={request} field={'feedback'} style={{ height: '8em' }} onInputChange={(bean: any, field: string, oldVal: any, newVal: any) => {
                let modified: any = { ...request, feedback: newVal }
                this.handleOnChangeStatus(modified)
              }} />
              <div className='flex-hbox justify-content-end align-items-center flex-grow-0 gap-2 border-top p-1'>
                <bs.Button laf='info' className='flex-hbox-grow-0 p-1 mx-1'
                  onClick={() => {
                    let modified: any = { ...request }
                    this.handleOnChangeStatus(modified)
                    pageCtx.back();
                  }} >
                  <FeatherIcon.Save size={14} className='me-2' /> {T('Save')}
                </bs.Button>
              </div>
            </div>
          );
        }
        pageContext.createPopupPage('update-feedback', T('Update Feedback'), createAppPage, { size: 'md' });
      }
    }
  }

  onShowBulkCargoInquiryRequest = (record: any) => {
    const { pageContext } = this.props;
    let fullName = SESSION.getAccountAcl().getFullName();
    let companyLabel = SESSION.getAccountAcl().getCompanyAcl().companyLabel;
    let title = `Bulk Cargo Request (${fullName} - ${companyLabel})`;

    const createContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UIMailBulkCargoRequestPricing appContext={appCtx} pageContext={pageCtx}
          observer={new entity.ComplexBeanObserver(record)} onPostCommit={(entity: any) => {
            pageCtx.back();
            this.nextViewId();
            this.reloadData();
          }} />
      )
    }
    pageContext.createPopupPage('create-cargo-inquiry-request', T(title), createContent, { size: 'flex-lg' })

  }

  onLoadBulkCargoInquiryRequest = (inquiryId?: number) => {
    let { appContext } = this.props;

    if (!inquiryId) {
      let request = {
        cargoType: 'BULK',
        unit: 'TNE'
      }
      appContext.createHttpBackendCall('TransportPriceMiscService', 'initBulkCargoInquiryRequest', { request: request })
        .withSuccessData((data: any) => {
          this.onShowBulkCargoInquiryRequest(data);
        })
        .withFail(() => {
          this.onShowBulkCargoInquiryRequest({});
        })
        .call();
      return;
    }

    appContext
      .createHttpBackendCall('TransportPriceMiscService', 'getBulkCargoInquiryRequest', { 'requestId': inquiryId })
      .withSuccessData((request: any) => {
        let newRequest: any = {
          ...request,
          status: 'IN_PROGRESS',
          id: undefined,
          code: undefined,
          mailSubject: undefined,
          mailMessage: undefined,
          pricingDate: undefined,
          pricingAccountId: undefined,
          pricingLabel: undefined,
          feedback: '',
          pricingNote: undefined,
          shipmentDetail: {
            ...request.shipmentDetail,
            laydaysDate: util.TimeUtil.javaCompactDateTimeFormat(new Date()),
          },
          requestDate: util.TimeUtil.javaCompactDateTimeFormat(new Date())
        }
        this.onShowBulkCargoInquiryRequest(newRequest);
      })
      .call();
  }

  onResendRequest(dRecord: grid.DisplayRecord) {
    const { appContext } = this.props;
    let entity: any = dRecord.record;
    let entityName = entity['entityName'];

    if (entityName == BULK_CARGO_INQUIRY_ENTITY_NAME) {
      this.onLoadBulkCargoInquiryRequest(entity['id']);
      return;
    }
    const onPostCommit = (_entity: any) => {
      this.reloadData();
      this.nextViewId();
      this.vgridContext.getVGrid().forceUpdateView();
    }
    appContext
      .createHttpBackendCall('TransportPriceMiscService', 'getInquiryRequest', { 'requestId': entity['id'] })
      .withSuccessData((request: any) => {
        let newRequest: any = {
          ...request,
          salemanBranchName: SESSION.getCurrentCompanyContext()?.companyLabel,
          id: undefined,
          code: undefined,
          mailSubject: undefined,
          mailMessage: undefined,
          pricingDate: undefined,
          pricingAccountId: undefined,
          pricingLabel: undefined,
          status: 'IN_PROGRESS',
          feedback: '',
          totalNewPricesCount: 0,
          totalAnalysisPricesCount: 0,
          stepTracking: 0,
          totalStepCounting: 0,
          pricingNote: undefined,
          laydaysDate: util.TimeUtil.javaCompactDateTimeFormat(new Date()),
          requestDate: util.TimeUtil.javaCompactDateTimeFormat(new Date())
        }
        onShowUIRequestPricing(this, newRequest, onPostCommit);
      })
      .call();
  }

  onDefaultSelect(dRecord: grid.DisplayRecord) { }

  /* -------------- Quotation Action ----------------- */
  onViewQuotation(dRecord: grid.DisplayRecord) {
    let quotation = dRecord.record;
    if (!this.checkRecordsPermission(quotation)) return;
    let quotationId = quotation['quotationId'];

    if (!quotationId) {
      let quotationCreator: SQuotationCreation = {
        inquiry: undefined,
        priceReferenceIds: [],
        isAutoSaved: false,
        inquiryRequestId: quotation['id']
      }
      UIQuotationUtils.createNewSpecificQuotation(this, quotationCreator);
    } else {
      UIQuotationUtils.showUISQuotationById(this, quotationId);
    }
  }

  onCopyQuotation(dRecord: grid.DisplayRecord) {
    const { appContext } = this.props;
    let record = dRecord.record;
    if (!this.checkRecordsPermission(record)) return;
    let quotationId = record['quotationId'];
    if (!quotationId) {
      bs.dialogShow(
        "Action Unavailable",
        <div className="text-warning fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />
          This action cannot be performed right now. Quotation ID not found.
        </div>,
        { backdrop: 'static', size: 'md' }
      );
      return;
    }
    appContext.createHttpBackendCall('QuotationService', 'copySpecificQuotation', { quotationId: quotationId })
      .withSuccessData((data: any) => {
        appContext.addOSNotification('success', "Save as quotation success!!!");
        UIQuotationUtils.showUISpecificQuotation(this, data);
      })
      .call();
  }

  /* -------------- Booking Action ----------------- */
  onViewBooking(dRecord: grid.DisplayRecord) {
    let record = dRecord.record;
    if (!this.checkRecordsPermission(record)) return;
    let bookingId = record['bookingId'];
    let quotationId = record['quotationId'];
    const referenceCode = record['code'];
    const { appContext, pageContext, } = this.props;

    const onPostCommit = (_entity: any, _uiEditor?: app.AppComponent) => {
      this.reloadData();
      this.nextViewId();
      this.vgridContext.getVGrid().forceUpdateView();
    }

    if (bookingId) {
      appContext.createHttpBackendCall("BookingService", "getBooking", { bookingId: bookingId })
        .withSuccessData((booking: any) => {
          let inquiry = booking['inquiry'] || {};
          const type = mapToTypeOfShipment(inquiry['purpose'], inquiry['mode'])
          let title = `IBooking: ${referenceCode || type}`;

          const createPageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
            return (
              <UIBooking appContext={appCtx} pageContext={pageCtx}
                observer={new entity.ComplexBeanObserver(booking)} onPostCommit={onPostCommit} />
            );
          }
          pageContext.createPopupPage(`booking-${referenceCode}`, title, createPageContent, { size: 'xl', backdrop: 'static' });
        })
        .call();
    } else if (quotationId) {
      appContext.createHttpBackendCall("QuotationService", "getSpecificQuotationById", { id: quotationId })
        .withSuccessData((quotation: any) => {
          UIBookingUtils.onNewBooking(appContext, pageContext, quotation, onPostCommit);
        })
        .call()
    } else {
      bs.dialogShow(
        "Action Unavailable",
        <div className="text-warning fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />
          This action cannot be performed right now. Create a quotation first.
        </div>,
        { backdrop: 'static', size: 'md' }
      );
      return;
    }
  }

  render(): React.JSX.Element {
    if (this.isLoading()) return this.renderLoading();
    return this.renderUIGrid();
  }
}
