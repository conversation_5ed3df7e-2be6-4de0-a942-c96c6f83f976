import React from 'react';
import { app, bs, server, entity, util } from '@datatp-ui/lib';

import { T } from '../backend';

import { UISpecificQuotation } from "./specific/UISpecificQuotation";
import { QuoteExportRequest, } from "./specific/xlsx/UIQuotationExportUtil";
import { SQuotationExportProcessor } from "./specific/xlsx/UIQuotationExportUtil";
import { LOCAL_CHARGES } from './QuotationConfigDialog';

import { ImportExportPurpose, PurposeTool, TransportationMode, TransportationTool } from 'app/crm/common';
import { ContainerType, ContainerTypeUnit } from 'app/crm/common/ContainerTypeUtil';
export interface SQuotationCreation {
  priceReferenceIds: Array<number>;
  inquiryRequestId: number | undefined;
  isAutoSaved: boolean;
  inquiry: any;
}

export class UIQuotationUtils {
  static showUISQuotationById(ui: app.AppComponent, quotationId: number, popup: boolean = false) {
    const { appContext } = ui.props;
    appContext.createHttpBackendCall("QuotationService", "getSpecificQuotationById", { id: quotationId })
      .withSuccessData((quotation: any) => {
        this.showUISpecificQuotation(ui, quotation, '', popup);
      })
      .call()
  }

  static showUISpecificQuotation(ui: bs.BaseComponent, quotation: any, ref: string = '', popup: boolean = false): void {
    let uiAppComp = (ui as app.AppComponent);
    const { pageContext, readOnly } = uiAppComp.props;
    let observer = new entity.ComplexBeanObserver(quotation);
    if (!ref) {
      ref = (quotation['inquiry'] || {})['referenceCode'] || 'NEW QUOTATION';
    }

    let createPageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (<UISpecificQuotation appContext={appCtx} pageContext={pageCtx} readOnly={readOnly} observer={observer} />);
    }
    let pageId = `squotation-detail-${util.IDTracker.next()}`;
    if (popup) {
      pageContext.createPopupPage(pageId, T(`SQuotation : ${ref}`), createPageContent, { size: 'xl', backdrop: 'static' });
    } else {
      pageContext.addPageContent(pageId, `${ref}`, createPageContent);
    }
  }

  static doExportQuoteAsXlsx(ui: bs.BaseComponent, request: QuoteExportRequest) {
    const { appContext } = (ui as app.AppComponent).props;
    appContext.createHttpBackendCall("QuotationDocumentService", "exportQuotationQuoteAsXlsx", { req: request })
      .withSuccessData((storeInfo: any) => {
        if (storeInfo.privateMode) {
          entity.StoreInfo.privateDownload(storeInfo);
        } else {
          entity.StoreInfo.download(storeInfo.url);
        }
      })
      .withFail((response?: server.BackendResponse) => {
        bs.notificationShow("danger", T("Export Quotation Failed!!"), response?.error.message);
        return;
      })
      .call();
  }

  static createExportRequest(uiQuotation: entity.AppDbComplexEntityEditor): QuoteExportRequest {
    const { observer } = uiQuotation.props;
    let quotation = observer.getMutableBean();
    let processor = new SQuotationExportProcessor(quotation);
    return processor.request;
  }

  static createNewSpecificQuotationForBulkCargo(ui: app.AppComponent, template: SQuotationCreation, popup: boolean = false, successCb?: (quotation: any) => void): void {
    const { appContext } = ui.props;
    appContext.createHttpBackendCall('QuotationService', 'newSpecificQuotationForBulkCargo', { template: template })
      .withSuccessData((quotation: any) => {
        //TODO: Dan - hardcode to fix bugs
        appContext.addOSNotification('success', "Create quotation success!!!");
        if (successCb) {
          successCb(quotation);
        } else {
          let inquiry: any = quotation['inquiry'];
          let mode: TransportationMode = inquiry['mode'];
          let purpose: 'IMPORT' | 'EXPORT' = inquiry['purpose'];

          if (TransportationTool.isSea(mode)) {
            const key = mode + '_' + purpose;
            const localCharges: any[] = LOCAL_CHARGES[key]
              .map(charge => ({
                ...charge,
                mode,
                currency: 'USD',
                quantity: '',
                unitPrice: '',
                totalAmount: '',
                note: '',
                quoteRate: {}
              }));
            let existingLocalCharges: any[] = quotation['localHandlingCharges'] || [];
            const existingCodes = new Set(existingLocalCharges.map(charge => charge.code));
            const newLocalCharges = localCharges.filter(charge => !existingCodes.has(charge.code));
            quotation['localHandlingCharges'] = [...existingLocalCharges, ...newLocalCharges];
          }
          UIQuotationUtils.showUISpecificQuotation(ui, quotation, '', popup);
        }
      })
      .withFail((response: server.BackendResponse) => {
        let title = T('Create Quotation Failed!');
        appContext.addOSNotification('danger', title, response.error, response);
        let message = response.error.message;
        if (message) {
          bs.notificationShow("danger", title, <div className='text-danger'>{message}</div>);
        }
        return;
      })
      .call();
  }

  static createNewSpecificQuotation(ui: app.AppComponent, template: SQuotationCreation, popup: boolean = false, successCb?: (quotation: any) => void): void {
    const { appContext } = ui.props;
    appContext.createHttpBackendCall('QuotationService', 'newSpecificQuotation', { template: template })
      .withSuccessData((quotation: any) => {
        //TODO: Dan - hardcode to fix bugs
        appContext.addOSNotification('success', "Create quotation success!!!");
        if (successCb) {
          successCb(quotation);
        } else {
          let inquiry: any = quotation['inquiry'];
          let mode: TransportationMode = inquiry['mode'];
          let purpose: 'IMPORT' | 'EXPORT' = inquiry['purpose'];

          if (TransportationTool.isSea(mode)) {
            const key = mode + '_' + purpose;
            const localCharges: any[] = LOCAL_CHARGES[key]
              .map(charge => ({
                ...charge,
                mode,
                currency: 'USD',
                quantity: '',
                unitPrice: '',
                totalAmount: '',
                note: '',
                quoteRate: {}
              }));
            let existingLocalCharges: any[] = quotation['localHandlingCharges'] || [];
            const existingCodes = new Set(existingLocalCharges.map(charge => charge.code));
            const newLocalCharges = localCharges.filter(charge => !existingCodes.has(charge.code));
            quotation['localHandlingCharges'] = [...existingLocalCharges, ...newLocalCharges];
          }
          UIQuotationUtils.showUISpecificQuotation(ui, quotation, '', popup);
        }
      })
      .withFail((response: server.BackendResponse) => {
        let title = T('Create Quotation Failed!');
        appContext.addOSNotification('danger', title, response.error, response);
        let message = response.error.message;
        if (message) {
          bs.notificationShow("danger", title, <div className='text-danger'>{message}</div>);
        }
        return;
      })
      .call();
  }

  static doExportQuotationTemplate = (sQuotation: any): entity.DataListExportModel => {
    const quoteList: any[] = sQuotation['quoteList'] || [];
    let mode: TransportationMode = quoteList[0]['mode'];
    let inquiry: any = sQuotation['inquiry'];
    let containers = inquiry['containers'] || [];

    let priceFields: any = {
      LCL: [
        { label: 'Minimum (< 1 CBM)', name: 'refFreightChargeLCL', dataType: 'currency' },
        { label: '< 2 CBM', name: 'refLess2CbmPrice', dataType: 'currency' },
        { label: '< 3 CBM', name: 'refLess3CbmPrice', dataType: 'currency' },
        { label: '< 5 CBM', name: 'refLess5CbmPrice', dataType: 'currency' },
        { label: '< 7 CBM', name: 'refLess7CbmPrice', dataType: 'currency' },
        { label: '< 10 CBM', name: 'refLess10CbmPrice', dataType: 'currency' },
        { label: '< 15 CBM', name: 'refGeq10CbmPrice', dataType: 'currency' },
      ],
      AIR: [
        { label: 'Min', name: 'refMinPrice', dataType: 'currency' },
        { label: '-45(KG)', name: 'refLevel1Price', dataType: 'currency' },
        { label: '+45(KG)', name: 'refLevel2Price', dataType: 'currency' },
        { label: '+100(KG)', name: 'refLevel3Price', dataType: 'currency' },
        { label: '+300(KG)', name: 'refLevel4Price', dataType: 'currency' },
        { label: '+500(KG)', name: 'refLevel5Price', dataType: 'currency' },
        { label: '+1000(KG)', name: 'refLevel6Price', dataType: 'currency' },
      ]
    };

    let FCL: any[] = []
    for (let cont of containers) {
      let containerType: string = (cont['containerType'] || '').trim();
      let type: ContainerType | undefined = ContainerTypeUnit.match(containerType);
      if (type && type.toFCLPriceLevel()) {
        let fclLevel = type.toFCLPriceLevel();
        let costFieldName = fclLevel ? `ref${fclLevel.charAt(0).toUpperCase() + fclLevel.slice(1)}` : '';
        let commissionFieldName = fclLevel ? `refCommission${fclLevel.charAt(0).toUpperCase() + fclLevel.slice(1)}` : '';
        let profitFieldName = `${fclLevel}Profit`
        let marginFieldName = `${fclLevel}Margin`

        FCL.push({ label: `${type.name} (Cost)`, name: costFieldName, dataType: 'currency' })
        FCL.push({ label: `${type.name} (KB)`, name: commissionFieldName, dataType: 'currency' })
        FCL.push({ label: `${type.name} (Sell)`, name: fclLevel, dataType: 'currency' })
        FCL.push({ label: `${type.name} (Profit)`, name: profitFieldName, dataType: 'currency' })
        FCL.push({ label: `${type.name} (Margin %)`, name: marginFieldName, dataType: 'string' })
      }
    }
    priceFields['FCL'] = FCL;

    let selectedPriceFields: entity.Field[] = priceFields.NONE_US;

    if (TransportationTool.isAir(mode)) {
      selectedPriceFields = priceFields.AIR;
    } else if (TransportationTool.isSeaLCL(mode)) {
      selectedPriceFields = priceFields.LCL;
    } else if (TransportationTool.isSeaFCL(mode)) {
      selectedPriceFields = priceFields['FCL']
    }


    let selectedGroupField: entity.GroupField = {
      label: "_blank_",
      fields: [
        { label: 'STT.', name: 'stt', dataType: 'number' },
        { label: 'Carrier', name: 'carrierLabel', dataType: 'string' },
        { label: 'POL', name: 'fromLocationLabel', dataType: 'string' },
        { label: 'POD', name: 'toLocationLabel', dataType: 'string' },
        { label: 'Final Destination', name: 'finalDestination', dataType: 'string' },
        { label: 'Curr', name: 'currency', dataType: 'string' },
        ...selectedPriceFields,
        { label: 'Services', name: 'transitTime', dataType: 'string' },
        { label: 'Free Time', name: 'freeTime', dataType: 'string' },
        { label: 'Effective Date', name: 'effectiveDate', dataType: 'date' },
        { label: 'Valid date', name: 'validity', dataType: 'date' },
        { label: 'Remark', name: 'note', dataType: 'string' },
        { label: 'Cost Ref', name: 'referenceCode', dataType: 'string' },
      ],
    };

    let records: Array<any> = [];
    for (let i = 0; i < quoteList.length; i++) {
      let record = quoteList[i];
      let newRecord: any = {
        stt: i + 1,
        carrierLabel: record['carrierLabel'],
        fromLocationLabel: record['fromLocationLabel'],
        toLocationLabel: record['toLocationLabel'],
        finalDestination: record['finalDestination'],
        currency: record['currency'],
        transitTime: record['transitTime'],
        freeTime: record['freeTime'],
        effectiveDate: record['effectiveDate'],
        validity: record['validity'],
        note: record['note'],
        referenceCode: record['referenceCode'],
      };

      if (TransportationTool.isAir(mode)) {
        newRecord.refMinPrice = record.priceGroup?.refMinPrice || '';
        newRecord.refLevel1Price = record.priceGroup?.refLevel1Price || '';
        newRecord.refLevel2Price = record.priceGroup?.refLevel2Price || '';
        newRecord.refLevel3Price = record.priceGroup?.refLevel3Price || '';
        newRecord.refLevel4Price = record.priceGroup?.refLevel4Price || '';
        newRecord.refLevel5Price = record.priceGroup?.refLevel5Price || '';
        newRecord.refLevel6Price = record.priceGroup?.refLevel6Price || '';
      } else if (TransportationTool.isSeaLCL(mode)) {
        newRecord.refFreightChargeLCL = record.priceGroup?.refFreightChargeLCL || '';
        newRecord.refLess2CbmPrice = record.priceGroup?.refLess2CbmPrice || '';
        newRecord.refLess3CbmPrice = record.priceGroup?.refLess3CbmPrice || '';
        newRecord.refLess5CbmPrice = record.priceGroup?.refLess5CbmPrice || '';
        newRecord.refLess7CbmPrice = record.priceGroup?.refLess7CbmPrice || '';
        newRecord.refLess10CbmPrice = record.priceGroup?.refLess10CbmPrice || '';
        newRecord.refGeq10CbmPrice = record.priceGroup?.refGeq10CbmPrice || '';
      } else if (TransportationTool.isSeaFCL(mode)) {
        // Commission fields
        newRecord.refCommissionDry20Price = record.priceGroup?.refCommissionDry20Price || '';
        newRecord.refCommissionDry40Price = record.priceGroup?.refCommissionDry40Price || '';
        newRecord.refCommissionHighCube40Price = record.priceGroup?.refCommissionHighCube40Price || '';
        newRecord.refCommissionHighCube45Price = record.priceGroup?.refCommissionHighCube45Price || '';
        newRecord.refCommissionNor40Price = record.priceGroup?.refCommissionNor40Price || '';
        newRecord.refCommissionReefer20Price = record.priceGroup?.refCommissionReefer20Price || '';
        newRecord.refCommissionReefer40Price = record.priceGroup?.refCommissionReefer40Price || '';

        // Price note fields
        newRecord.refDry20PriceNote = record.priceGroup?.refDry20PriceNote || '';
        newRecord.refDry40PriceNote = record.priceGroup?.refDry40PriceNote || '';
        newRecord.refHighCube40PriceNote = record.priceGroup?.refHighCube40PriceNote || '';
        newRecord.refHighCube45PriceNote = record.priceGroup?.refHighCube45PriceNote || '';
        newRecord.refNor40PriceNote = record.priceGroup?.refNor40PriceNote || '';
        newRecord.refReefer20PriceNote = record.priceGroup?.refReefer20PriceNote || '';
        newRecord.refReefer40PriceNote = record.priceGroup?.refReefer40PriceNote || '';

        // Sell fields (original)
        newRecord.dry20Price = record.priceGroup?.dry20Price || '';
        newRecord.dry40Price = record.priceGroup?.dry40Price || '';
        newRecord.dry45Price = record.priceGroup?.dry45Price || '';
        newRecord.highCube40Price = record.priceGroup?.highCube40Price || '';
        newRecord.highCube45Price = record.priceGroup?.highCube45Price || '';
        newRecord.reefer20Price = record.priceGroup?.reefer20Price || '';
        newRecord.reefer40Price = record.priceGroup?.reefer40Price || '';
        newRecord.isoTank20Price = record.priceGroup?.isoTank20Price || '';
        newRecord.flatRack20Price = record.priceGroup?.flatRack20Price || '';
        newRecord.flatRack40Price = record.priceGroup?.flatRack40Price || '';
        newRecord.openTop20Price = record.priceGroup?.openTop20Price || '';
        newRecord.openTop40Price = record.priceGroup?.openTop40Price || '';
        newRecord.reeferHighCube40Price = record.priceGroup?.reeferHighCube40Price || '';


        // Calculate Profit and Margin for each container type
        // Dry20
        const dry20Commission = parseFloat(record.priceGroup?.refCommissionDry20Price) || 0;
        const dry20Cost = (parseFloat(record.priceGroup?.refDry20Price) || 0) - dry20Commission;
        const dry20Sell = parseFloat(record.priceGroup?.dry20Price) || 0;
        const dry20Profit = dry20Sell - dry20Cost - dry20Commission;
        newRecord.refDry20Price = dry20Cost;
        newRecord.dry20PriceProfit = dry20Sell > 0 ? dry20Profit : '';
        newRecord.dry20PriceMargin = dry20Cost > 0 && dry20Sell > 0 ? `${((dry20Profit / dry20Cost) * 100).toFixed(1)}%` : '';

        // Dry40
        const dry40Commission = parseFloat(record.priceGroup?.refCommissionDry40Price) || 0;
        const dry40Cost = (parseFloat(record.priceGroup?.refDry40Price) || 0) - dry40Commission;
        const dry40Sell = parseFloat(record.priceGroup?.dry40Price) || 0;
        const dry40Profit = dry40Sell - dry40Cost - dry40Commission;
        newRecord.refDry40Price = dry40Cost;
        newRecord.dry40PriceProfit = dry40Sell > 0 ? dry40Profit : '';
        newRecord.dry40PriceMargin = dry40Cost > 0 && dry40Sell > 0 ? `${((dry40Profit / dry40Cost) * 100).toFixed(1)}%` : '';

        // HighCube40
        const highCube40Commission = parseFloat(record.priceGroup?.refCommissionHighCube40Price) || 0;
        const highCube40Cost = (parseFloat(record.priceGroup?.refHighCube40Price) || 0) - highCube40Commission;
        const highCube40Sell = parseFloat(record.priceGroup?.highCube40Price) || 0;
        const highCube40Profit = highCube40Sell - highCube40Cost - highCube40Commission;
        newRecord.refHighCube40Price = highCube40Cost
        newRecord.highCube40PriceProfit = highCube40Sell > 0 ? highCube40Profit : '';
        newRecord.highCube40PriceMargin = highCube40Cost > 0 && highCube40Sell > 0 ? `${((highCube40Profit / highCube40Cost) * 100).toFixed(1)}%` : '';

        // HighCube45
        const highCube45Commission = parseFloat(record.priceGroup?.refCommissionHighCube45Price) || 0;
        const highCube45Cost = (parseFloat(record.priceGroup?.refHighCube45Price) || 0) - highCube45Commission;
        const highCube45Sell = parseFloat(record.priceGroup?.highCube45Price) || 0;
        const highCube45Profit = highCube45Sell - highCube45Cost - highCube45Commission;
        newRecord.refHighCube45Price = highCube45Cost;
        newRecord.highCube45PriceProfit = highCube45Sell > 0 ? highCube45Profit : '';
        newRecord.highCube45PriceMargin = highCube45Cost > 0 && highCube45Sell > 0 ? `${((highCube45Profit / highCube45Cost) * 100).toFixed(1)}%` : '';

        // Nor40
        const nor40Commission = parseFloat(record.priceGroup?.refCommissionNor40Price) || 0;
        const nor40Cost = (parseFloat(record.priceGroup?.refNor40Price) || 0) - nor40Commission
        const nor40Sell = parseFloat(record.priceGroup?.nor40Price) || 0;
        const nor40Profit = nor40Sell - nor40Cost - nor40Commission;
        newRecord.refNor40Price = nor40Cost;
        newRecord.nor40PriceProfit = nor40Sell > 0 ? nor40Profit : '';
        newRecord.nor40PriceMargin = nor40Cost > 0 && nor40Sell > 0 ? `${((nor40Profit / nor40Cost) * 100).toFixed(1)}%` : '';

        // Reefer20
        const reefer20Commission = parseFloat(record.priceGroup?.refCommissionReefer20Price) || 0;
        const reefer20Cost = (parseFloat(record.priceGroup?.refReefer20Price) || 0) - reefer20Commission;
        const reefer20Sell = parseFloat(record.priceGroup?.reefer20Price) || 0;
        const reefer20Profit = reefer20Sell - reefer20Cost - reefer20Commission;
        newRecord.refReefer20Price = reefer20Cost;
        newRecord.reefer20PriceProfit = reefer20Sell > 0 ? reefer20Profit : '';
        newRecord.reefer20PriceMargin = reefer20Cost > 0 && reefer20Sell > 0 ? `${((reefer20Profit / reefer20Cost) * 100).toFixed(1)}%` : '';

        // Reefer40
        const reefer40Commission = parseFloat(record.priceGroup?.refCommissionReefer40Price) || 0;
        const reefer40Cost = (parseFloat(record.priceGroup?.refReefer40Price) || 0) - reefer40Commission;
        const reefer40Sell = parseFloat(record.priceGroup?.reefer40Price) || 0;
        const reefer40Profit = reefer40Sell - reefer40Cost - reefer40Commission;
        newRecord.refReefer40Price = reefer40Cost;
        newRecord.reefer40PriceProfit = reefer40Sell > 0 ? reefer40Profit : '';
        newRecord.reefer40PriceMargin = reefer40Cost > 0 && reefer40Sell > 0 ? `${((reefer40Profit / reefer40Cost) * 100).toFixed(1)}%` : '';
      }

      records.push(newRecord);
    }

    let exportModel: entity.DataListExportModel = {
      fieldGroups: [selectedGroupField],
      fields: [...selectedGroupField.fields],
      records: records,
      modelName: 'data_template',
      fileName: `Data_Template_${util.TimeUtil.toDateIdFormat(new Date())}.xlsx`,
    };

    return exportModel;
  }


}
