import React from 'react';
import * as FeatherIcon from 'react-feather'
import { sql, entity, bs, app, input, util } from '@datatp-ui/lib'
import { EventBean, UITaskCalendarUtils } from '../report/UITaskCalendarPage';

export type PartnerType = 'CUSTOMERS' | 'AGENTS' | 'CUSTOMER_LEAD' | 'AGENTS_APPROACHED' | 'COLOADERS'

const SESSION = app.host.DATATP_HOST.session;

interface BBRefUserCustomerProps extends entity.BBRefEntityProps {
  beanIdField: string;
  beanLabelField: string;
  partnerTypes: PartnerType[];
}

export class BBRefUserCustomer extends entity.BBRefEntity<BBRefUserCustomerProps> {

  protected createPlugin() {
    let { beanIdField, beanLabelField, partnerTypes } = this.props

    let config: entity.BBRefEntityPluginConfig = {
      backend: {
        context: 'company',
        service: 'CustomerLeadsService',
        searchMethod: 'searchUserCustomers',
        createSearchParams: (searchParams: sql.SqlSearchParams, _userInput: string) => {
          const normalizedInput = _userInput?.toLowerCase().replace(/\s+/g, '');
          const searchFilter = searchParams.filters?.find(sel => sel.name === 'search');
          if (searchFilter) {
            searchFilter.filterValue = normalizedInput
          } else if (searchParams.filters) {
            searchParams.filters.push({
              name: "search", fields: [], filterOp: 'ilike', filterValue: normalizedInput, "required": true
            });
          }
          //TODO: Dan - fix it after implement permission in sales.
          const accessAllAgentLoginIds: string[] = ['tessie.vnsgn', 'dan', 'leo.vnsgn', 'hanah.vnhph'];
          const loginId: string = SESSION.getLoginId();

          if (accessAllAgentLoginIds.includes(loginId)) {
            let params = searchParams.params || {};
            params = {
              space: 'System',
            }
            searchParams.params = params;
          }

          if (partnerTypes) {
            searchParams.params = {
              ...(searchParams.params || {}),
              type: partnerTypes,
            }
          }

          searchParams.maxReturn = 5000;
          return searchParams;
        }
      },
      bean: {
        idField: beanIdField,
        labelField: beanLabelField,
      },
      refEntity: {
        idField: 'partnerCode',
        labelField: 'partnerName',
        vgridRecordConfig: {
          fields: [
            entity.DbEntityListConfigTool.FIELD_INDEX(),
            entity.DbEntityListConfigTool.FIELD_ON_SELECT('partnerCode', ('Code'), 140),
            { name: 'taxCode', label: 'Tax Code', width: 140 },
            { name: 'partnerName', label: 'Partner Name', width: 350 },
            { name: 'email', label: 'Email', width: 200 },
            { name: 'countryLabel', label: 'Country', width: 150 },
            { name: 'personContact', label: 'Contact', width: 200 },
            { name: 'type', label: 'Type', width: 170, },
          ]
        }
      },

    }

    return new entity.BBRefEntityPlugin(config);
  }

  renderOptions(options: Array<any>) {
    let refRecord = this.plugin.config.refEntity;
    let idField = refRecord.idField;
    let labelField = refRecord.labelField;
    let labelFieldFunc = refRecord.labelFunc;
    const { model } = this.state;
    let optionHtml: JSX.Element[] = [];
    let max = options.length;
    for (let i = 0; i < max; i++) {
      let opt = options[i];
      let identifier = opt[idField];
      let label = opt[labelField];
      if (labelFieldFunc) {
        label = labelFieldFunc(opt);
      }
      let itemClassName = 'dropdown-item';
      if (model.currSelect === i) {
        itemClassName = `${itemClassName} active py-2`;
      }

      optionHtml.push(
        <div key={`opt-${i}`} className={`${itemClassName} option`} style={{ cursor: 'pointer', height: 35 }}
          onClick={() => this.onMouseSelectOption(i)}
          onContextMenu={(e) => e.preventDefault()}
          onMouseLeave={(e) => this.onMouseLeaveOut(e)}
          onMouseEnter={(e) => this.onMouseHoverOption(e, i)}>
          <div className='' style={{ width: 120 }}>{identifier}</div>
          <div className='desc'>{label}</div>
        </div>
      );
    }
    if (options.length > max) {
      optionHtml.push(<div key='more' className='text-center'>...</div>);
    }
    if (this.plugin.allowCreateNew(this)) {
      let itemClassName = 'dropdown-item';
      if (model.currSelect == max) {
        itemClassName = `${itemClassName} active`;
      }
      optionHtml.push(
        <div key={'new'} className={itemClassName}>
          <bs.Button laf='link' className='px-1' onClick={(_evt) => this.onMouseSelectOption(options.length)}>
            <FeatherIcon.Plus size={12} />Create New
          </bs.Button>
        </div>
      );
    }
    return optionHtml;
  }

}

interface BBRefMultiUserCustomerProps extends entity.BBRefMultiEntityProps {
  beanIdField: string;
  beanLabelField: string;
  partnerTypes?: PartnerType[];
  dateFilter?: util.TimeRange;
  showCountry?: boolean;
}

export class BBRefMultiUserCustomer extends entity.BBRefMultiEntity<BBRefMultiUserCustomerProps> {

  protected createPlugin() {
    let { beanIdField, beanLabelField, partnerTypes, dateFilter } = this.props

    let config: entity.BBRefEntityPluginConfig = {
      backend: {
        context: 'company',
        service: 'CustomerLeadsService',
        searchMethod: 'searchUserCustomers',
        createSearchParams: (searchParams: sql.SqlSearchParams, _userInput: string) => {
          const normalizedInput = _userInput?.toLowerCase().replace(/\s+/g, '');
          const searchFilter = searchParams.filters?.find(sel => sel.name === 'search');
          if (searchFilter) {
            searchFilter.filterValue = normalizedInput
          } else if (searchParams.filters) {
            searchParams.filters.push({
              name: "search", fields: [], filterOp: 'ilike', filterValue: normalizedInput, "required": true
            });
          }
          if (partnerTypes) {
            searchParams.params = {
              type: partnerTypes,
            }
          }
          if (dateFilter) {
            searchParams.rangeFilters = [
              sql.createDateTimeFilterNew("createdTime", "Created Time", dateFilter)
            ];
          }
          searchParams.maxReturn = 5000;
          return searchParams;
        }

      },
      bean: {
        idField: beanIdField,
        labelField: beanLabelField,
        mapSelect(ui, bean, selectOpt, idValue, labelValue) {
          let refEntities = bean as Array<any>;
          if (selectOpt) {
            let obj = { [beanIdField]: idValue, [beanLabelField]: labelValue, type: selectOpt['type'], countryLabel: selectOpt['countryLabel'] }
            refEntities.push(obj);
            return 'success';
          } else {
            const { allowUserInput } = ui.props;
            if (allowUserInput) {
              let obj = { [beanLabelField]: labelValue }
              refEntities.push(obj);
              return 'success';
            } else {
              return 'fail';
            }
          }
        },
      },
      refEntity: {
        idField: 'partnerCode',
        labelField: 'partnerName',
        vgridRecordConfig: {
          fields: [
            entity.DbEntityListConfigTool.FIELD_INDEX(),
            entity.DbEntityListConfigTool.FIELD_ON_SELECT('partnerCode', ('Code'), 120),
            { name: 'taxCode', label: 'Tax Code', width: 120 },
            { name: 'partnerName', label: 'Partner Name', width: 350 },
            { name: 'personContact', label: 'Contact', width: 200 },
            { name: 'countryLabel', label: 'Country', width: 150 },
            { name: 'type', label: 'Type', width: 150, format: (value: string) => value.toUpperCase() },
          ]
        }
      },

    }

    return new entity.BBRefMultiEntityPlugin(config);
  }

  showRefEntityInfo(refEntity: any) {
    const { appContext } = this.props;
    const isLead = refEntity['type'] === 'CUSTOMER_LEAD' || refEntity['type'] === 'AGENTS_APPROACHED';

    appContext.createHttpBackendCall(
      isLead ? 'CustomerLeadsService' : 'CRMPartnerService',
      isLead ? 'getCustomerLead' : 'getCRMPartner',
      { partnerId: refEntity['id'] }
    )
      .withSuccessData((bean: any) => {
        let eventBean: EventBean = {
          partnerId: bean.id,
          partnerCode: refEntity['partnerCode'],
          taxCode: bean['taxCode'],
          partnerLabel: bean['label'],
          partnerType: isLead ? 'forwarder_customer_leads' : 'bfsone_partner',
          address: bean['address'],
          kcnLabel: bean['kcnLabel']
        }
        UITaskCalendarUtils.showUIEventHistory(this, eventBean);
      })
      .call();
  }

  renderToggle() {
    let { label, bean, disable, showCountry, onPostUpdate } = this.props;

    let beanConfig = this.plugin.config.bean;
    let refEntities = bean as Array<any>
    let refEntityWidgets = [];
    for (let i = 0; i < refEntities.length; i++) {
      let refEntity = refEntities[i];
      let label = beanConfig.labelField ? refEntity[beanConfig.labelField] : refEntity[beanConfig.idField];
      if (showCountry && refEntity['countryLabel']) {
        label = `${label} - ${refEntity['countryLabel']}`;
      }
      let widget = (
        <div key={i} className="flex-hbox align-items-center justify-content-between border border-dashed rounded px-2 py-1 w-100 my-1" style={{ minWidth: "150px" }}>
          <div className='flex-vbox'>
            <bs.Button laf='link' className="btn btn-link text-decoration-none px-2 py-1 text-truncate text-start"
              onClick={() => this.showRefEntityInfo(refEntity)} >
              {label}
            </bs.Button>
          </div>
          <bs.Button hidden={disable} laf='link'
            className="btn btn-link text-danger px-2 py-1 border-start"
            onClick={() => {
              let { bean } = this.props;
              let refEntities = bean as Array<any>;
              refEntities.splice(i, 1);
              this.forceUpdate();
              if (onPostUpdate) onPostUpdate(this, refEntities, {}, '');
            }}>
            <FeatherIcon.X size={14} />
          </bs.Button>
        </div >
      );
      refEntityWidgets.push(widget);
    }

    let inputUI = (
      <div className='flex-hbox flex-wrap' >
        {refEntityWidgets}
        <div className='flex-hbox flex-nowrap my-1 py-2' id={`popover-trigger-${this.popoverId}`}>
          {this.renderInput()}
          {this.renderSearchRefEntities()}
        </div>
      </div>
    );

    if (!label) return inputUI;

    return (
      <div className='bb-field' >
        <label className="form-label">{label}</label>
        {inputUI}
      </div>
    );
  }
}