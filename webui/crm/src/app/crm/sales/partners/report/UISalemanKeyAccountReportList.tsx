import React from "react";

import * as FeatherIcon from 'react-feather'
import { util, grid, bs, entity, input, sql, app } from '@datatp-ui/lib';
import { T } from "../../backend";
import { module } from '@datatp-ui/erp';

import { UIKeyAccountFormReport } from "./UISalePerformanceFormReport";
import { WQuickTimeRangeSelector } from "app/crm/common";

const USD_CURR_FORMAT = (val: any) => util.text.formater.currency(val, 2);

export class UISalemanKeyAccountReportPlugin extends entity.DbEntityListPlugin {
  constructor(space: 'User' | 'Company' | 'System') {
    super([]);

    this.backend = {
      service: 'PartnerReportService',
      searchMethod: 'searchSalemanKeyAccountReports',
      deleteMethod: 'deleteSalemanKeyAccountReports',
    }
    this.searchParams = {
      params: {
        space: space,
      },
      filters: [...sql.createSearchFilter()],
      maxReturn: 1000
    }
  }

  loadData(uiList: entity.DbEntityList<any>): void {
    this.createBackendSearch(uiList, { params: this.searchParams }).call();
  }

  withSalemanAccountId(salemanAccountId: number) {
    this.addSearchParam("salemanAccountId", salemanAccountId)
    return this;
  }

  withDueDate(fromValue: string, toValue: string) {
    if (this.searchParams) {
      let rangeFilters = this.searchParams.rangeFilters;
      if (rangeFilters) {
        for (let i = 0; i < rangeFilters.length; i++) {
          let filter = rangeFilters[i];
          if (filter.name === 'dueDate') {
            filter.fromValue = fromValue;
            filter.toValue = toValue;
            break;
          }
        }
      }
      this.searchParams.rangeFilters = rangeFilters;
    }
    return this;
  }

}


interface FilterParam {
  dateFilter: { fromValue: string, toValue: string, label: string };
}

export class UISalemanKeyAccountReportList extends entity.DbEntityList {
  reportFilter: FilterParam;

  constructor(props: entity.DbEntityListProps) {
    super(props);

    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    let dateFilter = new util.TimeRange();
    dateFilter.fromSetDate(firstDayOfMonth);
    dateFilter.toSetDate(lastDayOfMonth);

    this.reportFilter = {
      dateFilter: { fromValue: dateFilter.fromFormat(), toValue: dateFilter.toFormat(), label: 'This Month' },
    }

    this.loadData();
  }

  createVGridConfig() {
    let config: grid.VGridConfig = {
      record: {
        dataCellHeight: 40,
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          {
            name: 'salemanLabel', label: T('Salesman'), width: 220, filterable: true, container: 'fixed-left',
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let uiList = ctx.uiRoot as entity.DbEntityList
              const { appContext, pageContext } = uiList.props;

              let employeeName: string = record['salemanLabel'] || 'N/A';

              let parts = employeeName.trim().toLowerCase().split(' ');
              parts = parts.map(word => word.charAt(0).toUpperCase() + word.slice(1));
              if (parts.length > 3) {
                let initials = parts[0][0] + '.' + parts[1][0] + '.';
                employeeName = `${initials} ${parts.slice(2).join(' ')}`;
              }

              return (
                <div className='flex-hbox justify-content-center align-items-center' onClick={() => this.onDefaultSelect(dRecord)}>
                  <module.account.WAvatars className='px-2'
                    appContext={appContext} pageContext={pageContext} avatarIds={[record['salemanAccountId']]}
                    avatarIdType='AccountId' width={20} height={20} borderRadius={10} />
                  <div className="flex-hbox">{employeeName}</div>
                </div>
              )
            },
          },
          {
            name: 'submittedDate', label: T('Submitted Date'), width: 130,
            format: util.text.formater.compactDate, filterable: true, filterableType: 'date'
          },
          {
            name: 'reportedDateFrom', label: T('Report From'), width: 130,
            format: util.text.formater.compactDate, filterable: true, filterableType: 'date'
          },
          {
            name: 'reportedDateTo', label: T('Report To'), width: 130,
            format: util.text.formater.compactDate, filterable: true, filterableType: 'date'
          },
          // {
          //   name: 'revenue', label: T('Revenue'), width: 100, sortable: true,
          //   customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
          //     let record = dRecord.record;
          //     let val = record['revenue'];
          //     let performanceVal = record['volumePerformance'];
          //     let performance: any = typeof performanceVal === 'string'
          //       ? JSON.parse(performanceVal)
          //       : (val || {});
          //     val = performance['totalRevenue'];
          //     return (<div className="text-wrap position-relative" >{util.text.formater.currency(val, 2)}</div>);
          //   }
          // },
          // {
          //   name: 'profit', label: T('Profit'), width: 100, sortable: true,
          //   customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
          //     let record = dRecord.record;
          //     let performanceVal = record['volumePerformance'];
          //     let performance: any = typeof performanceVal === 'string'
          //       ? JSON.parse(performanceVal)
          //       : (performanceVal || {});
          //     let val = performance['totalProfit'];
          //     return (<div className="text-wrap position-relative" >{util.text.formater.currency(val, 2)}</div>);
          //   }
          // },
          // {
          //   name: 'margin', label: T('Margin (GP/ REV)'), width: 150, sortable: true,
          //   fieldDataGetter(record) {
          //     let performanceVal = record['volumePerformance'];
          //     let performance: any = typeof performanceVal === 'string'
          //       ? JSON.parse(performanceVal)
          //       : (performanceVal || {});
          //     const totalRevenue = performance['totalRevenue'];
          //     const totalProfit = performance['totalProfit'];
          //     let profitRate: number = totalProfit / totalRevenue || 0;
          //     let margin = profitRate * 100
          //     return margin;
          //   },
          //   customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
          //     let record = dRecord.record;
          //     let performanceVal = record['volumePerformance'];
          //     let performance: any = typeof performanceVal === 'string'
          //       ? JSON.parse(performanceVal)
          //       : (performanceVal || {});

          //     const totalRevenue = performance['totalRevenue'];
          //     const totalProfit = performance['totalProfit'];
          //     let profitRate: number = totalProfit / totalRevenue || 0;
          //     let margin = USD_CURR_FORMAT(profitRate * 100)
          //     return (<div className="text-wrap position-relative" >{`${margin} %`}</div>);
          //   }
          // },
          {
            name: 'highlights', label: T('Highlights / Forecast'), width: 300,
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;

              // Parse highlights
              let val: any = record['highlights'];
              let highlights: any = typeof val === 'string'
                ? JSON.parse(val)
                : (val || {});

              // Parse forecast
              let forecastVal: any = record['forecast'];
              let forecast: any = typeof forecastVal === 'string'
                ? JSON.parse(forecastVal)
                : (forecastVal || {});
              let displayForecast: any = forecast['forecast'];

              // Suggestions/Requests
              let suggestionOrRequest = record['suggestionOrRequest'] || '';

              let htmlLines: any[] = [];

              if (record['type'] === 'SALES') {

                let expectedLeadsToWin = highlights['expectedLeadsToWin'] || [];
                if (!Array.isArray(expectedLeadsToWin)) {
                  if (typeof expectedLeadsToWin === 'string') {
                    try {
                      expectedLeadsToWin = JSON.parse(expectedLeadsToWin);
                    } catch {
                      expectedLeadsToWin = [];
                    }
                  } else if (expectedLeadsToWin == null) {
                    expectedLeadsToWin = [];
                  } else {
                    expectedLeadsToWin = [expectedLeadsToWin];
                  }
                }

                if (expectedLeadsToWin.length > 0) {
                  htmlLines.push(
                    <div className='flex-vbox border-0'>
                      <div className="tooltip-header">
                        <span className="tooltip-title">{`Khách hàng tiềm năng, dự kiến win (next month) (${expectedLeadsToWin.length})`}:</span>
                      </div>
                      <div className="tooltip-body">
                        {
                          expectedLeadsToWin.map((item: any, index: number) => (
                            <div className="mt-1 px-1 py-1" key={index}>{item.name}</div>
                          ))
                        }
                      </div>
                    </div>
                  );
                }

                let newCustomers = highlights['newCustomers'] || [];
                if (!Array.isArray(newCustomers)) {
                  if (typeof newCustomers === 'string') {
                    try {
                      newCustomers = JSON.parse(newCustomers);
                    } catch {
                      newCustomers = [];
                    }
                  } else if (newCustomers == null) {
                    newCustomers = [];
                  } else {
                    newCustomers = [newCustomers];
                  }
                }

                if (newCustomers.length > 0) {
                  htmlLines.push(
                    <div className='flex-vbox border-0'>
                      <div className="tooltip-header">
                        <span className="tooltip-title">{`Khách hàng mới (${newCustomers.length})`}:</span>
                      </div>
                      <div className="tooltip-body">
                        {
                          newCustomers.map((item: any, index: number) => (
                            <div className="mt-1 px-1 py-1" key={index}>{item.name}</div>
                          ))
                        }
                      </div>
                    </div>
                  );
                }

                let marketInformation = highlights['marketInformation'] || '';
                let reportedIssues = highlights['reportedIssues'] || '';

                if (marketInformation) {
                  htmlLines.push(
                    <div className='flex-vbox border-0'>
                      <div className="tooltip-header">
                        <span className="tooltip-title">{'Thông tin thị trường'}:</span>
                      </div>
                      <div className="tooltip-body">
                        {marketInformation}
                      </div>
                    </div>
                  );
                }

                if (reportedIssues) {
                  htmlLines.push(
                    <div className='flex-vbox border-0'>
                      <div className="tooltip-header">
                        <span className="tooltip-title">{'Các vấn đề phát sinh'}:</span>
                      </div>
                      <div className="tooltip-body">
                        {reportedIssues}
                      </div>
                    </div>
                  );
                }

                if (displayForecast) {
                  htmlLines.push(
                    <div className='flex-vbox border-0'>
                      <div className="tooltip-header">
                        <span className="tooltip-title">{'Forecast'}:</span>
                      </div>
                      <div className="tooltip-body">
                        {displayForecast}
                      </div>
                    </div>
                  );
                }

                if (suggestionOrRequest) {
                  htmlLines.push(
                    <div className='flex-vbox border-0'>
                      <div className="tooltip-header">
                        <span className="tooltip-title">{'Suggestions/Requests'}:</span>
                      </div>
                      <div className="tooltip-body">
                        {suggestionOrRequest}
                      </div>
                    </div>
                  );
                }

                return (
                  <bs.CssTooltip width={480} position='auto'>
                    <bs.CssTooltipToggle>
                      <div className="d-flex align-items-center justify-content-start w-100" style={{ cursor: 'pointer', userSelect: 'text' }}>
                        <div className={`d-flex align-items-center px-1 py-1 text-success`}>
                          {util.text.formater.uiTruncate(
                            [marketInformation, reportedIssues, displayForecast, suggestionOrRequest].filter(Boolean).join(' | '),
                            300, true
                          )}
                        </div>
                      </div>
                    </bs.CssTooltipToggle>
                    <bs.CssTooltipContent>
                      <div className='flex-vbox'>
                        {htmlLines}
                      </div>
                    </bs.CssTooltipContent>
                  </bs.CssTooltip>
                );
              } else {
                let signedAaContracts = highlights['signedAaContracts'] || [];
                if (!Array.isArray(signedAaContracts)) {
                  if (typeof signedAaContracts === 'string') {
                    try {
                      signedAaContracts = JSON.parse(signedAaContracts);
                    } catch {
                      signedAaContracts = [];
                    }
                  } else if (signedAaContracts == null) {
                    signedAaContracts = [];
                  } else {
                    signedAaContracts = [signedAaContracts];
                  }
                }

                let meetingIn2Weeks = highlights['meetingIn2Weeks'] || [];
                if (!Array.isArray(meetingIn2Weeks)) {
                  if (typeof meetingIn2Weeks === 'string') {
                    try {
                      meetingIn2Weeks = JSON.parse(meetingIn2Weeks);
                    } catch {
                      meetingIn2Weeks = [];
                    }
                  } else if (meetingIn2Weeks == null) {
                    meetingIn2Weeks = [];
                  } else {
                    meetingIn2Weeks = [meetingIn2Weeks];
                  }
                }

                let newAgentsApproachedIn2Weeks = highlights['newAgentsApproachedIn2Weeks'] || [];
                if (!Array.isArray(newAgentsApproachedIn2Weeks)) {
                  if (typeof newAgentsApproachedIn2Weeks === 'string') {
                    try {
                      newAgentsApproachedIn2Weeks = JSON.parse(newAgentsApproachedIn2Weeks);
                    } catch {
                      newAgentsApproachedIn2Weeks = [];
                    }
                  } else if (newAgentsApproachedIn2Weeks == null) {
                    newAgentsApproachedIn2Weeks = [];
                  } else {
                    newAgentsApproachedIn2Weeks = [newAgentsApproachedIn2Weeks];
                  }
                }

                let otherHighlights = highlights['otherHighlights'] || '';
                let lowlights = highlights['lowlights'] || '';

                if (signedAaContracts.length > 0) {
                  htmlLines.push(
                    <div className='flex-vbox border-0'>
                      <div className="tooltip-header">
                        <span className="tooltip-title">{`New Customers & Agents (${signedAaContracts.length})`}:</span>
                      </div>
                      <div className="tooltip-body">
                        {
                          signedAaContracts.map((item: any, index: number) => (
                            <div className="mt-1 px-1 py-1" key={index}>{item.partnerName}</div>
                          ))
                        }
                      </div>
                    </div>
                  );
                }

                if (meetingIn2Weeks.length > 0) {
                  htmlLines.push(
                    <div className='flex-vbox border-0'>
                      <div className="tooltip-header">
                        <span className="tooltip-title">{`Meeting with Agents (${meetingIn2Weeks.length})`}:</span>
                      </div>
                      <div className="tooltip-body">
                        {
                          meetingIn2Weeks.map((item: any, index: number) => (
                            <div className="mt-1 px-1 py-1" key={index}>{item.partnerName}</div>
                          ))
                        }
                      </div>
                    </div>
                  );
                }

                if (newAgentsApproachedIn2Weeks.length > 0) {
                  htmlLines.push(
                    <div className='flex-vbox border-0'>
                      <div className="tooltip-header">
                        <span className="tooltip-title">{`New Agents approached (${newAgentsApproachedIn2Weeks.length})`}:</span>
                      </div>
                      <div className="tooltip-body">
                        {
                          newAgentsApproachedIn2Weeks.map((item: any, index: number) => (
                            <div className="mt-1 px-1 py-1" key={index}>{item.partnerName}</div>
                          ))
                        }
                      </div>
                    </div>
                  );
                }

                if (otherHighlights) {
                  htmlLines.push(
                    <div className='flex-vbox border-0'>
                      <div className="tooltip-header">
                        <span className="tooltip-title">{'Other highlights'}:</span>
                      </div>
                      <div className="tooltip-body">
                        {otherHighlights}
                      </div>
                    </div>
                  );
                }

                if (lowlights) {
                  htmlLines.push(
                    <div className='flex-vbox border-0'>
                      <div className="tooltip-header">
                        <span className="tooltip-title">{'Lowlights'}:</span>
                      </div>
                      <div className="tooltip-body">
                        {lowlights}
                      </div>
                    </div>
                  );
                }

                if (suggestionOrRequest) {
                  htmlLines.push(
                    <div className='flex-vbox border-0'>
                      <div className="tooltip-header">
                        <span className="tooltip-title">{'Suggestions/Requests'}:</span>
                      </div>
                      <div className="tooltip-body">
                        {suggestionOrRequest}
                      </div>
                    </div>
                  );
                }
                return (
                  <bs.CssTooltip width={480} position='auto'>
                    <bs.CssTooltipToggle>
                      <div className="d-flex align-items-center justify-content-start w-100" style={{ cursor: 'pointer', userSelect: 'text' }}>
                        <div className={`d-flex align-items-center px-1 py-1 text-success`}>
                          {util.text.formater.uiTruncate(
                            [suggestionOrRequest, otherHighlights, lowlights].filter(Boolean).join(' | '),
                            300, true
                          )}
                        </div>
                      </div>
                    </bs.CssTooltipToggle>
                    <bs.CssTooltipContent>
                      <div className='flex-vbox'>
                        {htmlLines}
                      </div>
                    </bs.CssTooltipContent>
                  </bs.CssTooltip>
                );
              }
            }
          },
          {
            name: 'suggestionOrRequest', label: T('Suggestions/Requests'), width: 350, filterable: true,
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let val = record[_field.name] || '';
              const htmlVal = val.split('\n').map((line: any, i: any) =>
                <div key={i}>{line}</div>
              );

              return (
                <bs.CssTooltip position='bottom-left' width={400} offset={{ x: -200, y: -10 }}>
                  <bs.CssTooltipToggle className='flex-hbox justify-content-start h-100 w-100'>
                    {util.text.formater.uiTruncate(val, 360, true)}
                  </bs.CssTooltipToggle>
                  <bs.CssTooltipContent className='flex-vbox'>
                    <div className="tooltip-body">
                      {htmlVal}
                    </div>
                  </bs.CssTooltipContent>
                </bs.CssTooltip>
              )
            }
          },
        ],
      },
      toolbar: {
        hide: true
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          },
        }
      }
    }
    return config;
  }

  onDefaultSelect(dRecord: grid.DisplayRecord): void {
    const { pageContext, appContext } = this.props;
    let record: any = dRecord.record || {};
    let reportId: number = record['id'];
    appContext.createHttpBackendCall("PartnerReportService", "getSalemanKeyAccountReportById", { id: reportId })
      .withSuccessData((report: any) => {

        let volumePerformance: any = typeof report['volumePerformance'] === 'string'
          ? JSON.parse(report['volumePerformance'])
          : (report['volumePerformance'] || {});
        report['volumePerformance'] = volumePerformance;

        let forecast: any = typeof report['forecast'] === 'string'
          ? JSON.parse(report['forecast'])
          : (report['forecast'] || {});
        report['forecast'] = forecast;

        let highlights: any = typeof report['highlights'] === 'string'
          ? JSON.parse(report['highlights'])
          : (report['highlights'] || {});
        report['highlights'] = highlights;

        let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return <UIKeyAccountFormReport appContext={appCtx} pageContext={pageCtx} initReportBean={report} />
        }
        let popupId = `make-key-account-report-${util.IDTracker.next()}`;
        let popupLabel: string = `Performance Report - ${report['salemanLabel'] || 'N/A'}`;
        pageContext.createPopupPage(popupId, popupLabel, createAppPage, { size: 'xl', backdrop: 'static' });
      })
      .call()
  }

  onDeleteAction(): void {
    const { appContext, plugin } = this.props;
    const selectedIds = plugin.getListModel().getSelectedRecordIds();

    if (selectedIds.length === 0) {
      bs.notificationShow("warning", T("warning"), T("No saleman account report selected!"));
      return;
    }

    const callbackConfirm = () => {
      appContext.createHttpBackendCall('PartnerReportService', 'deleteSalemanKeyAccountReports', { targetIds: selectedIds })
        .withSuccessData((_data: any) => {
          appContext.addOSNotification("success", T("Delete saleman account report success"));
          this.reloadData();
        })
        .call();
    }
    let message = (<div className="text-danger">Do you want to delete these partner report?</div>);
    bs.dialogConfirmMessage('Confirm Message', message, callbackConfirm);
  }

  onModify = (_bean: any, _field: string, _oldVal: any, _newVal: any): void => {
    const { plugin } = this.props;
    let searchParam = plugin.getSearchParams();
    if (_field === 'maxReturn') {
      searchParam.maxReturn = _newVal || 5000;
    } else {
      searchParam.params = _bean;
    }
    plugin.searchParams = searchParam;
    this.reloadData();
    this.nextViewId();
    this.vgridContext.getVGrid().forceUpdateView();
  };

  doExport = () => {

  }

  renderHeader() {
    const { appContext, pageContext } = this.props;
    const { dateFilter } = this.reportFilter;

    return (
      <div className="flex-hbox align-items-center justify-content-between px-2 py-1">

        <div className="flex-hbox justify-content-start align-items-center flex-grow-0 gap-2" >
          <h5 style={{ color: '#6c757d' }}>
            <FeatherIcon.BarChart2 className="me-2" size={18} />
            Saleman Performance Report
          </h5>
          <div className='flex-hbox align-items-center flex-grow-0 border-start' >
          </div>
        </div>

        <div className="flex-hbox justify-content-end align-items-center gap-1" >

          <WQuickTimeRangeSelector appContext={appContext} pageContext={pageContext}
            initBean={dateFilter}
            onModify={(bean: any, _field: string, _oldVal: any, _newVal: any) => {
              this.reportFilter.dateFilter = bean;
              this.loadData();
            }} />

          <bs.Button laf="secondary" outline size="sm" className="p-1" style={{ color: '#6c757d' }}
            onClick={this.doExport}>
            <FeatherIcon.Download size={14} className="me-2" />
            Export
          </bs.Button>

          <bs.Button laf='warning' size="sm" className="p-1" outline
            onClick={() => this.onDeleteAction()} hidden={bs.ScreenUtil.isMobileScreen()}>
            <FeatherIcon.Trash2 size={14} /> Delete
          </bs.Button>

        </div>

      </div>
    )
  }

  render() {
    if (this.isLoading()) return this.renderLoading();
    const { plugin } = this.props;
    let searchParam = plugin.getSearchParams();

    return (
      <div className="flex-vbox mx-1 p-1 bg-white rounded-md w-100 h-100 my-1" >
        <div className='bg-white flex-hbox flex-grow-0 justify-content-between align-items-center mx-1 px-1 mt-1'>
          {this.renderHeader()}
        </div>
        <div key={this.viewId} className='flex-vbox border-top bg-body-highlight'>
          {this.renderUIGrid()}
        </div>
      </div>
    )
  }
}

