import React from 'react';
import * as FeatherIcon from 'react-feather';

import { bs, app, input, util, entity } from '@datatp-ui/lib';

import { BBRefMultiCustomerLead } from '../../leads/BBRefMultiCustomerLead';
import {
  UIVolumeSalemanKeyAccountReportList,
  UIVolumeSalemanKeyAccountReportPlugin
} from './UIVolumeSalemanKeyAccountReportList';
import { UIKeyAccountPerformance } from '../../dashboard/UIKeyAccountPerformance';
import { BBRefMultiUserCustomer } from '../BBRefUserCustomer';

const SESSION = app.host.DATATP_HOST.session;

export interface UIGridReportProps extends app.AppComponentProps {
  rawRecords: Array<any>;
  title?: string;
  type: 'FCL' | 'AIR' | 'LCL' | 'OTHER'
}
export class UIPerformanceGridReport extends bs.AvailableSize<UIGridReportProps> {
  config: bs.GridConfig;

  constructor(props: UIGridReportProps) {
    super(props);
    const { title, type } = this.props;
    let volumeLabel = 'GW (KGS)';
    if (type === 'FCL') {
      volumeLabel = 'TEUs'
    } else if (type === 'LCL') {
      volumeLabel = 'CBM'
    } else if (type === 'OTHER') {
      volumeLabel = 'TKs'
    }

    this.config = {
      header: {
        height: 20,
      },
      row: {
        height: 30,
      },
      showHeader: true,
      showBorder: true,
      columns: [
        { field: 'keyAccountName', label: `${title ? title : 'Key Account'}`, width: 320, cssClass: 'text-body fs-9 text-start' },
        { field: 'shipmentCount', label: 'Jobs', width: 120, cssClass: 'text-body fs-9 text-start' },
        { field: 'volume', label: volumeLabel, width: 120, cssClass: 'text-body fs-9 text-start' },
        { field: 'revenue', label: 'Revenue (USD)', width: 120, cssClass: 'text-body fs-9 text-start' },
        { field: 'profit', label: 'Profit (USD)', width: 120, cssClass: 'text-body fs-9 text-start' },
      ]
    }
  }

  getColumnWidths(contentWidth: number): number[] {
    const totalRatio = 2.5 + 1 + 1 + 1 + 1; // 5.5
    const unit = contentWidth / totalRatio;

    const widths = [
      Math.max(Math.floor(unit * 2.5), 120),
      Math.max(Math.floor(unit * 1), 120),
      Math.max(Math.floor(unit * 1), 120),
      Math.max(Math.floor(unit * 1), 120),
      Math.max(Math.floor(unit * 1), 120)
    ];

    return widths;
  }

  renderContent(width: number | string, _height: number): React.ReactElement {
    const contentWidth = typeof width === 'number' ? width : 500;
    const columnWidths = this.getColumnWidths(contentWidth - 30); // Subtract padding

    // Update column widths
    this.config.columns[0].width = columnWidths[0];
    this.config.columns[1].width = columnWidths[1];
    this.config.columns[2].width = columnWidths[2];
    this.config.columns[3].width = columnWidths[3];
    this.config.columns[4].width = columnWidths[4];

    const { rawRecords } = this.props;

    return (
      <div className="p-1" style={{ width: contentWidth }}>
        <bs.Grid config={this.config} beans={rawRecords} />
      </div>
    );
  }
}

export class SalemanReportBean {
  id: number | undefined;
  type: 'BD' | 'SALES';
  code: string;
  suggestionOrRequest: string;
  salemanAccountId: number;
  salemanLabel: string;
  reportedDateFrom: string;
  reportedDateTo: string;
  profit: number;
  volume: number;
  revenue: number;
  volumePerformance: any;
  highlights: any;
  forecast: any;
}

export interface UIKeyAccountReportFormProps extends app.AppComponentProps {
  initReportBean: SalemanReportBean
}
export class UIKeyAccountFormReport extends app.AppComponent<UIKeyAccountReportFormProps> {
  viewId: number = util.IDTracker.next();
  reportBean: SalemanReportBean;

  constructor(props: UIKeyAccountReportFormProps) {
    super(props);
    const { initReportBean } = this.props;
    this.reportBean = initReportBean;
  }

  onSave = () => {
    let { appContext } = this.props;
    appContext.createHttpBackendCall('PartnerReportService', 'saveSalemanKeyAccountReport', { report: this.reportBean })
      .withEntityOpNotification('commit', 'Sale Performance Report')
      .withSuccessData((reportBeanInDb: any) => {
        this.reportBean = reportBeanInDb
        this.viewId = util.IDTracker.next();
        this.forceUpdate();
      })
      .call();
  }

  /* ------------ REMIND: Dan - view raw data ------------- */
  onViewAllPerformance = () => {
    let { pageContext } = this.props;
    const { reportedDateFrom, reportedDateTo } = this.reportBean;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UIVolumeSalemanKeyAccountReportList
          appContext={appCtx} pageContext={pageCtx}
          plugin={new UIVolumeSalemanKeyAccountReportPlugin().withReportedDate(reportedDateFrom, reportedDateTo)} />
      )
    }
    pageContext.createPopupPage('performance', "Performance", createAppPage, { size: 'xl', backdrop: 'static' });
  }

  onModifyHighlights = (inputUI: React.Component, bean: any, selectOpt: any, userInput: string) => {
    this.viewId = util.IDTracker.next();
    this.forceUpdate();
  }

  renderBDFormSection() {
    const { appContext, pageContext } = this.props;
    {/* ------------------- Highlights ----------------------- */ }
    const highlights: any = this.reportBean['highlights'] || {}
    let signedAaContractsAgent = highlights['signedAaContractsAgent'] || [];
    let signedAaContractsCustomer = highlights['signedAaContractsCustomer'] || [];
    let meetingIn2WeeksAgent = highlights['meetingIn2WeeksAgent'] || [];
    let meetingIn2WeeksCustomer = highlights['meetingIn2WeeksCustomer'] || [];
    let newAgentsApproachedIn2Weeks = highlights['newAgentsApproachedIn2Weeks'] || [];
    let newCustomersApproachedIn2Weeks = highlights['newCustomersApproachedIn2Weeks'] || [];

    const forecast: any = this.reportBean['forecast'] || {}
    const airVolume: any = forecast['airVolume'] || { kgs: '' };
    const seaVolume: any = forecast['seaVolume'] || { teus: '', cbm: '' };
    const estimate: any = forecast['estimate'] || { gp: '', revenue: '' };

    const lightColor = '#D6EFD8';
    const borderColor = '#80AF81';

    const hoverStyle: any = {
      boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
      transform: 'translateY(-2px)',
      backgroundColor: lightColor
    };

    return (
      <div className="flex-hbox align-items-start flex-grow-0 flex-shrink-0 p-1 mb-1 gap-1" key={this.viewId}>

        <div className="bg-white border rounded-3 flex-vbox justify-content-center align-items-center w-50"
          style={{
            borderColor: borderColor,
            transition: 'all 0.3s ease',
            marginBottom: '10px'
          }}
          onMouseEnter={(e) => Object.assign(e.currentTarget.style, hoverStyle)}
          onMouseLeave={(e) => {
            e.currentTarget.style.boxShadow = 'none';
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.backgroundColor = '#fff';
          }}>

          <div className="flex-vbox mx-2 p-1 bg-white rounded-md w-100">
            <div className="flex-hbox flex-grow-0 align-items-center justify-content-between border-bottom px-2 py-1">
              <h5 style={{ color: '#6c757d', fontSize: '0.9rem' }}><FeatherIcon.Award className="me-2" size={18} />HIGHLIGHTS</h5>
            </div>

            <div className="flex-vbox">
              <div className="flex-vbox justify-content-start py-1 w-100">
                <div className="flex-grow-0 flex-hbox justify-content-start align-items-center w-100">
                  <FeatherIcon.FileText className="me-2 text-primary" size={14} />
                  <span>New Agents ({signedAaContractsAgent.length})</span>
                </div>
                <div className="flex-vbox">
                  <BBRefMultiUserCustomer placeholder='Enter agent' label='' hideMoreInfo
                    appContext={appContext} pageContext={pageContext} minWidth={400}
                    bean={signedAaContractsAgent} beanIdField={'code'} beanLabelField={'partnerName'} partnerTypes={['AGENTS']}
                    onPostUpdate={this.onModifyHighlights} />
                </div>
              </div>
              <div className="flex-vbox justify-content-start py-1 w-100">
                <div className="flex-grow-0 flex-hbox justify-content-start align-items-center w-100">
                  <FeatherIcon.FileText className="me-2 text-primary" size={14} />
                  <span>New Direct Customer ({signedAaContractsCustomer.length})</span>
                </div>
                <div className="flex-vbox">
                  <BBRefMultiUserCustomer placeholder='Enter customer' label='' hideMoreInfo
                    appContext={appContext} pageContext={pageContext} minWidth={400}
                    bean={signedAaContractsCustomer} beanIdField={'code'} beanLabelField={'partnerName'} partnerTypes={['CUSTOMERS']}
                    onPostUpdate={this.onModifyHighlights} />
                </div>
              </div>

              <div className="flex-vbox justify-content-start py-1 w-100">
                <div className="flex-grow-0 flex-hbox justify-content-start align-items-center w-100">
                  <FeatherIcon.Calendar className="me-2 text-info" size={14} />
                  <span>Meeting with Agents ({meetingIn2WeeksAgent.length})</span>
                </div>
                <div className="flex-vbox">
                  <BBRefMultiUserCustomer placeholder='Enter meeting with agent' label='' hideMoreInfo
                    appContext={appContext} pageContext={pageContext} minWidth={400} showCountry
                    bean={meetingIn2WeeksAgent} beanIdField={'code'} beanLabelField={'partnerName'}
                    partnerTypes={['AGENTS', 'AGENTS_APPROACHED']}
                    onPostUpdate={this.onModifyHighlights} />
                </div>
              </div>


              <div className="flex-vbox justify-content-start py-1 w-100">
                <div className="flex-grow-0 flex-hbox justify-content-start align-items-center w-100">
                  <FeatherIcon.Calendar className="me-2 text-info" size={14} />
                  <span>Meeting with Direct Customer ({meetingIn2WeeksCustomer.length})</span>
                </div>
                <div className="flex-vbox">
                  <BBRefMultiUserCustomer placeholder='Enter meeting with customer' label='' hideMoreInfo
                    appContext={appContext} pageContext={pageContext} minWidth={400} showCountry
                    bean={meetingIn2WeeksCustomer} beanIdField={'code'} beanLabelField={'partnerName'}
                    partnerTypes={['CUSTOMERS', 'CUSTOMER_LEAD',]}
                    onPostUpdate={this.onModifyHighlights} />
                </div>
              </div>

              <div className="flex-vbox justify-content-start py-1 w-100">
                <div className="flex-grow-0 flex-hbox justify-content-start align-items-center w-100">
                  <FeatherIcon.Calendar className="me-2 text-info" size={14} />
                  <span>New approaching Agent ({newAgentsApproachedIn2Weeks.length})</span>
                </div>
                <div className="flex-vbox">
                  <BBRefMultiUserCustomer placeholder='Enter new approaching agent' label='' hideMoreInfo
                    appContext={appContext} pageContext={pageContext} minWidth={400}
                    bean={newAgentsApproachedIn2Weeks} beanIdField={'code'} beanLabelField={'partnerName'}
                    partnerTypes={['AGENTS', 'AGENTS_APPROACHED']}
                    onPostUpdate={this.onModifyHighlights} />
                </div>
              </div>

              <div className="flex-vbox justify-content-start py-1 w-100">
                <div className="flex-grow-0 flex-hbox justify-content-start align-items-center w-100">
                  <FeatherIcon.Calendar className="me-2 text-info" size={14} />
                  <span>New approaching Direct Customer ({newCustomersApproachedIn2Weeks.length})</span>
                </div>
                <div className="flex-vbox">
                  <BBRefMultiUserCustomer placeholder='Enter new approaching customer' label='' hideMoreInfo
                    appContext={appContext} pageContext={pageContext} minWidth={400}
                    bean={newCustomersApproachedIn2Weeks} beanIdField={'code'} beanLabelField={'partnerName'}
                    partnerTypes={['CUSTOMERS', 'CUSTOMER_LEAD']}
                    onPostUpdate={this.onModifyHighlights} />
                </div>
              </div>

              <bs.CssTooltip position='top-right' width={400} offset={{ x: 200, y: 10 }}>
                <bs.CssTooltipToggle className='flex-vbox justify-content-start px-0 py-1 w-100'>
                  <div className='flex-grow-0 flex-hbox justify-content-start align-items-center w-100'>
                    <FeatherIcon.Star className="me-2 text-warning" size={14} />
                    <span>Other highlights</span>
                  </div>
                  <input.BBTextField bean={highlights} field="otherHighlights" style={{ height: '4em' }}
                    onInputChange={(bean: any, field: string, oldVal: any, newVal: any) => this.forceUpdate()} />
                </bs.CssTooltipToggle>
                <bs.CssTooltipContent className="d-flex flex-column rounded" >
                  <div className="tooltip-header mb-2">
                    <span className="tooltip-title">Other highlights:</span>
                  </div>
                  <ul className="mb-2 ps-3">
                    {(highlights['otherHighlights'] || '...').split('\n').map((line: string, i: number) => (
                      <li key={i}>{line}</li>
                    ))}
                  </ul>
                </bs.CssTooltipContent>
              </bs.CssTooltip>

              <bs.CssTooltip position='top-right' width={400} offset={{ x: 200, y: 10 }}>
                <bs.CssTooltipToggle className='flex-vbox justify-content-start px-0 py-1 w-100'>
                  <div className='flex-grow-0 flex-hbox justify-content-start align-items-center w-100'>
                    <h6 className="fw-bold py-2"><FeatherIcon.AlertTriangle className="me-2" size={16} />LOWLIGHTS</h6>
                  </div>
                  <input.BBTextField bean={highlights} field="lowlights" style={{ height: '4em' }}
                    onInputChange={(bean: any, field: string, oldVal: any, newVal: any) => this.forceUpdate()} />
                </bs.CssTooltipToggle>
                <bs.CssTooltipContent className="d-flex flex-column rounded" >
                  <div className="tooltip-header mb-2">
                    <span className="tooltip-title">LOWLIGHTS:</span>
                  </div>
                  <ul className="mb-2 ps-3">
                    {(highlights['lowlights'] || '...').split('\n').map((line: string, i: number) => (
                      <li key={i}>{line}</li>
                    ))}
                  </ul>
                </bs.CssTooltipContent>
              </bs.CssTooltip>
            </div>
          </div>
        </div>

        {/* ------------------- Forecast | Suggestion/ Request ----------------------- */}
        <div className="bg-white border rounded-3 flex-vbox justify-content-center align-items-center gap-1 flex-grow-0 w-50"
          style={{
            width: '40%',
            borderColor: borderColor,
            transition: 'all 0.3s ease',
            marginBottom: '10px'
          }}
          onMouseEnter={(e) => Object.assign(e.currentTarget.style, hoverStyle)}
          onMouseLeave={(e) => {
            e.currentTarget.style.boxShadow = 'none';
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.backgroundColor = '#fff';
          }}>

          <div className="flex-vbox mx-2 p-1 bg-white rounded-md w-100" style={{ minHeight: 250 }}>
            <div className="flex-hbox flex-grow-0 align-items-center justify-content-between border-bottom px-2 py-1">
              <h5 style={{ color: '#6c757d', fontSize: '0.9rem' }}><FeatherIcon.Star className="me-2" size={18} />FORECAST</h5>
            </div>

            <div className="flex-vbox">
              <div className="flex-hbox justify-content-start align-items-center px-0 py-1">
                <div className='flex-hbox justify-content-start align-items-center flex-grow-0 me-2' style={{ minWidth: 130 }}>
                  <FeatherIcon.Star className="me-2 text-warning" size={12} />
                  <span className="text-nowrap">Air Volume</span>
                </div>
                <div className="flex-hbox align-items-center">
                  <input.BBStringField bean={airVolume} field="kgs" />
                  <span className="text-nowrap me-2" style={{ fontSize: '0.75rem' }}>KGS</span>
                </div>
              </div>
              <div className="flex-hbox justify-content-start align-items-center px-0 py-1">
                <div className='flex-hbox justify-content-start align-items-center flex-grow-0 me-2' style={{ minWidth: 130 }}>
                  <FeatherIcon.Star className="me-2 text-warning" size={12} />
                  <span className="text-nowrap">Sea Volume</span>
                </div>
                <div className="flex-hbox align-items-center me-2">
                  <input.BBStringField bean={seaVolume} field="teus" />
                  <span className="text-nowrap me-2" style={{ fontSize: '0.75rem' }}>TEUs</span>
                </div>
                <div className="flex-hbox align-items-center">
                  <input.BBStringField bean={seaVolume} field="cbm" />
                  <span className="text-nowrap me-2" style={{ fontSize: '0.75rem' }}>CBM</span>
                </div>
              </div>
              <div className="flex-hbox justify-content-start align-items-center px-0 py-1">
                <div className='flex-hbox justify-content-start align-items-center flex-grow-0 me-2' style={{ minWidth: 130 }}>
                  <FeatherIcon.Star className="me-2 text-warning" size={12} />
                  <span className='text-nowrap'>Estimate</span>
                </div>
                <div className="flex-hbox align-items-center me-2">
                  <input.BBStringField bean={estimate} field="gp" />
                  <span className='text-nowrap me-2' style={{ fontSize: '0.75rem' }}>GP</span>
                </div>
                <div className="flex-hbox align-items-center">
                  <input.BBStringField bean={estimate} field="revenue" />
                  <span className='text-nowrap me-2' style={{ fontSize: '0.75rem' }}>Revenue(USD)</span>
                </div>
              </div>
            </div>

          </div>

          <div className="flex-vbox mx-2 p-1 bg-white rounded-md w-100 mt-2">
            <div className="flex-hbox flex-grow-0 align-items-center justify-content-between border-bottom px-2 py-1">
              <h5 style={{ color: '#6c757d', fontSize: '0.9rem' }}><FeatherIcon.MessageCircle className="me-2" size={18} />SUGGESTION/ REQUEST</h5>
            </div>

            <div className="flex-vbox p-2">
              <input.BBTextField bean={this.reportBean} field="suggestionOrRequest" style={{
                height: 170,
                fontSize: '1rem',
              }}
                placeholder='Any suggestion/ request for improvement?' />
            </div>
          </div>
        </div>
      </div>
    )
  }


  renderSaleFormSection() {
    const { appContext, pageContext } = this.props;
    const highlights: any = this.reportBean['highlights'] || {}
    let newCustomers: Array<any> = highlights['newCustomers'] || [];
    let expectedLeadsToWin: Array<any> = highlights['expectedLeadsToWin'] || [];
    let forecast: any = this.reportBean['forecast'] || {}


    const lightColor = '#D6EFD8';
    const borderColor = '#80AF81';

    const hoverStyle: any = {
      boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
      transform: 'translateY(-2px)',
      backgroundColor: lightColor
    };
    return (
      <div className="flex-hbox align-items-start justify-content-between flex-grow-0 flex-shrink-0 p-1 mb-1 gap-1" key={this.viewId}>

        <div className="bg-white border rounded-3 flex-vbox justify-content-center align-items-center"
          style={{
            borderColor: borderColor,
            transition: 'all 0.3s ease',
            marginBottom: '10px'
          }}
          onMouseEnter={(e) => Object.assign(e.currentTarget.style, hoverStyle)}
          onMouseLeave={(e) => {
            e.currentTarget.style.boxShadow = 'none';
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.backgroundColor = '#fff';
          }}>

          <div className="flex-vbox mx-2 p-1 bg-white rounded-md w-100">
            <div className="flex-hbox flex-grow-0 align-items-center justify-content-between border-bottom px-2 py-1">
              <h5 style={{ color: '#6c757d', fontSize: '0.9rem' }}><FeatherIcon.Award className="me-2" size={18} />HIGHLIGHTS</h5>
            </div>

            <div className="flex-vbox" style={{ flex: 1 }}>
              <div className="flex-vbox justify-content-start py-1 w-100">
                <div className="flex-grow-0 flex-hbox justify-content-start align-items-center w-100">
                  <FeatherIcon.FileText className="me-2 text-primary" size={14} />
                  <span>Khách hàng mới</span>
                </div>
                <div className="flex-vbox">
                  <BBRefMultiUserCustomer beanIdField='partnerCode' beanLabelField='name' partnerTypes={['CUSTOMERS']}
                    appContext={appContext} pageContext={pageContext} placeholder="Enter your customers..." className='w-100'
                    placement="bottom-start" offset={[0, 5]} bean={newCustomers} minWidth={400}
                    onPostUpdate={this.onModifyHighlights} />
                </div>
              </div>

              <div className="flex-vbox justify-content-start py-1 w-100">
                <div className="flex-grow-0 flex-hbox justify-content-start align-items-center w-100">
                  <FeatherIcon.Calendar className="me-2 text-info" size={14} />
                  <span>Khách hàng tiềm năng, dự kiến win (next month)</span>
                </div>
                <div className="flex-vbox">
                  <BBRefMultiCustomerLead beanIdField='code' beanLabelField='name'
                    appContext={appContext} pageContext={pageContext} placeholder="Enter your lead..." className='w-100'
                    placement="bottom-start" offset={[0, 5]} bean={expectedLeadsToWin} minWidth={400}
                    onPostUpdate={this.onModifyHighlights}
                    refCustomerLeadBy={'code'} />
                </div>
              </div>

              <bs.CssTooltip position='top-right' width={400} offset={{ x: 200, y: 10 }}>
                <bs.CssTooltipToggle className='flex-vbox justify-content-start py-1 w-100'>
                  <div className='flex-grow-0 flex-hbox justify-content-start align-items-center w-100'>
                    <FeatherIcon.Users className="me-2 text-success" size={14} />
                    <span>Đánh giá thị trường</span>
                  </div>
                  <input.BBTextField bean={highlights} field="marketInformation" style={{ height: '5rem' }}
                    onInputChange={this.onModifyHighlights} />
                </bs.CssTooltipToggle>
                <bs.CssTooltipContent className="d-flex flex-column rounded">
                  <div className="tooltip-header mb-2">
                    <span className="tooltip-title">Đánh giá thị trường:</span>
                  </div>
                  <ul className="mb-2 ps-3">
                    {(highlights['marketInformation'] || '...').split('\n').map((line: string, i: number) => (
                      <li key={i}>{line}</li>
                    ))}
                  </ul>
                </bs.CssTooltipContent>
              </bs.CssTooltip>

              <bs.CssTooltip position='top-right' width={400} offset={{ x: 200, y: 10 }}>
                <bs.CssTooltipToggle className='flex-vbox justify-content-start py-1 w-100'>
                  <div className='flex-grow-0 flex-hbox justify-content-start align-items-center w-100'>
                    <FeatherIcon.Star className="me-2 text-warning" size={14} />
                    <span>Các vấn đề phát sinh</span>
                  </div>
                  <input.BBTextField bean={highlights} field="reportedIssues" style={{ height: '5rem' }}
                    onInputChange={this.onModifyHighlights} />
                </bs.CssTooltipToggle>
                <bs.CssTooltipContent className="d-flex flex-column rounded">
                  <div className="tooltip-header mb-2">
                    <span className="tooltip-title">Các vấn đề phát sinh:</span>
                  </div>
                  <ul className="mb-2 ps-3">
                    {(highlights['reportedIssues'] || '...').split('\n').map((line: string, i: number) => (
                      <li key={i}>{line}</li>
                    ))}
                  </ul>
                </bs.CssTooltipContent>
              </bs.CssTooltip>
            </div>
          </div>
        </div>

        {/* ------------------- Forecast | Suggestion/ Request ----------------------- */}
        <div className="bg-white border rounded-3 flex-vbox justify-content-center align-items-center gap-1"
          style={{
            borderColor: borderColor,
            transition: 'all 0.3s ease',
            marginBottom: '10px'
          }}
          onMouseEnter={(e) => Object.assign(e.currentTarget.style, hoverStyle)}
          onMouseLeave={(e) => {
            e.currentTarget.style.boxShadow = 'none';
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.backgroundColor = '#fff';
          }}>

          <div className="flex-vbox mx-2 p-1 bg-white rounded-md w-100" style={{ minHeight: 180 }}>
            <div className="flex-hbox flex-grow-0 align-items-center justify-content-between border-bottom px-2 py-1">
              <h5 style={{ color: '#6c757d', fontSize: '0.9rem' }}><FeatherIcon.Star className="me-2" size={18} />FORECAST</h5>
            </div>

            <div className="flex-vbox">
              <input.BBTextField bean={forecast} field="forecast" style={{ height: 170, fontSize: '1rem', }} />
            </div>

          </div>

          <div className="flex-vbox mx-2 p-1 bg-white rounded-md w-100 mt-2">
            <div className="flex-hbox flex-grow-0 align-items-center justify-content-between border-bottom px-2 py-1">
              <h5 style={{ color: '#6c757d', fontSize: '0.9rem' }}><FeatherIcon.MessageCircle className="me-2" size={18} />SUGGESTION/ REQUEST</h5>
            </div>

            <div className="flex-vbox p-2">
              <input.BBTextField bean={this.reportBean} field="suggestionOrRequest" style={{
                height: 150,
                fontSize: '1rem',
              }}
                placeholder='Any suggestion/ request for improvement?' />
            </div>
          </div>
        </div>
      </div>

    )
  }

  render() {
    const { appContext, pageContext } = this.props;
    const writeCap = pageContext.hasUserWriteCapability();
    const moderatorCap = pageContext.hasUserModeratorCapability();
    const isOwnerReport: boolean = this.reportBean['salemanAccountId'] === SESSION.getAccountId();
    return (

      <div className="flex-vbox bg-white rounded-md" >
        <div className="flex-vbox bg-white rounded-md bg-body-highlight" >
          <bs.GreedyScrollable className='p-1'>

            {/* ------------------- Key Account Performance ----------------------- */}
            <div className="flex-vbox flex-grow-0">
              <UIKeyAccountPerformance
                appContext={appContext} pageContext={pageContext} space={'User'} hiddenHeader reportBean={this.reportBean} />
            </div>
            {this.reportBean['type'] === 'BD' ? this.renderBDFormSection() : this.renderSaleFormSection()}
          </bs.GreedyScrollable>

        </div>
        <bs.Toolbar size='sm' className='border p-1 m-1' hide={!writeCap || (!isOwnerReport && !moderatorCap)}>
          <entity.WButtonEntityWrite
            appContext={appContext} pageContext={pageContext}
            label={'Save'} onClick={this.onSave} />
        </bs.Toolbar>

      </div>
    )
  }
}

