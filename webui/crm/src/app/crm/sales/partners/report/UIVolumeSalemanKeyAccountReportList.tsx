import React from "react";

import * as FeatherIcon from 'react-feather'
import { util, grid, bs, entity, input, sql } from '@datatp-ui/lib';
import { T } from "../../backend";

export class UIVolumeSalemanKeyAccountReportPlugin extends entity.DbEntityListPlugin {
  constructor() {
    super([]);

    this.backend = {
      service: 'PartnerReportService',
      searchMethod: 'searchVolumeSalemanKeyAccountReport',
    }
    this.searchParams = {
      params: {
      },
      filters: [...sql.createSearchFilter()],
      maxReturn: 1000
    }
  }

  loadData(uiList: entity.DbEntityList<any>): void {
    this.createBackendSearch(uiList, { params: this.searchParams }).call();
  }

  withReportedDate(reportedDateFrom: string, reportedDateTo: string) {
    this.addSearchParam("fromDate", reportedDateFrom);
    this.addSearchParam("toDate", reportedDateTo);
    return this;
  }
}
export interface UIVolumeSalemanKeyAccountReportListProps extends entity.DbEntityListProps {
  hideSubtotalColumns?: boolean;
}

export class UIVolumeSalemanKeyAccountReportList extends entity.DbEntityList<UIVolumeSalemanKeyAccountReportListProps> {

  createVGridConfig() {
    let { hideSubtotalColumns = false } = this.props;
    let config: grid.VGridConfig = {
      record: {
        dataCellHeight: 40,
        fields: [
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          {
            name: 'transactionId', label: T('Transaction ID'), width: 160, filterable: true,
          },
          {
            name: 'transactionDate', label: T('Created Date'), width: 130, filterable: true,
            format: util.text.formater.compactDate
          },
          {
            name: 'reportDate', label: T('ETD/ ETA'), width: 130, filterable: true,
            format: util.text.formater.compactDate
          },
          { name: 'shipmentType', label: T('Shipment Type'), width: 150, filterable: true, },
          { name: 'typeOfService', label: T('Type of Service'), width: 200, filterable: true, },
          { name: 'hawbNo', label: T('Hawb No'), width: 200, filterable: true, },
          { name: 'customerName', label: T('Customer Name'), width: 200, filterable: true, },

          { name: 'hawbGw', label: T('G.W'), width: 120, },
          { name: 'hawbCw', label: T('C.W'), width: 120, },
          { name: 'hawbCbm', label: T('CBM'), width: 120, },
          { name: 'containerSize', label: T('Container Size'), width: 200, },
          ...(!hideSubtotalColumns ? [
            { name: 'subtotalSellingVnd', label: T('SubTotal Selling (VND)'), width: 200, },
            { name: 'subtotalBuyingVnd', label: T('SubTotal Buying (VND)'), width: 200, },
            { name: 'subtotalOtherDebitVnd', label: T('Other Debit (VND)'), width: 200, },
            { name: 'subtotalOtherCreditVnd', label: T('Other Credit (VND)'), width: 200, },
          ] : []),
          { name: 'agentCode', label: T('Agent Code'), width: 100, filterable: true, },
          { name: 'agentName', label: T('Agent Name'), width: 200, },
        ],
      },
      toolbar: {
        hide: true,
      },
      footer: {
        page: {
          render: (ctx: grid.VGridContext) => {
            let { } = this.props;
            return (
              <bs.Toolbar className='border'>
                <bs.Button laf="secondary" outline size="sm" className="p-1" style={{ color: '#6c757d' }} onClick={this.doExport}>
                  <FeatherIcon.Download size={14} className="me-2" />
                  Export
                </bs.Button>
              </bs.Toolbar>)
          }
        },
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          },
        }
      }
    }
    return config;
  }

  render(): React.JSX.Element {
    if (this.isLoading()) return this.renderLoading();
    return this.renderUIGrid();
  }

  doExport = () => {
    let { appContext, hideSubtotalColumns = false } = this.props;

    let shipmentDetailGroup: entity.GroupField = {
      label: "_blank_",
      fields: [
        { label: "STT.", name: "stt", dataType: 'number' },
        { label: "Transaction ID", name: "transactionId", dataType: 'string' },
        { label: "Created Date", name: "transactionDate", dataType: 'date' },
        { label: "ETD/ ETA", name: "reportDate", dataType: 'date' },
        { label: "Shipment Type", name: "shipmentType", dataType: 'string' },
        { label: "Type of Service", name: "typeOfService", dataType: 'string' },
        { label: "Hawb No", name: "hawbNo", dataType: 'string' },
        { label: "Customer Name", name: "customerName", dataType: 'string' },
        { label: "G.W", name: "hawbGw", dataType: 'number' },
        { label: "C.W", name: "hawbCw", dataType: 'number' },
        { label: "CBM", name: "hawbCbm", dataType: 'number' },
        { label: "Container Size", name: "containerSize", dataType: 'string' },
        ...(!hideSubtotalColumns ? [
          { label: "SubTotal Selling (VND)", name: "subtotalSellingVnd", dataType: 'number' },
          { label: "SubTotal Buying (VND)", name: "subtotalBuyingVnd", dataType: 'number' },
          { label: "Other Debit (VND)", name: "subtotalOtherDebitVnd", dataType: 'number' },
          { label: "Other Credit (VND)", name: "subtotalOtherCreditVnd", dataType: 'number' },
        ] : []),
        { label: "Agent Code", name: "agentCode", dataType: 'string' },
        { label: "Agent Name", name: "agentName", dataType: 'string' },
      ],
    }

    let records: Array<any> = [];
    let targetRecords = this.vgridContext.model.getRecords();
    for (let i = 0; i < targetRecords.length; i++) {
      let record = targetRecords[i];
      let newRecord: any = {
        stt: i + 1,
        transactionId: record.transactionId || '',
        transactionDate: record.transactionDate || '',
        reportDate: record.reportDate || '',
        shipmentType: record.shipmentType || '',
        typeOfService: record.typeOfService || '',
        hawbNo: record.hawbNo || '',
        customerName: record.customerName || '',
        hawbGw: record.hawbGw || 0,
        hawbCw: record.hawbCw || 0,
        hawbCbm: record.hawbCbm || 0,
        containerSize: record.containerSize || '',
        agentCode: record.agentCode || '',
        agentName: record.agentName || '',
      };
      if (!hideSubtotalColumns) {
        newRecord.subtotalSellingVnd = record.subtotalSellingVnd || 0;
        newRecord.subtotalBuyingVnd = record.subtotalBuyingVnd || 0;
        newRecord.subtotalOtherDebitVnd = record.subtotalOtherDebitVnd || 0;
        newRecord.subtotalOtherCreditVnd = record.subtotalOtherCreditVnd || 0;
      }
      records.push(newRecord);
    }

    let exportModel: entity.DataListExportModel = {
      fieldGroups: [],
      fields: [...shipmentDetailGroup.fields],
      records: records,
      modelName: 'Shipment_Detail',
      fileName: `Shipment_Detail_${util.TimeUtil.toCompactDateFormat(new Date())}.xlsx`,
    }

    appContext.createHttpBackendCall('DataMappingService', 'xlsxPrivateExport', { model: exportModel })
      .withSuccessData((data: any) => {
        let storeInfo = data;
        if (storeInfo.type == 'ir.actions.act_url') {
          entity.StoreInfo.download(storeInfo.url);
        } else if (storeInfo.privateMode) {
          entity.StoreInfo.privateDownload(storeInfo);
        } else {
          entity.StoreInfo.download(storeInfo.url);
        }
      })
      .call()
  }

}