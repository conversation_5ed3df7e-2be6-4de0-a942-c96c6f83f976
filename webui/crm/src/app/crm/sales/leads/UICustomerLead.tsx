import React from 'react'
import * as FeatherIcon from 'react-feather'

import { bs, input, entity, app, util } from '@datatp-ui/lib';

import { module } from '@datatp-ui/erp';
import { LeadUtils } from './LeadUtils';
import { T } from '../backend';
import { UICheckBFSOnePartnerList } from 'app/crm/partner/WCheckPartnerTaxCode';
import { BBRefCrmUserRole } from 'app/crm/common/template/BBRefCrmUserRole';

const SOURCE_OPTIONS: string[] = [
  "WCA", "WPA", "GFFG", "CLC Projects",
  "A2B (not a member of associations in this list, Agent has one-way nominations to <PERSON>)",
  "B2A (not a member of associations in this list, <PERSON> has one-way nominations to Agent)",
  "A2B2A (not a member of associations in this list, Agent and <PERSON> have reciprocal nominations)",
  "FREEHAND", "BEE", "MLN (Millenium Logistics Network)",
  "FREIGHT MIDPOINT", "LOGNET", "GAA", "PCN", "CFN",
  "EAA", "DCS", "ISS", "FCUBE", "TALA",
  "FPS - FAMOUS PACIFIC SHIPPING", "JOINT SALES", "PANGEA NETWORK", "GLA", "JCTRANS"
]

import BBRefCountry = module.settings.BBRefCountry;
import BBRefLocation = module.settings.BBRefLocation;
import BBRefState = module.settings.BBRefState;
import { UIPartnerContactListEditor } from './UIPartnerContactList';

const SESSION = app.host.DATATP_HOST.session;

export interface UICustomerLeadProps extends entity.AppComplexEntityEditorProps {
}

export class UICustomerLeadSimpleEditor extends entity.AppDbComplexEntityEditor<UICustomerLeadProps> {

  state = {
    isSending: false
  };

  onCheckTaxCode = (_wInput: input.WInput, _bean: any, _field: string, _oldVal: any, newVal: any) => {
    const { appContext } = this.props;
    appContext.createHttpBackendCall('CustomerLeadsService', 'ensureValidCustomerLeadByTaxCode', { taxCode: newVal })
      .withSuccessData((customerLead: any) => {
        if (!customerLead || Object.keys(customerLead).length === 0) return;
        let saleMan: string = customerLead['saleManLabel'] || '';
        let leadStatus = LeadUtils.getStatusInfo(customerLead['status']);
        let message = (
          <div className="ms-1 text-warning py-3 border-bottom">
            <div className="px-3 py-3 text-center border-bottom rounded" style={{ backgroundColor: '#d1ecf1', color: '#0c5460', fontSize: '1rem', fontWeight: 'bold' }}>
              <i className="bi bi-info-circle-fill" style={{ fontSize: '1.5rem', marginRight: '0.5rem' }}></i>
              A partner with the tax code "{newVal}" already exists in the system.
            </div>
            <div className="px-3 py-2 mt-2">
              <div className="d-flex align-items-center mb-2">
                <span className="fw-bold" style={{ color: '#0c5460', width: '120px' }}>Sales Owner:</span>
                <span className="ms-2" style={{ color: '#155724' }}>{saleMan || 'Not Assigned'}</span>
              </div>
              <div className="d-flex align-items-center">
                <span className="fw-bold" style={{ color: '#0c5460', width: '120px' }}>Lead Status:</span>
                <span className="ms-2 px-2 py-1 rounded" style={{
                  backgroundColor: '#e2e3e5',
                  color: '#383d41',
                  fontSize: '0.9em'
                }}>{`${leadStatus.value} - ${leadStatus.desc}`}</span>
              </div>
            </div>
          </div>
        );
        bs.dialogShow('Invalid Tax Code', message, { backdrop: 'static', size: 'md' });
      })
      .call();
  }

  onPreCommit = (observer: entity.ComplexBeanObserver) => {
    let lead = observer.getMutableBean();
    lead['dateModified'] = util.TimeUtil.javaCompactDateTimeFormat(new Date());

    if (lead['accountCreatorId']) {
      lead['accountCreatorId'] = SESSION.getAccountId();
      lead['accountCreatorLabel'] = SESSION.getAccountAcl().getFullName();
    }

    let missingFields: string[] = [];

    if (!lead['taxCode']) missingFields.push('Taxcode');
    if (!lead['salemanAccountId']) missingFields.push('Saleman');
    if (!lead['email'] || !lead['cell']) missingFields.push('Email, Phone');
    if (!lead['address']) missingFields.push('Address');
    if (!lead['countryId']) missingFields.push('Country');
    if (!lead['personalContact']) missingFields.push('Personal Contact');
    if (!lead['name']) missingFields.push('Partner Name (Abb)');
    if (!lead['label']) missingFields.push('Partner Name (Abb');
    if (!lead['provinceId'] && lead['countryLabel'] === 'VIETNAM') missingFields.push('Province');

    if (missingFields.length > 0) {
      bs.dialogShow('Missing Information',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />
          {`Please provide: ${missingFields.join(', ')}.`}
        </div>,
        { backdrop: 'static', size: 'sm' }
      );
      throw new Error(`Please provide: ${missingFields.join(', ')}.`);
    }

    this.setState({ isSending: true });
  }

  onPostCommit = (entity: any) => {
    let { onPostCommit } = this.props;
    this.nextViewId();
    this.setState({ isSending: false });


    if (onPostCommit) {
      onPostCommit(entity, this);
    } else {
      this.forceUpdate();
    }
  }

  render() {
    let { appContext, pageContext, observer, readOnly } = this.props;
    let writeCap = pageContext.hasUserWriteCapability() && !readOnly;
    let customerLead = observer.getMutableBean();
    const isNew = observer.isNewBean();

    return (
      <div className="flex-vbox">

        <div className='p-1'>

          {
            !isNew
            &&
            <bs.Row>
              <bs.Col span={3}>
                <input.BBStringField bean={customerLead} field='code' label={T("Code")} disable />
              </bs.Col>

              <bs.Col span={3}>
                <input.BBDateTimeField
                  className='rdt-bottom' bean={customerLead} label={T('Date Created')}
                  field={'date'} dateFormat={"DD/MM/YYYY"} timeFormat={false} disable />
              </bs.Col>

              <bs.Col span={6}>
                <BBRefCrmUserRole style={{ minWidth: 250 }} minWidth={400} label='Creator'
                  appContext={appContext} pageContext={pageContext} bean={customerLead} disable
                  beanIdField='accountCreatorId' beanLabelField='accountCreatorLabel' hideMoreInfo
                  placeholder='Creator...'
                  onPostUpdate={(_inputUI, bean, selectOpt, _userInput) => {
                    bean['accountCreatorId'] = selectOpt['accountId'];
                    bean['accountCreatorLabel'] = selectOpt['fullName'];
                  }} />
              </bs.Col>
            </bs.Row>
          }

          <bs.Row>
            <bs.Col span={6}>
              <input.BBStringField required
                bean={customerLead} field='name' label={T("Name")} disable={!writeCap}
                placeholder='Title.....'
                onInputChange={(bean: any, field: string, oldVal: any, newVal: any) => {
                  if (!bean['label']) bean['label'] = newVal;
                  this.forceUpdate();
                }} />
            </bs.Col>
            <bs.Col span={6}>
              <input.BBStringField
                placeholder='Company.....'
                bean={customerLead} field='label' label={T("Company (Fullname)")} disable={!writeCap} />
            </bs.Col>
          </bs.Row>

          <bs.Row>
            <bs.Col span={3}>
              <input.BBStringField
                bean={customerLead} field='taxCode' label={T("Tax Code")} disable={!writeCap} required
                onBgInputChange={this.onCheckTaxCode} />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBStringField
                bean={customerLead} field='personalContact' label={T("Personal Contact")} disable={!writeCap} required />
            </bs.Col>

            <bs.Col span={3}>
              <input.BBStringField
                bean={customerLead} field='cell' label={T("Cell Phone")} disable={!writeCap} required />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBStringField
                bean={customerLead} field='email' label={T("Email.")} disable={!writeCap} required />
            </bs.Col>
          </bs.Row>


          <bs.Row>
            <bs.Col span={3}>
              <BBRefCountry key={util.IDTracker.next()}
                appContext={appContext} pageContext={pageContext}
                placement="bottom-start" offset={[0, 5]} minWidth={350} required
                disable={!writeCap} label={T('Country')} placeholder="Enter Country"
                bean={customerLead} beanIdField={'countryId'} hideMoreInfo
                beanLabelField={'countryLabel'} refCountryBy='id'
                onPostUpdate={(_inputUI, bean, selectOpt, _userInput) => {
                  bean['countryId'] = selectOpt['id'];
                  bean['countryLabel'] = selectOpt['label'];
                  this.forceUpdate();
                }}
              />
            </bs.Col>
            <bs.Col span={3}>
              <BBRefState key={util.IDTracker.next()}
                appContext={appContext} pageContext={pageContext}
                placement="bottom-start" offset={[0, 5]} minWidth={350}
                disable={!writeCap} label={T('Province')} placeholder="Enter Province"
                bean={customerLead} beanIdField={'provinceId'} hideMoreInfo required
                beanLabelField={'provinceLabel'} countryId={customerLead.countryId} />
            </bs.Col>

            <bs.Col span={6}>
              <BBRefLocation label='KCN' minWidth={300} style={{ maxWidth: 800 }} placement='auto-start'
                appContext={appContext} pageContext={pageContext} bean={customerLead}
                beanIdField={'kcnCode'} beanLabelField={'kcnLabel'} placeholder='KCN' hideMoreInfo
                disable={!writeCap} inputObserver={observer} refLocationBy='id' locationTypes={['KCN']}
                beanRefLabelField='label'
                onPostUpdate={(_inputUI, bean, selectOpt, _userInput) => {
                  if (selectOpt && selectOpt['id']) {
                    bean['countryId'] = selectOpt['countryId'];
                    bean['countryLabel'] = selectOpt['countryLabel'];
                    bean['provinceId'] = selectOpt['stateId'];
                    bean['provinceLabel'] = selectOpt['stateLabel'];
                    bean['kcnLabel'] = selectOpt['label'];
                    bean['address'] = selectOpt['address'];
                    this.forceUpdate();
                  }
                }} />
            </bs.Col>
          </bs.Row>

          <bs.Row>
            <bs.Col span={12}>
              <input.BBTextField
                bean={customerLead} label={T('Address')} field="address" disable={!writeCap}
                style={{ height: '4em' }} />
            </bs.Col>
          </bs.Row>

          <bs.Row>
            <bs.Col span={3}>
              <input.BBSelectField bean={customerLead} field="source" label={T('Source')}
                options={SOURCE_OPTIONS} disable={!writeCap} />
            </bs.Col>
            <bs.Col span={3}>
              <module.resource.BBRefResource
                appContext={appContext} pageContext={pageContext}
                placement="bottom-start" offset={[0, 5]} minWidth={350}
                disable={!writeCap} label={T('Industry')} placeholder="Enter Industry"
                required bean={customerLead} beanIdField={'industryCode'} hideMoreInfo
                beanLabelField={'industryLabel'} resourceType={"industry"} refResourceBy="identifier" />
            </bs.Col>

            <bs.Col span={6}>
              <BBRefCrmUserRole minWidth={400} hideMoreInfo label='Saleman'
                appContext={appContext} pageContext={pageContext} bean={customerLead} disable={!writeCap || !observer.isNewBean()}
                beanIdField='salemanAccountId' beanLabelField='salemanLabel' placeholder='Saleman.'
                onPostUpdate={(_inputUI, bean, selectOpt, _userInput) => {
                  bean['salemanAccountId'] = selectOpt['accountId'];
                  bean['salemanCompanyId'] = selectOpt['companyBranchId'];
                  bean['salemanLabel'] = selectOpt['fullName'];
                  this.forceUpdate();
                }} />
            </bs.Col>
          </bs.Row>

          <bs.Row>
            <bs.Col span={3}>
              <input.BBStringField
                bean={customerLead} field='routing' label={T("Route")} disable={!writeCap} />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBStringField
                bean={customerLead} field='volumeNote' label={T("Volume")} disable={!writeCap} />
            </bs.Col>
            <bs.Col span={6}>
              <input.BBStringField
                bean={customerLead} field='customerOnboardingProgress' label={T("Progress")} disable={!writeCap} />
            </bs.Col>
          </bs.Row>

          <bs.Row>
            <bs.Col span={12}>
              <input.BBTextField bean={customerLead} label={T('Note')} field="note" disable={!writeCap} style={{ height: '8em' }} />
            </bs.Col>
          </bs.Row>
        </div>

        <div className='flex-grow-0 flex-hbox justify-content-end align-items-center mt-1 p-1 border-top'>

          <entity.ButtonEntityCommit className='py-1 px-3 mx-2' style={{ width: 120 }} btnLabel={`${this.state.isSending ? 'Saving...' : 'Save'}`}
            appContext={appContext} pageContext={pageContext}
            disable={this.state.isSending}
            observer={observer} hide={!writeCap}
            commit={{
              entityLabel: T(customerLead.code),
              context: 'company',
              service: 'CustomerLeadsService', commitMethod: 'saveCustomerLead'
            }}
            onPreCommit={this.onPreCommit}
            onPostCommit={this.onPostCommit} />
        </div>

      </div>
    )
  }
}

export class UICustomerAgentEditor extends entity.AppDbComplexEntityEditor<UICustomerLeadProps> {

  state = {
    isSending: false
  };

  onCheckAgentExist = (_bean: any, _field: string, _oldVal: any, newVal: any) => {
    const { appContext, pageContext } = this.props;
    appContext.addOSNotification('info', "Checking for duplicate Agent in the system...");

    appContext.createHttpBackendCall('BFSOneCRMService', 'checkPartnerExistInSystem', { searchPattern: newVal })
      .withSuccessData((records: any[]) => {
        if (!records || records.length === 0) return;
        const createPageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => (
          <div className='flex-vbox'>
            <UICheckBFSOnePartnerList
              appContext={appCtx} pageContext={pageCtx} plugin={new entity.DbEntityListPlugin(records)} />
          </div>
        );
        pageContext.createPopupPage(
          `partner-list-${util.IDTracker.next()}`, T(`Existing Partners Found: ${newVal.toUpperCase()}`),
          createPageContent, { size: 'xl', backdrop: 'static' }
        );
      })
      .call();
  }

  onPreCommit = (observer: entity.ComplexBeanObserver) => {
    let agent = observer.getMutableBean();
    agent['dateModified'] = util.TimeUtil.javaCompactDateTimeFormat(new Date());
    if (agent['accountCreatorId']) {
      agent['accountCreatorId'] = SESSION.getAccountId();
      agent['accountCreatorLabel'] = SESSION.getAccountAcl().getFullName();
    }

    let missingFields: string[] = [];

    if (!agent['salemanAccountId']) missingFields.push('Saleman');
    if (!agent['email'] || !agent['cell']) missingFields.push('Email, Cell Phone');
    if (!agent['address']) missingFields.push('Address');
    if (!agent['countryId']) missingFields.push('Country');
    if (!agent['personalContact']) missingFields.push('Personal Contact');
    if (!agent['name']) missingFields.push('Partner Name (Abb)');
    if (!agent['label']) missingFields.push('Partner Name (Abb');

    if (missingFields.length > 0) {
      bs.dialogShow('Missing Information',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />
          {`Please provide: ${missingFields.join(', ')}.`}
        </div>,
        { backdrop: 'static', size: 'sm' }
      );
      throw new Error(`Please provide: ${missingFields.join(', ')}.`);
    }

    this.setState({ isSending: true });
  }

  onPostCommit = (entity: any) => {
    let { onPostCommit } = this.props;
    this.nextViewId();
    this.setState({ isSending: false });
    if (onPostCommit) {
      onPostCommit(entity, this);
    } else {
      this.forceUpdate();
    }
  }

  render() {
    let { appContext, pageContext, observer, readOnly } = this.props;
    let writeCap = pageContext.hasUserWriteCapability() && !readOnly;
    let customerAgent = observer.getMutableBean();
    const isNew = observer.isNewBean();

    return (
      <div className="flex-vbox">

        <div className='p-1 flex-vbox flex-grow-0'>
          {!isNew &&
            <bs.Row>
              <bs.Col span={3}>
                <input.BBStringField bean={customerAgent} field='code' label={T("Code")} disable />
              </bs.Col>

              <bs.Col span={3}>
                <input.BBDateTimeField
                  className='rdt-bottom' bean={customerAgent} label={T('Date Created')}
                  field={'date'} dateFormat={"DD/MM/YYYY"} timeFormat={false} disable />
              </bs.Col>

              <bs.Col span={6}>
                <BBRefCrmUserRole style={{ minWidth: 250 }} minWidth={400} label='Creator'
                  appContext={appContext} pageContext={pageContext} bean={customerAgent} disable
                  beanIdField='accountCreatorId' beanLabelField='accountCreatorLabel' hideMoreInfo
                  placeholder='Creator...'
                  onPostUpdate={(_inputUI, bean, selectOpt, _userInput) => {
                    bean['accountCreatorId'] = selectOpt['accountId'];
                    bean['accountCreatorLabel'] = selectOpt['fullName'];
                  }} />
              </bs.Col>
            </bs.Row>
          }

          <bs.Row>
            <bs.Col span={6}>
              <input.BBStringField required
                bean={customerAgent} field='name' label={T("Agent Name (Abb)")} disable={!writeCap}
                placeholder='Agent Name (Abb).....'
                onInputChange={(bean: any, field: string, oldVal: any, newVal: any) => {
                  if (!bean['label']) bean['label'] = newVal;
                  this.forceUpdate();
                  this.onCheckAgentExist(bean, field, oldVal, newVal);
                }} />
            </bs.Col>
            <bs.Col span={6}>
              <input.BBStringField required
                placeholder='Agent Name'
                bean={customerAgent} field='label' label={T("Agent Name (Fullname)")} disable={!writeCap} />
            </bs.Col>
          </bs.Row>

          <bs.Row>
            <bs.Col span={3}>
              <input.BBStringField
                bean={customerAgent} field='personalContact' label={T("Personal Contact")} disable={!writeCap} required />
            </bs.Col>

            <bs.Col span={3}>
              <input.BBStringField
                bean={customerAgent} field='position' label={T("Position")} disable={!writeCap} required />
            </bs.Col>

            <bs.Col span={3}>
              <input.BBStringField
                bean={customerAgent} field='cell' label={T("Phone")} disable={!writeCap} required />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBStringField
                bean={customerAgent} field='email' label={T("Email.")} disable={!writeCap} required />
            </bs.Col>
          </bs.Row>

          <bs.Row>
            <bs.Col span={12}>
              <input.BBTextField
                bean={customerAgent} label={T('Address')} field="address" disable={!writeCap} required
                style={{ height: '4em' }} />
            </bs.Col>
          </bs.Row>

          <bs.Row>
            <bs.Col span={3}>
              <BBRefCrmUserRole minWidth={400} hideMoreInfo label='Saleman'
                appContext={appContext} pageContext={pageContext} bean={customerAgent} disable={!writeCap || !observer.isNewBean()}
                beanIdField='salemanAccountId' beanLabelField='salemanLabel' placeholder='Saleman.'
                onPostUpdate={(_inputUI, bean, selectOpt, _userInput) => {
                  bean['salemanAccountId'] = selectOpt['accountId'];
                  bean['salemanCompanyId'] = selectOpt['companyBranchId'];
                  bean['salemanLabel'] = selectOpt['fullName'];
                  this.forceUpdate();
                }} />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBSelectField bean={customerAgent} field="source" label={T('Source')}
                options={SOURCE_OPTIONS} disable={!writeCap} />
            </bs.Col>
            <bs.Col span={3}>
              <BBRefCountry key={util.IDTracker.next()}
                appContext={appContext} pageContext={pageContext}
                placement="bottom-start" offset={[0, 5]} minWidth={350}
                disable={!writeCap} label={T('Country')} placeholder="Enter Country"
                bean={customerAgent} beanIdField={'countryId'} hideMoreInfo
                beanLabelField={'countryLabel'} refCountryBy='id'
                onPostUpdate={(_inputUI, bean, selectOpt, _userInput) => {
                  bean['countryId'] = selectOpt['id'];
                  bean['countryLabel'] = selectOpt['label'];
                  this.forceUpdate();
                }}
              />
            </bs.Col>
            <bs.Col span={3}>
              <module.resource.BBRefResource
                appContext={appContext} pageContext={pageContext}
                placement="bottom-start" offset={[0, 5]} minWidth={350}
                disable={!writeCap} label={T('Industry')} placeholder="Enter Industry"
                required bean={customerAgent} beanIdField={'industryCode'} hideMoreInfo
                beanLabelField={'industryLabel'} resourceType={"industry"} refResourceBy="identifier" />
            </bs.Col>
          </bs.Row>

          <bs.Row>
            <bs.Col span={3}>
              <input.BBStringField
                bean={customerAgent} field='routing' label={T("Route")} disable={!writeCap} />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBStringField
                bean={customerAgent} field='volumeNote' label={T("Volume")} disable={!writeCap} />
            </bs.Col>
            <bs.Col span={6}>
              <input.BBStringField
                bean={customerAgent} field='customerOnboardingProgress' label={T("Progress")} disable={!writeCap} />
            </bs.Col>
          </bs.Row>

          <bs.Row>
            <bs.Col span={12}>
              <input.BBTextField bean={customerAgent} label={T('Note')} field="note" disable={!writeCap} style={{ height: '6em' }} />
            </bs.Col>
          </bs.Row>
        </div>
        <div className='flex-vbox' style={{ minHeight: 400 }}>
          <UIPartnerContactListEditor appContext={appContext} pageContext={pageContext}
            plugin={observer.createVGridEntityListEditorPlugin('partnerContacts', [])}
            editorTitle={'Partner Contacts'} dialogEditor={true} />
        </div>

        <div className='flex-grow-0 flex-hbox justify-content-end align-items-center mt-1 p-1 border-top'>

          <entity.ButtonEntityCommit className='py-1 px-3 mx-2' style={{ width: 120 }}
            btnLabel={`${this.state.isSending ? 'Saving...' : 'Save'}`}
            appContext={appContext} pageContext={pageContext}
            disable={this.state.isSending}
            observer={observer} hide={!writeCap}
            commit={{
              entityLabel: T(customerAgent.code),
              context: 'company',
              service: 'CustomerLeadsService', commitMethod: 'saveCustomerLead'
            }}
            onPreCommit={this.onPreCommit}
            onPostCommit={this.onPostCommit} />
        </div>
      </div>
    )
  }
}
