import React from "react";

import * as FeatherIcon from 'react-feather'
import { module } from '@datatp-ui/erp';
import { util, grid, bs, entity, app, sql, input } from '@datatp-ui/lib';

import { T } from "../backend";
import { UICustomerAgentEditor, UICustomerLeadSimpleEditor } from "./UICustomerLead";
import { ILeadStarRating, ILeadStatus, LeadStarRating, LeadStatus, LeadUtils } from "./LeadUtils";

import { buildTooltipValues, WRateFinderGridFilter } from "../../price";
import { UINewBFSOnePartnerEditor } from "../../partner";
import { responsiveGridConfig } from "../common";
import { BBRefCrmUserRole } from "app/crm/common/template/BBRefCrmUserRole";

const SESSION = app.host.DATATP_SESSION;

export class UICustomerLeadPlugin extends entity.DbEntityListPlugin {
  constructor(space: 'User' | 'Company' | 'System') {
    super([]);

    this.backend = {
      context: 'company',
      service: 'CustomerLeadsService',
      searchMethod: 'searchCustomerLeads',
    }

    this.searchParams = {
      params: {
        "space": space,
      },
      rangeFilters: [
        sql.createDateTimeFilterNew("date", T("Date Created")),
        sql.createDateTimeFilterNew("dateModified", T("Date Modified")),
      ],
      filters: [...sql.createSearchFilter()],
      optionFilters: [
        sql.createStorageStateFilter(["ACTIVE", "ARCHIVED"])
      ],
      maxReturn: 5000
    }
  }

  withSalemanAccountId(salemanAccountId: number) {
    this.addSearchParam('participantAccountIds', salemanAccountId);
    return this;
  }

  withStatus(status: typeof LeadStatus[keyof typeof LeadStatus]) {
    this.addSearchParam('status', status.value);
    return this;
  }

  withDateFilter(filterName: 'date' | 'dateModified', fromValue: string, toValue: string) {
    if (this.searchParams) {
      let rangeFilters = this.searchParams.rangeFilters;
      if (rangeFilters) {
        for (let i = 0; i < rangeFilters.length; i++) {
          let filter = rangeFilters[i];
          if (filter.name === filterName) {
            filter.fromValue = fromValue;
            filter.toValue = toValue;
            break;
          }
        }
      }
      this.searchParams.rangeFilters = rangeFilters;
    }
    return this;
  }

  loadData(uiList: entity.DbEntityList<any>): void {
    this.createBackendSearch(uiList, { params: this.searchParams }).call();
  }

}

export interface UICustomerLeadListProps extends entity.DbEntityListProps { }
export class UICustomerLeadList extends entity.DbEntityList<UICustomerLeadListProps> {

  createVGridConfig(): grid.VGridConfig {
    const { type } = this.props;

    const renderTooltipAdvanced = (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
      const record = dRecord.record;
      const { fieldDataGetter, format } = field;
      let val = field.name === '_index_' ? dRecord.getDisplayRow() : record[field.name];
      if (fieldDataGetter) val = fieldDataGetter(record)
      else if (format) val = format(val)

      const tooltipFields = [
        { key: 'name', label: 'Name' },
        { key: 'routing', label: 'Route' },
        { key: 'volumeNote', label: 'Volume' },
        { key: 'customerOnboardingProgress', label: 'Progress' },
        { key: 'kcnLabel', label: 'Industrial Park' },
        { key: 'address', label: 'Address' },
      ];


      // Build tooltip content from record fields
      const { htmlFormat, textFormat } = buildTooltipValues(record, tooltipFields);

      const handleClick = () => {
        navigator.clipboard.writeText(textFormat);
        bs.toastShow('Copied to clipboard!', { type: 'success' });
      };

      let offsetX = field.width || 120

      return (
        <bs.CssTooltip width={400} offset={{ x: offsetX, y: 0 }}>
          <bs.CssTooltipToggle >
            <div className='flex-hbox' onClick={handleClick}>
              {field.fieldDataGetter ? field.fieldDataGetter(record) : field.format ? field.format(val) : val}
            </div>
          </bs.CssTooltipToggle>
          <bs.CssTooltipContent>
            {htmlFormat}
          </bs.CssTooltipContent>
        </bs.CssTooltip>
      );
    }

    let config: grid.VGridConfig = {
      record: {
        dataCellHeight: 40,
        editor: {
          supportViewMode: ['table'],
          enable: true
        },
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          {
            name: 'code', label: T(`Lead Code`), width: 120, container: 'fixed-left',
            fieldDataGetter(record) {
              return record['code'] || 'N/A'
            },
            onClick(ctx: grid.VGridContext, record: grid.DisplayRecord) {
              let uiRoot = ctx.uiRoot as UICustomerLeadList;
              uiRoot.onSelect(record);
            },
          },
          {
            name: 'name', label: T(`Lead Name`), width: 350, format(val) {
              return util.text.formater.uiTruncate(val, 330, true);
            },
            customRender: renderTooltipAdvanced
          },
          {
            name: 'date', label: T(`Date Created`), width: 120, format: util.text.formater.compactDate,
            filterable: true, filterableType: 'date',
            fieldDataGetter(record) {
              return util.text.formater.compactDate(record['date']);
            },
            customRender: renderTooltipAdvanced
          },
          {
            name: 'taxCode', label: T(`Taxcode`), width: 130,
            customRender: renderTooltipAdvanced
          },
          {
            name: 'personalContact', label: T(`PIC`), width: 200,
            customRender: renderTooltipAdvanced
          },

          {
            name: "salemanLabel", label: 'Saleman.', width: 180, filterable: true, state: { visible: false },
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let uiList = ctx.uiRoot as entity.DbEntityList
              const { appContext, pageContext } = uiList.props;

              let employeeName: string = record['salemanLabel'] || 'N/A';

              let parts = employeeName.trim().toLowerCase().split(' ');
              parts = parts.map(word => word.charAt(0).toUpperCase() + word.slice(1));
              if (parts.length > 3) {
                let initials = parts[0][0] + '.' + parts[1][0] + '.';
                employeeName = `${initials} ${parts.slice(2).join(' ')}`;
              }

              return (
                <div className='flex-hbox justify-content-center align-items-center' >
                  <module.account.WAvatars className='px-2'
                    appContext={appContext} pageContext={pageContext} avatarIds={[record['salemanAccountId']]}
                    avatarIdType='AccountId' width={20} height={20} borderRadius={10} />
                  <div className="flex-hbox">{employeeName}</div>
                </div>
              )
            },
          },

          { name: 'industryLabel', label: T(`Field Industry`), width: 150, state: { visible: false } },
          {
            name: 'routing', label: T(`Route`), width: 200,
            customRender: renderTooltipAdvanced
          },
          {
            name: 'volumeNote', label: T(`Volume`), width: 200,
            customRender: renderTooltipAdvanced
          },
          {
            name: 'customerOnboardingProgress', label: T(`Progress`), width: 200,
            customRender: renderTooltipAdvanced
          },
          {
            name: 'kcnLabel', label: T(`Industrial Park`), width: 150,
            format(val) {
              return util.text.formater.uiTruncate(val, 150, true);
            },
          },
          {
            name: 'address', label: T(`Address`), width: 200,
            format(val) {
              return util.text.formater.uiTruncate(val, 200, true);
            },
            customRender: renderTooltipAdvanced
          },
          {
            name: 'starRating', label: T('Rating'), width: 80, container: 'fixed-right',
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let customerLead = dRecord.record;
              let currentStar = LeadUtils.getStarRatingInfo(customerLead['starRating']);
              let uiList = ctx.uiRoot as UICustomerLeadList;
              let StarIcon = currentStar.icon;
              let label = currentStar.label;
              let hexColor = currentStar.hexColor;

              const starList = LeadUtils.getStarRatingList();
              const starRemaining = starList.filter(star =>
                star.value !== customerLead['starRating']
              );


              return (
                <bs.Popover className="d-flex flex-center w-100 h-100" title={T('Status')} closeOnTrigger=".btn">
                  <bs.PopoverToggle
                    className="flex-hbox flex-center px-2 py-2 rounded-2 w-100"
                    style={{ backgroundColor: hexColor, color: '#212529' }}>
                    <StarIcon size={14} className="me-1" />
                    <span>{label}</span>
                  </bs.PopoverToggle>
                  <bs.PopoverContent>
                    <div className='flex-vbox gap-2' style={{ width: '200px' }}>
                      {starRemaining.map(opt => {
                        let OptIcon = opt.icon;
                        return (
                          <div
                            key={opt.value}
                            className="d-flex flex-center px-2 py-1 rounded-2 w-100 cursor-pointer"
                            style={{ backgroundColor: opt.hexColor, color: '#212529' }}
                            onClick={() => uiList.onChangeStarRecords(opt.value, [customerLead])}>
                            <OptIcon size={14} className="me-1" />
                            <span>{opt.label}</span>
                          </div>
                        );
                      })}
                    </div>
                  </bs.PopoverContent>
                </bs.Popover>
              );
            }
          },
          {
            name: 'status', label: T('Status'), width: 150, filterable: true, container: 'fixed-right',
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let uiList = ctx.uiRoot as UICustomerLeadList;

              let currentStatus = LeadUtils.getStatusInfo(record['status']);
              let StatusIcon = currentStatus.icon;
              let label = currentStatus.label;
              let color = currentStatus.color;

              const statusList = LeadUtils.getLeadStatusList();
              const statusRemaining = statusList.filter(status => status.value !== record['status']);

              return (
                <bs.Popover className="d-flex flex-center w-100 h-100" title={T('Status')} closeOnTrigger=".tooltip-trigger" >
                  <bs.PopoverToggle className={`flex-hbox flex-center px-2 py-2 rounded-2 bg-${color}-subtle text-${color} w-100`} >
                    <StatusIcon size={14} className="me-1" />
                    <span>{label}</span>
                  </bs.PopoverToggle>
                  <bs.PopoverContent>
                    <div className='flex-vbox gap-2' style={{ width: '200px' }}>
                      {statusRemaining.map((opt: ILeadStatus) => {
                        let OptIcon = opt.icon;
                        return (
                          <bs.CssTooltip width={200} position="bottom-left" offset={{ x: -200, y: -45 }}>
                            <bs.CssTooltipToggle key={opt.value} className={`d-flex flex-center px-2 py-1 rounded-2 bg-${opt.color}-subtle text-${opt.color} w-100 cursor-pointer`}>
                              <div key={opt.value}
                                className={`d-flex flex-center px-2 py-1 m-0 rounded-2 bg-${opt.color}-subtle text-${opt.color} w-100 cursor-pointer`}
                                onClick={() => uiList.onChangeStatus(opt.value, record)}>
                                <OptIcon size={14} className="me-1" />
                                <span>{opt.label}</span>
                              </div>
                            </bs.CssTooltipToggle>

                            <bs.CssTooltipContent className="d-flex flex-column rounded" >
                              {opt.desc}
                            </bs.CssTooltipContent>
                          </bs.CssTooltip>
                        )
                      })}
                    </div>
                  </bs.PopoverContent>
                </bs.Popover>
              );
            }
          },
        ],
      },
      toolbar: {
        hide: true,
      },
      footer: {
        default: {
          hide: type === 'selector',
          render: (ctx: grid.VGridContext) => {
            let uiList = ctx.uiRoot as UICustomerLeadList;
            let { appContext, pageContext } = uiList.props;
            let writeCap = pageContext.hasUserWriteCapability();

            return (
              <bs.Toolbar className='border'>
                <entity.WButtonEntityNew hide={!writeCap}
                  appContext={appContext} pageContext={pageContext}
                  label={'Agent Approach'} onClick={this.onNewAgentApproach} className="me-1" />

                <entity.WButtonEntityNew hide={!writeCap}
                  appContext={appContext} pageContext={pageContext}
                  label={'Customer Lead'} onClick={this.onNewCustomerLead} />
              </bs.Toolbar>
            );
          }
        },
        selector: entity.DbEntityListConfigTool.FOOTER_MULTI_SELECT(T('Select'), type),
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          }
        }
      },
    }

    return responsiveGridConfig(config);
  }

  onChangeStatusRecords(status: string, records: any[] = []) {
    const { appContext } = this.props;
    if (status === LeadStatus.CONVERTED.value) {
      bs.dialogShow('Information',
        <div className="px-2 text-center py-2 border-bottom" style={{ backgroundColor: '#e3f2fd', color: '#0d6efd' }}>
          Action not available. Please select a different status.
        </div>,
        { backdrop: 'static', size: 'sm' }
      );
    } else {
      let modifies: any[] = [];

      for (let record of records) {
        record['status'] = status;
        grid.getRecordState(record).markModified();
        modifies.push({
          id: record['id'],
          status: status,
        });
      }
      if (modifies.length === 0) {
        bs.dialogShow('Message',
          <div className="text-warning text-center p-2">
            No Customer Lead Selected!
          </div>
        );
        return;
      }

      appContext
        .createHttpBackendCall('CustomerLeadsService', 'saveCustomerLeadRecords', { modified: modifies })
        .withSuccessData((_data: any) => {
          appContext.addOSNotification("success", T("Success"), T("Update lead status success"));
          this.vgridContext.model.getDisplayRecordList().markSelectAllDisplayRecords(false);
          this.vgridContext.getVGrid().forceUpdateView();
        })
        .call();
      this.vgridContext.getVGrid().forceUpdateView();
    }
  }

  onChangeStarRecords(starRating: string, records: any[] = []) {
    const { appContext } = this.props;
    let modifies: any[] = [];

    for (let record of records) {
      record['starRating'] = starRating;
      grid.getRecordState(record).markModified();
      modifies.push({
        id: record['id'],
        starRating: starRating,
      });
    }
    if (modifies.length === 0) {
      bs.dialogShow('Message',
        <div className="text-warning text-center p-2">
          No Customer Lead Selected!
        </div>
      );
      return;
    }
    appContext
      .createHttpBackendCall('CustomerLeadsService', 'saveCustomerLeadRecords', { modified: modifies })
      .withSuccessData((_data: any) => {
        appContext.addOSNotification("success", T("Success"), T("Update lead star success"));
        this.vgridContext.model.getDisplayRecordList().markSelectAllDisplayRecords(false);
        this.vgridContext.getVGrid().forceUpdateView();
      })
      .call();
    this.vgridContext.getVGrid().forceUpdateView();
  }

  onChangeStatus(status: string, record: any) {
    const { appContext, pageContext } = this.props;
    if (!record || !record['id']) {
      appContext.addOSNotification("warning", T("Warning"), T("Unknown Customer Lead ID!"));
      return;
    }

    if (status === LeadStatus.CONVERTED.value) {
      let partnerType: any = record['type'] === 'CUSTOMER_LEAD' ? 'Customer' : 'Agent';
      appContext.createHttpBackendCall('CustomerLeadsService', 'convertToBFSOnePartner', { id: record['id'] })
        .withSuccessNotification("success", T("Success"), T("Update lead status success"))
        .withSuccessData((partner: any) => {
          record['status'] = status;
          this.vgridContext.model.addOrUpdateByRecordId(record);
          this.vgridContext.getVGrid().forceUpdateView(true);

          let callbackConfirm = () => {
            let observer = new entity.ComplexBeanObserver(partner);
            const pageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return (
                <div className='flex-vbox' style={{ minHeight: 600 }}>
                  <UINewBFSOnePartnerEditor appContext={appCtx} pageContext={pageCtx} observer={observer} partnerType={partnerType}
                    onPostCommit={(bean) => {
                      pageCtx.back();
                      this.reloadData();
                    }}
                  />
                </div>
              )
            }
            pageContext.createPopupPage('popup-request-to-bfsone', `New ${partnerType}`, pageContent, { size: 'flex-lg', backdrop: 'static' });
          }
          let message = (<div className="text-danger">Do you want to Request this to BFSOne?</div>);
          bs.dialogConfirmMessage('Confirm Message', message, callbackConfirm);
        })
        .call();
    } else {
      appContext
        .createHttpBackendCall('CustomerLeadsService', 'saveCustomerLeadRecords', { modified: [{ ...record, status: status }] })
        .withSuccessNotification("success", T("Success"), T("Update lead status success"))
        .withSuccessData((data: any) => {
          record['status'] = status;
          this.vgridContext.model.addOrUpdateByRecordId(record);
          this.vgridContext.getVGrid().forceUpdateView(true);
        })
        .call();
    }
  }

  onDefaultSelect(dRecord: grid.DisplayRecord): void {
    let customerLead = dRecord.record;
    let { appContext, pageContext } = this.props;
    let param: any = { id: customerLead.id };
    appContext.createHttpBackendCall('CustomerLeadsService', 'getCustomerLeadById', param)
      .withSuccessData((data: any) => {
        const leadType: 'AGENTS_APPROACHED' | 'CUSTOMER_LEAD' = data['type'];

        let observer = new entity.ComplexBeanObserver(data);
        if (leadType === 'AGENTS_APPROACHED') {
          let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
            return (
              <UICustomerAgentEditor appContext={appCtx} pageContext={pageCtx} observer={observer} />
            );
          }
          let pupupLabel: string = `Agent Approach: ${customerLead.name}`;
          pageContext.createPopupPage(`agent-approach-${util.IDTracker.next()}`, pupupLabel, createAppPage, { size: 'flex-lg', backdrop: 'static' });
        } else {
          let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
            return (
              <UICustomerLeadSimpleEditor appContext={appCtx} pageContext={pageCtx} observer={observer} />
            );
          }
          let pupupLabel: string = `Customer Lead: ${customerLead.name}`;
          pageContext.createPopupPage(`customer-lead-${util.IDTracker.next()}`, pupupLabel, createAppPage, { size: 'flex-lg', backdrop: 'static' });
        }
      })
      .call();
  }

  onDeleteAction(): void {
    const { appContext } = this.props;
    const selectedIds = this.getVGridContext().model.getSelectedRecordIds();

    if (selectedIds.length === 0) {
      bs.dialogShow('Message',
        <div className="text-warning text-center p-2">
          No Customer Lead Selected!
        </div>
      );
      return;
    }

    appContext.createHttpBackendCall('CustomerLeadsService', 'deleteCustomerLeads', { ids: selectedIds })
      .withSuccessData((_data: any) => {
        this.reloadData();
        this.getVGridContext().model.removeSelectedDisplayRecords();
        this.getVGridContext().getVGrid().forceUpdateView();
      })
      .withEntityOpNotification('delete', 'customer')
      .call();
  }

  initNewLead(leadType: 'AGENTS_APPROACHED' | 'CUSTOMER_LEAD') {
    let lead: any = {
      accountCreatorId: SESSION.getAccountId(),
      accountCreatorLabel: SESSION.getAccountAcl().getFullName(),
      salemanAccountId: SESSION.getAccountId(),
      salemanLabel: SESSION.getAccountAcl().getFullName(),
      salemanCompanyId: SESSION.getAccountAcl().getCompanyAcl().companyId,
      date: util.TimeUtil.javaCompactDateTimeFormat(new Date()),
      source: 'WCA',
      starRating: LeadStarRating.THREE_STARS.value,
      status: LeadStatus.NEW.value,
      type: leadType,
    }
    return lead;
  }

  onNewAgentApproach = () => {
    let { pageContext } = this.props;
    let observer = new entity.ComplexBeanObserver(this.initNewLead('AGENTS_APPROACHED'));

    const pageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <div className='flex-vbox' >
          <UICustomerAgentEditor appContext={appCtx} pageContext={pageCtx} observer={observer}
            onPostCommit={(_bean) => {
              this.onAddOrModifyDBRecordCallback(_bean);
              this.getVGridContext().getVGrid().forceUpdateView();
            }} />
        </div>
      )
    }
    pageContext.createPopupPage('new-agent-potential', `New Agent Potential`, pageContent, { size: 'xl', backdrop: 'static' });
  }

  onNewCustomerLead = () => {
    let { pageContext } = this.props;
    let observer = new entity.ComplexBeanObserver(this.initNewLead('CUSTOMER_LEAD'));
    const pageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <div className='flex-vbox' >
          <UICustomerLeadSimpleEditor appContext={appCtx} pageContext={pageCtx} observer={observer}
            onPostCommit={(_bean) => {
              this.onAddOrModifyDBRecordCallback(_bean);
              this.getVGridContext().getVGrid().forceUpdateView();
            }} />
        </div>
      )
    }
    pageContext.createPopupPage('new-customer-lead', `New Customer Lead`, pageContent, { size: 'flex-lg', backdrop: 'static' });
  }

  onAssignLead = () => {

    const { appContext, pageContext } = this.props;
    let selectedIds = this.getVGridContext().model.getSelectedRecordIds();

    let saleman = { salemanAccountId: undefined, salemanLabel: undefined };

    if (selectedIds.length === 0) {
      bs.dialogShow('Information',
        <div className="px-3 py-3 text-center border-bottom rounded" style={{ backgroundColor: '#fff3cd', color: '#856404' }}>
          <i className="bi bi-exclamation-triangle-fill" style={{ fontSize: '1.5rem', marginRight: '0.5rem' }}></i>
          Please select at least one Customer Lead to proceed with the assignment.
        </div>,
        { backdrop: 'static', size: 'sm' }
      );
      return;
    }

    const pageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {

      const onReassignLeadToSaleman = () => {
        appCtx.createHttpBackendCall('CustomerLeadsService', 'reassignLeadToSaleman', { leadIds: selectedIds, salemanAccountId: saleman['salemanAccountId'] })
          .withSuccessData((_data: any) => {
            appContext.addOSNotification("success", T("Success"), T("Successfully reassigned leads to new salesman"));
            pageCtx.back();
            this.getVGridContext().model.removeSelectedDisplayRecords();
            this.getVGridContext().getVGrid().forceUpdateView();
          })
          .call();
      };

      return (
        <div className='flex-vbox'>
          <div className='flex-vbox p-1'>
            <BBRefCrmUserRole minWidth={400}
              appContext={appCtx} pageContext={pageCtx}
              label={T('Salesman')} placeholder='Select Salesman' hideMoreInfo
              bean={saleman} beanIdField={'salemanAccountId'} beanLabelField={'salemanLabel'} />
          </div>
          <div className='flex-hbox-grow-0 justify-content-end mt-1 border-top pt-2'>
            <bs.Button laf='info' outline className="px-2 py-1 mx-1" onClick={onReassignLeadToSaleman}>
              <FeatherIcon.UserPlus size={12} /> Reassign
            </bs.Button>
          </div>
        </div>
      );
    };
    pageContext.createPopupPage(`assign-lead-${util.IDTracker.next()}`, `Lead Transfer`, pageContent, { size: 'sm' });
  }

  onModify = (_bean: any, _field: string, _oldVal: any, _newVal: any): void => {
    const { plugin } = this.props;
    let searchParam = plugin.getSearchParams();
    if (_field === 'maxReturn') {
      searchParam.maxReturn = _newVal || 5000;
    } else {
      searchParam.params = _bean;
    }
    plugin.searchParams = searchParam;
    this.reloadData();
    this.nextViewId();
    // this.forceUpdate();
    this.vgridContext.getVGrid().forceUpdateView();
  };

  render() {
    if (this.isLoading()) return this.renderLoading();
    const { plugin, pageContext } = this.props;
    let searchParam = plugin.getSearchParams();
    let moderatorCap = pageContext.hasUserModeratorCapability();

    return (
      <div className='flex-vbox'>
        <div className='bg-white flex-hbox flex-grow-0 justify-content-between align-items-center mx-1 px-1 mt-1'>

          <div className='flex-hbox justify-content-start align-items-center'>
            <div className='me-2'>
              <input.BBSelectField className="fw-bold text-primary border-bottom text-center"
                style={{ width: 150 }} bean={searchParam} field={"maxReturn"}
                options={[1000, 2000, 5000, 10000]}
                optionLabels={['Show 1000 records', 'Show 2000 records', 'Show 5000 records', 'Show 10.000 records']}
                onInputChange={this.onModify} />
            </div>
            <WRateFinderGridFilter context={this.vgridContext} />
          </div>
          <div className="flex-hbox justify-content-end align-items-center flex-grow-1">

            <div className="position-relative mx-1">
              <bs.Popover placement='auto-start' minWidth={200} closeOnTrigger={'.btn'}>
                <bs.PopoverToggle laf='info' className="border-0 p-1 border-end rounded-0" outline>
                  <FeatherIcon.CheckSquare size={12} /> Lead Star
                </bs.PopoverToggle>
                <bs.PopoverContent className="flex-vbox" style={{ minWidth: 200 }}>
                  <div className='flex-vbox border-bottom justify-content-start text-start gap-1'>
                    {
                      LeadUtils.getStarRatingList().map((opt: ILeadStarRating) => (
                        <bs.Button key={opt.value} laf='secondary' className="h-100 px-2 border-0 rounded-0"
                          style={{ backgroundColor: opt.hexColor, color: '#212529' }}
                          onClick={() => this.onChangeStarRecords(opt.value, this.getVGridContext().model.getSelectedRecords())}>
                          <FeatherIcon.Star className='mx-1' size={12} /> {opt.label}
                        </bs.Button>
                      ))
                    }
                  </div>
                </bs.PopoverContent>
              </bs.Popover>
            </div>

            <bs.Button laf='info' className="border-0 p-1 mx-1 border-end rounded-0" outline
              onClick={() => this.onAssignLead()} hidden={bs.ScreenUtil.isMobileScreen()}>
              <FeatherIcon.UserPlus size={12} /> Transfer Lead
            </bs.Button>

            <div className="position-relative mx-1">
              <bs.Popover placement='auto-start' minWidth={200} closeOnTrigger={'.btn'}>
                <bs.PopoverToggle laf='info' className="border-0 p-1 border-end rounded-0" outline>
                  <FeatherIcon.CheckSquare size={12} /> Lead Status
                </bs.PopoverToggle>
                <bs.PopoverContent className="flex-vbox" style={{ minWidth: 200 }}>
                  <div className='flex-vbox border-bottom justify-content-start text-start gap-1'>
                    {
                      LeadUtils.getLeadStatusList().map((opt: ILeadStatus) => (
                        <bs.Button key={opt.value} laf='secondary' className="h-100 px-2 border-0 rounded-0"
                          style={{ backgroundColor: opt.hexColor, color: '#fff' }}
                          onClick={() => this.onChangeStatusRecords(opt.value, this.getVGridContext().model.getSelectedRecords())}>
                          <bs.Tooltip className={'flex-hbox'} placement="left" tooltip={opt.desc}>
                            <FeatherIcon.UserCheck className='mx-1' size={12} /> {opt.label}
                          </bs.Tooltip>
                        </bs.Button>
                      ))
                    }
                  </div>
                </bs.PopoverContent>
              </bs.Popover>
            </div>

            <bs.Button laf='warning' className="border-0 p-1 mx-1 border-end rounded-0" outline
              onClick={() => this.onDeleteAction()} >
              <FeatherIcon.Trash size={12} /> Del
            </bs.Button>
          </div>

        </div>
        <div key={this.viewId} className='flex-vbox'>
          {this.renderUIGrid()}
        </div>
      </div>
    )
  }

}

