import React from 'react';
import { app, bs, util } from '@datatp-ui/lib';
import * as FeatherIcon from 'react-feather';
import { UISalesDailyTaskCalendar } from './UITaskCalendarCalendarView';
import { UIRecentActivities } from './UIRecentActivities';

interface UICalendarFilterProps extends app.AppComponentProps {
  uiRoot: UISalesDailyTaskCalendar;
}

export class UICalendarFilter extends app.AppComponent<UICalendarFilterProps> {
  popoverId: string = 'calendar-filter-' + util.IDTracker.next();
  isFiltering: boolean = false;

  onFilterRecords = () => {
    this.isFiltering = !this.isFiltering;
    alert("TODO: Dan - Implement this method!!!")
    this.forceUpdate();
  }

  render() {
    const { uiRoot, appContext, pageContext } = this.props;

    return (
      <div className="flex-vbox">
        <div className="flex-vbox flex-grow-0 border-bottom px-1">

          <div className="fw-bold border-bottom my-1 d-flex flex-grow-0 align-items-center justify-content-between">
            <span>My Calendar</span>
            <div className='px-1'>
              <bs.Button tabIndex={-1} laf='link' className='p-0'
                onClick={() => this.onFilterRecords()}>
                <FeatherIcon.Filter id={`popover-trigger-${this.popoverId}`}
                  size={16} className={`${this.isFiltering ? 'text-warning' : 'text-info'}`} />
              </bs.Button>
            </div>
          </div>

          <div className="d-flex flex-wrap gap-1 mb-1">
            {/* All Tasks Checkbox */}
            <div className="form-check d-flex align-items-center" style={{ minWidth: '110px' }}>
              <input className="form-check-input me-2" type="checkbox" checked={!uiRoot.activeFilter}
                onChange={() => {
                  uiRoot.activeFilter = null;
                  uiRoot.onLoadData();
                }}
                style={{
                  width: '16px',
                  height: '16px',
                  cursor: 'pointer',
                  backgroundColor: !uiRoot.activeFilter ? '#4285f4' : '#fff',
                  borderColor: '#4285f4'
                }}
              />
              <label className="form-check-label" style={{ cursor: 'pointer' }}>
                All Tasks
              </label>
            </div>

            {/* Inquiry Requests Checkbox */}
            <div className="form-check d-flex align-items-center" style={{ minWidth: '110px' }}>
              <input className="form-check-input me-2" type="checkbox" checked={uiRoot.activeFilter === 'MEETING'}
                onChange={() => {
                  uiRoot.activeFilter = 'MEETING'
                  uiRoot.onLoadData();
                }}
                style={{
                  width: '16px',
                  height: '16px',
                  cursor: 'pointer',
                  backgroundColor: uiRoot.activeFilter === 'MEETING' ? '#0f9d58' : '#fff',
                  borderColor: '#0f9d58'
                }}
              />
              <label className="form-check-label" style={{ cursor: 'pointer' }}>
                Meeting Note
              </label>
            </div>

            {/* Call Tasks Checkbox */}
            <div className="form-check d-flex align-items-center" style={{ minWidth: '110px' }}>
              <input
                className="form-check-input me-2"
                type="checkbox"
                checked={uiRoot.activeFilter === 'LEADS'}
                onChange={() => {
                  uiRoot.activeFilter = 'LEADS'
                  uiRoot.onLoadData();
                }}
                style={{
                  width: '16px',
                  height: '16px',
                  cursor: 'pointer',
                  backgroundColor: uiRoot.activeFilter === 'LEADS' ? '#db4437' : '#fff',
                  borderColor: '#db4437'
                }}
              />
              <label className="form-check-label" style={{ cursor: 'pointer' }}>
                Leads
              </label>
            </div>

            {/* Meet Customer Tasks Checkbox */}
            <div className="form-check d-flex align-items-center" style={{ minWidth: '110px' }}>
              <input className="form-check-input me-2" type="checkbox" checked={uiRoot.activeFilter === 'CUSTOMERS'}
                onChange={() => {
                  uiRoot.activeFilter = 'CUSTOMERS'
                  uiRoot.onLoadData();
                }}
                style={{
                  width: '16px',
                  height: '16px',
                  cursor: 'pointer',
                  backgroundColor: uiRoot.activeFilter === 'CUSTOMERS' ? '#f4b400' : '#fff',
                  borderColor: '#f4b400'
                }}
              />
              <label className="form-check-label" style={{ cursor: 'pointer' }}>
                Customers
              </label>
            </div>
          </div>
        </div>

        <UIRecentActivities appContext={appContext} pageContext={pageContext}
          records={uiRoot.records || []}
          onViewAllTasks={() => uiRoot.onViewAllTasks([])} />

      </div>
    );
  }
}
