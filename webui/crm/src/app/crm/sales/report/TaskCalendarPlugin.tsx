import React from 'react';
import * as FeatherIcon from 'react-feather'
import { module } from '@datatp-ui/erp'
import { calendar, bs, entity, app, util } from '@datatp-ui/lib';

import { SalesTaskUtils, SaleTaskTypeUtils, T } from '../backend';
import { UISalesDailyTaskEditor } from './UITaskCalendarEditor';
import { UISaleDailyTaskList, UISaleDailyTaskListPlugin } from './UITaskCalendarList';
import { SaleDailyTaskTree } from './TaskCalendarTreeList';

const SESSION = app.host.DATATP_SESSION;
export class SalesDailyTaskCalendarPlugin extends module.common.DbEntityTaskCalendarPlugin {
  constructor() {
    super('forwarder_sales_daily_task', 'Sales Daily Task');
    this.showActions = true;
    this.showFilter = true;
  }

  onAddTask = (context: calendar.CalendarContext, date: Date) => {
    let uiRoot = context.uiRoot as module.common.DbEntityTaskList;
    let { pageContext } = uiRoot.props;

    // Check if selected date is in the past
    const selectedDate = date;
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if ((selectedDate < today) && !pageContext.hasUserModeratorCapability()) {
      bs.dialogShow('Notice',
        <div className="text-warning fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />
          Cannot create tasks for past dates
        </div>,
        { backdrop: 'static', size: 'md' }
      );
      return;
    }

    let writeCap = pageContext.hasUserWriteCapability();
    const now = new Date();
    const createdDate = new Date(date);
    createdDate.setHours(now.getHours(), now.getMinutes(), now.getSeconds());
    // Tạo dueDate: set về cuối ngày (23:59:59)
    const dueDate = new Date(date);
    dueDate.setHours(23, 59, 59);
    let newBean = {
      createdDate: util.TimeUtil.javaCompactDateTimeFormat(createdDate),
      dueDate: util.TimeUtil.javaCompactDateTimeFormat(dueDate),
      creatorAccountId: SESSION.getAccountId(),
      creatorLabel: SESSION.getAccountAcl().getFullName(),
      salemanAccountId: SESSION.getAccountId(),
      salemanLabel: SESSION.getAccountAcl().getFullName(),
    };
    let observer = new entity.BeanObserver(newBean);

    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UISalesDailyTaskEditor appContext={appCtx} pageContext={pageCtx} observer={observer} readOnly={!writeCap}
          onPostCommit={(_bean) => {
            pageCtx.back();
            uiRoot.reloadData();
          }} />
      )
    }
    pageContext.createPopupPage(`create-tasks-${util.IDTracker.next()}`, "Create Task", createAppPage, { size: 'md', backdrop: 'static' });
  }

  onViewTask = (context: calendar.CalendarContext, task: any) => {
    let uiRoot = context.uiRoot as module.common.DbEntityTaskList;
    let { appContext, pageContext } = uiRoot.props;
    let { entityRefId } = task;

    appContext.createHttpBackendCall('TaskCalendarService', 'getSalesDailyTaskById', { taskId: entityRefId })
      .withSuccessData((data: any) => {
        let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          let observer = new entity.BeanObserver(data);
          const onPostCommit = (_entity: any, _uiEditor?: bs.BaseComponent) => {
            pageCtx.back();
          }
          return (
            <UISalesDailyTaskEditor
              appContext={appCtx} pageContext={pageCtx} observer={observer} onPostCommit={onPostCommit} />
          )
        }
        pageContext.createPopupPage("sale-daily-task", `${T("Sale Daily Task")}: ${task.label}`, createAppPage, { size: 'xl', backdrop: 'static' });

      })
      .call();
  }

  onViewAllTasks = (context: calendar.CalendarContext, allTasks: any[] = []) => {
    let uiRoot = context.uiRoot as module.common.DbEntityTaskList;
    let activeFilter = uiRoot.pluginActiveFilter ? uiRoot.pluginActiveFilter : null;
    let { appContext, pageContext, space } = uiRoot.props;
    let currentSpace: 'User' | 'Company' | 'System' = 'User';
    if (space == 'company') currentSpace = 'Company'

    let popupId = `view-all-tasks-${util.IDTracker.next()}`;

    let plugin: UISaleDailyTaskListPlugin = new UISaleDailyTaskListPlugin(currentSpace, new Date());
    if (allTasks && allTasks.length > 0) {
      plugin.withIds(allTasks.map(task => task.entityRefId));
      appContext.createHttpBackendCall('TaskCalendarService', 'searchSalesDailyTasks', { sqlParams: plugin.searchParams })
        .withSuccessData(data => {
          let records: any[] = data;
          const sortedTasks = [...records].sort((a, b) => {
            const statusPriority: any = {
              'IN_PROGRESS': 0,
              'NEED_SUPPORT': 1,
              'BLOCKED': 2,
              'COMPLETED': 3
            };

            const statusA = statusPriority[a.status] ?? 999;
            const statusB = statusPriority[b.status] ?? 999;
            return statusA - statusB;
          });
          plugin = new entity.DbEntityListPlugin(sortedTasks) as UISaleDailyTaskListPlugin;
        })
        .call();
    } else {
      plugin = new UISaleDailyTaskListPlugin(currentSpace, new Date());
    }

    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        currentSpace === 'User' ? <UISaleDailyTaskList appContext={appCtx} pageContext={pageCtx} plugin={plugin} space={currentSpace} /> :
          <SaleDailyTaskTree appContext={appCtx} pageContext={pageCtx} plugin={plugin} groupBy={'SALEMAN'} />
      )
    }

    if (currentSpace === 'User') {
      createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
        return (
          <UISaleDailyTaskList appContext={appCtx} pageContext={pageCtx} plugin={plugin} space={currentSpace} />
        )
      }
    }
    pageContext.createPopupPage(popupId, "Sale Daily Tasks", createAppPage, { size: 'xl', backdrop: 'static' });
  }

  renderTaskSummaryList(context: calendar.CalendarContext, tasks: any[]): React.JSX.Element {
    let uiRoot = context.uiRoot as module.common.DbEntityTaskList;
    let activeFilter = uiRoot.pluginActiveFilter ? uiRoot.pluginActiveFilter : null;
    let uiTasks: any[] = [];
    for (let task of tasks) {
      if (activeFilter) {
        let entityRefData = JSON.parse(task.entityRefData);
        if (entityRefData['taskType'] !== activeFilter) continue;
      }
      uiTasks.push(this.renderTaskSummary(context, task))
    }
    let html = (
      <div className='flex-vbox'>
        {
          tasks && tasks.length > 5 ?
            <bs.GreedyScrollable style={{ minHeight: 450 }}>
              <div className='flex-vbox' >
                {uiTasks}
              </div>
            </bs.GreedyScrollable>
            :
            uiTasks
        }
        <bs.Button laf='link' tabIndex={0}
          className="border-0 mx-1 p-1 w-100 text-center fs-9 text-decoration-none text-600"
          onClick={(e) => { this.onViewAllTasks(context, tasks) }}>
          <FeatherIcon.List size={12} /> View All
        </bs.Button>
      </div>
    )
    return html;
  }

  formatDateTime(dateStr: string) {
    const [datePart, timePart] = dateStr.split('@');
    const time = timePart.split('+')[0];

    return {
      date: datePart,
      time: time.substring(0, 5)
    };
  }

  formatEmployeeName(name: string): string {
    if (!name) return 'N/A';
    let parts = name.trim().toLowerCase().split(' ');
    parts = parts.map(word => word.charAt(0).toUpperCase() + word.slice(1));

    if (parts.length >= 3) {
      let initCount = parts.length - 2; // Luôn giữ lại 2 chữ cuối
      let initials = parts.slice(0, initCount).map(word => word[0] + '.').join('');
      return `${initials} ${parts.slice(-2).join(' ')}`;
    }

    return parts.join(' ');
  }

  renderTaskSummary(context: calendar.CalendarContext, task: any): React.JSX.Element {
    let uiRoot = context.uiRoot as module.common.DbEntityTaskList;
    let { appContext, pageContext } = uiRoot.props;
    let entityRefData: any = {
      taskType: 'OTHER',
      status: 'IN_PROGRESS',
      referenceId: null,
      referenceType: null
    }
    if (task.entityRefData != null) {
      entityRefData = JSON.parse(task.entityRefData)
    }

    const taskTypeInfo = SaleTaskTypeUtils.getTypeInfo(entityRefData['taskType']);
    const Icon = taskTypeInfo.icon;
    const statusInfo = SalesTaskUtils.getStatusInfo(entityRefData['status']);
    const StatusIcon = statusInfo.icon;

    let html = (
      <div className="d-flex align-items-start p-2 border-bottom cursor-pointer" onClick={() => this.onViewTask(context, task)}>
        <Icon size={16} className={`text-${taskTypeInfo.color} me-2 mt-1`} />

        <div className="flex-grow-1">
          <div className="fw-medium mb-1" style={{ fontSize: '0.9rem' }}>
            {task.label}
          </div>

          <div className="d-flex align-items-center justify-content-between mb-1">
            <div className="d-flex align-items-center small text-muted">
              <FeatherIcon.Calendar size={12} className="me-1" />
              <span className="me-2">{this.formatDateTime(task.modifiedTime).date}</span>
              <FeatherIcon.Clock size={12} className="me-1" />
              <span>{this.formatDateTime(task.modifiedTime).time}</span>
            </div>

            <span className={`d-flex align-items-center px-2 py-1 rounded-2 bg-${statusInfo.color}-subtle text-${statusInfo.color}`}
              style={{ fontSize: '0.75rem', fontWeight: 500 }}>
              <StatusIcon size={12} className="me-1" />
              <span>{statusInfo.label}</span>
            </span>

          </div>

          <div className="d-flex align-items-center small text-muted">
            <module.account.WAvatars className='me-1' appContext={appContext} pageContext={pageContext}
              avatarIds={[task.assigneeAccountId]} avatarIdType='AccountId' width={20} height={20} borderRadius={10} />
            <span>{this.formatEmployeeName(task.assigneeFullName)}</span>
          </div>
        </div>
      </div>
    );
    return html;
  }

  renderFilter = (context: calendar.CalendarContext) => {
    let uiRoot = context.uiRoot as module.common.DbEntityTaskList;
    const { appContext, pageContext } = uiRoot.props;
    return (
      <div className='flex-vbox'>
        <UICalendarFilter context={context} appContext={appContext} pageContext={pageContext} />
      </div>
    )
  }
}

interface UICalendarFilterProps extends app.AppComponentProps {
  context: calendar.CalendarContext
}

export class UICalendarFilter extends app.AppComponent<UICalendarFilterProps> {
  popoverId: string = 'calendar-filter-' + util.IDTracker.next();
  isFiltering: boolean = false;

  render() {
    let { context } = this.props;
    let uiRoot = context.uiRoot as module.common.DbEntityTaskList;

    return (
      <div className="flex-vbox">

        <div className="fw-bold border-bottom my-1 d-flex flex-grow-0 align-items-center justify-content-between">
          <span>Sales Daily Tasks</span>
        </div>

        <div className="d-flex flex-wrap gap-1 mb-1">
          {/* All Tasks Checkbox */}
          <div className="form-check d-flex align-items-center" style={{ minWidth: '110px' }}>
            <input className="form-check-input me-2" type="checkbox"
              checked={!uiRoot.pluginActiveFilter}
              onChange={() => {
                uiRoot.pluginActiveFilter = null;
                uiRoot.forceUpdate();
              }}
              style={{
                width: '16px',
                height: '16px',
                cursor: 'pointer',
                backgroundColor: !uiRoot.pluginActiveFilter ? '#4285f4' : '#fff',
                borderColor: '#4285f4'
              }}
            />
            <label className="form-check-label" style={{ cursor: 'pointer' }}>
              All Tasks
            </label>
          </div>

          {/* Inquiry Requests Checkbox */}
          <div className="form-check d-flex align-items-center" style={{ minWidth: '110px' }}>
            <input className="form-check-input me-2" type="checkbox"
              checked={uiRoot.pluginActiveFilter === 'REQUEST_INQUIRY'}
              onChange={() => {
                if (uiRoot.pluginActiveFilter === 'REQUEST_INQUIRY') uiRoot.pluginActiveFilter = null;
                else uiRoot.pluginActiveFilter = 'REQUEST_INQUIRY';
                uiRoot.forceUpdate();
              }}
              style={{
                width: '16px',
                height: '16px',
                cursor: 'pointer',
                backgroundColor: uiRoot.pluginActiveFilter === 'REQUEST_INQUIRY' ? '#0f9d58' : '#fff',
                borderColor: '#0f9d58'
              }}
            />
            <label className="form-check-label" style={{ cursor: 'pointer' }}>
              Inquiry Requests
            </label>
          </div>

          {/* Call Tasks Checkbox */}
          <div className="form-check d-flex align-items-center" style={{ minWidth: '110px' }}>
            <input
              className="form-check-input me-2"
              type="checkbox"
              checked={uiRoot.pluginActiveFilter === 'CALL'}
              onChange={() => {
                if (uiRoot.pluginActiveFilter === 'CALL') uiRoot.pluginActiveFilter = null;
                else uiRoot.pluginActiveFilter = 'CALL';
                uiRoot.forceUpdate();
              }}
              style={{
                width: '16px',
                height: '16px',
                cursor: 'pointer',
                backgroundColor: uiRoot.pluginActiveFilter === 'CALL' ? '#db4437' : '#fff',
                borderColor: '#db4437'
              }}
            />
            <label className="form-check-label" style={{ cursor: 'pointer' }}>
              Car Schedule
            </label>
          </div>

          {/* Meet Customer Tasks Checkbox */}
          <div className="form-check d-flex align-items-center" style={{ minWidth: '110px' }}>
            <input className="form-check-input me-2" type="checkbox"
              checked={uiRoot.pluginActiveFilter === 'MEET_CUSTOMER'}
              onChange={() => {
                if (uiRoot.pluginActiveFilter === 'MEET_CUSTOMER') uiRoot.pluginActiveFilter = null;
                else uiRoot.pluginActiveFilter = 'MEET_CUSTOMER';
                uiRoot.forceUpdate();
              }}
              style={{
                width: '16px',
                height: '16px',
                cursor: 'pointer',
                backgroundColor: uiRoot.pluginActiveFilter === 'MEET_CUSTOMER' ? '#f4b400' : '#fff',
                borderColor: '#f4b400'
              }}
            />
            <label className="form-check-label" style={{ cursor: 'pointer' }}>
              Meeting/ Training
            </label>
          </div>
        </div>
      </div>
    );
  }
}
