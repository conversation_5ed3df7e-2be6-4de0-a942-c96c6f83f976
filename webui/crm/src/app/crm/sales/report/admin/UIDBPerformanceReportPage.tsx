import React from 'react';
import * as FeatherIcon from 'react-feather';
import { bs, grid, app, util, entity, sql } from '@datatp-ui/lib';
import { responsiveGridConfig } from '../../common';
import { T } from 'app/crm/price';
import {
  WQuickTimeRangeSelector,
  WReportGroupBySelector,
} from 'app/crm/common';
import { UICompanyPerformanceReport } from './UICompanyVolumePerformanceReport';
import { UIVolumeSalemanKeyAccountReportList } from '../../partners/report/UIVolumeSalemanKeyAccountReportList';
import SalesmanDataProcessor from './ReportDataProcessor';

export type Space = 'User' | 'Company' | 'System'

export class UIBDPerformanceReportPlugin extends entity.DbEntityListPlugin {
  space: Space;

  constructor() {
    super([]);
    this.backend = {
      context: 'company',
      service: 'PerformanceReportService',
      searchMethod: 'searchVolumePerformanceBySalemanReport',
    }

    this.searchParams = {
      "params": {},
      "filters": [...sql.createSearchFilter()],
      rangeFilters: [
        ...sql.createDateTimeFilter("reportDate", T("Report Date")),
      ],
    }
  }

  loadData(uiList: entity.DbEntityList<any>) {
    this.createBackendSearch(uiList, { param: this.searchParams }).call();
  }

  withParams(name: string, value: string | undefined) {
    this.addSearchParam(name, value);
    return this;
  }

  withDateFilter(fromValue: string, toValue: string) {
    if (this.searchParams) {
      let rangeFilters = this.searchParams.rangeFilters || [];
      for (let i = 0; i < rangeFilters.length; i++) {
        let filter = rangeFilters[i];
        if (filter.name === 'reportDate') {
          filter.fromValue = fromValue;
          filter.toValue = toValue;
          break;
        }
      }
      this.searchParams.rangeFilters = rangeFilters;
    }
    return this;
  }

}

interface UIBDPerformanceReportProps extends entity.DbEntityListProps {
  rawRecords?: any[];
  previousRecords?: any[];
}
export class UIBDPerformanceReport extends entity.DbEntityList<UIBDPerformanceReportProps> {

  createVGridConfig(): grid.VGridConfig {
    let config: grid.VGridConfig = {
      record: {
        dataCellHeight: 35,
        fields: [
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          {
            name: 'salemanFullName', label: T('Saleman'), width: 320, filterable: true, container: 'fixed-left',
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              let record: any = dRec.record;
              let label = record['salemanFullName'] || '';

              // Check if this is a total row
              let isPreviousPeriodTotal = label === 'Previous Period Total';
              let isCurrentPeriodTotal = label === 'Current Period Total';
              let isTotalRow = isPreviousPeriodTotal || isCurrentPeriodTotal;

              // Determine styling
              let textColor = 'text-success';
              let fontWeight = 'normal';

              if (isPreviousPeriodTotal) {
                textColor = 'text-info'; // Blue for previous period
                fontWeight = 'bold';
              } else if (isCurrentPeriodTotal) {
                textColor = 'text-primary'; // Primary blue for current period
                fontWeight = 'bold';
              }

              return (
                <div className="d-flex align-items-center justify-content-start w-100"
                  onClick={() => !isTotalRow && this.onDefaultSelect(dRec)}
                  style={{
                    cursor: isTotalRow ? 'default' : 'pointer',
                    userSelect: 'text'
                  }}>
                  <div className={`d-flex align-items-center px-1 py-1 ${textColor}`}
                    style={{ fontWeight: fontWeight }}>
                    {util.text.formater.uiTruncate(label, 300, true)}
                  </div>
                </div>
              );
            }
          },
          {
            name: 'totalJobCountNM', label: T('Shipment'), width: 100,
            fieldDataGetter(record) {
              return record['totalJobCountNM'] || '-';
            },
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              let record: any = dRec.record;
              let label = record['salemanFullName'] || '';
              let isPreviousPeriodTotal = label === 'Previous Period Total';
              let isCurrentPeriodTotal = label === 'Current Period Total';
              let isTotalRow = isPreviousPeriodTotal || isCurrentPeriodTotal;

              let textColor = '';
              let fontWeight = 'normal';
              if (isPreviousPeriodTotal) {
                textColor = 'text-info';
                fontWeight = 'bold';
              } else if (isCurrentPeriodTotal) {
                textColor = 'text-primary';
                fontWeight = 'bold';
              }

              let val = record['totalJobCountNM'] || '-';
              return <div className={`flex-hbox justify-content-end text-end w-100 ${textColor}`} style={{ fontWeight }}>{val}</div>
            }
          },
          {
            name: 'totalTeuNM', label: T('FCL (TEUs)'), width: 100,
            fieldDataGetter(record) {
              return record['totalTeuNM'] || '-';
            },
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              let record: any = dRec.record;
              let label = record['salemanFullName'] || '';
              let isPreviousPeriodTotal = label === 'Previous Period Total';
              let isCurrentPeriodTotal = label === 'Current Period Total';
              let isTotalRow = isPreviousPeriodTotal || isCurrentPeriodTotal;

              let textColor = '';
              let fontWeight = 'normal';
              if (isPreviousPeriodTotal) {
                textColor = 'text-info';
                fontWeight = 'bold';
              } else if (isCurrentPeriodTotal) {
                textColor = 'text-primary';
                fontWeight = 'bold';
              }

              let val = record['totalTeuNM'] || '-';
              return <div className={`flex-hbox justify-content-end text-end w-100 ${textColor}`} style={{ fontWeight }}>{val}</div>
            }
          },
          {
            name: 'totalHawbCbmNM', label: T(`LCL (CBM)`), width: 100,
            fieldDataGetter(record) {
              let value = record['totalHawbCbmNM'];
              return value && !isNaN(value) ? util.text.formater.number(value, 2) : '-'
            },
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              let record: any = dRec.record;
              let label = record['salemanFullName'] || '';
              let isPreviousPeriodTotal = label === 'Previous Period Total';
              let isCurrentPeriodTotal = label === 'Current Period Total';
              let isTotalRow = isPreviousPeriodTotal || isCurrentPeriodTotal;

              let textColor = '';
              let fontWeight = 'normal';
              if (isPreviousPeriodTotal) {
                textColor = 'text-info';
                fontWeight = 'bold';
              } else if (isCurrentPeriodTotal) {
                textColor = 'text-primary';
                fontWeight = 'bold';
              }

              let value = record['totalHawbCbmNM'];
              let val = value && !isNaN(value) ? util.text.formater.number(value, 2) : '-';
              return <div className={`flex-hbox justify-content-end text-end w-100 ${textColor}`} style={{ fontWeight }}>{val}</div>
            }
          },
          {
            name: 'totalHawbCwNM', label: T(`Air (KGS)`), width: 100,
            fieldDataGetter(record) {
              let value = record['totalHawbCwNM'];
              return value && !isNaN(value) ? util.text.formater.number(value, 2) : '-'
            },
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              let record: any = dRec.record;
              let label = record['salemanFullName'] || '';
              let isPreviousPeriodTotal = label === 'Previous Period Total';
              let isCurrentPeriodTotal = label === 'Current Period Total';
              let isTotalRow = isPreviousPeriodTotal || isCurrentPeriodTotal;

              let textColor = '';
              let fontWeight = 'normal';
              if (isPreviousPeriodTotal) {
                textColor = 'text-info';
                fontWeight = 'bold';
              } else if (isCurrentPeriodTotal) {
                textColor = 'text-primary';
                fontWeight = 'bold';
              }

              let value = record['totalHawbCwNM'];
              let val = value && !isNaN(value) ? util.text.formater.number(value, 2) : '-';
              return <div className={`flex-hbox justify-content-end text-end w-100 ${textColor}`} style={{ fontWeight }}>{val}</div>
            }
          },
          // {
          //   name: 'totalRevenueNMInUSD', label: T(`Revenue (USD)`), width: 150,
          //   fieldDataGetter(record) {
          //     let value = record['totalRevenueNMInUSD'];
          //     return value && !isNaN(value) ? util.text.formater.number(value, 0) : '-'
          //   },
          //   customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
          //     let record: any = dRec.record;
          //     let label = record['salemanFullName'] || '';
          //     let isPreviousPeriodTotal = label === 'Previous Period Total';
          //     let isCurrentPeriodTotal = label === 'Current Period Total';
          //     let isTotalRow = isPreviousPeriodTotal || isCurrentPeriodTotal;

          //     let textColor = '';
          //     let fontWeight = 'normal';
          //     if (isPreviousPeriodTotal) {
          //       textColor = 'text-info';
          //       fontWeight = 'bold';
          //     } else if (isCurrentPeriodTotal) {
          //       textColor = 'text-primary';
          //       fontWeight = 'bold';
          //     }

          //     let value = record['totalRevenueNMInUSD'];
          //     let val = value && !isNaN(value) ? util.text.formater.number(value, 0) : '-';
          //     return <div className={`flex-hbox justify-content-end text-end w-100 ${textColor}`} style={{ fontWeight }}>{val}</div>
          //   }
          // },
          // {
          //   name: 'totalProfitNMInUSD', label: T(`G/P (USD)`), width: 150,
          //   fieldDataGetter(record) {
          //     let value = record['totalProfitNMInUSD'];
          //     return value && !isNaN(value) ? util.text.formater.number(value, 0) : '-'
          //   },
          //   customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
          //     let record: any = dRec.record;
          //     let label = record['salemanFullName'] || '';
          //     let isPreviousPeriodTotal = label === 'Previous Period Total';
          //     let isCurrentPeriodTotal = label === 'Current Period Total';
          //     let isTotalRow = isPreviousPeriodTotal || isCurrentPeriodTotal;

          //     let textColor = '';
          //     let fontWeight = 'normal';
          //     if (isPreviousPeriodTotal) {
          //       textColor = 'text-info';
          //       fontWeight = 'bold';
          //     } else if (isCurrentPeriodTotal) {
          //       textColor = 'text-primary';
          //       fontWeight = 'bold';
          //     }

          //     let value = record['totalProfitNMInUSD'];
          //     let val = value && !isNaN(value) ? util.text.formater.number(value, 0) : '-';
          //     return <div className={`flex-hbox justify-content-end text-end w-100 ${textColor}`} style={{ fontWeight }}>{val}</div>
          //   }
          // },
          // {
          //   name: 'totalRevenueNMInVND', label: T(`Revenue (VND)`), width: 150,
          //   fieldDataGetter(record) {
          //     let value = record['totalRevenueNMInVND'];
          //     return value && !isNaN(value) ? util.text.formater.number(value, 0) : '-'
          //   },
          //   customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
          //     let record: any = dRec.record;
          //     let label = record['salemanFullName'] || '';
          //     let isPreviousPeriodTotal = label === 'Previous Period Total';
          //     let isCurrentPeriodTotal = label === 'Current Period Total';
          //     let isTotalRow = isPreviousPeriodTotal || isCurrentPeriodTotal;

          //     let textColor = '';
          //     let fontWeight = 'normal';
          //     if (isPreviousPeriodTotal) {
          //       textColor = 'text-info';
          //       fontWeight = 'bold';
          //     } else if (isCurrentPeriodTotal) {
          //       textColor = 'text-primary';
          //       fontWeight = 'bold';
          //     }

          //     let value = record['totalRevenueNMInVND'];
          //     let val = value && !isNaN(value) ? util.text.formater.number(value, 0) : '-';
          //     return <div className={`flex-hbox justify-content-end text-end w-100 ${textColor}`} style={{ fontWeight }}>{val}</div>
          //   }
          // },
          // {
          //   name: 'totalProfitNMInVND', label: T(`G/P (VND)`), width: 150,
          //   fieldDataGetter(record) {
          //     let value = record['totalProfitNMInVND'];
          //     return value && !isNaN(value) ? util.text.formater.number(value, 0) : '-'
          //   },
          //   customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
          //     let record: any = dRec.record;
          //     let label = record['salemanFullName'] || '';
          //     let isPreviousPeriodTotal = label === 'Previous Period Total';
          //     let isCurrentPeriodTotal = label === 'Current Period Total';
          //     let isTotalRow = isPreviousPeriodTotal || isCurrentPeriodTotal;

          //     let textColor = '';
          //     let fontWeight = 'normal';
          //     if (isPreviousPeriodTotal) {
          //       textColor = 'text-info';
          //       fontWeight = 'bold';
          //     } else if (isCurrentPeriodTotal) {
          //       textColor = 'text-primary';
          //       fontWeight = 'bold';
          //     }

          //     let value = record['totalProfitNMInVND'];
          //     let val = value && !isNaN(value) ? util.text.formater.number(value, 0) : '-';
          //     return <div className={`flex-hbox justify-content-end text-end w-100 ${textColor}`} style={{ fontWeight }}>{val}</div>
          //   }
          // },
          {
            name: 'totalJobCountFH', label: T('Shipment'), width: 100,
            fieldDataGetter(record) {
              return record['totalJobCountFH'] || '-';
            },
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              let record: any = dRec.record;
              let label = record['salemanFullName'] || '';
              let isPreviousPeriodTotal = label === 'Previous Period Total';
              let isCurrentPeriodTotal = label === 'Current Period Total';
              let isTotalRow = isPreviousPeriodTotal || isCurrentPeriodTotal;

              let textColor = '';
              let fontWeight = 'normal';
              if (isPreviousPeriodTotal) {
                textColor = 'text-info';
                fontWeight = 'bold';
              } else if (isCurrentPeriodTotal) {
                textColor = 'text-primary';
                fontWeight = 'bold';
              }

              let val = record['totalJobCountFH'] || '-';
              return <div className={`flex-hbox justify-content-end text-end w-100 ${textColor}`} style={{ fontWeight }}>{val}</div>
            }
          },
          {
            name: 'totalTeuFH', label: T('FCL (TEUs)'), width: 100,
            fieldDataGetter(record) {
              return record['totalTeuFH'] || '-';
            },
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              let record: any = dRec.record;
              let label = record['salemanFullName'] || '';
              let isPreviousPeriodTotal = label === 'Previous Period Total';
              let isCurrentPeriodTotal = label === 'Current Period Total';
              let isTotalRow = isPreviousPeriodTotal || isCurrentPeriodTotal;

              let textColor = '';
              let fontWeight = 'normal';
              if (isPreviousPeriodTotal) {
                textColor = 'text-info';
                fontWeight = 'bold';
              } else if (isCurrentPeriodTotal) {
                textColor = 'text-primary';
                fontWeight = 'bold';
              }

              let val = record['totalTeuFH'] || '-';
              return <div className={`flex-hbox justify-content-end text-end w-100 ${textColor}`} style={{ fontWeight }}>{val}</div>
            }
          },
          {
            name: 'totalHawbCbmFH', label: T(`LCL (CBM)`), width: 100,
            fieldDataGetter(record) {
              let value = record['totalHawbCbmFH'];
              return value && !isNaN(value) ? util.text.formater.number(value, 2) : '-'
            },
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              let record: any = dRec.record;
              let label = record['salemanFullName'] || '';
              let isPreviousPeriodTotal = label === 'Previous Period Total';
              let isCurrentPeriodTotal = label === 'Current Period Total';
              let isTotalRow = isPreviousPeriodTotal || isCurrentPeriodTotal;

              let textColor = '';
              let fontWeight = 'normal';
              if (isPreviousPeriodTotal) {
                textColor = 'text-info';
                fontWeight = 'bold';
              } else if (isCurrentPeriodTotal) {
                textColor = 'text-primary';
                fontWeight = 'bold';
              }

              let value = record['totalHawbCbmFH'];
              let val = value && !isNaN(value) ? util.text.formater.number(value, 2) : '-';
              return <div className={`flex-hbox justify-content-end text-end w-100 ${textColor}`} style={{ fontWeight }}>{val}</div>
            }
          },
          {
            name: 'totalHawbCwFH', label: T(`Air (KGS)`), width: 100,
            fieldDataGetter(record) {
              let value = record['totalHawbCwFH'];
              return value && !isNaN(value) ? util.text.formater.number(value, 2) : '-'
            },
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              let record: any = dRec.record;
              let label = record['salemanFullName'] || '';
              let isPreviousPeriodTotal = label === 'Previous Period Total';
              let isCurrentPeriodTotal = label === 'Current Period Total';
              let isTotalRow = isPreviousPeriodTotal || isCurrentPeriodTotal;

              let textColor = '';
              let fontWeight = 'normal';
              if (isPreviousPeriodTotal) {
                textColor = 'text-info';
                fontWeight = 'bold';
              } else if (isCurrentPeriodTotal) {
                textColor = 'text-primary';
                fontWeight = 'bold';
              }

              let value = record['totalHawbCwFH'];
              let val = value && !isNaN(value) ? util.text.formater.number(value, 2) : '-';
              return <div className={`flex-hbox justify-content-end text-end w-100 ${textColor}`} style={{ fontWeight }}>{val}</div>
            }
          },
          // {
          //   name: 'totalRevenueFHInUSD', label: T(`Revenue (USD)`), width: 150,
          //   fieldDataGetter(record) {
          //     let value = record['totalRevenueFHInUSD'];
          //     return value && !isNaN(value) ? util.text.formater.number(value, 0) : '-'
          //   },
          //   customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
          //     let record: any = dRec.record;
          //     let label = record['salemanFullName'] || '';
          //     let isPreviousPeriodTotal = label === 'Previous Period Total';
          //     let isCurrentPeriodTotal = label === 'Current Period Total';
          //     let isTotalRow = isPreviousPeriodTotal || isCurrentPeriodTotal;

          //     let textColor = '';
          //     let fontWeight = 'normal';
          //     if (isPreviousPeriodTotal) {
          //       textColor = 'text-info';
          //       fontWeight = 'bold';
          //     } else if (isCurrentPeriodTotal) {
          //       textColor = 'text-primary';
          //       fontWeight = 'bold';
          //     }

          //     let value = record['totalRevenueFHInUSD'];
          //     let val = value && !isNaN(value) ? util.text.formater.number(value, 0) : '-';
          //     return <div className={`flex-hbox justify-content-end text-end w-100 ${textColor}`} style={{ fontWeight }}>{val}</div>
          //   }
          // },
          // {
          //   name: 'totalProfitFHInUSD', label: T(`G/P (USD)`), width: 150,
          //   fieldDataGetter(record) {
          //     let value = record['totalProfitFHInUSD'];
          //     return value && !isNaN(value) ? util.text.formater.number(value, 0) : '-'
          //   },
          //   customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
          //     let record: any = dRec.record;
          //     let label = record['salemanFullName'] || '';
          //     let isPreviousPeriodTotal = label === 'Previous Period Total';
          //     let isCurrentPeriodTotal = label === 'Current Period Total';
          //     let isTotalRow = isPreviousPeriodTotal || isCurrentPeriodTotal;

          //     let textColor = '';
          //     let fontWeight = 'normal';
          //     if (isPreviousPeriodTotal) {
          //       textColor = 'text-info';
          //       fontWeight = 'bold';
          //     } else if (isCurrentPeriodTotal) {
          //       textColor = 'text-primary';
          //       fontWeight = 'bold';
          //     }

          //     let value = record['totalProfitFHInUSD'];
          //     let val = value && !isNaN(value) ? util.text.formater.number(value, 0) : '-';
          //     return <div className={`flex-hbox justify-content-end text-end w-100 ${textColor}`} style={{ fontWeight }}>{val}</div>
          //   }
          // },
          // {
          //   name: 'totalRevenueFHInVND', label: T(`Revenue (VND)`), width: 150,
          //   fieldDataGetter(record) {
          //     let value = record['totalRevenueFHInVND'];
          //     return value && !isNaN(value) ? util.text.formater.number(value, 0) : '-'
          //   },
          //   customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
          //     let record: any = dRec.record;
          //     let label = record['salemanFullName'] || '';
          //     let isPreviousPeriodTotal = label === 'Previous Period Total';
          //     let isCurrentPeriodTotal = label === 'Current Period Total';
          //     let isTotalRow = isPreviousPeriodTotal || isCurrentPeriodTotal;

          //     let textColor = '';
          //     let fontWeight = 'normal';
          //     if (isPreviousPeriodTotal) {
          //       textColor = 'text-info';
          //       fontWeight = 'bold';
          //     } else if (isCurrentPeriodTotal) {
          //       textColor = 'text-primary';
          //       fontWeight = 'bold';
          //     }

          //     let value = record['totalRevenueFHInVND'];
          //     let val = value && !isNaN(value) ? util.text.formater.number(value, 0) : '-';
          //     return <div className={`flex-hbox justify-content-end text-end w-100 ${textColor}`} style={{ fontWeight }}>{val}</div>
          //   }
          // },
          // {
          //   name: 'totalProfitFHInVND', label: T(`G/P (VND)`), width: 150,
          //   fieldDataGetter(record) {
          //     let value = record['totalProfitFHInVND'];
          //     return value && !isNaN(value) ? util.text.formater.number(value, 0) : '-'
          //   },
          //   customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
          //     let record: any = dRec.record;
          //     let label = record['salemanFullName'] || '';
          //     let isPreviousPeriodTotal = label === 'Previous Period Total';
          //     let isCurrentPeriodTotal = label === 'Current Period Total';
          //     let isTotalRow = isPreviousPeriodTotal || isCurrentPeriodTotal;

          //     let textColor = '';
          //     let fontWeight = 'normal';
          //     if (isPreviousPeriodTotal) {
          //       textColor = 'text-info';
          //       fontWeight = 'bold';
          //     } else if (isCurrentPeriodTotal) {
          //       textColor = 'text-primary';
          //       fontWeight = 'bold';
          //     }

          //     let value = record['totalProfitFHInVND'];
          //     let val = value && !isNaN(value) ? util.text.formater.number(value, 0) : '-';
          //     return <div className={`flex-hbox justify-content-end text-end w-100 ${textColor}`} style={{ fontWeight }}>{val}</div>
          //   }
          // },

        ],
        fieldGroups: {
          "Freehand": {
            label: T('Freehand'),
            fields: [
              'totalJobCountFH', 'totalTeuFH', 'cont20FH', 'cont40FH', 'cont45FH', 'totalHawbCbmFH', 'totalHawbCwFH',
              'totalRevenueFHInUSD', 'totalProfitFHInUSD', 'totalRevenueFHInVND', 'totalProfitFHInVND']
          },
          "Nominated": {
            label: T('Nominated'),
            fields: [
              'totalJobCountNM', 'totalTeuNM', 'cont20NM', 'cont40NM', 'cont45NM', 'totalHawbCbmNM', 'totalHawbGwNM',
              'totalHawbCwNM', 'totalRevenueNMInUSD', 'totalProfitNMInUSD', 'totalRevenueNMInVND', 'totalProfitNMInVND']
          },
        },
      },
      toolbar: { hide: true },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table',
            header: {
              createRecords: (_ctx: grid.VGridContext) => {
                let records = _ctx.model.getFilterRecords();
                const { previousRecords } = this.props;

                // Calculate current period totals
                let currentTotalJobCountNM = 0;
                let currentTotalTeuNM = 0;
                let currentTotalHawbCbmNM = 0;
                let currentTotalHawbCwNM = 0;
                let currentTotalRevenueNMInUSD = 0;
                let currentTotalProfitNMInUSD = 0;
                let currentTotalRevenueNMInVND = 0;
                let currentTotalProfitNMInVND = 0;

                let currentTotalJobCountFH = 0;
                let currentTotalTeuFH = 0;
                let currentTotalHawbCbmFH = 0;
                let currentTotalHawbCwFH = 0;
                let currentTotalRevenueFHInUSD = 0;
                let currentTotalProfitFHInUSD = 0;
                let currentTotalRevenueFHInVND = 0;
                let currentTotalProfitFHInVND = 0;

                records.forEach(record => {
                  currentTotalJobCountNM += Number(record.totalJobCountNM || 0);
                  currentTotalTeuNM += Number(record.totalTeuNM || 0);
                  currentTotalHawbCbmNM += Number(record.totalHawbCbmNM || 0);
                  currentTotalHawbCwNM += Number(record.totalHawbCwNM || 0);
                  currentTotalRevenueNMInUSD += Number(record.totalRevenueNMInUSD || 0);
                  currentTotalProfitNMInUSD += Number(record.totalProfitNMInUSD || 0);
                  currentTotalRevenueNMInVND += Number(record.totalRevenueNMInVND || 0);
                  currentTotalProfitNMInVND += Number(record.totalProfitNMInVND || 0);

                  currentTotalJobCountFH += Number(record.totalJobCountFH || 0);
                  currentTotalTeuFH += Number(record.totalTeuFH || 0);
                  currentTotalHawbCbmFH += Number(record.totalHawbCbmFH || 0);
                  currentTotalHawbCwFH += Number(record.totalHawbCwFH || 0);
                  currentTotalRevenueFHInUSD += Number(record.totalRevenueFHInUSD || 0);
                  currentTotalProfitFHInUSD += Number(record.totalProfitFHInUSD || 0);
                  currentTotalRevenueFHInVND += Number(record.totalRevenueFHInVND || 0);
                  currentTotalProfitFHInVND += Number(record.totalProfitFHInVND || 0);
                });

                // Calculate previous period totals
                let previousTotalJobCountNM = 0;
                let previousTotalTeuNM = 0;
                let previousTotalHawbCbmNM = 0;
                let previousTotalHawbCwNM = 0;
                let previousTotalRevenueNMInUSD = 0;
                let previousTotalProfitNMInUSD = 0;
                let previousTotalRevenueNMInVND = 0;
                let previousTotalProfitNMInVND = 0;

                let previousTotalJobCountFH = 0;
                let previousTotalTeuFH = 0;
                let previousTotalHawbCbmFH = 0;
                let previousTotalHawbCwFH = 0;
                let previousTotalRevenueFHInUSD = 0;
                let previousTotalProfitFHInUSD = 0;
                let previousTotalRevenueFHInVND = 0;
                let previousTotalProfitFHInVND = 0;

                if (previousRecords && previousRecords.length > 0) {
                  // Previous records are already processed, use them directly
                  previousRecords.forEach(record => {
                    previousTotalJobCountNM += Number(record.totalJobCountNM || 0);
                    previousTotalTeuNM += Number(record.totalTeuNM || 0);
                    previousTotalHawbCbmNM += Number(record.totalHawbCbmNM || 0);
                    previousTotalHawbCwNM += Number(record.totalHawbCwNM || 0);
                    previousTotalRevenueNMInUSD += Number(record.totalRevenueNMInUSD || 0);
                    previousTotalProfitNMInUSD += Number(record.totalProfitNMInUSD || 0);
                    previousTotalRevenueNMInVND += Number(record.totalRevenueNMInVND || 0);
                    previousTotalProfitNMInVND += Number(record.totalProfitNMInVND || 0);

                    previousTotalJobCountFH += Number(record.totalJobCountFH || 0);
                    previousTotalTeuFH += Number(record.totalTeuFH || 0);
                    previousTotalHawbCbmFH += Number(record.totalHawbCbmFH || 0);
                    previousTotalHawbCwFH += Number(record.totalHawbCwFH || 0);
                    previousTotalRevenueFHInUSD += Number(record.totalRevenueFHInUSD || 0);
                    previousTotalProfitFHInUSD += Number(record.totalProfitFHInUSD || 0);
                    previousTotalRevenueFHInVND += Number(record.totalRevenueFHInVND || 0);
                    previousTotalProfitFHInVND += Number(record.totalProfitFHInVND || 0);
                  });
                }

                let summaryRows = [];

                // Only add Previous Period Total if previousRecords is not empty
                if (previousRecords && previousRecords.length > 0) {
                  summaryRows.push({
                    salemanFullName: 'Previous Period Total',
                    totalJobCountNM: previousTotalJobCountNM,
                    totalTeuNM: previousTotalTeuNM,
                    totalHawbCbmNM: previousTotalHawbCbmNM,
                    totalHawbCwNM: previousTotalHawbCwNM,
                    totalRevenueNMInUSD: previousTotalRevenueNMInUSD,
                    totalProfitNMInUSD: previousTotalProfitNMInUSD,
                    totalRevenueNMInVND: previousTotalRevenueNMInVND,
                    totalProfitNMInVND: previousTotalProfitNMInVND,
                    totalJobCountFH: previousTotalJobCountFH,
                    totalTeuFH: previousTotalTeuFH,
                    totalHawbCbmFH: previousTotalHawbCbmFH,
                    totalHawbCwFH: previousTotalHawbCwFH,
                    totalRevenueFHInUSD: previousTotalRevenueFHInUSD,
                    totalProfitFHInUSD: previousTotalProfitFHInUSD,
                    totalRevenueFHInVND: previousTotalRevenueFHInVND,
                    totalProfitFHInVND: previousTotalProfitFHInVND,
                  });
                }

                // Always add Current Period Total
                summaryRows.push({
                  salemanFullName: 'Current Period Total',
                  totalJobCountNM: currentTotalJobCountNM,
                  totalTeuNM: currentTotalTeuNM,
                  totalHawbCbmNM: currentTotalHawbCbmNM,
                  totalHawbCwNM: currentTotalHawbCwNM,
                  totalRevenueNMInUSD: currentTotalRevenueNMInUSD,
                  totalProfitNMInUSD: currentTotalProfitNMInUSD,
                  totalRevenueNMInVND: currentTotalRevenueNMInVND,
                  totalProfitNMInVND: currentTotalProfitNMInVND,
                  totalJobCountFH: currentTotalJobCountFH,
                  totalTeuFH: currentTotalTeuFH,
                  totalHawbCbmFH: currentTotalHawbCbmFH,
                  totalHawbCwFH: currentTotalHawbCwFH,
                  totalRevenueFHInUSD: currentTotalRevenueFHInUSD,
                  totalProfitFHInUSD: currentTotalProfitFHInUSD,
                  totalRevenueFHInVND: currentTotalRevenueFHInVND,
                  totalProfitFHInVND: currentTotalProfitFHInVND,
                });

                return summaryRows;
              }
            }
          }
        },
      },
    };
    return responsiveGridConfig(config);
  }

  onDefaultSelect(dRecord: grid.DisplayRecord) {
    const { appContext, pageContext } = this.props;
    let record: any = dRecord.record;
    let salemanContactId = record['salemanContactId'];

    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    let dateFilter = new util.TimeRange();
    dateFilter.fromSetDate(firstDayOfMonth);
    dateFilter.toSetDate(lastDayOfMonth);

    let searchParams = {
      "params": { salemanContactId: salemanContactId },
      "filters": [...sql.createSearchFilter()],
      rangeFilters: [
        ...sql.createDateTimeFilter("reportDate", T("Report Date")),
      ],
    }

    let rangeFilters = searchParams.rangeFilters;
    for (let i = 0; i < rangeFilters.length; i++) {
      let filter = rangeFilters[i];
      if (filter.name === 'reportDate') {
        filter.fromValue = dateFilter.fromFormat();
        filter.toValue = dateFilter.toFormat();
        break;
      }
    }
    searchParams.rangeFilters = rangeFilters;

    appContext.createHttpBackendCall("PerformanceReportService", "searchVolumePerformanceByHouseBill", { params: searchParams })
      .withSuccessData((records: Array<any>) => {

        let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (
            <UIVolumeSalemanKeyAccountReportList appContext={appCtx} pageContext={pageCtx}
              plugin={new entity.DbEntityListPlugin(records)} hideSubtotalColumns={true} />
          );
        }
        pageContext.createPopupPage(`view-all-${util.IDTracker.next()}`, "Shipment Details", createAppPage, { size: 'xl', backdrop: 'static' });
      })
      .call();
  }
}

interface FilterParam {
  dateFilter: { fromValue: string, toValue: string, label: string };
  groupedBy: { label: string, value: string };
  enablePanel: boolean;
}

interface UIBDPerformanceReportState { }
interface UIBDPerformanceReportPageProps extends app.AppComponentProps { }

export class UIBDPerformanceReportPage extends app.AppComponent<UIBDPerformanceReportPageProps, UIBDPerformanceReportState> {
  viewId: number = util.IDTracker.next();
  reportFilter: FilterParam;
  agentTransactionsTreeRef: React.RefObject<UIBDPerformanceReport>;
  rawRecords: any[] = [];
  previousRecords: any[] = [];

  constructor(props: UIBDPerformanceReportPageProps) {
    super(props);
    this.agentTransactionsTreeRef = React.createRef<UIBDPerformanceReport>();

    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    let dateFilter = new util.TimeRange();
    dateFilter.fromSetDate(firstDayOfMonth);
    dateFilter.toSetDate(lastDayOfMonth);

    this.reportFilter = {
      groupedBy: { label: 'Saleman', value: 'SALEMAN' },
      dateFilter: { fromValue: dateFilter.fromFormat(), toValue: dateFilter.toFormat(), label: 'This Month' },
      enablePanel: localStorage.getItem('enableBDPerformanceReportPanel') === 'true'
    }
    this.markLoading(true);
  }

  componentDidMount(): void {
    this.loadData();
  }

  loadData(): void {
    const { dateFilter, groupedBy } = this.reportFilter;
    const { appContext } = this.props;

    // Reset arrays
    this.rawRecords = [];
    this.previousRecords = [];

    // Tính toán kỳ trước đó dựa theo label
    let currentFromDate = util.TimeUtil.parseCompactDateTimeFormat(dateFilter.fromValue);
    let currentToDate = util.TimeUtil.parseCompactDateTimeFormat(dateFilter.toValue);
    let previousFromDate: Date;
    let previousToDate: Date;

    let label = dateFilter.label;
    if ('This Week' === label) {
      // Tuần trước: lùi 7 ngày
      previousFromDate = new Date(currentFromDate);
      previousFromDate.setDate(previousFromDate.getDate() - 7);
      previousToDate = new Date(currentToDate);
      previousToDate.setDate(previousToDate.getDate() - 7);
    } else if ('This Month' === label) {
      // Tháng trước: lùi 1 tháng
      previousFromDate = new Date(currentFromDate);
      previousFromDate.setMonth(previousFromDate.getMonth() - 1);
      previousToDate = new Date(currentToDate);
      previousToDate.setMonth(previousToDate.getMonth() - 1);
    } else if ('This Quarter' === label) {
      // Quý trước: lùi 3 tháng
      previousFromDate = new Date(currentFromDate);
      previousFromDate.setMonth(previousFromDate.getMonth() - 3);
      previousToDate = new Date(currentToDate);
      previousToDate.setMonth(previousToDate.getMonth() - 3);
    } else if ('This Year' === label) {
      // Năm trước: lùi 1 năm
      previousFromDate = new Date(currentFromDate);
      previousFromDate.setFullYear(previousFromDate.getFullYear() - 1);
      previousToDate = new Date(currentToDate);
      previousToDate.setFullYear(previousToDate.getFullYear() - 1);
    } else {
      // Mặc định: không lấy dữ liệu kỳ trước cho custom date range
      // Skip previous period for custom date ranges
    }

    let methodApi: { serviceName: string, methodName: string } = { serviceName: 'PerformanceReportService', methodName: 'searchVolumePerformanceBySalemanReport' };
    if (groupedBy.value === 'COMPANY') {
      methodApi = { serviceName: 'PerformanceReportService', methodName: 'searchVolumePerformanceByShipment' };
    }

    // Query 1: Lấy dữ liệu kỳ hiện tại
    let currentPlugin = new UIBDPerformanceReportPlugin()
      .withDateFilter(dateFilter.fromValue, dateFilter.toValue);

    appContext.createHttpBackendCall(methodApi.serviceName, methodApi.methodName, { params: currentPlugin.getSearchParams() })
      .withSuccessData((records: Array<any>) => {
        this.rawRecords = records;

        if (label === 'This Week' || label === 'This Month' || label === 'This Quarter' || label === 'This Year') {
          let previousTimeRange = new util.TimeRange();
          previousTimeRange.fromSetDate(previousFromDate);
          previousTimeRange.toSetDate(previousToDate);
          let previousFromValue = previousTimeRange.fromFormat();
          let previousToValue = previousTimeRange.toFormat();

          let previousPlugin = new UIBDPerformanceReportPlugin()
            .withDateFilter(previousFromValue, previousToValue);

          appContext.createHttpBackendCall(methodApi.serviceName, methodApi.methodName, { params: previousPlugin.getSearchParams() })
            .withSuccessData((previousRecords: Array<any>) => {
              this.previousRecords = previousRecords;
              this.markLoading(false);
              this.forceUpdate();
            })
            .call();
        } else {
          this.previousRecords = [];
          this.markLoading(false);
          this.forceUpdate();
        }
      })
      .call()
  }

  doExport = () => { }

  renderHeader() {
    const { appContext, pageContext } = this.props;
    const { dateFilter } = this.reportFilter;
    return (
      <div className="bg-white flex-hbox flex-grow-0 align-items-center justify-content-between px-2 py-1">

        <div className="flex-hbox justify-content-start align-items-center" >
          <h5 style={{ color: '#6c757d' }}>
            <FeatherIcon.Users className="me-2" size={18} />
            Volume Performance Overview
          </h5>

          <div className='flex-hbox align-items-center flex-grow-0 border-start' >
          </div>
        </div>

        <div className="flex-hbox justify-content-end align-items-center gap-1" >

          <WReportGroupBySelector appContext={appContext} pageContext={pageContext}
            groupBy={this.reportFilter.groupedBy}
            onModify={(bean: any, _field: string, _oldVal: any, _newVal: any) => {
              this.reportFilter.groupedBy = bean;
              this.markLoading(true);
              this.forceUpdate();
              this.loadData();
            }} />

          <WQuickTimeRangeSelector appContext={appContext} pageContext={pageContext}
            initBean={dateFilter}
            onModify={(bean: any, _field: string, _oldVal: any, _newVal: any) => {
              this.reportFilter.dateFilter = bean;
              this.markLoading(true);
              this.forceUpdate();
              this.loadData();
            }} />

          <bs.Button laf="secondary" outline size="sm" className="p-1" style={{ color: '#6c757d' }}
            onClick={this.doExport}>
            <FeatherIcon.Download size={14} className="me-2" />
            Export
          </bs.Button>

        </div>
      </div>
    );
  }

  renderReportView() {
    const { groupedBy } = this.reportFilter;
    const { appContext, pageContext } = this.props;

    const processor = new SalesmanDataProcessor();

    if (groupedBy.value === 'SALEMAN') {
      let records: any[] = processor.processGroupBySaleman(this.rawRecords);
      const plugin = new entity.DbEntityListPlugin(records);
      let previousRecords: any[] = processor.processGroupBySaleman(this.previousRecords);

      return (
        <UIBDPerformanceReport ref={this.agentTransactionsTreeRef}
          rawRecords={this.rawRecords} previousRecords={previousRecords}
          appContext={appContext} pageContext={pageContext} plugin={plugin} />
      )
    } else if (groupedBy.value === 'COMPANY') {
      let records: any[] = processor.processGroupByCompany(this.rawRecords);
      let previousRecords: any[] = processor.processGroupByCompany(this.previousRecords);
      const plugin = new entity.DbEntityListPlugin(records);
      return (
        <UICompanyPerformanceReport
          rawRecords={this.rawRecords} previousRecords={previousRecords}
          appContext={appContext} pageContext={pageContext} plugin={plugin} />
      )
    } else {
      let records: any[] = processor.processGroupBySaleman(this.rawRecords);
      const plugin = new entity.DbEntityListPlugin(records);
      let previousRecords: any[] = processor.processGroupBySaleman(this.previousRecords);
      return (
        <UIBDPerformanceReport ref={this.agentTransactionsTreeRef}
          rawRecords={this.rawRecords} previousRecords={previousRecords}
          appContext={appContext} pageContext={pageContext} plugin={plugin} />
      )
    }
  }

  render(): React.JSX.Element {
    return (
      <div className="flex-vbox mx-1 p-1 bg-white rounded-md w-100 h-100 my-1" >
        {this.renderHeader()}
        <div className="flex-vbox border-top bg-body-highlight h-100" key={util.IDTracker.next()}>
          {this.isLoading() ? this.renderLoading() : this.renderReportView()}
        </div>
      </div>
    )
  }
}
