import React from 'react';
import * as FeatherIcon from 'react-feather';
import { grid, util, entity, sql, bs } from '@datatp-ui/lib';
import { responsiveGridConfig } from '../../common';
import { T } from 'app/crm/price';
import {
  PieChart, Pie, Cell, Tooltip, Legend, BarChart,
  Bar, XAxis, YAxis, CartesianGrid, ResponsiveContainer, ComposedChart, Line
} from 'recharts';
import { UIFreehandPerformanceSummary, UINominatedPerformanceSummary } from './UIBranchPerformanceSummary';

export type Space = 'User' | 'Company' | 'System'

export class UICompanyPerformanceReportPlugin extends entity.DbEntityListPlugin {
  space: Space;

  constructor() {
    super([]);
    this.backend = {
      context: 'company',
      service: 'PerformanceReportService',
      searchMethod: 'searchVolumePerformanceByCompany',
    }

    this.searchParams = {
      "params": {},
      "filters": [...sql.createSearchFilter()],
      rangeFilters: [
        ...sql.createDateTimeFilter("reportDate", T("Report Date")),
      ],
    }
  }

  loadData(uiList: entity.DbEntityList<any>) {
    this.createBackendSearch(uiList, { param: this.searchParams }).call();
  }

  withParams(name: string, value: string | undefined) {
    this.addSearchParam(name, value);
    return this;
  }

  withDateFilter(fromValue: string, toValue: string) {
    let startOfFromDate: Date | null = null;
    let endOfToDate: Date | null = null;

    if ((fromValue || '').trim()) {
      let fromDate: Date = util.TimeUtil.parseCompactDateTimeFormat(fromValue);
      startOfFromDate = new Date(fromDate.getFullYear(), fromDate.getMonth(), fromDate.getDate());
    }

    if ((toValue || '').trim()) {
      let toDate: Date = util.TimeUtil.parseCompactDateTimeFormat(toValue);
      endOfToDate = new Date(toDate.getFullYear(), toDate.getMonth(), toDate.getDate(), 23, 59, 59, 999);
    }

    if (this.searchParams) {
      let rangeFilters = this.searchParams.rangeFilters;
      if (rangeFilters) {
        for (let i = 0; i < rangeFilters.length; i++) {
          let filter = rangeFilters[i];
          if (filter.name === 'reportDate') {
            filter.fromValue = startOfFromDate ? util.TimeUtil.javaCompactDateTimeFormat(startOfFromDate) : fromValue;
            filter.toValue = endOfToDate ? util.TimeUtil.javaCompactDateTimeFormat(endOfToDate) : toValue;
            break;
          }
        }
      }
      this.searchParams.rangeFilters = rangeFilters;
    }
    return this;
  }

}

interface UICompanyPerformanceReportProps extends entity.DbEntityListProps {
  rawRecords?: any[];
  previousRecords?: any[];
}
export class UICompanyPerformanceReport extends entity.DbEntityList<UICompanyPerformanceReportProps> {

  createVGridConfig(): grid.VGridConfig {
    let config: grid.VGridConfig = {
      record: {
        dataCellHeight: 35,
        fields: [
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          {
            name: 'companyBranchCode', label: T('Branch'), width: 180, filterable: true, container: 'fixed-left',
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              let record: any = dRec.record;
              let label = record['companyBranchCode'] || '';

              // Check if this is a total row
              let isPreviousPeriodTotal = label === 'Previous Period Total';
              let isCurrentPeriodTotal = label === 'Current Period Total';
              let isTotalRow = isPreviousPeriodTotal || isCurrentPeriodTotal;

              // Determine styling
              let textColor = 'text-success';
              let fontWeight = 'normal';

              if (isPreviousPeriodTotal) {
                textColor = 'text-info'; // Blue for previous period
                fontWeight = 'bold';
              } else if (isCurrentPeriodTotal) {
                textColor = 'text-primary'; // Primary blue for current period
                fontWeight = 'bold';
              }

              return (
                <div className="d-flex align-items-center justify-content-start w-100"
                  onClick={() => !isTotalRow && this.onDefaultSelect(dRec)}
                  style={{
                    cursor: isTotalRow ? 'default' : 'pointer',
                    userSelect: 'text'
                  }}>
                  <div className={`d-flex align-items-center px-1 py-1 ${textColor}`}
                    style={{ fontWeight: fontWeight }}>
                    {util.text.formater.uiTruncate(label, 300, true)}
                  </div>
                </div>
              );
            }
          },
          {
            name: 'totalJobCountNM', label: T('Shipment'), width: 100,
            fieldDataGetter(record) {
              return record['totalJobCountNM'] || '-';
            },
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              let record: any = dRec.record;
              let label = record['salemanFullName'] || '';
              let isPreviousPeriodTotal = label === 'Previous Period Total';
              let isCurrentPeriodTotal = label === 'Current Period Total';
              let isTotalRow = isPreviousPeriodTotal || isCurrentPeriodTotal;

              let textColor = '';
              let fontWeight = 'normal';
              if (isPreviousPeriodTotal) {
                textColor = 'text-info';
                fontWeight = 'bold';
              } else if (isCurrentPeriodTotal) {
                textColor = 'text-primary';
                fontWeight = 'bold';
              }

              let val = record['totalJobCountNM'] || '-';
              return <div className={`flex-hbox justify-content-end text-end w-100 ${textColor}`} style={{ fontWeight }}>{val}</div>
            }
          },
          {
            name: 'totalTeuNM', label: T('FCL (TEUs)'), width: 100,
            fieldDataGetter(record) {
              return record['totalTeuNM'] || '-';
            },
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              let record: any = dRec.record;
              let label = record['salemanFullName'] || '';
              let isPreviousPeriodTotal = label === 'Previous Period Total';
              let isCurrentPeriodTotal = label === 'Current Period Total';
              let isTotalRow = isPreviousPeriodTotal || isCurrentPeriodTotal;

              let textColor = '';
              let fontWeight = 'normal';
              if (isPreviousPeriodTotal) {
                textColor = 'text-info';
                fontWeight = 'bold';
              } else if (isCurrentPeriodTotal) {
                textColor = 'text-primary';
                fontWeight = 'bold';
              }

              let val = record['totalTeuNM'] || '-';
              return <div className={`flex-hbox justify-content-end text-end w-100 ${textColor}`} style={{ fontWeight }}>{val}</div>
            }
          },
          {
            name: 'totalHawbCbmNM', label: T(`LCL (CBM)`), width: 100,
            fieldDataGetter(record) {
              let value = record['totalHawbCbmNM'];
              return value && !isNaN(value) ? util.text.formater.number(value, 2) : '-'
            },
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              let record: any = dRec.record;
              let label = record['salemanFullName'] || '';
              let isPreviousPeriodTotal = label === 'Previous Period Total';
              let isCurrentPeriodTotal = label === 'Current Period Total';
              let isTotalRow = isPreviousPeriodTotal || isCurrentPeriodTotal;

              let textColor = '';
              let fontWeight = 'normal';
              if (isPreviousPeriodTotal) {
                textColor = 'text-info';
                fontWeight = 'bold';
              } else if (isCurrentPeriodTotal) {
                textColor = 'text-primary';
                fontWeight = 'bold';
              }

              let value = record['totalHawbCbmNM'];
              let val = value && !isNaN(value) ? util.text.formater.number(value, 2) : '-';
              return <div className={`flex-hbox justify-content-end text-end w-100 ${textColor}`} style={{ fontWeight }}>{val}</div>
            }
          },
          {
            name: 'totalHawbCwNM', label: T(`Air (KGS)`), width: 100,
            fieldDataGetter(record) {
              let value = record['totalHawbCwNM'];
              return value && !isNaN(value) ? util.text.formater.number(value, 2) : '-'
            },
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              let record: any = dRec.record;
              let label = record['salemanFullName'] || '';
              let isPreviousPeriodTotal = label === 'Previous Period Total';
              let isCurrentPeriodTotal = label === 'Current Period Total';
              let isTotalRow = isPreviousPeriodTotal || isCurrentPeriodTotal;

              let textColor = '';
              let fontWeight = 'normal';
              if (isPreviousPeriodTotal) {
                textColor = 'text-info';
                fontWeight = 'bold';
              } else if (isCurrentPeriodTotal) {
                textColor = 'text-primary';
                fontWeight = 'bold';
              }

              let value = record['totalHawbCwNM'];
              let val = value && !isNaN(value) ? util.text.formater.number(value, 2) : '-';
              return <div className={`flex-hbox justify-content-end text-end w-100 ${textColor}`} style={{ fontWeight }}>{val}</div>
            }
          },
          // {
          //   name: 'totalRevenueNMInUSD', label: T(`Revenue (USD)`), width: 150,
          //   fieldDataGetter(record) {
          //     let value = record['totalRevenueNMInUSD'];
          //     return value && !isNaN(value) ? util.text.formater.number(value, 0) : '-'
          //   },
          //   customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
          //     let record: any = dRec.record;
          //     let label = record['salemanFullName'] || '';
          //     let isPreviousPeriodTotal = label === 'Previous Period Total';
          //     let isCurrentPeriodTotal = label === 'Current Period Total';
          //     let isTotalRow = isPreviousPeriodTotal || isCurrentPeriodTotal;

          //     let textColor = '';
          //     let fontWeight = 'normal';
          //     if (isPreviousPeriodTotal) {
          //       textColor = 'text-info';
          //       fontWeight = 'bold';
          //     } else if (isCurrentPeriodTotal) {
          //       textColor = 'text-primary';
          //       fontWeight = 'bold';
          //     }

          //     let value = record['totalRevenueNMInUSD'];
          //     let val = value && !isNaN(value) ? util.text.formater.number(value, 0) : '-';
          //     return <div className={`flex-hbox justify-content-end text-end w-100 ${textColor}`} style={{ fontWeight }}>{val}</div>
          //   }
          // },
          // {
          //   name: 'totalProfitNMInUSD', label: T(`G/P (USD)`), width: 150,
          //   fieldDataGetter(record) {
          //     let value = record['totalProfitNMInUSD'];
          //     return value && !isNaN(value) ? util.text.formater.number(value, 0) : '-'
          //   },
          //   customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
          //     let record: any = dRec.record;
          //     let label = record['salemanFullName'] || '';
          //     let isPreviousPeriodTotal = label === 'Previous Period Total';
          //     let isCurrentPeriodTotal = label === 'Current Period Total';
          //     let isTotalRow = isPreviousPeriodTotal || isCurrentPeriodTotal;

          //     let textColor = '';
          //     let fontWeight = 'normal';
          //     if (isPreviousPeriodTotal) {
          //       textColor = 'text-info';
          //       fontWeight = 'bold';
          //     } else if (isCurrentPeriodTotal) {
          //       textColor = 'text-primary';
          //       fontWeight = 'bold';
          //     }

          //     let value = record['totalProfitNMInUSD'];
          //     let val = value && !isNaN(value) ? util.text.formater.number(value, 0) : '-';
          //     return <div className={`flex-hbox justify-content-end text-end w-100 ${textColor}`} style={{ fontWeight }}>{val}</div>
          //   }
          // },
          // {
          //   name: 'totalRevenueNMInVND', label: T(`Revenue (VND)`), width: 150,
          //   fieldDataGetter(record) {
          //     let value = record['totalRevenueNMInVND'];
          //     return value && !isNaN(value) ? util.text.formater.number(value, 0) : '-'
          //   },
          //   customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
          //     let record: any = dRec.record;
          //     let label = record['salemanFullName'] || '';
          //     let isPreviousPeriodTotal = label === 'Previous Period Total';
          //     let isCurrentPeriodTotal = label === 'Current Period Total';
          //     let isTotalRow = isPreviousPeriodTotal || isCurrentPeriodTotal;

          //     let textColor = '';
          //     let fontWeight = 'normal';
          //     if (isPreviousPeriodTotal) {
          //       textColor = 'text-info';
          //       fontWeight = 'bold';
          //     } else if (isCurrentPeriodTotal) {
          //       textColor = 'text-primary';
          //       fontWeight = 'bold';
          //     }

          //     let value = record['totalRevenueNMInVND'];
          //     let val = value && !isNaN(value) ? util.text.formater.number(value, 0) : '-';
          //     return <div className={`flex-hbox justify-content-end text-end w-100 ${textColor}`} style={{ fontWeight }}>{val}</div>
          //   }
          // },
          // {
          //   name: 'totalProfitNMInVND', label: T(`G/P (VND)`), width: 150,
          //   fieldDataGetter(record) {
          //     let value = record['totalProfitNMInVND'];
          //     return value && !isNaN(value) ? util.text.formater.number(value, 0) : '-'
          //   },
          //   customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
          //     let record: any = dRec.record;
          //     let label = record['salemanFullName'] || '';
          //     let isPreviousPeriodTotal = label === 'Previous Period Total';
          //     let isCurrentPeriodTotal = label === 'Current Period Total';
          //     let isTotalRow = isPreviousPeriodTotal || isCurrentPeriodTotal;

          //     let textColor = '';
          //     let fontWeight = 'normal';
          //     if (isPreviousPeriodTotal) {
          //       textColor = 'text-info';
          //       fontWeight = 'bold';
          //     } else if (isCurrentPeriodTotal) {
          //       textColor = 'text-primary';
          //       fontWeight = 'bold';
          //     }

          //     let value = record['totalProfitNMInVND'];
          //     let val = value && !isNaN(value) ? util.text.formater.number(value, 0) : '-';
          //     return <div className={`flex-hbox justify-content-end text-end w-100 ${textColor}`} style={{ fontWeight }}>{val}</div>
          //   }
          // },
          {
            name: 'totalJobCountFH', label: T('Shipment'), width: 100,
            fieldDataGetter(record) {
              return record['totalJobCountFH'] || '-';
            },
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              let record: any = dRec.record;
              let label = record['salemanFullName'] || '';
              let isPreviousPeriodTotal = label === 'Previous Period Total';
              let isCurrentPeriodTotal = label === 'Current Period Total';
              let isTotalRow = isPreviousPeriodTotal || isCurrentPeriodTotal;

              let textColor = '';
              let fontWeight = 'normal';
              if (isPreviousPeriodTotal) {
                textColor = 'text-info';
                fontWeight = 'bold';
              } else if (isCurrentPeriodTotal) {
                textColor = 'text-primary';
                fontWeight = 'bold';
              }

              let val = record['totalJobCountFH'] || '-';
              return <div className={`flex-hbox justify-content-end text-end w-100 ${textColor}`} style={{ fontWeight }}>{val}</div>
            }
          },
          {
            name: 'totalTeuFH', label: T('FCL (TEUs)'), width: 100,
            fieldDataGetter(record) {
              return record['totalTeuFH'] || '-';
            },
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              let record: any = dRec.record;
              let label = record['salemanFullName'] || '';
              let isPreviousPeriodTotal = label === 'Previous Period Total';
              let isCurrentPeriodTotal = label === 'Current Period Total';
              let isTotalRow = isPreviousPeriodTotal || isCurrentPeriodTotal;

              let textColor = '';
              let fontWeight = 'normal';
              if (isPreviousPeriodTotal) {
                textColor = 'text-info';
                fontWeight = 'bold';
              } else if (isCurrentPeriodTotal) {
                textColor = 'text-primary';
                fontWeight = 'bold';
              }

              let val = record['totalTeuFH'] || '-';
              return <div className={`flex-hbox justify-content-end text-end w-100 ${textColor}`} style={{ fontWeight }}>{val}</div>
            }
          },
          {
            name: 'totalHawbCbmFH', label: T(`LCL (CBM)`), width: 100,
            fieldDataGetter(record) {
              let value = record['totalHawbCbmFH'];
              return value && !isNaN(value) ? util.text.formater.number(value, 2) : '-'
            },
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              let record: any = dRec.record;
              let label = record['salemanFullName'] || '';
              let isPreviousPeriodTotal = label === 'Previous Period Total';
              let isCurrentPeriodTotal = label === 'Current Period Total';
              let isTotalRow = isPreviousPeriodTotal || isCurrentPeriodTotal;

              let textColor = '';
              let fontWeight = 'normal';
              if (isPreviousPeriodTotal) {
                textColor = 'text-info';
                fontWeight = 'bold';
              } else if (isCurrentPeriodTotal) {
                textColor = 'text-primary';
                fontWeight = 'bold';
              }

              let value = record['totalHawbCbmFH'];
              let val = value && !isNaN(value) ? util.text.formater.number(value, 2) : '-';
              return <div className={`flex-hbox justify-content-end text-end w-100 ${textColor}`} style={{ fontWeight }}>{val}</div>
            }
          },
          {
            name: 'totalHawbCwFH', label: T(`Air (KGS)`), width: 100,
            fieldDataGetter(record) {
              let value = record['totalHawbCwFH'];
              return value && !isNaN(value) ? util.text.formater.number(value, 2) : '-'
            },
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              let record: any = dRec.record;
              let label = record['salemanFullName'] || '';
              let isPreviousPeriodTotal = label === 'Previous Period Total';
              let isCurrentPeriodTotal = label === 'Current Period Total';
              let isTotalRow = isPreviousPeriodTotal || isCurrentPeriodTotal;

              let textColor = '';
              let fontWeight = 'normal';
              if (isPreviousPeriodTotal) {
                textColor = 'text-info';
                fontWeight = 'bold';
              } else if (isCurrentPeriodTotal) {
                textColor = 'text-primary';
                fontWeight = 'bold';
              }

              let value = record['totalHawbCwFH'];
              let val = value && !isNaN(value) ? util.text.formater.number(value, 2) : '-';
              return <div className={`flex-hbox justify-content-end text-end w-100 ${textColor}`} style={{ fontWeight }}>{val}</div>
            }
          },
          // {
          //   name: 'totalRevenueFHInUSD', label: T(`Revenue (USD)`), width: 150,
          //   fieldDataGetter(record) {
          //     let value = record['totalRevenueFHInUSD'];
          //     return value && !isNaN(value) ? util.text.formater.number(value, 0) : '-'
          //   },
          //   customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
          //     let record: any = dRec.record;
          //     let label = record['salemanFullName'] || '';
          //     let isPreviousPeriodTotal = label === 'Previous Period Total';
          //     let isCurrentPeriodTotal = label === 'Current Period Total';
          //     let isTotalRow = isPreviousPeriodTotal || isCurrentPeriodTotal;

          //     let textColor = '';
          //     let fontWeight = 'normal';
          //     if (isPreviousPeriodTotal) {
          //       textColor = 'text-info';
          //       fontWeight = 'bold';
          //     } else if (isCurrentPeriodTotal) {
          //       textColor = 'text-primary';
          //       fontWeight = 'bold';
          //     }

          //     let value = record['totalRevenueFHInUSD'];
          //     let val = value && !isNaN(value) ? util.text.formater.number(value, 0) : '-';
          //     return <div className={`flex-hbox justify-content-end text-end w-100 ${textColor}`} style={{ fontWeight }}>{val}</div>
          //   }
          // },
          // {
          //   name: 'totalProfitFHInUSD', label: T(`G/P (USD)`), width: 150,
          //   fieldDataGetter(record) {
          //     let value = record['totalProfitFHInUSD'];
          //     return value && !isNaN(value) ? util.text.formater.number(value, 0) : '-'
          //   },
          //   customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
          //     let record: any = dRec.record;
          //     let label = record['salemanFullName'] || '';
          //     let isPreviousPeriodTotal = label === 'Previous Period Total';
          //     let isCurrentPeriodTotal = label === 'Current Period Total';
          //     let isTotalRow = isPreviousPeriodTotal || isCurrentPeriodTotal;

          //     let textColor = '';
          //     let fontWeight = 'normal';
          //     if (isPreviousPeriodTotal) {
          //       textColor = 'text-info';
          //       fontWeight = 'bold';
          //     } else if (isCurrentPeriodTotal) {
          //       textColor = 'text-primary';
          //       fontWeight = 'bold';
          //     }

          //     let value = record['totalProfitFHInUSD'];
          //     let val = value && !isNaN(value) ? util.text.formater.number(value, 0) : '-';
          //     return <div className={`flex-hbox justify-content-end text-end w-100 ${textColor}`} style={{ fontWeight }}>{val}</div>
          //   }
          // },
          // {
          //   name: 'totalRevenueFHInVND', label: T(`Revenue (VND)`), width: 150,
          //   fieldDataGetter(record) {
          //     let value = record['totalRevenueFHInVND'];
          //     return value && !isNaN(value) ? util.text.formater.number(value, 0) : '-'
          //   },
          //   customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
          //     let record: any = dRec.record;
          //     let label = record['salemanFullName'] || '';
          //     let isPreviousPeriodTotal = label === 'Previous Period Total';
          //     let isCurrentPeriodTotal = label === 'Current Period Total';
          //     let isTotalRow = isPreviousPeriodTotal || isCurrentPeriodTotal;

          //     let textColor = '';
          //     let fontWeight = 'normal';
          //     if (isPreviousPeriodTotal) {
          //       textColor = 'text-info';
          //       fontWeight = 'bold';
          //     } else if (isCurrentPeriodTotal) {
          //       textColor = 'text-primary';
          //       fontWeight = 'bold';
          //     }

          //     let value = record['totalRevenueFHInVND'];
          //     let val = value && !isNaN(value) ? util.text.formater.number(value, 0) : '-';
          //     return <div className={`flex-hbox justify-content-end text-end w-100 ${textColor}`} style={{ fontWeight }}>{val}</div>
          //   }
          // },
          // {
          //   name: 'totalProfitFHInVND', label: T(`G/P (VND)`), width: 150,
          //   fieldDataGetter(record) {
          //     let value = record['totalProfitFHInVND'];
          //     return value && !isNaN(value) ? util.text.formater.number(value, 0) : '-'
          //   },
          //   customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
          //     let record: any = dRec.record;
          //     let label = record['salemanFullName'] || '';
          //     let isPreviousPeriodTotal = label === 'Previous Period Total';
          //     let isCurrentPeriodTotal = label === 'Current Period Total';
          //     let isTotalRow = isPreviousPeriodTotal || isCurrentPeriodTotal;

          //     let textColor = '';
          //     let fontWeight = 'normal';
          //     if (isPreviousPeriodTotal) {
          //       textColor = 'text-info';
          //       fontWeight = 'bold';
          //     } else if (isCurrentPeriodTotal) {
          //       textColor = 'text-primary';
          //       fontWeight = 'bold';
          //     }

          //     let value = record['totalProfitFHInVND'];
          //     let val = value && !isNaN(value) ? util.text.formater.number(value, 0) : '-';
          //     return <div className={`flex-hbox justify-content-end text-end w-100 ${textColor}`} style={{ fontWeight }}>{val}</div>
          //   }
          // },

        ],
        fieldGroups: {
          "Freehand": {
            label: T('Freehand'),
            fields: [
              'totalJobCountFH', 'totalTeuFH', 'cont20FH', 'cont40FH', 'cont45FH', 'totalHawbCbmFH', 'totalHawbCwFH',
              'totalRevenueFHInUSD', 'totalProfitFHInUSD', 'totalRevenueFHInVND', 'totalProfitFHInVND']
          },
          "Nominated": {
            label: T('Nominated'),
            fields: [
              'totalJobCountNM', 'totalTeuNM', 'cont20NM', 'cont40NM', 'cont45NM', 'totalHawbCbmNM', 'totalHawbGwNM',
              'totalHawbCwNM', 'totalRevenueNMInUSD', 'totalProfitNMInUSD', 'totalRevenueNMInVND', 'totalProfitNMInVND']
          },
        },
      },
      toolbar: { hide: true },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table',
            header: {
              createRecords: (_ctx: grid.VGridContext) => {
                let records = _ctx.model.getFilterRecords();
                const { previousRecords } = this.props;

                // Calculate current period totals
                let currentTotalJobCountNM = 0;
                let currentTotalTeuNM = 0;
                let currentTotalHawbCbmNM = 0;
                let currentTotalHawbCwNM = 0;
                let currentTotalRevenueNMInUSD = 0;
                let currentTotalProfitNMInUSD = 0;
                let currentTotalRevenueNMInVND = 0;
                let currentTotalProfitNMInVND = 0;

                let currentTotalJobCountFH = 0;
                let currentTotalTeuFH = 0;
                let currentTotalHawbCbmFH = 0;
                let currentTotalHawbCwFH = 0;
                let currentTotalRevenueFHInUSD = 0;
                let currentTotalProfitFHInUSD = 0;
                let currentTotalRevenueFHInVND = 0;
                let currentTotalProfitFHInVND = 0;

                records.forEach(record => {
                  currentTotalJobCountNM += Number(record.totalJobCountNM || 0);
                  currentTotalTeuNM += Number(record.totalTeuNM || 0);
                  currentTotalHawbCbmNM += Number(record.totalHawbCbmNM || 0);
                  currentTotalHawbCwNM += Number(record.totalHawbCwNM || 0);
                  currentTotalRevenueNMInUSD += Number(record.totalRevenueNMInUSD || 0);
                  currentTotalProfitNMInUSD += Number(record.totalProfitNMInUSD || 0);
                  currentTotalRevenueNMInVND += Number(record.totalRevenueNMInVND || 0);
                  currentTotalProfitNMInVND += Number(record.totalProfitNMInVND || 0);

                  currentTotalJobCountFH += Number(record.totalJobCountFH || 0);
                  currentTotalTeuFH += Number(record.totalTeuFH || 0);
                  currentTotalHawbCbmFH += Number(record.totalHawbCbmFH || 0);
                  currentTotalHawbCwFH += Number(record.totalHawbCwFH || 0);
                  currentTotalRevenueFHInUSD += Number(record.totalRevenueFHInUSD || 0);
                  currentTotalProfitFHInUSD += Number(record.totalProfitFHInUSD || 0);
                  currentTotalRevenueFHInVND += Number(record.totalRevenueFHInVND || 0);
                  currentTotalProfitFHInVND += Number(record.totalProfitFHInVND || 0);
                });

                // Calculate previous period totals
                let previousTotalJobCountNM = 0;
                let previousTotalTeuNM = 0;
                let previousTotalHawbCbmNM = 0;
                let previousTotalHawbCwNM = 0;
                let previousTotalRevenueNMInUSD = 0;
                let previousTotalProfitNMInUSD = 0;
                let previousTotalRevenueNMInVND = 0;
                let previousTotalProfitNMInVND = 0;

                let previousTotalJobCountFH = 0;
                let previousTotalTeuFH = 0;
                let previousTotalHawbCbmFH = 0;
                let previousTotalHawbCwFH = 0;
                let previousTotalRevenueFHInUSD = 0;
                let previousTotalProfitFHInUSD = 0;
                let previousTotalRevenueFHInVND = 0;
                let previousTotalProfitFHInVND = 0;

                if (previousRecords && previousRecords.length > 0) {
                  // Previous records are already processed, use them directly
                  previousRecords.forEach(record => {
                    previousTotalJobCountNM += Number(record.totalJobCountNM || 0);
                    previousTotalTeuNM += Number(record.totalTeuNM || 0);
                    previousTotalHawbCbmNM += Number(record.totalHawbCbmNM || 0);
                    previousTotalHawbCwNM += Number(record.totalHawbCwNM || 0);
                    previousTotalRevenueNMInUSD += Number(record.totalRevenueNMInUSD || 0);
                    previousTotalProfitNMInUSD += Number(record.totalProfitNMInUSD || 0);
                    previousTotalRevenueNMInVND += Number(record.totalRevenueNMInVND || 0);
                    previousTotalProfitNMInVND += Number(record.totalProfitNMInVND || 0);

                    previousTotalJobCountFH += Number(record.totalJobCountFH || 0);
                    previousTotalTeuFH += Number(record.totalTeuFH || 0);
                    previousTotalHawbCbmFH += Number(record.totalHawbCbmFH || 0);
                    previousTotalHawbCwFH += Number(record.totalHawbCwFH || 0);
                    previousTotalRevenueFHInUSD += Number(record.totalRevenueFHInUSD || 0);
                    previousTotalProfitFHInUSD += Number(record.totalProfitFHInUSD || 0);
                    previousTotalRevenueFHInVND += Number(record.totalRevenueFHInVND || 0);
                    previousTotalProfitFHInVND += Number(record.totalProfitFHInVND || 0);
                  });
                }

                let summaryRows = [];

                // Only add Previous Period Total if previousRecords is not empty
                if (previousRecords && previousRecords.length > 0) {
                  summaryRows.push({
                    companyBranchCode: 'Previous Period Total',
                    totalJobCountNM: previousTotalJobCountNM,
                    totalTeuNM: previousTotalTeuNM,
                    totalHawbCbmNM: previousTotalHawbCbmNM,
                    totalHawbCwNM: previousTotalHawbCwNM,
                    totalRevenueNMInUSD: previousTotalRevenueNMInUSD,
                    totalProfitNMInUSD: previousTotalProfitNMInUSD,
                    totalRevenueNMInVND: previousTotalRevenueNMInVND,
                    totalProfitNMInVND: previousTotalProfitNMInVND,
                    totalJobCountFH: previousTotalJobCountFH,
                    totalTeuFH: previousTotalTeuFH,
                    totalHawbCbmFH: previousTotalHawbCbmFH,
                    totalHawbCwFH: previousTotalHawbCwFH,
                    totalRevenueFHInUSD: previousTotalRevenueFHInUSD,
                    totalProfitFHInUSD: previousTotalProfitFHInUSD,
                    totalRevenueFHInVND: previousTotalRevenueFHInVND,
                    totalProfitFHInVND: previousTotalProfitFHInVND,
                  });
                }

                // Always add Current Period Total
                summaryRows.push({
                  companyBranchCode: 'Current Period Total',
                  totalJobCountNM: currentTotalJobCountNM,
                  totalTeuNM: currentTotalTeuNM,
                  totalHawbCbmNM: currentTotalHawbCbmNM,
                  totalHawbCwNM: currentTotalHawbCwNM,
                  totalRevenueNMInUSD: currentTotalRevenueNMInUSD,
                  totalProfitNMInUSD: currentTotalProfitNMInUSD,
                  totalRevenueNMInVND: currentTotalRevenueNMInVND,
                  totalProfitNMInVND: currentTotalProfitNMInVND,
                  totalJobCountFH: currentTotalJobCountFH,
                  totalTeuFH: currentTotalTeuFH,
                  totalHawbCbmFH: currentTotalHawbCbmFH,
                  totalHawbCwFH: currentTotalHawbCwFH,
                  totalRevenueFHInUSD: currentTotalRevenueFHInUSD,
                  totalProfitFHInUSD: currentTotalProfitFHInUSD,
                  totalRevenueFHInVND: currentTotalRevenueFHInVND,
                  totalProfitFHInVND: currentTotalProfitFHInVND,
                });

                return summaryRows;
              }
            }
          }
        },
      },
    };
    return responsiveGridConfig(config);
  }

  onDefaultSelect(dRecord: grid.DisplayRecord) {
  }

  // Color palette for consistent branch colors across charts
  getBranchColor(branchName: string): string {
    const colorPalette = [
      '#0088FE', '#00C49F', '#FFBB28', '#FF8042',
      '#8884d8', '#82ca9d', '#ffc658', '#8dd1e1',
      // '#a4de6c', '#d0ed57', '#FF6B6B', '#4ECDC4',
      // '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD',
      // '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
    ];

    // Create a hash from branch name to ensure consistent color assignment
    let hash = 0;
    for (let i = 0; i < branchName.length; i++) {
      const char = branchName.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }

    // Use absolute value to ensure positive index
    const colorIndex = Math.abs(hash) % colorPalette.length;
    return colorPalette[colorIndex];
  }

  calculateTeuDistribution(records: any[]): any[] {
    // Filter out total rows and only include branches with actual TEU data
    const branchRecords = records.filter(record =>
      record.companyBranchCode &&
      record.companyBranchCode !== 'Previous Period Total' &&
      record.companyBranchCode !== 'Current Period Total' &&
      (Number(record.totalTeuFH || 0) > 0 || Number(record.totalTeuNM || 0) > 0)
    );

    // Calculate total TEU across all branches (FH + NM)
    const totalTeu = branchRecords.reduce((sum, record) =>
      sum + Number(record.totalTeuFH || 0) + Number(record.totalTeuNM || 0), 0);

    // Return data formatted for pie chart
    return branchRecords.map(record => {
      const teuFH = Number(record.totalTeuFH || 0);
      const teuNM = Number(record.totalTeuNM || 0);
      const totalTeuValue = teuFH + teuNM;
      const percent = totalTeu > 0 ? Math.round((totalTeuValue / totalTeu) * 100) : 0;
      const branchName = record.companyBranchCode || record.branchName || 'Unknown';
      return {
        branch: branchName,
        totalTeuFH: teuFH,
        totalTeuNM: teuNM,
        totalTeu: totalTeuValue,
        percent: percent,
        color: this.getBranchColor(branchName),
        tooltipText: `${branchName}: ${totalTeuValue} TEUs (FH: ${teuFH}, NM: ${teuNM}) - ${percent}%`
      };
    }).sort((a, b) => b.totalTeu - a.totalTeu); // Sort by total TEU value descending
  }

  calculateCbmDistribution(records: any[]): any[] {
    // Filter out total rows and only include branches with actual CBM data
    const branchRecords = records.filter(record =>
      record.companyBranchCode &&
      record.companyBranchCode !== 'Previous Period Total' &&
      record.companyBranchCode !== 'Current Period Total' &&
      (Number(record.totalHawbCbmFH || 0) > 0 || Number(record.totalHawbCbmNM || 0) > 0)
    );

    // Calculate total CBM across all branches (FH + NM)
    const totalCbm = branchRecords.reduce((sum, record) =>
      sum + Number(record.totalHawbCbmFH || 0) + Number(record.totalHawbCbmNM || 0), 0);

    // Return data formatted for pie chart
    return branchRecords.map(record => {
      const cbmFH = Number(record.totalHawbCbmFH || 0);
      const cbmNM = Number(record.totalHawbCbmNM || 0);
      const totalCbmValue = cbmFH + cbmNM;
      const percent = totalCbm > 0 ? Math.round((totalCbmValue / totalCbm) * 100) : 0;
      const branchName = record.companyBranchCode || record.branchName || 'Unknown';
      return {
        branch: branchName,
        totalHawbCbmFH: cbmFH,
        totalHawbCbmNM: cbmNM,
        totalCbm: totalCbmValue,
        percent: percent,
        color: this.getBranchColor(branchName),
        tooltipText: `${branchName}: ${util.text.formater.number(totalCbmValue, 2)} CBM (FH: ${util.text.formater.number(cbmFH, 2)}, NM: ${util.text.formater.number(cbmNM, 2)}) - ${percent}%`
      };
    }).sort((a, b) => b.totalCbm - a.totalCbm); // Sort by total CBM value descending
  }

  calculateKgsDistribution(records: any[]): any[] {
    // Filter out total rows and only include branches with actual KGS data
    const branchRecords = records.filter(record =>
      record.companyBranchCode &&
      record.companyBranchCode !== 'Previous Period Total' &&
      record.companyBranchCode !== 'Current Period Total' &&
      (Number(record.totalHawbCwFH || 0) > 0 || Number(record.totalHawbCwNM || 0) > 0)
    );

    // Calculate total KGS across all branches (FH + NM)
    const totalKgs = branchRecords.reduce((sum, record) =>
      sum + Number(record.totalHawbCwFH || 0) + Number(record.totalHawbCwNM || 0), 0);

    // Return data formatted for pie chart
    return branchRecords.map(record => {
      const kgsFH = Number(record.totalHawbCwFH || 0);
      const kgsNM = Number(record.totalHawbCwNM || 0);
      const totalKgsValue = kgsFH + kgsNM;
      const percent = totalKgs > 0 ? Math.round((totalKgsValue / totalKgs) * 100) : 0;
      const branchName = record.companyBranchCode || record.branchName || 'Unknown';
      return {
        branch: branchName,
        totalHawbCwFH: kgsFH,
        totalHawbCwNM: kgsNM,
        totalKgs: totalKgsValue,
        percent: percent,
        color: this.getBranchColor(branchName),
        tooltipText: `${branchName}: ${util.text.formater.number(totalKgsValue, 2)} KGS (FH: ${util.text.formater.number(kgsFH, 2)}, NM: ${util.text.formater.number(kgsNM, 2)}) - ${percent}%`
      };
    }).sort((a, b) => b.totalKgs - a.totalKgs); // Sort by total KGS value descending
  }

  calculateRevenueVsProfitMarginData(records: any[]): any[] {
    // Filter out total rows and only include branches with actual revenue/profit data
    const branchRecords = records.filter(record =>
      record.companyBranchCode &&
      record.companyBranchCode !== 'Previous Period Total' &&
      record.companyBranchCode !== 'Current Period Total'
    );

    // Calculate revenue vs profit margin data for each branch
    return branchRecords.map(record => {
      const branchName = record.companyBranchCode || 'Unknown';

      // Calculate total revenue and profit (NM + FH) in USD
      const totalRevenueUSD = Number(record.totalRevenueNMInUSD || 0) + Number(record.totalRevenueFHInUSD || 0);
      const totalProfitUSD = Number(record.totalProfitNMInUSD || 0) + Number(record.totalProfitFHInUSD || 0);

      // Calculate profit margin percentage
      const profitMarginPercent = totalRevenueUSD > 0 ? ((totalProfitUSD / totalRevenueUSD) * 100) : 0;

      return {
        branch: branchName,
        revenue: totalRevenueUSD,
        profitMargin: parseFloat(profitMarginPercent.toFixed(1)),
        revenueFormatted: util.text.formater.number(totalRevenueUSD, 0),
        profitFormatted: util.text.formater.number(totalProfitUSD, 0),
        profitMarginFormatted: `${profitMarginPercent.toFixed(1)}%`,
        color: this.getBranchColor(branchName),
        tooltipText: `${branchName}: Revenue $${util.text.formater.number(totalRevenueUSD, 0)}, Margin ${profitMarginPercent.toFixed(1)}%`
      };
    }).filter(item => item.revenue > 0) // Only include branches with revenue
      .sort((a, b) => b.revenue - a.revenue); // Sort by revenue descending
  }

  calculateRevenueVsProfitData(records: any[]): any[] {
    // Filter out total rows and only include branches with actual revenue/profit data
    const branchRecords = records.filter(record =>
      record.companyBranchCode &&
      record.companyBranchCode !== 'Previous Period Total' &&
      record.companyBranchCode !== 'Current Period Total'
    );

    // Calculate revenue vs profit data for each branch
    return branchRecords.map(record => {
      const branchName = record.companyBranchCode || 'Unknown';

      // Calculate total revenue and profit (NM + FH) in USD
      const totalRevenueUSD = Number(record.totalRevenueNMInUSD || 0) + Number(record.totalRevenueFHInUSD || 0);
      const totalProfitUSD = Number(record.totalProfitNMInUSD || 0) + Number(record.totalProfitFHInUSD || 0);

      // Calculate profit margin percentage
      const profitMargin = totalRevenueUSD > 0 ? ((totalProfitUSD / totalRevenueUSD) * 100).toFixed(1) : '0.0';

      return {
        branch: branchName,
        revenue: totalRevenueUSD,
        profit: totalProfitUSD,
        profitMargin: parseFloat(profitMargin),
        revenueFormatted: util.text.formater.number(totalRevenueUSD, 0),
        profitFormatted: util.text.formater.number(totalProfitUSD, 0),
        color: this.getBranchColor(branchName),
        tooltipText: `${branchName}: Revenue $${util.text.formater.number(totalRevenueUSD, 0)}, Profit $${util.text.formater.number(totalProfitUSD, 0)} (${profitMargin}%)`
      };
    }).filter(item => item.revenue > 0 || item.profit > 0) // Only include branches with revenue or profit
      .sort((a, b) => b.revenue - a.revenue); // Sort by revenue descending
  }

  calculateNominatedSummary(records: any[]): any[] {
    // Filter out total rows
    const branchRecords = records.filter(record =>
      record.companyBranchCode &&
      record.companyBranchCode !== 'Previous Period Total' &&
      record.companyBranchCode !== 'Current Period Total'
    );

    const branchData = branchRecords.map(record => {
      // Shipment calculations
      const totalJobCount = Number(record.totalJobCountNM || 0) + Number(record.totalJobCountFH || 0);
      const jobCountNMPercent = totalJobCount > 0 ? ((Number(record.totalJobCountNM || 0) / totalJobCount) * 100).toFixed(1) : '0.0';

      // TEU calculations
      const totalTeu = Number(record.totalTeuNM || 0) + Number(record.totalTeuFH || 0);
      const teuNMPercent = totalTeu > 0 ? ((Number(record.totalTeuNM || 0) / totalTeu) * 100).toFixed(1) : '0.0';

      // CBM calculations
      const totalCbm = Number(record.totalHawbCbmNM || 0) + Number(record.totalHawbCbmFH || 0);
      const cbmNMPercent = totalCbm > 0 ? ((Number(record.totalHawbCbmNM || 0) / totalCbm) * 100).toFixed(1) : '0.0';

      // KGS calculations
      const totalKgs = Number(record.totalHawbCwNM || 0) + Number(record.totalHawbCwFH || 0);
      const kgsNMPercent = totalKgs > 0 ? ((Number(record.totalHawbCwNM || 0) / totalKgs) * 100).toFixed(1) : '0.0';

      // Revenue USD calculations
      const totalRevenueUSD = Number(record.totalRevenueNMInUSD || 0) + Number(record.totalRevenueFHInUSD || 0);
      const revenueUSDNMPercent = totalRevenueUSD > 0 ? ((Number(record.totalRevenueNMInUSD || 0) / totalRevenueUSD) * 100).toFixed(1) : '0.0';

      // Profit USD calculations
      const totalProfitUSD = Number(record.totalProfitNMInUSD || 0) + Number(record.totalProfitFHInUSD || 0);
      const profitUSDNMPercent = totalProfitUSD > 0 ? ((Number(record.totalProfitNMInUSD || 0) / totalProfitUSD) * 100).toFixed(1) : '0.0';

      // Revenue VND calculations
      const totalRevenueVND = Number(record.totalRevenueNMInVND || 0) + Number(record.totalRevenueFHInVND || 0);
      const revenueVNDNMPercent = totalRevenueVND > 0 ? ((Number(record.totalRevenueNMInVND || 0) / totalRevenueVND) * 100).toFixed(1) : '0.0';

      // Profit VND calculations
      const totalProfitVND = Number(record.totalProfitNMInVND || 0) + Number(record.totalProfitFHInVND || 0);
      const profitVNDNMPercent = totalProfitVND > 0 ? ((Number(record.totalProfitNMInVND || 0) / totalProfitVND) * 100).toFixed(1) : '0.0';

      return {
        branch: record.companyBranchCode || 'Unknown',
        shipmentNM: record.totalJobCountNM || 0,
        shipmentTotal: totalJobCount,
        shipmentPercent: `${jobCountNMPercent}%`,
        teuNM: record.totalTeuNM || 0,
        teuTotal: totalTeu,
        teuPercent: `${teuNMPercent}%`,
        cbmNM: Number(record.totalHawbCbmNM || 0).toFixed(2),
        cbmTotal: totalCbm.toFixed(2),
        cbmPercent: `${cbmNMPercent}%`,
        kgsNM: Number(record.totalHawbCwNM || 0).toFixed(2),
        kgsTotal: totalKgs.toFixed(2),
        kgsPercent: `${kgsNMPercent}%`,
        revenueUSDNM: util.text.formater.number(Number(record.totalRevenueNMInUSD || 0), 0),
        revenueUSDTotal: util.text.formater.number(totalRevenueUSD, 0),
        revenueUSDPercent: `${revenueUSDNMPercent}%`,
        profitUSDNM: util.text.formater.number(Number(record.totalProfitNMInUSD || 0), 0),
        profitUSDTotal: util.text.formater.number(totalProfitUSD, 0),
        profitUSDPercent: `${profitUSDNMPercent}%`,
        revenueVNDNM: util.text.formater.number(Number(record.totalRevenueNMInVND || 0), 0),
        revenueVNDTotal: util.text.formater.number(totalRevenueVND, 0),
        revenueVNDPercent: `${revenueVNDNMPercent}%`,
        profitVNDNM: util.text.formater.number(Number(record.totalProfitNMInVND || 0), 0),
        profitVNDTotal: util.text.formater.number(totalProfitVND, 0),
        profitVNDPercent: `${profitVNDNMPercent}%`
      };
    });

    // Calculate grand totals for all branches
    let grandTotalShipmentNM = 0;
    let grandTotalShipmentTotal = 0;
    let grandTotalTeuNM = 0;
    let grandTotalTeuTotal = 0;
    let grandTotalCbmNM = 0;
    let grandTotalCbmTotal = 0;
    let grandTotalKgsNM = 0;
    let grandTotalKgsTotal = 0;
    let grandTotalRevenueUSDNM = 0;
    let grandTotalRevenueUSDTotal = 0;
    let grandTotalProfitUSDNM = 0;
    let grandTotalProfitUSDTotal = 0;
    let grandTotalRevenueVNDNM = 0;
    let grandTotalRevenueVNDTotal = 0;
    let grandTotalProfitVNDNM = 0;
    let grandTotalProfitVNDTotal = 0;

    branchRecords.forEach(record => {
      grandTotalShipmentNM += Number(record.totalJobCountNM || 0);
      grandTotalShipmentTotal += Number(record.totalJobCountNM || 0) + Number(record.totalJobCountFH || 0);
      grandTotalTeuNM += Number(record.totalTeuNM || 0);
      grandTotalTeuTotal += Number(record.totalTeuNM || 0) + Number(record.totalTeuFH || 0);
      grandTotalCbmNM += Number(record.totalHawbCbmNM || 0);
      grandTotalCbmTotal += Number(record.totalHawbCbmNM || 0) + Number(record.totalHawbCbmFH || 0);
      grandTotalKgsNM += Number(record.totalHawbCwNM || 0);
      grandTotalKgsTotal += Number(record.totalHawbCwNM || 0) + Number(record.totalHawbCwFH || 0);
      grandTotalRevenueUSDNM += Number(record.totalRevenueNMInUSD || 0);
      grandTotalRevenueUSDTotal += Number(record.totalRevenueNMInUSD || 0) + Number(record.totalRevenueFHInUSD || 0);
      grandTotalProfitUSDNM += Number(record.totalProfitNMInUSD || 0);
      grandTotalProfitUSDTotal += Number(record.totalProfitNMInUSD || 0) + Number(record.totalProfitFHInUSD || 0);
      grandTotalRevenueVNDNM += Number(record.totalRevenueNMInVND || 0);
      grandTotalRevenueVNDTotal += Number(record.totalRevenueNMInVND || 0) + Number(record.totalRevenueFHInVND || 0);
      grandTotalProfitVNDNM += Number(record.totalProfitNMInVND || 0);
      grandTotalProfitVNDTotal += Number(record.totalProfitNMInVND || 0) + Number(record.totalProfitFHInVND || 0);
    });

    // Calculate grand total percentages
    const grandTotalShipmentPercent = grandTotalShipmentTotal > 0 ? ((grandTotalShipmentNM / grandTotalShipmentTotal) * 100).toFixed(1) : '0.0';
    const grandTotalTeuPercent = grandTotalTeuTotal > 0 ? ((grandTotalTeuNM / grandTotalTeuTotal) * 100).toFixed(1) : '0.0';
    const grandTotalCbmPercent = grandTotalCbmTotal > 0 ? ((grandTotalCbmNM / grandTotalCbmTotal) * 100).toFixed(1) : '0.0';
    const grandTotalKgsPercent = grandTotalKgsTotal > 0 ? ((grandTotalKgsNM / grandTotalKgsTotal) * 100).toFixed(1) : '0.0';
    const grandTotalRevenueUSDPercent = grandTotalRevenueUSDTotal > 0 ? ((grandTotalRevenueUSDNM / grandTotalRevenueUSDTotal) * 100).toFixed(1) : '0.0';
    const grandTotalProfitUSDPercent = grandTotalProfitUSDTotal > 0 ? ((grandTotalProfitUSDNM / grandTotalProfitUSDTotal) * 100).toFixed(1) : '0.0';
    const grandTotalRevenueVNDPercent = grandTotalRevenueVNDTotal > 0 ? ((grandTotalRevenueVNDNM / grandTotalRevenueVNDTotal) * 100).toFixed(1) : '0.0';
    const grandTotalProfitVNDPercent = grandTotalProfitVNDTotal > 0 ? ((grandTotalProfitVNDNM / grandTotalProfitVNDTotal) * 100).toFixed(1) : '0.0';

    // Add grand total row
    const grandTotalRow = {
      branch: 'TOTAL',
      shipmentNM: grandTotalShipmentNM,
      shipmentTotal: grandTotalShipmentTotal,
      shipmentPercent: `${grandTotalShipmentPercent}%`,
      teuNM: grandTotalTeuNM,
      teuTotal: grandTotalTeuTotal,
      teuPercent: `${grandTotalTeuPercent}%`,
      cbmNM: grandTotalCbmNM.toFixed(2),
      cbmTotal: grandTotalCbmTotal.toFixed(2),
      cbmPercent: `${grandTotalCbmPercent}%`,
      kgsNM: grandTotalKgsNM.toFixed(2),
      kgsTotal: grandTotalKgsTotal.toFixed(2),
      kgsPercent: `${grandTotalKgsPercent}%`,
      revenueUSDNM: util.text.formater.number(grandTotalRevenueUSDNM, 0),
      revenueUSDTotal: util.text.formater.number(grandTotalRevenueUSDTotal, 0),
      revenueUSDPercent: `${grandTotalRevenueUSDPercent}%`,
      profitUSDNM: util.text.formater.number(grandTotalProfitUSDNM, 0),
      profitUSDTotal: util.text.formater.number(grandTotalProfitUSDTotal, 0),
      profitUSDPercent: `${grandTotalProfitUSDPercent}%`,
      revenueVNDNM: util.text.formater.number(grandTotalRevenueVNDNM, 0),
      revenueVNDTotal: util.text.formater.number(grandTotalRevenueVNDTotal, 0),
      revenueVNDPercent: `${grandTotalRevenueVNDPercent}%`,
      profitVNDNM: util.text.formater.number(grandTotalProfitVNDNM, 0),
      profitVNDTotal: util.text.formater.number(grandTotalProfitVNDTotal, 0),
      profitVNDPercent: `${grandTotalProfitVNDPercent}%`,
      isGrandTotal: true
    };

    return [...branchData, grandTotalRow];
  }

  calculateFreehandSummary(records: any[]): any[] {
    // Filter out total rows
    const branchRecords = records.filter(record =>
      record.companyBranchCode &&
      record.companyBranchCode !== 'Previous Period Total' &&
      record.companyBranchCode !== 'Current Period Total'
    );

    const branchData = branchRecords.map(record => {
      // Shipment calculations
      const totalJobCount = Number(record.totalJobCountFH || 0) + Number(record.totalJobCountNM || 0);
      const jobCountFHPercent = totalJobCount > 0 ? ((Number(record.totalJobCountFH || 0) / totalJobCount) * 100).toFixed(1) : '0.0';

      // TEU calculations
      const totalTeu = Number(record.totalTeuFH || 0) + Number(record.totalTeuNM || 0);
      const teuFHPercent = totalTeu > 0 ? ((Number(record.totalTeuFH || 0) / totalTeu) * 100).toFixed(1) : '0.0';

      // CBM calculations
      const totalCbm = Number(record.totalHawbCbmFH || 0) + Number(record.totalHawbCbmNM || 0);
      const cbmFHPercent = totalCbm > 0 ? ((Number(record.totalHawbCbmFH || 0) / totalCbm) * 100).toFixed(1) : '0.0';

      // KGS calculations
      const totalKgs = Number(record.totalHawbCwFH || 0) + Number(record.totalHawbCwNM || 0);
      const kgsFHPercent = totalKgs > 0 ? ((Number(record.totalHawbCwFH || 0) / totalKgs) * 100).toFixed(1) : '0.0';

      // Revenue USD calculations
      const totalRevenueUSD = Number(record.totalRevenueFHInUSD || 0) + Number(record.totalRevenueNMInUSD || 0);
      const revenueUSDFHPercent = totalRevenueUSD > 0 ? ((Number(record.totalRevenueFHInUSD || 0) / totalRevenueUSD) * 100).toFixed(1) : '0.0';

      // Profit USD calculations
      const totalProfitUSD = Number(record.totalProfitFHInUSD || 0) + Number(record.totalProfitNMInUSD || 0);
      const profitUSDFHPercent = totalProfitUSD > 0 ? ((Number(record.totalProfitFHInUSD || 0) / totalProfitUSD) * 100).toFixed(1) : '0.0';

      // Revenue VND calculations
      const totalRevenueVND = Number(record.totalRevenueFHInVND || 0) + Number(record.totalRevenueNMInVND || 0);
      const revenueVNDFHPercent = totalRevenueVND > 0 ? ((Number(record.totalRevenueFHInVND || 0) / totalRevenueVND) * 100).toFixed(1) : '0.0';

      // Profit VND calculations
      const totalProfitVND = Number(record.totalProfitFHInVND || 0) + Number(record.totalProfitNMInVND || 0);
      const profitVNDFHPercent = totalProfitVND > 0 ? ((Number(record.totalProfitFHInVND || 0) / totalProfitVND) * 100).toFixed(1) : '0.0';

      return {
        branch: record.companyBranchCode || 'Unknown',
        shipmentFH: record.totalJobCountFH || 0,
        shipmentTotal: totalJobCount,
        shipmentPercent: `${jobCountFHPercent}%`,
        teuFH: record.totalTeuFH || 0,
        teuTotal: totalTeu,
        teuPercent: `${teuFHPercent}%`,
        cbmFH: Number(record.totalHawbCbmFH || 0).toFixed(2),
        cbmTotal: totalCbm.toFixed(2),
        cbmPercent: `${cbmFHPercent}%`,
        kgsFH: Number(record.totalHawbCwFH || 0).toFixed(2),
        kgsTotal: totalKgs.toFixed(2),
        kgsPercent: `${kgsFHPercent}%`,
        revenueUSDFH: util.text.formater.number(Number(record.totalRevenueFHInUSD || 0), 0),
        revenueUSDTotal: util.text.formater.number(totalRevenueUSD, 0),
        revenueUSDPercent: `${revenueUSDFHPercent}%`,
        profitUSDFH: util.text.formater.number(Number(record.totalProfitFHInUSD || 0), 0),
        profitUSDTotal: util.text.formater.number(totalProfitUSD, 0),
        profitUSDPercent: `${profitUSDFHPercent}%`,
        revenueVNDFH: util.text.formater.number(Number(record.totalRevenueFHInVND || 0), 0),
        revenueVNDTotal: util.text.formater.number(totalRevenueVND, 0),
        revenueVNDPercent: `${revenueVNDFHPercent}%`,
        profitVNDFH: util.text.formater.number(Number(record.totalProfitFHInVND || 0), 0),
        profitVNDTotal: util.text.formater.number(totalProfitVND, 0),
        profitVNDPercent: `${profitVNDFHPercent}%`
      };
    });

    // Calculate grand totals for all branches
    let grandTotalShipmentFH = 0;
    let grandTotalShipmentTotal = 0;
    let grandTotalTeuFH = 0;
    let grandTotalTeuTotal = 0;
    let grandTotalCbmFH = 0;
    let grandTotalCbmTotal = 0;
    let grandTotalKgsFH = 0;
    let grandTotalKgsTotal = 0;
    let grandTotalRevenueUSDFH = 0;
    let grandTotalRevenueUSDTotal = 0;
    let grandTotalProfitUSDFH = 0;
    let grandTotalProfitUSDTotal = 0;
    let grandTotalRevenueVNDFH = 0;
    let grandTotalRevenueVNDTotal = 0;
    let grandTotalProfitVNDFH = 0;
    let grandTotalProfitVNDTotal = 0;

    branchRecords.forEach(record => {
      grandTotalShipmentFH += Number(record.totalJobCountFH || 0);
      grandTotalShipmentTotal += Number(record.totalJobCountFH || 0) + Number(record.totalJobCountNM || 0);
      grandTotalTeuFH += Number(record.totalTeuFH || 0);
      grandTotalTeuTotal += Number(record.totalTeuFH || 0) + Number(record.totalTeuNM || 0);
      grandTotalCbmFH += Number(record.totalHawbCbmFH || 0);
      grandTotalCbmTotal += Number(record.totalHawbCbmFH || 0) + Number(record.totalHawbCbmNM || 0);
      grandTotalKgsFH += Number(record.totalHawbCwFH || 0);
      grandTotalKgsTotal += Number(record.totalHawbCwFH || 0) + Number(record.totalHawbCwNM || 0);
      grandTotalRevenueUSDFH += Number(record.totalRevenueFHInUSD || 0);
      grandTotalRevenueUSDTotal += Number(record.totalRevenueFHInUSD || 0) + Number(record.totalRevenueNMInUSD || 0);
      grandTotalProfitUSDFH += Number(record.totalProfitFHInUSD || 0);
      grandTotalProfitUSDTotal += Number(record.totalProfitFHInUSD || 0) + Number(record.totalProfitNMInUSD || 0);
      grandTotalRevenueVNDFH += Number(record.totalRevenueFHInVND || 0);
      grandTotalRevenueVNDTotal += Number(record.totalRevenueFHInVND || 0) + Number(record.totalRevenueNMInVND || 0);
      grandTotalProfitVNDFH += Number(record.totalProfitFHInVND || 0);
      grandTotalProfitVNDTotal += Number(record.totalProfitFHInVND || 0) + Number(record.totalProfitNMInVND || 0);
    });

    // Calculate grand total percentages
    const grandTotalShipmentPercent = grandTotalShipmentTotal > 0 ? ((grandTotalShipmentFH / grandTotalShipmentTotal) * 100).toFixed(1) : '0.0';
    const grandTotalTeuPercent = grandTotalTeuTotal > 0 ? ((grandTotalTeuFH / grandTotalTeuTotal) * 100).toFixed(1) : '0.0';
    const grandTotalCbmPercent = grandTotalCbmTotal > 0 ? ((grandTotalCbmFH / grandTotalCbmTotal) * 100).toFixed(1) : '0.0';
    const grandTotalKgsPercent = grandTotalKgsTotal > 0 ? ((grandTotalKgsFH / grandTotalKgsTotal) * 100).toFixed(1) : '0.0';
    const grandTotalRevenueUSDPercent = grandTotalRevenueUSDTotal > 0 ? ((grandTotalRevenueUSDFH / grandTotalRevenueUSDTotal) * 100).toFixed(1) : '0.0';
    const grandTotalProfitUSDPercent = grandTotalProfitUSDTotal > 0 ? ((grandTotalProfitUSDFH / grandTotalProfitUSDTotal) * 100).toFixed(1) : '0.0';
    const grandTotalRevenueVNDPercent = grandTotalRevenueVNDTotal > 0 ? ((grandTotalRevenueVNDFH / grandTotalRevenueVNDTotal) * 100).toFixed(1) : '0.0';
    const grandTotalProfitVNDPercent = grandTotalProfitVNDTotal > 0 ? ((grandTotalProfitVNDFH / grandTotalProfitVNDTotal) * 100).toFixed(1) : '0.0';

    // Add grand total row
    const grandTotalRow = {
      branch: 'TOTAL',
      shipmentFH: grandTotalShipmentFH,
      shipmentTotal: grandTotalShipmentTotal,
      shipmentPercent: `${grandTotalShipmentPercent}%`,
      teuFH: grandTotalTeuFH,
      teuTotal: grandTotalTeuTotal,
      teuPercent: `${grandTotalTeuPercent}%`,
      cbmFH: grandTotalCbmFH.toFixed(2),
      cbmTotal: grandTotalCbmTotal.toFixed(2),
      cbmPercent: `${grandTotalCbmPercent}%`,
      kgsFH: grandTotalKgsFH.toFixed(2),
      kgsTotal: grandTotalKgsTotal.toFixed(2),
      kgsPercent: `${grandTotalKgsPercent}%`,
      revenueUSDFH: util.text.formater.number(grandTotalRevenueUSDFH, 0),
      revenueUSDTotal: util.text.formater.number(grandTotalRevenueUSDTotal, 0),
      revenueUSDPercent: `${grandTotalRevenueUSDPercent}%`,
      profitUSDFH: util.text.formater.number(grandTotalProfitUSDFH, 0),
      profitUSDTotal: util.text.formater.number(grandTotalProfitUSDTotal, 0),
      profitUSDPercent: `${grandTotalProfitUSDPercent}%`,
      revenueVNDFH: util.text.formater.number(grandTotalRevenueVNDFH, 0),
      revenueVNDTotal: util.text.formater.number(grandTotalRevenueVNDTotal, 0),
      revenueVNDPercent: `${grandTotalRevenueVNDPercent}%`,
      profitVNDFH: util.text.formater.number(grandTotalProfitVNDFH, 0),
      profitVNDTotal: util.text.formater.number(grandTotalProfitVNDTotal, 0),
      profitVNDPercent: `${grandTotalProfitVNDPercent}%`,
      isGrandTotal: true
    };

    return [...branchData, grandTotalRow];
  }

  render(): React.JSX.Element {
    const { appContext, pageContext } = this.props;

    const filterRecords: any[] = this.vgridContext.model.getFilterRecords();

    let nominatedPlugin = new entity.VGridEntityListEditorPlugin(this.calculateNominatedSummary(filterRecords));
    let freehandPlugin = new entity.VGridEntityListEditorPlugin(this.calculateFreehandSummary(filterRecords));

    return (
      <div className="flex-vbox mx-1 p-1 bg-white rounded-md w-100 h-100 my-1" >

        <div className="flex-vbox flex-grow-0" key={util.IDTracker.next()}>
          {this.renderUIGrid()}
        </div>

        <div className='flex-vbox flex-grow-1'>

          <div className="d-flex flex-row gap-3 p-3" style={{ minWidth: '1800px' }}>
            {/* TEU Distribution Chart */}
            <div className="bg-white border rounded-3 flex-vbox justify-content-center align-items-center"
              style={{
                minWidth: 600,
                maxWidth: 600,
                borderColor: '#dee2e6',
                borderWidth: '1px',
                borderStyle: 'solid',
                transition: 'all 0.3s ease'
              }}
              onMouseEnter={(e) => Object.assign(e.currentTarget.style, {
                boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
                transform: 'translateY(-2px)',
                backgroundColor: '#f8f9fa'
              })}
              onMouseLeave={(e) => {
                e.currentTarget.style.boxShadow = 'none';
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.backgroundColor = '#fff';
              }}>
              <div className="flex-vbox w-100 h-100">
                <div className="flex-vbox mx-2 p-2 bg-white rounded-md w-100 h-100">
                  <div className="flex-hbox flex-grow-0 align-items-center justify-content-between px-2 py-2 border-bottom">
                    <h5 style={{ color: '#6c757d', fontSize: '14px' }}>
                      <FeatherIcon.PieChart className="me-2" size={16} />
                      TEU Distribution by Branch
                    </h5>
                  </div>
                  <div className="d-flex justify-content-center align-items-center" style={{ height: '350px' }}>
                    <PieChart width={580} height={350}>
                      <Pie
                        dataKey="totalTeu"
                        nameKey="branch"
                        isAnimationActive={false}
                        data={this.calculateTeuDistribution(filterRecords)}
                        cx={290}
                        cy={175}
                        outerRadius={120}
                        innerRadius={50}
                        fill="#8884d8"
                        labelLine={true}
                        label={(entry) => `${entry.percent}%`}>
                        {
                          this.calculateTeuDistribution(filterRecords).map((entry, index) => (
                            <Cell
                              key={`cell-${index}`}
                              fill={entry.color}
                            />
                          ))
                        }
                      </Pie>
                      <Tooltip
                        contentStyle={{
                          backgroundColor: 'white',
                          border: '1px solid #ccc',
                          width: '200px'
                        }}
                        formatter={(value, name) => [
                          `${value} TEUs`,
                          name
                        ]}
                      />
                      <Legend layout="vertical" verticalAlign="middle" align="right" wrapperStyle={{ fontSize: '12px' }} />
                    </PieChart>
                  </div>
                </div>
              </div>
            </div>

            {/* CBM Distribution Chart */}
            <div className="bg-white border rounded-3 flex-vbox justify-content-center align-items-center"
              style={{
                minWidth: 600,
                maxWidth: 600,
                borderColor: '#dee2e6',
                borderWidth: '1px',
                borderStyle: 'solid',
                transition: 'all 0.3s ease'
              }}
              onMouseEnter={(e) => Object.assign(e.currentTarget.style, {
                boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
                transform: 'translateY(-2px)',
                backgroundColor: '#f8f9fa'
              })}
              onMouseLeave={(e) => {
                e.currentTarget.style.boxShadow = 'none';
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.backgroundColor = '#fff';
              }}>
              <div className="flex-vbox w-100 h-100">
                <div className="flex-vbox mx-2 p-2 bg-white rounded-md w-100 h-100">
                  <div className="flex-hbox flex-grow-0 align-items-center justify-content-between px-2 py-2 border-bottom">
                    <h5 style={{ color: '#6c757d', fontSize: '14px' }}>
                      <FeatherIcon.PieChart className="me-2" size={16} />
                      LCL CBM Distribution by Branch
                    </h5>
                  </div>
                  <div className="d-flex justify-content-center align-items-center" style={{ height: '350px' }}>
                    <PieChart width={580} height={350}>
                      <Pie
                        dataKey="totalCbm"
                        nameKey="branch"
                        isAnimationActive={false}
                        data={this.calculateCbmDistribution(filterRecords)}
                        cx={290}
                        cy={175}
                        outerRadius={120}
                        innerRadius={50}
                        fill="#8884d8"
                        labelLine={true}
                        label={(entry) => `${entry.percent}%`}>
                        {
                          this.calculateCbmDistribution(filterRecords).map((entry, index) => (
                            <Cell
                              key={`cell-${index}`}
                              fill={entry.color}
                            />
                          ))
                        }
                      </Pie>
                      <Tooltip
                        contentStyle={{
                          backgroundColor: 'white',
                          border: '1px solid #ccc',
                          width: '250px'
                        }}
                        formatter={(value, name) => [
                          `${util.text.formater.number(Number(value), 2)} CBM`,
                          name
                        ]}
                      />
                      <Legend layout="vertical" verticalAlign="middle" align="right" wrapperStyle={{ fontSize: '12px' }} />
                    </PieChart>
                  </div>
                </div>
              </div>
            </div>

            {/* KGS Distribution Chart */}
            <div className="bg-white border rounded-3 flex-vbox justify-content-center align-items-center"
              style={{
                minWidth: 600,
                maxWidth: 600,
                borderColor: '#dee2e6',
                borderWidth: '1px',
                borderStyle: 'solid',
                transition: 'all 0.3s ease'
              }}
              onMouseEnter={(e) => Object.assign(e.currentTarget.style, {
                boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
                transform: 'translateY(-2px)',
                backgroundColor: '#f8f9fa'
              })}
              onMouseLeave={(e) => {
                e.currentTarget.style.boxShadow = 'none';
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.backgroundColor = '#fff';
              }}>
              <div className="flex-vbox w-100 h-100">
                <div className="flex-vbox mx-2 p-2 bg-white rounded-md w-100 h-100">
                  <div className="flex-hbox flex-grow-0 align-items-center justify-content-between px-2 py-2 border-bottom">
                    <h5 style={{ color: '#6c757d', fontSize: '14px' }}>
                      <FeatherIcon.PieChart className="me-2" size={16} />
                      Air KGS Distribution by Branch
                    </h5>
                  </div>
                  <div className="d-flex justify-content-center align-items-center" style={{ height: '350px' }}>
                    <PieChart width={580} height={350}>
                      <Pie
                        dataKey="totalKgs"
                        nameKey="branch"
                        isAnimationActive={false}
                        data={this.calculateKgsDistribution(filterRecords)}
                        cx={290}
                        cy={175}
                        outerRadius={120}
                        innerRadius={50}
                        fill="#8884d8"
                        labelLine={true}
                        label={(entry) => `${entry.percent}%`}>
                        {
                          this.calculateKgsDistribution(filterRecords).map((entry, index) => (
                            <Cell
                              key={`cell-${index}`}
                              fill={entry.color}
                            />
                          ))
                        }
                      </Pie>
                      <Tooltip
                        contentStyle={{
                          backgroundColor: 'white',
                          border: '1px solid #ccc',
                          width: '250px'
                        }}
                        formatter={(value, name) => [
                          `${util.text.formater.number(Number(value), 2)} KGS`,
                          name
                        ]}
                      />
                      <Legend layout="vertical" verticalAlign="middle" align="right" wrapperStyle={{ fontSize: '12px' }} />
                    </PieChart>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Revenue vs Profit Bar Chart - Standalone Section */}
          {/* <div className="mx-2 my-3">
            <div className="bg-white border rounded-3 flex-vbox justify-content-center align-items-center"
              style={{
                borderColor: '#dee2e6',
                borderWidth: '1px',
                borderStyle: 'solid',
                transition: 'all 0.3s ease'
              }}
              onMouseEnter={(e) => Object.assign(e.currentTarget.style, {
                boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
                transform: 'translateY(-2px)',
                backgroundColor: '#f8f9fa'
              })}
              onMouseLeave={(e) => {
                e.currentTarget.style.boxShadow = 'none';
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.backgroundColor = '#fff';
              }}>
              <div className="flex-vbox w-100 h-100">
                <div className="flex-vbox mx-2 p-2 bg-white rounded-md w-100 h-100">
                  <div className="flex-hbox flex-grow-0 align-items-center justify-content-between px-2 py-2 border-bottom">
                    <h5 style={{ color: '#6c757d', fontSize: '14px' }}>
                      <FeatherIcon.BarChart className="me-2" size={16} />
                      Revenue vs Profit by Branch (USD)
                    </h5>
                  </div>
                  <div className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        data={this.calculateRevenueVsProfitData(filterRecords)}
                        margin={{
                          top: 20,
                          right: 30,
                          left: 20,
                          bottom: 60,
                        }}>
                        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                        <XAxis
                          dataKey="branch"
                          angle={-45}
                          textAnchor="end"
                          height={80}
                          interval={0}
                          tick={{ fontSize: 12 }}
                        />
                        <YAxis
                          tick={{ fontSize: 12 }}
                          tickFormatter={(value) => `$${util.text.formater.number(value, 0)}`}
                        />
                        <Tooltip
                          contentStyle={{
                            backgroundColor: 'white',
                            border: '1px solid #ccc',
                            borderRadius: '4px'
                          }}
                          formatter={(value, name, props) => {
                            // Use dataKey from props to determine the correct label
                            const dataKey = props?.dataKey;
                            const displayName = dataKey === 'revenue' ? 'Revenue' :
                              dataKey === 'profit' ? 'Profit' :
                                name; // fallback to original name
                            return [
                              `$${util.text.formater.number(Number(value), 0)}`,
                              displayName
                            ];
                          }}
                          labelFormatter={(label) => `Branch: ${label}`}
                        />
                        <Legend
                          wrapperStyle={{ fontSize: '12px' }}
                          iconType="rect"
                        />
                        <Bar
                          dataKey="revenue"
                          name="Revenue"
                          fill="#0088FE"
                          radius={[2, 2, 0, 0]}
                        />
                        <Bar
                          dataKey="profit"
                          name="Profit"
                          fill="#00C49F"
                          radius={[2, 2, 0, 0]}
                        />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </div>
              </div>
            </div>
          </div> */}

          {/* Revenue vs Profit Margin Combo Chart - Standalone Section */}
          {/* <div className="mx-2 my-3">
            <div className="bg-white border rounded-3 flex-vbox justify-content-center align-items-center"
              style={{
                borderColor: '#dee2e6',
                borderWidth: '1px',
                borderStyle: 'solid',
                transition: 'all 0.3s ease'
              }}
              onMouseEnter={(e) => Object.assign(e.currentTarget.style, {
                boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
                transform: 'translateY(-2px)',
                backgroundColor: '#f8f9fa'
              })}
              onMouseLeave={(e) => {
                e.currentTarget.style.boxShadow = 'none';
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.backgroundColor = '#fff';
              }}>
              <div className="flex-vbox w-100 h-100">
                <div className="flex-vbox mx-2 p-2 bg-white rounded-md w-100 h-100">
                  <div className="flex-hbox flex-grow-0 align-items-center justify-content-between px-2 py-2 border-bottom">
                    <h5 style={{ color: '#6c757d', fontSize: '14px' }}>
                      <FeatherIcon.TrendingUp className="me-2" size={16} />
                      Revenue vs Profit Margin by Branch
                    </h5>
                  </div>
                  <div className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
                    <ResponsiveContainer width="100%" height="100%">
                      <ComposedChart
                        data={this.calculateRevenueVsProfitMarginData(filterRecords)}
                        margin={{
                          top: 20,
                          right: 30,
                          left: 20,
                          bottom: 60,
                        }}>
                        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                        <XAxis
                          dataKey="branch"
                          angle={-45}
                          textAnchor="end"
                          height={80}
                          interval={0}
                          tick={{ fontSize: 12 }}
                        />
                        <YAxis
                          yAxisId="revenue"
                          orientation="left"
                          tick={{ fontSize: 12 }}
                          tickFormatter={(value) => `$${util.text.formater.number(value, 0)}`}
                        />
                        <YAxis
                          yAxisId="margin"
                          orientation="right"
                          tick={{ fontSize: 12 }}
                          tickFormatter={(value) => `${value}%`}
                        />
                        <Tooltip
                          contentStyle={{
                            backgroundColor: 'white',
                            border: '1px solid #ccc',
                            borderRadius: '4px'
                          }}
                          formatter={(value, name, props) => {
                            const dataKey = props?.dataKey;
                            if (dataKey === 'revenue') {
                              return [`$${util.text.formater.number(Number(value), 0)}`, 'Revenue'];
                            } else if (dataKey === 'profitMargin') {
                              return [`${value}%`, 'Profit Margin'];
                            }
                            return [value, name];
                          }}
                          labelFormatter={(label) => `Branch: ${label}`}
                        />
                        <Legend
                          wrapperStyle={{ fontSize: '12px' }}
                          iconType="rect"
                        />
                        <Bar
                          yAxisId="revenue"
                          dataKey="revenue"
                          name="Revenue (USD)"
                          fill="#0088FE"
                          radius={[2, 2, 0, 0]}
                        />
                        <Line
                          yAxisId="margin"
                          type="monotone"
                          dataKey="profitMargin"
                          name="Profit Margin (%)"
                          stroke="#FF8042"
                          strokeWidth={3}
                          dot={{ fill: '#FF8042', strokeWidth: 2, r: 4 }}
                          activeDot={{ r: 6, stroke: '#FF8042', strokeWidth: 2 }}
                        />
                      </ComposedChart>
                    </ResponsiveContainer>
                  </div>
                </div>
              </div>
            </div>
          </div> */}

          {/* Branch Performance Summary - Nominated vs Total */}
          <UIFreehandPerformanceSummary dialogEditor={false} editorTitle={''}
            plugin={freehandPlugin} appContext={appContext} pageContext={pageContext} />

          {/* Branch Performance Summary - Nominated vs Total */}
          <UINominatedPerformanceSummary dialogEditor={false} editorTitle={''}
            plugin={nominatedPlugin} appContext={appContext} pageContext={pageContext} />
        </div>

      </div >
    )
  }

}

