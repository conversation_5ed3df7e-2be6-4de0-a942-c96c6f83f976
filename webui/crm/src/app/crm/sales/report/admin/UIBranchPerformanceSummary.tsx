import React from "react";
import { GridConfig } from "app/crm/common";
import { util, app, entity, grid, bs, sql, input } from '@datatp-ui/lib';
import * as FeatherIcon from 'react-feather'

const nominatedPerformanceSummaryGridConfig: GridConfig = {
  header: {
    height: 20,
  },
  row: {
    height: 30,
  },
  showHeader: true,
  showBorder: true,
  columns: [
    {
      field: 'branch', label: 'Branch',
      cssClass: 'border-start border-light text-start fw-bold'
    },
    // Shipment columns
    { field: 'shipmentNM', label: 'Shipment NM', cssClass: 'border-start border-light text-end' },
    { field: 'shipmentTotal', label: 'Shipment Total', cssClass: 'border-start border-light text-end' },
    { field: 'shipmentPercent', label: 'Shipment %', cssClass: 'border-start border-light text-end fw-bold text-primary' },

    // FCL (TEUs) columns
    { field: 'teuNM', label: 'TEU NM', cssClass: 'border-start border-light text-end' },
    { field: 'teuTotal', label: 'TEU Total', cssClass: 'border-start border-light text-end' },
    { field: 'teuPercent', label: 'TEU %', cssClass: 'border-start border-light text-end fw-bold text-primary' },

    // LCL (CBM) columns
    { field: 'cbmNM', label: 'CBM NM', cssClass: 'border-start border-light text-end' },
    { field: 'cbmTotal', label: 'CBM Total', cssClass: 'border-start border-light text-end' },
    { field: 'cbmPercent', label: 'CBM %', cssClass: 'border-start border-light text-end fw-bold text-primary' },

    // Air (KGS) columns
    { field: 'kgsNM', label: 'KGS NM', cssClass: 'border-start border-light text-end' },
    { field: 'kgsTotal', label: 'KGS Total', cssClass: 'border-start border-light text-end' },
    { field: 'kgsPercent', label: 'KGS %', cssClass: 'border-start border-light text-end fw-bold text-primary' },

    // Revenue (USD) columns
    { field: 'revenueUSDNM', label: 'Revenue NM (USD)', cssClass: 'border-start border-light text-end' },
    { field: 'revenueUSDTotal', label: 'Revenue Total (USD)', cssClass: 'border-start border-light text-end' },
    { field: 'revenueUSDPercent', label: 'Revenue %', cssClass: 'border-start border-light text-end fw-bold text-primary' },

    // G/P (USD) columns
    { field: 'profitUSDNM', label: 'G/P NM (USD)', cssClass: 'border-start border-light text-end' },
    { field: 'profitUSDTotal', label: 'G/P Total (USD)', cssClass: 'border-start border-light text-end' },
    { field: 'profitUSDPercent', label: 'G/P %', cssClass: 'border-start border-light text-end fw-bold text-primary' },
  ],
  widthConfig: {
    totalWidth: 1800,
    minColumnWidth: 70,
    ratios: [2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1.2, 1.2, 1, 1.2, 1.2, 1]
  }
}


interface UIShipmentTypePerformanceSummaryProps extends entity.VGridEntityListEditorProps { }

export class UINominatedPerformanceSummary extends entity.VGridEntityListEditor<UIShipmentTypePerformanceSummaryProps> {

  createVGridConfig(): grid.VGridConfig {
    // Helper function to render percentage fields with proper styling
    const renderPercentField = (field: grid.FieldConfig, record: any) => {
      let val = record[field.name];
      let isGrandTotal = record.isGrandTotal;
      let textColor = isGrandTotal ? 'text-success' : 'text-primary';
      return (
        <div className={`flex-hbox justify-content-end text-end w-100 fw-bold ${textColor}`}>
          {val}
        </div>
      );
    };

    // Helper function to render numeric fields with grand total styling
    const renderNumericField = (field: grid.FieldConfig, record: any) => {
      let val = record[field.name];
      return (
        <div className={`flex-hbox justify-content-end text-end w-100`}>
          {val}
        </div>
      );
    };

    let config: grid.VGridConfig = {
      record: {
        dataCellHeight: 40,
        editor: {
          supportViewMode: ['table'],
          enable: true
        },
        fields: [
          // Branch field
          {
            name: 'branch', label: 'Branch', width: 120,
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              let record: any = dRec.record;
              let val = record[field.name];
              let isGrandTotal = record.isGrandTotal;
              let textColor = isGrandTotal ? 'text-primary' : '';
              let fontWeight = 'fw-bold';
              return (
                <div className={`flex-hbox justify-content-start text-start w-100 ${fontWeight} ${textColor}`}>
                  {val}
                </div>
              );
            }
          },
          {
            name: 'shipmentNM', label: 'NM', width: 80,
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              return renderNumericField(field, dRec.record);
            }
          },
          {
            name: 'shipmentTotal', label: 'Total', width: 80,
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              return renderNumericField(field, dRec.record);
            }
          },
          {
            name: 'shipmentPercent', label: '%', width: 70,
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              return renderPercentField(field, dRec.record);
            }
          },

          // FCL (TEUs) fields
          {
            name: 'teuNM', label: 'NM', width: 80,
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              return renderNumericField(field, dRec.record);
            }
          },
          {
            name: 'teuTotal', label: 'Total', width: 80,
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              return renderNumericField(field, dRec.record);
            }
          },
          {
            name: 'teuPercent', label: '%', width: 70,
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              return renderPercentField(field, dRec.record);
            }
          },

          // LCL (CBM) fields
          {
            name: 'cbmNM', label: 'NM', width: 80,
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              return renderNumericField(field, dRec.record);
            }
          },
          {
            name: 'cbmTotal', label: 'Total', width: 80,
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              return renderNumericField(field, dRec.record);
            }
          },
          {
            name: 'cbmPercent', label: '%', width: 70,
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              return renderPercentField(field, dRec.record);
            }
          },

          // Air (KGS) fields
          {
            name: 'kgsNM', label: 'NM', width: 80,
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              return renderNumericField(field, dRec.record);
            }
          },
          {
            name: 'kgsTotal', label: 'Total', width: 80,
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              return renderNumericField(field, dRec.record);
            }
          },
          {
            name: 'kgsPercent', label: '%', width: 70,
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              return renderPercentField(field, dRec.record);
            }
          },

          // Revenue (USD) fields
          // {
          //   name: 'revenueUSDNM', label: 'NM', width: 120,
          //   customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
          //     return renderNumericField(field, dRec.record);
          //   }
          // },
          // {
          //   name: 'revenueUSDTotal', label: 'Total', width: 120,
          //   customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
          //     return renderNumericField(field, dRec.record);
          //   }
          // },
          // {
          //   name: 'revenueUSDPercent', label: '%', width: 70,
          //   customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
          //     return renderPercentField(field, dRec.record);
          //   }
          // },

          // G/P (USD) fields
          // {
          //   name: 'profitUSDNM', label: 'NM', width: 120,
          //   customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
          //     return renderNumericField(field, dRec.record);
          //   }
          // },
          // {
          //   name: 'profitUSDTotal', label: 'Total', width: 120,
          //   customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
          //     return renderNumericField(field, dRec.record);
          //   }
          // },
          // {
          //   name: 'profitUSDPercent', label: '%', width: 70,
          //   customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
          //     return renderPercentField(field, dRec.record);
          //   }
          // }
        ],
        fieldGroups: {
          "Shipment": {
            label: 'Shipment',
            fields: ['shipmentNM', 'shipmentTotal', 'shipmentPercent']
          },
          "fcl": {
            label: 'FCL (TEUs)',
            fields: ['teuNM', 'teuTotal', 'teuPercent']
          },
          "lcl": {
            label: 'LCL (CBM)',
            fields: ['cbmNM', 'cbmTotal', 'cbmPercent']
          },
          "air": {
            label: 'Air (KGS)',
            fields: ['kgsNM', 'kgsTotal', 'kgsPercent']
          },
          // "revenue": {
          //   label: 'Revenue (USD)',
          //   fields: ['revenueUSDNM', 'revenueUSDTotal', 'revenueUSDPercent']
          // },
          // "grossProfit": {
          //   label: 'G/P (USD)',
          //   fields: ['profitUSDNM', 'profitUSDTotal', 'profitUSDPercent']
          // },
        },
      },
      toolbar: { hide: true },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          }
        }
      },
    }
    return config;
  }

  // Method to load data from NominatedSummary
  loadNominatedSummaryData(summaryData: any[]) {
    // Load the data into the grid model
    this.vgridContext.model.update(summaryData);
    this.forceUpdate();
  }

  render(): React.JSX.Element {


    return (
      <div className="d-flex flex-column gap-2 p-3">
        <div className="bg-white border rounded-3 flex-vbox justify-content-center align-items-center"
          style={{
            minWidth: 600,
            borderColor: '#dee2e6',
            borderWidth: '1px',
            borderStyle: 'solid',
            transition: 'all 0.3s ease'
          }}
          onMouseEnter={(e) => Object.assign(e.currentTarget.style, {
            boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
            transform: 'translateY(-2px)',
            backgroundColor: '#f8f9fa'
          })}
          onMouseLeave={(e) => {
            e.currentTarget.style.boxShadow = 'none';
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.backgroundColor = '#fff';
          }}>
          <div className="flex-vbox mx-2 p-2 bg-white rounded-md w-100 h-100">
            <div className="flex-hbox flex-grow-0 align-items-center justify-content-between px-2 py-2 border-bottom">
              <h5 style={{ color: '#6c757d', fontSize: '16px' }}>
                <FeatherIcon.TrendingUp className="me-2" size={18} />
                Branch Performance Summary - Nominated vs Total
              </h5>
            </div>
            <div className="flex-vbox">
              <grid.VGrid context={this.vgridContext} />
            </div>
          </div>
        </div>
      </div>
    )
  }
}

export class UIFreehandPerformanceSummary extends entity.VGridEntityListEditor<UIShipmentTypePerformanceSummaryProps> {

  createVGridConfig(): grid.VGridConfig {
    // Helper function to render percentage fields with proper styling
    const renderPercentField = (field: grid.FieldConfig, record: any) => {
      let val = record[field.name];
      let isGrandTotal = record.isGrandTotal;
      let textColor = isGrandTotal ? 'text-success' : 'text-primary';
      return (
        <div className={`flex-hbox justify-content-end text-end w-100 fw-bold ${textColor}`}>
          {val}
        </div>
      );
    };

    // Helper function to render numeric fields with grand total styling
    const renderNumericField = (field: grid.FieldConfig, record: any) => {
      let val = record[field.name];
      return (
        <div className={`flex-hbox justify-content-end text-end w-100`}>
          {val}
        </div>
      );
    };

    let config: grid.VGridConfig = {
      record: {
        dataCellHeight: 40,
        editor: {
          supportViewMode: ['table'],
          enable: true
        },
        fields: [
          // Branch field
          {
            name: 'branch', label: 'Branch', width: 120,
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              let record: any = dRec.record;
              let val = record[field.name];
              let isGrandTotal = record.isGrandTotal;
              let textColor = isGrandTotal ? 'text-primary' : '';
              let fontWeight = 'fw-bold';
              return (
                <div className={`flex-hbox justify-content-start text-start w-100 ${fontWeight} ${textColor}`}>
                  {val}
                </div>
              );
            }
          },
          {
            name: 'shipmentFH', label: 'FH', width: 80,
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              return renderNumericField(field, dRec.record);
            }
          },
          {
            name: 'shipmentTotal', label: 'Total', width: 80,
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              return renderNumericField(field, dRec.record);
            }
          },
          {
            name: 'shipmentPercent', label: '%', width: 70,
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              return renderPercentField(field, dRec.record);
            }
          },

          // FCL (TEUs) fields
          {
            name: 'teuFH', label: 'FH', width: 80,
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              return renderNumericField(field, dRec.record);
            }
          },
          {
            name: 'teuTotal', label: 'Total', width: 80,
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              return renderNumericField(field, dRec.record);
            }
          },
          {
            name: 'teuPercent', label: '%', width: 70,
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              return renderPercentField(field, dRec.record);
            }
          },

          // LCL (CBM) fields
          {
            name: 'cbmFH', label: 'FH', width: 80,
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              return renderNumericField(field, dRec.record);
            }
          },
          {
            name: 'cbmTotal', label: 'Total', width: 80,
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              return renderNumericField(field, dRec.record);
            }
          },
          {
            name: 'cbmPercent', label: '%', width: 70,
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              return renderPercentField(field, dRec.record);
            }
          },

          // Air (KGS) fields
          {
            name: 'kgsFH', label: 'FH', width: 80,
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              return renderNumericField(field, dRec.record);
            }
          },
          {
            name: 'kgsTotal', label: 'Total', width: 80,
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              return renderNumericField(field, dRec.record);
            }
          },
          {
            name: 'kgsPercent', label: '%', width: 70,
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              return renderPercentField(field, dRec.record);
            }
          },

          // Revenue (USD) fields
          // {
          //   name: 'revenueUSDFH', label: 'FH', width: 120,
          //   customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
          //     return renderNumericField(field, dRec.record);
          //   }
          // },
          // {
          //   name: 'revenueUSDTotal', label: 'Total', width: 120,
          //   customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
          //     return renderNumericField(field, dRec.record);
          //   }
          // },
          // {
          //   name: 'revenueUSDPercent', label: '%', width: 70,
          //   customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
          //     return renderPercentField(field, dRec.record);
          //   }
          // },

          // G/P (USD) fields
          // {
          //   name: 'profitUSDFH', label: 'FH', width: 120,
          //   customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
          //     return renderNumericField(field, dRec.record);
          //   }
          // },
          // {
          //   name: 'profitUSDTotal', label: 'Total', width: 120,
          //   customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
          //     return renderNumericField(field, dRec.record);
          //   }
          // },
          // {
          //   name: 'profitUSDPercent', label: '%', width: 70,
          //   customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
          //     return renderPercentField(field, dRec.record);
          //   }
          // }
        ],
        fieldGroups: {
          "Shipment": {
            label: 'Shipment',
            fields: ['shipmentFH', 'shipmentTotal', 'shipmentPercent']
          },
          "fcl": {
            label: 'FCL (TEUs)',
            fields: ['teuFH', 'teuTotal', 'teuPercent']
          },
          "lcl": {
            label: 'LCL (CBM)',
            fields: ['cbmFH', 'cbmTotal', 'cbmPercent']
          },
          "air": {
            label: 'Air (KGS)',
            fields: ['kgsFH', 'kgsTotal', 'kgsPercent']
          },
          // "revenue": {
          //   label: 'Revenue (USD)',
          //   fields: ['revenueUSDFH', 'revenueUSDTotal', 'revenueUSDPercent']
          // },
          // "grossProfit": {
          //   label: 'G/P (USD)',
          //   fields: ['profitUSDFH', 'profitUSDTotal', 'profitUSDPercent']
          // },
        },
      },
      toolbar: { hide: true },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          }
        }
      },
    }
    return config;
  }

  // Method to load data from NominatedSummary
  loadNominatedSummaryData(summaryData: any[]) {
    // Load the data into the grid model
    this.vgridContext.model.update(summaryData);
    this.forceUpdate();
  }

  render(): React.JSX.Element {


    return (
      <div className="d-flex flex-column gap-2 p-3">
        <div className="bg-white border rounded-3 flex-vbox justify-content-center align-items-center"
          style={{
            minWidth: 600,
            borderColor: '#dee2e6',
            borderWidth: '1px',
            borderStyle: 'solid',
            transition: 'all 0.3s ease'
          }}
          onMouseEnter={(e) => Object.assign(e.currentTarget.style, {
            boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
            transform: 'translateY(-2px)',
            backgroundColor: '#f8f9fa'
          })}
          onMouseLeave={(e) => {
            e.currentTarget.style.boxShadow = 'none';
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.backgroundColor = '#fff';
          }}>
          <div className="flex-vbox mx-2 p-2 bg-white rounded-md w-100 h-100">
            <div className="flex-hbox flex-grow-0 align-items-center justify-content-between px-2 py-2 border-bottom">
              <h5 style={{ color: '#6c757d', fontSize: '16px' }}>
                <FeatherIcon.TrendingUp className="me-2" size={18} />
                Branch Performance Summary - Freehand vs Total
              </h5>
            </div>
            <div className="flex-vbox">
              <grid.VGrid context={this.vgridContext} />
            </div>
          </div>
        </div>
      </div>
    )
  }
}