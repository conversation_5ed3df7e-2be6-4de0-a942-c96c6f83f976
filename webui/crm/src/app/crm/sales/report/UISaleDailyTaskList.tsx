import React from "react";
import { entity, grid, sql, app, bs, util, server, input } from "@datatp-ui/lib";
import { module } from '@datatp-ui/erp';
import * as FeatherIcon from 'react-feather';
import { SalesTaskStatus, SalesTaskUtils, SaleTaskType, SaleTaskTypeUtils, T } from "../backend";
import { responsiveGridConfig } from "../common";
import { UITaskCalendarUtils } from "./UITaskCalendarPage";

const SESSION = app.host.DATATP_HOST.session;

export class UISaleDailyTaskListPlugin extends entity.DbEntityListPlugin {

  constructor(space: 'User' | 'Company' | 'System', selectedDate?: Date) {
    super([]);

    if (!selectedDate) selectedDate = new Date();

    let dateFilter = new util.TimeRange();
    dateFilter.fromSetDate(selectedDate).fromSubtract(2, "month").fromStartOf('month');
    dateFilter.toSetDate(selectedDate).fromAdd(2, 'month').toEndOf('month');

    this.backend = {
      context: 'company',
      service: 'TaskCalendarService',
      searchMethod: space === 'User' ? 'searchSalesDailyTasks' : "searchSalesDailyTaskReport",
      changeStorageStateMethod: 'changeDailyTaskStorageState',
      deleteMethod: 'deleteSalesDailyTaskByIds',
    }

    this.searchParams = {
      params: { space: space },
      filters: sql.createSearchFilter(),
      rangeFilters: [
        // sql.createDateTimeFilterNew("createdDate", "Created Date", dateFilter),
        sql.createDateTimeFilterNew("dueDate", "Due Date", dateFilter)
      ],
      optionFilters: [
        sql.createStorageStateFilter(
          [entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED]
        ),
      ],
      maxReturn: 5000
    }
  }

  withIds(ids: any) {
    if (ids) this.addSearchParam('ids', ids);
    return this;
  }

  withSalemanAccountId(salemanAccountId: number) {
    this.addSearchParam('participantAccountIds', [salemanAccountId]);
    return this;
  }

  withTaskType(taskType: typeof SaleTaskType[keyof typeof SaleTaskType]) {
    this.addSearchParam('taskType', taskType.value);
    return this;
  }

  withDueDate(fromValue: string, toValue: string) {
    let startOfFromDate: Date | null = null;
    let endOfToDate: Date | null = null;

    if ((fromValue || '').trim()) {
      let fromDate: Date = util.TimeUtil.parseCompactDateTimeFormat(fromValue);
      startOfFromDate = new Date(fromDate.getFullYear(), fromDate.getMonth(), fromDate.getDate());
    }

    if ((toValue || '').trim()) {
      let toDate: Date = util.TimeUtil.parseCompactDateTimeFormat(toValue);
      endOfToDate = new Date(toDate.getFullYear(), toDate.getMonth(), toDate.getDate(), 23, 59, 59, 999);
    }

    if (this.searchParams) {
      let rangeFilters = this.searchParams.rangeFilters;
      if (rangeFilters) {
        for (let i = 0; i < rangeFilters.length; i++) {
          let filter = rangeFilters[i];
          if (filter.name === 'dueDate') {
            filter.fromValue = startOfFromDate ? util.TimeUtil.javaCompactDateTimeFormat(startOfFromDate) : fromValue;
            filter.toValue = endOfToDate ? util.TimeUtil.javaCompactDateTimeFormat(endOfToDate) : toValue;
            break;
          }
        }
      }
      this.searchParams.rangeFilters = rangeFilters;
    }
    return this;
  }

  loadData(uiList: entity.DbEntityList<any>): void {
    this.createBackendSearch(uiList, { params: this.searchParams }).call();
  }

  loadDataCalendar(ui: app.AppComponent, callback: server.SuccessCallback): void {
    let backend = this.backend;
    if (!backend || !backend.searchMethod) throw new Error("Need to config backend");
    const { appContext } = ui.props;
    appContext.createHttpBackendCall(backend.service, backend.searchMethod, { sqlParams: this.searchParams })
      .withSuccess(callback).call();
  }

}

export interface UISaleDailyTaskListProps extends entity.DbEntityListProps {
  space: 'User' | 'Company' | 'System';
  hideToolbar?: boolean;
  hideFooter?: boolean;
}
export class UISaleDailyTaskList extends entity.DbEntityList<UISaleDailyTaskListProps> {

  createVGridConfig(): grid.VGridConfig {
    const { plugin, pageContext, space, hideToolbar, hideFooter } = this.props;
    let moderatorCap = pageContext.hasUserModeratorCapability();
    let writeCap = pageContext.hasUserWriteCapability();
    let isEnableDbSerach = !!plugin.searchParams;
    const CELL_HEIGHT: number = 40;

    let onInputChange = (ctx: grid.FieldContext, oldVal: any, newVal: any) => {
      let record: any = ctx.displayRecord.record;
      if (newVal && oldVal !== newVal) {
        let fieldName = ctx.fieldConfig.name;
        record[fieldName] = newVal;
      }
    }

    let config: grid.VGridConfig = {
      record: {
        control: {
          width: 40,
          items: [
            {
              name: 'resend', hint: 'Resend', icon: FeatherIcon.ExternalLink,
              onClick: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
                let uiList = ctx.uiRoot as UISaleDailyTaskList;
                uiList.onSelect(dRecord);
              },
            },
          ]
        },
        dataCellHeight: CELL_HEIGHT,
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          {
            name: 'salemanLabel', label: T(`Saleman`), width: 170, filterable: true,
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let uiList = ctx.uiRoot as UISaleDailyTaskList;
              const { appContext, pageContext } = uiList.props;
              let employeeName: string = record['salemanLabel'] || 'N/A';

              let parts = employeeName.trim().toLowerCase().split(' ');
              parts = parts.map(word => word.charAt(0).toUpperCase() + word.slice(1));

              if (parts.length >= 3) {
                let initCount = parts.length - 2; // Luôn giữ lại 2 chữ cuối
                let initials = parts.slice(0, initCount).map(word => word[0] + '.').join('');
                employeeName = `${initials} ${parts.slice(-2).join(' ')}`;
              }

              return (
                <div className='flex-hbox justify-content-center align-items-center' >
                  <module.account.WAvatars className='px-2'
                    appContext={appContext} pageContext={pageContext} avatarIds={[record['salemanAccountId']]}
                    avatarIdType='AccountId' width={20} height={20} borderRadius={10} />
                  <div className="flex-hbox">{employeeName}</div>
                </div>
              )
            },
          },
          {
            name: 'label', label: T('Label'), width: 280, filterable: true, style: { height: CELL_HEIGHT },
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
              let record: any = dRecord.record;
              let valTruncate = util.text.formater.uiTruncate(record[field.name], 280, true);
              return <div className="flex-hbox align-items-center">{valTruncate}</div>
            },
            editor: {
              type: 'string', enable: (space === 'User' && writeCap) || moderatorCap,
              renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                let { fieldConfig, gridContext, displayRecord, tabIndex, focus } = ctx;
                let record = displayRecord.record;
                let createdDate = util.TimeUtil.parseCompactDateTimeFormat(record['createdDate']);
                createdDate.setHours(0, 0, 0, 0);
                let today = new Date();
                today.setHours(0, 0, 0, 0);
                let enable = (fieldConfig.editor?.enable && createdDate.getTime() >= today.getTime())
                if (!enable) {
                  return fieldConfig.customRender?.(gridContext, fieldConfig, displayRecord, focus);
                }
                let style = fieldConfig.style;
                return <input.BBStringField bean={record} field={fieldConfig.name} tabIndex={tabIndex} focus={focus} onInputChange={onInputChange} style={style} />
              },
              onInputChange: onInputChange,
            }
          },
          {
            name: 'taskType', label: T('Task Type'), width: 150, filterable: true,
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let taskTypeInfo = SaleTaskTypeUtils.getTypeInfo(record['taskType']);
              let TaskIcon = taskTypeInfo.icon;
              let label = taskTypeInfo.label;
              let color = taskTypeInfo.color;
              const typeList = SaleTaskTypeUtils.getTaskTypeList();
              const typeRemaining = typeList.filter(type => type.value !== record['taskType']);
              return (
                <bs.Popover className="d-flex flex-center w-100 h-100"
                  title={T('Task Type')} closeOnTrigger=".btn" >

                  <bs.PopoverToggle
                    className={`flex-hbox flex-center px-2 py-2 rounded-2 bg-${color}-subtle text-${color} w-100 `}>
                    <TaskIcon size={14} className="me-1" />
                    <span>{label}</span>
                  </bs.PopoverToggle>

                  <bs.PopoverContent>
                    <div className='flex-vbox gap-2' style={{ width: '200px' }}>
                      {typeRemaining.map(opt => {
                        let OptIcon = opt.icon;
                        return (
                          <div key={opt.value}
                            className={`d-flex flex-center px-2 py-1 rounded-2 bg-${opt.color}-subtle text-${opt.color} w-100 cursor-pointer`}
                            onClick={() => {
                              record['taskType'] = opt.value;
                              this.forceUpdate();
                            }}>
                            <OptIcon size={14} className="me-1" />
                            <span>{opt.label}</span>
                          </div>
                        );
                      })}
                    </div>
                  </bs.PopoverContent>
                </bs.Popover>
              );
            },
          },
          {
            name: 'description', label: T('Description'), width: 400, style: { height: CELL_HEIGHT },
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
              let record: any = dRecord.record;
              let valTruncate = util.text.formater.uiTruncate(record[field.name], 400, true);
              return <div className="flex-hbox">{valTruncate}</div>
            },
            editor: { type: 'string', enable: (space === 'User' && writeCap) || moderatorCap, onInputChange: onInputChange }
          },
          {
            name: 'suggestedSupport', label: T('Suggested Support'), width: 280, style: { height: CELL_HEIGHT },
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
              let record: any = dRecord.record;
              let valTruncate = util.text.formater.uiTruncate(record[field.name], 280, true);
              return <div className="flex-hbox">{valTruncate}</div>
            },
            editor: { type: 'string', enable: (space === 'User' && writeCap) || moderatorCap, onInputChange: onInputChange }
          },
          {
            name: 'createdDate', label: T('Created Date'), width: 170, filterable: true, filterableType: 'date',
            format: util.text.formater.compactDateTime
          },

          {
            name: 'dueDate', label: T('Due Date'), width: 170, filterable: true, filterableType: 'date',
            format: util.text.formater.compactDateTime
          },

          {
            name: 'notificationTime', label: T('NotificationTime'), width: 170, filterable: true, filterableType: 'date',
            editor: {
              type: 'date',
              onInputChange: onInputChange,
              enable: true,
              renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                let { fieldConfig, gridContext, displayRecord, tabIndex, focus } = ctx;
                let record = displayRecord.record;
                if (record['id']) {
                  return <div className="flex-hbox">{util.text.formater.compactDateTime(record[fieldConfig.name])}</div>;
                }
                return (
                  <input.BBDateInputMask
                    bean={record} field={fieldConfig.name} tabIndex={tabIndex} focus={focus} format={"DD/MM/YYYY"}
                    timeFormat={true}
                    onInputChange={onInputChange} />

                );
              },
            },
            format: util.text.formater.compactDateTime,

          },

          {
            name: 'status', label: T('Status'), width: 150, filterable: true, container: 'fixed-right',
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let uiList = ctx.uiRoot as UISaleDailyTaskList;
              let currentStatus = SalesTaskUtils.getStatusInfo(record['status']);
              let StatusIcon = currentStatus.icon;
              let label = currentStatus.label;
              let color = currentStatus.color;

              const statusList = SalesTaskUtils.getSalesTaskStatusList();
              const statusRemaining = statusList.filter(status => status.value !== record['status']);
              let isNew = !record['id'] ? true : false;
              if (isNew) {
                return (
                  <div className={`flex-hbox flex-center px-2 py-2 rounded-2 bg-${color}-subtle text-${color} w-100 `}>
                    <StatusIcon size={14} className="me-1" />
                    <span>{label}</span>
                  </div>
                )
              }
              return (
                <bs.Popover className="d-flex flex-center w-100 h-100"
                  title={T('Task Status')} closeOnTrigger=".btn">

                  <bs.PopoverToggle
                    className={`flex-hbox flex-center px-2 py-2 rounded-2 bg-${color}-subtle text-${color} w-100 `}>
                    <StatusIcon size={14} className="me-1" />
                    <span>{label}</span>
                  </bs.PopoverToggle>

                  <bs.PopoverContent>
                    <div className='flex-vbox gap-2' style={{ width: '200px' }}>
                      {statusRemaining.map(opt => {
                        let OptIcon = opt.icon;
                        return (
                          <div key={opt.value}
                            className={`d-flex flex-center px-2 py-1 rounded-2 bg-${opt.color}-subtle text-${opt.color} w-100 cursor-pointer`}
                            onClick={() => uiList.handleOnChangeStatus(opt.value, [record['id']])}>
                            <OptIcon size={14} className="me-1" />
                            <span>{opt.label}</span>
                          </div>
                        );
                      })}
                    </div>
                  </bs.PopoverContent>
                </bs.Popover>
              );
            },

          },

        ],
      },

      toolbar: {
        hide: hideToolbar,
        actions: [
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_DELETE(!writeCap, 'Del'),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_ADD(!moderatorCap, 'Add'),
        ],
        filters: entity.DbEntityListConfigTool.TOOLBAR_FILTERS(isEnableDbSerach)
      },
      footer: {

        page: {
          hide: hideFooter,
          render: (ctx: grid.VGridContext) => {
            let { appContext, pageContext } = this.props;
            return (
              <bs.Toolbar className='border'>
                <entity.XlsxExportButton appContext={appContext} pageContext={pageContext} context={ctx}
                  options={{
                    fileName: `Daily_Task_Data_${util.TimeUtil.toCompactDateFormat(new Date())}.xlsx`,
                    modelName: `Daily_Task_Data_${util.TimeUtil.toDateIdFormat(new Date())}`
                  }}
                />

                <entity.WButtonEntityWrite className="me-1"
                  appContext={appContext} pageContext={pageContext}
                  icon={FeatherIcon.Save} label={T('Save Changes')}
                  onClick={this.saveChanges} />

              </bs.Toolbar>)
          }
        },
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          }
        }
      },
    }
    return responsiveGridConfig(config);
  }

  onNewAction() {
    let { plugin } = this.props;
    let account = SESSION.getAccountAcl();
    let dueDate = new Date();
    dueDate.setHours(23, 59, 59, 999);
    let createDate = new Date();
    let newRecord = {
      salemanAccountId: account.getAccountId(),
      salemanLabel: account.getFullName(),
      taskType: SaleTaskType.OTHER.value,
      createdDate: util.TimeUtil.javaCompactDateTimeFormat(createDate),
      dueDate: util.TimeUtil.javaCompactDateTimeFormat(dueDate),
      status: SalesTaskStatus.IN_PROGRESS.value
    };

    plugin.getListModel().addRecord(newRecord);
    let state = grid.getRecordState(newRecord);
    state.markModified();

    this.getVGridContext().getVGrid().forceUpdateView();
  }

  onDefaultSelect(dRecord: grid.DisplayRecord) {
    let task = dRecord.record;
    const onPostCommit = (_appCtx: app.AppContext, pageCtx: app.PageContext, _entity: any) => {
      pageCtx.back();
      this.reloadData()
      this.forceUpdate();
    }
    UITaskCalendarUtils.showUITaskDetail(this, task, onPostCommit);
  }

  saveChanges = () => {
    let { appContext, pageContext, plugin } = this.props;
    let modifiedRecords = plugin.getListModel().getModifiedRecords();

    //TODO: Dan - Implement this method
    // enable field editor by default if user space, tracking (marked) modified row to update through onInputChange
    appContext.createHttpBackendCall('TaskCalendarService', 'saveSaleDailyTaskRecords', { records: modifiedRecords })
      .withSuccessData((data: any) => {
        appContext.addOSNotification("success", T(`Save modified records success!!!`));
        pageContext.back();
      })
      .call();
  }

  onDeleteAction() {
    let { plugin, appContext } = this.props;
    let ids = plugin.getListModel().getSelectedRecordIds();

    appContext.createHttpBackendCall('TaskCalendarService', 'deleteSalesDailyTaskByIds', { ids: ids })
      .withSuccessData((_data: any) => {
        appContext.addOSNotification("success", T(`Delete Success`));
        plugin.getListModel().removeSelectedDisplayRecords();
        this.getVGridContext().getVGrid().forceUpdateView();
      })
      .call();
  }

  handleOnChangeStatus(status: string, targetRecordIds: number[] = []) {
    const { appContext } = this.props;
    if (!targetRecordIds || targetRecordIds.length === 0) return;

    let modified: any[] = targetRecordIds.map(id => ({
      id: id,
      status: status
    }));

    let records = this.vgridContext.model.getRecords();
    // optimistic update records before saving.
    for (let rec of records) {
      if (targetRecordIds.includes(rec['id'])) {
        rec['status'] = status;
        grid.getRecordState(rec).markModified();
      }
    }
    appContext
      .createHttpBackendCall('TaskCalendarService', 'saveSaleDailyTaskRecords', { records: modified })
      .withSuccessData((updatedRecords: any[]) => {
        appContext.addOSNotification("success", T("Success"), T("Update task status success"));
        // this.reloadData();
        // this.vgridContext.getVGrid().forceUpdateView();
      })
      .call();
    this.vgridContext.getVGrid().forceUpdateView();

  }

}
