import React from "react";
import { util, entity, bs, input } from '@datatp-ui/lib';
import * as FeatherIcon from 'react-feather';

import { SalesTaskUtils, SaleTaskType, SaleTaskTypeUtils, T } from "../backend";
import { BBRefUserCustomer } from "../partners/BBRefUserCustomer";
import { BBRefCrmUserRole } from "app/crm/common/template/BBRefCrmUserRole";

export interface UISalesDailyTaskEditorProps extends entity.AppDbEntityEditorProps {
  onAction?: (action: 'Delete' | 'Archieve', targetSrc: any) => void;
}
export class UISalesDailyTaskEditor extends entity.AppDbEntityEditor<UISalesDailyTaskEditorProps> {
  state = {
    isSending: false
  };

  onPostUpdatePartner = (bean: any, selectOpt: any, userInput: string) => {
    if (selectOpt && Object.keys(selectOpt).length > 0) {
      bean.partnerType = selectOpt.type;
      bean.partnerId = selectOpt.id;
      bean.partnerLabel = selectOpt['partnerName'];
      bean.partnerAddress = selectOpt.address || '';
      bean.partnerOnboardingStatus = selectOpt.customerOnboardingProgress || '';
      bean.partnerVolumeDetails = selectOpt.volumeNote || selectOpt.volume;
      bean.partnerShippingRoute = selectOpt.routing || '';
    } else {
      bean.partnerType = 'CUSTOMERS';
      bean.partnerId = undefined;
      bean.partnerLabel = userInput;
      bean.partnerAddress = '';
      bean.partnerOnboardingStatus = '';
      bean.partnerVolumeDetails = '';
      bean.partnerShippingRoute = '';
    }
    const { observer } = this.props;
    observer.setMutableBean(bean);
    this.forceUpdate();
  }

  private handleNotificationChange = (bean: any, field: 'sendingEmail' | 'sendingZalo', newVal: boolean) => {
    bean[field] = newVal;
    const hasAnyNotification = bean.sendingEmail || bean.sendingZalo;
    if (!hasAnyNotification) {
      bean.notificationTime = null;
    } else if (newVal && !bean.notificationTime) {
      bean.notificationTime = util.TimeUtil.javaCompactDateTimeFormat(new Date());
    }
    this.forceUpdate();
  }

  onRemove = () => {
    const { appContext, pageContext, observer, onAction } = this.props;
    let task = observer.getMutableBean();
    appContext.createHttpBackendCall("TaskCalendarService", "deleteSalesDailyTaskByIds", { ids: [task['id']] })
      .withSuccessData((result: any) => {
        appContext.addOSNotification("success", T('Delete Task Success!!!!'));
        if (onAction) onAction('Delete', task);
        else pageContext.back();
      })
      .call()
  }

  onPreCommit = (observer: entity.ComplexBeanObserver) => {
    let task = observer.getMutableBean();
    if (this.requirePartner()) {
      let partnerName: string = task['partnerLabel'];
      if (!partnerName) {
        bs.dialogShow('Error',
          <div className="text-danger fw-bold text-center py-3 border-bottom">
            <FeatherIcon.AlertCircle className="mx-2" />{T('Please enter Partner.')}
          </div>,
          { backdrop: 'static', size: 'md' }
        );
        throw new Error('Please enter Partner..')
      }

      if (!task['label']) {
        task['label'] = `${partnerName} (${SaleTaskTypeUtils.getTypeInfo(task['taskType']).label})`
      }
    }

    if (!task['label']) {
      bs.dialogShow('Error',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />{T('Please enter a title for the task.')}
        </div>,
        { backdrop: 'static', size: 'md' }
      );
      throw new Error('Please enter a title for the task..')
    }

    this.setState({ isSending: true });
  }

  requirePartner = () => {
    let { observer } = this.props;
    let task = observer.getMutableBean();
    let taskType = SaleTaskTypeUtils.getTypeInfo(task['taskType']);
    return (SaleTaskType.MEET_CUSTOMER === taskType || SaleTaskType.QUOTATION === taskType || SaleTaskType.TROUBLE_SHOOTING === taskType)
  }

  onPostCommit = (entity: any) => {
    let { onPostCommit } = this.props;
    this.nextViewId();
    this.setState({ isSending: false });
    if (onPostCommit) {
      onPostCommit(entity, this);
    } else {
      this.forceUpdate();
    }
  }

  render() {
    let { appContext, pageContext, observer, readOnly } = this.props;
    let writeCap = pageContext.hasUserWriteCapability() && !readOnly;
    const moderatorCap = pageContext.hasUserModeratorCapability();
    let task = observer.getMutableBean();
    let typeOpts = SaleTaskTypeUtils.getTaskTypeList();
    const statusOpts = SalesTaskUtils.getSalesTaskStatusList();

    return (
      <div className="flex-vbox">
        <div className='flex-vbox justify-content-start p-1'>
          <bs.Row>
            <bs.Col span={6}>
              <input.BBSelectField className="" label='Task type' bean={task} field={"taskType"}
                options={typeOpts.map(type => type.value)}
                optionLabels={typeOpts.map(type => type.label)}
                disable={!writeCap}
                onInputChange={(bean, field, _oldVal, newVal) => {
                  bean[field] = newVal;
                  if (newVal === 'MEETING') {
                    const today = new Date();
                    const formattedDate = util.text.formater.date(today);
                    bean.label = `Meeting Note at ${formattedDate}`;
                  }
                  this.forceUpdate();
                }} />
            </bs.Col>
            <bs.Col span={6}>
              <input.BBSelectField className="" label='Status' bean={task} field={"status"}
                options={statusOpts.map(status => status.value)}
                optionLabels={statusOpts.map(status => status.label)} disable={!writeCap} />
            </bs.Col>
          </bs.Row>

          <bs.Row>
            <bs.Col span={6}>
              <input.BBDateTimeField bean={task} label={T('Created date')} field={'createdDate'} timeFormat={true} disable />
            </bs.Col>
            <bs.Col span={6}>
              <input.BBDateTimeField bean={task} label={T('Due date')} field={'dueDate'} timeFormat={true} disable />
            </bs.Col>
          </bs.Row>

          {
            (pageContext.hasUserModeratorCapability() && observer.isNewBean())
            &&
            <BBRefCrmUserRole hideMoreInfo minWidth={400}
              appContext={appContext} pageContext={pageContext} bean={task}
              beanIdField='salemanAccountId' beanLabelField='salemanLabel'
              label='Salemans' placeholder='Salemans'
              onPostUpdate={(_inputUI, bean, selectOpt, _userInput) => {
                bean['salemanAccountId'] = selectOpt['accountId'];
                bean['salemanLabel'] = selectOpt['fullName'];
              }} />
          }

          {!this.requirePartner() && (
            <input.BBStringField bean={task} label={T('Title')} field="label" disable={!writeCap} required />
          )}

          {task.taskType !== 'MEETING' && (
            <bs.Row>
              <bs.Col span={12}>
                <BBRefUserCustomer placeholder='Lead/ Customer / Agent ...' label='Partner' allowUserInput hideMoreInfo
                  appContext={appContext} pageContext={pageContext} disable={!writeCap} minWidth={500} offset={[-200, 0]}
                  bean={task} beanIdField={'partnerId'} beanLabelField={'partnerLabel'}
                  partnerTypes={['CUSTOMERS', 'AGENTS', 'CUSTOMER_LEAD', 'AGENTS_APPROACHED']}
                  onPostUpdate={(_input: any, bean: any, selectOpt: any, userInput: string) => this.onPostUpdatePartner(bean, selectOpt, userInput)} />
              </bs.Col>
            </bs.Row>
          )}
          {/* Hiển thị thông tin chi tiết của Lead hoặc Customer */}
          {task.partnerLabel && (
            <div className="flex-vbox" key={util.IDTracker.next()}>
              <bs.Row>
                <bs.Col span={6}>
                  <input.BBTextField
                    bean={task} label={T('Partner Address')} field="partnerAddress" disable={!writeCap} />
                </bs.Col>
                <bs.Col span={6}>
                  <input.BBTextField
                    bean={task} label={'Sales Deployment Progress - Compared to Last Week (%'}
                    field="partnerOnboardingStatus" disable={!writeCap} />
                </bs.Col>
              </bs.Row>

              <bs.Row>
                <bs.Col span={6}>
                  <input.BBTextField bean={task} label={'Product/Route'}
                    field="partnerShippingRoute" disable={!writeCap} placeholder={'Enter product or route...'} />
                </bs.Col>
                <bs.Col span={6}>
                  <input.BBTextField
                    bean={task} label={T('Volume')} field="partnerVolumeDetails" disable={!writeCap} />
                </bs.Col>
              </bs.Row>
            </div>
          )}

          <bs.CssTooltip position='top-right' width={500} offset={{ x: 0, y: 20 }}>
            <bs.CssTooltipToggle className="d-flex w-100">
              <input.BBTextField
                bean={task} label={T('Description')} field="description" disable={!writeCap}
                style={{
                  height: task.taskType === 'MEETING' ? '25rem' : '15rem',
                  fontSize: task.taskType === 'MEETING' ? '0.9rem' : '0.85rem',
                }}
                onInputChange={(bean, field, _oldVal, newVal) => {
                  bean[field] = newVal;
                  this.forceUpdate();
                }} />
            </bs.CssTooltipToggle>
            <bs.CssTooltipContent className="d-flex flex-column rounded">
              <div className="tooltip-header mb-2">
                <span className="tooltip-title">{T('Description')}:</span>
              </div>
              <div className="tooltip-body">
                {(task['description'] || '...').split('\n').map((line: string, i: number) => (
                  <li key={i}>{line}</li>
                ))}
              </div>
            </bs.CssTooltipContent>
          </bs.CssTooltip>

          {task.taskType !== 'MEETING' && (
            <input.BBTextField
              bean={task} label={T('Suggested Support')} field="suggestedSupport" disable={!writeCap} style={{ height: '3rem' }} />
          )}

          <div className="d-flex align-items-center gap-4 p-2 border rounded bg-light">
            <label className="d-flex align-items-center gap-2 mb-0 cursor-pointer hover-opacity">
              <input.BBCheckboxField className='text-primary' bean={task} field='sendingEmail' value={false}
                onInputChange={(bean, field, _oldVal, newVal) => this.handleNotificationChange(bean, 'sendingEmail', newVal)} />
              <span className="fw-medium">{T('Send Email')}</span>
            </label>
            <label className="d-flex align-items-center gap-2 mb-0 cursor-pointer hover-opacity">
              <input.BBCheckboxField className='text-primary' bean={task} field='sendingZalo' value={false}
                onInputChange={(bean, field, oldVal, newVal) => { this.handleNotificationChange(bean, 'sendingZalo', newVal) }} />
              <span className="fw-medium">{T('Send Zalo')}</span>
            </label>

            {(task.sendingEmail || task.sendingZalo) && (
              <div className="d-flex align-items-center gap-2 border-start ps-4">
                <span className="form-label fw-medium mb-0 text-nowrap text-secondary">{T('Notification Time')}:</span>
                <input.BBDateInputMask bean={task} field={'notificationTime'} format='DD/MM/YYYY' timeFormat
                  className="flex-grow-1 form-control-sm" style={{ minWidth: '160px' }}
                  onInputChange={(bean, field, oldVal, newVal) => { bean[field] = newVal; this.forceUpdate(); }} />

              </div>
            )}
          </div>
        </div>

        <bs.Toolbar className='border'>

          <bs.Button laf='warning' outline className="px-2 py-1 mx-1" style={{ width: 120 }}
            hidden={!writeCap || observer.isNewBean()} onClick={this.onRemove}>
            <FeatherIcon.X size={12} /> Remove
          </bs.Button>

          <entity.ButtonEntityCommit btnLabel={`${this.state.isSending ? 'Saving...' : 'Save'}`}
            appContext={appContext} pageContext={pageContext} style={{ width: 120 }}
            observer={observer} hide={!writeCap}
            disable={this.state.isSending}
            commit={{
              entityLabel: T('Daily Task'),
              context: 'company',
              service: 'TaskCalendarService', commitMethod: 'handleSalesDailyTask'
            }}
            onPreCommit={this.onPreCommit}
            onPostCommit={this.onPostCommit} />
        </bs.Toolbar>
      </div>
    )
  }
}
