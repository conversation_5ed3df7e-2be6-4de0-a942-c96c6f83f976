import React from 'react';
import * as FeatherIcon from 'react-feather';
import { bs, app, util, grid, input, entity } from '@datatp-ui/lib';
import { WQuickTimeRangeSelector, WTaskGroupBySelector } from '../../common/UIDashboardUtility';
import { SaleDailyTaskTree } from './SaleDailyTaskTreeList';
import { UISaleDailyTaskList, UISaleDailyTaskListPlugin } from './UISaleDailyTaskList';
import { SalesTaskUtils, SaleTaskTypeUtils } from '../backend';
import { UIPartnerEventHistoryList, UIPartnerEventHistoryListPlugin } from '../leads/UIPartnerEventHistoryList';
import { UISalesDailyTaskEditor } from './UIDailyTaskEditor';

import IDTracker = util.IDTracker;

export type EventBean = {
  partnerId: number;
  partnerCode: string;
  taxCode: string;
  partnerLabel: string;
  address: string;
  kcnLabel: string;
  partnerType: 'forwarder_customer_leads' | 'bfsone_partner';
}

export class UITaskCalendarUtils {

  static showUIEventHistory(ui: app.AppComponent | entity.BBRefEntity, bean: EventBean): void {
    const { pageContext } = ui.props;
    let type = bean['partnerType'];

    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <div className='flex-vbox h-100 p-1'>
          <bs.Row>
            <bs.Col span={3}>
              <input.BBStringField
                bean={bean} field={'partnerCode'} label={'Partner Code'} disable />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBStringField
                bean={bean} field='taxCode' label={'Tax Code'} disable />
            </bs.Col>
            <bs.Col span={6}>
              <input.BBStringField bean={bean} field='partnerLabel' label={'Partner Label'} disable />
            </bs.Col>
          </bs.Row>
          <bs.Row>
            <bs.Col span={6}>
              <input.BBTextField bean={bean} field='kcnLabel' label={'Industrial Park'} disable />
            </bs.Col>
            <bs.Col span={6}>
              <input.BBTextField bean={bean} field='address' label={'Address'} disable />
            </bs.Col>
          </bs.Row>
          <UIPartnerEventHistoryList
            appContext={appCtx} pageContext={pageCtx}
            plugin={new UIPartnerEventHistoryListPlugin().withReferencePartner(bean.partnerId, type)}
            referencePartnerId={bean.partnerId} referencePartnerType={type} hideToolbar={true} readOnly />
        </div>
      )
    }

    let pupupLabel: string = `Partner Event History`;
    pageContext.createPopupPage(`partner-event-history-${IDTracker.next()}`, pupupLabel, createAppPage, { size: 'lg', backdrop: 'static' });
  }

  static showUITaskDetail(ui: app.AppComponent | entity.BBRefEntity, task: any,
    onPostCommit?: (appCtx: app.AppContext, pageCtx: app.PageContext, _entity: any) => void): void {
    const { appContext, pageContext } = ui.props;

    appContext.createHttpBackendCall('TaskCalendarService', 'getSalesDailyTaskById', { id: task['id'] })
      .withSuccessData((data: any) => {
        let observer = new entity.BeanObserver(data);
        let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (
            <UISalesDailyTaskEditor
              appContext={appCtx} pageContext={pageCtx} observer={observer} onPostCommit={(entity: any, uiEditor?: bs.BaseComponent) => {
                if (onPostCommit) {
                  onPostCommit(appCtx, pageCtx, entity);
                } else {
                  pageCtx.back();
                }
              }} />
          );
        };
        pageContext.createPopupPage("sale-daily-task", `${"Sale Daily Task"}: ${task.label}`, createAppPage, { size: 'flex-lg', backdrop: 'static' });
      })
      .call();
  }

}

interface UIInteractivePanelProps extends app.AppComponentProps { }
class UIInterractivePanel extends app.AppComponent<UIInteractivePanelProps> {
  activeView: 'quick-acts' | 'leader-board' | 'view-performance'

  constructor(props: UIInteractivePanelProps) {
    super(props);
    this.activeView = (localStorage.getItem('enableTaskCalendarPanel') as 'quick-acts' | 'leader-board' | 'view-performance') || 'quick-acts';
  }

  onToggleView = (view: 'quick-acts' | 'leader-board' | 'view-performance') => {
    this.activeView = view;
    localStorage.setItem('enableTaskCalendarPanel', view);
    this.forceUpdate();
  }

  renderLeaderBoard() {
    if (this.activeView !== 'leader-board') return <></>
    return (
      <div className='flex-hbox'>
        <div className="card flex-vbox flex-grow-1 shadow-sm border-0" style={{ minWidth: 260 }}>
          <div className="card-body py-2 px-3">
            <div className="d-flex align-items-center mb-2">
              <FeatherIcon.Star className="me-2 text-warning" size={18} />
              <h6 className="mb-0 fw-bold">Tổng điểm Sales</h6>
            </div>
            <div className="text-muted small">
              Tính tổng điểm dựa trên các task đã hoàn thành, đúng hạn, chất lượng feedback...
            </div>
          </div>
        </div>
        <div className="card flex-vbox flex-grow-1 shadow-sm border-0" style={{ minWidth: 260 }}>
          <div className="card-body py-2 px-3">
            <div className="d-flex align-items-center mb-2">
              <FeatherIcon.Award className="me-2 text-primary" size={18} />
              <h6 className="mb-0 fw-bold">Bảng xếp hạng Sales</h6>
            </div>
            <div className="text-muted small">
              Hiển thị bảng xếp hạng sales theo điểm số.
            </div>
          </div>
        </div>
        <div className="card flex-vbox flex-grow-1 shadow-sm border-0" style={{ minWidth: 260 }}>
          <div className="card-body py-2 px-3">
            <div className="d-flex align-items-center mb-2">
              <FeatherIcon.Info className="me-2 text-success" size={18} />
              <h6 className="mb-0 fw-bold">Chi tiết điểm số</h6>
            </div>
            <div className="text-muted small">
              Có thể thêm nút "Xem chi tiết điểm số" khi chọn sales.
            </div>
          </div>
        </div>
      </div>
    )
  }

  renderQuickActs() {
    if (this.activeView !== 'quick-acts') return <></>
    return (
      <div className='flex-hbox'>
        <div className="card flex-vbox flex-grow-1 shadow-sm border-0" style={{ minWidth: 260 }}>
          <div className="card-body py-2 px-3">
            <div className="d-flex align-items-center mb-2">
              <FeatherIcon.Star className="me-2 text-warning" size={18} />
              <h6 className="mb-0 fw-bold">Tổng điểm Sales</h6>
            </div>
            <div className="text-muted small">
              Tính tổng điểm dựa trên các task đã hoàn thành, đúng hạn, chất lượng feedback...
            </div>
          </div>
        </div>
        <div className="card flex-vbox flex-grow-1 shadow-sm border-0" style={{ minWidth: 260 }}>
          <div className="card-body py-2 px-3">
            <div className="d-flex align-items-center mb-2">
              <FeatherIcon.Award className="me-2 text-primary" size={18} />
              <h6 className="mb-0 fw-bold">Bảng xếp hạng Sales</h6>
            </div>
            <div className="text-muted small">
              Hiển thị bảng xếp hạng sales theo điểm số.
            </div>
          </div>
        </div>
        <div className="card flex-vbox flex-grow-1 shadow-sm border-0" style={{ minWidth: 260 }}>
          <div className="card-body py-2 px-3">
            <div className="d-flex align-items-center mb-2">
              <FeatherIcon.Info className="me-2 text-success" size={18} />
              <h6 className="mb-0 fw-bold">Chi tiết điểm số</h6>
            </div>
            <div className="text-muted small">
              Có thể thêm nút "Xem chi tiết điểm số" khi chọn sales.
            </div>
          </div>
        </div>
      </div>
    )
  }

  renderViewPerformance() {
    if (this.activeView !== 'view-performance') return <></>
    return (
      <div className='flex-vbox'>

      </div>
    )
  }

  render(): React.ReactNode {
    const { appContext, pageContext } = this.props;

    return (
      <div className="flex-vbox flex-grow-0 align-items-center justify-content-between border-top" style={{ maxHeight: 300, minHeight: 300 }}>

        <div className='flex-hbox flex-grow-0 border-top justify-content-start align-items-center w-100' >

          <div className='mt-1 py-1 px-2 flex-hbox flex-grow-0 align-items-center justify-content-start'>
            <h5 style={{ color: '#6c757d' }}>
              <FeatherIcon.Slack className="me-2" size={18} />
              Interactive Panel
            </h5>
          </div>

          <div className="flex-hbox justify-content-end align-items-center flex-grow-1 gap-1 ms-2 " >

            <div className={'btn-group flex-hbox-grow-0 align-self-end'} role="group" aria-label={'Button Group'}>

              <bs.Button laf={'secondary'} outline className={'p-1'} style={{ minWidth: '100px' }}
                disabled={this.activeView === 'quick-acts'}
                onClick={() => this.onToggleView('quick-acts')} >
                <FeatherIcon.FastForward size={14} className="me-1" /> Quick Acts
              </bs.Button>

              <bs.Button laf={'secondary'} outline className={'p-1'} style={{ minWidth: '100px' }}
                disabled={this.activeView === 'leader-board'}
                onClick={() => this.onToggleView('leader-board')} >
                <FeatherIcon.BookOpen size={14} className="me-1" /> Leaderboard
              </bs.Button>

              <bs.Button laf={'secondary'} outline className={'p-1'} style={{ minWidth: '100px' }}
                disabled={this.activeView === 'view-performance'}
                onClick={() => this.onToggleView('view-performance')} >
                <FeatherIcon.Layers size={14} className="me-1" /> View Performance / History
              </bs.Button>
            </div>
          </div>
        </div>

        <div className='flex-hbox border-top border-dashed w-100 h-100 gap-3 py-3 px-2'>
          {this.renderQuickActs()}
          {this.renderLeaderBoard()}
          {this.renderViewPerformance()}
        </div>

      </div>
    )
  }
}


interface FilterParam {
  dateFilter: { fromValue: string, toValue: string, label: string };
  groupedBy: { label: string, value: string };
  enablePanel: boolean;
}

export interface UITaskCalendarPageProps extends app.AppComponentProps { }
interface UITaskCalendarPageState { }

export class UITaskCalendarPage extends app.AppComponent<UITaskCalendarPageProps, UITaskCalendarPageState> {
  selectedRecord: any;

  reportFilter: FilterParam;
  private saleDailyTaskTreeRef: React.RefObject<SaleDailyTaskTree>;
  private saleDailyTaskListRef: React.RefObject<UISaleDailyTaskList>;

  viewId: number = util.IDTracker.next();

  constructor(props: UITaskCalendarPageProps) {
    super(props);

    this.saleDailyTaskTreeRef = React.createRef<SaleDailyTaskTree>();
    this.saleDailyTaskListRef = React.createRef<UISaleDailyTaskList>();

    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    let dateFilter = new util.TimeRange();
    dateFilter.fromSetDate(firstDayOfMonth);
    dateFilter.toSetDate(lastDayOfMonth);

    this.reportFilter = {
      dateFilter: { fromValue: dateFilter.fromFormat(), toValue: dateFilter.toFormat(), label: 'This Month' },
      groupedBy: { label: 'Saleman', value: 'Saleman' },
      enablePanel: localStorage.getItem('enableDailyTaskPanel') === 'true'
    }

    this.loadData();
  }

  doExport = () => {
    let { appContext } = this.props;

    if (!this.saleDailyTaskTreeRef.current && !this.saleDailyTaskListRef.current) {
      bs.dialogShow('Error',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />{'This action cannot be performed right now.'}
        </div>,
        { backdrop: 'static', size: 'md' }
      );
      return;
    }

    let uiRoot = this.saleDailyTaskTreeRef.current || this.saleDailyTaskListRef.current;
    if (!uiRoot) return;

    let { plugin } = uiRoot.props;
    let pluginImpl = plugin as UISaleDailyTaskListPlugin;
    let targetRecords = pluginImpl.getListModel().getFilterRecords();

    let salemanGroupField: entity.GroupField = {
      label: "_blank_",
      fields: [
        { label: "STT.", name: "stt", dataType: 'number' },
        { label: "Saleman", name: "salemanLabel", dataType: 'string' },
        { label: "Task Type", name: "taskType", dataType: 'string' },
        { label: "Partner Type", name: "partnerType", dataType: 'string' },
        { label: "Partner Name", name: "partnerLabel", dataType: 'string' },
        { label: "Partner Address", name: "partnerAddress", dataType: 'string' },
        { label: "Task Label", name: "taskLabel", dataType: 'string' },
        { label: "Description", name: "description", dataType: 'string' },
        { label: "Route", name: "partnerShippingRoute", dataType: 'string' },
        { label: "Volume", name: "partnerVolumeDetails", dataType: 'string' },
        { label: "Onboarding Status", name: "partnerOnboardingStatus", dataType: 'string' },
        { label: "Suggested Support", name: "suggestedSupport", dataType: 'string' },
        { label: "Created Date", name: "createdDate", dataType: 'string' },
        { label: "Due Date", name: "dueDate", dataType: 'string' },
        { label: "Status", name: "status", dataType: 'string' },
      ],
    }

    let records: Array<any> = [];
    for (let i = 0; i < targetRecords.length; i++) {
      let rec: any = targetRecords[i];
      let childRecData = {
        stt: records.length + 1,
        salemanLabel: rec['salemanLabel'],
        taskType: SaleTaskTypeUtils.getTypeInfo(rec['taskType']).label,
        partnerType: rec['partnerType'],
        partnerLabel: rec['partnerLabel'],
        partnerAddress: rec['partnerAddress'],
        taskLabel: rec['taskLabel'] || rec['label'],
        description: rec['description'] || '',
        partnerShippingRoute: rec['partnerShippingRoute'] || '',
        partnerVolumeDetails: rec['partnerVolumeDetails'] || '',
        partnerOnboardingStatus: rec['partnerOnboardingStatus'] || '',
        suggestedSupport: rec['suggestedSupport'],
        createdDate: util.text.formater.compactDate(rec['createdDate']),
        dueDate: util.text.formater.compactDate(rec['dueDate']),
        status: SalesTaskUtils.getStatusInfo(rec['status']).label,
      }
      records.push(childRecData);
    }

    let exportModel: entity.DataListExportModel = {
      fieldGroups: [],
      fields: [...salemanGroupField.fields],
      records: records,
      modelName: 'CRM_Tasks',
      fileName: `CRM_Tasks_${util.TimeUtil.toDateIdFormat(new Date())}.xlsx`,
    }

    appContext.createHttpBackendCall('DataMappingService', 'xlsxPrivateExport', { model: exportModel })
      .withSuccessData((data: any) => {
        let storeInfo = data;
        if (storeInfo.type == 'ir.actions.act_url') {
          entity.StoreInfo.download(storeInfo.url);
        } else if (storeInfo.privateMode) {
          entity.StoreInfo.privateDownload(storeInfo);
        } else {
          entity.StoreInfo.download(storeInfo.url);
        }
      })
      .call()
  }


  renderHeader() {
    const { appContext, pageContext } = this.props;
    const { dateFilter } = this.reportFilter;

    return (
      <div className="flex-hbox flex-grow-0 align-items-center justify-content-between px-2 py-1">

        <div className="flex-hbox justify-content-start align-items-center flex-grow-0 gap-2" >
          <h5 style={{ color: '#6c757d' }}>
            <FeatherIcon.Trello className="me-2" size={18} />
            Task Activity Overview
          </h5>

          <div className='flex-hbox align-items-center flex-grow-0 border-start' >
          </div>
        </div>

        <div className="flex-hbox justify-content-end align-items-center flex-grow-1 gap-1" >

          <WQuickTimeRangeSelector appContext={appContext} pageContext={pageContext}
            initBean={dateFilter}
            onModify={(bean: any, _field: string, _oldVal: any, _newVal: any) => {
              this.reportFilter.dateFilter = bean;
              this.loadData();
            }} />

          <WTaskGroupBySelector appContext={appContext} pageContext={pageContext}
            onModify={(bean: any, _field: string, _oldVal: any, _newVal: any) => {
              this.reportFilter.groupedBy = bean;
              this.forceUpdate();
              // this.loadData();
            }} />

          <div className='flex-hbox align-items-center flex-grow-0 px-2 border border-secondary rounded-md'>
            <div className="form-check form-switch d-flex align-items-center my-0">
              <input className="form-check-input mt-0 me-2" type="checkbox"
                role="switch" id="quotationPanelToggle" checked={this.reportFilter.enablePanel}
                onChange={() => {
                  let _newVal: any = !this.reportFilter.enablePanel;
                  this.reportFilter.enablePanel = _newVal;
                  localStorage.setItem('enableDailyTaskPanel', _newVal);
                  this.forceUpdate();
                }} />
              <label className="form-check-label mb-0 d-flex align-items-center" htmlFor="quotationPanelToggle" >
                <span>{'Toggle Panel'}</span>
              </label>
            </div>
          </div>

          <bs.Button laf="secondary" outline size="sm" className="p-1" style={{ color: '#6c757d' }}
            onClick={this.doExport}>
            <FeatherIcon.Download size={14} className="me-2" />
            Export
          </bs.Button>
        </div>
      </div>
    )
  }

  onViewDetail = (appContext: app.AppContext, _pageContext: app.PageContext, entity: any) => {
    const partnerId = entity['partnerId']

    if (partnerId) {
      const isLead = entity['partnerType'] === 'CUSTOMER_LEAD' || entity['partnerType'] === 'AGENTS_APPROACHED';
      appContext.createHttpBackendCall(
        isLead ? 'CustomerLeadsService' : 'CRMPartnerService',
        isLead ? 'getCustomerLeadById' : 'getCRMPartner',
        { id: entity['partnerId'] })
        .withSuccessData((bean: any) => {
          //TODO: An - review this code.
          let eventBean: EventBean = {
            partnerId: bean.id,
            partnerCode: isLead ? bean['code'] : bean['partnerCode'],
            taxCode: bean['taxCode'],
            partnerLabel: bean['name'],
            partnerType: isLead ? 'forwarder_customer_leads' : 'bfsone_partner',
            address: bean['address'],
            kcnLabel: bean['kcnLabel']
          }
          UITaskCalendarUtils.showUIEventHistory(this, eventBean);
        })
        .call();
    } else {
      entity['id'] = entity['referenceEntityId']
      UITaskCalendarUtils.showUITaskDetail(this, entity);
    }
  }

  render(): React.JSX.Element {
    const { appContext, pageContext } = this.props;
    const { dateFilter, groupedBy, enablePanel } = this.reportFilter;

    let plugin = new UISaleDailyTaskListPlugin('Company', undefined);
    plugin.withDueDate(dateFilter.fromValue, dateFilter.toValue);

    return (
      <div className="flex-vbox mx-1 p-1 bg-white rounded-md w-100 h-100 my-1" >
        {this.renderHeader()}
        <div className="flex-vbox border-top bg-body-highlight" key={util.IDTracker.next()}>
          {groupedBy.value === 'NONE'
            ? <UISaleDailyTaskList ref={this.saleDailyTaskListRef} hideFooter={true} hideToolbar={true}
              appContext={appContext} pageContext={pageContext} plugin={plugin} space={'Company'} />
            : <SaleDailyTaskTree ref={this.saleDailyTaskTreeRef} groupBy={groupedBy.value as any}
              appContext={appContext} pageContext={pageContext} plugin={plugin}
              onSelect={this.onViewDetail} />
          }
        </div>
        {enablePanel && <UIInterractivePanel appContext={appContext} pageContext={pageContext} />}
      </div>
    )
  }

}

