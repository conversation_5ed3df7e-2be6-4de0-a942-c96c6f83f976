import React from "react";
import { entity, grid, app, bs, util } from "@datatp-ui/lib";
import { module } from '@datatp-ui/erp';
import * as FeatherIcon from 'react-feather';
import { SalesTaskUtils, SaleTaskTypeUtils, T } from "../backend";
import { responsiveGridConfig } from "../common";
import { UITaskCalendarUtils } from "./UITaskCalendarPage";

export interface SaleDailyTaskTreeProps extends entity.DbEntityListProps {
  groupBy: 'SALEMAN' | 'TEAM';
}

export class SaleDailyTaskTree extends entity.DbEntityList<SaleDailyTaskTreeProps> {

  onViewDetailTask = (dRecord: grid.DisplayRecord) => {
    const task: any = dRecord.record;
    task['id'] = task['referenceEntityId'];
    if (task['id']) {
      const onPostCommit = (_appCtx: app.AppContext, pageCtx: app.PageContext, _entity: any) => {
        pageCtx.back();
        // this.reloadData()
        this.forceUpdate();
      }
      UITaskCalendarUtils.showUITaskDetail(this, task, onPostCommit);
    }
  }

  createVGridConfig(): grid.VGridConfig {
    const { groupBy } = this.props;
    const CELL_HEIGHT: number = 40;

    let config: grid.VGridConfig = {
      record: {
        control: {
          width: 40,
          items: [
            {
              name: 'view-detail', hint: 'View Detail', icon: FeatherIcon.ExternalLink,
              onClick: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
                let uiList = ctx.uiRoot as SaleDailyTaskTree;
                uiList.onViewDetailTask(dRecord);
              },
            },
          ]
        },
        dataCellHeight: CELL_HEIGHT,
        fields: [
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          {
            name: 'label', label: T(`Label`), width: 400, filterable: true,
            fieldDataGetter: (record: any) => {
              return record['departmentLabel'] || record['salemanLabel'] || 'N/A';
            },
            customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;

              let uiList = ctx.uiRoot as SaleDailyTaskTree;
              const { appContext, pageContext } = uiList.props;
              let type = record['groupType'];

              const onCollapseRecord = (dRecord: grid.DisplayRecord) => {
                dRecord.model['collapse'] = !dRecord.model['collapse'];
                let displayRecordList = ctx.model.getDisplayRecordList();
                if (displayRecordList instanceof grid.TreeDisplayModel) {
                  displayRecordList.updateDisplayRecords();
                  ctx.getVGrid().forceUpdateView();
                }
              }

              if (type === 'SALEMAN') {
                let employeeName: string = record['salemanLabel'] || 'N/A';

                return (
                  <div className='flex-hbox justify-content-center align-items-center' onClick={() => onCollapseRecord(dRecord)}>
                    <module.account.WAvatars className='px-2'
                      appContext={appContext} pageContext={pageContext} avatarIds={[record['salemanAccountId']]}
                      avatarIdType='AccountId' width={20} height={20} borderRadius={10} />
                    <div className="flex-hbox" style={{
                      cursor: 'pointer',
                      userSelect: 'text',
                      WebkitUserSelect: 'text',
                      MozUserSelect: 'text',
                      msUserSelect: 'text'
                    }}
                      onContextMenu={(e) => {
                        e.stopPropagation();
                        const range = document.createRange();
                        range.selectNodeContents(e.currentTarget);
                        const selection = window.getSelection();
                        selection?.removeAllRanges();
                        selection?.addRange(range);
                      }} >
                      {employeeName}
                    </div>
                  </div>
                )
              } else if (type === 'TEAM') {
                return (
                  <div className='flex-hbox justify-content-center align-items-center' onClick={() => onCollapseRecord(dRecord)}>
                    <div className="flex-hbox text-warning fw-bold" style={{
                      cursor: 'pointer',
                      userSelect: 'text',
                      WebkitUserSelect: 'text',
                      MozUserSelect: 'text',
                      msUserSelect: 'text'
                    }}
                      onContextMenu={(e) => {
                        e.stopPropagation();
                        const range = document.createRange();
                        range.selectNodeContents(e.currentTarget);
                        const selection = window.getSelection();
                        selection?.removeAllRanges();
                        selection?.addRange(range);
                      }} >
                      {record['label']}
                    </div>
                  </div>
                )
              } else {

                let val = record[field.name] || 'N/A';
                if (record['partnerId']) {
                  val = record['partnerLabel'];
                }
                let cssCustom = record['partnerId'] ? 'text-warning' : ''

                return (
                  <bs.CssTooltip position="bottom-right">
                    <bs.CssTooltipToggle className={`flex-hbox justify-content-start h-100 w-100 ${cssCustom}`}>
                      <div className="flex-grow-1" onClick={() => uiList.onSelect(dRecord)}>
                        {util.text.formater.uiTruncate(val, 370, true)}
                      </div>
                    </bs.CssTooltipToggle>
                    <bs.CssTooltipContent className='flex-vbox'>
                      <div className="tooltip-body">
                        {val}
                      </div>
                    </bs.CssTooltipContent>
                  </bs.CssTooltip>
                )
              }
            },
          },
          {
            name: 'taskType', label: T('Task Type'), width: 140, filterable: true,
            fieldDataGetter: (record: any) => {
              let taskType = record['taskType'];
              if (taskType === '-') return '';
              let taskTypeInfo = SaleTaskTypeUtils.getTypeInfo(taskType);
              let label = taskTypeInfo.label;
              return label;
            },
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let taskType = record['taskType'];
              if (taskType === '-') {
                return (
                  <div className="flex-hbox justify-content-center align-items-center">
                    {taskType}
                  </div>
                )
              }
              let taskTypeInfo = SaleTaskTypeUtils.getTypeInfo(taskType);
              let TaskIcon = taskTypeInfo.icon;
              let label = taskTypeInfo.label;
              let color = taskTypeInfo.color;
              return (
                <div className={`flex-hbox flex-center px-2 py-2 rounded-2 bg-${color}-subtle text-${color} w-100 `}>
                  <TaskIcon size={14} className="me-1" />
                  <span>{label}</span>
                </div>
              );
            },
          },
          {
            name: 'createdDate', label: T('Created Date'), width: 120, filterable: true, filterableType: 'date',
            fieldDataGetter: (record: any) => {
              if (record['createdDate'] === '-') return record['createdDate'];
              return util.text.formater.compactDate(record['createdDate']);
            },
            cssClass: 'justify-content-center align-items-center',
          },
          {
            name: 'description', label: T('Description'), width: 400, style: { height: CELL_HEIGHT },
            customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
              let record: any = dRecord.record;
              if (record[field.name] === '-') {
                return <div className="flex-hbox justify-content-center align-items-center">{record[field.name]}</div>
              } else {
                let val = record['description'] || '';

                // Tạo tooltip với thông tin chi tiết
                let htmlLines: any[] = []

                // Description chính
                if (val) {
                  htmlLines.push(
                    <div className='flex-vbox border-0 mb-2'>
                      <div className="tooltip-header">
                        <span className="tooltip-title fw-bold">Description:</span>
                      </div>
                      <div className="tooltip-body">
                        {val.split('\n').map((line: any, i: any) => <div key={i}>{line}</div>)}
                      </div>
                    </div>
                  );
                }

                // Task Label
                if (record['taskLabel']) {
                  htmlLines.push(
                    <div className='flex-vbox border-0 mb-2'>
                      <div className="tooltip-header">
                        <span className="tooltip-title fw-bold">Task Label:</span>
                      </div>
                      <div className="tooltip-body">
                        {record['taskLabel']}
                      </div>
                    </div>
                  );
                }

                // Partner Shipping Route
                if (record['partnerShippingRoute']) {
                  htmlLines.push(
                    <div className='flex-vbox border-0 mb-2'>
                      <div className="tooltip-header">
                        <span className="tooltip-title fw-bold">Shipping Route:</span>
                      </div>
                      <div className="tooltip-body">
                        {record['partnerShippingRoute']}
                      </div>
                    </div>
                  );
                }

                // Partner Volume Details
                if (record['partnerVolumeDetails']) {
                  htmlLines.push(
                    <div className='flex-vbox border-0 mb-2'>
                      <div className="tooltip-header">
                        <span className="tooltip-title fw-bold">Volume Details:</span>
                      </div>
                      <div className="tooltip-body">
                        {record['partnerVolumeDetails']}
                      </div>
                    </div>
                  );
                }

                // Partner Onboarding Status
                if (record['partnerOnboardingStatus']) {
                  htmlLines.push(
                    <div className='flex-vbox border-0'>
                      <div className="tooltip-header">
                        <span className="tooltip-title fw-bold">Onboarding Status:</span>
                      </div>
                      <div className="tooltip-body">
                        {record['partnerOnboardingStatus']}
                      </div>
                    </div>
                  );
                }

                return (
                  <bs.CssTooltip width={500} position='bottom-right'>
                    <bs.CssTooltipToggle className='flex-hbox justify-content-start'>
                      {util.text.formater.uiTruncate(val, (field.width || 400), true)}
                    </bs.CssTooltipToggle>
                    <bs.CssTooltipContent className="flex-vbox p-2 rounded text-start" >
                      <div className="flex-vbox">
                        {htmlLines}
                      </div>
                    </bs.CssTooltipContent>
                  </bs.CssTooltip>
                )
              }
            },
          },
          {
            name: 'suggestedSupport', label: T('Suggested Support'), width: 280, style: { height: CELL_HEIGHT },
            customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
              let record: any = dRecord.record;

              let val = record[field.name] || 'N/A';
              const htmlVal = val.split('\n').map((line: any, i: any) =>
                <div key={i}>{line}</div>
              );
              return (
                <bs.CssTooltip position='bottom-left' width={400} offset={{ x: -200, y: -10 }}>
                  <bs.CssTooltipToggle className='flex-hbox justify-content-start h-100 w-100'>
                    {util.text.formater.uiTruncate(val, 280, true)}
                  </bs.CssTooltipToggle>
                  <bs.CssTooltipContent className='flex-vbox'>
                    <div className="tooltip-body">
                      {htmlVal}
                    </div>
                  </bs.CssTooltipContent>
                </bs.CssTooltip>
              )
            },
          },
          {
            name: 'dueDate', label: T('Due Date'), width: 120, filterable: true, filterableType: 'date',
            fieldDataGetter: (record: any) => {
              if (record['dueDate'] === '-') return record['dueDate'];
              return util.text.formater.compactDate(record['dueDate']);
            },
            cssClass: 'justify-content-center align-items-center'
          },
          {
            name: 'status', label: T('Status'), width: 150, filterable: true, container: 'fixed-right',
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              if (record['groupType'] !== 'Label') {
                return (<div className="flex-hbox justify-content-center align-items-center">{record['status']} </div>)
              }
              let currentStatus = SalesTaskUtils.getStatusInfo(record['status']);
              let StatusIcon = currentStatus.icon;
              let label = currentStatus.label;
              let color = currentStatus.color;

              return (
                <div className={`flex-hbox flex-center px-2 py-2 rounded-2 bg-${color}-subtle text-${color} w-100 `}>
                  <StatusIcon size={14} className="me-1" />
                  <span>{label}</span>
                </div>
              );
            },
          },

        ],
      },
      toolbar: {
        hide: true
      },
      view: {
        currentViewName: 'tree',
        availables: {
          tree: {
            viewMode: 'tree',
            label: 'Tree View',
            treeField: 'label',
            plugin: new SaleDailyTaskTreePlugin(groupBy)
          }
        }
      },
    }
    return responsiveGridConfig(config);
  }


  onDefaultSelect(dRecord: grid.DisplayRecord) {
    let task = dRecord.record;
    if (task['groupType'] === 'SALEMAN' || task['groupType'] === 'TEAM') return;
    task['id'] = task['referenceEntityId']
    UITaskCalendarUtils.showUITaskDetail(this, task);
  }

  render(): React.JSX.Element {
    if (this.isLoading()) return this.renderLoading();
    return (
      <div className="flex-vbox h-100">
        {this.renderUIGrid()}
      </div>
    )
  }

}

class SaleDailyTaskTreePlugin extends grid.TreeDisplayModelPlugin {
  private groupBy: 'SALEMAN' | 'TEAM';

  constructor(groupBy: 'SALEMAN' | 'TEAM') {
    super();
    this.groupBy = groupBy;
  }

  override setCollapse(record: grid.TreeRecord) {
    record.collapse = true;
  }

  buildTreeRecords(records: Array<any>): Array<grid.TreeRecord> {
    let treeRecords: Array<any> = [];
    let idCounter = 1;

    if (this.groupBy === 'TEAM') {

      // Group by delivery province
      const teamGroups = records.reduce((acc, record) => {
        const province = record['departmentLabel'] || 'N/A';
        if (!acc[province]) acc[province] = [];
        acc[province].push(record);
        return acc;
      }, {} as Record<string, Array<any>>);

      const sortedTeamGroups = Object.entries(teamGroups)
        .sort(([a], [b]) => a.localeCompare(b));

      for (let [teamLabel, teamRecords] of sortedTeamGroups as [string, Array<any>][]) {
        // Tạo node phòng ban
        let teamNode: any = {
          id: idCounter++,
          parentId: undefined,
          referenceEntityId: undefined,
          groupType: 'TEAM',
          label: teamLabel,
          taskType: '-',
          partnerType: '-',
          partnerId: '',
          partnerLabel: '',
          description: '-',
          suggestedSupport: '-',
          createdDate: '-',
          dueDate: '-',
          status: `${teamRecords.filter(record => record.status === 'IN_PROGRESS').length} / ${teamRecords.length}`
        }
        treeRecords.push(teamNode);

        // Group tiếp theo salemanLabel trong từng phòng ban
        const salemanGroups = teamRecords.reduce((acc, record) => {
          const saleman = record['salemanLabel'] || 'N/A';
          if (!acc[saleman]) acc[saleman] = [];
          acc[saleman].push(record);
          return acc;
        }, {} as Record<string, Array<any>>);

        // Sort saleman theo tên
        const sortedSalemanGroups = Object.entries(salemanGroups)
          .sort(([a], [b]) => a.localeCompare(b));

        for (let [salemanLabel, salemanRecords] of sortedSalemanGroups as [string, Array<any>][]) {
          // Tạo node saleman
          const salemanNode = {
            id: idCounter++,
            parentId: teamNode.id,
            referenceEntityId: undefined,
            groupType: 'SALEMAN',
            salemanAccountId: salemanRecords[0].salemanAccountId,
            salemanLabel: salemanLabel,
            label: salemanLabel,
            taskType: '-',
            partnerType: '-',
            partnerId: '',
            partnerLabel: '',
            description: '-',
            suggestedSupport: '-',
            createdDate: '-',
            dueDate: '-',
            status: `${salemanRecords.filter(record => record.status === 'IN_PROGRESS').length} / ${salemanRecords.length}`
          }
          treeRecords.push(salemanNode);

          // Thêm các task vào dưới node saleman
          salemanRecords.forEach(record => {
            const partnerType = record['partnerType']
            const partnerId = record['partnerId']
            const partnerLabel = record['partnerLabel']

            const taskLabel = record['label']
            const partnerShippingRoute = record['partnerShippingRoute']
            const partnerVolumeDetails = record['partnerVolumeDetails']
            const partnerOnboardingStatus = record['partnerOnboardingStatus']
            const description = partnerLabel ? `${record['label'] || ''}\n ${record['description'] || ''}` : record['description'];

            const labelNode = {
              id: idCounter++,
              parentId: salemanNode.id,
              referenceEntityId: record.id,
              groupType: 'Label',
              label: partnerLabel || record['label'],
              taskType: record.taskType,
              partnerType: partnerType,
              partnerId: partnerId,
              partnerLabel: partnerLabel,
              partnerAddress: record['partnerAddress'],
              taskLabel: taskLabel,
              partnerShippingRoute: partnerShippingRoute,
              partnerVolumeDetails: partnerVolumeDetails,
              partnerOnboardingStatus: partnerOnboardingStatus,
              description: description,
              suggestedSupport: record.suggestedSupport,
              createdDate: record.createdDate,
              dueDate: record.dueDate,
              status: record.status,
            }
            treeRecords.push(labelNode);

          });
        }
      }

    } else {
      let salemanGroups: util.ListRecordMap<string> = new util.ListRecordMap<string>();
      salemanGroups.addAllRecords('salemanAccountId', records);

      for (let salemanAccountId of salemanGroups.getListNames()) {
        let records: Array<any> = salemanGroups.getList(salemanAccountId);

        let salemanNode: any = {
          id: idCounter++,
          parentId: undefined,
          referenceEntityId: undefined,
          groupType: 'SALEMAN',
          salemanAccountId: salemanAccountId,
          salemanLabel: records[0].salemanLabel,
          label: records[0].salemanLabel,
          partnerType: '-',
          partnerId: '',
          partnerLabel: '',
          taskType: '-',
          description: '-',
          suggestedSupport: '-',
          createdDate: '-',
          dueDate: '-',
          status: `${records.filter(record => record.status === 'IN_PROGRESS').length} / ${records.length}`
        }
        treeRecords.push(salemanNode);

        records.forEach((record, _index, _records) => {
          const partnerType = record['partnerType']
          const partnerId = record['partnerId']
          const partnerLabel = record['partnerLabel']

          const taskLabel = record['label']
          const partnerShippingRoute = record['partnerShippingRoute']
          const partnerVolumeDetails = record['partnerVolumeDetails']
          const partnerOnboardingStatus = record['partnerOnboardingStatus']
          const description = record['description'];

          const labelNode = {
            id: idCounter++,
            parentId: salemanNode.id,
            referenceEntityId: record.id,
            groupType: 'Label',
            label: partnerLabel || record.label,
            partnerType: partnerType,
            partnerId: partnerId,
            partnerLabel: partnerLabel,
            partnerAddress: record['partnerAddress'],
            taskLabel: taskLabel,
            partnerShippingRoute: partnerShippingRoute,
            partnerVolumeDetails: partnerVolumeDetails,
            partnerOnboardingStatus: partnerOnboardingStatus,
            taskType: record.taskType,
            description: description,
            suggestedSupport: record.suggestedSupport,
            createdDate: record.createdDate,
            dueDate: record.dueDate,
            status: record.status,
          }
          treeRecords.push(labelNode);
        })

      }
    }

    grid.initRecordStates(treeRecords);

    return super.buildTreeRecords(treeRecords);
  }

}

