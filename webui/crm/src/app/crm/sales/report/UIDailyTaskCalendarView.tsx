import React from "react";

import { util, entity, bs, app, calendar, server } from '@datatp-ui/lib';
import * as FeatherIcon from 'react-feather';

import { UISalesDailyTaskEditor } from "./UIDailyTaskEditor";
import { UISaleDailyTaskList, UISaleDailyTaskListPlugin } from "./UISaleDailyTaskList";
import { CalendarDayCell } from "./UICalendarDayCell";
import { UICalendarFilter } from "./UICalendarFilter";
import {
  UIMailRequestPricing
} from "app/crm/price";

import { SaleDailyTaskTree } from "./SaleDailyTaskTreeList";
import { SalesTaskStatus } from "../backend";
import { mapToTypeOfShipment } from 'app/crm/common';

const SESSION = app.host.DATATP_SESSION;

import Config = calendar.CalendarConfig
import Context = calendar.CalendarContext


export class UISalesDailyTaskCalendarManager extends calendar.UICalendarManager {

  render() {
    const { context, config } = this.props;
    let plugin = config.plugin;
    if (!plugin || !plugin.render) {
      return (
        <div className='ui-calendar-manager flex-vbox'>
          {this.renderCalendarView()}
        </div>
      );
    }

    let pluginWidth: number | string = plugin.width || 200;

    return (
      <bs.VSplit updateOnResize smallScreenView="offcanvas" className="ui-calendar-manager">
        <bs.VSplitPane title={plugin.label} width={pluginWidth} collapse={this.collapsePlugin()}>
          {plugin.render(context, config)}
        </bs.VSplitPane>
        <bs.VSplitPane>
          {this.renderCalendarView()}
        </bs.VSplitPane>
      </bs.VSplit>
    );
  }
}

export interface UISalesDailyTaskCalendarProps extends app.AppComponentProps {
  space: 'User' | 'Company' | 'System';
}
export class UISalesDailyTaskCalendar extends app.AppComponent<UISalesDailyTaskCalendarProps> {
  config: Config
  records: Array<any> = [];
  activeFilter: string | null = null;

  constructor(props: any) {
    super(props);
    const { space } = this.props;
    this.records = [];

    this.config = {
      view: calendar.CalendarViewType.Month,
      cellAction: calendar.CellAction.None,
      cellMode: calendar.HourCellMode.DataOnly,
      selectedDate: new Date(),
      year: {},
      record: { dateField: 'date', dateFieldSelect: true },

      month: {
        renderCell(ctx: Context, config: Config, date: Date) {
          return <CalendarDayCell context={ctx} config={config} date={date} />;
        },
        onCellClick(_ctx: Context, _config: Config, _date: Date) {
          let uiRoot = _ctx.uiRoot as UISalesDailyTaskCalendar;
          const now = new Date();
          const createdDate = new Date(_date);
          createdDate.setHours(now.getHours(), now.getMinutes(), now.getSeconds());
          // Tạo dueDate: set về cuối ngày (23:59:59)
          const dueDate = new Date(_date);
          dueDate.setHours(23, 59, 59);

          uiRoot.onCreateTask({
            createdDate: util.TimeUtil.javaCompactDateTimeFormat(createdDate),
            dueDate: util.TimeUtil.javaCompactDateTimeFormat(dueDate)
          });
        }
      },

      week: {
        renderCell(ctx: Context, config: Config, date: Date) {
          let dayRecord = ctx.getDateRecordMap().getByDay(date);
          if (!dayRecord) return null;
          let allRecords = dayRecord.getRecordAtHour(config, date);
          if (allRecords.length == 0) return null;
          return <div>{allRecords.length} records</div>;
        },
      },

      day: {
        renderCell(ctx: Context, config: Config, date: Date) {
          let dayRecord = ctx.getDateRecordMap().getByDay(date);
          if (!dayRecord) return null;
          let allRecords = dayRecord.getRecordAtHour(config, date);
          if (allRecords.length == 0) return null;
          return <div>{allRecords.length} records</div>;
        },
      },

      plugin: {
        label: 'Calendar Config',
        width: 320,
        render: (ctx: Context, _config: Config) => {
          let uiRoot = ctx.uiRoot as UISalesDailyTaskCalendar;
          return <UICalendarFilter uiRoot={uiRoot} {...this.props} />;
        }
      }

    }

    if (space === 'User') {
      this.config.plugin = undefined;
    }

  }

  componentDidMount(): void {
    this.onLoadData();
  }

  onLoadData() {

    this.markLoading(true);
    const { space } = this.props;
    let _view: calendar.CalendarViewType = this.config.view;
    let selectedDate: Date = this.config.selectedDate;

    let plugin = new UISaleDailyTaskListPlugin(space, selectedDate);
    plugin.loadDataCalendar(this, (response: server.BackendResponse) => {
      this.records = response.data || [];
      if (this.activeFilter === 'MEETING') {
        let filterRecords: Array<any> = this.records.filter(record => record['taskType'] === 'MEETING')
        this.records = filterRecords;
      } else if (this.activeFilter === 'LEADS') {
        let filterRecords: Array<any> = this.records.filter(record => !!record['partnerLabel'])
        this.records = filterRecords;
      }
      this.markLoading(false);
      this.forceUpdate();
    });

  }

  onCreateTask(template: any = {}): void {
    let { pageContext } = this.props;
    // Check if selected date is in the past
    const selectedDate = util.TimeUtil.parseCompactDateTimeFormat(template.createdDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if ((selectedDate < today) && !pageContext.hasUserModeratorCapability()) {
      bs.dialogShow('Notice',
        <div className="text-warning fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />
          Cannot create tasks for past dates
        </div>,
        { backdrop: 'static', size: 'md' }
      );
      return;
    }

    let writeCap = pageContext.hasUserWriteCapability();

    let newBean = {
      ...template,
      creatorAccountId: SESSION.getAccountId(),
      creatorLabel: SESSION.getAccountAcl().getFullName(),
      salemanAccountId: SESSION.getAccountId(),
      salemanLabel: SESSION.getAccountAcl().getFullName(),
      status: SalesTaskStatus.COMPLETED.value,
    };
    let observer = new entity.BeanObserver(newBean);

    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UISalesDailyTaskEditor appContext={appCtx} pageContext={pageCtx} observer={observer} readOnly={!writeCap}
          onPostCommit={(_bean) => {
            pageCtx.back();
            this.records.push(_bean);
            this.forceUpdate();
          }} />
      )
    }
    pageContext.createPopupPage(`create-tasks-${util.IDTracker.next()}`, "Create Task", createAppPage, { size: 'flex-lg', backdrop: 'static' });
  }

  onViewAllTasks(allTasks: any[] = []): void {
    let { pageContext, space } = this.props;
    let selectedDate: Date = this.config.selectedDate;

    let popupId = `view-all-tasks-${util.IDTracker.next()}`;
    let plugin: UISaleDailyTaskListPlugin = new UISaleDailyTaskListPlugin(space, selectedDate);
    if (allTasks && allTasks.length > 0) {
      const sortedTasks = [...allTasks].sort((a, b) => {
        const statusPriority: any = {
          'IN_PROGRESS': 0,
          'NEED_SUPPORT': 1,
          'BLOCKED': 2,
          'COMPLETED': 3
        };
        const statusA = statusPriority[a.status] ?? 999;
        const statusB = statusPriority[b.status] ?? 999;
        return statusA - statusB;
      });
      plugin = new entity.DbEntityListPlugin(sortedTasks) as UISaleDailyTaskListPlugin;
    } else {
      plugin = new UISaleDailyTaskListPlugin(space, selectedDate);
    }

    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        space === 'User' ? <UISaleDailyTaskList appContext={appCtx} pageContext={pageCtx} plugin={plugin} space={space} /> :
          <SaleDailyTaskTree appContext={appCtx} pageContext={pageCtx} plugin={plugin} groupBy={"SALEMAN"} />
      )
    }

    if (space === 'User') {
      createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
        return (
          <UISaleDailyTaskList appContext={appCtx} pageContext={pageCtx} plugin={plugin} space={space} />
        )
      }
    }
    pageContext.createPopupPage(popupId, "Sale Daily Tasks", createAppPage, { size: 'xl', backdrop: 'static' });
  }

  onSelectTask(task: any): void {
    let { appContext, pageContext } = this.props;
    let writeCap = pageContext.hasUserWriteCapability();
    if (!task['id']) return;

    let salemanLabel = task['salemanLabel'] || 'N/A';

    if (this.activeFilter === 'REQUEST_INQUIRY') {

      const onPostCommit = (_entity: any, uiEditor?: app.AppComponent) => {
        uiEditor?.props.pageContext.back();
        //TODO: Dan - review, implement callback
        this.forceUpdate();
      }

      appContext
        .createHttpBackendCall('TransportPriceMiscService', 'getInquiryRequest', { 'requestId': task['id'] })
        .withSuccessData((request: any) => {

          const createPageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => (
            <div className='flex-hbox'>
              <UIMailRequestPricing appContext={appCtx} pageContext={pageCtx}
                observer={new entity.ComplexBeanObserver(request)} onPostCommit={onPostCommit} />
            </div>
          );

          let type: string = mapToTypeOfShipment(request['purpose'], request['mode']) || 'N/A';
          let popupLabel: string = `Inquiry Request (${type}) - ${salemanLabel}`;

          // We avoid using bs.ScreenUtil.getScreenWidth (window.screen.width) because it may not account for certain UI elements like scrollbars
          //  Or when the user resizes the browser window
          let size: 'flex-lg' | 'xl' = 'flex-lg';
          if (window.innerWidth < 1300) {
            size = 'xl';
          }
          pageContext.createPopupPage(`request-pricing-${util.IDTracker.next()}`, popupLabel, createPageContent, { size: size, backdrop: 'static' });
        }).call();

    } else {
      appContext.createHttpBackendCall("TaskCalendarService", "getSalesDailyTaskById", { id: task['id'] })
        .withSuccessData((taskInDb: any) => {
          let observer = new entity.BeanObserver(taskInDb);
          let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
            return (
              <UISalesDailyTaskEditor
                appContext={appCtx} pageContext={pageCtx} observer={observer} readOnly={!writeCap}
                onAction={(_action, task) => {
                  pageCtx.back();
                  this.records = this.records.filter(sel => sel.id !== task.id);
                  this.forceUpdate();
                }}
                onPostCommit={(_bean) => {
                  pageCtx.back();
                  this.records = this.records.filter(sel => sel.id !== _bean.id);
                  this.records.push(_bean);
                  this.forceUpdate();
                }} />
            )
          }
          pageContext.createPopupPage(`tasks-${util.IDTracker.next()} `, `Task Detail - ${salemanLabel} `, createAppPage, { size: 'flex-lg', backdrop: 'static' });
        })
        .call()
    }

  }

  onChangeMonth(direction: 'prev' | 'next') {
    const currentDate = this.config.selectedDate;
    const newDate = new Date(currentDate);

    if (direction === 'prev') {
      newDate.setMonth(currentDate.getMonth() - 1);
    } else {
      newDate.setMonth(currentDate.getMonth() + 1);
    }

    this.config.selectedDate = newDate;
    this.onLoadData();
    // this.forceUpdate();
  }

  onToday() {
    this.config.selectedDate = new Date();
    this.onLoadData();
    // this.forceUpdate()
  }

  renderToolbar() {
    const currentDate = this.config.selectedDate;
    const monthYear = currentDate.toLocaleString('default', { month: 'long', year: 'numeric' });

    return (
      <div className="flex-hbox flex-grow-0 align-items-center px-2 py-1" >
        {/* Left section - Today button */}
        <div className="d-flex align-items-center">
          <bs.Button laf="primary" outline className="px-2 py-1"
            onClick={() => this.onToday()} title="Go to Today">
            Today
          </bs.Button>
        </div>

        {/* Center section - Month navigation and display */}
        <div className="flex-grow-1 d-flex align-items-center justify-content-center">
          <div className="d-flex align-items-center gap-2">
            <bs.Button
              laf="link"
              className="d-flex align-items-center justify-content-center p-1 text-secondary"
              style={{ width: '32px', height: '32px' }}
              title="Previous Month"
              onClick={() => this.onChangeMonth('prev')}>
              <FeatherIcon.ChevronLeft size={16} />
            </bs.Button>

            <h5 className="mb-0 fw-semibold px-2" style={{ minWidth: '150px', textAlign: 'center' }}>
              {monthYear}
            </h5>

            <bs.Button
              laf="link"
              className="d-flex align-items-center justify-content-center p-1 text-secondary"
              style={{ width: '32px', height: '32px' }}
              title="Next Month"
              onClick={() => this.onChangeMonth('next')}>
              <FeatherIcon.ChevronRight size={16} />
            </bs.Button>
          </div>
        </div>

        {/* Right section - View options */}
        <div className="d-flex align-items-center">
          <bs.ButtonGroup label="View Options" laf='group' smallScreenLaf="popover"
            popover={{ laf: 'primary', placement: 'bottom', outline: true }}>

            <bs.Button laf="secondary" outline className="px-2 py-1"
              active={this.config.view === calendar.CalendarViewType.Month}
              onClick={() => {
                this.config.view = calendar.CalendarViewType.Month;
                this.forceUpdate();
              }}
              title="Month View">
              <FeatherIcon.Calendar size={14} />
              <span className="ms-1 d-none d-md-inline">Month</span>
            </bs.Button>

            <bs.Button laf="secondary" outline className="px-2 py-1"
              active={this.config.view === calendar.CalendarViewType.Week}
              onClick={() => {
                this.config.view = calendar.CalendarViewType.Week;
                this.forceUpdate();
              }}
              title="Week View">
              <FeatherIcon.Calendar size={14} />
              <span className="ms-1 d-none d-md-inline">Week</span>
            </bs.Button>

            <bs.Button laf="secondary" outline className="px-2 py-1"
              active={this.config.view === calendar.CalendarViewType.Day}
              onClick={() => {
                this.config.view = calendar.CalendarViewType.Day;
                this.forceUpdate();
              }}
              title="Day View">
              <FeatherIcon.Calendar size={14} />
              <span className="ms-1 d-none d-md-inline">Day</span>
            </bs.Button>

          </bs.ButtonGroup>
        </div>
      </div>
    );
  }

  render() {
    const { } = this.props;
    let dayRecordMap: calendar.DateRecordMap = new calendar.DateRecordMap(this.records).mapByDateField('dueDate');
    dayRecordMap.dateRecordMap = {};

    for (const record of this.records) {
      const startDate = util.TimeUtil.parseCompactDateTimeFormat(record.createdDate);
      const endDate = util.TimeUtil.parseCompactDateTimeFormat(record.dueDate);

      // Reset time parts to ensure date-only comparison
      startDate.setHours(0, 0, 0, 0);
      endDate.setHours(0, 0, 0, 0); // Set to start of day for accurate day calculation

      // Calculate number of days between dates (inclusive)
      const daysDiff = Math.floor((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

      // Add record for each day in the range (inclusive)
      for (let i = 0; i <= daysDiff; i++) {
        const currentDate = new Date(startDate);
        currentDate.setDate(startDate.getDate() + i);
        dayRecordMap._addRecord(record, currentDate);
      }
    }

    let context = new Context(this, dayRecordMap);
    return (
      <div className="flex-vbox bg-white">
        {this.renderToolbar()}
        <UISalesDailyTaskCalendarManager context={context} config={this.config} />
      </div>
    )
  }
}

