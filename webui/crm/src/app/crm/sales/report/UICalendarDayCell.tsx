import React from "react";
import * as FeatherIcon from 'react-feather';
import { app, calendar, bs, util } from '@datatp-ui/lib';
import { module } from '@datatp-ui/erp';
import { SalesTaskUtils, SaleTaskTypeUtils } from "../backend";
import { UISalesDailyTaskCalendar } from "./UITaskCalendarCalendarView";

const SESSION = app.host.DATATP_SESSION;

interface CalendarDayCellProps extends bs.BaseComponentProps {
  context: calendar.CalendarContext;
  config: calendar.CalendarConfig;
  date: Date;
}

export class CalendarDayCell extends bs.AvailableSize<CalendarDayCellProps> {

  renderContent(_width: number | string, _height: number): React.ReactElement {
    const { context: ctx, config: _config, date } = this.props;
    let dayRecord = ctx.getDateRecordMap().getByDay(date);

    if (!dayRecord) return <></>;
    let allRecords = dayRecord.getAllRecords();
    if (allRecords.length == 0) return <></>;
    let uiRoot = ctx.uiRoot as UISalesDailyTaskCalendar;

    // Clone and sort records
    let clonedRecords = [...allRecords].sort((a, b) => {
      return new Date(a.modifiedTime).getTime() - new Date(b.modifiedTime).getTime();
    });

    // Check if date is in current week
    const today = new Date();
    const currentWeekStart = new Date(today);
    currentWeekStart.setDate(today.getDate() - today.getDay() + 1);
    currentWeekStart.setHours(0, 0, 0, 0);

    const currentWeekEnd = new Date(currentWeekStart);
    currentWeekEnd.setDate(currentWeekStart.getDate() + 6);
    currentWeekEnd.setHours(23, 59, 59, 999);

    // Constants for height calculations

    const EVENT_ITEM_HEIGHT = 34; // Height of each event item including margin
    const REMAINING_BUTTON_HEIGHT = 24; // Height of the "more" button including margin
    const PADDING = 16; // Total padding (8px top + 8px bottom)

    // Calculate maximum number of events that can fit
    const availableHeight = _height - REMAINING_BUTTON_HEIGHT - PADDING;
    const maxEventsCanFit = Math.floor(availableHeight / EVENT_ITEM_HEIGHT);

    // Determine number of records to show based on both space and current week logic
    const maxRecordsForCurrentWeek = (date >= currentWeekStart && date <= currentWeekEnd) ? 5 : 3;
    const recordsToShow = Math.min(
      maxEventsCanFit,
      maxRecordsForCurrentWeek,
      allRecords.length
    );

    const events = clonedRecords.slice(0, recordsToShow).map((record: any, index: number) => {
      let accountId = record['salemanAccountId'] || SESSION.getAccountId();
      const taskType = SaleTaskTypeUtils.getTypeInfo(record.taskType)
      const TaskTypeIcon = taskType.icon;
      const statusInfo = SalesTaskUtils.getStatusInfo(record.status);
      const StatusIcon = statusInfo.icon;


      return (
        <div tabIndex={0} key={index} className="d-block text-decoration-none mb-1 cursor-pointer event-item"
          onClick={(e) => {
            e.stopPropagation()
            uiRoot.onSelectTask(record)
          }}
          style={{ cursor: 'pointer', transition: 'all 0.2s ease-in-out' }
          } >
          <div className={`rounded-2 bg-white border-0 p-1 event-container`}>
            <bs.CssTooltip width={400} position="top-right">
              <bs.CssTooltipToggle>
                <div className="d-flex align-items-center flex-grow-1 me-1">
                  <module.account.WAvatars className='me-1' appContext={uiRoot.props.appContext} pageContext={uiRoot.props.pageContext}
                    avatarIds={[accountId]} avatarIdType='AccountId' width={20} height={20} borderRadius={10} />
                  <div className={`text-truncate text-${taskType.color} fw-medium`}
                    style={{ fontSize: '0.9rem' }}>
                    {util.text.formater.uiTruncate(record.label, 200, true)}
                  </div>
                </div>
              </bs.CssTooltipToggle>

              <bs.CssTooltipContent>

                <div className="rounded-2 bg-white p-1 border-0 shadow-none">

                  {/* Header with Task Type and Status */}
                  <div className="d-flex justify-content-between align-items-center mb-3 py-1">

                    <span className={`badge bg-${statusInfo.color}-subtle text-${statusInfo.color} d-flex align-items-center gap-1 px-2 py-1 rounded-pill`}>
                      <TaskTypeIcon size={12} />
                      <span className="fw-semibold text-800 fs-9 text-uppercase">{taskType.label}</span>
                    </span>

                    <span className={`badge bg-${statusInfo.color}-subtle text-${statusInfo.color} d-flex align-items-center gap-1 px-2 py-1 rounded-pill`}>
                      <StatusIcon size={12} />
                      <span className="fw-medium fs-9">{statusInfo.label}</span>
                    </span>

                  </div>

                  {/* Task Title and Assignee */}
                  <div className="mb-3">

                    <h6 className="mb-2 fw-bold text-1000"
                      style={{
                        maxWidth: '350px',
                        wordWrap: 'break-word',
                        whiteSpace: 'normal',
                        lineHeight: '1.4'
                      }}>
                      {record.label}
                    </h6>

                    <div className="d-flex align-items-center gap-3 border-top border-dashed py-1">
                      <div className="d-flex align-items-center gap-2">
                        <module.account.WAvatars
                          appContext={uiRoot.props.appContext}
                          pageContext={uiRoot.props.pageContext}
                          avatarIds={[record.salemanAccountId]}
                          avatarIdType='AccountId'
                          width={20} height={20} borderRadius={10} />
                        <div className="d-flex flex-column">
                          <span className="fs-9 text-700"
                            title={record.salemanLabel || 'N/A'}>
                            {record.salemanLabel || 'N/A'}
                          </span>
                        </div>
                      </div>
                      {record.dueDate && (
                        <div className="d-flex align-items-center gap-1 text-700">
                          <FeatherIcon.Clock size={13} className="text-600" />
                          <span className="fs-9">
                            {util.text.formater.compactDate(record.dueDate)}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Description */}
                  {record.description && (
                    <div className="mb-3 border-top border-dashed py-1">
                      <div className="d-flex align-items-center gap-1 mb-1">
                        <FeatherIcon.AlignLeft size={13} className="text-600" />
                        <span className="text-700 fw-semibold fs-9">Description</span>
                      </div>
                      <div className="bg-light-subtle rounded-2 p-2"
                        style={{
                          maxHeight: '80px',
                          overflowY: 'auto',
                          fontSize: '0.85rem',
                          lineHeight: '1.5'
                        }}>
                        <p className="text-700 mb-0">
                          {util.text.formater.uiTruncate(record.description, 300, true)}
                        </p>
                      </div>
                    </div>
                  )}

                  {(record.partnerLabel) && (
                    <div className="border-top pt-2">
                      <div className="d-flex flex-column gap-1">
                        <div className="d-flex align-items-center gap-2">
                          <div className="bg-primary-subtle p-1 rounded">
                            <FeatherIcon.Briefcase size={12} className="text-primary" />
                          </div>
                          <span className="fs-9 text-700 text-truncate"
                            style={{ maxWidth: '300px' }}
                            title={record.partnerLabel}>
                            {record.partnerLabel}
                          </span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

              </bs.CssTooltipContent>

            </bs.CssTooltip>
          </div>
        </div>
      )
    });

    const remainingCount = allRecords.length - recordsToShow;

    return (
      <div className="p-1 h-100" style={{
        width: _width,
        height: _height,
        overflow: 'hidden' // Prevent content from overflowing
      }}>
        {events}

        <div className="mt-1">
          <bs.Button laf='link' tabIndex={0}
            className="border-0 mx-1 p-1 w-100 text-start fs-9 text-decoration-none text-600"
            onClick={(e) => {
              e.stopPropagation();
              uiRoot.onViewAllTasks(allRecords);
            }}>
            {remainingCount > 0 ? `+${remainingCount} more` : 'View more'}
          </bs.Button>
        </div>
      </div>
    );
  }
}
