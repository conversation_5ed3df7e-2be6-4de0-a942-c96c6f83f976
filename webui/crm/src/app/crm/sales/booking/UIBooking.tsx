import React from 'react';
import * as FeatherIcon from 'react-feather'

import { bs, input, entity, server } from '@datatp-ui/lib';
import { T } from '../backend';

import { UIBookingChargeListEditor } from './UIBookingChargeListEditor';
import { calculateSellingRate } from './BookingUtils';
import {
  FreightTerm, ImportExportPurpose,
  mapToTypeOfShipment, TransportationMode, TransportationTool
} from 'app/crm/common';
import { BBRefUserCustomer } from '../partners/BBRefUserCustomer';
import { BBRefCrmUserRole } from 'app/crm/common/template/BBRefCrmUserRole';

class UIBookingFormInfo extends entity.AppDbComplexEntity {
  displayService: string = '';

  constructor(props: entity.AppDbComplexEntityProps) {
    super(props);
    const { observer } = this.props;
    let inquiry = observer.getComplexBeanProperty('inquiry', {});
    let purpose: ImportExportPurpose = inquiry['purpose']
    let mode: TransportationMode = inquiry['mode']
    this.displayService = mapToTypeOfShipment(purpose, mode);
  }

  onModify = (_booking: any, field: string, oldVal: any, newVal: any) => {
    const { observer, onModify } = this.props;
    if (field.length > 0) {
      observer.replaceBeanProperty(field, newVal);
    } else {
      observer.setMutableBean(_booking);
    }
    if (onModify) {
      onModify(observer.getMutableBean(), field, oldVal, newVal);
    } else {
      this.forceUpdate();
    }
  }

  onPostSelectPartner = (type: 'AGENTS' | 'CUSTOMERS', _bean: any, selectBean: any = {}) => {
    const { observer } = this.props;
    let booking: any = observer.getMutableBean();
    let inquiry: any = observer.getComplexBeanProperty('inquiry', {});
    const clientPartnerType: string = inquiry['clientPartnerType'] || 'CUSTOMERS';

    const oldClientPartnerId = inquiry['clientPartnerId'];
    const newClientPartnerId = selectBean['id'];
    const newClientName = selectBean['partnerName'];

    if (type === clientPartnerType) {
      let sellingRates = observer.getComplexArrayProperty('sellingRates', []);
      let updatedSellingRates = sellingRates.map((item: any) => {
        if (!item.payerPartnerId || item.payerPartnerId === oldClientPartnerId) {
          return {
            ...item,
            payerPartnerId: newClientPartnerId,
            payerPartnerCode: selectBean['partnerCode'],
            payerPartnerLabel: newClientName
          };
        }
        return item;
      });
      booking['sellingRates'] = updatedSellingRates;
    }

    if (type === 'AGENTS') {
      inquiry['handlingAgentPartnerId'] = newClientPartnerId;
      inquiry['handlingAgentLabel'] = newClientName;
      booking['handlingAgentPartnerId'] = newClientPartnerId;
      booking['handlingAgentLabel'] = newClientName;
    } else {
      inquiry['clientPartnerId'] = newClientPartnerId;
      inquiry['clientLabel'] = newClientName;
      inquiry['attention'] = selectBean['personalContact'];
    }
    booking['inquiry'] = inquiry;
    this.onModify(booking, '', null, null);
  }

  onCheckHawb = (_wInput: input.WInput, _bean: any, _field: string, _oldVal: any, newVal: any) => {
    const { appContext } = this.props;
    if (newVal) {
      appContext.addOSNotification('info', "Checking for duplicate HAWB number in the system...");

      appContext.createHttpBackendCall('BFSOneCRMService', 'findBookingLocalByHawb', { hawb: newVal })
        .withSuccessData((bookingLocals: any[] = []) => {
          if (bookingLocals && bookingLocals.length > 0) {
            _bean[_field] = _oldVal;
            bs.dialogShow('Warning',
              <div className="text-warning fw-bold text-center py-3 border-bottom">
                <FeatherIcon.AlertCircle className="mx-2" />{T(`The HWBNO: ${newVal} was existed on Internal Booking , please type new HWBNO to continue !`)}
              </div>,
              { backdrop: 'static', size: 'sm' }
            );
            this.forceUpdate();
          }
        })
        .withFail((response: server.BackendResponse) => {
          let title = T('Check Hawb No Failed!');
          let message = response.error.message || '';
          bs.notificationShow("danger", title, <div className='text-danger'>{message}</div>);
          return;
        })
        .call();
    }
  }

  render() {
    const { appContext, pageContext, observer } = this.props;
    const writeCap = pageContext.hasUserWriteCapability();
    let booking = observer.getMutableBean();
    let inquiry = observer.getComplexBeanProperty('inquiry', {});
    let mode: TransportationMode = inquiry.mode;

    return (
      <div className="flex-vbox rounded bg-white px-2 py-1" style={{}}>
        <div className='flex-grow-0'>
          <bs.Row>
            <bs.Col span={3}>
              <input.BBStringField
                label={T('Reference No.')} bean={booking} field={'bfsoneReference'} disable />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBStringField label={T('B/K No.')} bean={booking} field={'bookingNumber'} disable={!writeCap}
                placeholder='Enter Carrier B/K No.' />
            </bs.Col>
            <bs.Col span={3} className='flex-hbox gap-1'>
              <input.BBSelectField
                label={T('Shipment Type')} bean={booking} field={"shipmentType"}
                options={['FREE-HAND', 'NOMINATED']} disable={!writeCap} />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBSelectField
                label={T('Payment Term')} bean={booking} field={"paymentTerm"}
                options={[FreightTerm.PREPAID, FreightTerm.COLLECT]} disable={!writeCap} />
            </bs.Col>
          </bs.Row>

          <bs.Row>
            <bs.Col span={3}>
              <input.BBStringField label={T('M-B/L No.')} bean={booking} field={'mawbNo'} disable={!writeCap} />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBStringField label={T('H-B/L No.')} bean={booking} field={'hawbNo'} disable={!writeCap}
                onBgInputChange={this.onCheckHawb} />
            </bs.Col>

            <bs.Col lg={3}>
              <input.BBDateTimeField
                className='rdt-bottom' bean={booking} label={T('Date of Arrival')}
                field={'planTimeArrival'} dateFormat={"DD/MM/YYYY"} timeFormat={false} disable={!writeCap} />
            </bs.Col>
            <bs.Col lg={3}>
              <input.BBDateTimeField
                className='rdt-bottom' label={T('Date of Loading')}
                field={'planTimeDeparture'} bean={booking} dateFormat={"DD/MM/YYYY"}
                timeFormat={false} disable={!writeCap} />
            </bs.Col>
          </bs.Row>

          <bs.Row>
            <bs.Col span={6}>
              <BBRefUserCustomer placeholder='Shipper/ Consignee...' label='Customer' hideMoreInfo
                appContext={appContext} pageContext={pageContext} disable={!writeCap} minWidth={500}
                bean={inquiry} beanIdField={'clientPartnerId'} beanLabelField={'clientLabel'}
                partnerTypes={['CUSTOMERS']}
                onPostUpdate={(_input: any, bean: any, selectOpt: any) => {
                  this.onPostSelectPartner('CUSTOMERS', bean, selectOpt);
                }} />
            </bs.Col>

            <bs.Col span={3} className='flex-hbox gap-1'>
              <div className="flex-grow-1">
                <input.BBNumberField precision={3} maxPrecision={3}
                  bean={inquiry} label={T("Gross Weight (KGS)")} field={'grossWeightKg'} disable={!writeCap}
                  onInputChange={this.onModify} />
              </div>
              <div className="flex-grow-1">
                {
                  TransportationTool.isAir(mode)
                    ?
                    <input.BBNumberField precision={3} maxPrecision={3} label={T('C.W (KGS)')} bean={inquiry} field={'chargeableWeight'}
                      disable={!writeCap} onInputChange={this.onModify} />
                    :
                    <input.BBNumberField precision={3} maxPrecision={3}
                      bean={inquiry} label={T("Volume (CBM)")} field={'volumeCbm'} disable={!writeCap}
                      onInputChange={this.onModify} />
                }
              </div>
            </bs.Col>

            <bs.Col lg={3} className='flex-hbox gap-1'>
              {
                TransportationTool.isSeaFCL(mode)
                  ? <input.BBStringField label={T('Container Types')} disable bean={inquiry} field={'containerTypes'} />
                  : <input.BBNumberField label={T('Packages')} bean={inquiry} field={'packageQty'} disable={!writeCap} />
              }
            </bs.Col>
          </bs.Row>

          <bs.Row>
            <bs.Col span={3}>
              <BBRefUserCustomer placeholder='Agent...' label='Handling Agent' hideMoreInfo
                appContext={appContext} pageContext={pageContext} disable={!writeCap} minWidth={500}
                bean={booking} beanIdField={'handlingAgentPartnerId'} beanLabelField={'handlingAgentLabel'}
                partnerTypes={['AGENTS']}
                onPostUpdate={(_input: any, bean: any, selectOpt: any) => {
                  this.onPostSelectPartner('AGENTS', bean, selectOpt);
                }} />
            </bs.Col>
            <bs.Col span={3}>
              <BBRefUserCustomer placeholder='Coloader...' label='Coloader' hideMoreInfo
                appContext={appContext} pageContext={pageContext} disable={!writeCap} minWidth={500}
                bean={booking} beanIdField={'carrierPartnerId'} beanLabelField={'carrierLabel'} partnerTypes={['COLOADERS']}
                onPostUpdate={(_input: any, bean: any, selectOpt: any) => {
                  bean.carrierPartnerId = selectOpt['id'];
                  bean.carrierLabel = selectOpt['partnerName'];
                  this.onModify(booking, '', null, null);
                }} />
            </bs.Col>
            <bs.Col span={6}>
              <input.BBStringField bean={booking} label={T('Note')} field="note" disable={!writeCap} />
            </bs.Col>
          </bs.Row>

        </div>
      </div>
    );
  }
}

class UIBookingEditor extends entity.AppDbComplexEntityEditor {

  state = {
    isSending: false
  };

  onPostCommit = (entity: any) => {
    let { onPostCommit } = this.props;
    this.nextViewId();
    if (onPostCommit) {
      onPostCommit(entity, this);
    } else {
      this.forceUpdate();
    }
  }

  onModify = (booking: any, field: string, oldVal: any, newVal: any) => {
    const { observer, onModify } = this.props;
    if (field) observer.replaceBeanProperty(field, newVal);
    else observer.setMutableBean(booking);

    if (field === 'grossWeightKg' || field === 'volumeCbm' || field === 'chargeableWeight') {
      let sellingRates = observer.getComplexArrayProperty('sellingRates', []);
      let updateSellingRates: any[] = [];

      for (let record of sellingRates) {
        const unit = (record.unit || '').trim();
        if ((field === 'grossWeightKg' || field === 'chargeableWeight') &&
          (unit === 'KGS' || unit === 'KGM' || unit === 'KG')) {
          record.quantity = newVal;
        } else if (field === 'volumeCbm' && unit === 'CBM') {
          record.quantity = newVal;
        } else {
          record.quantity = 1;
        }
        updateSellingRates.push(calculateSellingRate(record));
      }
      observer.replaceBeanProperty('sellingRates', updateSellingRates);
    }

    this.nextViewId();
    if (onModify) onModify(observer.getMutableBean(), field, oldVal, newVal);
    else this.forceUpdate();
  }

  ensureIBookingValidate() {
    let { observer } = this.props;
    let booking: any = observer.getMutableBean();
    let inquiry: any = booking['inquiry'] || {}

    let sellingRates = observer.getComplexArrayProperty('sellingRates', []);
    // Validate note length for each selling rate
    const MAX_NOTE_LENGTH = 500;
    const invalidNotes = sellingRates.filter((rate: any) =>
      rate.note && rate.note.length > MAX_NOTE_LENGTH
    );

    if (invalidNotes.length > 0) {
      bs.dialogShow('Warning',
        <div className="text-warning fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />
          {T('Some selling rates have notes exceeding 1000 characters. Please shorten them.')}
        </div>,
        { backdrop: 'static', size: 'md' }
      );
      throw new Error('Selling rates notes exceed maximum length');
    }

    if (!inquiry['clientPartnerId']) {
      bs.dialogShow('Error',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />{T('Please provide a valid Client.')}
        </div>,
        { backdrop: 'static', size: 'md' }
      );
      throw new Error('Please provide a valid Client.')
    }

    if (!booking['receiverBFSOneCode']) {
      bs.dialogShow('Error',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />{T('Please provide a valid Receiver.')}
        </div>,
        { backdrop: 'static', size: 'md' }
      );
      throw new Error('Please provide a valid Receiver.')
    }

  }

  onSaveBFSIBooking = () => {
    let { observer, appContext, onPostCommit } = this.props;
    let booking = observer.getMutableBean();
    this.ensureIBookingValidate();
    this.setState({ isSending: true });

    appContext.createHttpBackendCall('BookingService', 'sendBFSOneIBooking', { 'booking': booking })
      .withSuccessData((iBooking: any) => {
        if (iBooking['bfsoneReference']) {
          observer.replaceWith(iBooking)
        }
        this.setState({ isSending: false });
        this.nextViewId();
        this.forceUpdate();
        if (onPostCommit) onPostCommit(iBooking, this);
      })
      .withEntityOpNotification('commit', "IBooking (BFSOne)")
      .withFail((response: server.BackendResponse) => {
        let title = T('IBooking (BFSOne)');
        let message = response.error.message || '';
        bs.notificationShow("danger", title, <div className='text-danger'>{message}</div>);
        this.setState({ isSending: false });
        // return;
      })
      .call();
  }

  onSaveAsBFSIBooking = () => {
    let { observer, appContext, onPostCommit } = this.props;
    let booking = observer.getMutableBean();
    this.ensureIBookingValidate();
    this.setState({ isSending: true });

    appContext.createHttpBackendCall('BookingService', 'resendBFSOneIBooking', { 'booking': booking })
      .withSuccessData((iBooking: any) => {
        if (iBooking['bfsoneReference']) {
          observer.replaceWith(iBooking)
        }
        this.setState({ isSending: false });
        this.nextViewId();
        this.forceUpdate();
        if (onPostCommit) onPostCommit(iBooking, this);
      })
      .withEntityOpNotification('commit', "IBooking (BFSOne)")
      .withFail((response: server.BackendResponse) => {
        let title = T('IBooking (BFSOne)');
        let message = response.error.message || '';
        bs.notificationShow("danger", title, <div className='text-danger'>{message}</div>);
        this.setState({ isSending: false });
        // return;
      })
      .call();
  }

  onPreCommit = (observer: entity.ComplexBeanObserver) => {

    let booking = observer.getMutableBean();
    let inquiry = observer.getComplexBeanProperty('inquiry', {});

    if (!inquiry['clientPartnerId']) {
      bs.dialogShow('Error',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />{T('Please provide a valid Client.')}
        </div>,
        { backdrop: 'static', size: 'md' }
      );
      throw new Error('Please provide a valid Client.')
    }

    let gw: number = booking['grossWeightKg'] || 0;
    let cbm: number = booking['volumeCbm'] || 0;
    let containerTypes: string = inquiry['containerTypes'] || '';
    let mode: TransportationMode = inquiry['mode'];

    if (TransportationTool.isSeaFCL(mode) && !containerTypes) {
      bs.dialogShow('Error',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />{T('Please provide Container Types.')}
        </div>,
        { backdrop: 'static', size: 'sm' }
      );
      return;
    } else if (TransportationTool.isAir(mode) || TransportationTool.isSeaLCL(mode)) {
      if (gw + cbm === 0) {
        bs.dialogShow('Error',
          <div className="text-danger fw-bold text-center py-3 border-bottom">
            <FeatherIcon.AlertCircle className="mx-2" />{T('Please provide GrossWeight/ CBM.')}
          </div>,
          { backdrop: 'static', size: 'sm' }
        );
        return;
      }
    }

    if (!booking['receiverAccountId']) {
      bs.dialogShow('Error',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />{T('Please provide Receiver.')}
        </div>,
        { backdrop: 'static', size: 'md' }
      );
      throw new Error('Please provide Receiver.')
    }
  }

  onModifySellingRates = (beans: any | Array<any>, _action?: entity.ModifyBeanActions) => {
    const { observer } = this.props;
    observer.replaceBeanProperty('sellingRates', beans);
  }

  render() {
    const { appContext, pageContext, observer } = this.props;
    const writeCap = pageContext.hasUserWriteCapability();
    let booking = observer.getMutableBean();

    const bfsoneReference = booking['bfsoneReference'];
    const isNew = !bfsoneReference;

    return (
      <div className='flex-vbox' >

        <bs.HSplit updateOnResize>
          <bs.HSplitPane title={T('General Info')} heightGrow={0} className='flex-grow-0'>
            <UIBookingFormInfo {...this.props} onModify={this.onModify} />
          </bs.HSplitPane>

          <bs.HSplitPane title={T('Selling Rates')} className='py-0'>

            <div className='flex-vbox' style={{ flex: 1, overflow: 'auto' }} key={this.viewId}>
              <UIBookingChargeListEditor
                plugin={observer.createVGridEntityListEditorPlugin('sellingRates', [])}
                appContext={appContext} pageContext={pageContext} booking={booking}
                editorTitle={T('Selling Rates')} dialogEditor={true} onModifyBean={this.onModifySellingRates} />
            </div>

            <div className="bg-light border rounded" style={{ padding: '0 0.75rem' }}>
              <div className="d-flex gap-2">
                <div className="flex-grow-1 d-flex align-items-center gap-2">
                  <label className="form-label mb-0 text-nowrap" style={{ fontSize: '0.775rem', fontWeight: 700, color: '#495057' }}>
                    <FeatherIcon.User size={13} className="text-primary me-1" />{T('Sender')}
                  </label>

                  <BBRefCrmUserRole minWidth={500} hideMoreInfo
                    appContext={appContext} pageContext={pageContext} bean={booking} disable={!writeCap || !observer.isNewBean()}
                    beanIdField='senderAccountId' beanLabelField='senderLabel' placeholder='Saleman.'
                    onPostUpdate={(_inputUI, bean, selectOpt, _userInput) => {
                      bean['senderAccountId'] = selectOpt['accountId'];
                      bean['senderBFSOneCode'] = selectOpt['bfsoneCode'];
                      bean['senderLabel'] = selectOpt['fullName'];
                      this.onModify(bean, 'senderAccountId', null, selectOpt['accountId'])
                    }} />
                </div>

                <div className="flex-grow-1 d-flex align-items-center gap-2">
                  <label className="form-label mb-0 text-nowrap" style={{ fontSize: '0.775rem', fontWeight: 700, color: '#495057' }}>
                    <FeatherIcon.Users size={13} className="text-warning me-1" />{T('Receiver')}
                  </label>
                  <BBRefCrmUserRole minWidth={500} hideMoreInfo
                    appContext={appContext} pageContext={pageContext} bean={booking} disable={!writeCap}
                    beanIdField='receiverAccountId' beanLabelField='receiverLabel' placeholder='Select CS/DOCS...'
                    onPostUpdate={(_inputUI, bean, selectOpt, _userInput) => {
                      bean['receiverAccountId'] = selectOpt['accountId'];
                      bean['receiverBFSOneCode'] = selectOpt['bfsoneCode'];
                      bean['receiverLabel'] = selectOpt['fullName'];
                      this.onModify(bean, '', null, null)
                    }} />
                </div>
              </div>
            </div>

          </bs.HSplitPane>

        </bs.HSplit>
        <bs.Toolbar className='border gap-1'>
          <entity.ButtonEntityCommit btnLabel='Draft IB' className='flex-hbox-grow-0 justify-content-center align-items-center me-2'
            appContext={appContext} pageContext={pageContext} hide={!pageContext.hasUserModeratorCapability()} observer={observer}
            commit={{ entityLabel: 'Booking', context: 'company', service: 'BookingService', commitMethod: 'saveBooking' }}
            onPreCommit={this.onPreCommit}
            onPostCommit={this.onPostCommit} />

          {isNew ? (
            <bs.Button laf='primary'
              className='flex-hbox-grow-0 justify-content-center align-items-center'
              hidden={!writeCap} onClick={this.onSaveBFSIBooking} disabled={this.state.isSending} >
              <FeatherIcon.Send size={14} className="me-2" />
              {this.state.isSending ? 'Sending...' : T('Push to BFSOne')}
            </bs.Button>
          ) : (
            <>
              <bs.Button laf='secondary' className='flex-hbox-grow-0 justify-content-center align-items-center me-2'
                hidden={!writeCap} onClick={this.onSaveBFSIBooking} disabled={this.state.isSending} >
                <FeatherIcon.Edit size={14} className="me-2" />
                {T('Update IB')}
              </bs.Button>
              <bs.Button laf='primary' className='flex-hbox-grow-0 justify-content-center align-items-center'
                hidden={!writeCap} onClick={this.onSaveAsBFSIBooking} disabled={this.state.isSending} >
                <FeatherIcon.Send size={14} className="me-2" />
                {this.state.isSending ? 'Sending...' : T('Push as New IB')}
              </bs.Button>
            </>
          )}
        </bs.Toolbar>

      </div>
    );
  }
}

export class UIBooking extends entity.AppDbComplexEntityEditor {

  render(): React.ReactNode {
    return (
      <UIBookingEditor {...this.props} />
    )
  }
}
