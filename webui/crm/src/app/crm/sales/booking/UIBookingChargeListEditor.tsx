import React from 'react';
import * as FeatherIcon from 'react-feather'
import { util, grid, input, bs, entity, app } from '@datatp-ui/lib';
import { module } from '@datatp-ui/erp';

import { T } from '../backend';
import { CalculatorContext } from '../calculator';
import { calculateSellingRate } from './BookingUtils';
import { BBRefBFSOneUnit } from '../../partner';

import { responsiveGridConfig } from '../common';
import { BBRefNameFeeDesc } from 'app/crm/common';
import { BBRefUserCustomer } from '../partners/BBRefUserCustomer';

const { formater } = util.text;

interface UIBookingChargeListEditorProps extends entity.VGridEntityListEditorProps {
  initUnitRecordMap?: () => Record<string, number>;
  booking: any
}

export class UIBookingChargeListEditor extends entity.VGridEntityListEditor<UIBookingChargeListEditorProps> {
  currentExchangeRate: number = 25470;

  componentDidMount(): void {
    this.currentExchangeRate = 25470;

    let records = this.vgridContext.model.getRecords();
    if (records.length > 0) {
      this.sort(records);
    }
  }

  sort(records: any[]): void {
    records.sort((a: any, b: any) => {
      let field = 'type'
      const typeOrder = ['SEAFREIGHT', 'AIRFREIGHT', 'LOCAL_CHARGE', 'CUSTOM', 'TRUCKING']
      const getTypeIndex = (type: string) => {
        const index = typeOrder.indexOf(type);
        return index === -1 ? typeOrder.length : index; // Put unknown types at the end
      }

      const aTypeIndex = getTypeIndex(a[field]);
      const bTypeIndex = getTypeIndex(b[field]);

      if (aTypeIndex !== bTypeIndex) {
        return aTypeIndex - bTypeIndex;
      }
      if (a.id < b.id) return -1;
      if (a.id > b.id) return 1;
      return 0;
    })
    this.vgridContext.model.displayRecordList.updateRecords(records);
  }

  onSelect(row: number, record: any) {
    let dRecords: Array<grid.DisplayRecord> = this.vgridContext.model.getDisplayRecordList().getDisplayRecords();
    for (let rec of dRecords) {
      grid.initRecordState(rec.record, rec.row)
    }
    record = calculateSellingRate(record);
    grid.getRecordState(record).markModified();
    this.onModify();
    this.vgridContext.getVGrid().forceUpdateView();
  }

  onModify() {
    const { onModifyBean } = this.props;
    let updatedRecords = this.vgridContext.model.getRecords();
    if (onModifyBean) onModifyBean(updatedRecords, entity.ModifyBeanActions.MODIFY);
  }

  override renderBeanEditor() {
    return <div>TODO</div>
  }

  createVGridConfig(): grid.VGridConfig {
    const { appContext, pageContext, booking } = this.props;
    const writeCap = pageContext.hasUserWriteCapability();
    const inquiry: any = booking['inquiry'] || {}
    const clientPartnerId = inquiry['clientPartnerId'];

    const onInputChange = (ctx: grid.FieldContext, oldVal: any, newVal: any) => {
      if (oldVal !== newVal) {
        const { displayRecord } = ctx;
        this.onSelect(displayRecord.row, displayRecord.record);
        calculateSellingRate(displayRecord.record)
        this.onModify();
      }
    }

    let control: any = undefined

    if (writeCap) {
      control = {
        width: 60,
        items: [
          {
            name: 'del', hint: 'Delete', icon: FeatherIcon.Trash2,
            onClick: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
              ctx.model.removeRecord(dRecord.record);
              this.onModify();
              ctx.getVGrid().forceUpdateView();
            },
          },
          {
            name: 'copy', hint: 'Copy', icon: FeatherIcon.Copy,
            onClick: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
              let item = dRecord.record;
              let clone: any = { ...item, id: null }
              ctx.model.insertDisplayRecordAt(dRecord.row, clone);
              ctx.model.getDisplayRecordList().updateDisplayRecords();
              this.onModify();
              this.onSelect(dRecord.row, clone)
            },
          },
        ]
      }
    }

    const CELL_HEIGHT: number = 40;

    let config: grid.VGridConfig = {
      record: {
        dataCellHeight: CELL_HEIGHT,
        editor: {
          supportViewMode: ['table'],
          enable: true
        },
        control: control,
        fields: [
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(),
          {
            name: 'payerPartnerLabel', label: T(`Payer`), width: 180,
            customRender: (_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              const record = dRecord.record;
              let cssCustom = '';
              if (record['code'] && record['code'] === 'total') {
                cssCustom = 'fw-bold w-100';
              } else if (record['code'] && record['code'].startsWith('B_')) {
                cssCustom = 'w-100 text-warning';
              } else if (record['payerPartnerId'] === clientPartnerId) {
                cssCustom = 'w-100 text-primary'
              } else {
                cssCustom = 'w-100 text-success'
              }
              return <div className={`${cssCustom}`}> {record.payerPartnerLabel}</div>;
            },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                let _rowRecord = cell.getDisplayRecord().record;
                let fieldName: string = event.field.name;
                if (fieldName === 'payerPartnerLabel' || fieldName === 'name') {
                  cell.forceUpdate()
                }
              },
            },
            editor: {
              type: 'string',
              enable: true,
              onInputChange: (ctx: grid.FieldContext, oldVal: any, newVal: any) => {
                if (oldVal === newVal) return;
                onInputChange(ctx, oldVal, newVal);
                const { displayRecord, fieldConfig } = ctx;
                let record = displayRecord.record || {};
                record[fieldConfig.name] = newVal;
                grid.getRecordState(record).markModified();
                let event: grid.VGridCellEvent = {
                  row: record.row,
                  field: fieldConfig,
                  event: 'Modified'
                }
                this.vgridContext.broadcastCellEvent(event);
              },
              renderCustom: (ctx: grid.FieldContext, _onInputChange: grid.OnInputChange) => {
                const { displayRecord, tabIndex, focus } = ctx;
                let record = displayRecord.record;
                let cssCustom = '';
                if (record['code'] && record['code'] === 'total') {
                  cssCustom = 'fw-bold w-100';
                } else if (record['code'] && record['code'].startsWith('B_')) {
                  cssCustom = 'w-100 text-warning';
                } else if (record['payerPartnerId'] === clientPartnerId) {
                  cssCustom = 'w-100 text-primary'
                } else {
                  cssCustom = 'w-100 text-success'
                }

                return (
                  <BBRefUserCustomer placeholder='Payer ...' tabIndex={tabIndex} autofocus={focus} hideMoreInfo
                    appContext={appContext} pageContext={pageContext} minWidth={400} offset={[-200, 0]}
                    partnerTypes={['CUSTOMERS']}
                    bean={record} beanIdField={''} beanLabelField={'payerPartnerLabel'}
                    onPostUpdate={(_inputUI: React.Component, bean: any, selectOpt: any, userInput: string) => {
                      if (selectOpt && selectOpt['partnerCode'] && selectOpt['partnerName']) {
                        bean['payerPartnerCode'] = selectOpt['partnerCode'];
                        bean['payerPartnerLabel'] = selectOpt['partnerName'];
                        bean['payerPartnerId'] = selectOpt['id'];

                        record['payerPartnerCode'] = selectOpt['partnerCode']
                        record['payerPartnerLabel'] = selectOpt['partnerName']
                        record['payerPartnerId'] = selectOpt['id']
                      } else {
                        bean['payerPartnerLabel'] = userInput;
                      }
                      _onInputChange(bean, 'payerPartnerLabel', null, selectOpt['partnerName'] || userInput);
                    }}
                  />
                );
              }
            }
          },
          {
            name: 'name', label: T('Description'), width: 140, container: 'fixed-left',
            customRender: (_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record: any = dRecord.record;
              let cssCustom = '';

              let displayVal = record.name;
              if (record['code'] && record['code'] === 'total') {
                cssCustom = 'fw-bold w-100';
              } else if (record['code'] && record['code'].startsWith('B_')) {
                cssCustom = 'w-100 text-warning';
              } else if (record['payerPartnerId'] === clientPartnerId) {
                cssCustom = 'w-100 text-primary'
              } else {
                cssCustom = 'w-100 text-success'
              }
              return (<div className={cssCustom}>{displayVal}</div>)
            },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                let _rowRecord = cell.getDisplayRecord().record;
                let fieldName: string = event.field.name;
                if (fieldName === 'payerPartnerLabel') {
                  cell.forceUpdate()
                }
              },
            },
            editor: {
              type: 'string',
              enable: true,
              renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                const { displayRecord, tabIndex, fieldConfig, focus } = ctx;
                const record = displayRecord.record;
                let cssCustom = '';
                let refType: 'BUYING' | 'SELLING' = 'SELLING';
                if (record['code'] && record['code'] === 'total') {
                  cssCustom = 'fw-bold w-100';
                } else if (record['code'] && record['code'].startsWith('B_')) {
                  refType = 'BUYING';
                  cssCustom = 'w-100 text-warning';
                } else if (record['payerPartnerId'] === clientPartnerId) {
                  cssCustom = 'w-100 text-primary'
                } else {
                  cssCustom = 'w-100 text-success'
                }

                return (
                  <BBRefNameFeeDesc type={refType} className={`${cssCustom}`} required hideMoreInfo minWidth={350}
                    appContext={appContext} pageContext={pageContext} tabIndex={tabIndex} autofocus={focus}
                    bean={record} beanIdField='code' beanLabelField='name'
                    placeholder='Fee Name' disable={!writeCap}
                    onPostUpdate={(_inputUI: React.Component, bean: any, selectOpt: any, _userInput: string) => {
                      onInputChange(bean, fieldConfig.name, null, selectOpt);
                    }} />
                );
              },
              onInputChange: onInputChange
            },

          },

          {
            name: 'quantity', label: T('Qty'), width: 80,
            editor: {
              type: 'number',
              enable: true,
              renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                const { displayRecord, tabIndex, focus } = ctx;
                let item = displayRecord.record;
                if (item['code'] === 'total') return <></>;
                return (<input.BBNumberField style={{ height: CELL_HEIGHT }} precision={3}
                  bean={item} field={'quantity'} tabIndex={tabIndex} focus={focus} onInputChange={onInputChange} />)
              },
              onInputChange: (ctx: grid.FieldContext, oldVal: any, newVal: any) => {
                onInputChange(ctx, oldVal, newVal);
                const { displayRecord, fieldConfig, gridContext } = ctx;
                let event: grid.VGridCellEvent = {
                  row: displayRecord.row,
                  field: fieldConfig,
                  event: 'Modified'
                }
                gridContext.broadcastCellEvent(event);
              }
            },
          },
          {
            name: 'unit', label: T('Unit'), width: 80,
            editor: {
              type: 'string', enable: true,
              renderCustom: (ctx: grid.FieldContext, _onInputChange: grid.OnInputChange) => {
                const { displayRecord, tabIndex, focus } = ctx;
                let item = displayRecord.record;
                return (
                  <BBRefBFSOneUnit required hideMoreInfo minWidth={300} style={{ height: CELL_HEIGHT }}
                    appContext={appContext} pageContext={pageContext} tabIndex={tabIndex} autofocus={focus}
                    bean={item} beanIdField='unit' placeholder='Unit' disable={!writeCap}
                    onPostUpdate={(_inputUI: React.Component, _bean: any, _selectOpt: any, userInput: string) => {
                      _onInputChange(item, 'unit', null, userInput)
                    }} />
                )
              },
              onInputChange: onInputChange
            },
          },
          {
            name: 'unitPrice', label: T('Unit Price'), style: { height: CELL_HEIGHT }, width: 100,
            editor: {
              type: 'currency', enable: true,
              onInputChange: (ctx: grid.FieldContext, oldVal: any, newVal: any) => {
                onInputChange(ctx, oldVal, newVal);
                const { displayRecord, fieldConfig, gridContext } = ctx;
                let event: grid.VGridCellEvent = {
                  row: displayRecord.row,
                  field: fieldConfig,
                  event: 'Modified'
                }
                gridContext.broadcastCellEvent(event);
              }
            }
          },
          {
            name: 'taxRate', label: T('Tax (%)'), width: 80,
            editor: {
              type: 'percent', enable: true,
              onInputChange: (ctx: grid.FieldContext, oldVal: any, newVal: any) => {
                onInputChange(ctx, oldVal, newVal);
                const { displayRecord, fieldConfig, gridContext } = ctx;
                let event: grid.VGridCellEvent = {
                  row: displayRecord.row,
                  field: fieldConfig,
                  event: 'Modified'
                }
                gridContext.broadcastCellEvent(event);
              },
              renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                const { displayRecord, tabIndex, focus } = ctx;
                let item = displayRecord.record;
                if (item['code'] === 'total') return <div>-</div>
                return (<input.BBPercentField style={{ height: CELL_HEIGHT }}
                  bean={item} field={'taxRate'} tabIndex={tabIndex} focus={focus} onInputChange={onInputChange} />)
              },
            },
          },
          {
            name: 'note', label: T('Note'), width: 370, style: { height: CELL_HEIGHT },
            editor: { type: 'string', enable: true, onInputChange: onInputChange }
          },
          {
            name: 'currency', label: T('Curr'), width: 80,
            editor: {
              type: 'string', enable: true,
              renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                const { displayRecord, tabIndex, focus } = ctx;
                let item = displayRecord.record;
                return (
                  <module.settings.BBRefCurrency style={{ height: CELL_HEIGHT }}
                    appContext={appContext} pageContext={pageContext} hideMoreInfo tabIndex={tabIndex} autofocus={focus}
                    bean={item} beanIdField={'currency'} placeholder='Curr'
                    onPostUpdate={(_inputUI, bean, selectOpt, _userInput) => onInputChange(bean, 'currency', null, selectOpt.name)} />
                );
              },
              onInputChange: onInputChange
            },
          },
          {
            name: 'exchangeRate', label: T('Exc. Rate.'), style: { height: CELL_HEIGHT },
            editor: {
              type: 'currency', enable: true,
              onInputChange: (ctx: grid.FieldContext, oldVal: any, newVal: any) => {
                onInputChange(ctx, oldVal, newVal);
                const { displayRecord, fieldConfig, gridContext } = ctx;
                let event: grid.VGridCellEvent = {
                  row: displayRecord.row,
                  field: fieldConfig,
                  event: 'Modified'
                }
                gridContext.broadcastCellEvent(event);
              }
            }
          },
          {
            name: 'domesticUnitPrice', label: T('Price (VND)'), format: (val: any) => formater.currency(val, 0),
            width: 140, style: { height: CELL_HEIGHT, display: 'flex', alignItems: 'center', fontWeight: 'bold' },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                let _rowRecord = cell.getDisplayRecord().record;
                let fieldName: string = event.field.name;
                if (fieldName === 'quantity' || fieldName === 'unitPrice' || fieldName === 'exchangeRate' || fieldName === 'taxRate') {
                  cell.forceUpdate()
                }
              },
            }
          },
          {
            name: 'code', label: T('Code Ref.'), width: 100,
            computeStyle(ctx, dRecord) {
              const types = ['SEAFREIGHT', 'AIRFREIGHT', 'LOCAL_CHARGE', 'CUSTOM', 'TRUCKING'];
              const typeStyles: any = {
                SEAFREIGHT: { backgroundColor: '#e6f2ff', color: '#0066cc' },
                AIRFREIGHT: { backgroundColor: '#fff2e6', color: '#cc6600' },
                LOCAL_CHARGE: { backgroundColor: '#e6ffe6', color: '#006600' },
                CUSTOM: { backgroundColor: '#ffe6e6', color: '#cc0000' },
                TRUCKING: { backgroundColor: '#f2e6ff', color: '#6600cc' }
              };
              if (types.includes(dRecord.record.type)) {
                return { ...typeStyles[dRecord.record.type], height: 35, display: 'flex', alignItems: 'center' }
              }
              return {};
            },
          },

          {
            name: 'totalAmount', label: T('Total Amount'), format: formater.currency, width: 130,
            container: 'fixed-right',
            style: { height: CELL_HEIGHT, display: 'flex', alignItems: 'center', fontWeight: 'bold' },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                let _rowRecord = cell.getDisplayRecord().record;
                let fieldName: string = event.field.name;
                if (fieldName === 'quantity' || fieldName === 'unitPrice' || fieldName === 'exchangeRate' || fieldName === 'taxRate') {
                  cell.forceUpdate()
                }
              },
            }
          },
          {
            name: 'domesticTotalAmount', label: T('Amount (VND)'), format: (val: any) => formater.currency(val, 0),
            width: 130, container: 'fixed-right',
            style: { height: CELL_HEIGHT, display: 'flex', alignItems: 'center', fontWeight: 'bold' },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                let _rowRecord = cell.getDisplayRecord().record;
                let fieldName: string = event.field.name;
                if (fieldName === 'quantity' || fieldName === 'unitPrice' || fieldName === 'exchangeRate' || fieldName === 'taxRate') {
                  cell.forceUpdate()
                }
              },
            }
          },
        ],
      },
      toolbar: {
        hide: true,
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table',
            footer: {
              createRecords: (_ctx: grid.VGridContext) => {
                const records = _ctx.model.getFilterRecords();
                const footerRecords: any[] = [];

                const sumFields = ['totalAmount', 'domesticTotalAmount'];

                const currencies = ['VND', 'USD'];

                // Calculate totals
                currencies.forEach(currency => {
                  const currencyRecords = util.Collections.filter(records, 'currency', currency);
                  if (currencyRecords.length > 0) {
                    const template = {
                      name: `Total (${currency})`,
                      currency: currency,
                      code: 'total'
                    };
                    const grandTotal = util.CollectionMath.sum(currencyRecords, sumFields, template);
                    footerRecords.push(grandTotal);
                  }
                });

                return footerRecords;
              },
            }
          }
        }
      },
    }
    return responsiveGridConfig(config);
  }

  onClearPrice = () => {
    let selectedRecords: Array<any> = this.vgridContext.model.getSelectedRecords();
    if (selectedRecords.length > 0) {
      for (let rec of selectedRecords) {
        this.vgridContext.model.removeRecord(rec);
      }
    } else {
      bs.dialogConfirmMessage('Clear Prices', 'You want to delete all prices?', () => {
        this.vgridContext.model.update([])
      });
    }
    this.onModify();
    this.vgridContext.getVGrid().forceUpdateView(true);
  }

  onAddCommissionFee = () => {

    const { booking } = this.props;
    let inquiry = booking['inquiry'] || {};

    if (!inquiry['mode']) {
      bs.dialogShow('Error',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />{T('Unable to determine Local Charge type')}
        </div>,
        { backdrop: 'static', size: 'sm' }
      );
      return;
    }

    let newItem = {
      type: 'LOCAL_CHARGE',
      group: inquiry['mode'],
      name: 'ADMIN FEE',
      code: 'B_ADRF',
      currency: 'USD',
      quantity: 1,
      unitPrice: 0,
      unit: 'SET',
      domesticUnitPrice: 0,
      domesticTotalAmount: 0,
      domesticCurrency: "VND",
    };
    newItem = calculateSellingRate(newItem);
    this.vgridContext.model.addRecord(newItem);
    grid.initRecordState(newItem, 0).markNew();
    this.onModify();
    this.vgridContext.getVGrid().forceUpdateView();
  }

  onLocalCharge = () => {
    const { booking } = this.props;
    let inquiry = booking['inquiry'] || {};
    if (!inquiry['mode']) {
      bs.dialogShow('Error',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />{T('Unable to determine Local Charge type')}
        </div>,
        { backdrop: 'static', size: 'sm' }
      );
      return;
    }

    let newItem = {
      type: 'LOCAL_CHARGE',
      group: inquiry['mode'],
      currency: 'USD',
      quantity: 1,
      unitPrice: 0,
      domesticUnitPrice: 0,
      domesticTotalAmount: 0,
      domesticCurrency: "VND",
    };
    newItem = calculateSellingRate(newItem);
    this.vgridContext.model.addRecord(newItem);
    grid.initRecordState(newItem, 0).markNew();
    this.onModify();
    this.vgridContext.getVGrid().forceUpdateView();
  }

  onApplyExchangeRateNote = () => {
    const { pageContext } = this.props;

    let createAppPage = (_appCtx: app.AppContext, _pageCtx: app.PageContext) => {

      const onApply = () => {
        _pageCtx.back();

        let formatRate = entity.DbEntityListConfigTool.VND_CURR_FORMAT(this.currentExchangeRate);
        let allRecords = this.vgridContext.model.getRecords();
        let selectedRecords = this.vgridContext.model.getSelectedRecords();
        let recordsToProcess = selectedRecords.length > 0 ? selectedRecords : allRecords;
        recordsToProcess.forEach(rec => {
          if (rec['currency'] === 'VND') return;
          rec['exchangeRate'] = this.currentExchangeRate;
          const calculatedRec = calculateSellingRate(rec);
          rec['totalAmount'] = calculatedRec.totalAmount;
          rec['domesticUnitPrice'] = calculatedRec.domesticUnitPrice;
          rec['domesticTotalAmount'] = calculatedRec.domesticTotalAmount;
          rec['domesticCurrency'] = calculatedRec.domesticCurrency;
          let totalAmountFormat = entity.DbEntityListConfigTool.USD_CURR_FORMAT(calculatedRec.totalAmount);
          rec['note'] = `Rate: ${totalAmountFormat} x ${formatRate}`;
        });

        this.onModify();
        this.vgridContext.getVGrid().forceUpdateView();
      }

      return (
        <div className='flex-hbox flex-grow-0'>
          <div className='flex-grow-1'>
            <input.BBCurrencyField label={T("Rate")} field={'exchangeRate'}
              maxPrecision={3} precision={3} bean={{ exchangeRate: this.currentExchangeRate }}
              onInputChange={(bean: any, field: string, oldVal: any, newVal: any) => {
                this.currentExchangeRate = Number(newVal) || 0;
              }} />
          </div>

          <div className='flex-vbox align-items-center justify-content-end flex-grow-0'>
            <bs.Button laf='info' className="py-1 px-2 mx-2 mb-1" outline onClick={onApply}>
              <FeatherIcon.UserCheck size={12} className='me-1' /> Apply
            </bs.Button>
          </div>

        </div>
      );
    }
    pageContext.createPopupPage('apply-exchange-rate', T('Apply Local Rate'), createAppPage, { size: 'sm', backdrop: 'static' });
  }

  render() {
    const { booking } = this.props;
    let inquiry: any = booking['inquiry']
    let fromLocationLabel = inquiry['fromLocationLabel'] || 'POL'
    let toLocationLabel = inquiry['toLocationLabel'] || 'POD'
    let title: string = `Selling Rates`;
    let route = `${fromLocationLabel} - ${toLocationLabel}`



    return (
      <div className="flex-vbox border shadow-sm rounded bg-white h-100 w-100" style={{ overflow: 'hidden' }}>

        <div className="flex-hbox flex-grow-0 justify-content-between align-items-center px-3 py-1 rounded-top"
          style={{ backgroundColor: '#f8f9fa', borderBottom: '1px solid #e9ecef' }}>

          <div className="d-flex align-items-center gap-2">
            <h5 className="mb-0" style={{ color: '#344767', fontWeight: 600 }}>{title}</h5>
            <div className="d-flex align-items-center">
              <div className="badge d-flex align-items-center gap-1"
                style={{
                  backgroundColor: 'rgba(13, 110, 253, 0.08)',
                  color: '#0d6efd',
                  fontSize: '0.75rem',
                  padding: '0.15rem 0.5rem',
                  fontWeight: 500,
                  border: '1px solid rgba(13, 110, 253, 0.15)',
                  borderRadius: '4px'
                }}>
                <FeatherIcon.Navigation size={10} style={{ marginTop: '-1px' }} />
                {route}
              </div>
            </div>
          </div>

          <div className="flex-hbox flex-grow-0 gap-1">
            <bs.Button laf='info' className="text-decoration-none border-0 p-1" outline
              onClick={this.onApplyExchangeRateNote}>
              <FeatherIcon.Plus size={12} className="me-1" /> Apply Local Rate
            </bs.Button>

            <bs.Button laf='info' className="text-decoration-none border-0 p-1" outline
              onClick={this.onAddCommissionFee}>
              <FeatherIcon.Plus size={12} className="me-1" /> Commission Fee
            </bs.Button>

            <bs.Button laf='info' className="text-decoration-none border-0 p-1" outline
              onClick={this.onLocalCharge}>
              <FeatherIcon.Plus size={12} className="me-1" /> Local Charge
            </bs.Button>

            <bs.Button laf='warning' className="text-decoration-none border-0 p-1" outline
              onClick={this.onClearPrice}>
              <FeatherIcon.Trash2 size={12} className="me-1" /> Remove
            </bs.Button>
          </div>
        </div>

        <div className='flex-vbox flex-grow-1 rounded-bottom'>
          <grid.VGrid context={this.vgridContext} />
        </div>

      </div>
    )
  }
}

UIBookingChargeListEditor.contextType = CalculatorContext;