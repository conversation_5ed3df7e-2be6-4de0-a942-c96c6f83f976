import React from 'react';
import { bs, input, entity } from '@datatp-ui/lib';
import { module } from '@datatp-ui/erp';
import { T } from 'app/crm/price';

import BBRefCompany = module.company.BBRefCompany;

export class UICrmUserPermissionEditor extends entity.AppDbEntityEditor {
  render() {
    let { appContext, pageContext, observer } = this.props;

    let bean = observer.getMutableBean();
    let permissionOptions = ['NONE', 'SELF_ONLY', 'COMPANY_ONLY', 'GROUP_ALL'];
    let permissionLabels = ['None', 'Self Only', 'Company Only', 'Group All'];

    return (
      <div className='flex-vbox'>
        <bs.Row>
          <bs.Col span={6}>
            <input.BBStringField bean={bean} field={'fullName'} label='Full Name' disable />
          </bs.Col>
          <bs.Col span={3}>
            <input.BBStringField bean={bean} field={'bfsoneUsername'} label='BFSOne Username' disable />
          </bs.Col>
          <bs.Col span={3}>
            <BBRefCompany appContext={appContext} pageContext={pageContext} label={T('Company')} placeholder='Enter Company'
              bean={bean} companyIdField={'companyId'} companyLabelField={'targetCompanyCode'} hideMoreInfo beanRefLabelField='code' />
          </bs.Col>
        </bs.Row>

        <div className="mt-3">
          {/* Hàng 1: Agent & Coloader Permissions */}
          <bs.Row className="mb-2">
            <bs.Col span={6}>
              <div className="border rounded p-2" style={{ backgroundColor: '#f8f9fa' }}>
                <h6 className="mb-1 text-primary fw-bold" style={{ fontSize: '0.85rem' }}>Agent Permissions</h6>
                <div style={{ fontSize: '0.8rem' }}>
                  <input.BBRadioInputField className="text-sm mb-1" label='View'
                    bean={bean} field={'agentViewScope'} optionLabels={permissionLabels} options={permissionOptions} />
                  <input.BBRadioInputField className="text-sm mb-1" label='Edit'
                    bean={bean} field={'agentEditScope'} optionLabels={permissionLabels} options={permissionOptions} />
                  <input.BBRadioInputField className="text-sm" label='Approve'
                    bean={bean} field={'agentApproveScope'} optionLabels={permissionLabels} options={permissionOptions} />
                </div>
              </div>
            </bs.Col>

            <bs.Col span={6}>
              <div className="border rounded p-2" style={{ backgroundColor: '#f8f9fa' }}>
                <h6 className="mb-1 text-primary fw-bold" style={{ fontSize: '0.85rem' }}>Coloader Permissions</h6>
                <div style={{ fontSize: '0.8rem' }}>
                  <input.BBRadioInputField className="text-sm mb-1" label='View'
                    bean={bean} field={'coloaderViewScope'} optionLabels={permissionLabels} options={permissionOptions} />
                  <input.BBRadioInputField className="text-sm mb-1" label='Edit'
                    bean={bean} field={'coloaderEditScope'} optionLabels={permissionLabels} options={permissionOptions} />
                  <input.BBRadioInputField className="text-sm" label='Approve'
                    bean={bean} field={'coloaderApproveScope'} optionLabels={permissionLabels} options={permissionOptions} />
                </div>
              </div>
            </bs.Col>

            <bs.Col span={6}>
              <div className="border rounded p-2" style={{ backgroundColor: '#f8f9fa' }}>
                <h6 className="mb-1 text-primary fw-bold" style={{ fontSize: '0.85rem' }}>Customer Permissions</h6>
                <div style={{ fontSize: '0.8rem' }}>
                  <input.BBRadioInputField className="text-sm mb-1" label='View'
                    bean={bean} field={'customerViewScope'} optionLabels={permissionLabels} options={permissionOptions} />
                  <input.BBRadioInputField className="text-sm mb-1" label='Edit'
                    bean={bean} field={'customerEditScope'} optionLabels={permissionLabels} options={permissionOptions} />
                  <input.BBRadioInputField className="text-sm" label='Approve'
                    bean={bean} field={'customerApproveScope'} optionLabels={permissionLabels} options={permissionOptions} />
                </div>
              </div>
            </bs.Col>

            <bs.Col span={6}>
              <div className="border rounded p-2" style={{ backgroundColor: '#f8f9fa' }}>
                <h6 className="mb-1 text-primary fw-bold" style={{ fontSize: '0.85rem' }}>Customer Lead Permissions</h6>
                <div style={{ fontSize: '0.8rem' }}>
                  <input.BBRadioInputField className="text-sm mb-1" label='View'
                    bean={bean} field={'customerLeadViewScope'} optionLabels={permissionLabels} options={permissionOptions} />
                  <input.BBRadioInputField className="text-sm mb-1" label='Edit'
                    bean={bean} field={'customerLeadEditScope'} optionLabels={permissionLabels} options={permissionOptions} />
                  <input.BBRadioInputField className="text-sm" label='Approve'
                    bean={bean} field={'customerLeadApproveScope'} optionLabels={permissionLabels} options={permissionOptions} />
                </div>
              </div>
            </bs.Col>
          </bs.Row>

          {/* Hàng 2: Agent Potential Permissions */}
          <bs.Row>
            <bs.Col span={12}>
              <div className="border rounded p-2" style={{ backgroundColor: '#f8f9fa' }}>
                <h6 className="mb-1 text-primary fw-bold" style={{ fontSize: '0.85rem' }}>Agent Potential Permissions</h6>
                <bs.Row>
                  <bs.Col span={8}>
                    <input.BBRadioInputField className="text-sm" label='View' style={{ fontSize: '0.8rem' }}
                      bean={bean} field={'agentPotentialViewScope'} optionLabels={permissionLabels} options={permissionOptions} />
                  </bs.Col>
                  <bs.Col span={8}>
                    <input.BBRadioInputField className="text-sm" label='Edit' style={{ fontSize: '0.8rem' }}
                      bean={bean} field={'agentPotentialEditScope'} optionLabels={permissionLabels} options={permissionOptions} />
                  </bs.Col>
                  <bs.Col span={8}>
                    <input.BBRadioInputField className="text-sm" label='Approve' style={{ fontSize: '0.8rem' }}
                      bean={bean} field={'agentPotentialApproveScope'} optionLabels={permissionLabels} options={permissionOptions} />
                  </bs.Col>
                </bs.Row>
              </div>
            </bs.Col>
          </bs.Row>
        </div>

        <bs.Toolbar className='border mt-3'>
          <entity.ButtonEntityCommit
            appContext={appContext} pageContext={pageContext}
            observer={observer}
            commit={{
              entityLabel: 'User Permission',
              context: 'company',
              service: 'CRMPermissionTemplateService', commitMethod: 'savePermissionTemplate'
            }}
            onPostCommit={this.onPostCommit}
          />
        </bs.Toolbar>
      </div>
    );
  }
}