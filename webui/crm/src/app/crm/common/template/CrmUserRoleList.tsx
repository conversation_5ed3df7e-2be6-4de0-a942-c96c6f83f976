import React from 'react';
import * as FeatherIcon from "react-feather";
import { grid, entity, util, app } from '@datatp-ui/lib';
import { module } from '@datatp-ui/erp';
import { T } from 'app/crm/price';
import { UICrmUserPermissionEditor } from './UICrmUserPermissionTemplate';

export class CrmUserRolePlugin extends entity.DbEntityListPlugin {
  constructor() {
    super([]);

    this.backend = {
      service: 'CRMUserRoleService',
      searchMethod: 'searchCrmUserRoles',
      deleteMethod: 'deleteCrmUserByIds'
    }

    this.searchParams = {
      maxReturn: 1000,
    };

  }

  loadData(uiList: entity.DbEntityList<any>): void {
    this.createBackendSearch(uiList, { sqlParams: this.searchParams }).call();
  }
}

export class CrmUserRoleList extends entity.DbEntityList {

  createVGridConfig() {
    let { pageContext } = this.props;

    const copyAction = {
      name: 'update-permission', hint: T('Setup Permission'), icon: FeatherIcon.ExternalLink,
      onClick: (ctx: grid.VGridContext, record: grid.DisplayRecord) => {
        let uiList = ctx.uiRoot as CrmUserRoleList;
        uiList.onSetupPermission(record);
      },
    }

    let moderatorCap = pageContext.hasUserModeratorCapability();

    const CELL_HEIGHT: number = 40;
    let config: grid.VGridConfig = {
      record: {
        editor: {
          enable: true,
          supportViewMode: ['table']
        },
        dataCellHeight: CELL_HEIGHT,
        control: {
          width: 30,
          items: moderatorCap ? [copyAction] : [],
        },
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          { name: 'bfsoneCode', label: T('BFSOne Code'), width: 100, filterable: true, },
          { name: 'bfsoneUsername', label: T('BFSOne Username'), width: 180, filterable: true, },
          {
            name: "fullName", label: T('Full Name'), width: 350, cssClass: 'pe-1', filterable: true,
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let uiList = ctx.uiRoot as CrmUserRoleList
              const { appContext, pageContext } = uiList.props;
              let val = record[_field.name] || 'N/A';
              return (
                <div className='flex-hbox justify-content-center align-items-center' >
                  <module.account.WAvatars className='px-2'
                    appContext={appContext} pageContext={pageContext} avatarIds={[record['accountId']]}
                    avatarIdType='AccountId' width={20} height={20} borderRadius={10} />
                  <div className="flex-hbox">{val}</div>
                </div>
              )
            }
          },
          { name: 'email', label: T('Email'), width: 250, filterable: true, },
          { name: 'phone', label: T('Phone'), width: 150, filterable: true, },
          { name: 'position', label: T('Position'), width: 150, filterable: true, },
          { name: 'companyBranchName', label: T('Company'), width: 150, filterable: true, },
          { name: 'departmentLabel', label: T('Department'), width: 300, filterable: true, },
          { name: 'team', label: T('Team'), width: 250 },
          { name: 'type', label: T('Type'), width: 150, filterable: true },
        ],
      },
      toolbar: {
        hide: true,
      },

      footer: {
      },

      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table',
          },
        },
      },
    };
    return config;
  }

  onSetupPermission(dRecord: grid.DisplayRecord) {
    const { appContext, pageContext } = this.props;
    let user: any = dRecord.record;
    let title = `Setup User Permission: ${user['fullName']}`;

    let newPermission: any = {
      accountId: user['accountId'],
      bfsoneUsername: user['bfsoneUsername'],
      fullName: user['fullName'],
      targetCompanyCode: user['companyBranchCode'],
      companyId: user['companyBranchId']
    }

    appContext.createHttpBackendCall("CRMPermissionTemplateService", "getByAccountId", { accountId: user['accountId'] })
      .withSuccessData((permission: any) => {
        const createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          let observer = new entity.BeanObserver(permission);
          if (!permission) {
            observer = new entity.BeanObserver(newPermission);
          }

          return (
            <UICrmUserPermissionEditor appContext={appCtx} pageContext={pageCtx} observer={observer}
              onPostCommit={(_entity) => {
                this.reloadData();
                this.nextViewId();
                pageCtx.back();
                this.vgridContext.getVGrid().forceUpdateView();
              }} />
          );
        }
        pageContext.createPopupPage(`setup-permission-${util.IDTracker.next()}`, title, createAppPage, { size: 'flex-lg', backdrop: 'static' });
      })
      .call();
  }

}
