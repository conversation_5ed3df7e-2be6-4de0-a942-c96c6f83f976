import React from 'react';
import { util, grid, bs, entity, app, input } from '@datatp-ui/lib';
import { T, WRateFinderGridFilter } from 'app/crm/price';
import { UIPartnerRequestEditor } from './UIPartnerRequest';
import { UIPartnerRefundRequestEditor } from './UIPartnerRefundRequest';

export class UIPartnerRequestPlugin extends entity.DbEntityListPlugin {
  constructor(space: 'User' | 'Company' | 'System') {
    super([]);

    this.backend = {
      service: 'PartnerRequestService',
      searchMethod: 'searchPartnerRequests',
    }

    this.searchParams = {
      params: {
        space: space,
      },
      maxReturn: 1000,
    };
  }

  loadData(uiList: entity.DbEntityList<any>): void {
    this.createBackendSearch(uiList, { sqlParams: this.searchParams }).call();
  }
}

interface UIPartnerRequestListProps extends entity.DbEntityListProps {
  space: 'User' | 'Company' | 'System';
}
export class UIPartnerRequestList extends entity.DbEntityList<UIPartnerRequestListProps> {

  createVGridConfig() {
    const CELL_HEIGHT: number = 40;
    let config: grid.VGridConfig = {
      title: 'Partner Request List',
      record: {
        editor: {
          enable: true,
          supportViewMode: ['table']
        },
        dataCellHeight: CELL_HEIGHT,
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          entity.DbEntityListConfigTool.FIELD_ON_SELECT('bfsonePartnerCodeTemp', T('Code'), 80),
          { name: 'partnerName', label: T('Partner Name'), width: 420 },
          { name: 'partnerLabel', label: T('Partner Label'), width: 450 },
          { name: 'requestByLabel', label: T('Request By'), width: 220 },
          {
            name: 'requestDate', label: T('Request Date'), width: 100,
            fieldDataGetter(record) {
              if (!record['requestDate']) return '';
              return util.text.formater.compactDate(record['requestDate']);
            },
          },
          { name: 'approvedByLabel', label: T('Approver'), width: 220 },
          {
            name: 'approvedDate', label: T('Date Approval'), width: 120,
            fieldDataGetter(record) {
              if (!record['requestDate']) return '';
              return util.text.formater.compactDate(record['requestDate']);
            },
          },
          { name: 'approvedNote', label: T('Note'), width: 500 },
          { name: 'status', label: T('Status'), width: 100, filterable: true, filterableType: 'Options', container: 'fixed-right' },
        ],
      },
      toolbar: {
        hide: true,
      },

      footer: {
      },

      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table',
          },
        },
      },
    };
    return config;
  }

  onModify = (_bean: any, _field: string, _oldVal: any, _newVal: any): void => {
    const { plugin } = this.props;
    let searchParam = plugin.getSearchParams();
    if (_field === 'maxReturn') {
      searchParam.maxReturn = _newVal || 5000;
    } else {
      searchParam.params = _bean;
    }
    plugin.searchParams = searchParam;
    this.reloadData();
    this.nextViewId();
    // this.forceUpdate();
    this.vgridContext.getVGrid().forceUpdateView();
  };

  onDefaultSelect(dRecord: grid.DisplayRecord): void {
    let request = dRecord.record;
    let { appContext, pageContext, space } = this.props;

    appContext.createHttpBackendCall('CRMPartnerService', 'getCRMPartner', { id: request.partnerId })
      .withSuccessData((data: any) => {
        let partner = {
          ...data,
          requestSalemanAccountId: request.requestByAccountId,
          requestSalemanLabel: request.requestByLabel,
        }
        let observer = new entity.BeanObserver(partner);
        let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          const onPostCommit = (_entity: any, _uiEditor?: bs.BaseComponent) => {
            this.reloadData();
          }

          if (partner['refund']) {
            return (
              <UIPartnerRefundRequestEditor appContext={appCtx} pageContext={pageCtx} observer={observer}
                request={request} space={space} onPostCommit={onPostCommit} />
            );
          }
          return (
            <UIPartnerRequestEditor appContext={appCtx} pageContext={pageCtx} observer={observer}
              request={request} space={space} onPostCommit={onPostCommit} />
          );

        }
        let popupLabel: string = `Partner: ${request.partnerName}`;
        pageContext.createPopupPage(`partner-${util.IDTracker.next()}`, popupLabel, createAppPage, { size: 'flex-lg', backdrop: 'static' });
      })
      .call();
  }

  render() {
    const { plugin, pageContext } = this.props;
    let searchParam = plugin.getSearchParams();

    return (
      <div className='flex-vbox'>
        <div className='bg-white flex-hbox flex-grow-0 justify-content-between align-items-center mx-1 px-1'>

          <div className='flex-hbox'>
            <div className='me-2'>
              <input.BBSelectField className="fw-bold text-primary border-bottom text-center"
                style={{ width: 150 }} bean={searchParam} field={"maxReturn"}
                options={[1000, 2000, 5000, 10000]}
                optionLabels={['Show 1000 records', 'Show 2000 records', 'Show 5000 records', 'Show 10.000 records']}
                onInputChange={this.onModify} />
            </div>
            <WRateFinderGridFilter context={this.vgridContext} />
          </div>
        </div>

        <div key={this.viewId} className='flex-vbox'>
          {this.renderUIGrid()}
        </div>
      </div>
    )
  }

}
