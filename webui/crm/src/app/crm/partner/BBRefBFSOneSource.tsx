import React from "react";
import { entity, sql, bs } from "@datatp-ui/lib";
import * as FeatherIcon from "react-feather";
import { T } from "../price";


interface BBRefBFSOneSourceProps extends entity.BBRefEntityProps {
  beanIdField: string;
  beanLabelField: string;
}
export class BBRefBFSOneSource extends entity.BBRefEntity<BBRefBFSOneSourceProps> {
  createPlugin() {
    let { beanIdField, beanLabelField } = this.props;

    let config: entity.BBRefEntityPluginConfig = {
      backend: {
        context: 'company',
        service: 'CRMPartnerService',
        searchMethod: 'searchBFSOnePartnerSources',
      },
      bean: {
        idField: beanIdField,
        labelField: beanLabelField
      },
      refEntity: {
        idField: 'IDKey',
        labelField: 'srcName',
        vgridRecordConfig: {
          fields: [
            entity.DbEntityListConfigTool.FIELD_INDEX(),
            entity.DbEntityListConfigTool.FIELD_ON_SELECT('srcName', T('Source'), 400),
          ]
        }
      },
    }
    return new entity.BBRefEntityPlugin(config);
  }
}


interface BBRefMultiBFSOneSourceProps extends entity.BBRefMultiEntityProps {
  beanIdField: string;
  beanLabelField: string;
}

export class BBRefMultiBFSOneSource extends entity.BBRefMultiEntity<BBRefMultiBFSOneSourceProps> {

  createExcludeRecordFilter() {
    let { bean } = this.props;
    let refEntities = bean as Array<any>;
    return new entity.ExcludeRecordFilter(refEntities, 'sourceName', 'srcName');
  }

  protected createPlugin() {
    let { beanIdField, beanLabelField } = this.props;
    let config: entity.BBRefEntityPluginConfig = {
      backend: {
        context: 'company',
        service: 'CRMPartnerService',
        searchMethod: 'searchBFSOnePartnerSources',
      },
      bean: {
        idField: beanIdField,
        labelField: beanLabelField
      },
      refEntity: {
        idField: 'IDKey',
        labelField: 'srcName',
        vgridRecordConfig: {
          fields: [
            entity.DbEntityListConfigTool.FIELD_INDEX(),
            entity.DbEntityListConfigTool.FIELD_ON_SELECT('srcName', T('Source'), 400),
          ]
        }
      },
    }
    return new entity.BBRefMultiEntityPlugin(config);
  }
}