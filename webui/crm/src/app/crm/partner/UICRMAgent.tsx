import React from 'react'
import * as FeatherIcon from "react-feather";
import { bs, input, entity, util } from '@datatp-ui/lib';
import { module } from '@datatp-ui/erp';

import { T } from '../price';
import { BBRefCrmUserRole } from '../common/template/BBRefCrmUserRole';
import { BBRefMultiBFSOneSource } from './BBRefBFSOneSource';

import BBRefCountry = module.settings.BBRefCountry;
import BBRefState = module.settings.BBRefState;

function validateAgent(partner: any) {
  let missingFields: string[] = [];
  if (!partner['label'] || !partner['localizedLabel']) missingFields.push('Partner Name (En/Vn)');
  if (!partner['address'] || !partner['localizedAddress']) missingFields.push('Address (En/Vn)');
  if (!partner['countryId']) missingFields.push('Country');
  if (!partner['personalContact']) missingFields.push('Personal Contact');
  if (!partner['cell']) missingFields.push('Cell Phone');
  if (!partner['email']) missingFields.push('Email');
  if (!partner['industryCode'] || !partner['industryLabel']) missingFields.push('Industry');

  if (missingFields.length > 0) {
    bs.dialogShow('Missing Information',
      <div className="text-danger fw-bold text-center py-3 border-bottom">
        <FeatherIcon.AlertCircle className="mx-2" />
        {`Please provide: ${missingFields.join(', ')}.`}
      </div>,
      { backdrop: 'static', size: 'sm' }
    );
    throw new Error(`Please provide: ${missingFields.join(', ')}.`);
  }
}

export interface UINewBFSOneAgentProps extends entity.AppComplexEntityEditorProps {
}

export class UINewBFSOneAgentEditor extends entity.AppDbComplexEntityEditor<UINewBFSOneAgentProps> {
  state = {
    isSending: false
  };

  sources: { sourceId: string, sourceName: string }[] = [];
  categories: string[] = ['AGENT_OVERSEAS', 'AGENT_DOMESTIC'];
  partnerPattern: string = '';

  constructor(props: UINewBFSOneAgentProps) {
    super(props);
    this.partnerPattern = '';
  }

  onPreCommit = (observer: entity.ComplexBeanObserver) => {
    let partner = observer.getMutableBean();
    validateAgent(partner);
    if (!partner['sources'] || partner['sources'].length === 0) {
      bs.dialogShow('Missing Information',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />
          {`Please provide Source.`}
        </div>,
        { backdrop: 'static', size: 'sm' }
      );
      throw new Error(`Please provide Source.`);
    }

    this.computeTransportGroupFromServiceNames(partner);
    this.setState({ isSending: true });
  }

  onPostCommit = (entity: any) => {
    let { onPostCommit } = this.props;
    this.nextViewId();
    this.setState({ isSending: false });
    if (onPostCommit) {
      onPostCommit(entity, this);
    } else {
      this.forceUpdate();
    }
  }

  onChangePartnerCode = (_bean: any, field: string, oldVal: any, newVal: any) => {
    if (!newVal) return;
    this.partnerPattern = newVal;
    let { appContext, observer } = this.props;
    appContext.addOSNotification('info', "Fetch Partner Information in the system...");
    let param: any = { partnerCode: newVal };
    appContext.createHttpBackendCall("CRMPartnerService", "syncBFSOnePartnersByCode", param)
      .withSuccessData((data: any) => {
        if (!data || Object.keys(data).length === 0) return;
        appContext.addOSNotification('success', "Permission updated and partner refreshed.");
        observer.replaceWith(data);
        this.nextViewId();
        this.forceUpdate();
      })
      .call()
  }

  onUpdateSimilarFields = (bean: any, field: string, _oldVal: any, newVal: any) => {
    bean[field] = newVal;
    if (field === 'name') {
      if (!bean['label'] || bean['label'].length == 0) bean['label'] = newVal;
      if (!bean['localizedLabel'] || bean['localizedLabel'].length == 0) bean['localizedLabel'] = newVal;
    }
    if (field === 'address') {
      if (!bean['localizedAddress'] || bean['localizedAddress'].length == 0) bean['localizedAddress'] = newVal;
    }
    this.forceUpdate();
  }

  computeTransportGroupFromServiceNames = (partner: any) => {
    const selectedServiceNames = partner['serviceNames'] || [];
    const allServiceNames = [
      'AIR_EXPORT',
      'AIR_IMPORT',
      'SEA_EXPORT',
      'SEA_IMPORT',
      'SEA_AIR_EXP_TO_OTHER_COUNTRY',
      'SEA_AIR_NOMINATED_EXPORT'
    ];
    const serviceNameMapping: { [key: string]: string } = {
      'AIR_EXPORT': 'Air Export',
      'AIR_IMPORT': 'Air Import',
      'SEA_EXPORT': 'Sea Export',
      'SEA_IMPORT': 'Sea Import',
      'SEA_AIR_EXP_TO_OTHER_COUNTRY': 'Sea-Air Exp to Other Country',
      'SEA_AIR_NOMINATED_EXPORT': 'Sea-Air Nominated Export'
    };

    const transportGroup = allServiceNames.map((serviceName: string) => ({
      serviceName: serviceNameMapping[serviceName] || serviceName,
      allow: selectedServiceNames.includes(serviceName),
    }));

    partner['transportGroup'] = transportGroup;
  }

  render() {
    let { appContext, pageContext, observer, readOnly } = this.props;
    let partner = observer.getMutableBean();
    if (!partner['sources'] || partner['sources'].length === 0) partner['sources'] = [];
    let writeCap = pageContext.hasUserWriteCapability() && !readOnly

    return (
      <div className="flex-vbox">
        <bs.Scrollable style={{ maxHeight: 600 }}>

          <div className='flex-vbox shadow-sm rounded h-100 bg-white p-1'>

            <bs.Row className='pb-2'>
              <bs.Col span={12}>
                <bs.CssTooltip position='bottom-left' width={400} offset={{ x: 200, y: -12 }}>
                  <bs.CssTooltipToggle className='flex-vbox justify-content-start py-1 w-100'>
                    <div className='flex-grow-0 flex-hbox justify-content-start align-items-center w-100'>
                      <FeatherIcon.Search className="me-2 text-info" size={14} />
                      <span className='' style={{ fontSize: '0.9rem' }}>Partner No/ Tax Code</span>
                    </div>
                    <input.BBStringField className='px-2'
                      bean={{ partnerCode: this.partnerPattern }} field='partnerCode'
                      onInputChange={this.onChangePartnerCode} placeholder='Enter BFSOne Partner Code like AG002815' />
                  </bs.CssTooltipToggle>

                  <bs.CssTooltipContent className="d-flex flex-column rounded">
                    <div className="tooltip-header mb-2">
                      <span className="tooltip-title">Hướng dẫn nhập thông tin</span>
                    </div>
                    <div className="tooltip-body text-secondary">
                      Nếu bạn đã được phân quyền vào Agent có sẵn, hãy tìm kiếm thông tin bằng <b>Agent Code</b> tương ứng để cập nhật.
                    </div>
                  </bs.CssTooltipContent>

                </bs.CssTooltip>
              </bs.Col>
            </bs.Row>

            <bs.Row>
              <bs.Col span={3}>
                <input.BBStringField
                  bean={partner} field='partnerCode' label={T("Partner No.")} disable />
              </bs.Col>
              <bs.Col span={3}>
                <input.BBSelectField
                  bean={partner} field='category' label={T("Category.")} disable={!writeCap || !observer.isNewBean()}
                  options={this.categories} />
              </bs.Col>
              <bs.Col span={6}>
                <BBRefCrmUserRole minWidth={500} hideMoreInfo label='Saleman'
                  appContext={appContext} pageContext={pageContext} bean={partner} disable={!writeCap}
                  beanIdField='requestSalemanAccountId' beanLabelField='requestSalemanLabel' placeholder='Select Saleman Request...'
                  onPostUpdate={(_inputUI: React.Component, bean: any, selectOpt: any, userInput: string) => {
                    bean['requestSalemanAccountId'] = selectOpt['accountId'];
                    bean['requestSalemanLabel'] = selectOpt['fullName'];
                    this.forceUpdate();
                  }} />
              </bs.Col>
            </bs.Row>

            <bs.Row>
              <bs.Col span={12}>
                <input.BBStringField
                  bean={partner} field='name' label={T("Partner Name (Abb)")} disable={!writeCap} required
                  onInputChange={this.onUpdateSimilarFields} />
              </bs.Col>
            </bs.Row>

            <bs.Row>
              <bs.Col span={12}>
                <input.BBStringField
                  bean={partner} field='label' label={T("Partner Name (En)")} disable={!writeCap} />
              </bs.Col>
            </bs.Row>

            <bs.Row>
              <bs.Col span={12}>
                <input.BBStringField
                  bean={partner} field='localizedLabel' label={T("Partner Name (VN)")} disable={!writeCap} />
              </bs.Col>
            </bs.Row>

            <bs.Row>
              <bs.Col span={3}>
                <input.BBStringField
                  bean={partner} field='personalContact' label={T("Personal Contact")} disable={!writeCap} required />
              </bs.Col>
              <bs.Col span={3}>
                <input.BBStringField
                  bean={partner} field='position' label={T("Position.")} disable={!writeCap} required />
              </bs.Col>
              <bs.Col span={3}>
                <input.BBStringField
                  bean={partner} field='cell' label={T("Cell Phone")} disable={!writeCap} />
              </bs.Col>
              <bs.Col span={3}>
                <input.BBStringField
                  bean={partner} field='email' label={T("Email")} disable={!writeCap} />
              </bs.Col>
            </bs.Row>

            <bs.Row>
              <bs.Col span={3}>
                <input.BBStringField
                  bean={partner} field='vipContact' label={T("VIP Contact")} disable={!writeCap} />
              </bs.Col>
              <bs.Col span={3}>
                <input.BBStringField
                  bean={partner} field='vipPosition' label={T("VIP Position.")} disable={!writeCap} />
              </bs.Col>
              <bs.Col span={3}>
                <input.BBStringField
                  bean={partner} field='vipCellphone' label={T("VIP Cell Phone")} disable={!writeCap} />
              </bs.Col>
              <bs.Col span={3}>
                <input.BBStringField
                  bean={partner} field='vipEmail' label={T("VIP Email")} disable={!writeCap} />
              </bs.Col>
            </bs.Row>

            <bs.Row>
              <bs.Col span={6}>
                <BBRefCountry
                  appContext={appContext} pageContext={pageContext}
                  placement="bottom-start" offset={[0, 5]} minWidth={350}
                  disable={!writeCap} label={T('Country')} placeholder="Enter Country"
                  required bean={partner} beanIdField={'countryId'} hideMoreInfo
                  beanLabelField={'countryLabel'} refCountryBy='id'
                  onPostUpdate={(_inputUI: React.Component, bean: any, selectOpt: any, userInput: string) => {
                    bean['countryId'] = selectOpt['id'];
                    bean['countryLabel'] = selectOpt['label'];
                    this.forceUpdate();
                  }} />
              </bs.Col>
              <bs.Col span={6}>
                <BBRefState key={util.IDTracker.next()}
                  appContext={appContext} pageContext={pageContext}
                  placement="bottom-start" offset={[0, 5]} minWidth={350}
                  disable={!writeCap} label={T('Province')} placeholder="Enter Province"
                  bean={partner} beanIdField={'provinceId'} hideMoreInfo allowUserInput
                  beanLabelField={'provinceLabel'} countryId={partner.countryId} />
              </bs.Col>
            </bs.Row>

            <bs.Row>
              <bs.Col span={6}>
                <input.BBTextField
                  bean={partner} label={T('Address (En) 1')} field="address" disable={!writeCap} required
                  onInputChange={this.onUpdateSimilarFields} style={{ height: '4em' }} />
              </bs.Col>
              <bs.Col span={6}>
                <input.BBTextField
                  bean={partner} label={T('Address (En) 2')} field="workAddress" disable={!writeCap} required
                  style={{ height: '4em' }} />
              </bs.Col>
            </bs.Row>

            <bs.Row>
              <bs.Col span={12}>
                <input.BBTextField
                  bean={partner} label={T('Address (Vn)')} field="localizedAddress" disable={!writeCap} required
                  style={{ height: '4em' }} />
              </bs.Col>
            </bs.Row>

            <bs.Row>
              <bs.Col span={3}>
                <input.BBStringField
                  bean={partner} field='workPhone' label={T("Work Phone.")} disable={!writeCap} />
              </bs.Col>
              <bs.Col span={3}>
                <input.BBStringField
                  bean={partner} field='homePhone' label={T("Home Phone.")} disable={!writeCap} />
              </bs.Col>
              <bs.Col span={3}>
                <input.BBStringField
                  bean={partner} field='fax' label={T("Fax.")} disable={!writeCap} />
              </bs.Col>
              <bs.Col span={3}>
                <input.BBSelectField
                  bean={partner} field='scope' label={T("Location")} disable={!writeCap || !observer.isNewBean()}
                  options={['Domestic', 'Overseas']} />
              </bs.Col>
            </bs.Row>

            <bs.Row>
              <bs.Col span={6}>
                <BBRefMultiBFSOneSource
                  key={`new-agent-network-${this.viewId}`}
                  appContext={appContext} pageContext={pageContext}
                  placement="bottom-start" offset={[0, 5]} minWidth={350}
                  disable={!writeCap} label={T('Source')} placeholder="Enter Source"
                  bean={partner['sources']} beanIdField={'sourceId'} beanLabelField={'sourceName'} hideMoreInfo />
              </bs.Col>
              <bs.Col span={3}>
                <module.resource.BBRefResource
                  key={`new-agent-industry-${this.viewId}`}
                  appContext={appContext} pageContext={pageContext}
                  placement="bottom-start" offset={[0, 5]} minWidth={350}
                  disable={!writeCap} label={T('Industry')} placeholder="Enter Industry"
                  required bean={partner} beanIdField={'industryCode'} hideMoreInfo
                  beanLabelField={'industryLabel'} resourceType={"industry"} refResourceBy="identifier" />
              </bs.Col>
              <bs.Col span={3}>
                <input.BBStringField
                  bean={partner} field='routing' label={T("Route")} disable={!writeCap} placeholder='Enter Route'
                />
              </bs.Col>
            </bs.Row>

            <div className='bb-field'>
              <bs.FormLabel>{T('Service Names')}</bs.FormLabel>
              <input.BBMultiCheckboxInputField
                bean={partner} field={'serviceNames'} disable={!writeCap}
                options={['AIR_EXPORT', 'AIR_IMPORT', 'SEA_EXPORT', 'SEA_IMPORT', 'SEA_AIR_EXP_TO_OTHER_COUNTRY', 'SEA_AIR_NOMINATED_EXPORT']}
                optionLabels={[T('Air Export'), T('Air Import'), T('Sea Export'), T('Sea Import'), T('Sea-Air Exp to Other Country'), T('Sea-Air Nominated Export')]} />
            </div>

            <input.BBTextField
              bean={partner} label={T('Bank Information.')} field="bankName" disable={!writeCap}
              style={{ height: '5rem', fontSize: '1rem' }} />

            <input.BBTextField
              bean={partner} label={T('Common Notes')} field="note" disable={!writeCap}
              style={{ height: '5rem', fontSize: '1rem' }} />

            <input.BBTextField
              bean={partner} label={T('BD Suggestions')} field="suggestion" disable={!writeCap}
              style={{ height: '5rem', fontSize: '1rem' }} />

          </div>
        </bs.Scrollable>

        <bs.Toolbar className='border'>

          <entity.ButtonEntityCommit btnLabel={`${this.state.isSending ? 'Request Agent ...' : 'Request Agent'}`}
            appContext={appContext} pageContext={pageContext}
            observer={observer} hide={!writeCap} disable={this.state.isSending}
            commit={{
              entityLabel: T('Agent'), context: 'company',
              service: "CRMPartnerService", commitMethod: "createBFSOnePartner"
            }}
            onPreCommit={this.onPreCommit} onPostCommit={this.onPostCommit} />

        </bs.Toolbar>
      </div >
    )
  }
}