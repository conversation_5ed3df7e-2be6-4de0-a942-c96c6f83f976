import React from 'react';
import * as FeatherIcon from 'react-feather';
import { bs, app, util, entity, sql } from '@datatp-ui/lib';

import { T } from '../../price';
import {
  WQuickTimeRangeSelector
} from '../../common/UIDashboardUtility';
import {
  AgentTransactionReportParams,
  WAgentTransactionGroupBySelector,
  WAgentTransactionReportFilter
} from './UIAgentTransactionsUtils';
import { UIAgentTransactionsHierarchy } from './UIAgentTransactionsHierarchyList';

export type Space = 'User' | 'Company' | 'System'

export class UIAgentTransactionsListPlugin extends entity.DbEntityListPlugin {
  space: Space;

  constructor() {
    super([]);
    this.backend = {
      context: 'company',
      service: 'PartnerReportService',
      searchMethod: 'searchAgentTransactionsReport',
      deleteMethod: '',
      changeStorageStateMethod: '',
      entityLabel: 'Agent'
    }

    this.searchParams = {
      "params": {},
      "filters": [...sql.createSearchFilter()],
      rangeFilters: [
        ...sql.createDateTimeFilter("shipmentDate", T("Shipment Date")),
        ...sql.createDateTimeFilter("modifiedDate", T("Modified Date")),
      ],
    }
  }

  loadData(uiList: entity.DbEntityList<any>) {
    this.createBackendSearch(uiList, { param: this.searchParams }).call();
  }

  withParams(name: string, value: string | undefined) {
    this.addSearchParam(name, value);
    return this;
  }

  withSearchPattern(searchPattern: string) {
    const normalizedInput = searchPattern?.toUpperCase().replace(/\s+/g, '');
    if (this.searchParams) {
      const searchFilter = this.searchParams.filters?.find(sel => sel.name === 'search');
      if (searchFilter) {
        searchFilter.filterValue = normalizedInput;
      } else {
        this.searchParams.filters?.push({ name: 'search', fields: [], filterOp: 'ilike', filterValue: normalizedInput, "required": true });
      }
    }
    return this;
  }

  withDateFilter(filterName: 'shipmentDate' | 'modifiedDate', fromValue: string, toValue: string) {
    if (this.searchParams) {
      let rangeFilters = this.searchParams.rangeFilters || [];
      for (let i = 0; i < rangeFilters.length; i++) {
        let filter = rangeFilters[i];
        if (filter.name === filterName) {
          filter.fromValue = fromValue;
          filter.toValue = toValue;
          break;
        }
      }
      this.searchParams.rangeFilters = rangeFilters;
    }
    return this;
  }
}

export interface AgentTransactionsReportFilter {
  collapsed: boolean;
  groupedBy: { label: string, value: string };
  dateFilter: { fromValue: string, toValue: string, label: string };
  partnerParams: AgentTransactionReportParams;
}

export interface AgentTransactionReportModel {
  agentTransactionRecords: any[];
  companyGroupedRecords: any[];
}

interface UIAgentTransactionsPageState { }
interface UAgentTransactionsPageProps extends app.AppComponentProps { }

export class UIBFSOneAgentTransactionsPage extends app.AppComponent<UAgentTransactionsPageProps, UIAgentTransactionsPageState> {
  viewId: number = util.IDTracker.next();
  reportFilter: AgentTransactionsReportFilter;
  agentTransactionsTreeRef: React.RefObject<UIAgentTransactionsHierarchy>;
  agentTransactionReportModel: AgentTransactionReportModel = { agentTransactionRecords: [], companyGroupedRecords: [] };

  constructor(props: app.AppComponentProps) {
    super(props);
    this.agentTransactionsTreeRef = React.createRef<UIAgentTransactionsHierarchy>();

    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    let dateFilter = new util.TimeRange();
    dateFilter.fromSetDate(firstDayOfMonth);
    dateFilter.toSetDate(lastDayOfMonth);

    this.reportFilter = {
      collapsed: false,
      groupedBy: { label: 'Agent', value: "AGENT" },
      dateFilter: { fromValue: dateFilter.fromFormat(), toValue: dateFilter.toFormat(), label: 'This Month' },
      partnerParams: {
        searchPattern: '',
        agentCode: '',
        country: '',
        continent: '',
        source: '',
        fromLocationCode: '',
        toLocationCode: '',
        companyBranch: '',
        shipmentType: ''
      }
    }
    this.markLoading(true);
  }

  componentDidMount(): void {
    this.loadData();
  }

  loadData(): void {
    const { dateFilter, partnerParams } = this.reportFilter;
    const { appContext } = this.props;

    let plugin = new UIAgentTransactionsListPlugin()
      .withDateFilter('shipmentDate', dateFilter.fromValue, dateFilter.toValue)
      .withSearchPattern(partnerParams.searchPattern)
      .withParams('continent', partnerParams.continent)
      .withParams('source', partnerParams.source)
      .withParams('country', partnerParams.country)
      .withParams('fromLocationCode', partnerParams.fromLocationCode)
      .withParams('toLocationCode', partnerParams.toLocationCode)

    appContext.createHttpBackendCall('PartnerReportService', 'reportAgentTransactionsGroupByAgent', { params: plugin.getSearchParams() })
      .withSuccessData((data: any) => {
        this.agentTransactionReportModel = data;
        this.markLoading(false);
        this.forceUpdate();
      })
      .call()
  }

  doExport = () => {
    let { appContext } = this.props;

    let agentGroupField: entity.GroupField = {
      label: "_blank_",
      fields: [
        { label: "STT.", name: "stt", dataType: 'number' },
        { label: "Agent Name", name: "agentName", dataType: 'string' },
        { label: "FH (times)", name: "freehandCount", dataType: 'integer' },
        { label: "NM (times)", name: "nominatedCount", dataType: 'integer' },
        { label: "FCL TEUs(F/H)", name: "freehandTeus", dataType: 'integer' },
        { label: `Cont 20'(F/H)`, name: "freehandCont20", dataType: 'integer' },
        { label: `Cont 40'(F/H)`, name: "freehandCont40", dataType: 'integer' },
        { label: `Cont 45'(F/H)`, name: "freehandCont45", dataType: 'integer' },
        { label: `CBM(F/H)`, name: "freehandCbm", dataType: 'number' },
        { label: `KGS(F/H)`, name: "freehandCw", dataType: 'number' },
        { label: "FCL TEUs(N/M)", name: "nominatedTeus", dataType: 'integer' },
        { label: `Cont 20'(N/M)`, name: "nominatedCont20", dataType: 'integer' },
        { label: `Cont 40'(N/M)`, name: "nominatedCont40", dataType: 'integer' },
        { label: `Cont 45'(N/M)`, name: "nominatedCont45", dataType: 'integer' },
        { label: `CBM(N/M)`, name: "nominatedCbm", dataType: 'number' },
        { label: `KGS(N/M)`, name: "nominatedCw", dataType: 'number' },
        { label: "Last contact", name: "latestContact", dataType: 'date' },
        { label: "Last Job ID", name: "latestTransactionId", dataType: 'string' },
        { label: "Source", name: "source", dataType: 'string' },
        { label: "Continent", name: "continent", dataType: 'string' },
        { label: "Country", name: "countryLabel", dataType: 'string', },
      ],
    }

    let records: Array<any> = [];
    let targetRecords = this.agentTransactionReportModel.agentTransactionRecords;
    for (let i = 0; i < targetRecords.length; i++) {
      let record = targetRecords[i];
      let newRecord: any = {
        stt: i + 1,
        agentName: record.agentName,
        freehandCount: record.freehandCount || 0,
        nominatedCount: record.nominatedCount || 0,
        freehandTeus: record.freehandTeus || 0,
        freehandCont20: record.freehandCont20 || 0,
        freehandCont40: record.freehandCont40 || 0,
        freehandCont45: record.freehandCont45 || 0,
        freehandCbm: record.freehandCbm || 0,
        freehandCw: record.freehandCw || 0,
        nominatedTeus: record.nominatedTeus || 0,
        nominatedCont20: record.nominatedCont20 || 0,
        nominatedCont40: record.nominatedCont40 || 0,
        nominatedCont45: record.nominatedCont45 || 0,
        nominatedCbm: record.nominatedCbm || 0,
        nominatedCw: record.nominatedCw || 0,
        latestContact: record.latestContact || '',
        latestTransactionId: record.latestTransactionId || '',
        source: record.source || '',
        continent: record.continent || '',
        countryLabel: record.countryLabel || '',
      };
      records.push(newRecord);
    }

    let exportModel: entity.DataListExportModel = {
      fieldGroups: [],
      fields: [...agentGroupField.fields],
      records: records,
      modelName: 'Agent_Transactions',
      fileName: `Agent_Transactions_${util.TimeUtil.toDateIdFormat(new Date())}.xlsx`,
    }

    appContext.createHttpBackendCall('DataMappingService', 'xlsxPrivateExport', { model: exportModel })
      .withSuccessData((data: any) => {
        let storeInfo = data;
        if (storeInfo.type == 'ir.actions.act_url') {
          entity.StoreInfo.download(storeInfo.url);
        } else if (storeInfo.privateMode) {
          entity.StoreInfo.privateDownload(storeInfo);
        } else {
          entity.StoreInfo.download(storeInfo.url);
        }
      })
      .call()
  }

  renderHeader() {
    const { appContext, pageContext } = this.props;
    const { dateFilter } = this.reportFilter;
    let fromDate = dateFilter.fromValue.substring(0, 10);
    let toDate = dateFilter.toValue.substring(0, 10);
    let title = `Agent Transactions (${fromDate} - ${toDate})`;
    if (this.reportFilter.groupedBy.value == 'COUNTRY') title = `Agent Transactions By Country (${fromDate} - ${toDate})`;
    if (this.reportFilter.groupedBy.value == 'NETWORK') title = `Network Performance Evaluation (${fromDate} - ${toDate})`;

    return (
      <div className="bg-white flex-hbox flex-grow-0 align-items-center justify-content-between px-2 py-1">

        <div className="flex-hbox justify-content-start align-items-center" >
          <h5 style={{ color: '#6c757d' }}>
            <FeatherIcon.Users className="me-2" size={18} />
            {title}
          </h5>

          <div className='flex-hbox align-items-center flex-grow-0 border-start' >
          </div>
        </div>

        <div className="flex-hbox justify-content-end align-items-center gap-1" >

          <WAgentTransactionGroupBySelector appContext={appContext} pageContext={pageContext}
            groupBy={this.reportFilter.groupedBy}
            onModify={(bean: any, _field: string, _oldVal: any, _newVal: any) => {
              this.reportFilter.groupedBy = bean;
              this.markLoading(true);
              this.forceUpdate();
              this.loadData();
            }} />

          <WAgentTransactionReportFilter appContext={appContext} pageContext={pageContext}
            params={this.reportFilter.partnerParams}
            onModify={(bean: any, _field: string, _oldVal: any, _newVal: any) => {
              this.reportFilter.partnerParams = bean;
              this.markLoading(true);
              this.forceUpdate();
              this.loadData();
            }} />

          <WQuickTimeRangeSelector appContext={appContext} pageContext={pageContext}
            initBean={dateFilter}
            onModify={(bean: any, _field: string, _oldVal: any, _newVal: any) => {
              this.reportFilter.dateFilter = bean;
              this.markLoading(true);
              this.forceUpdate();
              this.loadData();
            }} />

          <bs.Button laf="secondary" outline size="sm" className="p-1" style={{ color: '#6c757d' }} onClick={this.doExport}>
            <FeatherIcon.Download size={14} className="me-2" />
            Export
          </bs.Button>

        </div>
      </div>
    );
  }

  renderReportView() {
    const { appContext, pageContext } = this.props;
    const plugin = new entity.DbEntityListPlugin(this.agentTransactionReportModel.agentTransactionRecords);
    return (
      <UIAgentTransactionsHierarchy ref={this.agentTransactionsTreeRef} agentTransactionReportModel={this.agentTransactionReportModel}
        appContext={appContext} pageContext={pageContext} plugin={plugin} reportFilter={this.reportFilter} />
    )
  }

  render(): React.JSX.Element {

    return (
      <div className="flex-vbox mx-1 p-1 bg-white rounded-md w-100 h-100 my-1" >
        {this.renderHeader()}
        <div className="flex-vbox border-top bg-body-highlight h-100" key={util.IDTracker.next()}>
          {this.isLoading()
            ? this.renderLoading()
            : this.renderReportView()
          }
        </div>
      </div>
    )
  }
}
