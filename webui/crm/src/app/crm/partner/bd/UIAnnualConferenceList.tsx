import React, { RefObject } from 'react';
import * as FeatherIcon from 'react-feather';
import { util, grid, entity, sql, bs, app, server, input } from '@datatp-ui/lib';
import { module } from '@datatp-ui/erp';

import { BBRefCrmUserRole } from 'app/crm/common/template/BBRefCrmUserRole';
import { T } from 'app/crm/price';

const SESSION = app.host.DATATP_SESSION;

export class UIAnnualConferenceEditor extends entity.DbEntityEditor {
  sendToEmails: { email: string }[] = [];

  onSave = () => {
    let { appContext, observer, onPostCommit } = this.props;
    let annualConference = observer.getMutableBean();

    annualConference.sendToEmails = this.sendToEmails.filter(sel => !!sel.email).map(sel => sel.email);

    const missingNetwork = !annualConference['network'];
    if (missingNetwork) {
      let message = (<div className="ms-1 text-danger py-3 border-bottom text-center">Please enter the Network before saving.</div>);
      bs.dialogShow('Message', message, { backdrop: 'static', size: 'md' });
      return false;
    }

    const missingEvent = !annualConference['event'];
    if (missingEvent) {
      let message = (<div className="ms-1 text-danger py-3 border-bottom text-center">Please enter the Event before saving.</div>);
      bs.dialogShow('Message', message, { backdrop: 'static', size: 'md' });
      return false;
    }

    const autoReminder = annualConference['autoReminder'];
    const missingNotificationTime = !annualConference['notificationTime'];
    const missingSendToEmails = !annualConference['sendToEmails'] || annualConference['sendToEmails'].length === 0;
    if (autoReminder && (missingNotificationTime || missingSendToEmails)) {
      let message = (<div className="ms-1 text-danger py-3 border-bottom text-center">Please enter the Notification Time and Send To Emails before saving.</div>);
      bs.dialogShow('Message', message, { backdrop: 'static', size: 'md' });
      return false;
    }

    const delegatesFee = annualConference['delegatesFee'] || 0;
    const missingCurrency = !annualConference['currency'];
    if (delegatesFee > 0 && missingCurrency) {
      let message = (<div className="ms-1 text-danger py-3 border-bottom text-center">Please enter the Currency before saving.</div>);
      bs.dialogShow('Message', message, { backdrop: 'static', size: 'md' });
      return false;
    }

    appContext.createHttpBackendCall('BDService', 'saveAnnualConference', { annualConference: annualConference })
      .withSuccessData((data: any) => {
        appContext.addOSNotification("success", `Save Success`);
        observer.replaceWith(data);
        if (onPostCommit) {
          onPostCommit(annualConference);
        }
      })
      .withFail((response: server.BackendResponse) => {
        let messageError: string = response.error['message'] || 'An unexpected error occurred. Please try again later.';
        bs.dialogShow('Error',
          <div className="text-danger fw-bold text-center py-3 border-bottom">
            <FeatherIcon.AlertCircle className="mx-2" />{messageError}
          </div>,
          { backdrop: 'static', size: 'md' }
        );
        return;
      })
      .call();
  }

  handleNotificationChange = (bean: any, _field: string, _oldVal: any, newVal: boolean) => {
    if (!newVal) {
      bean.notificationTime = null;
      bean.sendToEmails = [];
      this.sendToEmails = [];
    } else if (newVal) {
      bean.notificationTime = util.TimeUtil.javaCompactDateTimeFormat(new Date());
    }
    this.forceUpdate();
  }

  onPostUpdateMail = (_inputUI: React.Component, bean: any[], selectOpt: any, _userInput: string) => {
    if (!selectOpt || Object.keys(selectOpt).length === 0 || !selectOpt['email']) return
    let email: string = selectOpt['email']
    let filtered: any[] = (bean || []).filter(sel => sel['email'] !== email);
    let updateMailBean: any = {
      email: email,
    };
    filtered.push(updateMailBean);
    this.sendToEmails = filtered;
    this.nextViewId();
    this.forceUpdate();
  }

  render() {
    const { appContext, pageContext, observer } = this.props;
    let writeCap = pageContext.hasUserWriteCapability();
    let annualConference = observer.getMutableBean();

    if ((!this.sendToEmails || this.sendToEmails.length < 1) && annualConference.sendToEmails) {
      let sendToEmails: any[] = annualConference.sendToEmails || [];
      this.sendToEmails = sendToEmails.filter(sel => !!sel).map(sel => { return { email: sel } });
    }

    return (
      <div className='flex-vbox'>
        <bs.Scrollable style={{ maxHeight: 600 }}>
          <div className='flex-grow-1 p-1'>
            <bs.Row>
              <bs.Col span={6}>
                <input.BBDateTimeField field='dateCreated' label={T('Date Created')} bean={annualConference} timeFormat={false} disable />
              </bs.Col>
              <bs.Col span={6}>
                <BBRefCrmUserRole appContext={appContext} pageContext={pageContext} placeholder='Input By' label='Input By'
                  bean={annualConference} beanIdField={'inputByAccountId'} beanLabelField={'inputByAccountLabel'} disable={!writeCap} />
              </bs.Col>
            </bs.Row>

            <bs.Row>
              <bs.Col span={6}>
                <input.BBStringField field='network' label={T('Network')} bean={annualConference} disable={!writeCap} />
              </bs.Col>
              <bs.Col span={6}>
                <input.BBStringField field='event' label={T('Event')} bean={annualConference} disable={!writeCap} />
              </bs.Col>
            </bs.Row>

            <bs.Row>
              <bs.Col span={3}>
                <input.BBDateInputMask
                  bean={annualConference} field={'startDate'} label={T('Start Period')} format='DD/MM/YYYY' disabled={!writeCap}
                  onInputChange={(bean, field, _oldVal, newVal) => { bean[field] = newVal; this.forceUpdate(); }} />
              </bs.Col>
              <bs.Col span={3}>
                <input.BBDateInputMask
                  bean={annualConference} field={'endDate'} label={T('End Period')} format='DD/MM/YYYY' disabled={!writeCap}
                  onInputChange={(bean, field, _oldVal, newVal) => { bean[field] = newVal; this.forceUpdate(); }} />
              </bs.Col>
              <bs.Col span={3}>
                <input.BBCurrencyField bean={annualConference} field={"delegatesFee"} label={T('Delegates Fee')} disable={!writeCap}
                  placeholder='Input Fee ...' />
              </bs.Col>
              <bs.Col span={3}>
                <module.settings.BBRefCurrency appContext={appContext} pageContext={pageContext}
                  bean={annualConference} beanIdField='currency' placeholder='Currency' label='Currency' disable={!writeCap} />
              </bs.Col>
            </bs.Row>

            <bs.Row>
              <bs.Col span={3}>
                <input.BBDateInputMask bean={annualConference} field={'registrationPeriod'} label={T('Registration Period')}
                  format='DD/MM/YYYY' timeFormat disabled={!writeCap}
                  onInputChange={(bean, field, _oldVal, newVal) => { bean[field] = newVal; this.forceUpdate(); }} />
              </bs.Col>
              <bs.Col span={3}>
                <input.BBIntField field='expectedAttendance' label={T('Expected Attendance')} bean={annualConference}
                  placeholder='Input Number ...' disable={!writeCap} />
              </bs.Col>
              <bs.Col span={6}>
                <input.BBStringField field='assignedDelegates' label={T('Assigned Delegates')} bean={annualConference} disable={!writeCap} />
              </bs.Col>
            </bs.Row>

            <input.BBStringField field='venue' label={T('Venue')} bean={annualConference} disable={!writeCap} />

            <input.BBTextField field='note' label={T('Note')} bean={annualConference} style={{ height: '6em' }} disable={!writeCap} />

            <div className='flex-vbox border rounded bg-light mt-1 p-1'>
              <div className="d-flex align-items-center justify-content-start gap-4 ps-1"
                style={{ minHeight: 48 }}>
                <label className="d-flex align-items-center gap-2 mb-0 cursor-pointer hover-opacity">
                  <input.BBCheckboxField field='autoReminder' bean={annualConference} value={false} disable={!writeCap}
                    onInputChange={this.handleNotificationChange} />
                  <span className="form-label fw-medium mb-0 text-nowrap text-secondary">{T('Auto Reminder')}</span>
                </label>

                {
                  annualConference.autoReminder &&
                  <div className="d-flex align-items-center gap-2 border-start ps-4">
                    <span className="form-label fw-medium mb-0 text-nowrap text-secondary">{T('Notification Time')}:</span>
                    <input.BBDateInputMask bean={annualConference} field={'notificationTime'} format='DD/MM/YYYY' timeFormat
                      className="flex-grow-1 form-control-sm" style={{ width: 120 }}
                      onInputChange={(bean, field, _oldVal, newVal) => { bean[field] = newVal; this.forceUpdate(); }} />
                  </div>
                }
              </div>
              {
                annualConference.autoReminder &&
                <div className='bb-field ps-1 pt-2 border-top'>
                  <bs.FormLabel>Send To Emails ({this.sendToEmails.length})</bs.FormLabel>
                  <module.communication.message.BBRefMultiEmail
                    beanIdField='email' beanLabelField='' disable={!writeCap}
                    appContext={appContext} pageContext={pageContext} placeholder="Enter to email..." className='w-100' minWidth={600}
                    placement="bottom-start" offset={[0, 5]} bean={this.sendToEmails} hideMoreInfo
                    onPostUpdate={this.onPostUpdateMail} />
                </div>
              }
            </div>

          </div>
        </bs.Scrollable>
        <bs.Toolbar className='border' >
          <entity.WButtonEntityWrite disable={!writeCap}
            appContext={appContext} pageContext={pageContext}
            icon={FeatherIcon.Save} label={T('Save')}
            onClick={this.onSave}
          />
        </bs.Toolbar>
      </div >
    );
  }
}

export class UIAnnualConferenceListPlugin extends entity.DbEntityListPlugin {
  constructor(space: 'User' | 'Company' | 'System') {
    super([]);

    this.backend = {
      context: 'company',
      service: 'BDService',
      searchMethod: 'searchAnnualConferences'
    }

    this.searchParams = {
      "params": { space: space },
      "filters": [
        ...sql.createSearchFilter()
      ],
      "optionFilters": [
        sql.createStorageStateFilter([entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED])
      ],
      "orderBy": {
        fields: ["modifiedTime"],
        fieldLabels: ["Modified Time"],
        selectFields: ["modifiedTime"],
        sort: "DESC"
      },
      maxReturn: 1000,
    }
  }

  withSearchPattern(pattern: string) {
    if (!this.searchParams) throw new Error("Need to config search params");
    const searchFilter = this.searchParams.filters?.find(sel => sel.name === 'search');
    if (searchFilter) {
      searchFilter.filterValue = pattern
    } else if (this.searchParams.filters) {
      this.searchParams.filters = [...sql.createSearchFilter(pattern)];
    }
    return this;
  }


  loadData(uiList: entity.DbEntityList<any>) {
    this.createBackendSearch(uiList, { params: this.searchParams }).call();
  }
}

export interface UIAnnualConferenceListProps extends entity.DbEntityListProps { }

export class UIAnnualConferenceList extends entity.DbEntityList<UIAnnualConferenceListProps> {

  createVGridConfig(): grid.VGridConfig {
    const CELL_HEIGHT: number = 70;
    let config: grid.VGridConfig = {
      record: {
        dataCellHeight: CELL_HEIGHT,
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          entity.DbEntityListConfigTool.FIELD_ON_SELECT('network', T('Network'), 120),
          {
            name: 'event', label: T('Event'), width: 300,
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              return (
                <input.BBTextField bean={dRecord.record} field={field.name} style={{ height: CELL_HEIGHT }} disable />
              );
            }
          },
          {
            name: 'startDate', label: T('Start Date'), width: 150, format: util.text.formater.compactDate,
            filterable: true, filterableType: 'date', sortable: true,
          },
          {
            name: 'endDate', label: T('End Date'), width: 150, format: util.text.formater.compactDate,
            filterable: true, filterableType: 'date', sortable: true,
          },
          { name: 'venue', label: T('Venue'), width: 200 },
          { name: 'delegatesFee', label: T('Delegates Fee'), width: 150 },
          { name: 'registrationPeriod', label: T('Registration Period'), width: 160, format: util.text.formater.compactDate },
          { name: 'expectedAttendance', label: T('Expected Attendance'), width: 160 },
          { name: 'assignedDelegates', label: T('Assigned Delegates'), width: 150 },
          {
            name: 'note', label: T('Note'), width: 300,
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              return (
                <input.BBTextField bean={dRecord.record} field={field.name} style={{ height: CELL_HEIGHT }} disable />
              );
            }
          },
          { name: 'inputByAccountLabel', label: T('Input By'), width: 250, filterable: true, filterableType: 'options' },
          {
            name: 'dateCreated', label: T('Date Created'), width: 150, format: util.text.formater.compactDate,
            filterable: true, filterableType: 'date', sortable: true,
          },
          {
            name: 'autoReminder', label: T('Reminder'), width: 170, filterable: true, filterableType: 'boolean',
            customRender: (_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let rec = dRecord.record;
              if (!rec.autoReminder) return (
                <div className='d-flex w-100 justify-content-center align-items-center'>
                  <FeatherIcon.XCircle size={18} className='text-danger' />
                </div>
              )
              let notiTime = rec.notificationTime;
              let notiTimeStr = 'Notification Time Not Set';
              notiTimeStr = util.TimeUtil.format(notiTime, 'DD/MM/YYYY HH:mm')
              return (
                <div className='d-flex gap-1 w-100'>
                  <div className='d-flex flex-grow-0'>
                    <FeatherIcon.CheckCircle size={18} className='text-success' />
                  </div>
                  <div className='d-flex flex-grow-1'>
                    {notiTimeStr}
                  </div>
                </div>
              );
            }
          },
        ]
      },
      toolbar: { hide: true },
      footer: {
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          }
        }
      }
    }
    return config;
  }

  onDefaultSelect(dRecord: grid.DisplayRecord): void {
    let { appContext, pageContext } = this.props;
    let record = dRecord.record;
    appContext.createHttpBackendCall('BDService', 'getAnnualConferenceById', { id: record.id })
      .withSuccessData((data: any) => {
        let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (
            <UIAnnualConferenceEditor appContext={appCtx} pageContext={pageCtx} observer={new entity.BeanObserver(data)}
              onPostCommit={(entity: any) => {
                pageCtx.back();
                this.onModifyBean(entity);
              }} />
          );
        }
        pageContext.createPopupPage("edit-annual-conference", T("Annual Conference"), createAppPage, { size: 'flex-lg', backdrop: "static" });
      })
      .call();
  }

  onModifyBean = (bean: any, action?: entity.ModifyBeanActions) => {
    let { onModifyBean } = this.props;
    if (onModifyBean) {
      onModifyBean(bean, action);
    } else {
      this.viewId = util.IDTracker.next();
      this.forceUpdate();
    }
  }
}

interface UIAnnualConferenceListPageProps extends app.AppComponentProps {
  space: 'User' | 'Company' | 'System'
}
export class UIAnnualConferenceListPage extends app.AppComponent<UIAnnualConferenceListPageProps> {
  listRef: RefObject<UIAnnualConferenceList>;
  viewId: number = util.IDTracker.next();
  plugin: UIAnnualConferenceListPlugin;
  filter: any = { maxReturn: 500, pattern: '' };

  constructor(props: UIAnnualConferenceListPageProps) {
    super(props);
    this.listRef = React.createRef();
    this.plugin = new UIAnnualConferenceListPlugin(this.props.space);
  }

  componentDidMount(): void {
    if (this.listRef.current) this.forceUpdate();
  }

  onModify = (_bean: any, _field: string, _oldVal: any, _newVal: any): void => {
    this.viewId = util.IDTracker.next();
    this.forceUpdate();
  };

  onModifyBean = (_bean: any, _action?: entity.ModifyBeanActions) => {
    this.viewId = util.IDTracker.next();
    this.forceUpdate();
  }

  onChangePattern = (_oldVal: any, newVal: any) => {
    if (_oldVal !== newVal) {
      this.filter.pattern = newVal;
      if (this.listRef.current) {
        let uiList: UIAnnualConferenceList = this.listRef.current;
        uiList.getVGridContext().model.getRecordFilter().withPattern(newVal);
        uiList.getVGridContext().model.filter();
        uiList.forceUpdate();
      }
    }
  }

  onAdd = () => {
    let { pageContext } = this.props;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {

      let today: string = util.TimeUtil.javaCompactDateTimeFormat(new Date());
      let newBean = {
        inputByAccountId: SESSION.getAccountId(),
        inputByAccountLabel: SESSION.getAccountAcl().getFullName(),
        dateCreated: today,
        currency: 'USD',
        autoReminder: false,
      }

      return (
        <UIAnnualConferenceEditor appContext={appCtx} pageContext={pageCtx} observer={new entity.BeanObserver(newBean)}
          onPostCommit={(_entity: any) => {
            pageCtx.back();
            this.viewId = util.IDTracker.next();
            this.forceUpdate();
          }} />
      );
    }
    pageContext.createPopupPage("new-annual-conference", T("New Annual Conference"), createAppPage, { size: 'flex-lg', backdrop: "static" });
  }

  onDeleteAction(): void {
    const { appContext } = this.props;
    const selectedIds = this.plugin.getListModel().getSelectedRecordIds();

    if (selectedIds.length === 0) {
      bs.notificationShow("warning", T("warning"), T("No Annual Conference Selected!"));
      return;
    }

    const onConfirmDelete = () => {
      appContext.createHttpBackendCall('BDService', 'deleteAnnualConferenceByIds', { ids: selectedIds })
        .withSuccessData((_data: any) => {
          this.viewId = util.IDTracker.next();
          this.forceUpdate();
        })
        .withEntityOpNotification('delete', 'Annual Conference')
        .call();
    };

    let messageEle = (<div className="text-danger">Do you want to delete these records?</div>);
    bs.dialogConfirmMessage(T("Confirm Delete"), messageEle, onConfirmDelete);
  }

  render() {
    const { appContext, pageContext } = this.props;
    let writeCap = pageContext.hasUserWriteCapability();

    return (
      <div className='flex-vbox'>
        <div className='bg-white flex-hbox flex-grow-0 justify-content-between align-items-center mx-1 px-1'>
          <div className="flex-hbox justify-content-start align-items-center flex-grow-0 gap-2" >
            <input.BBSelectField className="fw-bold text-primary border-bottom text-center"
              style={{ width: 150 }} bean={this.filter} field={"maxReturn"}
              options={[500, 1000, 2000, 5000]}
              optionLabels={['Show 500 records', 'Show 1000 records', 'Show 2000 records', 'Show 5000 records']}
              onInputChange={this.onModify} />

            <div className='flex-hbox align-items-center flex-grow-0 ps-2'>
              <input.WStringInput className={'flex-hbox'} style={{ width: 300 }}
                name='search' value={this.filter.pattern}
                placeholder={('Enter Network or Event...')} onChange={this.onChangePattern} />
            </div>
          </div>

          <div className="flex-hbox flex-grow-0 justify-content-end align-items-center">

            <bs.Button laf='info' className="border-0 border-end rounded-0 p-1 mx-1" outline
              onClick={this.onAdd} hidden={bs.ScreenUtil.isMobileScreen() || !writeCap} >
              <FeatherIcon.Plus size={12} /> Add
            </bs.Button>

            <bs.Button laf='warning' className="border-0 p-1 mx-1 border-end rounded-0" outline
              onClick={() => this.onDeleteAction()} hidden={bs.ScreenUtil.isMobileScreen() || !writeCap}>
              <FeatherIcon.Trash size={12} /> Del
            </bs.Button>

            {
              this.listRef.current ?
                <XLSXButton appContext={appContext} pageContext={pageContext} context={this.listRef.current.getVGridContext()} />
                : null
            }
          </div>
        </div>

        <div className='flex-vbox' key={this.viewId}>
          <UIAnnualConferenceList ref={this.listRef}
            appContext={appContext} pageContext={pageContext} plugin={this.plugin} onModifyBean={this.onModifyBean} />
        </div>
      </div>
    )
  }
}

class XLSXButton extends entity.XlsxExportButton {
  override customDataListExportModel = (model: entity.DataListExportModel) => {
    model["fileName"] = 'Annual Conference.xlsx'
    for (let fieldGroup of model.fieldGroups) {
      if (!fieldGroup.label) fieldGroup.label = '_blank_'
      for (let field of fieldGroup.fields) {
        field.dataType = field.dataType?.toLowerCase();
      }
    }

    for (let field of model.fields) {
      field.dataType = field.dataType?.toLowerCase();
    }
    return model;
  }

  render() {
    const { style, disable } = this.props;
    return (
      <bs.Button laf={"info"} onClick={this.onExportCustomization} className="border-0 border-end rounded-0 p-1 mx-1"
        style={style} outline disabled={disable} >
        <FeatherIcon.Download size={12} /> {T('XLSX Export')}
      </bs.Button>
    )
  }
}
