import React, { RefObject } from 'react';
import * as FeatherIcon from 'react-feather';
import { util, grid, entity, sql, bs, app, server, input } from '@datatp-ui/lib';
import { module } from '@datatp-ui/erp';

import { BBRefCrmUserRole } from 'app/crm/common/template/BBRefCrmUserRole';
import { T } from 'app/crm/price';

const SESSION = app.host.DATATP_SESSION;

export class UINetworkMembershipFeeEditor extends entity.DbEntityEditor {
  sendToEmails: { email: string }[] = [];

  onSave = () => {
    let { appContext, observer, onPostCommit } = this.props;
    let fee = observer.getMutableBean();

    fee.sendToEmails = this.sendToEmails.filter(sel => !!sel.email).map(sel => sel.email);

    const missingNetwork = !fee['network'];
    if (missingNetwork) {
      let message = (<div className="ms-1 text-danger py-3 border-bottom text-center">Please enter the Network before saving.</div>);
      bs.dialogShow('Message', message, { backdrop: 'static', size: 'md' });
      return false;
    }

    const autoReminder = fee['autoReminder'];
    const missingNotificationTime = !fee['notificationTime'];
    const missingSendToEmails = !fee['sendToEmails'] || fee['sendToEmails'].length === 0;
    if (autoReminder && (missingNotificationTime || missingSendToEmails)) {
      let message = (<div className="ms-1 text-danger py-3 border-bottom text-center">Please enter the Notification Time and Send To Emails before saving.</div>);
      bs.dialogShow('Message', message, { backdrop: 'static', size: 'md' });
      return false;
    }

    const hasFinancialProtection = fee['hasFinancialProtection'];
    const missingFinancialProtection = !fee['financialProtection'];
    if (hasFinancialProtection && missingFinancialProtection) {
      let message = (<div className="ms-1 text-danger py-3 border-bottom text-center">Please enter the Financial Protection before saving.</div>);
      bs.dialogShow('Message', message, { backdrop: 'static', size: 'md' });
      return false;
    }

    const annualMembershipFee = fee['annualMembershipFee'] || 0;
    const missingCurrency = !fee['currency'];
    if (annualMembershipFee > 0 && missingCurrency) {
      let message = (<div className="ms-1 text-danger py-3 border-bottom text-center">Please enter the Currency before saving.</div>);
      bs.dialogShow('Message', message, { backdrop: 'static', size: 'md' });
      return false;
    }

    appContext.createHttpBackendCall('BDService', 'saveNetworkMembershipFee', { fee: fee })
      .withSuccessData((data: any) => {
        appContext.addOSNotification("success", `Save Success`);
        observer.replaceWith(data);
        if (onPostCommit) {
          onPostCommit(fee);
        }
      })
      .withFail((response: server.BackendResponse) => {
        let messageError: string = response.error['message'] || 'An unexpected error occurred. Please try again later.';
        bs.dialogShow('Error',
          <div className="text-danger fw-bold text-center py-3 border-bottom">
            <FeatherIcon.AlertCircle className="mx-2" />{messageError}
          </div>,
          { backdrop: 'static', size: 'md' }
        );
        return;
      })
      .call();
  }

  handleFinancialProtectionChange = (bean: any, _field: string, _oldVal: any, newVal: boolean) => {
    if (!newVal) {
      bean.financialProtection = null;
    }
    this.forceUpdate();
  }

  handleNotificationChange = (bean: any, _field: string, _oldVal: any, newVal: boolean) => {
    if (!newVal) {
      bean.notificationTime = null;
      bean.sendToEmails = [];
      this.sendToEmails = [];
    } else if (newVal) {
      bean.notificationTime = util.TimeUtil.javaCompactDateTimeFormat(new Date());
    }
    this.forceUpdate();
  }

  onPostUpdateMail = (_inputUI: React.Component, bean: any[], selectOpt: any, _userInput: string) => {
    if (!selectOpt || Object.keys(selectOpt).length === 0 || !selectOpt['email']) return
    let email: string = selectOpt['email']
    let filtered: any[] = (bean || []).filter(sel => sel['email'] !== email);
    let updateMailBean: any = {
      email: email,
    };
    filtered.push(updateMailBean);
    this.sendToEmails = filtered;
    this.nextViewId();
    this.forceUpdate();
  }

  render() {
    const { appContext, pageContext, observer } = this.props;
    let writeCap = pageContext.hasUserWriteCapability();
    let fee = observer.getMutableBean();

    if ((!this.sendToEmails || this.sendToEmails.length < 1) && fee.sendToEmails) {
      let sendToEmails: any[] = fee.sendToEmails || [];
      this.sendToEmails = sendToEmails.filter(sel => !!sel).map(sel => { return { email: sel } });
    }

    return (
      <div className='flex-vbox'>
        <bs.Scrollable style={{ maxHeight: 600 }}>
          <div className='flex-grow-1 p-1'>

            <bs.Row>
              <bs.Col span={6}>
                <input.BBDateTimeField field='dateCreated' label={T('Date Created')} bean={fee} timeFormat={false} disable />
              </bs.Col>
              <bs.Col span={6}>
                <BBRefCrmUserRole appContext={appContext} pageContext={pageContext} placeholder='Input By' label='Input By'
                  bean={fee} beanIdField={'inputByAccountId'} beanLabelField={'inputByAccountLabel'} disable={!writeCap} />
              </bs.Col>
            </bs.Row>

            <input.BBStringField field='network' label={T('Network')} bean={fee} disable={!writeCap} />

            <bs.Row>
              <bs.Col span={6}>
                <input.BBStringField field='representativeAdminName' label={T('Representative Admin Name')} bean={fee} disable={!writeCap} />
              </bs.Col>
              <bs.Col span={6}>
                <input.BBStringField field='representativeAdminEmail' label={T('Representative Admin Email')} bean={fee}
                  validators={[util.validator.EMAIL_VALIDATOR]} disable={!writeCap} />
              </bs.Col>
            </bs.Row>

            <bs.Row>
              <bs.Col span={3}>
                <input.BBIntField field='numberOfMembers' label={T('Number Of Members')} bean={fee} disable={!writeCap}
                  placeholder='Input Number ...' />
              </bs.Col>
              <bs.Col span={3}>
                <input.BBStringField field='website' label={T('Website')} bean={fee} disable={!writeCap} />
              </bs.Col>
              <bs.Col span={3}>
                <input.BBCurrencyField bean={fee} field={"annualMembershipFee"} label={T('Annual Membership Fee')} disable={!writeCap}
                  placeholder='Input Fee ...' />
              </bs.Col>
              <bs.Col span={3}>
                <module.settings.BBRefCurrency appContext={appContext} pageContext={pageContext}
                  bean={fee} beanIdField='currency' placeholder='Currency' label='Currency' disable={!writeCap} />
              </bs.Col>
            </bs.Row>

            <bs.Row>
              <bs.Col span={3}>
                <input.BBDateInputMask bean={fee} field={'startDate'} label={T('Start Date')} format='DD/MM/YYYY' disabled={!writeCap}
                  onInputChange={(bean, field, _oldVal, newVal) => { bean[field] = newVal; this.forceUpdate(); }} />
              </bs.Col>
              <bs.Col span={3}>
                <input.BBDateInputMask bean={fee} field={'expireDate'} label={T('Expire Date')} format='DD/MM/YYYY' disabled={!writeCap}
                  onInputChange={(bean, field, _oldVal, newVal) => { bean[field] = newVal; this.forceUpdate(); }} />
              </bs.Col>
              <bs.Col span={6}>
                <div className="d-flex align-items-center justify-content-start gap-4 mt-1"
                  style={{ minHeight: 48 }}>
                  <label className="d-flex flex-grow-0 align-items-center gap-2 mb-0 cursor-pointer hover-opacity">
                    <bs.FormLabel>{T('Financial Protection')}:</bs.FormLabel>
                    <input.BBCheckboxField field='hasFinancialProtection' bean={fee} value={false}
                      onInputChange={this.handleFinancialProtectionChange} disable={!writeCap} />
                  </label>

                  {
                    fee.hasFinancialProtection &&
                    <div className="d-flex flex-grow-1 align-items-center gap-2 border-start ps-4">
                      <input.BBStringField field='financialProtection' bean={fee} disable={!writeCap} placeholder='Financial Protection' />
                    </div>
                  }
                </div>
              </bs.Col>
            </bs.Row>

            <input.BBTextField field='note' label={T('Note')} bean={fee} style={{ height: '6em' }} disable={!writeCap} />


            <div className='flex-vbox border rounded bg-light mt-1 p-1'>
              <div className="d-flex align-items-center justify-content-start gap-4 ps-1"
                style={{ minHeight: 48 }}>
                <label className="d-flex align-items-center gap-2 mb-0 cursor-pointer hover-opacity">
                  <input.BBCheckboxField field='autoReminder' bean={fee} value={false} disable={!writeCap}
                    onInputChange={this.handleNotificationChange} />
                  <span className="form-label fw-medium mb-0 text-nowrap text-secondary">{T('Auto Reminder')}</span>
                </label>

                {
                  fee.autoReminder &&
                  <div className="d-flex align-items-center gap-2 border-start ps-4">
                    <span className="form-label fw-medium mb-0 text-nowrap text-secondary">{T('Notification Time')}:</span>
                    <input.BBDateInputMask bean={fee} field={'notificationTime'} format='DD/MM/YYYY' timeFormat
                      className="flex-grow-1 form-control-sm" style={{ width: 120 }}
                      onInputChange={(bean, field, _oldVal, newVal) => { bean[field] = newVal; this.forceUpdate(); }} />
                  </div>
                }
              </div>
              {
                fee.autoReminder &&
                <div className='bb-field ps-1 pt-2 border-top'>
                  <bs.FormLabel>Send To Emails ({this.sendToEmails.length})</bs.FormLabel>
                  <module.communication.message.BBRefMultiEmail
                    beanIdField='email' beanLabelField='' disable={!writeCap}
                    appContext={appContext} pageContext={pageContext} placeholder="Enter to email..." className='w-100' minWidth={600}
                    placement="bottom-start" offset={[0, 5]} bean={this.sendToEmails} hideMoreInfo
                    onPostUpdate={this.onPostUpdateMail} />
                </div>
              }
            </div>

          </div>
        </bs.Scrollable>
        <bs.Toolbar className='border' >
          <entity.WButtonEntityWrite disable={!writeCap}
            appContext={appContext} pageContext={pageContext}
            icon={FeatherIcon.Save} label={T('Save')}
            onClick={this.onSave}
          />
        </bs.Toolbar>
      </div >
    );
  }
}

export class UINetworkMembershipFeeListPlugin extends entity.DbEntityListPlugin {
  constructor(space: 'User' | 'Company' | 'System') {
    super([]);

    this.backend = {
      context: 'company',
      service: 'BDService',
      searchMethod: 'searchNetworkMembershipFees'
    }

    this.searchParams = {
      "params": { space: space },
      "filters": [
        ...sql.createSearchFilter()
      ],
      "optionFilters": [
        sql.createStorageStateFilter([entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED])
      ],
      "orderBy": {
        fields: ["modifiedTime"],
        fieldLabels: ["Modified Time"],
        selectFields: ["modifiedTime"],
        sort: "DESC"
      },
      maxReturn: 1000,
    }
  }

  withSearchPattern(pattern: string) {
    if (!this.searchParams) throw new Error("Need to config search params");
    const searchFilter = this.searchParams.filters?.find(sel => sel.name === 'search');
    if (searchFilter) {
      searchFilter.filterValue = pattern
    } else if (this.searchParams.filters) {
      this.searchParams.filters = [...sql.createSearchFilter(pattern)];
    }
    return this;
  }

  loadData(uiList: entity.DbEntityList<any>) {
    this.createBackendSearch(uiList, { params: this.searchParams }).call();
  }
}

export interface UINetworkMembershipFeeListProps extends entity.DbEntityListProps { }

export class UINetworkMembershipFeeList extends entity.DbEntityList<UINetworkMembershipFeeListProps> {

  createVGridConfig(): grid.VGridConfig {
    const CELL_HEIGHT: number = 70;
    let config: grid.VGridConfig = {
      record: {
        dataCellHeight: CELL_HEIGHT,
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          entity.DbEntityListConfigTool.FIELD_ON_SELECT('network', T('Network'), 120),
          { name: 'representativeAdminName', label: T('Name'), hint: T('Representative Admin Name'), width: 300 },
          { name: 'representativeAdminEmail', label: T('Email'), hint: T('Representative Admin Email'), width: 250 },
          { name: 'numberOfMembers', label: T('N.O Members'), hint: T('Number Of Members'), width: 120 },
          { name: 'website', label: T('Website'), width: 200 },
          { name: 'annualMembershipFee', label: T('Annual Membership Fee'), width: 200, format: util.text.formater.currency },
          { name: 'currency', label: T('Currency'), width: 100 },
          {
            name: 'startDate', label: T('Start Date'), width: 150, format: util.text.formater.compactDate,
            filterable: true, filterableType: 'date', sortable: true,
          },
          {
            name: 'expireDate', label: T('Expire Date'), width: 150, format: util.text.formater.compactDate,
            filterable: true, filterableType: 'date', sortable: true,
          },
          {
            name: 'autoReminder', label: T('Reminder'), width: 170, filterable: true, filterableType: 'boolean',
            customRender: (_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let fee = dRecord.record;
              if (!fee.autoReminder) return (
                <div className='d-flex w-100 justify-content-center align-items-center'>
                  <FeatherIcon.XCircle size={18} className='text-danger' />
                </div>
              )
              let notiTime = fee.notificationTime;
              let notiTimeStr = 'Notification Time Not Set';
              notiTimeStr = util.TimeUtil.format(notiTime, 'DD/MM/YYYY HH:mm')
              return (
                <div className='d-flex gap-1 w-100'>
                  <div className='d-flex flex-grow-0'>
                    <FeatherIcon.CheckCircle size={18} className='text-success' />
                  </div>
                  <div className='d-flex flex-grow-1'>
                    {notiTimeStr}
                  </div>
                </div>
              );
            }
          },
          {
            name: 'hasFinancialProtection', label: T('Financial Protection'), width: 300,
            customRender: (_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let fee = dRecord.record;
              if (!fee.hasFinancialProtection) return (
                <div className='d-flex w-100 justify-content-center align-items-center'>
                  <FeatherIcon.XCircle size={18} className='text-danger' />
                </div>
              )
              return (
                <div className='d-flex gap-1 w-100'>
                  <div className='d-flex flex-grow-0 align-items-center'>
                    <FeatherIcon.CheckCircle size={18} className='text-success' />
                  </div>
                  <div className='d-flex flex-grow-1'>
                    <input.BBTextField field='financialProtection' bean={dRecord.record} style={{ height: CELL_HEIGHT }} disable />
                  </div>
                </div>
              );
            }
          },
          {
            name: 'note', label: T('Note'), width: 300,
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              return (
                <input.BBTextField bean={dRecord.record} field={field.name} style={{ height: CELL_HEIGHT }} disable />
              );
            }
          },
          {
            name: 'dateCreated', label: T('Date Created'), width: 150, format: util.text.formater.compactDate,
            filterable: true, filterableType: 'date', sortable: true,
          },
          { name: 'inputByAccountLabel', label: T('Input By'), width: 250, filterable: true, filterableType: 'options' },
        ],
        fieldGroups: {
          representativeAdmin: {
            label: 'Representative Admin',
            fields: ['representativeAdminName', 'representativeAdminEmail']
          },
        },
      },
      toolbar: { hide: true },
      footer: {
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          }
        }
      }
    }
    return config;
  }

  onDefaultSelect(dRecord: grid.DisplayRecord): void {
    let { appContext, pageContext } = this.props;
    let record = dRecord.record;
    appContext.createHttpBackendCall('BDService', 'getNetworkMembershipFeeById', { id: record.id })
      .withSuccessData((data: any) => {
        let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (
            <UINetworkMembershipFeeEditor appContext={appCtx} pageContext={pageCtx} observer={new entity.BeanObserver(data)}
              onPostCommit={(entity: any) => {
                pageCtx.back();
                this.onModifyBean(entity);
              }} />
          );
        }
        pageContext.createPopupPage("edit-network-membership-fee", T("Network Membership Fee"), createAppPage, { size: 'flex-lg', backdrop: "static" });
      })
      .call();
  }

  onModifyBean = (bean: any, action?: entity.ModifyBeanActions) => {
    let { onModifyBean } = this.props;
    if (onModifyBean) {
      onModifyBean(bean, action);
    } else {
      this.viewId = util.IDTracker.next();
      this.forceUpdate();
    }
  }
}

interface UINetworkMembershipFeeListPageProps extends app.AppComponentProps {
  space: 'User' | 'Company' | 'System'
}
export class UINetworkMembershipFeeListPage extends app.AppComponent<UINetworkMembershipFeeListPageProps> {
  listRef: RefObject<UINetworkMembershipFeeList>;
  viewId: number = util.IDTracker.next();
  plugin: UINetworkMembershipFeeListPlugin;
  filter: any = { maxReturn: 500, pattern: '' };

  constructor(props: UINetworkMembershipFeeListPageProps) {
    super(props);
    this.listRef = React.createRef();
    this.plugin = new UINetworkMembershipFeeListPlugin(this.props.space);
  }

  componentDidMount(): void {
    if (this.listRef.current) this.forceUpdate();
  }

  onModify = (_bean: any, _field: string, _oldVal: any, _newVal: any): void => {
    this.viewId = util.IDTracker.next();
    this.forceUpdate();
  };

  onModifyBean = (_bean: any, _action?: entity.ModifyBeanActions) => {
    this.viewId = util.IDTracker.next();
    this.forceUpdate();
  }

  onChangePattern = (_oldVal: any, newVal: any) => {
    if (_oldVal !== newVal) {
      this.filter.pattern = newVal;
      if (this.listRef.current) {
        let uiList: UINetworkMembershipFeeList = this.listRef.current;
        uiList.getVGridContext().model.getRecordFilter().withPattern(newVal);
        uiList.getVGridContext().model.filter();
        uiList.forceUpdate();
      }
    }
  }

  onAdd = () => {
    let { pageContext } = this.props;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {

      let today: string = util.TimeUtil.javaCompactDateTimeFormat(new Date());
      let newBean = {
        currency: 'USD',
        inputByAccountId: SESSION.getAccountId(),
        inputByAccountLabel: SESSION.getAccountAcl().getFullName(),
        dateCreated: today,
        autoReminder: false,
        hasFinancialProtection: false,
      }

      return (
        <UINetworkMembershipFeeEditor appContext={appCtx} pageContext={pageCtx} observer={new entity.BeanObserver(newBean)}
          onPostCommit={(_entity: any) => {
            pageCtx.back();
            this.viewId = util.IDTracker.next();
            this.forceUpdate();
          }} />
      );
    }
    pageContext.createPopupPage("new-network-membership-fee", T("New Network Membership Fee"), createAppPage, { size: 'flex-lg', backdrop: "static" });
  }

  onDeleteAction(): void {
    const { appContext } = this.props;
    const selectedIds = this.plugin.getListModel().getSelectedRecordIds();

    if (selectedIds.length === 0) {
      bs.notificationShow("warning", T("warning"), T("No Network Membership Fee Selected!"));
      return;
    }

    const onConfirmDelete = () => {
      appContext.createHttpBackendCall('BDService', 'deleteNetworkMembershipFeeByIds', { ids: selectedIds })
        .withSuccessData((_data: any) => {
          this.viewId = util.IDTracker.next();
          this.forceUpdate();
        })
        .withEntityOpNotification('delete', 'Network Membership Fee')
        .call();
    };

    let messageEle = (<div className="text-danger">Do you want to delete these records?</div>);
    bs.dialogConfirmMessage(T("Confirm Delete"), messageEle, onConfirmDelete);
  }

  render() {
    const { appContext, pageContext } = this.props;
    let writeCap = pageContext.hasUserWriteCapability();

    return (
      <div className='flex-vbox'>
        <div className='bg-white flex-hbox flex-grow-0 justify-content-between align-items-center mx-1 px-1'>
          <div className="flex-hbox justify-content-start align-items-center flex-grow-0 gap-2" >
            <input.BBSelectField className="fw-bold text-primary border-bottom text-center"
              style={{ width: 150 }} bean={this.filter} field={"maxReturn"}
              options={[500, 1000, 2000, 5000]}
              optionLabels={['Show 500 records', 'Show 1000 records', 'Show 2000 records', 'Show 5000 records']}
              onInputChange={this.onModify} />

            <div className='flex-hbox align-items-center flex-grow-0 ps-2'>
              <input.WStringInput className={'flex-hbox'} style={{ width: 300 }}
                name='search' value={this.filter.pattern}
                placeholder={('Enter Network or Representative ...')} onChange={this.onChangePattern} />
            </div>
          </div>

          <div className="flex-hbox flex-grow-0 justify-content-end align-items-center">

            <bs.Button laf='info' className="border-0 border-end rounded-0 p-1 mx-1" outline
              onClick={this.onAdd} hidden={bs.ScreenUtil.isMobileScreen() || !writeCap} >
              <FeatherIcon.Plus size={12} /> Add
            </bs.Button>

            <bs.Button laf='warning' className="border-0 p-1 mx-1 border-end rounded-0" outline
              onClick={() => this.onDeleteAction()} hidden={bs.ScreenUtil.isMobileScreen() || !writeCap}>
              <FeatherIcon.Trash size={12} /> Del
            </bs.Button>

            {
              this.listRef.current ?
                <XLSXButton appContext={appContext} pageContext={pageContext} context={this.listRef.current.getVGridContext()} />
                : null
            }
          </div>
        </div>

        <div className='flex-vbox' key={this.viewId}>
          <UINetworkMembershipFeeList ref={this.listRef}
            appContext={appContext} pageContext={pageContext} plugin={this.plugin} onModifyBean={this.onModifyBean} />
        </div>
      </div >
    )
  }
}

class XLSXButton extends entity.XlsxExportButton {
  override customDataListExportModel = (model: entity.DataListExportModel) => {
    model["fileName"] = 'Network Membership Fee.xlsx'
    for (let fieldGroup of model.fieldGroups) {
      if (!fieldGroup.label) fieldGroup.label = '_blank_'
      for (let field of fieldGroup.fields) {
        field.dataType = field.dataType?.toLowerCase();
      }
    }

    for (let field of model.fields) {
      field.dataType = field.dataType?.toLowerCase();
    }
    return model;
  }

  render() {
    const { style, disable } = this.props;
    return (
      <bs.Button laf={"info"} onClick={this.onExportCustomization} className="border-0 border-end rounded-0 p-1 mx-1"
        style={style} outline disabled={disable} >
        <FeatherIcon.Download size={12} /> {T('XLSX Export')}
      </bs.Button>
    )
  }
}
