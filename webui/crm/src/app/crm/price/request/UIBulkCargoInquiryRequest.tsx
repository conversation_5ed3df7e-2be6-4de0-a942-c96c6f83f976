import React, { createRef, RefObject } from 'react';
import { util, input, entity, component, bs, app } from '@datatp-ui/lib';
import { module } from '@datatp-ui/erp';
import * as FeatherIcon from "react-feather";

import { Stackable, StackableTypeUtils, StackableUtils, T } from '../backend';

import { TABLE_STYLE, TEXT_STYLE } from '../common';

import BBRefLocation = module.settings.BBRefLocation;
import { BBRefCommodityType } from 'app/crm/common/commodity';
import { BBRefUserCustomer } from 'app/crm/sales/partners/BBRefUserCustomer';

export const BULK_CARGO_INQUIRY_ENTITY_NAME = 'lgc_price_bulk_cargo_inquiry_request';
const SESSION = app.host.DATATP_SESSION;

const MAIL_TO = [
  { fullName: 'Nguyễ<PERSON>', email: '<EMAIL>' },
  { fullName: 'Chartering', email: '<EMAIL>' }
];

export class UIBulkCargoInquiry extends entity.AppDbComplexEntityEditor {
  containerRef: RefObject<HTMLDivElement>;
  attachmentsSize = 0;

  constructor(props: entity.AppComplexEntityEditorProps) {
    super(props);
    this.containerRef = createRef<HTMLDivElement>();
    const { observer } = this.props;
    let request = observer.getMutableBean();
    let shipmentDetail = request['shipmentDetail'] || {};
    if (!request.isNewBean || !shipmentDetail.cargoType) {
      shipmentDetail.cargoType = '';
    }

    let mailToBeans: Array<any> = request['mailToBeans'] || [];
    mailToBeans.push(...MAIL_TO)
    observer.replaceBeanProperty('mailToBeans', mailToBeans);
  }

  pushAttachmentFile(attachmentFile: any) {
    let { observer } = this.props;
    let attachments: Array<any> = observer.getComplexArrayProperty('attachments', []);
    let size = attachmentFile['size'];
    if (this.attachmentsSize + size > 35 * 1000000) {
      bs.dialogShow("Notifications", <div>{`File size has exceeded 35M (Total Size ${this.attachmentsSize + size})`}</div>);
      return false;
    }
    this.attachmentsSize += size;
    attachments.push(attachmentFile);
    return true;
  }

  popupUIUploadPreview(props: app.AppComponentProps, url: string) {
    let { pageContext } = props;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (<module.communication.message.UIUploadPreview appContext={appCtx} pageContext={pageCtx} url={url} />);
    }
    pageContext.createPopupPage("preview", T("Preview"), createAppPage, { size: "lg" });
  }

  onRenderAttachFiles = () => {
    let { appContext, observer } = this.props;
    let requestModel: any = observer.getMutableBean();

    let attachFiles: Array<any> = [];
    let attachments: Array<any> = requestModel['attachments'] || []

    for (let i = 0; i < attachments.length; i++) {
      let att = attachments[i];
      attachFiles.push(
        <div key={`${i}-att-file`} className='flex-grow-0 flex-hbox m-1 border'>
          <div className='flex-vbox align-items-center' style={{ width: 120 }}>
            {module.communication.message.FACTORY.create(appContext, att['name'], att['resourceUri'])}
            <bs.Button className='p-0' laf='link' onClick={() => {
              this.popupUIUploadPreview(this.props, att['resourceUri']);
            }}>
              <div title={att['name']}>
                {util.text.formater.truncate(att['name'], 18, true)}
              </div>
            </bs.Button>
          </div>
          <bs.Button laf='link' className='p-0 flex-hbox justify-content-start' style={{ marginLeft: -15 }} onClick={() => {
            attachments.splice(i, 1);
            this.forceUpdate();
          }} >
            <FeatherIcon.X size={15} />
          </bs.Button>
        </div>
      )
    }
    return attachFiles;
  }

  onUpload = (uploadResources: Array<any>) => {
    const { appContext, observer } = this.props;
    for (let sel of uploadResources) {
      sel['resourceUri'] = appContext.getServerContext().createRestURL("/upload/resource/" + sel['storeId']);
      sel['restURL'] = appContext.getServerContext().createRestURL("");
      if (!this.pushAttachmentFile(sel)) {
        break;
      };
    }
    this.onModify(observer.getMutableBean(), 'attachments', observer.getComplexArrayProperty('attachments', []), uploadResources)
  }

  onInputChange(event: any) {
    const { appContext } = this.props;
    let rest = appContext.getServerContext().getRestClient();
    let data = new FormData()
    for (let i = 0; i < event.target.files.length; i++) {
      let file = event.target.files[i];
      data.append('files', file);
    }
    let successCB = (result: any) => {
      let uploadResources: Array<component.UploadResource> = result.data;
      this.onUpload(uploadResources)
    }
    rest.formSubmit('upload/multi-file', data, successCB);
  }

  getMailMessage = () => {
    return this.containerRef.current?.innerHTML || '';
  }

  onModify = (bean: any, _field: string, _oldVal: any, newVal: any) => {
    if ((_field === 'mailCcBeans' || _field === 'mailToBeans') && newVal && Object.keys(newVal).length > 0) {
      const emailList: any[] = bean[_field] || [];
      const displayName = newVal['fullName'] || newVal['zaloDisplayName'] || newVal['loginId'];
      const emailAddress = newVal['email'];
      const label = `${displayName} ( ${emailAddress} )`;

      if (displayName) {
        // Filter out any items with an id property that has a value
        const filteredList = emailList.filter(item => !item.id);
        filteredList.push({ label: label, email: emailAddress });
        bean[_field] = filteredList;
      }
    }
    this.nextViewId();
    this.forceUpdate();
  }

  render(): React.ReactNode {
    const { appContext, pageContext, observer } = this.props;
    let cargoInquiry = observer.getMutableBean();
    cargoInquiry['salemanBranchName'] = SESSION.getAccountAcl().getCompanyAcl().companyLabel;

    let shipmentDetail: any = observer.getBeanProperty('shipmentDetail', {});
    let locationTypes: any[] = ['Port'];
    const termOfServiceOptions: string[] = [
      '', 'FIO', 'FLT', 'LIFO', 'FILO', 'CROSS BORDER', 'EXW', 'RORO'
    ];
    const termOfServiceLabels: string[] = [
      'Select...',
      'FIO (Ocean freight only)',
      'FLT (Ocean freight and loading, discharging cost)',
      'FILO (Ocean freight and discharging cost)',
      'LIFO (Ocean freight and loading cost)',
      'CROSS BORDER',
      'EXW',
      'RORO'
    ];

    let cargoType = shipmentDetail.cargoType;


    let mailCc: any[] = cargoInquiry['mailCcBeans'] || [];
    let mailTo: any[] = cargoInquiry['mailToBeans'] || [];
    return (
      <div className='flex-vbox rounded-bottom mx-md-2 px-md-2'>
        <div className='flex-grow-1'>
          <bs.Row>
            <bs.Col span={12} md={6}>
              <BBRefLocation appContext={appContext} pageContext={pageContext} bean={cargoInquiry}
                beanIdField={'fromLocationCode'} beanLabelField={'fromLocationLabel'} locationTypes={locationTypes}
                hideMoreInfo
                label='Port Of Loading' placeholder='' refLocationBy='code' style={{ minWidth: 350 }}
                onPostUpdate={(_inputUi, bean, selectOpt, _userInput) => {
                  bean['fromLocationCode'] = selectOpt['code'];
                  bean['fromLocationLabel'] = selectOpt['label'];
                }} />
            </bs.Col>

            <bs.Col span={12} md={6}>
              <BBRefLocation appContext={appContext} pageContext={pageContext} bean={cargoInquiry}
                beanIdField={'toLocationCode'} beanLabelField={'toLocationLabel'} locationTypes={locationTypes}
                hideMoreInfo
                label='Port Of Discharge' placeholder='' refLocationBy='code' style={{ minWidth: 350 }}
                onPostUpdate={(_inputUi, bean, selectOpt, _userInput) => {
                  bean['toLocationCode'] = selectOpt['code'];
                  bean['toLocationLabel'] = selectOpt['label'];
                }} />
            </bs.Col>
          </bs.Row>

          <bs.Row>
            <bs.Col span={6}>
              <BBRefUserCustomer placeholder='Customer/ Lead...' label='Customer/ Lead' allowUserInput hideMoreInfo
                appContext={appContext} pageContext={pageContext} minWidth={400} offset={[-200, 0]}
                partnerTypes={['CUSTOMERS']} style={{ width: '100%' }} placement='auto-start'
                bean={cargoInquiry} beanIdField={'clientPartnerId'} beanLabelField={'clientLabel'} />
            </bs.Col>
            <bs.Col md={6}>
              <div className='bb-field'>
                <bs.FormLabel>Laycan</bs.FormLabel>
                <div className='flex-hbox'>
                  <div className='w-50 me-2'>
                    <input.BBDateTimeField
                      label={T('')} bean={shipmentDetail}
                      field={'laydaysDate'} dateFormat={'DD/MM/YYYY'} timeFormat={false} />
                  </div>
                  <div className='w-50'>
                    <input.BBDateTimeField
                      label={T('')} bean={shipmentDetail}
                      field={'cancellingDate'} dateFormat={'DD/MM/YYYY'} timeFormat={false} />
                  </div>
                </div>
              </div>
            </bs.Col>
          </bs.Row>
          <bs.Row>
            <bs.Col span={6} md={3}>
              <input.BBSelectField bean={shipmentDetail} label={T('Cargo Type')} field={'cargoType'}
                options={['', 'BULK', 'PACKAGE', 'EQUIPMENTS', 'VEHICLE', 'STEEL_PRODUCT', 'LIVE_ANIMAL', 'SHIPPING_AGENCY']}
                optionLabels={['Select...', 'BULK (Clinker, sand, maize...)', 'PACKAGE (Rice in bag, wood in bundle...)', 'EQUIPMENTS', 'VEHICLE', 'STEEL PRODUCT', 'LIVE ANIMAL', 'SHIPPING AGENCY']}
                onInputChange={(bean: any, field: string, oldVal: any, newVal: any) => {
                  this.forceUpdate();
                  if (newVal === 'BULK') shipmentDetail.unit = 'TNE';
                }} />
            </bs.Col>
            <bs.Col span={6} md={3}>
              <input.BBSelectField bean={shipmentDetail} label={T('Term of Service')} field={'termOfService'}
                options={termOfServiceOptions} optionLabels={termOfServiceLabels} />
            </bs.Col>
            <bs.Col span={12} md={6}>
              <input.BBSelectField bean={shipmentDetail} label={T('Cargo Proceeding')} field={'cargoProceeding'}
                options={[
                  'Select...',
                  'Ready to load (Complete trading contract and done LC)',
                  'Bidding cargo',
                  'Comparing with container method cost',
                  'Referring for trading contract',
                ]} />
            </bs.Col>
          </bs.Row>

          <bs.Row className='pt-md-1'>
            <bs.Col span={6}>
              <BBRefCommodityType appContext={appContext} pageContext={pageContext} hideMoreInfo
                bean={shipmentDetail} placeholder='Commodity' label='Commodities / Descriptions Of Goods' beanIdField='commodity' />
              <input.BBTextField bean={shipmentDetail} field={"descOfGoods"} style={{ height: '3.9em' }} />
            </bs.Col>

            {cargoType === 'BULK' ?
              <bs.Col span={6}>
                <div className='flex-hbox'>
                  <div className='w-50 me-2'>
                    <input.BBNumberField bean={shipmentDetail} label={T(`Quantity (Metric Ton)`)} field={'quantity'} />
                  </div>
                  <div className='w-50'>
                    <bs.CssTooltip position='top-left' width={360} >
                      <bs.CssTooltipToggle >
                        <input.BBNumberField bean={shipmentDetail} label={T("Stowage Factor (CBM/MT)")} field={'stowageFactor'} />
                      </bs.CssTooltipToggle>
                      <bs.CssTooltipContent className="d-flex flex-column rounded" >
                        <div className="tooltip-body" style={{ color: '#198754' }}>
                          What is the volume (in CBM) per metric ton of cargo?
                        </div>
                      </bs.CssTooltipContent>
                    </bs.CssTooltip>
                  </div>
                </div>
              </bs.Col>
              :
              <bs.Col span={6}>
                <div className='flex-hbox'>
                  <div className='w-50 me-2'>
                    <input.BBNumberField bean={shipmentDetail} label={T(`Quantity (Metric Ton)`)} field={'quantity'} />
                  </div>
                  <div className='w-50'>
                    <input.BBNumberField bean={shipmentDetail} label={T(`Volume (CBM)`)} field={'volume'} />
                  </div>
                </div>
                {cargoType === 'PACKAGE' ? (
                  <div className='flex-hbox'>
                    <div className='w-50 me-2'>
                      <input.BBStringField bean={shipmentDetail} label={T('DIM')} field={'dim'} />
                    </div>
                    <div className='w-50'>
                      <bs.CssTooltip position='top-left' width={360} >
                        <bs.CssTooltipToggle >
                          <input.BBNumberField bean={shipmentDetail} label={T("Stowage Factor (CBM/MT)")} field={'stowageFactor'} />
                        </bs.CssTooltipToggle>
                        <bs.CssTooltipContent className="d-flex flex-column rounded" >
                          <div className="tooltip-body" style={{ color: '#198754' }}>
                            What is the volume (in CBM) per metric ton of cargo?
                          </div>
                        </bs.CssTooltipContent>
                      </bs.CssTooltip>
                    </div>
                  </div>
                ) : (
                  cargoType === 'EQUIPMENTS' || cargoType === 'VEHICLE' || cargoType === 'STEEL_PRODUCT' ? (
                    <div className='flex-hbox'>
                      <div className='w-50 me-2'>
                        <input.BBSelectField bean={shipmentDetail} field={'stackable'} label={T('Stackability')}
                          optionLabels={['Non Stackable', 'Stackable']} options={['NON_STACKABLE', 'STACKABLE']}
                          onInputChange={this.onModify} />
                      </div>
                      <div className='w-50'>
                        <div className='flex-hbox'>
                          <div className='w-50 me-2'>
                            <input.BBSelectField bean={shipmentDetail} field={'stackableType'} label={T('Stowage Option')}
                              optionLabels={['On deck', 'Under deck']}
                              options={['ON_DECK', 'UNDER_DECK']} />
                          </div>
                          <div className='w-50 flex-vbox align-items-center justify-content-end'>
                            <input.BBCheckboxField bean={shipmentDetail} field={'allowCombine'} label={T('Allow Combine')} value={false} />
                          </div>
                        </div>
                      </div>
                    </div>
                  ) : null
                )}
              </bs.Col>
            }
          </bs.Row>

          <bs.Row>
            <bs.Col span={6}>
              <input.BBStringField className='flex-hbox' label={T('Load Rate (MT/Day)')} bean={shipmentDetail} field={'loadRate'}
                placeholder='How many tons can the shipper load per day?' onInputChange={this.onModify} />
            </bs.Col>
            <bs.Col span={6}>
              <input.BBStringField className='flex-vbox' label={T('Discharge Rate (MT/Day)')} bean={shipmentDetail} field={'dischargeRate'}
                placeholder='How many tons can the consignee discharge per day?' onInputChange={this.onModify} />
            </bs.Col>
          </bs.Row>
          <bs.Row className='my-md-2'>
            <bs.Col span={12} md={6}>
              <input.BBTextField className='flex-vbox' label='Other request' bean={cargoInquiry} field={'note'} style={{ height: '3rem' }}
                placeholder='Other information, if any' onInputChange={this.onModify} />
            </bs.Col>
            <bs.Col span={12} md={6}>
              <input.BBTextField style={{ height: '3rem' }}
                bean={shipmentDetail} label={T('Target rate (if any)')} field={'targetRate'} placeholder='Rate idea (If any)'
                onInputChange={this.onModify} />
            </bs.Col>
          </bs.Row>
        </div>
        <div className='flex-vbox flex-grow-0'>
          <hr />
          <div className='d-flex flex-column flex-md-row' key={util.IDTracker.next()}
            style={bs.ScreenUtil.isMobileScreen() ? {} : { minHeight: 300 }}>
            <div className='flex-vbox' style={{ maxWidth: 500, minWidth: 350 }}>
              <div className='d-flex flex-wrap py-2 px-1 my-1 bg-light border-bottom'>
                <span className='fw-bold me-1 text-primary' style={{ fontSize: '0.875rem' }}>Saleman: </span>
                <span className='text-secondary' style={{ fontSize: '0.875rem' }}>
                  {`${cargoInquiry.salemanLabel} (${cargoInquiry.salemanEmail})`}
                </span>
              </div>
              <div className='flex-vbox h-100' style={{ overflowY: 'auto', overflowX: 'hidden' }}>
                <div className='flex-vbox'>
                  <label className="form-label">{'Mail To:'}</label>
                  <module.communication.message.BBRefMultiEmail beanIdField='email' beanLabelField='label'
                    appContext={appContext} pageContext={pageContext} placeholder="Enter to email..."
                    placement="bottom-start" offset={[0, 5]} bean={mailTo} minWidth={400}
                    onPostUpdate={(_inputUI, bean, _selectOpt, _userInput) => this.onModify(cargoInquiry, 'mailToBeans', null, _selectOpt)}
                  />
                </div>

                <input.BBStringArrayField bean={cargoInquiry} label='Mail To: (External)'
                  field={'externalEmailTo'}
                  onInputChange={this.onModify}
                />

                <div className='flex-vbox border-top'>
                  <label className="form-label">{'Mail Cc:'}</label>
                  <module.communication.message.BBRefMultiEmail beanIdField='email' beanLabelField='label' className='w-100'
                    appContext={appContext} pageContext={pageContext} placeholder="Enter cc email..."
                    placement="bottom-start" offset={[0, 5]} bean={mailCc}
                    onPostUpdate={(_inputUI, bean, _selectOpt, _userInput) => this.onModify(cargoInquiry, 'mailCcBeans', null, _selectOpt)}
                  />
                </div>
              </div>
            </div>

            <div className='flex-vbox border border-start border-dashed mx-md-2'>
              <div className='flex-vbox flex-grow-0 justify-content-center align-items-start border-bottom border-dashed'>
                <p className='py-1 mx-2'>Attachments (PL, MSDS, xlsx, pdf, jpg, png, ...)</p>
              </div>

              <div className='flex-vbox'>
                <div className='flex-vbox flex-grow-0 border rounded-md border-dashed border-info p-2 mx-1'>
                  <input style={{ height: 80 }} type='file' name={`uploadFiles[]`} multiple={true}
                    onChange={event => this.onInputChange(event)}
                  />
                </div>
                <bs.GreedyScrollable className='flex-vbox my-1'>
                  {this.onRenderAttachFiles()}
                </bs.GreedyScrollable>
              </div>
            </div>
          </div>
          <div className='flex-grow-1'>
            <bs.Button outline laf='link' className='p-0' onClick={() => {
              let createContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
                return <RequestPricingMailTemplate {...this.props} />;
              };
              pageContext.createPopupPage('preview', 'Preview', createContent, { size: 'flex-lg' })
            }}>
              <FeatherIcon.Info size={12} /> Preview Mail
            </bs.Button>
          </div>
          <div ref={this.containerRef} style={{ width: '100%', height: '100%', display: 'none' }} key={this.viewId}>
            {/* Renders the email template with all props passed through */}
            <RequestPricingMailTemplate {...this.props} />
          </div>

        </div>
      </div>
    )
  }
}

export class RequestPricingMailTemplate extends entity.AppDbComplexEntityEditor {

  renderRowField = (name: string, value: any) => {
    if (!value) value = '';
    const nameLines = name.split('\n').length;

    let valueLines = 1;
    if (typeof value === 'string') {
      valueLines = value.split('\n').length;
    }
    const maxLines = Math.max(nameLines, valueLines);
    const height = Math.max(20, maxLines * 20); // Minimum 20px, add 20px per line

    const rowStyle = {
      ...TABLE_STYLE.row,
      height: `${height}px`,
    };

    const cellStyle = {
      ...TABLE_STYLE.cell,
    };

    const headerCellStyle = {
      ...TABLE_STYLE.cell,
      ...TABLE_STYLE.headerCell,
    };

    return (
      <tr style={rowStyle}>
        <td colSpan={6} style={headerCellStyle}>
          <p style={TABLE_STYLE.paragraph}>
            {name.split('\n').map((line, i) => (
              <React.Fragment key={i}>
                {i > 0 && <br />}
                <span style={TABLE_STYLE.lineText}>{line}</span>
              </React.Fragment>
            ))}
          </p>
        </td>
        <td colSpan={6} style={TABLE_STYLE.cell}>
          <p style={TABLE_STYLE.paragraph}>
            {typeof value !== 'string'
              ? value
              : value.split('\n').map((line: string, i: number) => (
                <React.Fragment key={i}>
                  {i > 0 && <br />}
                  <span style={{
                    ...TABLE_STYLE.lineText,
                    color: line === '[Yes]' ? '#000000' : 'inherit',
                    fontWeight: line === '[Yes]' ? 600 : 'normal'
                  }}>{line}</span>
                </React.Fragment>
              ))}
          </p>
        </td>
      </tr>
    )
  }

  renderRowData = (val: string) => {
    if (!val) val = '';
    const valLines = val.split('\n').length;
    const height = Math.max(20, valLines * 20);

    const rowStyle = {
      ...TABLE_STYLE.row,
      height: `${height}px`,
    };

    return (
      <tr style={rowStyle}>
        <td colSpan={12} style={{ ...TABLE_STYLE.cell }} >
          <p style={TABLE_STYLE.paragraph}>
            {val.split('\n').map((line, i) => (
              <React.Fragment key={i}>
                {i > 0 && <br />}
                <span>{line}</span>
              </React.Fragment>
            ))}
          </p>
        </td>
      </tr>
    )
  }

  renderRowHeading = (name: string) => {
    const headerCellStyle = {
      ...TABLE_STYLE.cell,
      backgroundColor: 'rgb(245, 129, 35)',
      border: '1px solid black',
      verticalAlign: 'center',
      textAlign: 'start'
    };

    const rowStyle = {
      ...TABLE_STYLE.row,
      height: `20px`,
    };

    return (
      <tr style={rowStyle}>
        <th colSpan={12} style={headerCellStyle}>
          <p style={TABLE_STYLE.paragraph} dir="ltr">
            <span>{name}</span>
          </p>
        </th>
      </tr>
    )
  }


  render(): React.ReactNode {
    const { observer } = this.props;
    let requestPricing = observer.getMutableBean();
    let shipmentDetail: any = requestPricing['shipmentDetail'];
    let laydaysDate: string = shipmentDetail['laydaysDate']
      ? util.TimeUtil.toCompactDateFormat(util.TimeUtil.parseCompactDateTimeFormat(shipmentDetail['laydaysDate']))
      : util.TimeUtil.toCompactDateFormat(new Date());
    let cancellingDate: string = shipmentDetail['cancellingDate']
      ? util.TimeUtil.toCompactDateFormat(util.TimeUtil.parseCompactDateTimeFormat(shipmentDetail['cancellingDate']))
      : util.TimeUtil.toCompactDateFormat(new Date());

    let commodity: string = shipmentDetail['commodity'] || '';
    if (commodity) commodity += '\n ';
    commodity += shipmentDetail['descOfGoods'] || '';
    let customerLabel = requestPricing['clientLabel'];

    let unit = shipmentDetail['unit'] ? ` ${shipmentDetail['unit']}` : ``;

    let quantityCagoType = shipmentDetail['cargoType'] === 'BULK' ? shipmentDetail['quantity'] + ' TON' : shipmentDetail['quantity'] + unit;

    let quantity = shipmentDetail['quantity'] !== 0 ? quantityCagoType : shipmentDetail['volume'];

    let stackableLabel = '';
    if (shipmentDetail['cargoType'] === 'EQUIPMENTS') {
      let stackable = shipmentDetail['stackable'];
      let allowCombine = shipmentDetail['allowCombine'] ? ', Allow Combine' : '';
      if (stackable == Stackable.STACKABLE) {
        stackableLabel = `${StackableUtils.getLabel(stackable)}, ${StackableTypeUtils.getLabel(shipmentDetail['stackableType'])} ${allowCombine}`;
      } else {
        stackableLabel = `${StackableUtils.getLabel(stackable)}`
      }
    }
    console.log('Cargo Type: ', shipmentDetail['cargoType']);
    console.log('DIM', shipmentDetail['dim']);

    return (
      <div className='flex-vbox container-fluid'>

        <p style={{ ...TEXT_STYLE.paragraph, ...TEXT_STYLE.greeting }}>
          <span style={{ fontWeight: 500 }}>Dear Pricing Team,</span>
        </p>

        <p style={{ ...TEXT_STYLE.paragraph, ...TEXT_STYLE.introduction }}>
          Please kindly help to check the below inquiry:
        </p>

        <table style={TABLE_STYLE.table}>
          <tbody>
            <>
              {this.renderRowHeading('General Information')}
              {this.renderRowField('Cargo Type', shipmentDetail['cargoType'])}
              {customerLabel && this.renderRowField('Customer/ Lead', requestPricing['clientLabel'])}
              {this.renderRowField('Commodity', commodity)}
              {this.renderRowField('Quantity', quantity)}
              {this.renderRowField('Cargo Proceeding', `${shipmentDetail['cargoProceeding']}`)}
              {this.renderRowField('Loading port', requestPricing['fromLocationLabel'])}
              {this.renderRowField('Destination port', requestPricing['toLocationLabel'])}
              {(shipmentDetail['cargoType'] === 'BULK' || shipmentDetail['cargoType'] === 'PACKAGE') &&
                this.renderRowField('Stowage Factor', shipmentDetail['stowageFactor'])}
              {this.renderRowField('Load Rate', shipmentDetail['loadRate'])}
              {this.renderRowField('Discharge Rate', shipmentDetail['dischargeRate'])}
              {this.renderRowField('Freight idea and term', shipmentDetail['termOfService'])}
              {this.renderRowField('Laycan', laydaysDate + ' - ' + cancellingDate)}
              {shipmentDetail['cargoType'] === 'PACKAGE' && this.renderRowField('DIM', shipmentDetail['dim'])}
              {stackableLabel && this.renderRowField('Stackable', stackableLabel)}
              {requestPricing['note'] && (
                <>
                  {this.renderRowHeading('Note')}
                  {this.renderRowData(requestPricing['note'])}
                </>
              )}
            </>
          </tbody>
        </table>

        <div style={{
          marginTop: '10px',
          padding: '20px',
          borderLeft: '3px solid rgb(245,129,35)',
          backgroundColor: 'rgb(248,249,250)'
        }}>
          <p style={{
            fontSize: '16px',
            color: 'rgb(44,62,80)',
            marginBottom: '12px',
            fontWeight: 600
          }}>{requestPricing['salemanLabel'] || SESSION.getAccountAcl().getFullName()}</p>
          <div style={{
            fontSize: '14px',
            color: 'rgb(52,73,94)',
            lineHeight: 1.6
          }}>
            <p style={{ marginBottom: '8px' }}>
              <span style={{ fontWeight: 500 }}>Sales Executive</span>
              <br />
              <span style={{
                color: 'rgb(245,129,35)',
                fontWeight: 500
              }}>Bee Logistics Corporation</span>
            </p>
            <p style={{ marginBottom: '15px' }}>
              <span style={{
                display: 'inline-block',
                width: '14px',
                height: '14px',
                marginRight: '8px',
                backgroundColor: 'rgb(245,129,35)',
                WebkitMask: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'%3E%3Cpath d='M497.39 361.8l-112-48a24 24 0 0 0-28 6.9l-49.6 60.6A370.66 370.66 0 0 1 130.6 204.11l60.6-49.6a23.94 23.94 0 0 0 6.9-28l-48-112A24.16 24.16 0 0 0 122.6.61l-104 24A24 24 0 0 0 0 48c0 256.5 207.9 464 464 464a24 24 0 0 0 23.4-18.6l24-104a24.29 24.29 0 0 0-14.01-27.6z'/%3E%3C/svg%3E") center/contain no-repeat`,
                mask: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'%3E%3Cpath d='M497.39 361.8l-112-48a24 24 0 0 0-28 6.9l-49.6 60.6A370.66 370.66 0 0 1 130.6 204.11l60.6-49.6a23.94 23.94 0 0 0 6.9-28l-48-112A24.16 24.16 0 0 0 122.6.61l-104 24A24 24 0 0 0 0 48c0 256.5 207.9 464 464 464a24 24 0 0 0 23.4-18.6l24-104a24.29 24.29 0 0 0-14.01-27.6z'/%3E%3C/svg%3E") center/contain no-repeat`
              }} />
              <span>Mobile: {requestPricing['salemanPhone'] || 'N/A'}</span>
              <br />
              <span style={{
                display: 'inline-block',
                width: '14px',
                height: '14px',
                marginRight: '8px',
                backgroundColor: 'rgb(245,129,35)',
                WebkitMask: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'%3E%3Cpath d='M502.3 190.8c3.9-3.1 9.7-.2 9.7 4.7V400c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V195.6c0-5 5.7-7.8 9.7-4.7 22.4 17.4 52.1 39.5 154.1 113.6 21.1 15.4 56.7 47.8 92.2 47.6 35.7.3 72-32.8 92.3-47.6 102-74.1 131.6-96.3 154-113.7zM256 320c23.2.4 56.6-29.2 73.4-41.4 132.7-96.3 142.8-104.7 173.4-128.7 5.8-4.5 9.2-11.5 9.2-18.9v-19c0-26.5-21.5-48-48-48H48C21.5 64 0 85.5 0 112v19c0 7.4 3.4 14.3 9.2 18.9 30.6 23.9 40.7 32.4 173.4 128.7 16.8 12.2 50.2 41.8 73.4 41.4z'/%3E%3C/svg%3E") center/contain no-repeat`,
                mask: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'%3E%3Cpath d='M502.3 190.8c3.9-3.1 9.7-.2 9.7 4.7V400c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V195.6c0-5 5.7-7.8 9.7-4.7 22.4 17.4 52.1 39.5 154.1 113.6 21.1 15.4 56.7 47.8 92.2 47.6 35.7.3 72-32.8 92.3-47.6 102-74.1 131.6-96.3 154-113.7zM256 320c23.2.4 56.6-29.2 73.4-41.4 132.7-96.3 142.8-104.7 173.4-128.7 5.8-4.5 9.2-11.5 9.2-18.9v-19c0-26.5-21.5-48-48-48H48C21.5 64 0 85.5 0 112v19c0 7.4 3.4 14.3 9.2 18.9 30.6 23.9 40.7 32.4 173.4 128.7 16.8 12.2 50.2 41.8 73.4 41.4z'/%3E%3C/svg%3E") center/contain no-repeat`
              }} />
              <span>Email: {requestPricing['salemanEmail']}</span>
            </p>
          </div>
          <p className="mt-4 mb-0" style={{
            color: '#e67e22',
            fontWeight: 600,
            fontStyle: 'italic',
            fontSize: '14px',
            letterSpacing: '0.5px'
          }}>
            Thanks & Best Regards!
          </p>
        </div>
      </div>
    )
  }

}

export class UIMailBulkCargoRequestPricing extends entity.AppDbComplexEntityEditor {
  requestObserver: entity.ComplexBeanObserver;
  requestPricingRef: RefObject<UIBulkCargoInquiry>;

  state = {
    isSending: false
  };

  constructor(props: entity.AppComplexEntityEditorProps) {
    super(props);
    this.requestPricingRef = createRef<UIBulkCargoInquiry>();
    const { observer } = this.props;
    let requestModel: any = observer.getMutableBean();
    this.requestObserver = new entity.ComplexBeanObserver(requestModel);
  }

  onRequestPricing = () => {
    const { appContext } = this.props;
    let requestModel = this.requestObserver.commitAndGet();
    let shipmentDetail = requestModel['shipmentDetail'] || {};

    const validateRequiredFields = () => {

      const isFromLocationMissing = !requestModel['fromLocationCode'] || !requestModel['fromLocationLabel'];
      const isToLocationMissing = !requestModel['toLocationCode'] || !requestModel['toLocationLabel'];
      const missingPort = isFromLocationMissing || isToLocationMissing;
      if (missingPort) {
        let message = (<div className="ms-1 text-danger py-3 border-bottom text-center">Please provide both Port Of Loading and Port Of Discharge.</div>);
        bs.dialogShow('Message', message, { backdrop: 'static', size: 'md' });
        return false;
      }

      const missingClient = !requestModel['clientLabel'];
      if (missingClient) {
        let message = (<div className="ms-1 text-danger py-3 border-bottom text-center">Please enter Client.</div>);
        bs.dialogShow('Message', message, { backdrop: 'static', size: 'md' });
        return false;
      }

      const missingLaycan = !shipmentDetail['laydaysDate'] || !shipmentDetail['cancellingDate'];
      if (missingLaycan) {
        let message = (<div className="ms-1 text-danger py-3 border-bottom text-center">Please enter Laycan.</div>);
        bs.dialogShow('Message', message, { backdrop: 'static', size: 'md' });
        return false;
      }

      const missingCargoType = !shipmentDetail['cargoType'];
      if (missingCargoType) {
        let message = (<div className="ms-1 text-danger py-3 border-bottom text-center">Please enter Cargo Type.</div>);
        bs.dialogShow('Message', message, { backdrop: 'static', size: 'md' });
        return false;
      }

      const misstingTermOfService = !shipmentDetail['termOfService'];
      if (misstingTermOfService) {
        let message = (<div className="ms-1 text-danger py-3 border-bottom text-center">Please enter Term Of Service.</div>);
        bs.dialogShow('Message', message, { backdrop: 'static', size: 'md' });
        return false;
      }

      const missingCargoProceeding = shipmentDetail['cargoProceeding'] === 'Select...';
      if (missingCargoProceeding) {
        let message = (<div className="ms-1 text-danger py-3 border-bottom text-center">Please enter Cargo Proceeding.</div>);
        bs.dialogShow('Message', message, { backdrop: 'static', size: 'md' });
        return false;
      }

      const missingDescOfGoods = !shipmentDetail['descOfGoods'];
      if (missingDescOfGoods) {
        let message = (<div className="ms-1 text-danger py-3 border-bottom text-center">Please enter Desc Of Goods.</div>);
        bs.dialogShow('Message', message, { backdrop: 'static', size: 'md' });
        return false;
      }

      const missingQuantity = !shipmentDetail['quantity'];
      if (missingQuantity) {
        let message = (<div className="ms-1 text-danger py-3 border-bottom text-center">Please enter Quantity.</div>);
        bs.dialogShow('Message', message, { backdrop: 'static', size: 'md' });
        return false;
      }

      const missingStowageFactor = !shipmentDetail['stowageFactor'];
      if (shipmentDetail['cargoType'] === 'BULK' && missingStowageFactor) {
        let message = (<div className="ms-1 text-danger py-3 border-bottom text-center">Please enter Stowage Factor.</div>);
        bs.dialogShow('Message', message, { backdrop: 'static', size: 'md' });
        return false;
      }

      const missingVolume = !shipmentDetail['volume'];
      if (shipmentDetail['cargoType'] != 'BULK' && missingVolume) {
        let message = (<div className="ms-1 text-danger py-3 border-bottom text-center">Please enter Volume.</div>);
        bs.dialogShow('Message', message, { backdrop: 'static', size: 'md' });
        return false;
      }

      const missingDim = !shipmentDetail['dim'];
      if (shipmentDetail['cargoType'] === 'PACKAGE' && missingDim) {
        let message = (<div className="ms-1 text-danger py-3 border-bottom text-center">Please enter Dim.</div>);
        bs.dialogShow('Message', message, { backdrop: 'static', size: 'md' });
        return false;
      }

      const missingRates = !shipmentDetail['loadRate'] || !shipmentDetail['dischargeRate'];
      if (missingRates) {
        let message = (<div className="ms-1 text-danger py-3 border-bottom text-center">Please enter Load Rate and Discharge Rate.</div>);
        bs.dialogShow('Message', message, { backdrop: 'static', size: 'md' });
        return false;
      }

      return true;
    };

    const sendRequest = () => {
      this.setState({ isSending: true });

      const mailMessage = this.requestPricingRef.current?.getMailMessage() || '';

      let mailTo: any[] = requestModel['mailToBeans'] || []
      let toEmails: string[] = mailTo.filter(sel => sel['email']).map(sel => sel['email'])

      let mailExtTo: any[] = requestModel['externalEmailTo'] || []

      let mailCc: any[] = requestModel['mailCcBeans'] || []
      let ccEmails: string[] = mailCc.filter(sel => sel['email']).map(sel => sel['email'])

      let attachments: Array<any> = this.requestObserver.getComplexBeanProperty('attachments', []);
      attachments.forEach(sel => sel['resourceUrl'] = encodeURI(sel['resourceUri']));

      const allTo: string[] = [...(toEmails || []), ...(mailExtTo || [])];

      if (allTo.length === 0) {
        let message = (<div className="ms-1 text-danger py-3 border-bottom text-center">Please enter at least one email address to send the request.</div>);
        bs.dialogShow('Message', message, { backdrop: 'static', size: 'md' });
        this.setState({ isSending: false });
        return;
      }

      let mailRequest: any = {
        ...requestModel,
        from: requestModel['from'] || '',
        to: allTo,
        cc: [...(ccEmails || [])],
        attachments: attachments || [],
        mailMessage: mailMessage || '',
      }

      appContext.createHttpBackendCall('TransportPriceMiscService', 'sendBulkCargoInquiryRequest',
        { request: mailRequest })
        .withSuccessData((newRequest: any) => {
          this.requestObserver = new entity.ComplexBeanObserver(newRequest);
          this.setState({ isSending: false });
          this.onPostCommit(this.requestObserver.getMutableBean());
        })
        .withEntityOpNotification('commit', 'Send Request Success!!!!')
        .withFail(() => {
          this.setState({ isSending: false });
        })
        .call();
    };

    const checkNote = () => {
      const missingNote = !requestModel['note'];
      if (missingNote) {
        let messageEle = (<div className="text-warning">You forgot to enter the Note field. Are you sure you want to proceed with this request?</div>);
        bs.dialogConfirmMessage(T("Confirm Send"), messageEle, sendRequest);
      } else {
        sendRequest();
      }
    };

    const checkTargetRate = () => {
      const missingTargetRate = !shipmentDetail['targetRate'];
      if (missingTargetRate) {
        let messageEle = (
          <div className="text-warning">You forgot to enter the Target rate field, which makes price checking take longer.
            Are you sure you want to proceed with this request?</div>);
        bs.dialogConfirmMessage(T("Confirm Send"), messageEle, checkNote);
      } else {
        checkNote();
      }
    };

    if (validateRequiredFields()) {
      const missingTargetRate = !shipmentDetail['targetRate'];
      const missingNote = !requestModel['note'];

      if (!missingTargetRate && !missingNote) {
        sendRequest();
      } else if (missingTargetRate) {
        checkTargetRate();
      } else {
        checkNote();
      }
    }
  }
  render(): React.ReactNode {
    if (this.isLoading()) return this.renderLoading();
    let sended: boolean = !this.requestObserver.isNewBean();

    return (
      <div className="flex-vbox" key={this.viewId}>
        <UIBulkCargoInquiry {...this.props} observer={this.requestObserver} ref={this.requestPricingRef} />
        <div className="flex-hbox flex-grow-0 mt-2 w-100">
          <bs.Button laf={sended ? 'success' : 'info'} className="border-0 py-2 px-2 mx-md-2 w-100 h-100"
            onClick={this.onRequestPricing} disabled={this.state.isSending || sended}>
            <FeatherIcon.Mail className='mx-1' size={12} /> {this.state.isSending ? 'Sending...' : !sended ? 'Send Request' : 'Send Request (Success)'}
          </bs.Button>
        </div>
      </div>
    )
  }
}
