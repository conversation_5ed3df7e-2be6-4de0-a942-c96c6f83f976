import { app } from "@datatp-ui/lib";
import * as erp from "@datatp-ui/erp";

import UserSpaceFeatureRegistry = erp.app.user.UserSpaceFeatureRegistry;
import UserPartnerSpaceFeatureRegistry = erp.app.user.UserPartnerSpaceFeatureRegistry;

import CompanyFeatureRegistry = erp.app.company.CompanyFeatureRegistry;
import AdminSpaceFeatureRegistry = erp.app.admin.AdminSpaceFeatureRegistry;
import PartnerSpaceFeatureRegistry = erp.app.partner.PartnerSpaceFeatureRegistry;

import AppFeatureGroup = app.host.AppFeatureGroup;

erp.module.account.init();
erp.module.accounting.init()
erp.module.communication.init();
erp.module.system.init();
erp.module.company.hr.init();
erp.module.company.partner.init();
erp.module.hr.init();
erp.module.asset.init();
erp.module.dtable.init();
erp.module.project.init();
erp.module.project.workflow.init.init();
erp.module.okr.init();
erp.module.sample.init();
erp.module.odoo.init();

const AppFeatureRegistryManager = app.host.AppFeatureManager;

let userEmployeeGroup = new AppFeatureGroup('user', 'User');
AppFeatureRegistryManager.addGroupApps(userEmployeeGroup, [new UserSpaceFeatureRegistry()]);

let userPartnerGroup = new AppFeatureGroup('user-partner', 'User (Partner)');
AppFeatureRegistryManager.addGroupApps(userPartnerGroup, [new UserPartnerSpaceFeatureRegistry()]);

let companyGroup = new AppFeatureGroup('company', 'Company');
AppFeatureRegistryManager.addGroupApps(companyGroup, [new CompanyFeatureRegistry()]);

let adminGroup = new AppFeatureGroup('admin', 'Admin');
AppFeatureRegistryManager.addGroupApps(adminGroup, [new AdminSpaceFeatureRegistry()]);

let partnerGroup = new AppFeatureGroup('partner', 'Partner');
AppFeatureRegistryManager.addGroupApps(partnerGroup, [new PartnerSpaceFeatureRegistry()]);

