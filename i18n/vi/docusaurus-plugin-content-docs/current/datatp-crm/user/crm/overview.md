---
sidebar_position: 1
hide_table_of_contents: true
displayed_sidebar: userSidebar
---

# CRM

**Vào module nghiệp vụ**

<details>
  <summary>Góc trái màn hình, click chọn ở logo tên công ty -> chọn `CRM`.</summary>

  ![../img/sales/sale_access.gif](./img/crm_access.gif)

</details>

<hr />

### Quy trình tổng quan Báo giá.

<details>
<summary>Xem chi tiết quy trình</summary>

```text
┌─────────────────┐     ┌───────────────────────┐
│  Khách hàng     │────▶│  Sales tiếp nhận      │
│  gửi yêu cầu    │     │  thông tin            │
└─────────────────┘     └──────────┬────────────┘
                                   │
                                   ▼
┌───────────────────────────────────────────────────────────────────────────┐
│                          Kiểm tra giá trên hệ thống                       │
└───────────┬───────────────────────────────────────┬───────────────────────┘
            │                                       │
            ▼                                       ▼
┌───────────────────────┐                  ┌───────────────────────────┐
│  Giá đã có sẵn        │                  │  Giá chưa có sẵn          │
└──────────┬────────────┘                  └───────────┬───────────────┘
           │                                           │
           ▼                                           ▼
┌────────────────────────────┐             ┌───────────────────────────┐
│  Request a Quote           │             │  Request Pricing          │
│  trên thanh công cụ        │             │  trên thanh công cụ       │
└──────────┬─────────────────┘             └───────────┬───────────────┘
           │                                           │
           ▼                                           ▼
┌────────────────────────────┐             ┌───────────────────────────┐
│  Tạo Inquiry Request       │             │  Tạo Inquiry Request      │
│  (không gửi mail)          │             │  (tự động gửi email)      │
└──────────┬─────────────────┘             └───────────┬───────────────┘
           │                                           │
           │                                           ▼
           │                              ┌─────────────────────────────┐
           │      ┌─────────────────────┐ │  Bộ phận Pricing            │
           └─────▶│  Request Pricing    │ │  cập nhật thông tin         │
           │      │(gửi mail-tùy chọn)  │ └───────────┬─────────────────┘
           │      └─────────┬───────────┘             │
           │                │                         │
           │                ▼                         │
           │     ┌────────────────────────┐           │
           │     │ Bộ phận Pricing cập    │           │
           │     │ nhật thông tin (nếu có)│           │
           │     └─────────┬──────────────┘           │
           │               │                          │
           ▼               ▼                          ▼
┌───────────────────────────────────────────────────────────────────────────┐
│                  Tạo Quotation và gửi báo giá cho khách hàng              │
└───────────────────────────────────────┬───────────────────────────────────┘
                                        │
                                        ▼
┌───────────────────────────────────────────────────────────────────────────┐
│                           Tạo Internal Booking                            │
└───────────────────────────────────────┬───────────────────────────────────┘
                                        │
                                        ▼
┌───────────────────────────────────────────────────────────────────────────┐
│              Cập nhật Inquiry Request thành trạng thái "won"              │
└───────────────────────────────────────────────────────────────────────────┘
```
</details>


#### 1. Tiếp nhận yêu cầu từ khách hàng.
- Sales tiếp nhận thông tin từ khách hàng.

#### 2. Kiểm tra và xử lý giá trên CRM/Hệ thống.
  Sales kiểm tra giá trên ứng dụng CRM.  [Xem hướng dẫn tìm kiếm giá tại đây](/docs/datatp-crm/user/crm/references/search_prices)

- <mark><u>Trường hợp 1: Giá đã có sẵn.</u></mark>
    - Sales chọn giá ở bảng và nhấn nút `Request a Quote` trên thanh công cụ.
        ![./img/select_prices.gif](./img/select_prices.gif)
    - Hệ thống tự động tạo Inquiry Request tương ứng (không gửi mail) và chuyển qua màn hình `Quotation Form` - nhập thông tin báo giá.

    - Sales có thể gửi lại request cho Pricing bằng cách nhấn nút chức năng `Request Pricing` để xác nhận lại với pricing (tùy chọn).

        ![./img/request_confirm.gif](./img/request_confirm.gif)
        [Xem hướng dẫn gửi request xác nhận lại giá tại đây](/docs/datatp-crm/user/crm/references/mail_request)

    - [Chi tiết các bước tạo, chỉnh sửa, gửi báo giá tại đây](/docs/datatp-crm/user/crm/references/quotation).

- <mark><u>Trường hợp 2: Giá chưa có sẵn.</u></mark>
    - Sales sử dụng chức năng Request Pricing trên thanh công cụ, hệ thống tạo, gửi email đến bộ phận Pricing.

      ![./img/request_pricing.gif](./img/request_pricing.gif)

    <div style={{
      background: '#fff3cd',
      color: '#856404',
      padding: '12px 16px',
      margin: '16px 16px',
      borderRadius: '6px',
      border: '1px solid #ffeeba',
    }}>
      <strong>Lưu ý:</strong> Đối với từng dịch vụ (Sea/ Air/ Truck) và Import/ Export yêu cầu chọn đúng trên màn hình trước khi click nút Request Pricing. <br />
        [Xem hướng dẫn gửi request check giá tại đây](/docs/datatp-crm/user/crm/references/mail_request)
    </div>

    ```ascii
    +-------------------------+     +-------------------------------+     +--------------------------+
    | Nhận giá cuối từ        | --> | Cập nhật feedback khách hàng, | --> | Tạo báo giá & gửi mail   |
    | Pricing                 |     | cập nhật status (Win/Mismatch)|     | cho khách hàng           |
    +-------------------------+     +-------------------------------+     +--------------------------+
    ```
      ![./img/update_status_quote.gif](./img/update_status_quote.gif)

#### 3. Tạo, gửi báo giá và xác nhận booking.  [Xem hướng dẫn chi tiết tại đây](/docs/datatp-crm/user/crm/references/quotation)
  - Sales chỉnh sửa báo giá (thêm chi phí khác, margin, v.v.).
  - Xuất báo giá ra file Excel (`Export (Excel)`) hoặc gửi mail trực tiếp cho khách hàng (Phần gửi mail đang pending).

  - Sales tạo Internal Booking gửi cho Customer Service với thông tin selling đã nhập trước đó.
  - <mark>Khi gửi Internal Booking thành công:</mark>
    >- Hệ thống tự động cập nhật trạng thái của Inquiry Request thành "won".
    >- Hệ thống tự động gửi thông báo qua zalo cho `Customer Service` về thông tin `Internal Booking`.
    >- Hệ thống tự động cập nhật feedback giá bảng, đánh dấu giá đó đã win lô hàng với các thông tin: tên saleman, volume, thông tin hàng.

<hr />

### Hướng dẫn các chức năng khác trong CRM.

#### 1. Tạo, quản lý thông tin khách hàng tiềm năng (Lead / Agent Potential).

  [Xem hướng dẫn chi tiết tại đây](/docs/datatp-crm/user/crm/references/lead_management)

#### 2. Tạo, quản lý thông tin khách hàng.

  [Xem hướng dẫn chi tiết tại đây](/docs/datatp-crm/user/crm/references/customer_management)

#### 3. Quản lý yêu cầu tạo Customer/Agent/Coloader.
  [Xem hướng dẫn chi tiết tại đây](/docs/datatp-crm/user/crm/references/partner_request.md)

#### 4. Tạo, quản lý công việc, báo cáo cho nhân viên kinh doanh.
  [Xem hướng dẫn chi tiết tại đây](/docs/datatp-crm/user/crm/references/tasks_calendar)

#### 5. Partners.

  - Key Account Performance Report.
  [See detailed instructions here](/docs/datatp-crm/user/crm/references/partner_overview)

  - Partners Catalog.
  [See detailed instructions here](/docs/datatp-crm/user/crm/references/partner_overview)

<hr />

```
Thông tin khách hàng được đồng bộ tự động từ hệ thống BFSOne.
Nếu có bất kỳ sai sót hoặc cần cập nhật, vui lòng liên hệ IT để được hỗ trợ.
```

