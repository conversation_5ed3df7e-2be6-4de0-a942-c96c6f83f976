---
sidebar_position: 1
sidebar: false
hide_table_of_contents: true
displayed_sidebar: userSidebar
---

# Task Request - Software Support Request Tool

> **Purpose:** A systematic tool for sending support requests and reporting software issues.

## 🚀 How to Access

**Step 1:** Go to business module
- Left corner of screen → Click on company logo
- Select **User** → **Project** → **Task Requests**

![Access Method](./img/access.gif)

---

## 👤 Part 1: Request Sender

### Create New Request

**Step 1:** Create request
- Click **"New Request"** button in the top right corner of screen

![Create new request](./img/Picture1.png)

**Step 2:** Fill in request information
- "New Task Request" dialog will appear

![Request form](./img/Picture2.png)

### 📝 Information Filling Guide

#### Required Fields:

| Field | Description | Example |
|-------|-------------|---------|
| **Request Type** | Type of request | File Access (file unlock)<br/>Invoice Correction (invoice adjustment) |
| **Approved** | Request handler | Ms. <PERSON> Accounting |
| **Reference Note** | Reference number | FILE number or HBL number |
| **Label** | Brief title | "REQUEST FILE UNLOCK DUE TO ABC..." |
| **Send Email (Cc)** | Related persons | Department head, other related parties |
| **Description** | Detailed description | "Customer ABC changed address..." |

#### Form Filling Illustration:

![Request type](./img/Picture3.png)
*Select request type*

![Handler selection](./img/Picture4.png)
*Select handler*

![Complete example](./img/Picture5.png)
*Example of fully completed form*

**Step 3:** Save and send
- Click **"Save"** to send the request

### 📊 Manage Sent Requests

- All requests will be displayed in the summary table
- **Note:** To delete a request, please contact IT

![Request list](./img/Picture6.png)

#### 🔐 View Permissions:
Each account can only see:
- Requests they have sent
- Requests sent to them
- Requests they are CC'd on

### 📧 Response Notifications

When there is a response, the system will send email notifications to:
- Request sender
- Those who are CC'd

![Email notification](./img/Picture7.png)

---

## 👥 Part 2: Request Receiver and Handler

### 📨 Receive Notifications

When there is a new request, the handler will receive email notifications from the system.

![Email notification for handler](./img/Picture7.png)

### ⚡ How to Handle Requests

**Response Options:**
- ✅ **Approved** (Accept)
- ❌ **Reject** (Decline)

**Response Locations:**
- Directly via email
- Or on the CRM system

### 📈 Advanced Features

#### 📋 Export Excel
- Compile multiple requests into Excel file
- Support batch processing

#### 🏷️ Priority Level Assessment
After processing, the receiver will assess the request priority level:

| Level | Meaning |
|-------|---------|
| 🟢 **Low** | Low priority |
| 🟡 **Medium** | Medium priority |
| 🔴 **High** | High priority |

---

## 💡 Important Notes

### ✅ Checklist before sending request:
- [ ] Selected correct request type
- [ ] Selected correct handler
- [ ] Filled in complete reference information
- [ ] Brief and clear title
- [ ] CC'd relevant persons
- [ ] Detailed and clear problem description

### 🎯 Tips for quick processing:
- **High priority**: Urgent files, affecting operations
- **Medium priority**: Regular information adjustments
- **Low priority**: Non-urgent requests, can wait

### 📞 Support:
- **System errors**: Contact IT
- **User guide**: Refer to this documentation
- **Emergency**: Call the handler directly

---

## 🔄 Request Workflow

1. **Create** → Fill form with complete information
2. **Submit** → System sends notification to handler
3. **Process** → Handler reviews and responds
4. **Notify** → System sends response to requester
5. **Track** → Monitor status in the system

## 📋 Common Request Types

| Request Type | Description | Processing Time |
|--------------|-------------|-----------------|
| **File Access** | Unlock locked files | 1-2 hours |
| **Invoice Correction** | Adjust invoice information | 1-2 business days |
| **Data Update** | Update customer/shipment data | 2-4 hours |
| **System Access** | Grant/revoke system permissions | 1 business day |
| **Report Request** | Generate custom reports | 1-3 business days |

## 🚨 Emergency Procedures

For urgent requests:
1. Mark priority as **High**
2. Call handler directly after submitting
3. CC department head and relevant supervisors
4. Follow up within 2 hours if no response

**Emergency Contact:** IT Helpdesk - ext. 100