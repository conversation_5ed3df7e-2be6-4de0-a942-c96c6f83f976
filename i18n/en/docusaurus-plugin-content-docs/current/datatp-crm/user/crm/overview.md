---
sidebar_position: 1
hide_table_of_contents: true
displayed_sidebar: userSidebar
---

# CRM

**Access the business module**

<details>
  <summary>On the top left corner, click the company logo -> select `CRM`.</summary>

  ![../img/sales/sale_access.gif](./img/crm_access.gif)

</details>

<hr />

### Overview of the Quotation Process.

<details>
<summary>View detailed process</summary>

```text
┌───────────────┐     ┌───────────────────────┐
│  Customer     │────▶│  Sales receives       │
│  sends request│     │  information          │
└───────────────┘     └──────────┬────────────┘
                                 │
                                 ▼
┌───────────────────────────────────────────────────────────────────────────┐
│                        Check price on the system                         │
└───────────┬───────────────────────────────────────┬───────────────────────┘
            │                                       │
            ▼                                       ▼
┌───────────────────────┐                  ┌───────────────────────────┐
│  Price available      │                  │  Price not available      │
└──────────┬────────────┘                  └───────────┬───────────────┘
           │                                           │
           ▼                                           ▼
┌────────────────────────────┐             ┌───────────────────────────┐
│  Request a Quote           │             │  Request Pricing          │
│  on the toolbar            │             │  on the toolbar           │
└──────────┬─────────────────┘             └───────────┬───────────────┘
           │                                           │
           ▼                                           ▼
┌────────────────────────────┐             ┌───────────────────────────┐
│  Create Inquiry Request    │             │  Create Inquiry Request   │
│  (no email sent)           │             │  (email sent automatically)│
└──────────┬─────────────────┘             └───────────┬───────────────┘
           │                                           │
           │                                           ▼
           │                              ┌─────────────────────────────┐
           │      ┌─────────────────────┐ │  Pricing Department         │
           └─────▶│  Request Pricing    │ │  updates information        │
           │      │(optional email)     │ └───────────┬─────────────────┘
           │      └─────────┬───────────┘             │
           │                │                         │
           │                ▼                         │
           │     ┌────────────────────────┐           │
           │     │ Pricing updates info   │           │
           │     │ (if any)               │           │
           │     └─────────┬──────────────┘           │
           │               │                          │
           ▼               ▼                          ▼
┌───────────────────────────────────────────────────────────────────────────┐
│                Create Quotation and send to customer                      │
└───────────────────────────────────────┬───────────────────────────────────┘
                                        │
                                        ▼
┌───────────────────────────────────────────────────────────────────────────┐
│                         Create Internal Booking                           │
└───────────────────────────────────────┬───────────────────────────────────┘
                                        │
                                        ▼
┌───────────────────────────────────────────────────────────────────────────┐
│         Update Inquiry Request status to "won"                            │
└───────────────────────────────────────────────────────────────────────────┘
```
</details>


#### 1. Receive customer request.
- Sales receives information from the customer.

#### 2. Check and process price on CRM/System.
  Sales checks the price on the CRM application.  [See instructions for searching prices here](/docs/datatp-crm/user/crm/references/search_prices)

- <mark><u>Case 1: Price is available.</u></mark>
    - Sales selects the price in the table and clicks the `Request a Quote` button on the toolbar.
        ![./img/select_prices.gif](./img/select_prices.gif)
    - The system automatically creates a corresponding Inquiry Request (no email sent) and switches to the `Quotation Form` screen - enter quotation information.

    - Sales can resend the request to Pricing by clicking the `Request Pricing` function button to confirm with pricing (optional).

        ![./img/request_confirm.gif](./img/request_confirm.gif)
        [See instructions for sending price confirmation request here](/docs/datatp-crm/user/crm/references/mail_request)

    - [See detailed steps to create, edit, and send quotations here](/docs/datatp-crm/user/crm/references/quotation).

- <mark><u>Case 2: Price is not available.</u></mark>
    - Sales uses the Request Pricing function on the toolbar, the system creates and sends an email to the Pricing department.

      ![./img/request_pricing.gif](./img/request_pricing.gif)

    <div style={{
      background: '#fff3cd',
      color: '#856404',
      padding: '12px 16px',
      margin: '16px 16px',
      borderRadius: '6px',
      border: '1px solid #ffeeba',
    }}>
      <strong>Note:</strong> For each service (Sea/ Air/ Truck) and Import/ Export, make sure to select the correct option on the screen before clicking the Request Pricing button. <br />
        [See instructions for sending price check request here](/docs/datatp-crm/user/crm/references/mail_request)
    </div>

    ```ascii
    +-------------------------+     +-------------------------------+     +--------------------------+
    | Receive final price     | --> | Update customer feedback,     | --> | Create quotation & send  |
    | from Pricing            |     | update status (Win/Mismatch)  |     | email to customer        |
    +-------------------------+     +-------------------------------+     +--------------------------+
    ```
      ![./img/update_status_quote.gif](./img/update_status_quote.gif)

#### 3. Create, send quotation and confirm booking.  [See detailed instructions here](/docs/datatp-crm/user/crm/references/quotation)
  - Sales edits the quotation (add other costs, margin, etc.).
  - Export the quotation to Excel file (`Export (Excel)`) or send email directly to the customer (Email sending is pending).

  - Sales creates Internal Booking and sends it to Customer Service with the previously entered selling information.
  - <mark>When Internal Booking is sent successfully:</mark>
    >- The system automatically updates the Inquiry Request status to "won".
    >- The system automatically sends a Zalo notification to `Customer Service` about the `Internal Booking` information.
    >- The system automatically updates the price feedback table, marking that price as won for the shipment with information: salesman name, volume, cargo details.

<hr />

### Instructions for other functions in CRM.

#### 1. Create and manage potential customer information (Lead / Agent Potential).
  [See detailed instructions here](/docs/datatp-crm/user/crm/references/lead_management)

#### 2. Create and manage customer information.
  [See detailed instructions here](/docs/datatp-crm/user/crm/references/customer_management)

#### 3. Manage create request for Customer/Agent/Coloader.
  [See detailed instructions here](/docs/datatp-crm/user/crm/references/partner_request.md)

#### 4. Create and manage tasks, reports for sales staff.
  [See detailed instructions here](/docs/datatp-crm/user/crm/references/tasks_calendar)

#### 5. Partner Reports.

  - Key Account Performance Report.
  [See detailed instructions here](/docs/datatp-crm/user/crm/references/partner_overview)

  - Partners Catalog.
  [See detailed instructions here](/docs/datatp-crm/user/crm/references/partner_overview)


<hr />
