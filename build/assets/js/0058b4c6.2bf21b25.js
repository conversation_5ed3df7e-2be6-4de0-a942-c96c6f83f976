"use strict";(self.webpackChunkbeelogistics_docs=self.webpackChunkbeelogistics_docs||[]).push([[849],{6164:e=>{e.exports=JSON.parse('{"version":{"pluginId":"default","version":"current","label":"Next","banner":null,"badge":false,"noIndex":false,"className":"docs-version-current","isLast":true,"docsSidebars":{"userSidebar":[{"type":"link","label":"System","href":"/docs/shared/user/system"},{"type":"link","label":"Car/Meeting Schedule","href":"/docs/shared/user/asset"},{"type":"category","label":"Pricing Tools/ CRM","items":[{"type":"link","label":"Overview","href":"/docs/datatp-crm/user/intro"},{"type":"link","label":"Pricing Tools","href":"/docs/datatp-crm/user/pricing-tools"},{"type":"link","label":"CRM","href":"/docs/datatp-crm/user/crm/overview"},{"type":"link","label":"Dashboard","href":"/docs/datatp-crm/user/crm/dashboard"}],"collapsed":true,"collapsible":true},{"type":"category","label":"HR","items":[{"type":"link","label":"OKR","href":"/docs/datatp-hr/user/okr"},{"type":"link","label":"KPI","href":"/docs/datatp-hr/user/kpi"}],"collapsed":true,"collapsible":true},{"type":"category","label":"TMS","items":[{"type":"link","label":"Vendor App","href":"/docs/datatp-tms/user/vendor-bill"},{"type":"link","label":"TMS Bill","href":"/docs/datatp-tms/user/tms-bill"},{"type":"link","label":"Fleet","href":"/docs/datatp-tms/user/fleet"}],"collapsed":true,"collapsible":true}],"developerSidebar":[{"type":"link","label":"Setup","href":"/docs/shared/developer/SETUP"},{"type":"link","label":"Changelog","href":"/docs/shared/developer/CHANGELOG"},{"type":"link","label":"Backlog","href":"/docs/shared/developer/BACKLOG"},{"type":"category","label":"CRM","items":[{"type":"link","label":"DataTP CRM Backlog","href":"/docs/datatp-crm/developer/BACKLOG","docId":"datatp-crm/developer/BACKLOG","unlisted":false},{"type":"link","label":"Changelog","href":"/docs/datatp-crm/developer/CHANGELOG","docId":"datatp-crm/developer/CHANGELOG","unlisted":false},{"type":"link","label":"CRM Flowchart","href":"/docs/datatp-crm/developer/crm_flow","docId":"datatp-crm/developer/crm_flow","unlisted":false},{"type":"category","label":"feature","collapsible":true,"collapsed":true,"items":[{"type":"link","label":"CRM Features","href":"/docs/datatp-crm/developer/feature/crm","docId":"datatp-crm/developer/feature/crm","unlisted":false},{"type":"link","label":"HRM, Workflow, Project Features.","href":"/docs/datatp-crm/developer/feature/HRM","docId":"datatp-crm/developer/feature/HRM","unlisted":false},{"type":"link","label":"MAIL-TICKET","href":"/docs/datatp-crm/developer/feature/MAIL-TICKET","docId":"datatp-crm/developer/feature/MAIL-TICKET","unlisted":false}]}],"collapsed":true,"collapsible":true},{"type":"category","label":"Document IE","items":[{"type":"link","label":"Changelog","href":"/docs/document-ie/developer/CHANGELOG","docId":"document-ie/developer/CHANGELOG","unlisted":false},{"type":"link","label":"CRM Features","href":"/docs/document-ie/developer/features","docId":"document-ie/developer/features","unlisted":false}],"collapsed":true,"collapsible":true},{"type":"category","label":"HR","items":[{"type":"link","label":"Changelog","href":"/docs/datatp-hr/developer/CHANGELOG","docId":"datatp-hr/developer/CHANGELOG","unlisted":false},{"type":"link","label":"DataTP HR Backlog","href":"/docs/datatp-hr/developer/BACKLOG","docId":"datatp-hr/developer/BACKLOG","unlisted":false}],"collapsed":true,"collapsible":true}]},"docs":{"datatp-crm/developer/BACKLOG":{"id":"datatp-crm/developer/BACKLOG","title":"DataTP CRM Backlog","description":"Tasks","sidebar":"developerSidebar"},"datatp-crm/developer/CHANGELOG":{"id":"datatp-crm/developer/CHANGELOG","title":"Changelog","description":"All notable changes to this project will be documented in this file.","sidebar":"developerSidebar"},"datatp-crm/developer/crm_flow":{"id":"datatp-crm/developer/crm_flow","title":"CRM Flowchart","description":"Quy tr\xecnh t\u1ed5ng quan.","sidebar":"developerSidebar"},"datatp-crm/developer/feature/crm":{"id":"datatp-crm/developer/feature/crm","title":"CRM Features","description":"1. Th\xf4ng b\xe1o nh\u1eefng gi\xe1 m\u1edbi \u0111\u01b0\u1ee3c update/\u0111ang promote.","sidebar":"developerSidebar"},"datatp-crm/developer/feature/HRM":{"id":"datatp-crm/developer/feature/HRM","title":"HRM, Workflow, Project Features.","description":"1. Task Request.","sidebar":"developerSidebar"},"datatp-crm/developer/feature/MAIL-TICKET":{"id":"datatp-crm/developer/feature/MAIL-TICKET","title":"MAIL-TICKET","description":"Mail ticket: Nghi\xean c\u1ee9u, t\xecm gi\u1ea3i ph\xe1p, case study.","sidebar":"developerSidebar"},"datatp-crm/user/crm/dashboard":{"id":"datatp-crm/user/crm/dashboard","title":"Dashboard","description":"Company Dashboard","sidebar":"userSidebar"},"datatp-crm/user/crm/overview":{"id":"datatp-crm/user/crm/overview","title":"CRM","description":"V\xe0o module nghi\u1ec7p v\u1ee5","sidebar":"userSidebar"},"datatp-crm/user/crm/references/customer_management":{"id":"datatp-crm/user/crm/references/customer_management","title":"Qu\u1ea3n l\xfd Partners","description":"H\u01b0\u1edbng d\u1eabn qu\u1ea3n l\xfd th\xf4ng tin kh\xe1ch h\xe0ng (Customer/ Agent) trong h\u1ec7 th\u1ed1ng CRM.","sidebar":"userSidebar"},"datatp-crm/user/crm/references/features":{"id":"datatp-crm/user/crm/references/features","title":"CRM Features","description":"1. Th\xf4ng b\xe1o nh\u1eefng gi\xe1 m\u1edbi \u0111\u01b0\u1ee3c update/\u0111ang promote.","sidebar":"userSidebar"},"datatp-crm/user/crm/references/lead_management":{"id":"datatp-crm/user/crm/references/lead_management","title":"Qu\u1ea3n l\xfd Lead/ Agent Potential","description":"H\u01b0\u1edbng d\u1eabn qu\u1ea3n l\xfd th\xf4ng tin kh\xe1ch h\xe0ng ti\u1ec1m n\u0103ng (Lead) trong h\u1ec7 th\u1ed1ng CRM.","sidebar":"userSidebar"},"datatp-crm/user/crm/references/mail_request":{"id":"datatp-crm/user/crm/references/mail_request","title":"Inquiry Request","description":"T\xednh n\u0103ng g\u1eedi y\xeau c\u1ea7u check gi\xe1 qua email khi kh\xf4ng t\xecm th\u1ea5y gi\xe1 ph\xf9 h\u1ee3p tr\xean Pricing Tools.","sidebar":"userSidebar"},"datatp-crm/user/crm/references/MAIL-TICKET":{"id":"datatp-crm/user/crm/references/MAIL-TICKET","title":"MAIL-TICKET","description":"Mail ticket: Nghi\xean c\u1ee9u, t\xecm gi\u1ea3i ph\xe1p, case study.","sidebar":"userSidebar"},"datatp-crm/user/crm/references/partner_overview":{"id":"datatp-crm/user/crm/references/partner_overview","title":"Partner Dashboard Overview","description":"H\u01b0\u1edbng d\u1eabn s\u1eed d\u1ee5ng m\xe0n h\xecnh Overview trong h\u1ec7 th\u1ed1ng CRM.","sidebar":"userSidebar"},"datatp-crm/user/crm/references/partner_request":{"id":"datatp-crm/user/crm/references/partner_request","title":"Qu\u1ea3n l\xfd Partner Requests","description":"H\u01b0\u1edbng d\u1eabn qu\u1ea3n l\xfd Partner Request (Customer/Agent/Coloader) trong h\u1ec7 th\u1ed1ng CRM (D\xe0nh cho Sales).","sidebar":"userSidebar"},"datatp-crm/user/crm/references/partners":{"id":"datatp-crm/user/crm/references/partners","title":"Partners","description":"Ch\u1ee9c n\u0103ng, m\xe0n h\xecnh nghi\u1ec7p v\u1ee5 li\xean quan \u0111\u1ebfn vi\u1ec7c theo d\xf5i c\xe1c Partners c\u1ee7a t\u1eebng salesman.","sidebar":"userSidebar"},"datatp-crm/user/crm/references/quotation":{"id":"datatp-crm/user/crm/references/quotation","title":"B\xe1o gi\xe1.","description":"S\u01a1 \u0111\u1ed3 quy tr\xecnh c\xe1c m\xe0n h\xecnh & ch\u1ee9c n\u0103ng b\xe1o gi\xe1.","sidebar":"userSidebar"},"datatp-crm/user/crm/references/search_prices":{"id":"datatp-crm/user/crm/references/search_prices","title":"T\xecm ki\u1ebfm gi\xe1","description":"Click ch\u1ecdn Quick Rate Finder tr\xean thanh c\xf4ng c\u1ee5 \u0111\u1ec3 v\xe0o m\xe0n h\xecnh t\xecm ki\u1ebfm gi\xe1.","sidebar":"userSidebar"},"datatp-crm/user/crm/references/tasks_calendar":{"id":"datatp-crm/user/crm/references/tasks_calendar","title":"Tasks Calendar","description":"M\xe0n h\xecnh qu\u1ea3n l\xfd c\xf4ng vi\u1ec7c theo ng\xe0y cho nh\xe2n vi\xean kinh doanh.","sidebar":"userSidebar"},"datatp-crm/user/intro":{"id":"datatp-crm/user/intro","title":"Overview","description":"M\u1ee5c ti\xeau c\u1ee7a h\u1ec7 th\u1ed1ng","sidebar":"userSidebar"},"datatp-crm/user/pricing-tools":{"id":"datatp-crm/user/pricing-tools","title":"Pricing Tools","description":"Nghi\u1ec7p v\u1ee5 li\xean quan:","sidebar":"userSidebar"},"datatp-hr/developer/BACKLOG":{"id":"datatp-hr/developer/BACKLOG","title":"DataTP HR Backlog","description":"Tasks","sidebar":"developerSidebar"},"datatp-hr/developer/CHANGELOG":{"id":"datatp-hr/developer/CHANGELOG","title":"Changelog","description":"All notable changes to this project will be documented in this file.","sidebar":"developerSidebar"},"datatp-hr/user/kpi/kpi":{"id":"datatp-hr/user/kpi/kpi","title":"KPI","description":"Quy tr\xecnh nghi\u1ec7p v\u1ee5:","sidebar":"userSidebar"},"datatp-hr/user/kpi/kpi_for_employee":{"id":"datatp-hr/user/kpi/kpi_for_employee","title":"kpi_for_employee","description":"I. Truy c\u1eadp KPI","sidebar":"userSidebar"},"datatp-hr/user/kpi/kpi_for_manager":{"id":"datatp-hr/user/kpi/kpi_for_manager","title":"kpi_for_manager","description":"I. Truy c\u1eadp KPI","sidebar":"userSidebar"},"datatp-hr/user/okr/okr":{"id":"datatp-hr/user/okr/okr","title":"OKR - C\u1ea5p qu\u1ea3n l\xfd.","description":"I. Truy c\u1eadp OKR","sidebar":"userSidebar"},"datatp-keycloak/developer/SETUP":{"id":"datatp-keycloak/developer/SETUP","title":"H\u01b0\u1edbng d\u1eabn c\xe0i \u0111\u1eb7t v\xe0 s\u1eed d\u1ee5ng Keycloak (Windows & macOS)","description":"Y\xeau c\u1ea7u h\u1ec7 th\u1ed1ng"},"datatp-keycloak/user/README":{"id":"datatp-keycloak/user/README","title":"README","description":"\ud83d\ude80 Test v\u1edbi Postman"},"datatp-tms/developer/BACKLOG":{"id":"datatp-tms/developer/BACKLOG","title":"DataTP TMS Backlog","description":"Tasks","sidebar":"developerSidebar"},"datatp-tms/developer/CHANGELOG":{"id":"datatp-tms/developer/CHANGELOG","title":"Changelog","description":"All notable changes to this project will be documented in this file."},"datatp-tms/developer/features":{"id":"datatp-tms/developer/features","title":"CRM Features","description":"- Lead Management: Qu\u1ea3n l\xfd kh\xe1ch h\xe0ng ti\u1ec1m n\u0103ng."},"datatp-tms/user/fleet/fleet":{"id":"datatp-tms/user/fleet/fleet","title":"FLEET APP (App \u0110i\u1ec1u V\u1eadn)","description":"I. Truy c\u1eadp Fleet App","sidebar":"userSidebar"},"datatp-tms/user/tms-bill/tms-bill":{"id":"datatp-tms/user/tms-bill/tms-bill","title":"TMS BILL APP","description":"I. \u0110\u0103ng Nh\u1eadp/Truy C\u1eadp App","sidebar":"userSidebar"},"datatp-tms/user/vendor-bill/vendor-bill":{"id":"datatp-tms/user/vendor-bill/vendor-bill","title":"H\u01af\u1edaNG D\u1eaaN S\u1eec D\u1ee4NG TMS D\xc0NH CHO TH\u1ea6U PH\u1ee4","description":"L\u1eddi c\u1ea3m \u01a1n","sidebar":"userSidebar"},"document-ie/developer/CHANGELOG":{"id":"document-ie/developer/CHANGELOG","title":"Changelog","description":"All notable changes to this project will be documented in this file.","sidebar":"developerSidebar"},"document-ie/developer/features":{"id":"document-ie/developer/features","title":"CRM Features","description":"- Lead Management: Qu\u1ea3n l\xfd kh\xe1ch h\xe0ng ti\u1ec1m n\u0103ng.","sidebar":"developerSidebar"},"document-ie/user/intro":{"id":"document-ie/user/intro","title":"intro","description":""},"shared/developer/BACKLOG":{"id":"shared/developer/BACKLOG","title":"DataTP CRM Backlog","description":"Tasks","sidebar":"developerSidebar"},"shared/developer/CHANGELOG":{"id":"shared/developer/CHANGELOG","title":"Changelog","description":"All notable changes to this project will be documented in this file.","sidebar":"developerSidebar"},"shared/developer/features":{"id":"shared/developer/features","title":"CRM Features","description":"- Lead Management: Qu\u1ea3n l\xfd kh\xe1ch h\xe0ng ti\u1ec1m n\u0103ng."},"shared/developer/SETUP":{"id":"shared/developer/SETUP","title":"Setup DataTP Project","description":"\ud83d\udccb Y\xeau c\u1ea7u c\xf4ng c\u1ee5 v\xe0 c\u1ea5u h\xecnh","sidebar":"developerSidebar"},"shared/user/asset/asset":{"id":"shared/user/asset/asset","title":"Car/Meeting Schedule","description":"Nghi\u1ec7p v\u1ee5 li\xean quan:","sidebar":"userSidebar"},"shared/user/system":{"id":"shared/user/system","title":"System","description":"Ch\xe0o m\u1eebng b\u1ea1n \u0111\u1ebfn v\u1edbi h\u01b0\u1edbng d\u1eabn s\u1eed d\u1ee5ng h\u1ec7 th\u1ed1ng Bee Logistics. Trong h\u01b0\u1edbng d\u1eabn n\xe0y, ch\xfang t\xf4i s\u1ebd gi\xfap b\u1ea1n b\u1eaft \u0111\u1ea7u s\u1eed d\u1ee5ng h\u1ec7 th\u1ed1ng m\u1ed9t c\xe1ch nhanh ch\xf3ng v\xe0 hi\u1ec7u qu\u1ea3.","sidebar":"userSidebar"},"shared/user/welcome":{"id":"shared/user/welcome","title":"welcome","description":"Welcome"}}}}')}}]);