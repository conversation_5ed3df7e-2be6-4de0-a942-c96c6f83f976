"use strict";(self.webpackChunkbeelogistics_docs=self.webpackChunkbeelogistics_docs||[]).push([[617],{173:(e,t,n)=>{n.r(t),n.d(t,{assets:()=>c,contentTitle:()=>r,default:()=>a,frontMatter:()=>i,metadata:()=>d,toc:()=>h});var s=n(216),l=n(494);const i={sidebar_position:1,hide_table_of_contents:!0,displayed_sidebar:"developerSidebar"},r="DataTP TMS Backlog",d={id:"datatp-tms/developer/BACKLOG",title:"DataTP TMS Backlog",description:"Tasks",source:"@site/docs/datatp-tms/developer/BACKLOG.md",sourceDirName:"datatp-tms/developer",slug:"/datatp-tms/developer/BACKLOG",permalink:"/en/docs/datatp-tms/developer/BACKLOG",draft:!1,unlisted:!1,tags:[],version:"current",sidebarPosition:1,frontMatter:{sidebar_position:1,hide_table_of_contents:!0,displayed_sidebar:"developerSidebar"},sidebar:"developerSidebar"},c={},h=[{value:"Tasks",id:"tasks",level:2},{value:"Current Sprint",id:"current-sprint",level:2}];function o(e){const t={em:"em",h1:"h1",h2:"h2",header:"header",li:"li",ol:"ol",p:"p",strong:"strong",table:"table",tbody:"tbody",td:"td",th:"th",thead:"thead",tr:"tr",ul:"ul",...(0,l.R)(),...e.components};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(t.header,{children:(0,s.jsx)(t.h1,{id:"datatp-tms-backlog",children:"DataTP TMS Backlog"})}),"\n",(0,s.jsx)(t.h2,{id:"tasks",children:"Tasks"}),"\n",(0,s.jsxs)(t.table,{children:[(0,s.jsx)(t.thead,{children:(0,s.jsxs)(t.tr,{children:[(0,s.jsx)(t.th,{style:{textAlign:"left"},children:"Category"}),(0,s.jsx)(t.th,{style:{textAlign:"left"},children:"Title"}),(0,s.jsx)(t.th,{style:{textAlign:"left"},children:"Status"}),(0,s.jsx)(t.th,{style:{textAlign:"left"},children:"Timeline"}),(0,s.jsx)(t.th,{style:{textAlign:"left"},children:"PIC"})]})}),(0,s.jsxs)(t.tbody,{children:[(0,s.jsxs)(t.tr,{children:[(0,s.jsx)(t.td,{style:{textAlign:"left"},children:"Enhancement"}),(0,s.jsx)(t.td,{style:{textAlign:"left"},children:"Task 1"}),(0,s.jsx)(t.td,{style:{textAlign:"left"},children:"In Progress"}),(0,s.jsx)(t.td,{style:{textAlign:"left"},children:"2025-07-16"}),(0,s.jsx)(t.td,{style:{textAlign:"left"},children:"Quan"})]}),(0,s.jsxs)(t.tr,{children:[(0,s.jsx)(t.td,{style:{textAlign:"left"},children:"Enhancement"}),(0,s.jsx)(t.td,{style:{textAlign:"left"},children:"Task 2"}),(0,s.jsx)(t.td,{style:{textAlign:"left"},children:"In Progress"}),(0,s.jsx)(t.td,{style:{textAlign:"left"},children:"2025-07-4"}),(0,s.jsx)(t.td,{style:{textAlign:"left"},children:"Chi\u1ebfn"})]})]})]}),"\n",(0,s.jsx)(t.p,{children:(0,s.jsx)(t.strong,{children:"Sprint Rules:"})}),"\n",(0,s.jsx)(t.h2,{id:"current-sprint",children:"Current Sprint"}),"\n",(0,s.jsxs)(t.ol,{children:["\n",(0,s.jsxs)(t.li,{children:["\n",(0,s.jsx)(t.p,{children:"\u0110\u1ea5u n\u1ed1i API push c\u01b0\u1edbc v\xe0 th\xf4ng tin xe t\u1eeb TMS -> BFSOne [DONE]"}),"\n"]}),"\n",(0,s.jsxs)(t.li,{children:["\n",(0,s.jsx)(t.p,{children:"Config d\u1eef li\u1ec7u ngu\u1ed3n cho office, c\u1eaft k\xed t\u1ef1 trong s\u1ed1 file"}),"\n"]}),"\n",(0,s.jsxs)(t.li,{children:["\n",(0,s.jsx)(t.p,{children:"Tri\u1ec3n khai vendor app"}),"\n"]}),"\n",(0,s.jsxs)(t.li,{children:["\n",(0,s.jsx)(t.p,{children:"\u0110\xf3ng g\xf3i file Doc IE theo \u0111\u1ecbnh d\u1ea1ng s\u1ed1 hbl-s\u1ed1 inv.pdf -> t\u1ea3i l\xean BFSOne"}),"\n"]}),"\n",(0,s.jsxs)(t.li,{children:["\n",(0,s.jsx)(t.p,{children:"T\xednh n\u0103ng - \u0110i\u1ec1u v\u1eadn:"}),"\n"]}),"\n"]}),"\n",(0,s.jsxs)(t.ul,{children:["\n",(0,s.jsx)(t.li,{children:"Check v\u1ecb tr\xed xe t\u1edbi v\u1ecb tr\xed kho kh\xe1ch h\xe0ng/c\xe1c xe ch\u01b0a t\u1edbi kho theo th\u1eddi gian k\u1ebf ho\u1ea1ch (Marine check 1 ti\u1ebfng tr\u01b0\u1edbc gi\u1edd k\u1ebf ho\u1ea1ch)"}),"\n",(0,s.jsx)(t.li,{children:"T\xednh kho\u1ea3ng c\xe1ch theo b\xe1n k\xednh"}),"\n"]}),"\n",(0,s.jsxs)(t.ol,{start:"6",children:["\n",(0,s.jsxs)(t.li,{children:["C\xe1c TMSBill \u0111\xe3 verify v\u1edbi HBL th\xec t\u1ea1o HBL\n",(0,s.jsx)(t.em,{children:"Future tasks"})]}),"\n"]})]})}function a(e={}){const{wrapper:t}={...(0,l.R)(),...e.components};return t?(0,s.jsx)(t,{...e,children:(0,s.jsx)(o,{...e})}):o(e)}},494:(e,t,n)=>{n.d(t,{R:()=>r,x:()=>d});var s=n(6372);const l={},i=s.createContext(l);function r(e){const t=s.useContext(i);return s.useMemo(function(){return"function"==typeof e?e(t):{...t,...e}},[t,e])}function d(e){let t;return t=e.disableParentContext?"function"==typeof e.components?e.components(l):e.components||l:r(e.components),s.createElement(i.Provider,{value:t},e.children)}}}]);