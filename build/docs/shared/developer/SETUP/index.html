<!doctype html>
<html lang="vi-VN" dir="ltr" class="docs-wrapper plugin-docs plugin-id-default docs-version-current docs-doc-page docs-doc-id-shared/developer/SETUP" data-has-hydrated="false">
<head>
<meta charset="UTF-8">
<meta name="generator" content="Docusaurus v3.5.2">
<title data-rh="true">Setup DataTP Project | DataTP Cloud Documentation</title><meta data-rh="true" name="viewport" content="width=device-width,initial-scale=1"><meta data-rh="true" name="twitter:card" content="summary_large_image"><meta data-rh="true" property="og:url" content="https://docs.beelogistics.cloud/docs/shared/developer/SETUP"><meta data-rh="true" property="og:locale" content="vi_VN"><meta data-rh="true" property="og:locale:alternate" content="en_GB"><meta data-rh="true" name="docusaurus_locale" content="vi"><meta data-rh="true" name="docsearch:language" content="vi"><meta data-rh="true" name="docusaurus_version" content="current"><meta data-rh="true" name="docusaurus_tag" content="docs-default-current"><meta data-rh="true" name="docsearch:version" content="current"><meta data-rh="true" name="docsearch:docusaurus_tag" content="docs-default-current"><meta data-rh="true" property="og:title" content="Setup DataTP Project | DataTP Cloud Documentation"><meta data-rh="true" name="description" content="📋 Yêu cầu công cụ và cấu hình"><meta data-rh="true" property="og:description" content="📋 Yêu cầu công cụ và cấu hình"><link data-rh="true" rel="icon" href="/img/favicon.ico"><link data-rh="true" rel="canonical" href="https://docs.beelogistics.cloud/docs/shared/developer/SETUP"><link data-rh="true" rel="alternate" href="https://docs.beelogistics.cloud/docs/shared/developer/SETUP" hreflang="vi-VN"><link data-rh="true" rel="alternate" href="https://docs.beelogistics.cloud/en/docs/shared/developer/SETUP" hreflang="en-GB"><link data-rh="true" rel="alternate" href="https://docs.beelogistics.cloud/docs/shared/developer/SETUP" hreflang="x-default"><link rel="stylesheet" href="/assets/css/styles.a320300f.css">
<script src="/assets/js/runtime~main.1cc49893.js" defer="defer"></script>
<script src="/assets/js/main.791fed99.js" defer="defer"></script>
</head>
<body class="navigation-with-keyboard">
<script>!function(){function t(t){document.documentElement.setAttribute("data-theme",t)}var e=function(){try{return new URLSearchParams(window.location.search).get("docusaurus-theme")}catch(t){}}()||function(){try{return window.localStorage.getItem("theme")}catch(t){}}();t(null!==e?e:"light")}(),function(){try{const n=new URLSearchParams(window.location.search).entries();for(var[t,e]of n)if(t.startsWith("docusaurus-data-")){var a=t.replace("docusaurus-data-","data-");document.documentElement.setAttribute(a,e)}}catch(t){}}()</script><div id="__docusaurus"><div role="region" aria-label="Nhảy tới nội dung"><a class="skipToContent_sO_d" href="#__docusaurus_skipToContent_fallback">Nhảy tới nội dung</a></div><nav aria-label="Thanh điều hướng" class="navbar navbar--fixed-top"><div class="navbar__inner"><div class="navbar__items"><button aria-label="Đóng - mở thanh điều hướng" aria-expanded="false" class="navbar__toggle clean-btn" type="button"><svg width="30" height="30" viewBox="0 0 30 30" aria-hidden="true"><path stroke="currentColor" stroke-linecap="round" stroke-miterlimit="10" stroke-width="2" d="M4 7h22M4 15h22M4 23h22"></path></svg></button><a class="navbar__brand" href="/"><div class="navbar__logo"><img src="/img/logo.png" alt="DataTP Cloud Logo" class="themedComponent_ybjk themedComponent--light_zoQ6"><img src="/img/logo.png" alt="DataTP Cloud Logo" class="themedComponent_ybjk themedComponent--dark_iuml"></div></a><a class="navbar__item navbar__link" href="/docs/shared/user/system">User Guides<!-- --></a><a aria-current="page" class="navbar__item navbar__link navbar__link--active" href="/docs/shared/developer/SETUP">Developer<!-- --></a></div><div class="navbar__items navbar__items--right"><div class="navbar__item dropdown dropdown--hoverable dropdown--right"><a href="#" aria-haspopup="true" aria-expanded="false" role="button" class="navbar__link"><svg viewBox="0 0 24 24" width="20" height="20" aria-hidden="true" class="iconLanguage_M5xz"><path fill="currentColor" d="M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z"></path></svg>Tiếng Việt<!-- --></a><ul class="dropdown__menu"><li><a href="/docs/shared/developer/SETUP" target="_self" rel="noopener noreferrer" class="dropdown__link dropdown__link--active" lang="vi-VN">Tiếng Việt<!-- --></a></li><li><a href="/en/docs/shared/developer/SETUP" target="_self" rel="noopener noreferrer" class="dropdown__link" lang="en-GB">English<!-- --></a></li></ul></div><div class="toggle_vwHj colorModeToggle_BRnA"><button class="clean-btn toggleButton_pCMA toggleButtonDisabled_e4z_" type="button" disabled="" title="Chuyển đổi chế độ sáng và tối (hiện tại chế độ sáng)" aria-label="Chuyển đổi chế độ sáng và tối (hiện tại chế độ sáng)" aria-live="polite"><svg viewBox="0 0 24 24" width="24" height="24" class="lightToggleIcon_O8M2"><path fill="currentColor" d="M12,9c1.65,0,3,1.35,3,3s-1.35,3-3,3s-3-1.35-3-3S10.35,9,12,9 M12,7c-2.76,0-5,2.24-5,5s2.24,5,5,5s5-2.24,5-5 S14.76,7,12,7L12,7z M2,13l2,0c0.55,0,1-0.45,1-1s-0.45-1-1-1l-2,0c-0.55,0-1,0.45-1,1S1.45,13,2,13z M20,13l2,0c0.55,0,1-0.45,1-1 s-0.45-1-1-1l-2,0c-0.55,0-1,0.45-1,1S19.45,13,20,13z M11,2v2c0,0.55,0.45,1,1,1s1-0.45,1-1V2c0-0.55-0.45-1-1-1S11,1.45,11,2z M11,20v2c0,0.55,0.45,1,1,1s1-0.45,1-1v-2c0-0.55-0.45-1-1-1C11.45,19,11,19.45,11,20z M5.99,4.58c-0.39-0.39-1.03-0.39-1.41,0 c-0.39,0.39-0.39,1.03,0,1.41l1.06,1.06c0.39,0.39,1.03,0.39,1.41,0s0.39-1.03,0-1.41L5.99,4.58z M18.36,16.95 c-0.39-0.39-1.03-0.39-1.41,0c-0.39,0.39-0.39,1.03,0,1.41l1.06,1.06c0.39,0.39,1.03,0.39,1.41,0c0.39-0.39,0.39-1.03,0-1.41 L18.36,16.95z M19.42,5.99c0.39-0.39,0.39-1.03,0-1.41c-0.39-0.39-1.03-0.39-1.41,0l-1.06,1.06c-0.39,0.39-0.39,1.03,0,1.41 s1.03,0.39,1.41,0L19.42,5.99z M7.05,18.36c0.39-0.39,0.39-1.03,0-1.41c-0.39-0.39-1.03-0.39-1.41,0l-1.06,1.06 c-0.39,0.39-0.39,1.03,0,1.41s1.03,0.39,1.41,0L7.05,18.36z"></path></svg><svg viewBox="0 0 24 24" width="24" height="24" class="darkToggleIcon_hKRZ"><path fill="currentColor" d="M9.37,5.51C9.19,6.15,9.1,6.82,9.1,7.5c0,4.08,3.32,7.4,7.4,7.4c0.68,0,1.35-0.09,1.99-0.27C17.45,17.19,14.93,19,12,19 c-3.86,0-7-3.14-7-7C5,9.07,6.81,6.55,9.37,5.51z M12,3c-4.97,0-9,4.03-9,9s4.03,9,9,9s9-4.03,9-9c0-0.46-0.04-0.92-0.1-1.36 c-0.98,1.37-2.58,2.26-4.4,2.26c-2.98,0-5.4-2.42-5.4-5.4c0-1.81,0.89-3.42,2.26-4.4C12.92,3.04,12.46,3,12,3L12,3z"></path></svg></button></div><div class="navbarSearchContainer_zC86"></div></div></div><div role="presentation" class="navbar-sidebar__backdrop"></div></nav><div id="__docusaurus_skipToContent_fallback" class="main-wrapper mainWrapper_btEJ"><div class="docsWrapper_LEG8"><button aria-label="Trở lại đầu trang" class="clean-btn theme-back-to-top-button backToTopButton_yO2w" type="button"></button><div class="docRoot_QXfX"><aside class="theme-doc-sidebar-container docSidebarContainer_YfKm"><div class="sidebarViewport_kETH"><div class="sidebar_Jdpv"><nav aria-label="Thanh điều hướng tài liệu" class="menu thin-scrollbar menu_kjpJ"><ul class="theme-doc-sidebar-menu menu__list"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-1 menu__list-item"><a class="menu__link menu__link--active" aria-current="page" href="/docs/shared/developer/SETUP">Setup<!-- --></a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-1 menu__list-item"><a class="menu__link" href="/docs/shared/developer/CHANGELOG">Changelog<!-- --></a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-1 menu__list-item"><a class="menu__link" href="/docs/shared/developer/BACKLOG">Backlog<!-- --></a></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" href="/docs/datatp-crm/developer/BACKLOG">CRM</a></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" href="/docs/document-ie/developer/CHANGELOG">Document IE</a></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" href="/docs/datatp-hr/developer/CHANGELOG">HR</a></div></li></ul></nav><button type="button" title="Thu gọn thanh bên" aria-label="Thu gọn thanh bên" class="button button--secondary button--outline collapseSidebarButton_Vuw3"><svg width="20" height="20" aria-hidden="true" class="collapseSidebarButtonIcon_Vp19"><g fill="#7a7a7a"><path d="M9.992 10.023c0 .2-.062.399-.172.547l-4.996 7.492a.982.982 0 01-.828.454H1c-.55 0-1-.453-1-1 0-.2.059-.403.168-.551l4.629-6.942L.168 3.078A.939.939 0 010 2.528c0-.548.45-.997 1-.997h2.996c.352 0 .649.18.828.45L9.82 9.472c.11.148.172.347.172.55zm0 0"></path><path d="M19.98 10.023c0 .2-.058.399-.168.547l-4.996 7.492a.987.987 0 01-.828.454h-3c-.547 0-.996-.453-.996-1 0-.2.059-.403.168-.551l4.625-6.942-4.625-6.945a.939.939 0 01-.168-.55 1 1 0 01.996-.997h3c.348 0 .649.18.828.45l4.996 7.492c.11.148.168.347.168.55zm0 0"></path></g></svg></button></div></div></aside><main class="docMainContainer_hcuh"><div class="container padding-top--md padding-bottom--lg"><div class="row"><div class="col"><div class="docItemContainer_jkBE"><article><nav class="theme-doc-breadcrumbs breadcrumbsContainer_ON7I" aria-label="Liên kết điều hướng"><ul class="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList"><li class="breadcrumbs__item"><a aria-label="Trang chủ" class="breadcrumbs__link" href="/"><svg viewBox="0 0 24 24" class="breadcrumbHomeIcon_H14Z"><path d="M10 19v-5h4v5c0 .55.45 1 1 1h3c.55 0 1-.45 1-1v-7h1.7c.46 0 .68-.57.33-.87L12.67 3.6c-.38-.34-.96-.34-1.34 0l-8.36 7.53c-.34.3-.13.87.33.87H5v7c0 .55.45 1 1 1h3c.55 0 1-.45 1-1z" fill="currentColor"></path></svg></a></li><li itemscope="" itemprop="itemListElement" itemtype="https://schema.org/ListItem" class="breadcrumbs__item breadcrumbs__item--active"><span class="breadcrumbs__link" itemprop="name">Setup</span><meta itemprop="position" content="1"></li></ul></nav><div class="theme-doc-markdown markdown"><header><h1>Setup DataTP Project</h1></header>
<!-- --><h2 class="anchor anchorWithStickyNavbar_Sb3Y" id="-yêu-cầu-công-cụ-và-cấu-hình">📋 Yêu cầu công cụ và cấu hình<!-- --><a href="#-yêu-cầu-công-cụ-và-cấu-hình" class="hash-link" aria-label="Đường dẫn trực tiếp tới 📋 Yêu cầu công cụ và cấu hình" title="Đường dẫn trực tiếp tới 📋 Yêu cầu công cụ và cấu hình">​</a></h2>
<!-- --><h3 class="anchor anchorWithStickyNavbar_Sb3Y" id="️-công-cụ-chính">🛠️ Công cụ chính<!-- --><a href="#️-công-cụ-chính" class="hash-link" aria-label="Đường dẫn trực tiếp tới 🛠️ Công cụ chính" title="Đường dẫn trực tiếp tới 🛠️ Công cụ chính">​</a></h3>
<!-- --><table><thead><tr><th>Công cụ</th><th>Phiên bản</th><th>Mô tả</th></tr></thead><tbody><tr><td>Node.js</td><td>&gt; 23</td><td>JavaScript runtime</td></tr><tr><td>pnpm</td><td>Mới nhất</td><td>Package manager</td></tr><tr><td>Git</td><td>Mới nhất</td><td>Version control</td></tr><tr><td>Java</td><td>21</td><td>Java runtime</td></tr><tr><td>Gradle</td><td>&gt; 8.7</td><td>Build tool</td></tr><tr><td>VS Code/Eclipse</td><td>Mới nhất</td><td>IDE</td></tr><tr><td>Postgres</td><td>&gt; 16</td><td>Database server</td></tr><tr><td>DBeaver</td><td>Mới nhất</td><td>Database explorer</td></tr></tbody></table>
<!-- --><h3 class="anchor anchorWithStickyNavbar_Sb3Y" id="-công-cụ-bổ-sung">🧰 Công cụ bổ sung<!-- --><a href="#-công-cụ-bổ-sung" class="hash-link" aria-label="Đường dẫn trực tiếp tới 🧰 Công cụ bổ sung" title="Đường dẫn trực tiếp tới 🧰 Công cụ bổ sung">​</a></h3>
<!-- --><ul>
<!-- --><li>Python (phiên bản 3.10 trở lên)</li>
<!-- --><li>Docker</li>
<!-- --><li>K3s</li>
<!-- --></ul>
<!-- --><h2 class="anchor anchorWithStickyNavbar_Sb3Y" id="️-cấu-hình">⚙️ Cấu hình<!-- --><a href="#️-cấu-hình" class="hash-link" aria-label="Đường dẫn trực tiếp tới ⚙️ Cấu hình" title="Đường dẫn trực tiếp tới ⚙️ Cấu hình">​</a></h2>
<!-- --><h3 class="anchor anchorWithStickyNavbar_Sb3Y" id="1-git">1. Git<!-- --><a href="#1-git" class="hash-link" aria-label="Đường dẫn trực tiếp tới 1. Git" title="Đường dẫn trực tiếp tới 1. Git">​</a></h3>
<!-- --><div class="language-bash codeBlockContainer_lHyG theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_o3WU"><pre tabindex="0" class="prism-code language-bash codeBlock_SsTB thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_GnfZ"><span class="token-line" style="color:#393A34"><span class="token plain"># Cấu hình thông tin người dùng</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">git config --global user.email &quot;<EMAIL>&quot;</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">git config --global user.name &quot;Your Name&quot;</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"># Cấu hình file mode</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">git config --global core.filemode false</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"># Cấu hình line ending với unix style</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">git config --global core.autocrlf false</span><br></span></code></pre><div class="buttonGroup_bRv8"><button type="button" aria-label="Sao chép code vào bộ nhớ tạm" title="Sao chép" class="clean-btn"><span class="copyButtonIcons_U7Rx" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_RLFU"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_aQBr"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<!-- --><h3 class="anchor anchorWithStickyNavbar_Sb3Y" id="2-ssh-key">2. SSH Key<!-- --><a href="#2-ssh-key" class="hash-link" aria-label="Đường dẫn trực tiếp tới 2. SSH Key" title="Đường dẫn trực tiếp tới 2. SSH Key">​</a></h3>
<!-- --><div class="language-bash codeBlockContainer_lHyG theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_o3WU"><pre tabindex="0" class="prism-code language-bash codeBlock_SsTB thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_GnfZ"><span class="token-line" style="color:#393A34"><span class="token plain"># Tạo SSH key mới</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">ssh-keygen -t ed25519 -b 4096 -C &quot;<EMAIL>&quot;</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"># Khởi động SSH agent</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">eval &quot;$(ssh-agent -s)&quot;</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"># Thêm SSH key vào SSH agent</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">ssh-add ~/.ssh/id_ed25519</span><br></span></code></pre><div class="buttonGroup_bRv8"><button type="button" aria-label="Sao chép code vào bộ nhớ tạm" title="Sao chép" class="clean-btn"><span class="copyButtonIcons_U7Rx" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_RLFU"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_aQBr"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<!-- --><h3 class="anchor anchorWithStickyNavbar_Sb3Y" id="3-thêm-ssh-key-vào-tài-khoản-gitlab">3. Thêm SSH key vào tài khoản GitLab<!-- --><a href="#3-thêm-ssh-key-vào-tài-khoản-gitlab" class="hash-link" aria-label="Đường dẫn trực tiếp tới 3. Thêm SSH key vào tài khoản GitLab" title="Đường dẫn trực tiếp tới 3. Thêm SSH key vào tài khoản GitLab">​</a></h3>
<!-- --><ol>
<!-- --><li>Sao chép nội dung SSH key<!-- -->
<!-- --><div class="language-bash codeBlockContainer_lHyG theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_o3WU"><pre tabindex="0" class="prism-code language-bash codeBlock_SsTB thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_GnfZ"><span class="token-line" style="color:#393A34"><span class="token plain">cat ~/.ssh/id_ed25519.pub | pbcopy</span><br></span></code></pre><div class="buttonGroup_bRv8"><button type="button" aria-label="Sao chép code vào bộ nhớ tạm" title="Sao chép" class="clean-btn"><span class="copyButtonIcons_U7Rx" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_RLFU"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_aQBr"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<!-- --></li>
<!-- --><li>Đăng nhập vào GitLab</li>
<!-- --><li>Vào <!-- --><strong>Settings &gt; SSH Keys</strong></li>
<!-- --><li>Dán SSH key và đặt tên cho key</li>
<!-- --></ol>
<!-- --><h2 class="anchor anchorWithStickyNavbar_Sb3Y" id="-cài-đặt-dự-án">🚀 Cài đặt dự án<!-- --><a href="#-cài-đặt-dự-án" class="hash-link" aria-label="Đường dẫn trực tiếp tới 🚀 Cài đặt dự án" title="Đường dẫn trực tiếp tới 🚀 Cài đặt dự án">​</a></h2>
<!-- --><h3 class="anchor anchorWithStickyNavbar_Sb3Y" id="1-tạo-thư-mục-root-và-clone-dự-án">1. Tạo thư mục root và clone dự án<!-- --><a href="#1-tạo-thư-mục-root-và-clone-dự-án" class="hash-link" aria-label="Đường dẫn trực tiếp tới 1. Tạo thư mục root và clone dự án" title="Đường dẫn trực tiếp tới 1. Tạo thư mục root và clone dự án">​</a></h3>
<!-- --><div class="language-bash codeBlockContainer_lHyG theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_o3WU"><pre tabindex="0" class="prism-code language-bash codeBlock_SsTB thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_GnfZ"><span class="token-line" style="color:#393A34"><span class="token plain"># Tạo thư mục root</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">mkdir datatp</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">cd datatp</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"># Clone các dự án</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">git clone git@gitlab:datatp.net:tuan/datatp-core.git</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">git clone git@gitlab:datatp.net:tuan/datatp-erp.git</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">git clone git@gitlab:datatp.net:tuan/datatp-document-ie.git</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">git clone git@gitlab:datatp.net:tuan/datatp-logistics.git</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">git clone git@gitlab:datatp.net:tuan/datatp-crm.git</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">git clone git@gitlab:datatp.net:tuan/datatp-build.git</span><br></span></code></pre><div class="buttonGroup_bRv8"><button type="button" aria-label="Sao chép code vào bộ nhớ tạm" title="Sao chép" class="clean-btn"><span class="copyButtonIcons_U7Rx" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_RLFU"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_aQBr"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<!-- --><h4 class="anchor anchorWithStickyNavbar_Sb3Y" id="-cấu-trúc-dự-án">📂 Cấu trúc dự án<!-- --><a href="#-cấu-trúc-dự-án" class="hash-link" aria-label="Đường dẫn trực tiếp tới 📂 Cấu trúc dự án" title="Đường dẫn trực tiếp tới 📂 Cấu trúc dự án">​</a></h4>
<!-- --><p>Sau khi clone, cấu trúc thư mục của dự án sẽ như sau:</p>
<!-- --><div class="codeBlockContainer_lHyG theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_o3WU"><pre tabindex="0" class="prism-code language-text codeBlock_SsTB thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_GnfZ"><span class="token-line" style="color:#393A34"><span class="token plain">datatp/</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">├── datatp-core/               # Core services và utilities</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">│   ├── app/                   # Core application</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">│   ├── service/               # Core services</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">│   └── webui/                 # Core UI components</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">│</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">├── datatp-erp/                # Enterprise Resource Planning</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">│   ├── app/                   # ERP application</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">│   ├── module/                # ERP modules</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">│   └── webui/                 # ERP UI components</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">│       ├── lib/               # Shared UI library</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">│       └── erp/               # ERP specific UI</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">│</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">├── datatp-document-ie/        # Document Information Extractor</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">│   ├── app/                   # Document IE application</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">│   └── webui/                 # Document IE UI</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">│</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">├── datatp-logistics/          # Logistics management</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">│   ├── app/                   # Logistics application</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">│   ├── module/                # Logistics modules</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">│   └── webui/                 # Logistics UI</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">│</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">├── datatp-crm/                # Customer Relationship Management</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">│   ├── app/                   # CRM application</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">│   └── webui/                 # CRM UI</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">│</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">└── datatp-build/              # Build tools and configurations</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    ├── scripts/               # Build scripts</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    └── config/                # Build configurations</span><br></span></code></pre><div class="buttonGroup_bRv8"><button type="button" aria-label="Sao chép code vào bộ nhớ tạm" title="Sao chép" class="clean-btn"><span class="copyButtonIcons_U7Rx" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_RLFU"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_aQBr"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<!-- --><h4 class="anchor anchorWithStickyNavbar_Sb3Y" id="-quan-hệ-giữa-các-dự-án">🔄 Quan hệ giữa các dự án<!-- --><a href="#-quan-hệ-giữa-các-dự-án" class="hash-link" aria-label="Đường dẫn trực tiếp tới 🔄 Quan hệ giữa các dự án" title="Đường dẫn trực tiếp tới 🔄 Quan hệ giữa các dự án">​</a></h4>
<!-- --><div class="codeBlockContainer_lHyG theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_o3WU"><pre tabindex="0" class="prism-code language-text codeBlock_SsTB thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_GnfZ"><span class="token-line" style="color:#393A34"><span class="token plain">                                ┌─────────────────┐</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">                                │   datatp-build  │</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">                                └─────────┬───────┘</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">                                          ▼</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">          ┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">          │document-ie      │     │  datatp-crm     │     │datatp-logistics │</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">          └─────────┬───────┘     └─────────┬───────┘     └─────────┬───────┘</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">                    │                       │                       │</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">                    │                       │                       │</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">                    ▼                       ▼                       ▼</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">                                  ┌─────────────────┐</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">                                  │   datatp-erp    │</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">                                  └─────────┬───────┘</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">                                            │</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">                                            │</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">                                            ▼</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">                                  ┌─────────────────┐</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">                                  │   datatp-core   │</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">                                  └─────────────────┘</span><br></span></code></pre><div class="buttonGroup_bRv8"><button type="button" aria-label="Sao chép code vào bộ nhớ tạm" title="Sao chép" class="clean-btn"><span class="copyButtonIcons_U7Rx" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_RLFU"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_aQBr"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<!-- --><h3 class="anchor anchorWithStickyNavbar_Sb3Y" id="2-backend">2. Backend<!-- --><a href="#2-backend" class="hash-link" aria-label="Đường dẫn trực tiếp tới 2. Backend" title="Đường dẫn trực tiếp tới 2. Backend">​</a></h3>
<!-- --><p>Cài đặt dependencies và build cho từng projects theo thứ tự:</p>
<!-- --><blockquote>
<!-- --><p>⚠️ <!-- --><strong>Quan trọng</strong>: Tuân thủ đúng thứ tự build<!-- --></p>
<!-- --></blockquote>
<!-- --><ol>
<!-- --><li>datatp-core</li>
<!-- --><li>datatp-erp</li>
<!-- --><li>datatp-document-ie</li>
<!-- --><li>datatp-logistics</li>
<!-- --><li>datatp-crm</li>
<!-- --><li>datatp-build</li>
<!-- --></ol>
<!-- --><div class="language-bash codeBlockContainer_lHyG theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_o3WU"><pre tabindex="0" class="prism-code language-bash codeBlock_SsTB thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_GnfZ"><span class="token-line" style="color:#393A34"><span class="token plain">cd datatp-core</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">gradle clean build -x test</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">gradle publishToMaven</span><br></span></code></pre><div class="buttonGroup_bRv8"><button type="button" aria-label="Sao chép code vào bộ nhớ tạm" title="Sao chép" class="clean-btn"><span class="copyButtonIcons_U7Rx" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_RLFU"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_aQBr"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<!-- --><h3 class="anchor anchorWithStickyNavbar_Sb3Y" id="3-frontend">3. Frontend<!-- --><a href="#3-frontend" class="hash-link" aria-label="Đường dẫn trực tiếp tới 3. Frontend" title="Đường dẫn trực tiếp tới 3. Frontend">​</a></h3>
<!-- --><p>Install và build webui cho các dự án:</p>
<!-- --><div class="language-bash codeBlockContainer_lHyG theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_o3WU"><pre tabindex="0" class="prism-code language-bash codeBlock_SsTB thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_GnfZ"><span class="token-line" style="color:#393A34"><span class="token plain">cd datatp-erp/webui/lib</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">pnpm install</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">pnpm run build</span><br></span></code></pre><div class="buttonGroup_bRv8"><button type="button" aria-label="Sao chép code vào bộ nhớ tạm" title="Sao chép" class="clean-btn"><span class="copyButtonIcons_U7Rx" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_RLFU"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_aQBr"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<!-- --><h3 class="anchor anchorWithStickyNavbar_Sb3Y" id="4-database-và-môi-trường">4. Database và môi trường<!-- --><a href="#4-database-và-môi-trường" class="hash-link" aria-label="Đường dẫn trực tiếp tới 4. Database và môi trường" title="Đường dẫn trực tiếp tới 4. Database và môi trường">​</a></h3>
<!-- --><blockquote>
<!-- --><p>📁 Thực hiện trong thư mục: <!-- --><code>working/release-dev/server-env/</code></p>
<!-- --></blockquote>
<!-- --><ul>
<!-- --><li>Cấu hình thông tin server posgres trong file <!-- --><code>db-env.sh</code></li>
<!-- --></ul>
<!-- --><div class="codeBlockContainer_lHyG theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_o3WU"><pre tabindex="0" class="prism-code language-text codeBlock_SsTB thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_GnfZ"><span class="token-line" style="color:#393A34"><span class="token plain">  DB_HOST=&#x27;localhost&#x27;</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  DB_PORT=&#x27;5432&#x27;</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  PG_ADMIN_USER=&#x27;postgres&#x27; # User admin database</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  PG_ADMIN_PASSWORD=&#x27;pgadmin&#x27; # Password admin database</span><br></span></code></pre><div class="buttonGroup_bRv8"><button type="button" aria-label="Sao chép code vào bộ nhớ tạm" title="Sao chép" class="clean-btn"><span class="copyButtonIcons_U7Rx" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_RLFU"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_aQBr"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<!-- --><ul>
<!-- --><li>Cấu hình thông tin files application config theo môi trường.</li>
<!-- --></ul>
<!-- --><div class="codeBlockContainer_lHyG theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_o3WU"><pre tabindex="0" class="prism-code language-text codeBlock_SsTB thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_GnfZ"><span class="token-line" style="color:#393A34"><span class="token plain">  # Copy file application-dev-sample.yaml thành application-dev.yaml</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  cp application-dev-sample.yaml application-dev.yaml</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  # Copy file application-prod-sample.yaml thành application-prod.yaml</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  cp application-prod-sample.yaml application-prod.yaml</span><br></span></code></pre><div class="buttonGroup_bRv8"><button type="button" aria-label="Sao chép code vào bộ nhớ tạm" title="Sao chép" class="clean-btn"><span class="copyButtonIcons_U7Rx" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_RLFU"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_aQBr"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<!-- --><ul>
<!-- --><li>Tạo database, user, restore database mẫu.</li>
<!-- --></ul>
<!-- --><div class="codeBlockContainer_lHyG theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_o3WU"><pre tabindex="0" class="prism-code language-text codeBlock_SsTB thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_GnfZ"><span class="token-line" style="color:#393A34"><span class="token plain">./db.sh [db_name] [create-admin][create-user][create-ro-user][vacuum][new][drop]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"># Example</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">./db.sh datatpdb create-admin</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">./db.sh datatpdb create-user</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">./db.sh datatpdb create-ro-user</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">./db.sh datatpdb new</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">./db.sh datatpdb restore --file=datatpdb-latest.dump</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">./db.sh datatpdb vacuum</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">./db.sh datatpdb dump</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">./db.sh datatpdb drop</span><br></span></code></pre><div class="buttonGroup_bRv8"><button type="button" aria-label="Sao chép code vào bộ nhớ tạm" title="Sao chép" class="clean-btn"><span class="copyButtonIcons_U7Rx" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_RLFU"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_aQBr"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<!-- --><h4 class="anchor anchorWithStickyNavbar_Sb3Y" id="41-datatp-core">4.1. DataTP Core<!-- --><a href="#41-datatp-core" class="hash-link" aria-label="Đường dẫn trực tiếp tới 4.1. DataTP Core" title="Đường dẫn trực tiếp tới 4.1. DataTP Core">​</a></h4>
<!-- --><ol>
<!-- --><li>Download database tại: <!-- --><a href="https://beelogistics.cloud/download/datatpdb-latest.dump" target="_blank" rel="noopener noreferrer">https://beelogistics.cloud/download/datatpdb-latest.dump</a></li>
<!-- --><li>Tạo user, khởi tạo DB, và restore database mẫu:<!-- -->
<!-- --><div class="language-bash codeBlockContainer_lHyG theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_o3WU"><pre tabindex="0" class="prism-code language-bash codeBlock_SsTB thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_GnfZ"><span class="token-line" style="color:#393A34"><span class="token plain"> ./db.sh datatpdb new</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"> ./db.sh datatpdb create-user</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"> ./db.sh datatpdb new</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"> ./db.sh datatpdb restore --file=datatpdb-latest.dump</span><br></span></code></pre><div class="buttonGroup_bRv8"><button type="button" aria-label="Sao chép code vào bộ nhớ tạm" title="Sao chép" class="clean-btn"><span class="copyButtonIcons_U7Rx" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_RLFU"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_aQBr"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<!-- --></li>
<!-- --></ol>
<!-- --><h4 class="anchor anchorWithStickyNavbar_Sb3Y" id="42-datatp-document-ie">4.2. DataTP Document IE<!-- --><a href="#42-datatp-document-ie" class="hash-link" aria-label="Đường dẫn trực tiếp tới 4.2. DataTP Document IE" title="Đường dẫn trực tiếp tới 4.2. DataTP Document IE">​</a></h4>
<!-- --><ol>
<!-- --><li>Download database tại: <!-- --><a href="https://beelogistics.cloud/download/document_ie_db-latest.dump" target="_blank" rel="noopener noreferrer">https://beelogistics.cloud/download/document_ie_db-latest.dump</a></li>
<!-- --><li>Tạo user, khởi tạo DB, và restore database mẫu:<!-- -->
<!-- --><div class="language-bash codeBlockContainer_lHyG theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_o3WU"><pre tabindex="0" class="prism-code language-bash codeBlock_SsTB thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_GnfZ"><span class="token-line" style="color:#393A34"><span class="token plain"> ./db.sh document_ie_db new</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"> ./db.sh document_ie_db create-user</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"> ./db.sh document_ie_db new</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"> ./db.sh document_ie_db restore --file=document_ie_db-latest.dump</span><br></span></code></pre><div class="buttonGroup_bRv8"><button type="button" aria-label="Sao chép code vào bộ nhớ tạm" title="Sao chép" class="clean-btn"><span class="copyButtonIcons_U7Rx" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_RLFU"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_aQBr"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<!-- --></li>
<!-- --></ol>
<!-- --><h3 class="anchor anchorWithStickyNavbar_Sb3Y" id="5-khởi-động-server-phát-triển">5. Khởi động server phát triển.<!-- --><a href="#5-khởi-động-server-phát-triển" class="hash-link" aria-label="Đường dẫn trực tiếp tới 5. Khởi động server phát triển." title="Đường dẫn trực tiếp tới 5. Khởi động server phát triển.">​</a></h3>
<!-- --><h4 class="anchor anchorWithStickyNavbar_Sb3Y" id="51-backend">5.1. Backend.<!-- --><a href="#51-backend" class="hash-link" aria-label="Đường dẫn trực tiếp tới 5.1. Backend." title="Đường dẫn trực tiếp tới 5.1. Backend.">​</a></h4>
<!-- --><div class="language-bash codeBlockContainer_lHyG theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_o3WU"><pre tabindex="0" class="prism-code language-bash codeBlock_SsTB thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_GnfZ"><span class="token-line" style="color:#393A34"><span class="token plain">cd datatp-build</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">./datatp.sh build:all -build-ui</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">./datatp.sh instances run:update</span><br></span></code></pre><div class="buttonGroup_bRv8"><button type="button" aria-label="Sao chép code vào bộ nhớ tạm" title="Sao chép" class="clean-btn"><span class="copyButtonIcons_U7Rx" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_RLFU"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_aQBr"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<!-- --><h4 class="anchor anchorWithStickyNavbar_Sb3Y" id="52-frontend">5.2. Frontend.<!-- --><a href="#52-frontend" class="hash-link" aria-label="Đường dẫn trực tiếp tới 5.2. Frontend." title="Đường dẫn trực tiếp tới 5.2. Frontend.">​</a></h4>
<!-- --><div class="language-bash codeBlockContainer_lHyG theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_o3WU"><pre tabindex="0" class="prism-code language-bash codeBlock_SsTB thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_GnfZ"><span class="token-line" style="color:#393A34"><span class="token plain">cd datatp-build/web/phoenix</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">ADD_APPS=&quot;logistics&quot; pnpm run dev-server</span><br></span></code></pre><div class="buttonGroup_bRv8"><button type="button" aria-label="Sao chép code vào bộ nhớ tạm" title="Sao chép" class="clean-btn"><span class="copyButtonIcons_U7Rx" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_RLFU"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_aQBr"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<!-- --><p>Mở <!-- --><a href="http://localhost:3000" target="_blank" rel="noopener noreferrer">http://localhost:3000</a> để xem trang web.<!-- --></p>
<!-- --><h2 class="anchor anchorWithStickyNavbar_Sb3Y" id="-kiểm-tra-cài-đặt">🔍 Kiểm tra cài đặt<!-- --><a href="#-kiểm-tra-cài-đặt" class="hash-link" aria-label="Đường dẫn trực tiếp tới 🔍 Kiểm tra cài đặt" title="Đường dẫn trực tiếp tới 🔍 Kiểm tra cài đặt">​</a></h2>
<!-- --><ul class="contains-task-list containsTaskList_cs9p">
<!-- --><li class="task-list-item"><input type="checkbox" disabled=""> <!-- -->Tất cả các dự án đã được clone thành công<!-- --></li>
<!-- --><li class="task-list-item"><input type="checkbox" disabled=""> <!-- -->Backend đã được build theo đúng thứ tự<!-- --></li>
<!-- --><li class="task-list-item"><input type="checkbox" disabled=""> <!-- -->Frontend đã được cài đặt và build<!-- --></li>
<!-- --><li class="task-list-item"><input type="checkbox" disabled=""> <!-- -->Database đã được khởi tạo và restore<!-- --></li>
<!-- --><li class="task-list-item"><input type="checkbox" disabled=""> <!-- -->Server phát triển đã khởi động thành công<!-- --></li>
<!-- --></ul></div></article><nav class="pagination-nav docusaurus-mt-lg" aria-label="Trang tài liệu"></nav></div></div></div></div></main></div></div></div><footer class="footer footer--dark"><div class="container container-fluid"><div class="footer__bottom text--center"><div class="footer__copyright">Copyright © 2025 DataTP Cloud.</div></div></div></footer></div>
</body>
</html>