import org.gradle.plugins.ide.eclipse.model.SourceFolder

allprojects {
}


subprojects {
  apply plugin: 'java'
  apply plugin: 'java-library'
  apply plugin: 'groovy'
  apply plugin: 'eclipse'
  apply plugin: 'maven-publish'
  
  ext {
    set("jakarta-servlet.version", '5.0.0')
  }
  
  project.ext {
    //releaseDir               = "${buildDir}/release"
  }
  
  eclipse {
    classpath {
      downloadSources=true
    }
    
    project {
    }
  }
  
  eclipse.classpath.defaultOutputDir = file( 'build/classes' )
  
  eclipse.classpath.file {
    beforeMerged { classpath ->
      classpath.entries.clear()
    }
    whenMerged {  cp ->
      cp.entries.findAll { it instanceof SourceFolder && it.path.startsWith("src/main/") }*.output = "build/bin/main"
      cp.entries.findAll { it instanceof SourceFolder && it.path.startsWith("src/test/") }*.output = "build/bin/test"
      cp.entries.removeAll { it.kind == "output" }
    }
  }
  
  group = 'net.datatp'
  version = '1.0.0'
  sourceCompatibility = 17
  compileJava.options.encoding = 'UTF-8'
  
  compileJava {
    options.compilerArgs.add("-parameters")
  }
  
  configurations {
    all {
      resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
    }
    
    compile {
      transitive = true
    }
  }
  
  repositories {
    mavenLocal()
    mavenCentral()
    //maven { url "https://nexus.dev.datatp.net/repository/maven-central/" }
  }
  
  task sourcesJar(type: Jar, dependsOn: classes) {
    archiveClassifier = 'sources'
    from sourceSets.main.allSource
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
  }
  
  artifacts {
    archives sourcesJar
  }
  
  
  publishing {
    publications {
      "$project.name"(MavenPublication) {
        groupId project.group
        artifactId project.name
        version project.version
        from components.java
        artifact sourcesJar
      }
    }
    
    
    repositories {
      maven {
        name = 'ahaysoft'
        credentials(PasswordCredentials)
        url = uri('https://nexus.dev.datatp.net/repository/maven-releases/')
      }
    }
  }

  dependencies {
    implementation "org.slf4j:slf4j-api:${slf4jVersion}"
    implementation "org.slf4j:slf4j-ext:${slf4jVersion}"
    
    implementation 'org.projectlombok:lombok:1.18.30'
    annotationProcessor 'org.projectlombok:lombok:1.18.30'
    testCompileOnly 'org.projectlombok:lombok:1.18.32'
    testAnnotationProcessor 'org.projectlombok:lombok:1.18.32'

    api group: 'javax.annotation', name: 'javax.annotation-api', version: '1.3.2'
    api group: 'junit', name: 'junit', version: '4.12'

    testRuntimeOnly "org.junit.platform:junit-platform-commons:1.10.1"
    testRuntimeOnly 'org.junit.platform:junit-platform-launcher:1.10.1'
    implementation group: 'org.junit.jupiter', name: 'junit-jupiter-engine', version: '5.10.1'
    
    testImplementation "org.springframework.batch:spring-batch-test:${springBatchVersion}"
    testImplementation "org.springframework.boot:spring-boot-test-autoconfigure:${springBootVersion}"
    testImplementation ("org.springframework.boot:spring-boot-starter-test:${springBootVersion}") ;
    
  }
  
  test {
    testLogging.showStandardStreams = false
    useJUnitPlatform()
    
    forkEvery = 0
    ignoreFailures = true
    
    filter {
      includeTestsMatching "*UnitTest"
    }
    
    testLogging {
      events "passed", "skipped", "failed"
    }
    
    useJUnitPlatform {
      //excludeTags "integration"
    }
    
  }
  
  task itest(type: Test) {
    description = 'Runs integration tests.'
    testLogging.showStandardStreams = true
    
    testLogging {
      events "passed", "skipped", "failed"
    }
    
    useJUnitPlatform {
      includeTags('integration')
    }
    
  }

  task utest(type: Test) {
    ignoreFailures = true
    description = 'Runs Unit tests.'
    
    //testLogging.showStandardStreams = true
    
    testLogging {
      events "passed", "skipped", "failed"
    }
    
    useJUnitPlatform {
      includeTags('unit')
    }
    
  }
  
  task tofix(type: Test) {
    description = 'Runs tofix tests.'
    
    testLogging.showStandardStreams = true
    
    testLogging {
      events "passed", "skipped", "failed"
    }
    
    useJUnitPlatform {
      includeTags('tofix')
    }
    
  }
  
  task testJar(type: Jar) {
    archiveClassifier = 'tests'
    from sourceSets.test.output
  }
  
  configurations {
    tests
    published.extendsFrom tests, archives
  }
  
  artifacts {
    tests testJar
  }
  
  
  sourceSets {
    main {
      resources {
        srcDirs += ['src/main/java']
        //include  '**/*.groovy'
        //exclude  ['**/~$*', '**/*.java']
        exclude  '**/*.java'
      }
    }
  }
}