package cloud.datatp.fforwarder.sales.partner.plugin;

import cloud.datatp.fforwarder.core.template.CRMUserRoleLogic;
import cloud.datatp.fforwarder.sales.partner.CustomerLeadsLogic;
import cloud.datatp.fforwarder.sales.partner.entity.CustomerLeads;
import cloud.datatp.fforwarder.sales.partner.entity.PartnerEventHistory;
import lombok.extern.slf4j.Slf4j;
import net.datatp.security.client.ClientContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class CustomerLeadHistoryServicePlugin extends CustomerLeadServicePlugin {

  public CustomerLeadHistoryServicePlugin() {
    super("customer-lead-tracking-history");
  }

  @Autowired
  private CRMUserRoleLogic crmUserRoleLogic;

  @Autowired
  CustomerLeadsLogic customerLeadsLogic;

  @Override
  public void onPreSave(ClientContext client, CustomerLeads lead, boolean isNew) {
  }

  @Override
  public void onPostSave(ClientContext client, CustomerLeads lead, boolean isNew) {
    if (isNew) {
      PartnerEventHistory eventHistory = PartnerEventHistory.leadCreator(lead);
      customerLeadsLogic.savePartnerEventHistory(client, eventHistory);
    }
  }

}