package cloud.datatp.fforwarder.sales.partner;

import cloud.datatp.fforwarder.core.db.CRMDaoService;
import cloud.datatp.fforwarder.core.partner.CRMPartnerLogic;
import cloud.datatp.fforwarder.core.partner.entity.CRMPartner;
import cloud.datatp.fforwarder.core.template.CRMUserRoleLogic;
import cloud.datatp.fforwarder.core.template.entity.CrmUserRole;
import cloud.datatp.fforwarder.sales.integration.BFSOneCRMLogic;
import cloud.datatp.fforwarder.sales.partner.entity.CustomerLeads;
import cloud.datatp.fforwarder.sales.partner.entity.LeadStatus;
import cloud.datatp.fforwarder.sales.partner.entity.PartnerEventHistory;
import cloud.datatp.fforwarder.sales.partner.plugin.CustomerLeadServicePlugin;
import cloud.datatp.fforwarder.sales.partner.repository.CustomerLeadsRepository;
import cloud.datatp.fforwarder.sales.partner.repository.PartnerEventHistoryRepository;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.company.CompanyConfigLogic;
import net.datatp.module.company.CompanyLogic;
import net.datatp.module.core.security.SecurityLogic;
import net.datatp.module.core.security.entity.AppPermission;
import net.datatp.module.data.db.ExternalDataSourceManager;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.data.db.util.DBConnectionUtil;
import net.datatp.module.data.db.util.DeleteGraphBuilder;
import net.datatp.security.client.ClientContext;
import net.datatp.security.client.DataScope;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.StringUtil;
import net.datatp.util.text.TokenUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Getter
@Component
public class CustomerLeadsLogic extends CRMDaoService {

  @Autowired
  private CustomerLeadsRepository leadsRepo;

  @Autowired
  private SecurityLogic securityLogic;

  @Autowired
  private CRMUserRoleLogic crmUserRoleLogic;

  @Autowired
  private PartnerEventHistoryRepository transactionRepo;

  @Autowired
  private ExternalDataSourceManager dataSourceManager;

  @Autowired
  private CompanyConfigLogic companyConfigLogic;

  @Autowired
  private CompanyLogic companyLogic;

  @Autowired
  private CRMPartnerLogic bfsOnePartnerLogic;

  @Autowired
  private BFSOneCRMLogic bfsOneCRMLogic;

  @Autowired(required = false)
  List<CustomerLeadServicePlugin> plugins = new ArrayList<>();

  public CustomerLeads getById(ClientContext client, ICompany company, Long id) {
    return leadsRepo.findById(id).get();
  }

  public CustomerLeads saveCustomerLead(ClientContext client, CustomerLeads lead) {
    final boolean isNew = lead.isNew();

    if (!lead.isAgentApproach() && isNew) {
      MapObject result = this.ensureValidCustomerLeadByTaxCode(client, lead.getTaxCode());
      if (result != null) throw new RuntimeException("Lead already exists with tax code: " + lead.getTaxCode());
    }

    if (isNew && StringUtil.isEmpty(lead.getCode())) {
      if (lead.isAgentApproach()) {
        String code = "AGP" + TokenUtil.DATE_TIME_ID.nextId();
        lead.setCode(code);
      } else {
        String code = "LEA" + TokenUtil.DATE_TIME_ID.nextId();
        lead.setCode(code);
      }
    }

    if (lead.isAgentApproach() && StringUtil.isEmpty(lead.getTaxCode())) {
      lead.setTaxCode(lead.getCode());
    }

    if (lead.getAccountCreatorId() == null) {
      CrmUserRole creator = crmUserRoleLogic.getByAccountId(client, client.getAccountId());
      Objects.assertNotNull(creator, "CRM User Role not found for account id = " + client.getAccountId());
      lead.setAccountCreatorId(creator.getAccountId());
      lead.setAccountCreatorLabel(creator.getFullName());
    }

    if (lead.getSalemanAccountId() == null || lead.getSalemanCompanyId() == null) {
      CrmUserRole crmUserRole = crmUserRoleLogic.getByAccountId(client, client.getAccountId());
      Objects.assertNotNull(crmUserRole, "CRM User Role not found for account id = " + client.getAccountId());
      lead.setSalemanAccountId(crmUserRole.getAccountId());
      lead.setSalemanLabel(crmUserRole.getFullName());
      lead.setCompanyId(crmUserRole.getCompanyBranchId());
    }

    for (CustomerLeadServicePlugin plugin : plugins) {
      plugin.onPreSave(client, lead, isNew);
    }

    lead.set(client, client.getCompanyId());
    CustomerLeads saved = leadsRepo.save(lead);

    for (CustomerLeadServicePlugin plugin : plugins) {
      plugin.onPreSave(client, saved, isNew);
    }

    return saved;
  }

  public List<MapObject> saveCustomerLeadRecords(ClientContext client, ICompany company, List<MapObject> modified) {
    if (Collections.isNotEmpty(modified)) {
      for (MapObject sel : modified) {
        final Long id = sel.getLong("id", null);
        CustomerLeads lead = new CustomerLeads();
        if (id != null) {
          lead = getById(client, company, id);
          Objects.assertNotNull(lead, "Customer Lead not found: id = " + id);
        }
        lead = lead.computeFromMapObject(sel);
        CustomerLeads updated = saveCustomerLead(client, lead);
        sel.put("id", updated.getId());
      }
    }
    return modified;
  }

  public List<CustomerLeads> findCustomerLeadByTaxCode(ClientContext client, String taxCode) {
    return leadsRepo.findByTaxCode(taxCode);
  }

  public List<CustomerLeads> findCustomerLeadByTaxCodeOrName(ClientContext client, String pattern) {
    return leadsRepo.findByTaxCodeOrName(pattern);
  }

  public MapObject ensureValidCustomerLeadByTaxCode(ClientContext client, String taxCode) {
    List<CustomerLeads> leads = leadsRepo.findByTaxCode(taxCode);
    boolean match = false;
    if (Collections.isNotEmpty(leads)) {
      CustomerLeads first = LeadStatus.findFirstNonRecyclableLeads(leads);
      if (first != null) {
        match = true;
        MapObject result = new MapObject();
        result.put("saleManLabel", first.getSalemanLabel());
        result.put("status", first.getStatus());
        return result;
      }
    }

    /*
    List<SqlMapRecord> partners = bfsOnePartnerLogic.checkBFSOnePartnerByTaxCode(client, company, taxCode);
    if (Collections.isNotEmpty(partners)) {
      MapObject result = new MapObject();
      result.put("saleManLabel", partners.get(0).get("saleManLabel"));
      result.put("status", "CONVERTED");
      return result;
    }
     */

    return null;
  }

  public CRMPartner convertToBFSOnePartner(ClientContext client, Long id) {
    CustomerLeads lead = leadsRepo.findById(id).get();
    lead.setStatus(LeadStatus.CONVERTED);
    saveCustomerLead(client, lead);
    Objects.assertNotNull(lead, "Lead not found: id = " + id);
    return lead.convertToBFSOnePartner();
  }

  public List<CustomerLeads> reassignLeadToSaleman(ClientContext client, ICompany company, List<Long> leadIds, Long salemanAccountId) {
    List<CustomerLeads> leads = leadsRepo.findByIds(leadIds);
    Objects.assertNotNull(salemanAccountId, "Saleman account id is required");
    CrmUserRole crmUserRole = crmUserRoleLogic.getByAccountId(client, salemanAccountId);
    for (CustomerLeads lead : leads) {
      String oldLeadCode = lead.getCode();
      bfsOneCRMLogic.deleteLead(client, lead);
      String previousSaleman = lead.getSalemanLabel();
      lead.withSaleman(crmUserRole);
      lead.setDate(new Date());
      lead.setBfsoneLeadCode(null);
      CustomerLeads saved = saveCustomerLead(client, lead);
      PartnerEventHistory log = new PartnerEventHistory(saved);
      log.setTransactionDate(new Date());
      log.setType("REASSIGN");
      log.setSalemanAccountId(salemanAccountId);
      log.setSalemanLabel(crmUserRole.getFullName());
      log.setLabel("Lead Transfer");
      log.setReferencePartnerId(saved.getId());
      log.setReferencePartnerType(CustomerLeads.TABLE_NAME);
      log.setDescription(String.format("Transfer lead from %s to %s, old code = %s, new code = %s", previousSaleman, crmUserRole.getFullName(), oldLeadCode, saved.getCode()));
      savePartnerEventHistory(client, log);
    }
    return leads;
  }

  public CustomerLeads getCustomerLeadById(ClientContext client, ICompany company, Long id) {
    return leadsRepo.getById(id);
  }

  public CustomerLeads getCustomerLeadByCode(ClientContext client, String code) {
    return leadsRepo.getByCode(code);
  }

  public List<CustomerLeads> findByCompany(ClientContext client, ICompany company) {
    return leadsRepo.findByCompanyId(company.getId());
  }

  public List<SqlMapRecord> searchCustomerLeads(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    AppPermission permission = securityLogic.getAppPermission(client, company.getId(), "logistics", "user-logistics-sales");
    if (permission == null) return new ArrayList<>();

    sqlParams.addParam("companyId", company.getId());
    sqlParams.addParam("accessAccountId", client.getAccountId());

    try {
      DataScope dataScope = permission.getDataScope();
      if (DataScope.Group == dataScope) {
        String scriptDir = appEnv.addonPath("core", "groovy");
        String scriptFile = "net/datatp/module/hr/groovy/EmployeeSql.groovy";
        List<SqlMapRecord> accountIds = searchPlatformDbRecords(client, scriptDir, scriptFile, "FindEmployeeIdsByManagerId", sqlParams);
        List<Long> participantAccountIds = accountIds.stream()
          .map(record -> record.getLong("accountId", null))
          .collect(Collectors.toList());
        sqlParams.addParam("participantAccountIds", participantAccountIds);
      }

      String scriptDir = appEnv.addonPath("logistics", "groovy");
      String scriptFile = "cloud/datatp/fforwarder/sales/groovy/CustomerLeadsSql.groovy";
      return searchDbRecords(client, scriptDir, scriptFile, "SearchCustomerLeads", sqlParams);
    } catch (Exception e) {
      log.error("Error when search leads", e);
      return new ArrayList<>();
    }

  }

  public Boolean deleteCustomerLeads(ClientContext client, ICompany company, List<Long> ids) {
    List<CustomerLeads> leads = leadsRepo.findByIds(ids);
    for (CustomerLeads lead : leads) {
      bfsOneCRMLogic.deleteLead(client, lead);
    }
    leadsRepo.deleteAll(leads);
    return true;
  }

  // ----------------- Lead Log -----------------
  public PartnerEventHistory savePartnerEventHistory(ClientContext client, PartnerEventHistory partner) {
    partner.set(client, client.getCompanyId());
    return transactionRepo.save(partner);
  }

  public List<MapObject> savePartnerEventHistoryRecords(ClientContext client, ICompany company, List<MapObject> requests) {
    if (net.datatp.util.ds.Collections.isNotEmpty(requests)) {
      for (MapObject req : requests) {
        final Long id = req.getLong("id", null);
        PartnerEventHistory partner = new PartnerEventHistory();
        if (id != null) {
          partner = getPartnerEventHistoryById(client, company, id);
          Objects.assertNotNull(partner, "Customer Service Partner Obligation not found: id = " + id);
        }
        partner = partner.computeFromMapObject(req);
        PartnerEventHistory updated = savePartnerEventHistory(client, partner);
        req.put("id", updated.getId());
      }
    }
    return requests;
  }

  public List<PartnerEventHistory> findPartnerEventBySaleMan(ClientContext client, ICompany company, Long saleManAccountId) {
    return transactionRepo.findBySaleManAccountId(saleManAccountId);
  }

  public List<SqlMapRecord> searchPartnerEventHistories(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/sales/groovy/PartnerEventHistorySql.groovy";
    return searchDbRecords(client, scriptDir, scriptFile, "SearchPartnerEventHistory", sqlParams);
  }

  public List<SqlMapRecord> searchPartnerEventHistoryReports(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    AppPermission permission = securityLogic.getAppPermission(client, company.getId(), "logistics", "user-logistics-sales");
    if (permission == null) return new ArrayList<>();

    sqlParams.addParam("companyId", company.getId());
    sqlParams.addParam("accessAccountId", client.getAccountId());

    DataScope dataScope = permission.getDataScope();
    if (DataScope.Group == dataScope) {
      String scriptDir = appEnv.addonPath("core", "groovy");
      String scriptFile = "net/datatp/module/hr/groovy/EmployeeSql.groovy";
      List<SqlMapRecord> accountIds = searchPlatformDbRecords(client, scriptDir, scriptFile, "FindEmployeeIdsByManagerId", sqlParams);
      List<Long> participantAccountIds = accountIds.stream()
        .map(record -> record.getLong("accountId", null))
        .collect(Collectors.toList());
      sqlParams.addParam("participantAccountIds", participantAccountIds);
    }

    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/sales/groovy/PartnerEventHistorySql.groovy";
    return searchDbRecords(client, scriptDir, scriptFile, "SearchPartnerEventHistoryReport", sqlParams);
  }

  public List<SqlMapRecord> searchUserCustomers(ClientContext client, SqlQueryParams sqlParams) {
    sqlParams.addParam("accessAccountId", client.getAccountId());
    if (!sqlParams.hasParam("space")) sqlParams.addParam("space", "User");
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/sales/groovy/UserCRMPartnerSql.groovy";

    return searchDbRecords(client, scriptDir, scriptFile, "SearchUserCRMPartner", sqlParams);
  }

  public int deletePartnerEventHistories(ClientContext client, ICompany company, List<Long> ids) {
    DBConnectionUtil connectionUtil = getDBConnectionUtil();
    DeleteGraphBuilder deleteGraphBuilder = new DeleteGraphBuilder(connectionUtil, company.getId(), PartnerEventHistory.class, ids);
    final int count = deleteGraphBuilder.runDelete();
    connectionUtil.commit();
    connectionUtil.close();
    return count;
  }

  public PartnerEventHistory getPartnerEventHistoryById(ClientContext client, ICompany company, Long id) {
    return transactionRepo.findById(id).get();
  }

}