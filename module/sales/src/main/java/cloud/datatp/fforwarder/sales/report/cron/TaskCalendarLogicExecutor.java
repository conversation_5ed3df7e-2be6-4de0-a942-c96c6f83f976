package cloud.datatp.fforwarder.sales.report.cron;

import cloud.datatp.fforwarder.sales.project.TaskCalendarLogic;
import cloud.datatp.fforwarder.sales.project.entity.TaskCalendar;
import java.util.Date;
import java.util.List;
import net.datatp.lib.executable.ExecutableContext;
import net.datatp.lib.executable.ExecutableUnit;
import net.datatp.lib.executable.Executor;
import net.datatp.module.backend.Notification;
import net.datatp.module.company.CompanyLogic;
import net.datatp.module.company.entity.Company;
import net.datatp.security.client.ClientContext;
import org.springframework.context.ApplicationContext;


public class TaskCalendarLogicExecutor extends Executor {

  public static class UpdateStatusTaskCalendar extends ExecutableUnit {
    @Override
    public Notification execute(ApplicationContext appCtx, ExecutableContext ctx) {
      Notification notification = new Notification(Notification.Type.info, "Update Sale Task Calendar", "");
      ClientContext client = ctx.getClient();
      CompanyLogic companyLogic = appCtx.getBean(CompanyLogic.class);
      TaskCalendarLogic saleTaskReportLogic = appCtx.getBean(TaskCalendarLogic.class);
      Date startTime = ctx.getParams().getDate("startTime", new Date());
      Date endTime = ctx.getParams().getDate("endTime", new Date());
      int count = 0;
      List<TaskCalendar> tasks = saleTaskReportLogic.findTasksByCreatedTimeRange(client, startTime, endTime);
      for (TaskCalendar task : tasks) {
        Company company = companyLogic.getCompany(client, task.getCompanyId());
        task.setStatus(TaskCalendar.SalesTaskStatus.COMPLETED);
        saleTaskReportLogic.saveSalesDailyTask(client, company, task);
        count++;
      }
      return notification.withMessage("Updated " + count + "Status Task Calendar.");
    }
  }

  public TaskCalendarLogicExecutor() {
    register(new UpdateStatusTaskCalendar());
  }
}