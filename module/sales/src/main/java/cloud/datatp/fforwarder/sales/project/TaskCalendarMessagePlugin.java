package cloud.datatp.fforwarder.sales.project;

import cloud.datatp.fforwarder.core.message.MailMessageProvider;
import cloud.datatp.fforwarder.core.message.MessageServicePlugin;
import cloud.datatp.fforwarder.core.message.entity.CRMMessageSystem;
import cloud.datatp.fforwarder.core.message.entity.MessageStatus;
import cloud.datatp.fforwarder.core.message.entity.MessageType;
import cloud.datatp.fforwarder.sales.common.TaskNotificationTemplate;
import cloud.datatp.fforwarder.sales.project.entity.TaskCalendar;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.communication.CommunicationMessageLogic;
import net.datatp.module.communication.entity.CommunicationAccount;
import net.datatp.module.company.CompanyLogic;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class TaskCalendarMessagePlugin extends MessageServicePlugin {
  public static final String PLUGIN_TYPE = "task-calendar-message";

  @Autowired
  private MailMessageProvider mailMessageProvider;

  @Autowired
  private CompanyLogic companyLogic;

  @Autowired
  private TaskCalendarLogic saleTaskReportLogic;

  @Autowired
  private CommunicationMessageLogic communicationMessageLogic;

  protected TaskCalendarMessagePlugin() {
    super(PLUGIN_TYPE);
  }

  public void onPostSend(ClientContext client, CRMMessageSystem message) {
  }

  public void onSendError(ClientContext client, CRMMessageSystem message, Exception error) throws Exception {
    if (message.getStatus() != MessageStatus.CANCELLED && message.getMessageType() == MessageType.ZALO) {
      Objects.assertNotNull(message.getReferenceType(), "Message has no reference type");
      Objects.assertNotNull(message.getReferenceId(), "Message has no reference id");
      if (!TaskCalendar.TABLE_NAME.equals(message.getReferenceType())) {
        throw new RuntimeException("Message reference type is not SalesDailyTask: " + message.getReferenceType());
      }

      TaskCalendar task= null;
      try {
        task = saleTaskReportLogic.getSalesDailyTaskById(client, null, message.getReferenceId());
      } catch (Exception e) {
        log.error("❌ Inquiry request [id={}] not found for message [id={}]", message.getReferenceId(), message.getId());
        return;
      }
      CommunicationAccount communicationAccount = communicationMessageLogic.getCommunicationAccount(client, task.getSalemanAccountId());
      CRMMessageSystem crmMessageSystem = new CRMMessageSystem();
      crmMessageSystem.setScheduledAt(task.getNotificationTime());
      crmMessageSystem.setPluginName(TaskCalendarMessagePlugin.PLUGIN_TYPE);
      crmMessageSystem.setReferenceId(task.getId());
      crmMessageSystem.setReferenceType(TaskCalendar.TABLE_NAME);
      crmMessageSystem.setMessageType(MessageType.MAIL);
      MapObject metadata = new MapObject();
      crmMessageSystem.setContent(TaskNotificationTemplate.buildMailTaskMessage(task));
      crmMessageSystem.setRecipients(Set.of(communicationAccount.getEmail()));
      List<String> ccList = Arrays.asList("<EMAIL>");

      metadata.put("subject", "CRM - Daily Task Notification");
      metadata.put("fromEmail", "<EMAIL>");
      metadata.put("ccList", ccList);
      crmMessageSystem.setMetadata(metadata);

      CRMMessageSystem mailMessage = saleTaskReportLogic.createCRMMessageSystem(client, task, MessageType.MAIL);

      mailMessageProvider.send(client, mailMessage);
    }
  }
}