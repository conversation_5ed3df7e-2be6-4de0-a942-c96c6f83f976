package cloud.datatp.fforwarder.sales.project;

import cloud.datatp.fforwarder.sales.project.entity.TaskCalendar;
import java.util.List;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.service.BaseComponent;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.MapObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component("TaskCalendarService")
public class TaskCalendarService extends BaseComponent {

  @Autowired
  private TaskCalendarLogic taskCalendarLogic;

  // ---------------------- Sales Daily Tasks ----------------------
  @Transactional(value = "crmTransactionManager", readOnly = true)
  public TaskCalendar getSalesDailyTaskById(ClientContext client, ICompany company, Long id) {
    return taskCalendarLogic.getSalesDailyTaskById(client, company, id);
  }

  @Transactional("crmTransactionManager")
  public TaskCalendar saveSalesDailyTask(ClientContext client, ICompany company, TaskCalendar task) {
    return taskCalendarLogic.saveSalesDailyTask(client, company, task);
  }

  @Transactional("crmTransactionManager")
  public TaskCalendar handleSalesDailyTask(ClientContext client, ICompany company, TaskCalendar task) {
    return taskCalendarLogic.handleSalesDailyTask(client, company, task);
  }

  @Transactional("crmTransactionManager")
  public List<MapObject> saveSaleDailyTaskRecords(ClientContext client, ICompany company, List<MapObject> records) {
    return taskCalendarLogic.saveSaleDailyTaskRecords(client, company, records);
  }

  @Transactional(value = "crmTransactionManager", readOnly = true)
  public List<SqlMapRecord> searchSalesDailyTasks(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    return taskCalendarLogic.searchSalesDailyTasks(client, company, sqlParams);
  }

  @Transactional(value = "crmTransactionManager", readOnly = true)
  public List<SqlMapRecord> searchSalesDailyTaskReport(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    return taskCalendarLogic.searchSalesDailyTaskReport(client, company, sqlParams);
  }

  @Transactional("crmTransactionManager")
  public boolean deleteSalesDailyTaskByIds(ClientContext client, ICompany company, List<Long> ids) {
    return taskCalendarLogic.deleteSalesDailyTaskByIds(client, company, ids);
  }

  @Transactional("crmTransactionManager")
  public boolean changeDailyTaskStorageState(ClientContext client, ICompany company, ChangeStorageStateRequest req) {
    return taskCalendarLogic.changeDailyTaskStorageState(client, company, req);
  }

}