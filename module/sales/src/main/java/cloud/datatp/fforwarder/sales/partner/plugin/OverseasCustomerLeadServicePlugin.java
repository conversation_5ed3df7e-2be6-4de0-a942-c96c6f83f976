package cloud.datatp.fforwarder.sales.partner.plugin;

import cloud.datatp.fforwarder.core.template.CRMUserRoleLogic;
import cloud.datatp.fforwarder.core.template.entity.CrmUserRole;
import cloud.datatp.fforwarder.sales.partner.CustomerLeadsLogic;
import cloud.datatp.fforwarder.sales.partner.entity.CustomerLeads;
import lombok.extern.slf4j.Slf4j;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class OverseasCustomerLeadServicePlugin extends CustomerLeadServicePlugin {

  public OverseasCustomerLeadServicePlugin() {
    super("overseas-customer-lead");
  }

  @Autowired
  private CRMUserRoleLogic crmUserRoleLogic;

  @Autowired
  CustomerLeadsLogic customerLeadsLogic;

  @Override
  public void onPreSave(ClientContext client, CustomerLeads lead, boolean isNew) {
    CrmUserRole saleman = crmUserRoleLogic.getByAccountId(client, client.getAccountId());
    Objects.assertNotNull(saleman, "Creator Employee {} is not found", client.getRemoteUser());

    if(saleman.isOverseas()) {
      log.info("Overseas-partner: onPreSave called for customer lead: {}, isNew: {}", lead.getId(), isNew);

    }

  }

  @Override
  public void onPostSave(ClientContext client, CustomerLeads lead, boolean isNew) {
    CrmUserRole saleman = crmUserRoleLogic.getByAccountId(client, client.getAccountId());
    Objects.assertNotNull(saleman, "Creator Employee {} is not found", client.getRemoteUser());

    if(saleman.isOverseas()) {
      log.info("Overseas-partner: onPostSave called for customer lead: {}, isNew: {}", lead.getId(), isNew);

    }

  }

}