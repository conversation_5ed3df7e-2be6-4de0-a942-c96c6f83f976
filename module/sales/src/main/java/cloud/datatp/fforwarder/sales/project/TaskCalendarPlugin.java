package cloud.datatp.fforwarder.sales.project;

import cloud.datatp.fforwarder.sales.project.entity.TaskCalendar;
import net.datatp.module.backend.Notification;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.wfms.EntityTaskPlugin;
import net.datatp.module.wfms.entity.EntityTask;
import net.datatp.module.wfms.entity.EntityTaskStatus;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.MapObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component("TaskCalendarPlugin")
public class TaskCalendarPlugin extends EntityTaskPlugin<TaskCalendar>{
  
  @Autowired
  private TaskCalendarLogic logic;
  
  @Transactional
  public Notification handle(ClientContext client, ICompany company, TaskCalendar entity, EntityTask task, EntityTaskStatus status) {
    return doPersist(client, company, entity, task, status);
  }
  
  @Transactional
  public TaskCalendar saveEntity(ClientContext client, ICompany company, TaskCalendar entity) {
    return doSaveEntity(client, company, entity);
  }
  
  @Override
  protected TaskCalendar doSaveEntity(ClientContext client, ICompany company, TaskCalendar entity) {
    return logic.saveSalesDailyTask(client, company, entity);
  }

  protected void computeEntityRef(EntityTask task, TaskCalendar entity) {
    task.setEntityRefType(TaskCalendar.TABLE_NAME);
    task.setEntityRefId(entity.getId());
    
    MapObject entityRefData = buildEntityRefData(entity);
    task.setEntityRefData(entityRefData);
  }
  
  private MapObject buildEntityRefData(TaskCalendar entity) {
    MapObject data = new MapObject();
    data.put("taskType", entity.getTaskType());
    data.put("status", entity.getStatus());
    data.put("referenceId", entity.getReferenceId());
    data.put("referenceType", entity.getReferenceType());
    return data;
  }
}