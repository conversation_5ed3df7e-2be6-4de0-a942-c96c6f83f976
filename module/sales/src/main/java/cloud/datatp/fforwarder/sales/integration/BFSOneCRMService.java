package cloud.datatp.fforwarder.sales.integration;

import java.util.List;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.service.BaseComponent;
import net.datatp.security.client.ClientContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component("BFSOneCRMService")
public class BFSOneCRMService extends BaseComponent {

  @Autowired
  private BFSOneCRMLogic bfsOneCRMLogic;

  @Transactional
  public List<SqlMapRecord> findBookingLocalByHawb(ClientContext client, String hawb) {
    return bfsOneCRMLogic.findBookingLocalByHawb(client, hawb);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> checkPartnerExistInSystem(ClientContext client, String searchPattern) {
    return bfsOneCRMLogic.checkPartnerExistInSystem(client, searchPattern);
  }

}