package cloud.datatp.fforwarder.sales.partner;

import cloud.datatp.fforwarder.core.partner.entity.CRMPartner;
import cloud.datatp.fforwarder.sales.partner.entity.CustomerLeads;
import cloud.datatp.fforwarder.sales.partner.entity.PartnerEventHistory;
import java.util.List;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.service.BaseComponent;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.MapObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("CustomerLeadsService")
public class CustomerLeadsService extends BaseComponent {

  @Autowired
  private CustomerLeadsLogic leadsLogic;

  @Transactional(readOnly = true)
  public CustomerLeads getCustomerLeadById(ClientContext client, ICompany company, Long id) {
    return leadsLogic.getById(client, company, id);
  }

  @Transactional(readOnly = true)
  public MapObject ensureValidCustomerLeadByTaxCode(ClientContext client, String taxCode) {
    return leadsLogic.ensureValidCustomerLeadByTaxCode(client, taxCode);
  }

  @Transactional(readOnly = true)
  public CRMPartner convertToBFSOnePartner(ClientContext client, Long id) {
    return leadsLogic.convertToBFSOnePartner(client, id);
  }

  @Transactional(readOnly = true)
  public CustomerLeads getCustomerLeadByCode(ClientContext client, String code) {
    return leadsLogic.getCustomerLeadByCode(client, code);
  }

  @Transactional(readOnly = true)
  public List<CustomerLeads> findByCompany(ClientContext client, ICompany company) {
    return leadsLogic.findByCompany(client, company);
  }

  @Transactional
  public List<CustomerLeads> reassignLeadToSaleman(ClientContext client, ICompany company, List<Long> leadIds, Long salemanAccountId) {
    return leadsLogic.reassignLeadToSaleman(client, company, leadIds, salemanAccountId);
  }

  @Transactional
  public CustomerLeads saveCustomerLead(ClientContext client, CustomerLeads lead) {
    return leadsLogic.saveCustomerLead(client, lead);
  }

  @Transactional
  public List<MapObject> saveCustomerLeadRecords(ClientContext client, ICompany company, List<MapObject> modified) {
    return leadsLogic.saveCustomerLeadRecords(client, company, modified);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchCustomerLeads(ClientContext client, ICompany company, SqlQueryParams params) {
    return leadsLogic.searchCustomerLeads(client, company, params);
  }

  @Transactional
  public Boolean deleteCustomerLeads(ClientContext client, ICompany company, List<Long> ids) {
    return leadsLogic.deleteCustomerLeads(client, company, ids);
  }

  // -------------------------- Log --------------------------

  @Transactional
  public PartnerEventHistory savePartnerEventHistory(ClientContext client, PartnerEventHistory partner) {
    return leadsLogic.savePartnerEventHistory(client, partner);
  }

  @Transactional
  public List<MapObject> savePartnerEventHistoryRecords(ClientContext client, ICompany company, List<MapObject> requests) {
    return leadsLogic.savePartnerEventHistoryRecords(client, company, requests);
  }

  @Transactional(readOnly = true)
  public List<PartnerEventHistory> findPartnerEventBySaleMan(ClientContext client, ICompany company, Long saleManAccountId) {
    return leadsLogic.findPartnerEventBySaleMan(client, company, saleManAccountId);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchPartnerEventHistories(ClientContext client, ICompany company, SqlQueryParams params) {
    return leadsLogic.searchPartnerEventHistories(client, company, params);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchPartnerEventHistoryReports(ClientContext client, ICompany company, SqlQueryParams params) {
    return leadsLogic.searchPartnerEventHistoryReports(client, company, params);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchUserCustomers(ClientContext client, SqlQueryParams params) {
    return leadsLogic.searchUserCustomers(client, params);
  }

  @Transactional
  public int deletePartnerEventHistories(ClientContext client, ICompany company, List<Long> ids) {
    return leadsLogic.deletePartnerEventHistories(client, company, ids);
  }

  @Transactional(readOnly = true)
  public PartnerEventHistory getPartnerEventHistoryById(ClientContext client, ICompany company, Long id) {
    return leadsLogic.getPartnerEventHistoryById(client, company, id);
  }

}