package cloud.datatp.fforwarder.sales.partner;

import cloud.datatp.fforwarder.core.db.CRMDaoService;
import cloud.datatp.fforwarder.core.db.CRMSqlQueryUnitManager;
import cloud.datatp.fforwarder.core.partner.CRMPartnerLogic;
import cloud.datatp.fforwarder.core.template.CRMUserRoleLogic;
import cloud.datatp.fforwarder.core.template.entity.CrmUserRole;
import cloud.datatp.fforwarder.sales.partner.dto.AgentTransactionReportModel;
import cloud.datatp.fforwarder.sales.partner.entity.SalemanKeyAccountReport;
import cloud.datatp.fforwarder.sales.partner.repository.SalemanKeyAccountReportRepository;
import cloud.datatp.fforwarder.sales.report.SaleReportMetricsHelper;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.company.CompanyConfigLogic;
import net.datatp.module.company.CompanyLogic;
import net.datatp.module.core.security.SecurityLogic;
import net.datatp.module.core.security.entity.AppPermission;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.SqlSelectView;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.data.db.util.DBConnectionUtil;
import net.datatp.module.data.db.util.DeleteGraphBuilder;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Getter
@Component
public class PartnerReportLogic extends CRMDaoService {

  @Autowired
  private SecurityLogic securityLogic;

  @Autowired
  private CompanyLogic companyLogic;

  @Autowired
  private CRMUserRoleLogic crmUserRoleLogic;

  @Autowired
  private CompanyConfigLogic companyConfigLogic;

  @Autowired
  private CRMPartnerLogic bfsonePartnerLogic;

  @Autowired
  private SalemanKeyAccountReportRepository salemanKeyAccountReportRepo;

  public List<SqlMapRecord> searchVolumeSalemanKeyAccountReport(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    Long salemanAccountId = sqlParams.getLong("salemanAccountId");
    if (salemanAccountId == null) salemanAccountId = client.getAccountId();

    CrmUserRole crmUserRole = crmUserRoleLogic.getByAccountId(client, salemanAccountId);
    Objects.assertNotNull(crmUserRole, "CRM User Role not found for accountId = " + salemanAccountId);
    if (StringUtil.isEmpty(crmUserRole.getBfsoneCode())) {
      log.warn("CRM User Role BFSOne Code is not found: accountId = {}", salemanAccountId);
      return new ArrayList<>();
    }

    sqlParams.addParam("salemanContactId", crmUserRole.getBfsoneCode().trim());

    try {
      String scriptDir = appEnv.addonPath("logistics", "groovy");
      String scriptFile = "cloud/datatp/fforwarder/sales/groovy/report/KeyAccountReportSql.groovy";
      String scriptName = "KeyAccountVolumeReport";
      CRMSqlQueryUnitManager.QueryContext queryContext = sqlQueryUnitManager.create(scriptDir, scriptFile, scriptName);
      SqlSelectView view = queryContext.createSqlSelectView(legacyDataSource, sqlParams);
      List<SqlMapRecord> records = view.renameColumWithJavaConvention().getSqlMapRecords();
      log.info("Retrieved {} records", records.size());
      return records;
    } catch (Exception e) {
      log.error("Error when search Saleman Key Account Report", e);
      return new ArrayList<>();
    }
  }

  public SalemanKeyAccountReport getLatestReportBySaleman(ClientContext client, ICompany company) {
    return salemanKeyAccountReportRepo.getLatestReportBySaleman(company.getId(), client.getAccountId());
  }

  public List<SalemanKeyAccountReport> findSalemanKeyAccountReports(ClientContext client, ICompany company) {
    return salemanKeyAccountReportRepo.findAll(company.getId());
  }

  public List<SqlMapRecord> searchSalemanKeyAccountReports(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    AppPermission permission = securityLogic.getAppPermission(client, company.getId(), "logistics", "user-logistics-sales");
    if (permission == null) return new ArrayList<>();
    sqlParams.addParam("companyId", company.getId());
    sqlParams.addParam("accessAccountId", company.getId());
    List<Long> salemanAccountIds = new ArrayList<>();

    if (permission.isGroupScope()) {
      String coreScriptDir = appEnv.addonPath("core", "groovy");
      String coreScriptFile = "net/datatp/module/hr/groovy/EmployeeSql.groovy";
      List<SqlMapRecord> accountIds = searchPlatformDbRecords(client, coreScriptDir, coreScriptFile, "FindEmployeeIdsByManagerId", sqlParams);
      salemanAccountIds = accountIds.stream()
        .map(record -> record.getLong("accountId", null))
        .collect(Collectors.toList());
    } else if (permission.isOwnerScope()) {
      salemanAccountIds.add(client.getAccountId());
    }

    sqlParams.addParam("salemanAccountIds", salemanAccountIds);
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/sales/groovy/report/KeyAccountReportSql.groovy";
    return searchDbRecords(client, scriptDir, scriptFile, "SearchKeyAccountVolumeReport", sqlParams);
  }

  public SalemanKeyAccountReport saveSalemanKeyAccountReport(ClientContext client, ICompany company, SalemanKeyAccountReport report) {
    report.set(client, company);
    if (report.isNew()) {
      //DAN_20053005-1
      String code = report.generateCodePrefix(client.getAccountId());
      String[] split = code.split("-");
      String prefix = split[0];
      List<SalemanKeyAccountReport> reportsInDb = salemanKeyAccountReportRepo.findByCodePrefix(company.getId(), prefix);
      if (!reportsInDb.isEmpty()) {
        SalemanKeyAccountReport existingReport = reportsInDb.get(0);
        //DAN_20053005-2
        String lastRecordCode = existingReport.getCode();
        String[] lastRecordCodeSplit = lastRecordCode.split("-");
        try {
          int codeIdx = Integer.parseInt(lastRecordCodeSplit[1]);
          codeIdx++;
          code = prefix + "-" + codeIdx;
        } catch (NumberFormatException e) {
          log.warn("Last record code is not valid: {}", lastRecordCode);
        }
      }

      report.setSubmittedDate(new Date());
      report.setCode(code);
      report.set(client, company);
    }
    return salemanKeyAccountReportRepo.save(report);
  }

  public int deleteSalemanKeyAccountReports(ClientContext client, ICompany company, List<Long> targetIds) {
    DBConnectionUtil connectionUtil = getDBConnectionUtil();
    DeleteGraphBuilder deleteGraphBuilder = new DeleteGraphBuilder(connectionUtil, company.getId(), SalemanKeyAccountReport.class, targetIds);
    final int count = deleteGraphBuilder.runDelete();
    connectionUtil.commit();
    connectionUtil.close();
    return count;
  }

  public SalemanKeyAccountReport getSalemanKeyAccountReportById(ClientContext client, ICompany company, Long id) {
    return salemanKeyAccountReportRepo.findById(id).get();
  }

  private List<SqlMapRecord> searchIntegratedPartners(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    if (!sqlParams.hasParam("space")) {
      sqlParams.addParam("space", "User");
    }
    sqlParams.addParam("companyId", client.getCompanyId());
    sqlParams.addParam("accessAccountId", client.getAccountId());

    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/core/groovy/SearchCRMPartnerSql.groovy";
    String scriptNamePartnerCode = "SearchAuthorizePartnerCode";

    List<SqlMapRecord> authorizePartnerCodes = searchDbRecords(client, scriptDir, scriptFile, scriptNamePartnerCode, sqlParams);
    List<String> partnerCodes = authorizePartnerCodes.stream().map(r -> r.getString("partnerCode")).collect(Collectors.toList());


    String scriptFile2 = "cloud/datatp/fforwarder/sales/groovy/SalesCustomerReportSql.groovy";
    String scriptNameIntegratedPartner = "SearchIntegratedPartner";
    sqlParams.addParam("partnerCodes", partnerCodes);
    CRMSqlQueryUnitManager.QueryContext queryContextIntegratedPartner = sqlQueryUnitManager.create(scriptDir, scriptFile2, scriptNameIntegratedPartner);
    SqlSelectView viewIntegratedPartner = queryContextIntegratedPartner.createSqlSelectView(legacyDataSource, sqlParams);
    List<SqlMapRecord> records = viewIntegratedPartner.renameColumWithJavaConvention().getSqlMapRecords();
    log.info("Retrieved {} records", records.size());

    return records;
  }

  public List<SqlMapRecord> searchCustomerReports(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    List<SqlMapRecord> holder = new ArrayList<>();

    List<SqlMapRecord> integratedPartners = searchIntegratedPartners(client, company, sqlParams);
    if (integratedPartners.isEmpty()) return holder;

    Map<String, List<SqlMapRecord>> groupedByPartner = integratedPartners.stream()
      .collect(Collectors.groupingBy(record -> record.getString("partnerCode", "")));

    for (String partnerCode : groupedByPartner.keySet()) {
      List<SqlMapRecord> records = groupedByPartner.get(partnerCode);
      if (net.datatp.util.ds.Collections.isNotEmpty(records)) {
        SqlMapRecord lastedRecord = records.get(0);
        lastedRecord.put("totalJobCount", 0);
        lastedRecord.put("lastTransactionDate", "");
        lastedRecord.put("lastTransactionID", "");
        lastedRecord.put("totalGw", 0.0);
        lastedRecord.put("totalCbm", 0.0);
        lastedRecord.put("totalContainer", "");

        String lastTransactionDate = lastedRecord.getString("jobDate", "").split("@")[0];
        String lastTransactionID = lastedRecord.getString("jobNo", "");
        lastedRecord.put("totalJobCount", records.size());
        lastedRecord.put("lastTransactionDate", lastTransactionDate);
        lastedRecord.put("lastTransactionID", lastTransactionID);
        SaleReportMetricsHelper.calculateAllTimeMetrics(lastedRecord, records);
        holder.add(lastedRecord);
      }
    }
    return holder;
  }

  public List<SqlMapRecord> searchAgentTransactionsReport(ClientContext client, SqlQueryParams sqlParams) {
    try {
      String scriptDir = appEnv.addonPath("logistics", "groovy");
      String scriptFile = "cloud/datatp/fforwarder/core/groovy/AgentTransactionSql.groovy";
      String scriptName = "SearchAgentTransactions";
      CRMSqlQueryUnitManager.QueryContext queryContext = sqlQueryUnitManager.create(scriptDir, scriptFile, scriptName);
      SqlSelectView view = queryContext.createSqlSelectView(legacyDataSource, sqlParams);
      return view.renameColumWithJavaConvention().getSqlMapRecords();
    } catch (Exception e) {
      log.error("Error when search Agent Transactions Report", e);
      return new ArrayList<>();
    }
  }

  public AgentTransactionReportModel reportAgentTransactionsGroupByAgent(ClientContext client, SqlQueryParams sqlParams) {
    try {
      String scriptDir = appEnv.addonPath("logistics", "groovy");
      String scriptFile = "cloud/datatp/fforwarder/core/groovy/AgentTransactionSql.groovy";
      String agentTransScriptName = "SearchAgentTransactionsGroupByAgent";

      CRMSqlQueryUnitManager.QueryContext agentTransQueryContext = sqlQueryUnitManager.create(scriptDir, scriptFile, agentTransScriptName);
      SqlSelectView agentTransWiew = agentTransQueryContext.createSqlSelectView(legacyDataSource, sqlParams);
      List<SqlMapRecord> agentTransactionRecords = agentTransWiew.renameColumWithJavaConvention().getSqlMapRecords();

      String companyGroupedScriptName = "SearchAgentTransactionsGroupByCompany";
      CRMSqlQueryUnitManager.QueryContext companyGroupedQueryContext = sqlQueryUnitManager.create(scriptDir, scriptFile, companyGroupedScriptName);
      SqlSelectView companyGroupedWiew = companyGroupedQueryContext.createSqlSelectView(legacyDataSource, sqlParams);
      List<SqlMapRecord> companyGroupedRecords = companyGroupedWiew.renameColumWithJavaConvention().getSqlMapRecords();

      AgentTransactionReportModel model = new AgentTransactionReportModel(agentTransactionRecords, companyGroupedRecords);
      return model;
    } catch (Exception e) {
      log.error("Error when search Agent Transactions Report Group By Agent", e);
      return new AgentTransactionReportModel();
    }
  }

  public AgentTransactionReportModel reportAgentTransactionsGroupByCountry(ClientContext client, SqlQueryParams sqlParams) {
    try {
      String scriptDir = appEnv.addonPath("logistics", "groovy");
      String scriptFile = "cloud/datatp/fforwarder/core/groovy/AgentTransactionSql.groovy";
      String scriptName = "SearchAgentTransactionsGroupByCountry";
      CRMSqlQueryUnitManager.QueryContext queryContext = sqlQueryUnitManager.create(scriptDir, scriptFile, scriptName);
      SqlSelectView view = queryContext.createSqlSelectView(legacyDataSource, sqlParams);
      List<SqlMapRecord> agentTransactionRecords = view.renameColumWithJavaConvention().getSqlMapRecords();
      return new AgentTransactionReportModel(agentTransactionRecords, new ArrayList<>());
    } catch (Exception e) {
      log.error("Error when search Agent Transactions Report Group By Country", e);
      return new AgentTransactionReportModel();
    }
  }

  public AgentTransactionReportModel reportAgentTransactionsGroupByContinent(ClientContext client, SqlQueryParams sqlParams) {
    try {
      String scriptDir = appEnv.addonPath("logistics", "groovy");
      String scriptFile = "cloud/datatp/fforwarder/core/groovy/AgentTransactionSql.groovy";
      String scriptName = "SearchAgentTransactionsGroupByContinent";
      CRMSqlQueryUnitManager.QueryContext queryContext = sqlQueryUnitManager.create(scriptDir, scriptFile, scriptName);
      SqlSelectView view = queryContext.createSqlSelectView(legacyDataSource, sqlParams);
      List<SqlMapRecord> agentTransactionRecords = view.renameColumWithJavaConvention().getSqlMapRecords();
      return new AgentTransactionReportModel(agentTransactionRecords, new ArrayList<>());
    } catch (Exception e) {
      log.error("Error when search Agent Transactions Report Group By Continent", e);
      return new AgentTransactionReportModel();
    }
  }

  public AgentTransactionReportModel reportAgentTransactionsGroupByNetwork(ClientContext client, SqlQueryParams sqlParams) {
    try {
      String scriptDir = appEnv.addonPath("logistics", "groovy");
      String scriptFile = "cloud/datatp/fforwarder/core/groovy/AgentTransactionSql.groovy";
      String scriptName = "SearchAgentTransactionsGroupByNetwork";
      CRMSqlQueryUnitManager.QueryContext queryContext = sqlQueryUnitManager.create(scriptDir, scriptFile, scriptName);
      SqlSelectView view = queryContext.createSqlSelectView(legacyDataSource, sqlParams);
      List<SqlMapRecord> agentTransactionRecords = view.renameColumWithJavaConvention().getSqlMapRecords();
      return new AgentTransactionReportModel(agentTransactionRecords, new ArrayList<>());
    } catch (Exception e) {
      log.error("Error when search Agent Transactions Report Group By Network", e);
      return new AgentTransactionReportModel();
    }
  }
}