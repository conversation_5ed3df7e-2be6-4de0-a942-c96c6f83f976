package cloud.datatp.fforwarder.sales.project;

import cloud.datatp.fforwarder.core.common.ClientPartnerType;
import cloud.datatp.fforwarder.core.db.CRMDaoService;
import cloud.datatp.fforwarder.core.message.CRMMessageLogic;
import cloud.datatp.fforwarder.core.message.entity.CRMMessageSystem;
import cloud.datatp.fforwarder.core.message.entity.MessageType;
import cloud.datatp.fforwarder.core.partner.CRMPartnerLogic;
import cloud.datatp.fforwarder.core.partner.entity.CRMPartner;
import cloud.datatp.fforwarder.price.InquiryRequestLogic;
import cloud.datatp.fforwarder.sales.common.TaskNotificationTemplate;
import cloud.datatp.fforwarder.sales.partner.CustomerLeadsLogic;
import cloud.datatp.fforwarder.sales.partner.entity.CustomerLeads;
import cloud.datatp.fforwarder.sales.partner.entity.PartnerEventHistory;
import cloud.datatp.fforwarder.sales.project.entity.TaskCalendar;
import cloud.datatp.fforwarder.sales.project.entity.TaskCalendar.SalesTaskStatus;
import cloud.datatp.fforwarder.sales.project.repository.TaskCalendarRepository;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.Getter;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.entity.Account;
import net.datatp.module.asset.entity.TaskableAsset;
import net.datatp.module.communication.CommunicationMessageLogic;
import net.datatp.module.communication.entity.CommunicationAccount;
import net.datatp.module.company.CompanyLogic;
import net.datatp.module.core.security.SecurityLogic;
import net.datatp.module.core.security.entity.AppPermission;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.SqlQueryUnitManager;
import net.datatp.module.data.db.SqlSelectView;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.data.db.seq.SeqService;
import net.datatp.module.graphapi.GraphApiService;
import net.datatp.module.hr.EmployeeLogic;
import net.datatp.module.http.upload.UploadService;
import net.datatp.module.wfms.entity.EntityTask;
import net.datatp.module.wfms.entity.EntityTaskApprovalStatus;
import net.datatp.module.wfms.entity.EntityTaskRequest;
import net.datatp.module.wfms.entity.EntityTaskStatus;
import net.datatp.module.wfms.repo.EntityTaskRepo;
import net.datatp.module.zalo.ZaloLogic;
import net.datatp.security.client.ClientContext;
import net.datatp.security.client.DataScope;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Getter
public class TaskCalendarLogic extends CRMDaoService {

  @Autowired
  private TaskCalendarRepository taskCalendarRepo;

  @Autowired
  private EntityTaskRepo entityTaskRepo;
  
  @Autowired
  private CommunicationMessageLogic messageLogic;

  @Autowired
  private AccountLogic accountLogic;

  @Autowired
  private GraphApiService graphApiService;

  @Autowired
  private UploadService uploadService;

  @Autowired
  private SeqService seqService;

  @Autowired
  private SecurityLogic securityLogic;

  @Autowired
  private ZaloLogic zaloLogic;

  @Autowired
  private EmployeeLogic employeeLogic;

  @Autowired
  private CompanyLogic companyLogic;

  @Autowired
  private InquiryRequestLogic inquiryReqLogic;

  @Autowired
  private CustomerLeadsLogic customerLeadsLogic;

  @Autowired
  private CRMPartnerLogic bfsonePartnerLogic;

  @Autowired
  private CommunicationMessageLogic communicationMessageLogic;

  @Autowired
  private CRMMessageLogic crmMessageLogic;

  @Autowired
  private TaskCalendarPlugin taskPlugin;


  //   ---------------------- Sales Daily Tasks ----------------------
  public TaskCalendar getSalesDailyTaskById(ClientContext client, ICompany company, Long id) {
    return taskCalendarRepo.findById(id).get();
  }

  public List<TaskCalendar> findYesterdayUncompletedTasks(ClientContext client, ICompany company) {
    Calendar cal = Calendar.getInstance();
    cal.add(Calendar.DATE, -1);
    cal.set(Calendar.HOUR_OF_DAY, 0);
    cal.set(Calendar.MINUTE, 0);
    cal.set(Calendar.SECOND, 0);
    cal.set(Calendar.MILLISECOND, 0);
    Date startDate = cal.getTime();
    cal.add(Calendar.DATE, 1);
    Date endDate = cal.getTime();
    return taskCalendarRepo.findYesterdayUncompletedTasks(company.getId(), startDate, endDate);
  }

  public TaskCalendar handleSalesDailyTask(ClientContext client, ICompany company, TaskCalendar saleTask) {
    EntityTask entityTask = new EntityTask();
    if (!saleTask.isNew()) {
      List<EntityTask> entityTasks = taskPlugin.findByEntityId(client, TaskCalendar.TABLE_NAME, saleTask.getId());
      if (Collections.isNotEmpty(entityTasks)) entityTask = entityTasks.get(0);
    } else {
      saleTask.setEntityTaskRequest(new EntityTaskRequest());
    }

    computeEntityTaskData(entityTask, saleTask);

    taskPlugin.handle(client, company, saleTask, entityTask, EntityTaskStatus.Submitted);

    return saleTask;
  }


  private void computeEntityTaskData(EntityTask entityTask, TaskCalendar saleTask) {
    if (StringUtil.isNotEmpty(saleTask.getLabel())) {
      entityTask.setLabel(saleTask.getLabel());
    } else {
      entityTask.setLabel("N/A");
    }

    entityTask.setStatus(EntityTaskStatus.Submitted);
    entityTask.setApprovalStatus(EntityTaskApprovalStatus.Approved);
    entityTask.setTaskType("CRM");
    entityTask.setDueDate(saleTask.getDueDate());
    entityTask.setDeadline(saleTask.getDueDate());
    entityTask.setAssigneeAccountId(saleTask.getCreatorAccountId());
    entityTask.setAssigneeFullName(saleTask.getCreatorLabel());
    entityTask.setReporterAccountId(saleTask.getSalemanAccountId());
    entityTask.setReporterFullName(saleTask.getSalemanLabel());
  }

  public TaskCalendar saveSalesDailyTask(ClientContext client, ICompany company, TaskCalendar task) {
    boolean isNotiEnabled = task.isSendingZalo() || task.isSendingEmail();
    boolean isNewTask = task.isNew();
    boolean hasNotificationTime = task.getNotificationTime() != null;

    if (task.isNew()) {
      if (task.getCreatedDate() == null) {
        task.setCreatedDate(new Date());
      }
      if (task.getCreatorAccountId() == null) {
        Account account = accountLogic.getAccountActiveById(client, client.getAccountId());
        Objects.assertNotNull(account, "Account not found!!!, login id: " + client.getRemoteUser());
        task.setCreatorAccountId(account.getId());
        task.setCreatorLabel(account.getFullName());
      }
      if (task.getDueDate() == null) task.initializeDueDate();

      if (task.getPartnerId() != null) {
        if (task.getPartnerType().equals(ClientPartnerType.CUSTOMER_LEAD) || task.getPartnerType().equals(ClientPartnerType.AGENTS_APPROACHED)) {
          CustomerLeads customerLead = customerLeadsLogic.getCustomerLeadById(client, company, task.getPartnerId());
          PartnerEventHistory eventHistory = new PartnerEventHistory(customerLead);
          eventHistory.withSalesDailyTask(task);
          customerLeadsLogic.savePartnerEventHistory(client, eventHistory);
        } else if (task.getPartnerType().equals(ClientPartnerType.CUSTOMERS) ||  task.getPartnerType().equals(ClientPartnerType.AGENTS)) {
          CRMPartner customer = bfsonePartnerLogic.getById(client, task.getPartnerId());
          PartnerEventHistory eventHistory = new PartnerEventHistory(customer);
          eventHistory.withSalesDailyTask(task);
          customerLeadsLogic.savePartnerEventHistory(client, eventHistory);
        }
      }
    } else {
      TaskCalendar taskInDb = getSalesDailyTaskById(client, company, task.getId());
      SalesTaskStatus status = task.getStatus();
      if (!SalesTaskStatus.isCompleted(taskInDb.getStatus()) && SalesTaskStatus.isCompleted(status)) {
        task.setDueDate(new Date());
      }

    }

    task.set(client, company);
    TaskCalendar saved = taskCalendarRepo.save(task);

    // Handle Zalo notification
    if (isNotiEnabled && hasNotificationTime) {

      if (!isNewTask) {
        TaskCalendar taskInDb = getSalesDailyTaskById(client, company, task.getId());
        if (!taskInDb.getNotificationTime().equals(task.getNotificationTime())) {
          if (task.isSendingZalo()) {
            CRMMessageSystem crmMessageSystem = createCRMMessageSystem(client, task, MessageType.ZALO);
            crmMessageLogic.scheduleMessage(client, crmMessageSystem);
          }

          if (task.isSendingEmail()) {
            CRMMessageSystem crmMessageSystem = createCRMMessageSystem(client, task, MessageType.MAIL);
            crmMessageLogic.scheduleMessage(client, crmMessageSystem);
          }
        }
      } else {
        if (task.isSendingZalo()) {
          CRMMessageSystem crmMessageSystem = createCRMMessageSystem(client, task, MessageType.ZALO);
          crmMessageLogic.scheduleMessage(client, crmMessageSystem);
        }
        if (task.isSendingEmail()) {
          CRMMessageSystem crmMessageSystem = createCRMMessageSystem(client, task, MessageType.MAIL);
          crmMessageLogic.scheduleMessage(client, crmMessageSystem);
        }
      }
    }

    return saved;
  }

  public List<TaskCalendar> findTasksByCreatedTimeRange(ClientContext client, Date startTime, Date endTime) {
    return taskCalendarRepo.findTasksByCreatedTimeRange(startTime, endTime);
  }

  public CRMMessageSystem createCRMMessageSystem(ClientContext client, TaskCalendar task, MessageType messageType) {
    CommunicationAccount communicationAccount = communicationMessageLogic.getCommunicationAccount(client, task.getSalemanAccountId());
    CRMMessageSystem crmMessageSystem = new CRMMessageSystem();
    crmMessageSystem.setScheduledAt(task.getNotificationTime());
    crmMessageSystem.setPluginName(TaskCalendarMessagePlugin.PLUGIN_TYPE);
    crmMessageSystem.setReferenceId(task.getId());
    crmMessageSystem.setReferenceType(TaskCalendar.TABLE_NAME);
    crmMessageSystem.setMessageType(messageType);
    MapObject metadata = new MapObject();
    if (messageType == MessageType.ZALO) {
      crmMessageSystem.setContent(TaskNotificationTemplate.buildZaloTaskMessage(task));
      crmMessageSystem.setRecipients(Set.of(communicationAccount.getMobile()));
    } else if (messageType == MessageType.MAIL) {
      crmMessageSystem.setContent(TaskNotificationTemplate.buildMailTaskMessage(task));
      crmMessageSystem.setRecipients(Set.of(communicationAccount.getEmail()));
      metadata.put("subject", "CRM - Daily Task Notification");
      metadata.put("fromEmail", "<EMAIL>");
      List<String> ccList = new ArrayList<>();
      ccList.add("<EMAIL>");
      metadata.put("ccList", ccList);
    }
    crmMessageSystem.setMetadata(metadata);
    return crmMessageSystem;
  }

  public List<MapObject> saveSaleDailyTaskRecords(ClientContext client, ICompany company, List<MapObject> requests) {
    if (Collections.isNotEmpty(requests)) {
      for (MapObject req : requests) {
        final Long id = req.getLong("id", null);
        TaskCalendar task = new TaskCalendar();
        if (id != null) {
          task = getSalesDailyTaskById(client, company, id);
          Objects.assertNotNull(task, "Sales Daily Task not found: id = " + id);
        }
        task = task.computeFromMapObject(req);
        TaskCalendar updated = handleSalesDailyTask(client, company, task);
        req.put("id", updated.getId());
      }
    }
    return requests;
  }

  public List<SqlMapRecord> searchSalesDailyTasks(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    sqlParams.addParam("companyId", company.getId());
    sqlParams.addParam("accessAccountId", client.getAccountId());
    AppPermission permission = securityLogic.getAppPermission(client, company.getId(), "logistics", "user-logistics-sales");
    if (permission == null) return java.util.Collections.emptyList();

    DataScope dataScope = permission.getDataScope();
    if (DataScope.Group == dataScope) {
      String scriptDir = appEnv.addonPath("core", "groovy");
      String scriptFile = "net/datatp/module/hr/groovy/EmployeeSql.groovy";
      List<SqlMapRecord> accountIds = searchPlatformDbRecords(client, scriptDir, scriptFile, "FindEmployeeIdsByManagerId", sqlParams);
      List<Long> participantAccountIds = accountIds.stream()
        .map(record -> record.getLong("accountId", null))
        .collect(Collectors.toList());
      sqlParams.addParam("participantAccountIds", participantAccountIds);
    }

    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/sales/groovy/SalesDailyTaskSql.groovy";
    return searchDbRecords(client, scriptDir, scriptFile, "SearchSalesDailyTasks", sqlParams);
  }

  public List<SqlMapRecord> searchSalesDailyTaskReport(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    AppPermission permission = securityLogic.getAppPermission(client, company.getId(), "logistics", "company-logistics-sales");
    if (permission == null) return java.util.Collections.emptyList();
    sqlParams.addParam("companyId", company.getId());
    sqlParams.addParam("accessAccountId", client.getAccountId());

    DataScope dataScope = permission.getDataScope();
    if (DataScope.Group == dataScope) {
      sqlParams.addParam("managerAccountId", client.getAccountId());
    }
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/sales/groovy/SalesDailyTaskSql.groovy";

    SqlQueryUnitManager.QueryContext queryContext = platformQueryUnitManager.create(scriptDir, scriptFile, "SearchSalemansWithDepartment");
    final SqlSelectView view = queryContext.createSqlSelectView(sqlParams);
    List<SqlMapRecord> salemans = view.renameColumWithJavaConvention().getSqlMapRecords();

    List<Long> salemanAccountIds = salemans.stream().map(s -> s.getLong("accountId")).collect(Collectors.toList());

    Map<Long, List<SqlMapRecord>> salesGroupByAccountId = salemans.stream().collect(Collectors.groupingBy(s -> s.getLong("accountId")));

    sqlParams.addParam("salemanAccountIds", salemanAccountIds);

    List<SqlMapRecord> salemanReports = searchDbRecords(client, scriptDir, scriptFile, "SearchSalesDailyTaskReport", sqlParams);

    List<SqlMapRecord> collect = salemanReports.stream().map((SqlMapRecord report) -> {
      Long accountId = report.getLong("salemanAccountId");
      List<SqlMapRecord> list = salesGroupByAccountId.get(accountId);
      if (Collections.isNotEmpty(list)) {
        SqlMapRecord saleman = list.get(0);
        report.set("departmentName", saleman.getString("departmentName"));
        report.set("departmentLabel", saleman.getString("departmentLabel"));
      }
      return report;
    }).collect(Collectors.toList());

    return collect;
  }

  public boolean deleteSalesDailyTaskByIds(ClientContext client, ICompany company, List<Long> ids) {
    for (Long id : ids) {
      List<EntityTask> entityTasks = entityTaskRepo.findByEntity(TaskableAsset.TABLE_NAME, id);
      if (Collections.isNotEmpty(entityTasks)) entityTaskRepo.deleteAll(entityTasks);
    }
    
    taskCalendarRepo.deleteByIds(company.getId(), ids);
    return true;
  }

  public boolean changeDailyTaskStorageState(ClientContext client, ICompany company, ChangeStorageStateRequest req) {
    for (Long id : req.getEntityIds()) {
      List<EntityTask> entityTasks = entityTaskRepo.findByEntity(TaskableAsset.TABLE_NAME, id);
      if (Collections.isNotEmpty(entityTasks)) {
        List<Long> entityTaskIds = entityTasks.stream().map(EntityTask::getId).collect(Collectors.toList());
        entityTaskRepo.updateStorageState(req.getNewStorageState(), entityTaskIds);
      }
    }
    taskCalendarRepo.setSaleDailyTaskState(req.getNewStorageState(), req.getEntityIds());
    return true;
  }
}