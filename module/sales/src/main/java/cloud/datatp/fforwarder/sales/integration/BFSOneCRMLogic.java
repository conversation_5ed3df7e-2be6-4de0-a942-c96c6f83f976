package cloud.datatp.fforwarder.sales.integration;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cloud.datatp.fforwarder.core.db.CRMDaoService;
import cloud.datatp.fforwarder.core.db.CRMSqlQueryUnitManager;
import cloud.datatp.fforwarder.core.integration.BFSOneApi;
import cloud.datatp.fforwarder.core.partner.CRMPartnerLogic;
import cloud.datatp.fforwarder.core.partner.entity.CRMPartner;
import cloud.datatp.fforwarder.core.template.CRMUserRoleLogic;
import cloud.datatp.fforwarder.core.template.entity.CrmUserRole;
import cloud.datatp.fforwarder.sales.booking.entity.Booking;
import cloud.datatp.fforwarder.sales.inquiry.entity.SpecificServiceInquiry;
import cloud.datatp.fforwarder.sales.partner.CustomerLeadsLogic;
import cloud.datatp.fforwarder.sales.partner.entity.CustomerLeads;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.company.entity.CompanyConfig;
import net.datatp.module.data.db.ExternalDataSourceManager;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.SqlSelectView;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.data.db.seq.SeqService;
import net.datatp.security.client.ClientContext;
import net.datatp.util.dataformat.DataSerializer;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import net.datatp.util.error.RuntimeError;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;

@Slf4j
@Component
public class BFSOneCRMLogic extends CRMDaoService {

  @Autowired
  private BFSOneApi bfsOneApi;

  @Autowired
  private CRMPartnerLogic bfsOnePartnerLogic;

  @Autowired
  private CustomerLeadsLogic customerLeadsLogic;

  @Autowired
  private CRMUserRoleLogic crmUserRoleLogic;


  @Autowired
  protected SeqService seqService;

  @PostConstruct
  public void onInit() {
    seqService.createIfNotExists(CRMPartner.SEQUENCE, 6);
  }

  public Booking createInternalBookingNew(ClientContext client, Booking booking) {
    CrmUserRole saleman = crmUserRoleLogic.getByAccountId(client, booking.getSenderAccountId());
    Objects.assertNotNull(saleman, "Saleman is not found, name = " + booking.getSenderLabel());
    String bfsoneContactId = saleman.getBfsoneCode();
    String bfsoneUsername = saleman.getBfsoneUsername();
    String authenticate = bfsOneApi.authenticate(bfsoneContactId, bfsoneUsername);

    SpecificServiceInquiry inquiry = booking.getInquiry();
    Long clientPartnerId = inquiry.getClientPartnerId();
    CRMPartner customerPartner = bfsOnePartnerLogic.getById(client, clientPartnerId);
    Objects.assertNotNull(customerPartner, "Customer is not found, id = " + clientPartnerId);
    MapObject bfsOneIBooking = booking.toBFSOneIBooking(customerPartner);

    CRMPartner agent = bfsOnePartnerLogic.getById(client, inquiry.getHandlingAgentPartnerId());
    if (agent != null) bfsOneIBooking.put("AgentID", agent.getPartnerCode());

    /* --------------Saleman - Cus/ Docs------------*/
    String receiverEmpl = booking.getReceiverLabel();
    String receiverBFSOneCode = booking.getReceiverBFSOneCode();
    Objects.assertNotNull(receiverBFSOneCode, "Receiver Contact Code is not found, name = " + receiverEmpl);
    bfsOneIBooking.put("ReceiveUserID", receiverBFSOneCode);
    bfsOneIBooking.put("SendUserID", bfsoneContactId);

    CRMPartner coloader = bfsOnePartnerLogic.getById(client, booking.getCarrierPartnerId());
    if (coloader != null) bfsOneIBooking.put("ColoaderID", coloader.getPartnerCode());
    MapObject savedIB = null;
    try {
      String bkgID = bfsOneIBooking.getString("BkgID");
      if (StringUtil.isNotBlank(bkgID)) {
        MapObject bfsOneBKExist = bfsOneApi.loadIBooking(authenticate, bkgID);
        DataSerializer.JSON.dump(bfsOneBKExist);
        String bkgIDExist = bfsOneBKExist.getString("BkgID", null);
        if (StringUtil.isEmpty(bkgIDExist)) bfsOneIBooking.put("BkgID", null);
      }
      savedIB = bfsOneApi.createIBooking(authenticate, bfsOneIBooking);
    } catch (Exception ex) {
      log.info(ex.getMessage());
      log.error("Error when create internal booking: \n");
      DataSerializer.JSON.dump(bfsOneIBooking);
      throw RuntimeError.UnknownError(ex.getMessage());
    }

    String bkgID = savedIB.getString("BkgID");
    Objects.assertNotNull(bkgID, "BFSOne Reference is not found, id = " + booking.getId());
    booking.setBfsoneReference(bkgID);
    return booking;
  }

  public Boolean deleteInternalBooking(ClientContext client, String ibCode) {
    CrmUserRole employee = crmUserRoleLogic.getByAccountId(client, client.getAccountId());
    Objects.assertNotNull(employee, "Employee is not found, login id = " + client.getRemoteUser());
    String bfsoneEmployeeCode = employee.getBfsoneCode();
    String bfsoneUsername = employee.getBfsoneUsername();
    String authenticate = bfsOneApi.authenticate(bfsoneEmployeeCode, bfsoneUsername);
    bfsOneApi.deleteInternalBooking(authenticate, ibCode);
    return true;
  }

  public MapObject loadInternalBooking(ClientContext client, String ibCode) {
    CrmUserRole employee = crmUserRoleLogic.getByAccountId(client, client.getAccountId());
    Objects.assertNotNull(employee, "Employee is not found, login id = " + client.getRemoteUser());
    String bfsoneEmployeeCode = employee.getBfsoneCode();
    String bfsoneUsername = employee.getBfsoneUsername();
    String authenticate = bfsOneApi.authenticate(bfsoneEmployeeCode, bfsoneUsername);
    return bfsOneApi.loadIBooking(authenticate, ibCode);
  }

  public List<SqlMapRecord> findBookingLocalByHawb(ClientContext client, String hawb) {
    SqlQueryParams params = new SqlQueryParams();
    params.addParam("hawb", hawb.trim());
    String scriptDir = bfsOnePartnerLogic.getAppEnv().addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/core/groovy/FindBookingLocalSql.groovy";
    String scriptName = "FindBookingLocalByHawb";
    CompanyConfig companyConfig = bfsOnePartnerLogic.getCompanyConfigLogic().getCompanyConfigByCompanyId(client, client.getCompanyId());
    ExternalDataSourceManager.DataSourceParams dsPrams = companyConfig.getSubConfigAs("bfsone.ds", ExternalDataSourceManager.DataSourceParams.class);
    DataSource ds = bfsOnePartnerLogic.getDataSourceManager().getDataSource(client, dsPrams);
    CRMSqlQueryUnitManager.QueryContext queryContext = bfsOnePartnerLogic.getSqlQueryUnitManager().create(scriptDir, scriptFile, scriptName);
    SqlSelectView view = queryContext.createSqlSelectView(ds, params);
    List<SqlMapRecord> records = view.getSqlMapRecords();
    log.info("Retrieved {} records", records.size());
    return records;
  }

  public MapObject saveBFSOneCustomerLead(ClientContext client, CustomerLeads lead) {
    CrmUserRole saleman = crmUserRoleLogic.getByAccountId(client, lead.getSalemanAccountId());
    Objects.assertNotNull(saleman, "Saleman is not found, account id = " + lead.getSalemanAccountId());
    String bfsoneEmployeeCode = saleman.getBfsoneCode();
    String bfsoneUsername = saleman.getBfsoneUsername();
    String authenticate = bfsOneApi.authenticate(bfsoneEmployeeCode, bfsoneUsername);

    String leadCode = lead.getBfsoneLeadCode() != null ? lead.getBfsoneLeadCode() : lead.getCode();
    if (StringUtil.isNotEmpty(leadCode)) {
      MapObject bfsOneCustomerLeadInDb = bfsOneApi.loadLead(authenticate, leadCode);
      if (Objects.nonNull(bfsOneCustomerLeadInDb)) {
        String contactId = bfsOneCustomerLeadInDb.getString("ContactID");
        if (StringUtil.isNotEmpty(contactId)) {
          lead.setBfsoneLeadCode(contactId);
          lead.setCode(contactId);
        }
      }
    }
    MapObject bfsOneCustomerLead = lead.toBFSOneCustomerLead(bfsoneUsername);
    return bfsOneApi.saveBFSOneCustomerLead(authenticate, bfsOneCustomerLead);
  }

  public MapObject deleteLead(ClientContext client, CustomerLeads lead) {
    CrmUserRole saleman = crmUserRoleLogic.getByAccountId(client, lead.getSalemanAccountId());
    Objects.assertNotNull(saleman, "Saleman is not found, account id = " + lead.getSalemanAccountId());
    String bfsoneEmployeeCode = saleman.getBfsoneCode();
    String bfsoneUsername = saleman.getBfsoneUsername();
    String authenticate = bfsOneApi.authenticate(bfsoneEmployeeCode, bfsoneUsername);
    String leadCode = lead.getBfsoneLeadCode() != null ? lead.getBfsoneLeadCode() : lead.getCode();
    return bfsOneApi.deleteLead(authenticate, leadCode);
  }

  public List<SqlMapRecord> checkPartnerExistInSystem(ClientContext client, String searchPattern) {
    /* check partners, agent/ agent potential, customer lead */
    Objects.assertNotNull(StringUtil.isNotEmpty(searchPattern), "Partner Name/ Tax code must be required!!!");
    SqlQueryParams sqlParams = new SqlQueryParams();
    String normalizePattern = searchPattern.trim().toUpperCase(Locale.ROOT);
    sqlParams.addParam("searchPattern", normalizePattern);
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/core/groovy/BFSOnePartnerSql.groovy";
    String scriptName = "CheckBFSOnePartnerByTaxCode";
    CompanyConfig companyConfig = bfsOnePartnerLogic.getCompanyConfigLogic().getCompanyConfigByCompanyId(client, client.getCompanyId());
    ExternalDataSourceManager.DataSourceParams dsPrams = companyConfig.getSubConfigAs("bfsone.ds", ExternalDataSourceManager.DataSourceParams.class);
    DataSource ds = bfsOnePartnerLogic.getDataSourceManager().getDataSource(client, dsPrams);
    CRMSqlQueryUnitManager.QueryContext queryContext = sqlQueryUnitManager.create(scriptDir, scriptFile, scriptName);
    SqlSelectView view = queryContext.createSqlSelectView(ds, sqlParams);
    List<SqlMapRecord> existingPartners = view.renameColumWithJavaConvention().getSqlMapRecords();

    if (Collections.isNotEmpty(existingPartners)) {
      Map<String, SqlMapRecord> existingPartnerMap = new HashMap<>();
      for (SqlMapRecord partner : existingPartners) {
        String partnerId = partner.getString("partnerId", "");
        if (partnerId.startsWith("AG")) partner.put("partnerType", "AGENT");
        else partner.put("partnerType", "CUSTOMER");
        if (existingPartnerMap.containsKey(partnerId)) continue;
        existingPartnerMap.put(partnerId, partner);
      }
      sqlParams.addParam("partnerIds", new ArrayList<>(existingPartnerMap.keySet()));
      CRMSqlQueryUnitManager.QueryContext queryContextBFSOne = sqlQueryUnitManager.create(scriptDir, scriptFile, "FindTop10LatestTransactionByCustomer");
      DataSource bfsoneReportDataSource = bfsOnePartnerLogic.getLegacyDataSource();
      SqlSelectView viewBFSOne = queryContextBFSOne.createSqlSelectView(bfsoneReportDataSource, sqlParams);
      List<SqlMapRecord> top10Transactions = viewBFSOne.renameColumWithJavaConvention().getSqlMapRecords();
      if (net.datatp.util.ds.Collections.isNotEmpty(top10Transactions)) {
        Map<String, List<SqlMapRecord>> partnerTransactionMap = new LinkedHashMap<>();
        for (SqlMapRecord record : top10Transactions) {
          String customerCode = record.getString("customerCode");
          if (StringUtil.isEmpty(customerCode)) continue;
          partnerTransactionMap
            .computeIfAbsent(customerCode, k -> new ArrayList<>())
            .add(record);
        }

        for (String partnerCode : existingPartnerMap.keySet()) {
          if (!partnerTransactionMap.containsKey(partnerCode)) continue;
          SqlMapRecord record = existingPartnerMap.get(partnerCode);
          List<SqlMapRecord> transactions = partnerTransactionMap.get(partnerCode);
          SqlMapRecord lastedRecord = transactions.get(0);
          record.put("transactionId", lastedRecord.getString("transactionId"));
          record.put("transactionDate", lastedRecord.getString("reportDate"));
          record.put("shipmentType", lastedRecord.getString("shipmentType"));
          for (SqlMapRecord rec : transactions) {
            Date reportDate = rec.getDate("reportDate", null);
            if (reportDate == null) continue;
            rec.put("reportDate", DateUtil.asCompactDate(reportDate));
            CrmUserRole userRole = crmUserRoleLogic.getByBfsoneCode(client, lastedRecord.getString("salemanContactId"));
            if (Objects.nonNull(userRole)) {
              rec.put("salemanLabel", userRole.getFullName());
              rec.put("salemanEmail", userRole.getEmail());
            } else {
              rec.put("salemanLabel", lastedRecord.getString("salemanContactId"));
            }
          }
          record.put("transactionSummary", transactions);
        }
      }

    }

    List<CustomerLeads> customerLeads = customerLeadsLogic.findCustomerLeadByTaxCodeOrName(client, normalizePattern);
    if (net.datatp.util.ds.Collections.isNotEmpty(customerLeads)) {
      for (CustomerLeads lead : customerLeads) {
        SqlMapRecord leadRec = new SqlMapRecord();
        leadRec.put("partnerType", "LEAD");
        leadRec.put("partnerId", lead.getCode());
        leadRec.put("partnerName", lead.getName());
        leadRec.put("address", lead.getAddress());
        leadRec.put("taxCode", lead.getTaxCode());
        leadRec.put("dateCreated", DateUtil.asCompactDateTime(lead.getDate()));
        try {
          CrmUserRole saleman = crmUserRoleLogic.getByAccountId(client, lead.getSalemanAccountId());
          Objects.assertNotNull(saleman, "Saleman is not found, account id = " + lead.getSalemanAccountId());
          leadRec.put("salemanLabel", saleman.getFullName() + " (" + saleman.getBfsoneUsername() + ")");
          leadRec.put("salemanUsername", saleman.getBfsoneUsername());
        } catch (Exception e) {
          leadRec.put("salemanLabel", lead.getSalemanLabel());
        }
        existingPartners.add(leadRec);
      }
    }
    return existingPartners;
  }

  public List<MapObject> loadUsers(ClientContext client) {
    CrmUserRole employee = crmUserRoleLogic.getByBfsoneUsername(client, "JESSE.VNHPH");
    Objects.assertNotNull(employee, "Admin User is not found, login id = JESSE.VNHPH");
    String bfsoneEmployeeCode = employee.getBfsoneCode();
    String bfsoneUsername = employee.getBfsoneUsername();
    String authenticate = bfsOneApi.authenticate(bfsoneEmployeeCode, bfsoneUsername);
    return bfsOneApi.loadUsers(authenticate);
  }

}