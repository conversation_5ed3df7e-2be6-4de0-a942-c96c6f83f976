package cloud.datatp.fforwarder.sales.report;

import cloud.datatp.fforwarder.core.db.CRMDaoService;
import cloud.datatp.fforwarder.core.db.CRMSqlQueryUnitManager;
import cloud.datatp.fforwarder.core.integration.BFSOneApi;
import cloud.datatp.fforwarder.core.template.CRMUserRoleLogic;
import cloud.datatp.fforwarder.core.template.entity.CrmUserRole;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.core.security.SecurityLogic;
import net.datatp.module.core.security.entity.AppPermission;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.SqlQueryUnitManager;
import net.datatp.module.data.db.SqlSelectView;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.security.client.ClientContext;
import net.datatp.security.client.DataScope;
import net.datatp.util.ds.Collections;
import net.datatp.util.text.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j @Getter
public class PerformanceReportLogic extends CRMDaoService {

  @Autowired
  private CRMUserRoleLogic crmUserRoleLogic;

  @Autowired
  private BFSOneApi bfsOneApi;

  @Autowired
  private SecurityLogic securityLogic;

  public List<SqlMapRecord> searchVolumePerformanceBySalemanReport(ClientContext client, SqlQueryParams sqlParams) {
    //String userTypeStr = sqlParams.getString("userType");
    List<CrmUserRole> saleman = crmUserRoleLogic.findByUserType(client, CrmUserRole.UserType.SALE_AGENT);
    if(Collections.isEmpty(saleman)) return new ArrayList<>();

    Map<String, String> salemanMap = saleman.stream()
      .collect(Collectors.toMap(
        CrmUserRole::getBfsoneCode,
        user -> user.getFullName() != null ? user.getFullName() : "",
        (existing, replacement) -> existing
      ));

    List<String> salemanContactIds = new ArrayList<>(salemanMap.keySet());
    sqlParams.addParam("salemanContactIds", salemanContactIds);

    try {
      String scriptDir = appEnv.addonPath("logistics", "groovy");
      String scriptFile = "cloud/datatp/fforwarder/sales/groovy/report/VolumePerformanceReportSql.groovy";
      String scriptName = "SearchVolumePerformanceBySalemanReport";
      CRMSqlQueryUnitManager.QueryContext queryContext = sqlQueryUnitManager.create(scriptDir, scriptFile, scriptName);
      SqlSelectView view = queryContext.createSqlSelectView(legacyDataSource, sqlParams);
      List<SqlMapRecord> records = view.renameColumWithJavaConvention().getSqlMapRecords();

      //Merge fullName into records
      records.forEach(record -> {
        String contactId = record.getString("salemanContactId"); // Adjust field name as needed
        if (contactId != null && salemanMap.containsKey(contactId)) {
          record.put("salemanFullName", salemanMap.get(contactId));
        }
      });

      List<SqlMapRecord> allRecords = new ArrayList<>(records);
      for (Map.Entry<String, String> entry : salemanMap.entrySet()) {
        String contactId = entry.getKey();
        String fullName = entry.getValue();

        // Check if this salesman already has a record
        boolean hasRecord = records.stream()
          .anyMatch(record -> contactId.equals(record.getString("salemanContactId")));

        if (!hasRecord) {
          // Create empty record for salesman with no data
          SqlMapRecord emptyRecord = new SqlMapRecord();
          emptyRecord.put("salemanContactId", contactId);
          emptyRecord.put("salemanFullName", fullName);
          allRecords.add(emptyRecord);
        }
      }
      log.info("Retrieved {} records", allRecords.size());
      return allRecords;
    } catch (Exception e) {
      log.error("Error when search Saleman Key Account Report", e);
      return new ArrayList<>();
    }
  }

  public List<SqlMapRecord> searchVolumePerformanceByCompany(ClientContext client, SqlQueryParams sqlParams) {
    try {
      String scriptDir = appEnv.addonPath("logistics", "groovy");
      String scriptFile = "cloud/datatp/fforwarder/sales/groovy/report/VolumePerformanceReportSql.groovy";
      String scriptName = "SearchVolumePerformanceBySalemanReport";
      CRMSqlQueryUnitManager.QueryContext queryContext = sqlQueryUnitManager.create(scriptDir, scriptFile, scriptName);
      SqlSelectView view = queryContext.createSqlSelectView(legacyDataSource, sqlParams);
      List<SqlMapRecord> records = view.renameColumWithJavaConvention().getSqlMapRecords();
      log.info("Retrieved {} records", records.size());
      return records;
    } catch (Exception e) {
      log.error("Error when search Saleman Key Account Report", e);
      return new ArrayList<>();
    }
  }

  public List<SqlMapRecord> searchVolumePerformanceByShipment(ClientContext client, SqlQueryParams sqlParams) {
    try {
      String scriptDir = appEnv.addonPath("logistics", "groovy");
      String scriptFile = "cloud/datatp/fforwarder/sales/groovy/report/VolumePerformanceReportSql.groovy";
      String scriptName = "SearchVolumePerformanceByShipment";
      CRMSqlQueryUnitManager.QueryContext queryContext = sqlQueryUnitManager.create(scriptDir, scriptFile, scriptName);
      SqlSelectView view = queryContext.createSqlSelectView(legacyDataSource, sqlParams);
      List<SqlMapRecord> records = view.renameColumWithJavaConvention().getSqlMapRecords();
      log.info("Retrieved {} records", records.size());
      return records;
    } catch (Exception e) {
      log.error("Error when search Saleman Key Account Report", e);
      return new ArrayList<>();
    }
  }
  
  public List<SqlMapRecord> searchVolumePerformanceByHouseBill(ClientContext client, SqlQueryParams sqlParams) {
    try {
      String scriptDir = appEnv.addonPath("logistics", "groovy");
      String scriptFile = "cloud/datatp/fforwarder/sales/groovy/report/VolumePerformanceReportSql.groovy";
      String scriptName = "SearchVolumePerformanceByHouseBill";
      CRMSqlQueryUnitManager.QueryContext queryContext = sqlQueryUnitManager.create(scriptDir, scriptFile, scriptName);
      SqlSelectView view = queryContext.createSqlSelectView(legacyDataSource, sqlParams);
      List<SqlMapRecord> records = view.renameColumWithJavaConvention().getSqlMapRecords();
      log.info("Retrieved {} records", records.size());
      return records;
    } catch (Exception e) {
      log.error("Error when search Saleman Key Account Report", e);
      return new ArrayList<>();
    }
  }


  // ---------------------- Sale Report ----------------------
  public List<SqlMapRecord> saleAccountReport(ClientContext client, SqlQueryParams sqlParams) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/sales/groovy/SaleReportSql.groovy";

    SqlQueryUnitManager.QueryContext queryContext = platformQueryUnitManager.create(scriptDir, scriptFile, "SaleAccountFullInfoReport");
    final SqlSelectView view = queryContext.createSqlSelectView(sqlParams);
    List<SqlMapRecord> accountInfos = view.renameColumWithJavaConvention().getSqlMapRecords();

    List<Long> accountIds = accountInfos.stream().map(r -> r.getLong("saleAccountId")).collect(Collectors.toList());
    sqlParams.addParam("accountIds", accountIds);

    List<SqlMapRecord> stats = searchDbRecords(client, scriptDir, scriptFile, "SaleAccountStatsReport", sqlParams);

    for (SqlMapRecord stat : stats) {
      Long accountId = stat.getLong("saleAccountId");
      SqlMapRecord info = accountInfos.stream().filter(r -> accountId.equals(r.getLong("saleAccountId"))).findFirst().orElse(null);
      if (info != null) {
        stat.put("companyCode", info.getString("companyCode"));
        stat.put("saleManLabel", info.getString("saleManLabel"));
        stat.put("totalSearchCount", info.getLong("totalSearchCount"));
        stat.put("searchSource", info.getString("searchSource"));
        stat.put("searchDestination", info.getString("searchDestination"));
        stat.put("topRouteSearchCount", info.getLong("topRouteSearchCount"));
      }
    }
    stats.sort((a, b) -> Long.compare(b.getLong("totalSearchCount", 0L), a.getLong("totalSearchCount", 0L)));
    return stats;
  }

  public List<SqlMapRecord> salemanSystemPerformanceReport(ClientContext client, SqlQueryParams sqlParams) {
    AppPermission permission = securityLogic.getAppPermission(client, client.getCompanyId(), "logistics", "company-logistics-sales");
    if (permission == null) return java.util.Collections.emptyList();
    sqlParams.addParam("companyId", client.getCompanyId());
    sqlParams.addParam("accessAccountId", client.getAccountId());
    DataScope dataScope = permission.getDataScope();
    if (DataScope.Group == dataScope) {
      sqlParams.addParam("managerAccountId", client.getAccountId());
    }
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/sales/groovy/SaleReportSql.groovy";
    if (!sqlParams.hasParam("companyId")) sqlParams.addParam("companyId", client.getCompanyId());

    // Step 1: Get salemanAccountIds
    List<SqlMapRecord> salemanAccountRecords = searchPlatformDbRecords(client, scriptDir, scriptFile, "SalemanActivityTrackerReportAccount", sqlParams);
    if (Collections.isEmpty(salemanAccountRecords)) return java.util.Collections.emptyList();

    Map<Long, SqlMapRecord> groupedByAccountId = salemanAccountRecords.stream().collect(
      Collectors.toMap(record -> record.getLong("employeeAccountId"), record -> record, (existing, replacement) -> existing));
    sqlParams.addParam("salemanAccountIds", new ArrayList<>(groupedByAccountId.keySet()));

    // Step 2: Get stats
    List<SqlMapRecord> stats = searchDbRecords(client, scriptDir, scriptFile, "SalemanActivityTrackerReport", sqlParams);
    Map<Long, SqlMapRecord> groupedStatByAccountId = stats.stream().collect(
      Collectors.toMap(record -> record.getLong("employeeAccountId"), record -> record, (existing, replacement) -> existing));

    List<SqlMapRecord> mergedResults = new ArrayList<>();

    for (Long salemanAccountId : groupedByAccountId.keySet()) {
      SqlMapRecord accountInfo = groupedByAccountId.get(salemanAccountId);
      SqlMapRecord statInfo = groupedStatByAccountId.get(salemanAccountId);

      if (statInfo != null) {
        // Account có stats data - merge account info vào stats
        statInfo.add("employeeLabel", accountInfo.getString("employeeLabel"));
        statInfo.add("companyBranch", accountInfo.getString("companyBranch"));
        mergedResults.add(statInfo);
      } else {
        //mergedResults.add(accountInfo);

        SqlMapRecord defaultStat = new SqlMapRecord();
        defaultStat.add("employeeAccountId", salemanAccountId);
        defaultStat.add("employeeLabel", accountInfo.getString("employeeLabel"));
        defaultStat.add("companyBranch", accountInfo.getString("companyBranch"));
        // Add default values for all stat fields
        defaultStat.add("total_tasks", 0);
        defaultStat.add("in_progress_tasks", 0);
        defaultStat.add("meet_customer_tasks", 0);
        defaultStat.add("total_requests_pricing", 0);
        defaultStat.add("no_response_requests", 0);
        defaultStat.add("booking_count", 0);
        defaultStat.add("quotation_count", 0);
        defaultStat.add("inquiry_count", 0);
        defaultStat.add("new_customer_count", 0);
        defaultStat.add("new_lead_count", 0);
        defaultStat.add("overdue_request_count", 0);
        mergedResults.add(defaultStat);
      }
    }
    sortStatsByBranchAndEmployee(mergedResults);
    return mergedResults;
  }

  private void sortStatsByBranchAndEmployee(List<SqlMapRecord> stats) {
    stats.sort((recordA, recordB) -> {
      String branchA = recordA.getString("companyBranch", "");
      String branchB = recordB.getString("companyBranch", "");
      boolean isEmptyBranchA = StringUtil.isEmpty(branchA);
      boolean isEmptyBranchB = StringUtil.isEmpty(branchB);

      // Empty branches go last
      if (isEmptyBranchA && !isEmptyBranchB) return 1;
      if (!isEmptyBranchA && isEmptyBranchB) return -1;

      int branchComparison = branchA.compareToIgnoreCase(branchB);
      if (branchComparison != 0) {
        return branchComparison;
      }

      String labelA = recordA.getString("employeeLabel", "");
      String labelB = recordB.getString("employeeLabel", "");
      return labelA.compareToIgnoreCase(labelB);
    });
  }

  public List<SqlMapRecord> saleConversationRateReport(ClientContext client, SqlQueryParams sqlParams) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/sales/groovy/SaleReportSql.groovy";
    if (!sqlParams.hasParam("companyId")) sqlParams.addParam("companyId", client.getCompanyId());
    return searchDbRecords(client, scriptDir, scriptFile, "SalemanActivityTrackerReport", sqlParams);
  }

}