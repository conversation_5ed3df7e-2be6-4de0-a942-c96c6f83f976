package cloud.datatp.fforwarder.sales.automation;

import cloud.datatp.fforwarder.core.message.CRMMessageLogic;
import cloud.datatp.fforwarder.core.message.entity.CRMMessageSystem;
import cloud.datatp.fforwarder.core.message.entity.MessageType;
import cloud.datatp.fforwarder.sales.common.TaskNotificationTemplate;
import cloud.datatp.fforwarder.sales.project.TaskCalendarMessagePlugin;
import cloud.datatp.fforwarder.sales.project.TaskCalendarLogic;
import cloud.datatp.fforwarder.sales.project.entity.TaskCalendar;
import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.app.AppEnv;
import net.datatp.module.bot.cron.CronJob;
import net.datatp.module.bot.cron.CronJobFrequency;
import net.datatp.module.bot.cron.CronJobLogger;
import net.datatp.module.communication.CommunicationMessageLogic;
import net.datatp.module.communication.entity.CommunicationAccount;
import net.datatp.module.company.CompanyReadLogic;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.security.client.ClientContext;
import net.datatp.security.client.DeviceInfo;
import net.datatp.security.client.DeviceType;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class UncompletedTaskProcessorCronJob extends CronJob {

  @Autowired
  private AppEnv appEnv;

  @Autowired
  private CompanyReadLogic companyLogic;

  @Autowired
  private TaskCalendarLogic taskReportLogic;

  @Autowired
  private CRMMessageLogic crmMessageLogic;

  @Autowired
  private CommunicationMessageLogic communicationMessageLogic;

  public UncompletedTaskProcessorCronJob() {
    super("lgc-sales-daily-tasks-uncompleted-processor:daily", "Uncompleted Task Processor Cron Job");
  }

  @PostConstruct
  public void onInit() {
    if(appEnv.isDevEnv()) {
      setFrequencies(CronJobFrequency.NONE);
    } else {
      setFrequencies(CronJobFrequency.EVERY_DAY_08_AM);
    }
  }

  @Override
  protected List<ICompany> getTargetCompanies() {
    List<ICompany> companies = new ArrayList<>();
    ICompany       iCompany   = ICompany.SYSTEM;
    String[] companyCodes = new String[] {"bee", "beehph", "beehan", "beehcm", "beedad"};
    for (String companyCode : companyCodes) {
      ICompany company = companyLogic.getCompany(getClientContext(iCompany), companyCode);
      companies.add(company);
    }
    return companies;
  }

  @Override
  protected ClientContext getClientContext(ICompany company) {
    ClientContext client = new ClientContext("default", "dan", "localhost");
    if(appEnv.isProdEnv()) {
      client.setDeviceInfo(new DeviceInfo(DeviceType.Server));
    } else {
      client.setDeviceInfo(new DeviceInfo(DeviceType.Computer));
    }
    client.setCompany(company);
    client.setAccountId(3L);
    return client;
  }

  protected Set<String> getReportToUsers(ClientContext client, ICompany company) {
    Set<String> userSet = super.getReportToUsers(client, company);
    userSet.add("dan");
    return userSet;
  }

  @Override
  protected void run(ClientContext client, ICompany iCompany, CronJobLogger logger) {
    List<TaskCalendar> uncompletedTasks = taskReportLogic.findYesterdayUncompletedTasks(client, iCompany);
    if (uncompletedTasks.isEmpty()) return;
    log.info("\n\n======== Starting Inquiry Request Task Creator job at {}\n", DateUtil.asLocalDateTime(new Date()));

    List<TaskCalendar> tasksToUpdate = new ArrayList<>();

    for (TaskCalendar task : uncompletedTasks) {
      try {
          Calendar cal = Calendar.getInstance();
          cal.setTime(new Date());
          cal.set(Calendar.HOUR_OF_DAY, 0);
          cal.set(Calendar.MINUTE, 0);
          cal.set(Calendar.SECOND, 0);
          cal.set(Calendar.MILLISECOND, 0);
          task.setDueDate(cal.getTime());
          TaskCalendar saved = taskReportLogic.handleSalesDailyTask(client, iCompany, task);
          tasksToUpdate.add(saved);
      } catch (Exception ex) {
        log.error("Failed to update due date for task {}", task.getId(), ex);
      }
    }

    Map<Long, List<TaskCalendar>> tasksBySaleAccount = tasksToUpdate.stream()
        .collect(Collectors.groupingBy(TaskCalendar::getSalemanAccountId));

    // Send notifications for each sale account
    for (Map.Entry<Long, List<TaskCalendar>> entry : tasksBySaleAccount.entrySet()) {
      Long saleAccountId = entry.getKey();
      List<TaskCalendar> userTasks = entry.getValue();
      try {
        // Try to send Zalo notification first
        CRMMessageSystem message = new CRMMessageSystem();
        message.setContent(TaskNotificationTemplate.buildUncompletedZaloMessage(userTasks));
        message.setScheduledAt(new Date());
        message.setMessageType(MessageType.ZALO);
        message.setPluginName(TaskCalendarMessagePlugin.PLUGIN_TYPE);
        CommunicationAccount targetAccount = communicationMessageLogic.getCommunicationAccount(client, saleAccountId);
        Objects.assertNotNull(targetAccount, "Account {} is not found", saleAccountId);
        message.setRecipients(new HashSet<>(Collections.singletonList(targetAccount.getMobile())));
        crmMessageLogic.scheduleMessage(client, message);
      } catch (Exception e) {
        log.error("Failed to send Zalo notifications to account {}, error: {}", saleAccountId, e.getMessage());
      }
    }
  }
}