package cloud.datatp.fforwarder.sales.partner.entity;

import cloud.datatp.fforwarder.core.partner.entity.CRMPartner;
import cloud.datatp.fforwarder.core.template.entity.CrmUserRole;
import cloud.datatp.fforwarder.sales.project.entity.TaskCalendar;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;

import java.util.Date;
import java.util.List;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.company.entity.CompanyEntity;
import net.datatp.util.bean.BeanUtil;
import net.datatp.util.ds.MapObject;
import net.datatp.util.text.DateUtil;

/**
 * <AUTHOR>
 */
@Entity
@Table(
  name = PartnerEventHistory.TABLE_NAME,
  indexes = {
    @Index(
      name = PartnerEventHistory.TABLE_NAME + "_id_idx",
      columnList = "reference_partner_id"
    ),
    @Index(
      name = PartnerEventHistory.TABLE_NAME + "_saleman_account_id_idx",
      columnList = "saleman_account_id"
    ),
  }
)
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor
@Getter
@Setter
public class PartnerEventHistory extends CompanyEntity {

  final static public String TABLE_NAME = "forwarder_partner_event_history";

  @Column(name = "transaction_date")
  @JsonFormat(pattern = DateUtil.COMPACT_DATE_FORMAT)
  private Date transactionDate;

  @Column(name = "saleman_account_id")
  private Long salemanAccountId;

  @Column(name = "saleman_label")
  private String salemanLabel;

  @Column(name = "reference_partner_type")
  private String referencePartnerType;

  @NotNull
  @Column(name = "reference_partner_id")
  private Long referencePartnerId;

  @Column(name = "type")
  private String type;

  @Column(name = "label", length = 2 * 1024)
  private String label;

  @Column(length = 9 * 1024)
  private String description;

  @Column(name = "shipping_route", length = 1024)
  private String shippingRoute;

  @Column(name = "volume_note", length = 1024)
  private String volumeNote;

  @Column(name = "company_support", length = 9 * 1024)
  private String companySupport;

  @Column(name = "customer_feedback", length = 9 * 1024)
  private String customerFeedback;

  @Column(name = "manager_evaluation", length = 9 * 1024)
  private String managerEvaluation;

  public PartnerEventHistory(CustomerLeads lead) {
    transactionDate = new Date();
    referencePartnerId = lead.getId();
    referencePartnerType = CustomerLeads.TABLE_NAME;
  }

  public PartnerEventHistory(CRMPartner partner) {
    transactionDate = new Date();
    referencePartnerId = partner.getId();
    referencePartnerType = CRMPartner.TABLE_NAME;
  }

  public PartnerEventHistory withSalesDailyTask(TaskCalendar task) {
    salemanAccountId = task.getSalemanAccountId();
    salemanLabel = task.getSalemanLabel();
    type = task.getTaskType().getLabel();
    label = task.getLabel();
    description = task.getDescription();
    shippingRoute = task.getPartnerShippingRoute();
    volumeNote = task.getPartnerVolumeDetails();
    companySupport = task.getSuggestedSupport();
    return this;
  }

  public PartnerEventHistory computeFromMapObject(MapObject sel) {
    List<String> fields = List.of(
      "transactionDate", "salemanAccountId", "salemanLabel", "referencePartnerType",
      "referencePartnerId", "type", "label", "description", "shippingRoute", "volumeNote",
      "companySupport", "customerFeedback", "managerEvaluation"
    );
    BeanUtil.updateFieldsFromMap(this, sel, fields);
    return this;
  }

  public static PartnerEventHistory leadCreator(CustomerLeads lead) {
    PartnerEventHistory log = new PartnerEventHistory(lead);
    log.setTransactionDate(new Date());
    log.setType("CREATE");
    log.setLabel("New Lead Created");
    log.setReferencePartnerId(lead.getId());
    log.setReferencePartnerType(CustomerLeads.TABLE_NAME);
    log.setSalemanLabel(lead.getSalemanLabel());
    log.setSalemanAccountId(lead.getSalemanAccountId());
    log.setVolumeNote(lead.getVolumeNote());
    log.setShippingRoute(lead.getRouting());
    return log;
  }

  public static PartnerEventHistory partnerCreator(CRMPartner partner, CrmUserRole saleman) {
    PartnerEventHistory partnerEventHistory = new PartnerEventHistory(partner);
    partnerEventHistory.setTransactionDate(new Date());
    partnerEventHistory.setType("CREATE");
    partnerEventHistory.setSalemanAccountId(saleman.getAccountId());
    partnerEventHistory.setSalemanLabel(saleman.getFullName());
    partnerEventHistory.setLabel("Customer Converted from lead: " + partner.getLeadCode());
    partnerEventHistory.setReferencePartnerType(CRMPartner.TABLE_NAME);
    partnerEventHistory.setReferencePartnerId(partner.getId());
    return partnerEventHistory;
  }

}