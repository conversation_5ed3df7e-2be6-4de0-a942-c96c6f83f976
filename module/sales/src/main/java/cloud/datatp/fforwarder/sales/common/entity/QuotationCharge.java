package cloud.datatp.fforwarder.sales.common.entity;

import cloud.datatp.fforwarder.core.common.Purpose;
import cloud.datatp.fforwarder.core.common.TransportationMode;
import cloud.datatp.fforwarder.price.entity.AirTransportCharge;
import cloud.datatp.fforwarder.price.entity.SeaFclTransportCharge;
import cloud.datatp.fforwarder.price.entity.SeaLclTransportCharge;
import cloud.datatp.fforwarder.price.entity.TransportFrequency;
import cloud.datatp.fforwarder.sales.common.quote.CustomerAirPriceGroup;
import cloud.datatp.fforwarder.sales.common.quote.CustomerSeaFCLPriceGroup;
import cloud.datatp.fforwarder.sales.common.quote.CustomerSeaLCLPriceGroup;
import cloud.datatp.fforwarder.sales.booking.dto.SellingRate;
import cloud.datatp.fforwarder.sales.booking.entity.Booking;
import cloud.datatp.fforwarder.sales.common.ContainerType;
import cloud.datatp.fforwarder.sales.inquiry.entity.SpecificServiceInquiry;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.core.type.TypeReference;
import jakarta.persistence.Access;
import jakarta.persistence.AccessType;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import java.io.Serial;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.company.entity.CompanyEntity;
import net.datatp.util.dataformat.DataSerializer;
import net.datatp.util.ds.MapObject;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;

@Entity
@Table(name = QuotationCharge.TABLE_NAME)
@JsonInclude(Include.NON_NULL)
@Setter
@Getter
@NoArgsConstructor
public class QuotationCharge extends CompanyEntity {

  @Serial
  private static final long serialVersionUID = 1L;
  public static final String TABLE_NAME = "lgc_sales_quotation_charge";

  @Column(name = "specific_quotation_id")
  private Long specificQuotationId;

  @Column(name = "reference_code")
  private String referenceCode; // rate ref

  @Column(name = "is_confirm")
  private Boolean isConfirm;

  @Enumerated(EnumType.STRING)
  @Column(name = "purpose")
  private Purpose purpose = Purpose.EXPORT;

  @Enumerated(EnumType.STRING)
  @Column(name = "mode")
  private TransportationMode mode = TransportationMode.AIR;

  /* ------------ Port/ Airport/ Location -------------- */
  @Column(name = "from_location_code")
  private String fromLocationCode;

  @Column(name = "from_location_label")
  private String fromLocationLabel;

  @Column(name = "to_location_code")
  private String toLocationCode;

  @Column(name = "to_location_label")
  private String toLocationLabel;

  @Column(name = "final_destination")
  private String finalDestination;

  /* ------------ Agent/ Carrier/ Pricing Input Rate -------------- */
  @Column(name = "handling_agent_partner_label")
  private String handlingAgentPartnerLabel;

  @Column(name = "handling_agent_partner_id")
  private Long handlingAgentPartnerId;

  @Column(name = "carrier_label")
  private String carrierLabel;

  @Column(name = "carrier_partner_id")
  private Long carrierPartnerId;

  /* ------------ Rate Info-------------- */
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "effective_date")
  private Date effectiveDate;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "validity")
  private Date validity;

  @Column(name = "frequency")
  private String frequency;

  @Column(name = "transit_port")
  private String transitPort;

  @Column(name = "transit_time")
  private String transitTime;

  @Column(name = "free_time", length = 1024 * 4)
  private String freeTime;

  @Column(name = "note", length = 1024 * 4)
  private String note;

  /* ------------ Volume/ Weight/ Quantity -------------- */
  //TODO: Dan - consider to remove this field, follow by inquiry
  // for sea lcl freight
  @Column(name = "volume_in_cbm")
  private double volumeInCbm;

  //TODO: Dan - consider to remove this field, follow by inquiry
  // for air freight
  @Column(name = "chargeable_weight")
  private double chargeableWeight;

  /* ------------ Price Info -------------- */
  @Column(name = "ref_currency")
  private String refCurrency;

  @Column(name = "currency")
  private String currency;

  @Transient
  private MapObject priceGroup = new MapObject();

  @JsonIgnore
  @Access(AccessType.PROPERTY)
  @Basic(fetch = FetchType.EAGER)
  @Column(name = "price_group", length = 64 * 1024)
  public String getPriceGroupJson() {
    if (this.priceGroup == null) return null;
    return DataSerializer.JSON.toString(this.priceGroup);
  }

  public void setPriceGroupJson(String json) {
    if (StringUtil.isEmpty(json)) {
      this.priceGroup = null;
    } else {
      this.priceGroup = DataSerializer.JSON.fromString(json, new TypeReference<>() {
      });
    }
  }

  public String getCarrierLabel() {
    if (carrierLabel == null) return "";
    return carrierLabel;
  }

  public boolean isConfirm() {
    if (isConfirm == null) return false;
    return isConfirm;
  }

  public boolean getConfirm() {
    if (isConfirm == null) return false;
    return isConfirm;
  }

  public void setConfirm(Boolean confirm) {
    if (confirm == null) this.isConfirm = false;
    isConfirm = confirm;
  }

  public QuotationCharge(SeaLclTransportCharge price) {
    referenceCode = price.getCode();
    mode = TransportationMode.SEA_LCL;
    purpose = price.getPurpose();

    handlingAgentPartnerLabel = price.getHandlingAgentPartnerLabel();
    handlingAgentPartnerId = price.getHandlingAgentPartnerId();
    carrierLabel = price.getCarrierLabel();
    carrierPartnerId = price.getCarrierPartnerId();
    refCurrency = price.getCurrency();
    currency = price.getCurrency();
    effectiveDate = price.getValidFrom();
    validity = price.getValidTo();

    fromLocationCode = price.getFromLocationCode();
    fromLocationLabel = price.getFromLocationLabel();
    toLocationCode = price.getToLocationCode();
    toLocationLabel = price.getToLocationLabel();

    freeTime = price.getStuffingNote();

    note = Stream.of(
        Optional.ofNullable(price.getNote())
          .filter(StringUtil::isNotEmpty)
          .map(note -> note + "\n")
          .orElse(""),
        Optional.ofNullable(price.getSurchargeNote())
          .filter(StringUtil::isNotEmpty)
          .map(note -> note + "\n")
          .orElse(""),
        Optional.ofNullable(price.getLocalChargeAtDest())
          .filter(StringUtil::isNotEmpty)
          .map(note -> note + "\n")
          .orElse("")
      )
      .filter(StringUtil::isNotEmpty)
      .collect(Collectors.joining(""));

    List<TransportFrequency> frequencies = price.getTransportFrequencies();
    if (net.datatp.util.ds.Collections.isNotEmpty(frequencies)) {
      frequency = frequencies.get(0).getLabel();

      transitTime = StringUtil.join(new String[]{
        String.valueOf(frequencies.get(0).getTransitTime()),
        String.valueOf(frequencies.get(0).getTransitLabel())
      }, " ").trim();

    }

    volumeInCbm = 0;
    priceGroup = new CustomerSeaLCLPriceGroup(price.getPriceGroup(), 0).toMapObject();
  }

  public QuotationCharge(SeaFclTransportCharge price) {
    referenceCode = price.getCode();
    mode = TransportationMode.SEA_FCL;
    purpose = price.getPurpose();

    handlingAgentPartnerLabel = price.getHandlingAgentPartnerLabel();
    handlingAgentPartnerId = price.getHandlingAgentPartnerId();
    carrierLabel = price.getCarrierLabel();
    carrierPartnerId = price.getCarrierPartnerId();
    refCurrency = price.getCurrency();
    currency = price.getCurrency();
    effectiveDate = price.getValidFrom();
    validity = price.getValidTo();

    fromLocationCode = price.getFromLocationCode();
    fromLocationLabel = price.getFromLocationLabel();
    toLocationCode = price.getToLocationCode();
    toLocationLabel = price.getToLocationLabel();
    finalDestination = price.getFinalTerminalLocationLabel();

    freeTime = price.getFreeTime();

    note = Stream.of(
        Optional.ofNullable(price.getNote())
          .filter(StringUtil::isNotEmpty)
          .map(note -> note + "\n")
          .orElse(""),
        Optional.ofNullable(price.getRemarks())
          .filter(StringUtil::isNotEmpty)
          .map(note -> note + "\n")
          .orElse(""),
        Optional.ofNullable(price.getSurchargeNote())
          .filter(StringUtil::isNotEmpty)
          .map(note -> note + "\n")
          .orElse("")
      )
      .filter(StringUtil::isNotEmpty)
      .collect(Collectors.joining(""));

    List<TransportFrequency> frequencies = price.getTransportFrequencies();
    if (net.datatp.util.ds.Collections.isNotEmpty(frequencies)) {
      frequency = frequencies.get(0).getLabel();
      transitTime = StringUtil.join(new String[]{
        String.valueOf(frequencies.get(0).getTransitTime()),
        String.valueOf(frequencies.get(0).getTransitLabel())
      }, " ").trim();
    }
    priceGroup = new CustomerSeaFCLPriceGroup(price.getPriceGroup()).toMapObject();
  }

  public QuotationCharge(AirTransportCharge price) {
    referenceCode = price.getCode();
    mode = TransportationMode.AIR;
    purpose = price.getPurpose();

    handlingAgentPartnerLabel = price.getHandlingAgentPartnerLabel();
    handlingAgentPartnerId = price.getHandlingAgentPartnerId();
    carrierLabel = price.getCarrierLabel();
    carrierPartnerId = price.getCarrierPartnerId();
    refCurrency = price.getCurrency();
    currency = price.getCurrency();
    effectiveDate = price.getValidFrom();
    validity = price.getValidTo();

    fromLocationCode = price.getFromLocationCode();
    fromLocationLabel = price.getFromLocationLabel();
    toLocationCode = price.getToLocationCode();
    toLocationLabel = price.getToLocationLabel();

    note = Stream.of(
        Optional.ofNullable(price.getNote())
          .filter(StringUtil::isNotEmpty)
          .map(note -> note + "\n")
          .orElse(""))
      .filter(StringUtil::isNotEmpty)
      .collect(Collectors.joining(""));

    List<TransportFrequency> frequencies = price.getTransportFrequencies();
    if (net.datatp.util.ds.Collections.isNotEmpty(frequencies)) {
      frequency = frequencies.get(0).getLabel();
      transitTime = StringUtil.join(new String[]{
        String.valueOf(frequencies.get(0).getTransitTime()),
        String.valueOf(frequencies.get(0).getTransitLabel())
      }, " ").trim();
    }

    priceGroup = new CustomerAirPriceGroup(price.getPriceGroup()).toMapObject();
  }

  public QuotationCharge computeFromInquiry(SpecificServiceInquiry inquiry) {
    if (inquiry == null) return this;
    fromLocationLabel = inquiry.getFromLocationLabel();
    fromLocationCode = inquiry.getFromLocationCode();
    toLocationLabel = inquiry.getToLocationLabel();
    toLocationCode = inquiry.getToLocationCode();
    finalDestination = inquiry.getFinalDestination();
    return this;
  }

  @SuppressWarnings("unchecked")
  public QuotationCharge clearIds() {
    clearId(this);
    setCreatedBy(null);
    setCreatedTime(null);
    setModifiedBy(null);
    setModifiedTime(null);
    return this;
  }

  public static QuotationCharge fromBookingSellingRates(Booking booking, List<SellingRate> freightRates) {
    if (freightRates == null || freightRates.isEmpty() || booking == null || booking.getInquiry() == null) return null;

    SellingRate firstRate = freightRates.get(0);
    TransportationMode mode = firstRate.getGroup();
    SpecificServiceInquiry inquiry = booking.getInquiry();

    QuotationCharge quote = new QuotationCharge();
    quote.setMode(mode);
    quote.setPurpose(inquiry.getPurpose());
    quote.setFromLocationCode(inquiry.getFromLocationCode());
    quote.setFromLocationLabel(inquiry.getFromLocationLabel());
    quote.setToLocationCode(inquiry.getToLocationCode());
    quote.setToLocationLabel(inquiry.getToLocationLabel());
    quote.setFinalDestination(inquiry.getFinalDestination());
    quote.setEffectiveDate(new Date());
    quote.setValidity(new Date());
    quote.setConfirm(true);

    quote.setCurrency(firstRate.getCurrency());
    quote.setRefCurrency(firstRate.getCurrency());
    quote.setNote(firstRate.getNote());

    quote.setCarrierPartnerId(booking.getCarrierPartnerId());
    quote.setCarrierLabel(booking.getCarrierLabel());
    quote.setHandlingAgentPartnerId(booking.getHandlingAgentPartnerId());
    quote.setHandlingAgentPartnerLabel(booking.getHandlingAgentLabel());

    if (TransportationMode.isSeaFCLTransport(mode)) {
      createSeaFCLPriceGroup(quote, freightRates);
    } else if (TransportationMode.isSeaLCLTransport(mode)) {
      createSeaLCLPriceGroup(quote, freightRates);
    } else if (TransportationMode.AIR.equals(mode)) {
      createAirPriceGroup(quote, freightRates);
    }

    return quote;
  }

  private static void createSeaFCLPriceGroup(QuotationCharge quote, List<SellingRate> freightRates) {
    MapObject priceGroupMap = new MapObject();

    for (SellingRate rate : freightRates) {
      String containerUnit = rate.getUnit();
      if (StringUtil.isNotEmpty(containerUnit)) {
        ContainerType containerType = ContainerType.ContainerTypeUnit.match(containerUnit);
        if (containerType != null) {
          String priceLevel = containerType.toFCLPriceLevel();
          priceGroupMap.put(priceLevel, rate.getUnitPrice());
        }
      }
    }

    quote.setPriceGroup(priceGroupMap);
  }

  private static void createSeaLCLPriceGroup(QuotationCharge quote, List<SellingRate> freightRates) {
    if (!freightRates.isEmpty()) {
      SellingRate rate = freightRates.get(0);
      MapObject priceGroupMap = new MapObject();
      priceGroupMap.put("selectedPrice", rate.getUnitPrice());
      priceGroupMap.put("quantity", rate.getQuantity());
      quote.setPriceGroup(priceGroupMap);
    }
  }

  private static void createAirPriceGroup(QuotationCharge quote, List<SellingRate> freightRates) {
    if (!freightRates.isEmpty()) {
      SellingRate rate = freightRates.get(0); 
      MapObject priceGroupMap = new MapObject();
      priceGroupMap.put("selectedPrice", rate.getUnitPrice());
      quote.setPriceGroup(priceGroupMap);
    }
  }
}