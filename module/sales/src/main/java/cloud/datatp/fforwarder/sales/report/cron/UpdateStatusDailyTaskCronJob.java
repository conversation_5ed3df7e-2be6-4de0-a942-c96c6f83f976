package cloud.datatp.fforwarder.sales.report.cron;

import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Set;
import net.datatp.lib.executable.ExecutableContext;
import net.datatp.module.app.AppEnv;
import net.datatp.module.bot.BotEvent;
import net.datatp.module.bot.BotService;
import net.datatp.module.bot.cron.CronJob;
import net.datatp.module.bot.cron.CronJobFrequency;
import net.datatp.module.bot.cron.CronJobLogger;
import net.datatp.module.bot.task.TaskUnitBotEvent;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.monitor.SourceType;
import net.datatp.security.client.ClientContext;
import net.datatp.security.client.DeviceInfo;
import net.datatp.security.client.DeviceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class UpdateStatusDailyTaskCronJob extends CronJob {

  @Autowired
  private AppEnv appEnv;

  @Autowired
  private BotService botService;

  public UpdateStatusDailyTaskCronJob() {
    super("task-request:update-status:weekly-sunday", "Update Status Task Request Cron Job on Monday");
  }

  @PostConstruct
  public void onInit() {
    if (appEnv.isDevEnv()) {
      setFrequencies(CronJobFrequency.NONE);
    } else {
      setFrequencies(CronJobFrequency.EVERY_WEEK_MONDAY_07_AM);
    }
  }

  @Override
  protected List<ICompany> getTargetCompanies() {
    List<ICompany> companies = new ArrayList<>();
    ICompany company = ICompany.SYSTEM;
    companies.add(company);
    return companies;
  }

  @Override
  protected ClientContext getClientContext(ICompany company) {
    ClientContext client = new ClientContext("default", "dan", "localhost");
    if (appEnv.isProdEnv()) {
      client.setDeviceInfo(new DeviceInfo(DeviceType.Server));
    } else {
      client.setDeviceInfo(new DeviceInfo(DeviceType.Computer));
    }
    client.setCompany(company);
    client.setAccountId(3L);
    return client;
  }

  protected Set<String> getReportToUsers(ClientContext client, ICompany company) {
    Set<String> userSet = super.getReportToUsers(client, company);
    userSet.add("dan");
    return userSet;
  }

  @Override
  protected void run(ClientContext client, ICompany company, CronJobLogger logger) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    Calendar now = Calendar.getInstance();
    Date endTime = now.getTime();

    now.add(Calendar.WEEK_OF_YEAR, -1);
    now.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
    now.set(Calendar.HOUR_OF_DAY, 0);
    now.set(Calendar.MINUTE, 0);
    now.set(Calendar.SECOND, 0);
    now.set(Calendar.MILLISECOND, 0);
    Date startTime = now.getTime();

    ExecutableContext ctx =
      new ExecutableContext(client, company)
        .withScriptEnv(
          scriptDir,
          TaskCalendarLogicExecutor.class,
          TaskCalendarLogicExecutor.UpdateStatusTaskCalendar.class)
        .withParam("startTime", startTime)
        .withParam("endTime", endTime);

    BotEvent<?> botEvent =
      new TaskUnitBotEvent(client, company, ctx)
        .withProcessMode(BotEvent.ProcessMode.Queueable)
        .withReportToUsers(getReportToUsers(client, company));
    botService.broadcast(SourceType.UserBot, botEvent);
  }
}