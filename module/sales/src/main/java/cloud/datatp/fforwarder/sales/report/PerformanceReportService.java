package cloud.datatp.fforwarder.sales.report;

import java.util.List;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.service.BaseComponent;
import net.datatp.security.client.ClientContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j @Getter
@Service("PerformanceReportService")
public class PerformanceReportService extends BaseComponent {

  @Autowired
  private PerformanceReportLogic performanceReportLogic;

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchVolumePerformanceBySalemanReport(ClientContext client, SqlQueryParams sqlParams) {
    return performanceReportLogic.searchVolumePerformanceBySalemanReport(client, sqlParams);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchVolumePerformanceByCompany(ClientContext client, SqlQueryParams sqlParams) {
    return performanceReportLogic.searchVolumePerformanceByCompany(client, sqlParams);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchVolumePerformanceByShipment(ClientContext client, SqlQueryParams sqlParams) {
    return performanceReportLogic.searchVolumePerformanceByShipment(client, sqlParams);
  }
  
  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchVolumePerformanceByHouseBill(ClientContext client, SqlQueryParams sqlParams) {
    return performanceReportLogic.searchVolumePerformanceByHouseBill(client, sqlParams);
  }

  // ---------------------- Customer/ Customer Lead Report----------------------
  @Transactional(value = "crmTransactionManager", readOnly = true)
  public List<SqlMapRecord> saleAccountReport(ClientContext client, SqlQueryParams sqlParams) {
    return performanceReportLogic.saleAccountReport(client, sqlParams);
  }

  @Transactional(value = "crmTransactionManager", readOnly = true)
  public List<SqlMapRecord> salemanSystemPerformanceReport(ClientContext client, SqlQueryParams sqlParams) {
    return performanceReportLogic.salemanSystemPerformanceReport(client, sqlParams);
  }

  @Transactional(value = "crmTransactionManager", readOnly = true)
  public List<SqlMapRecord> saleConversationRateReport(ClientContext client, SqlQueryParams sqlParams) {
    return performanceReportLogic.saleConversationRateReport(client, sqlParams);
  }

}