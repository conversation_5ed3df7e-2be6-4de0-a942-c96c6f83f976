package cloud.datatp.fforwarder.sales.integration;

import cloud.datatp.fforwarder.core.message.CRMMessageLogic;
import cloud.datatp.fforwarder.core.message.entity.CRMMessageSystem;
import cloud.datatp.fforwarder.core.partner.CRMPartnerLogic;
import cloud.datatp.fforwarder.core.partner.PartnerRequestLogic;
import cloud.datatp.fforwarder.core.partner.entity.BFSOnePartnerGroup;
import cloud.datatp.fforwarder.core.partner.entity.CRMPartner;
import cloud.datatp.fforwarder.core.partner.entity.PartnerRequest;
import cloud.datatp.fforwarder.core.partner.plugin.PartnerServicePlugin;
import cloud.datatp.fforwarder.core.template.CRMUserRoleLogic;
import cloud.datatp.fforwarder.core.template.entity.CrmUserPermissionTemplate;
import cloud.datatp.fforwarder.core.template.entity.CrmUserRole;
import cloud.datatp.fforwarder.sales.partner.CustomerLeadsLogic;
import cloud.datatp.fforwarder.sales.partner.entity.CustomerLeads;
import cloud.datatp.fforwarder.sales.partner.entity.PartnerEventHistory;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class WorkflowPartnerServicePlugin extends PartnerServicePlugin {

  public WorkflowPartnerServicePlugin() {
    super("workflow-partner");
  }

  @Autowired
  private CRMUserRoleLogic crmUserRoleLogic;

  @Autowired
  private PartnerRequestLogic partnerRequestLogic;

  @Autowired
  private CustomerLeadsLogic customerLeadsLogic;

  @Autowired
  private CRMPartnerLogic crmPartnerLogic;
  
  @Autowired
  private CRMMessageLogic crmMessageLogic;

  public void onPreSave(ClientContext client, CRMPartner partner, boolean isNew) {
    log.info("Workflow-partner: onPreSave called for partner: {}, isNew: {}", partner.getId(), isNew);
  }

  public void onPostSave(ClientContext client, CRMPartner partner, boolean isNew) {
    log.info("Workflow-partner: onPostSave called for partner: {}, isNew: {}", partner.getId(), isNew);

    CrmUserRole saleman = crmUserRoleLogic.getByAccountId(client, client.getAccountId());
    Objects.assertNotNull(saleman, "Creator Employee {} is not found", client.getRemoteUser());

    if (isNew) {
      String leadCode = partner.getLeadCode();
      PartnerEventHistory createdEvent = PartnerEventHistory.partnerCreator(partner, saleman);
      if (StringUtil.isNotEmpty(leadCode)) {
        CustomerLeads lead = customerLeadsLogic.getCustomerLeadByCode(client, leadCode);
        Objects.assertNotNull(lead, "Lead not found: leadCode = " + leadCode);
        createdEvent.setLabel("Customer Converted from lead: " + leadCode);
      }
      customerLeadsLogic.savePartnerEventHistory(client, createdEvent);
    }
    
    BFSOnePartnerGroup partnerGroup = partner.getPartnerGroup();
    PartnerRequest req = new PartnerRequest(partner);
    req.withRequestBy(partner.getRequestSalemanAccountId(), partner.getRequestSalemanLabel());

    if(saleman.isOverseas()) {
      log.info("Skip create partner request for Overseas user type");
      req.setStatus(PartnerRequest.PartnerRequestStatus.APPROVED);
      req.setApprovedByAccountId(saleman.getAccountId());
      req.setApprovedByLabel(saleman.getFullName());
      req.setApprovedDate(new Date());
      req.setApprovedNote("Auto approved for OVERSEAS user");
      PartnerRequest partnerRequest = partnerRequestLogic.savePartnerRequest(client, req);
      partnerRequestLogic.updatePartnerRequestStatus(client, partnerRequest);
      partner.setInputUsername("ADMIN");
      crmPartnerLogic.save(client, partner);
    } else {
      CrmUserPermissionTemplate approver = crmPartnerLogic.getPartnerRequestApprover(client, partnerGroup, saleman.getCompanyBranchCode());
      if (approver != null) {
        CrmUserRole approverUserRole = crmUserRoleLogic.getByAccountId(client, approver.getAccountId());
        Objects.assertNotNull(approverUserRole, "CrmUserRole is not found by accountId = {}", approverUserRole.getAccountId());
        req.withRequestBy(saleman.getAccountId(), saleman.getFullName());
        req.withApprovedBy(approverUserRole.getAccountId(), approverUserRole.getFullName());
        req.setTo(approverUserRole.getEmail());
        req = partnerRequestLogic.savePartnerRequest(client, req);
        CRMMessageSystem crmMessageSystem = req.toRequestMailMessage(client, partnerGroup);
        crmMessageLogic.scheduleMessage(client, crmMessageSystem);
      }
    }

  }

}