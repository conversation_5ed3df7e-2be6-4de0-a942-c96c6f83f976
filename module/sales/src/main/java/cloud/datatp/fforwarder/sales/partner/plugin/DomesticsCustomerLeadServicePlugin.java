package cloud.datatp.fforwarder.sales.partner.plugin;

import cloud.datatp.fforwarder.core.template.CRMUserRoleLogic;
import cloud.datatp.fforwarder.core.template.entity.CrmUserRole;
import cloud.datatp.fforwarder.sales.integration.BFSOneCRMLogic;
import cloud.datatp.fforwarder.sales.partner.CustomerLeadsLogic;
import cloud.datatp.fforwarder.sales.partner.entity.CustomerLeads;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.communication.CommunicationMessageService;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class DomesticsCustomerLeadServicePlugin extends CustomerLeadServicePlugin {

  @Autowired
  CommunicationMessageService service;

  @Autowired
  CustomerLeadsLogic customerLeadsLogic;

  @Autowired
  private BFSOneCRMLogic bfsOneCRMLogic;

  public DomesticsCustomerLeadServicePlugin() {
    super("domestics-customer-lead");
  }

  @Autowired
  private CRMUserRoleLogic crmUserRoleLogic;

  @Override
  public void onPreSave(ClientContext client, CustomerLeads lead, boolean isNew) {
    CrmUserRole saleman = crmUserRoleLogic.getByAccountId(client, client.getAccountId());
    Objects.assertNotNull(saleman, "Creator Employee {} is not found", client.getRemoteUser());

    if(!saleman.isOverseas()) {
      log.info("Domestics-partner: onPreSave called for partner: {}, isNew: {}", lead.getId(), isNew);

    }
  }

  @Override
  public void onPostSave(ClientContext client, CustomerLeads lead, boolean isNew) {
    CrmUserRole saleman = crmUserRoleLogic.getByAccountId(client, client.getAccountId());
    Objects.assertNotNull(saleman, "Creator Employee {} is not found", client.getRemoteUser());

    if(!saleman.isOverseas()) {
      log.info("Domestics-partner: onPostSave called for partner: {}, isNew: {}", lead.getId(), isNew);
      try {
        MapObject bfsOneCustomerLead = bfsOneCRMLogic.saveBFSOneCustomerLead(client, lead);
        lead.setBfsoneLeadCode(bfsOneCustomerLead.getString("ContactID", null));
        if (isNew) lead.setCode(bfsOneCustomerLead.getString("ContactID", null));
        customerLeadsLogic.getLeadsRepo().save(lead);
      } catch(Exception ex) {
        log.error("Failed to sync lead to BFSOne, lead = " + lead, ex);
      }
    }

  }

}