package cloud.datatp.fforwarder.sales.booking;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cloud.datatp.fforwarder.core.common.ChargeType;
import cloud.datatp.fforwarder.core.common.TransportationMode;
import cloud.datatp.fforwarder.core.db.CRMDaoService;
import cloud.datatp.fforwarder.core.partner.CRMPartnerLogic;
import cloud.datatp.fforwarder.core.partner.entity.CRMPartner;
import cloud.datatp.fforwarder.core.template.CRMUserRoleLogic;
import cloud.datatp.fforwarder.core.template.entity.CrmUserRole;
import cloud.datatp.fforwarder.price.InquiryRequestLogic;
import cloud.datatp.fforwarder.price.entity.InquiryRequest;
import cloud.datatp.fforwarder.sales.booking.dto.SellingRate;
import cloud.datatp.fforwarder.sales.booking.dto.SellingRate.Type;
import cloud.datatp.fforwarder.sales.booking.entity.Booking;
import cloud.datatp.fforwarder.sales.booking.entity.BookingAirTransportCharge;
import cloud.datatp.fforwarder.sales.booking.entity.BookingCustomClearance;
import cloud.datatp.fforwarder.sales.booking.entity.BookingSeaTransportCharge;
import cloud.datatp.fforwarder.sales.booking.entity.BookingTruckTransportCharge;
import cloud.datatp.fforwarder.sales.booking.repository.BookingRepository;
import cloud.datatp.fforwarder.sales.common.ContainerType;
import cloud.datatp.fforwarder.sales.common.CustomerChargeLogic;
import cloud.datatp.fforwarder.sales.common.entity.CustomerAdditionalCharge;
import cloud.datatp.fforwarder.sales.common.entity.CustomerAirTransportAdditionalCharge;
import cloud.datatp.fforwarder.sales.common.entity.CustomerAirTransportCommissionDistribution;
import cloud.datatp.fforwarder.sales.common.entity.CustomerCommissionDistribution;
import cloud.datatp.fforwarder.sales.common.entity.CustomerSeaTransportAdditionalCharge;
import cloud.datatp.fforwarder.sales.common.entity.CustomerSeaTransportCommissionDistribution;
import cloud.datatp.fforwarder.sales.common.entity.QuotationAdditionalCharge;
import cloud.datatp.fforwarder.sales.common.entity.QuotationCharge;
import cloud.datatp.fforwarder.sales.common.repository.BookingAirTransportChargeRepository;
import cloud.datatp.fforwarder.sales.common.repository.BookingCustomClearanceRepository;
import cloud.datatp.fforwarder.sales.common.repository.BookingSeaTransportChargeRepository;
import cloud.datatp.fforwarder.sales.common.repository.BookingTruckTransportChargeRepository;
import cloud.datatp.fforwarder.sales.common.repository.CustomerAirTransportAdditionalChargeRepository;
import cloud.datatp.fforwarder.sales.common.repository.CustomerAirTransportCommissionDistributionRepository;
import cloud.datatp.fforwarder.sales.common.repository.CustomerSeaTransportAdditionalChargeRepository;
import cloud.datatp.fforwarder.sales.common.repository.CustomerSeaTransportCommissionDistributionRepository;
import cloud.datatp.fforwarder.sales.inquiry.entity.SpecificServiceInquiry;
import cloud.datatp.fforwarder.sales.integration.BFSOneCRMLogic;
import cloud.datatp.fforwarder.sales.quotation.SpecificQuotationLogic;
import cloud.datatp.fforwarder.sales.quotation.dto.LocalCharge;
import cloud.datatp.fforwarder.sales.quotation.entity.SpecificQuotation;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.data.db.util.DBConnectionUtil;
import net.datatp.module.data.db.util.DeleteGraphBuilder;
import net.datatp.security.client.ClientContext;
import net.datatp.util.dataformat.DataSerializer;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import net.datatp.util.error.RuntimeError;
import net.datatp.util.text.StringUtil;

@Component
public class BookingLogic extends CRMDaoService {

  @Autowired
  private BookingRepository bookingRepo;

  @Autowired
  private BookingSeaTransportChargeRepository seaChargeRepo;

  @Autowired
  private BookingAirTransportChargeRepository airChargeRepo;

  @Autowired
  private BookingTruckTransportChargeRepository truckChargeRepo;

  @Autowired
  private BookingCustomClearanceRepository ccRepo;

  @Autowired
  private CustomerSeaTransportAdditionalChargeRepository seaAdditionalChargeRepo;

  @Autowired
  private CustomerSeaTransportCommissionDistributionRepository seaCommissionRepo;

  @Autowired
  private CustomerAirTransportAdditionalChargeRepository airAdditionalChargeRepo;

  @Autowired
  private CustomerAirTransportCommissionDistributionRepository airCommissionRepo;

  @Autowired
  private BFSOneCRMLogic bfsOneCRMLogic;

  @Autowired
  private CustomerChargeLogic customerChargeLogic;

  @Autowired
  private SpecificQuotationLogic sQuotationLogic;

  @Autowired
  private InquiryRequestLogic requestLogic;

  @Autowired
  private CRMUserRoleLogic crmUserRoleLogic;

  @Autowired
  private CRMPartnerLogic crmPartnerLogic;

  public Booking sendBFSOneIBooking(ClientContext client, Booking booking) {
    try {
      Booking updatedBooking = bfsOneCRMLogic.createInternalBookingNew(client, booking);
      try {
        booking = saveBooking(client, client.getCompany(), updatedBooking);
      } catch(Exception ex) {

      }

      // * update status request to won
      SpecificServiceInquiry inquiry = booking.getInquiry();
      String referenceCode = inquiry.getReferenceCode();
      InquiryRequest inquiryRequest = requestLogic.getInquiryRequest(client, client.getCompany(), referenceCode);
      Objects.assertNotNull(inquiryRequest, "InquiryRequest is not found, referenceCode = " + referenceCode);
      inquiryRequest.setStatus(InquiryRequest.InquiryStatus.SUCCESS);
      requestLogic.saveInquiryRequest(client, client.getCompany(), inquiryRequest);
      return booking;
    } catch (Exception e) {
      throw RuntimeError.UnknownError(e.getMessage());
    }
  }

  public Booking resendBFSOneIBooking(ClientContext client, Booking booking) {
    try {
      SpecificServiceInquiry inquiry = booking.getInquiry();
      SpecificServiceInquiry newInquiry = DataSerializer.JSON.clone(inquiry);

      newInquiry.clearIds();
      newInquiry.setRequestDate(new Date());
      newInquiry.setReferenceCode(null);
      newInquiry.setCargoReadyDate(booking.getPlanTimeArrival());
      newInquiry.setEstimatedTimeDeparture(booking.getPlanTimeDeparture());

      Long sQuotationChargeId = booking.getSpecificQuotationChargeId();
      QuotationCharge quotationCharge = null;
      QuotationCharge savedQuote = null;
      if(sQuotationChargeId != null) {
        quotationCharge = customerChargeLogic.getQuotationChargeById(client, client.getCompany(), sQuotationChargeId);
      }

      if (quotationCharge == null) {
        QuotationCharge newQuote = new QuotationCharge();
        newQuote.setReferenceCode(null);
        newQuote.setConfirm(true);
        newQuote.setValidity(new Date());
        newQuote.setEffectiveDate(new Date());
        newQuote.setMode(newInquiry.getMode());
        newQuote.setPurpose(newInquiry.getPurpose());
        newQuote.setFromLocationCode(newInquiry.getFromLocationCode());
        newQuote.setFromLocationLabel(newInquiry.getFromLocationLabel());
        newQuote.setToLocationCode(newInquiry.getToLocationCode());
        newQuote.setToLocationLabel(newInquiry.getToLocationLabel());
        newQuote.setFinalDestination(newInquiry.getFinalDestination());

        SpecificQuotation newQuotation = new SpecificQuotation();
        newQuotation.setInquiry(newInquiry);

        // Create QuotationCharge for Sea/Air freight from booking.sellingRates
        List<SellingRate> freightRates = booking.getSellingRates().stream()
            .filter(rate -> rate.getType() == Type.SEAFREIGHT || rate.getType() == Type.AIRFREIGHT)
            .collect(Collectors.toList());

        List<QuotationCharge> quoteCharges = new ArrayList<>();
        if (!freightRates.isEmpty()) {
          // Create QuotationCharge from freight rates
          QuotationCharge freightQuote = createQuotationChargeFromSellingRates(newInquiry, freightRates);
          if (freightQuote != null) {
            quoteCharges.add(freightQuote);
          }
        }

        // If no freight rates found, use the default quote
        if (quoteCharges.isEmpty()) {
          quoteCharges.add(newQuote);
        }

        newQuotation.setQuoteList(quoteCharges);

        // Handle local charges (LOCAL_CHARGE, TRUCKING, CUSTOM)
        List<SellingRate> allLocalChargeRates = booking.getSellingRates().stream()
            .filter(rate -> rate.getType() == Type.LOCAL_CHARGE || rate.getType() == Type.TRUCKING || rate.getType() == Type.CUSTOM)
            .collect(Collectors.toList());

        if (!allLocalChargeRates.isEmpty()) {
          List<QuotationAdditionalCharge> addCharges = QuotationAdditionalCharge.computeFromLocalChargeSellingRates(allLocalChargeRates);
          List<LocalCharge> localCharges = LocalCharge.convertToLocalCharge(addCharges);
          newQuotation.setLocalHandlingCharges(localCharges);
        }

        DataSerializer.JSON.dump(newQuotation);

        SpecificQuotation savedQuotation = sQuotationLogic.saveSpecificQuotation(client, client.getCompany(), newQuotation);
        List<QuotationCharge> quoteList = customerChargeLogic.findQuotationChargeBySQuotationId(client.getCompany(), savedQuotation.getId());
        savedQuote = quoteList.stream().findFirst().get();
      } else {
//        QuotationCharge newQuote = DataSerializer.JSON.clone(quotationCharge);
//        newQuote.clearIds();
//        newQuote.setReferenceCode(null);
//        newQuote.setConfirm(true);
//        newQuote.setValidity(new Date());
//
//        SpecificQuotation quotation = sQuotationLogic.getById(client, client.getCompany(), quotationCharge.getSpecificQuotationId());
//        Objects.assertNotNull(quotation, "SpecificQuotation is not found, id = " + quotationCharge.getSpecificQuotationId());
//
//        SpecificQuotation newQuotation = DataSerializer.JSON.clone(quotation);
//        newQuotation.clearIds();
//        newQuotation.setInquiry(newInquiry);
//        newQuotation.setQuoteList(Arrays.asList(newQuote));
//        SpecificQuotation savedQuotation = sQuotationLogic.saveSpecificQuotation(client, client.getCompany(), newQuotation);
//
//        List<QuotationCharge> quoteList = customerChargeLogic.findQuotationChargeBySQuotationId(client.getCompany(), savedQuotation.getId());
//        savedQuote = quoteList.stream().findFirst().get();
      }

//      SpecificServiceInquiry newBookingInquiry = DataSerializer.JSON.clone(newInquiry);
//      newBookingInquiry.clearIds();
//
//      Booking newBooking = DataSerializer.JSON.clone(booking);
//      newBooking.setInquiry(newBookingInquiry);
//      newBooking.setBfsoneReference(null);
//      newBooking.setId(null);
//      newBooking.setSpecificQuotationChargeId(savedQuote.getId());
//      return sendBFSOneIBooking(client, newBooking);
      return null;
    } catch (Exception e) {
      throw RuntimeError.UnknownError(e.getMessage());
    }
  }

  public Booking newBooking(ClientContext client, Booking booking) {
    CrmUserRole saleman = crmUserRoleLogic.getByAccountId(client, client.getAccountId());
    Objects.assertNotNull(saleman, "Saleman is not found, account id = " + client.getAccountId());

    booking.setSenderAccountId(saleman.getAccountId());
    booking.setSenderLabel(saleman.getFullName());
    booking.setSenderBFSOneCode(saleman.getBfsoneCode());

    for (SellingRate sellingRate : booking.getSellingRates()) {
      if (sellingRate.getPayerPartnerId() != null) {
        CRMPartner payer = crmPartnerLogic.getById(client, sellingRate.getPayerPartnerId());
        Objects.assertNotNull(payer, "CRMPartner is not found by id = {}", payer);
        sellingRate.setPayerPartnerCode(payer.getPartnerCode());
      }
    }

    return booking;
  }





  /* ------------ GET, LOAD METHOD -------------- */
  public Booking getBooking(ClientContext client, Long bookingId) {
    Booking booking = bookingRepo.findById(bookingId).orElse(null);
    Objects.assertNotNull(booking, "Booking is not found!!!, id = " + bookingId);

    SpecificServiceInquiry inquiry = booking.getInquiry();
    List<SellingRate> holder = new ArrayList<>();

    if (booking.getChargeId() != null) {
      if (booking.getChargeType() == ChargeType.SEA) {
        BookingSeaTransportCharge seaCharge = seaChargeRepo.getById(booking.getChargeId());
        Objects.assertNotNull(seaCharge, "BookingSeaCharge is not found by id = {}", booking.getChargeId());
        List<CustomerSeaTransportAdditionalCharge> addCharges = seaAdditionalChargeRepo.findByBookingSeaChargeId(seaCharge.getId());
        seaCharge.setTemporaryAdditionalCharges(addCharges);
        List<SellingRate> sellingRates = SellingRate.sellingRateCreator(inquiry.getContainers(), seaCharge);
        holder.addAll(sellingRates);

      } else if (booking.getChargeType() == ChargeType.AIR) {
        BookingAirTransportCharge airCharge = airChargeRepo.getById(booking.getChargeId());
        Objects.assertNotNull(airCharge, "BookingAirCharge is not found by id = {}", booking.getChargeId());
        List<CustomerAirTransportAdditionalCharge> addCharges = airAdditionalChargeRepo.findByBookingAirChargeId(airCharge.getId());
        airCharge.setTemporaryAdditionalCharges(addCharges);
        List<SellingRate> sellingRates = SellingRate.sellingRateCreator(airCharge);
        holder.addAll(sellingRates);
      }
    }

    List<BookingTruckTransportCharge> truckCharges = truckChargeRepo.findByBookingId(booking.getId());
    List<SellingRate> truckingRates = SellingRate.sellingRateTruckingCreator(truckCharges);
    holder.addAll(truckingRates);

    List<BookingCustomClearance> customClearances = ccRepo.findByBookingId(booking.getId());
    List<SellingRate> customRates = SellingRate.sellingRateCustomCreator(customClearances);
    holder.addAll(customRates);
    booking.setSellingRates(holder);
    return booking;
  }

  public Booking saveBooking(ClientContext client, ICompany company, Booking booking) {
    QuotationCharge quoteCharge = customerChargeLogic.getQuotationChargeById(client, company, booking.getSpecificQuotationChargeId());

    SpecificServiceInquiry inquiry = booking.getInquiry();
    Long clientPartnerId = inquiry.getClientPartnerId();
    Objects.assertNotNull(clientPartnerId, "Client must be not null");
    inquiry.computeTypeOfService();

    if (booking.getChargeType() == ChargeType.SEA) {
      BookingSeaTransportCharge seaCharge = null;
      if (booking.getChargeId() != null) {
        seaCharge = seaChargeRepo.getById(booking.getChargeId());
        Objects.assertNotNull(seaCharge, "Ocean Freight is not found by id = {}", booking.getChargeId());
      }
      seaCharge = booking.computeSeaCharge(seaCharge, quoteCharge);
      seaCharge = saveBookingSeaCharge(client, seaCharge);
      booking.setChargeId(seaCharge.getId());
    } else if (booking.getChargeType() == ChargeType.AIR) {
      BookingAirTransportCharge airCharge = null;
      if (booking.getChargeId() != null) {
        airCharge = airChargeRepo.getById(booking.getChargeId());
        Objects.assertNotNull(airCharge, "Air Freight is not found by id = {}", booking.getChargeId());
      }
      airCharge = booking.computeAirCharge(airCharge, quoteCharge);
      airCharge = saveBookingAirCharge(client, airCharge);
      booking.setChargeId(airCharge.getId());
    } else if (booking.getChargeType() == ChargeType.NONE) {
    } else {
      throw RuntimeError.UnknownError("Not support yet!!!");
    }

    booking.set(client, company);
    booking = bookingRepo.save(booking);

    if (booking.getId() != null) {
      deleteTruckCharges(client, booking.getId());
      deleteCustomClearances(client, booking.getId());

      TransportationMode mode = inquiry.getMode();
      Map<TransportationMode, List<SellingRate>> sellingRateByGroup = booking.getSellingRates().stream()
        .collect(Collectors.groupingBy(SellingRate::getGroup));

      List<SellingRate> freightRates = sellingRateByGroup.getOrDefault(mode, new ArrayList<>());
      Map<Type, List<SellingRate>> sellingGroup = freightRates.stream().collect(Collectors.groupingBy(SellingRate::getType));

      List<SellingRate> truckingRates = sellingGroup.getOrDefault(Type.TRUCKING, new ArrayList<>());
      List<BookingTruckTransportCharge> truckCharges = booking.computeTruckTransportCharges(truckingRates);
      saveTruckTransportCharges(client, booking.getId(), truckCharges);

      List<SellingRate> customRates = sellingGroup.getOrDefault(Type.CUSTOM, new ArrayList<>());
      List<BookingCustomClearance> customClearances = booking.computeCustomClearances(customRates);
      saveCustomClearances(client, booking.getId(), customClearances);
    }

    return booking;
  }

  public List<SqlMapRecord> searchBookings(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    sqlParams.addParam("companyId", company.getId());
    sqlParams.addParam("accessAccountId", client.getAccountId());
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/sales/groovy/BookingSql.groovy";
    return searchDbRecords(client, scriptDir, scriptFile, "SearchBooking", sqlParams);
  }

  List<Booking> findBookingsInTimeRange(ClientContext client, Date startTime, Date endTime) {
    return bookingRepo.findBookingsInTimeRange(startTime, endTime);
  }

  public boolean changeBookingStorageState(ClientContext client, ChangeStorageStateRequest req) {
    bookingRepo.setStorageState(req.getNewStorageState(), req.getEntityIds());
    return true;
  }

  public int deleteBookings(ClientContext client, ICompany company, List<Long> bookingIds) {
    for (Long bookingId : bookingIds) {
      Booking booking = getBooking(client, bookingId);
      if (booking == null) continue;
      if (booking.getChargeId() != null) {
        if (booking.getChargeType() == ChargeType.SEA) deleteSeaCharges(client, booking.getChargeId());
        else if (booking.getChargeType() == ChargeType.AIR) deleteAirCharges(client, booking.getChargeId());
        //        else if (booking.getChargeType() == ChargeType.RAIL)
      }

      String caseReference = booking.getBfsoneReference();
      if (StringUtil.isNotEmpty(caseReference)) {
        bfsOneCRMLogic.deleteInternalBooking(client, caseReference);
      }
    }
    DBConnectionUtil connectionUtil = getDBConnectionUtil();
    DeleteGraphBuilder deleteGraphBuilder = new DeleteGraphBuilder(connectionUtil, company.getId(), Booking.class, bookingIds);
    int count = deleteGraphBuilder.runDelete();
    connectionUtil.commit();
    connectionUtil.close();
    return count;
  }

  private BookingSeaTransportCharge saveBookingSeaCharge(ClientContext client, BookingSeaTransportCharge charge) {
    if (charge.getId() != null) {
      seaAdditionalChargeRepo.deleteByBookingSeaChargeId(charge.getId());
      seaCommissionRepo.deleteByBookingSeaChargeId(charge.getId());
    }
    charge.set(client, client.getCompany());
    BookingSeaTransportCharge savedCharge = seaChargeRepo.save(charge);

    List<? extends CustomerAdditionalCharge> additionalCharges = charge.getTemporaryAdditionalCharges();
    if (additionalCharges != null && !additionalCharges.isEmpty()) {
      for (CustomerAdditionalCharge addCharge : additionalCharges) {
        if (addCharge instanceof CustomerSeaTransportAdditionalCharge seaAddCharge) {
          seaAddCharge.setBookingSeaChargeId(savedCharge.getId());
          seaAddCharge.set(client, client.getCompany());
          seaAdditionalChargeRepo.save(seaAddCharge);
        }
      }
    }

    List<? extends CustomerCommissionDistribution> commissions = charge.getTemporaryCommissionDistributions();
    if (commissions != null && !commissions.isEmpty()) {
      for (CustomerCommissionDistribution commission : commissions) {
        if (commission instanceof CustomerSeaTransportCommissionDistribution seaCommission) {
          seaCommission.setBookingSeaChargeId(savedCharge.getId());
          seaCommission.set(client, client.getCompany());
          seaCommissionRepo.save(seaCommission);
        }
      }
    }
    return savedCharge;
  }

  private BookingAirTransportCharge saveBookingAirCharge(ClientContext client, BookingAirTransportCharge charge) {
    if (charge.getId() != null) {
      airAdditionalChargeRepo.deleteByBookingAirChargeId(charge.getId());
      airCommissionRepo.deleteByBookingAirChargeId(charge.getId());
    }

    charge.set(client, client.getCompany());
    BookingAirTransportCharge savedCharge = airChargeRepo.save(charge);

    List<? extends CustomerAdditionalCharge> additionalCharges = charge.getTemporaryAdditionalCharges();
    if (additionalCharges != null && !additionalCharges.isEmpty()) {
      for (CustomerAdditionalCharge addCharge : additionalCharges) {
        if (addCharge instanceof CustomerAirTransportAdditionalCharge airAddCharge) {
          airAddCharge.setBookingAirChargeId(savedCharge.getId());
          airAddCharge.set(client, client.getCompany());
          airAdditionalChargeRepo.save(airAddCharge);
        }
      }
    }

    List<? extends CustomerCommissionDistribution> commissions = charge.getCommissionDistributions();
    if (commissions != null && !commissions.isEmpty()) {
      for (CustomerCommissionDistribution commission : commissions) {
        if (commission instanceof CustomerAirTransportCommissionDistribution airCommission) {
          airCommission.setBookingAirChargeId(savedCharge.getId());
          airCommission.set(client, client.getCompany());
          airCommissionRepo.save(airCommission);
        }
      }
    }

    return savedCharge;
  }

  private void saveTruckTransportCharges(ClientContext client, Long bookingId, List<BookingTruckTransportCharge> charges) {
    if (charges != null && !charges.isEmpty()) {
      for (BookingTruckTransportCharge charge : charges) {
        charge.setBookingId(bookingId);
        charge.set(client, client.getCompany());
      }
      truckChargeRepo.saveAll(charges);
    }
  }

  private void saveCustomClearances(ClientContext client, Long bookingId, List<BookingCustomClearance> customClearances) {
    if (customClearances != null && !customClearances.isEmpty()) {
      for (BookingCustomClearance clearance : customClearances) {
        clearance.setBookingId(bookingId);
        clearance.set(client, client.getCompany());
      }
      ccRepo.saveAll(customClearances);
    }
  }

  private void deleteSeaCharges(ClientContext client, Long chargeId) {
    seaAdditionalChargeRepo.deleteByBookingSeaChargeId(chargeId);
    seaCommissionRepo.deleteByBookingSeaChargeId(chargeId);
    seaChargeRepo.deleteById(chargeId);
  }

  private void deleteAirCharges(ClientContext client, Long chargeId) {
    airAdditionalChargeRepo.deleteByBookingAirChargeId(chargeId);
    airCommissionRepo.deleteByBookingAirChargeId(chargeId);
    airChargeRepo.deleteById(chargeId);
  }

  private void deleteTruckCharges(ClientContext client, Long bookingId) {
    DBConnectionUtil connectionUtil = getDBConnectionUtil();
    List<Long> truckChargeIds = truckChargeRepo.findIdByBookingId(bookingId);
    if (!truckChargeIds.isEmpty()) {
      DeleteGraphBuilder truckBuilder = new DeleteGraphBuilder(
        connectionUtil, client.getCompanyId(), BookingTruckTransportCharge.class, truckChargeIds);
      truckBuilder.runDelete();
    }
    connectionUtil.commit();
    connectionUtil.close();
  }

  private void deleteCustomClearances(ClientContext client, Long bookingId) {
    DBConnectionUtil connectionUtil = getDBConnectionUtil();
    List<Long> customClearanceIds = ccRepo.findIdByBookingId(bookingId);
    if (!customClearanceIds.isEmpty()) {
      DeleteGraphBuilder ccBuilder = new DeleteGraphBuilder(
        connectionUtil, client.getCompanyId(), BookingCustomClearance.class, customClearanceIds);
      ccBuilder.runDelete();
    }
    connectionUtil.commit();
    connectionUtil.close();
  }

  /**
   * Create QuotationCharge from SellingRates (freight rates)
   */
  private QuotationCharge createQuotationChargeFromSellingRates(SpecificServiceInquiry inquiry, List<SellingRate> freightRates) {
    if (freightRates.isEmpty()) return null;

    // Get the first freight rate to determine the mode and basic info
    SellingRate firstRate = freightRates.get(0);
    TransportationMode mode = firstRate.getGroup();

    QuotationCharge quote = new QuotationCharge();
    quote.setMode(mode);
    quote.setPurpose(inquiry.getPurpose());
    quote.setFromLocationCode(inquiry.getFromLocationCode());
    quote.setFromLocationLabel(inquiry.getFromLocationLabel());
    quote.setToLocationCode(inquiry.getToLocationCode());
    quote.setToLocationLabel(inquiry.getToLocationLabel());
    quote.setFinalDestination(inquiry.getFinalDestination());
    quote.setEffectiveDate(new Date());
    quote.setValidity(new Date());
    quote.setConfirm(true);

    // Set currency and exchange rate
    quote.setCurrency(firstRate.getCurrency());
    quote.setRefCurrency(firstRate.getCurrency());
    quote.setNote(firstRate.getNote());

    // Create price group based on transportation mode
    if (TransportationMode.isSeaFCLTransport(mode)) {
      createSeaFCLPriceGroup(quote, freightRates);
    } else if (TransportationMode.isSeaLCLTransport(mode)) {
      createSeaLCLPriceGroup(quote, freightRates);
    } else if (TransportationMode.AIR.equals(mode)) {
      createAirPriceGroup(quote, freightRates);
    }

    return quote;
  }

  /**
   * Create Sea FCL price group from freight rates
   */
  private void createSeaFCLPriceGroup(QuotationCharge quote, List<SellingRate> freightRates) {
    MapObject priceGroupMap = new MapObject();
    for (SellingRate rate : freightRates) {
      String containerUnit = rate.getUnit();
      if (StringUtil.isNotEmpty(containerUnit)) {
        ContainerType containerType = ContainerType.ContainerTypeUnit.match(containerUnit);
        if (containerType != null) {
          String priceLevel = containerType.toFCLPriceLevel();
          priceGroupMap.put(priceLevel, rate.getUnitPrice());
        }
      }
    }

    quote.setPriceGroup(priceGroupMap);
  }

  /**
   * Create Sea LCL price group from freight rates
   */
  private void createSeaLCLPriceGroup(QuotationCharge quote, List<SellingRate> freightRates) {
    if (!freightRates.isEmpty()) {
      SellingRate rate = freightRates.get(0); // LCL should have only one rate
      MapObject priceGroupMap = new MapObject();
      priceGroupMap.put("selectedPrice", rate.getUnitPrice());
      priceGroupMap.put("quantity", rate.getQuantity());
      quote.setPriceGroup(priceGroupMap);
    }
  }

  /**
   * Create Air price group from freight rates
   */
  private void createAirPriceGroup(QuotationCharge quote, List<SellingRate> freightRates) {
    if (!freightRates.isEmpty()) {
      SellingRate rate = freightRates.get(0); // Air should have only one rate
      MapObject priceGroupMap = new MapObject();
      priceGroupMap.put("selectedPrice", rate.getUnitPrice());
      quote.setPriceGroup(priceGroupMap);
    }
  }
}