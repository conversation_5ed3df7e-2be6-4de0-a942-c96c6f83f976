package cloud.datatp.fforwarder.sales.common;

import cloud.datatp.fforwarder.core.common.ClientPartnerType;
import cloud.datatp.fforwarder.sales.project.entity.TaskCalendar;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;

public class TaskNotificationTemplate {

  public static String buildZaloTaskMessage(TaskCalendar task) {

    List<String> customerInfoParts = new ArrayList<>();
    if (task.getPartnerLabel() != null && task.getPartnerType().equals(ClientPartnerType.CUSTOMER_LEAD)) {
      customerInfoParts.add("👤 Lead: " + task.getPartnerLabel());
    }
    if (task.getPartnerLabel() != null && task.getPartnerType().equals(ClientPartnerType.CUSTOMERS)) {
      customerInfoParts.add("🏢 Customer: " + task.getPartnerLabel());
    }
    if (task.getPartnerAddress() != null) {
      customerInfoParts.add("📍 Address: " + task.getPartnerAddress());
    }
    String customerInfo = String.join("\n", customerInfoParts);

    return String.format("""
        📢 DAILY TASK NOTIFICATION
        ━━━━━━━━━━━━━━━━━━━━
        %s
        
        📝 Description:
        %s
        
        %s
        👨‍💼 Created by: %s
        📅 Created: %s
        📌 Status: %s
        ━━━━━━━━━━━━━━━━━━━━
        """.stripIndent().trim(),
      StringUtil.isNotEmpty(task.getLabel()) ? "🔷 Task: " + task.getLabel() : "",
      task.getDescription() != null ? task.getDescription() : "N/A",
      customerInfo,
      task.getCreatorLabel(),
      DateUtil.asLocalDateTime(task.getCreatedDate()),
      task.getStatus() != null ? task.getStatus() : "N/A"
    );
  }

  public static String buildMailTaskMessage(TaskCalendar task) {
    // Build description section
    String descriptionSection = task.getDescription() != null ? String.format("""
      <div style="background-color: #f3f4f6; padding: 15px; border-radius: 8px; margin-top: 12px;">
          <p style="margin: 0; color: #374151;">
              <strong style="color: #1f2937;">📝 Description:</strong><br>
              %s
          </p>
      </div>
      """, task.getDescription()) : "";

    // Build suggested support section
    String suggestedSection = task.getSuggestedSupport() != null ? String.format("""
      <div style="background-color: #f3f4f6; padding: 15px; border-radius: 8px; margin: 16px 0;">
          <p style="margin: 0; color: #374151;">
              <strong style="color: #1f2937;">💡 Suggested Support:</strong><br>
              %s
          </p>
      </div>
      """, task.getSuggestedSupport()) : "";

    // Build status section
    String statusSection = task.getStatus() != null ? String.format("""
      <div style="display: inline-block; background-color: #3b82f6; color: white; padding: 8px 16px; border-radius: 16px; font-size: 14px;">
          📌 Status: %s
      </div>
      """, task.getStatus()) : "";

    // Build final message
    return String.format("""
        <div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 15px; background-color: #f9fafb;">
            <div style="background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <h1 style="color: #1f2937; font-size: 20px; margin: 0 0 16px 0; padding-bottom: 12px; border-bottom: 1px solid #e5e7eb;">
                    📢 Daily Task Notification
                </h1>
                <div style="margin-bottom: 20px;">
                    <h2 style="color: #3b82f6; font-size: 18px; margin: 0 0 8px 0;">
                        🔷 Task: %s
                    </h2>
                    %s
                </div>
                <div style="background-color: #f3f4f6; padding: 15px; border-radius: 8px; margin: 16px 0;">
                    <p style="margin: 0 0 8px 0; color: #374151;">
                        <strong style="color: #1f2937;">👨‍💼 Created by:</strong> %s
                    </p>
                    <p style="margin: 0; color: #374151;">
                        <strong style="color: #1f2937;">📅 Created:</strong> %s
                    </p>
                </div>
                %s
                %s
                <div style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
                    <p style="color: #6b7280; font-size: 14px; margin: 0;">
                        This is an automated notification from the CRM Task Management System.
                    </p>
                </div>
            </div>
        </div>
        """,
      task.getLabel(), descriptionSection,
      task.getCreatorLabel(), DateUtil.asLocalDateTime(task.getCreatedDate()),
      suggestedSection, statusSection
    );
  }

  public static String buildUncompletedZaloMessage(List<TaskCalendar> tasks) {
    String currentDate = new SimpleDateFormat("dd/MM/yyyy").format(new Date());
    List<String> messages = new ArrayList<>();
    tasks.sort(Comparator.comparing(TaskCalendar::getTaskType));
    if (!tasks.isEmpty()) {
      StringBuilder taskListBuilder = new StringBuilder();
      for (TaskCalendar task : tasks) {
        String taskDescription = StringUtil.isNotEmpty(task.getLabel()) ? task.getLabel() : task.getDescription();
        String targetLead = task.getPartnerLabel();
        taskListBuilder
          .append("[").append(task.getTaskType().getLabel()).append("]");

        if (StringUtil.isNotEmpty(targetLead)) {
          taskListBuilder.append(" - ").append(targetLead);
        }

        if (StringUtil.isNotEmpty(taskDescription)) {
          taskListBuilder.append(" - ").append(taskDescription);
        }
        taskListBuilder.append("\n");
      }
      String taskList = taskListBuilder.toString();

      messages.add(String.format("""
        ⚠️ THÔNG BÁO: Tasks chưa hoàn thành (%s)
        
        Bạn có %d task(s) chưa hoàn thành từ ngày hôm qua:
        
        %s
        
        Hệ thống đã tự động gia hạn due date các task trên sang ngày hôm nay.
        
        📝 Vui lòng:
        - Cập nhật trạng thái 'Completed' nếu task đã hoàn thành
        - Hoặc chuyển sang 'Block' nếu task bị dừng/hoãn
        
        🔔 Lưu ý: Task không được cập nhật sẽ tiếp tục được gia hạn vào ngày mai.
        """.stripIndent(), currentDate, tasks.size(), taskList));
    }
    // Xử lý inquiry tasks using the new method
    return String.join("\n\n", messages);
  }

}