package cloud.datatp.fforwarder.sales.partner.plugin;

import cloud.datatp.fforwarder.sales.partner.entity.CustomerLeads;
import net.datatp.module.data.db.plugin.ServicePlugin;
import net.datatp.security.client.ClientContext;

abstract public class CustomerLeadServicePlugin extends ServicePlugin {

  protected CustomerLeadServicePlugin(String type) {
    super("crm", "CustomerLeadService", type);
  }

  public void onPreSave(ClientContext client, CustomerLeads customerLead, boolean isNew) {
  }

  public void onPostSave(ClientContext client, CustomerLeads customerLead, boolean isNew) {
  }

}