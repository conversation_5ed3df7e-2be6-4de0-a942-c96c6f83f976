package cloud.datatp.fforwarder.sales.common.entity;

import cloud.datatp.fforwarder.sales.booking.dto.SellingRate;
import cloud.datatp.fforwarder.sales.booking.dto.SellingRate.Type;
import cloud.datatp.fforwarder.sales.common.ContainerType;
import cloud.datatp.fforwarder.sales.quotation.dto.LocalCharge;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import java.io.Serial;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.util.dataformat.DataSerializer;

@Entity
@Table(name = QuotationAdditionalCharge.TABLE_NAME)
@Setter
@Getter
@NoArgsConstructor
public class QuotationAdditionalCharge extends CustomerAdditionalCharge {
  @Serial
  private static final long serialVersionUID = 1L;
  public static final String TABLE_NAME = "lgc_sales_quotation_additional_charge";

  @Column(name = "specific_quotation_id", nullable = false)
  private Long specificQuotationId;

  public static List<QuotationAdditionalCharge> computeFromLocalCharges(List<LocalCharge> localCharges) {
    QuotationAdditionalCharge template = new QuotationAdditionalCharge();
    List<QuotationAdditionalCharge> addCharges = new ArrayList<>();
    for (LocalCharge localCharge : localCharges) {
      template.setTarget(localCharge.getTarget());
      template.setMode(localCharge.getMode());
      template.setName(localCharge.getCode());
      template.setLabel(localCharge.getName());
      template.setCurrency(localCharge.getCurrency());
      template.setTaxRate(localCharge.getTaxRate());
      template.setQuantity(localCharge.getQuantity());
      template.setNote(localCharge.getNote());

      if (localCharge.getUnitPrice() > 0) {
        QuotationAdditionalCharge clone = DataSerializer.JSON.clone(template);
        clone.setUnitPrice(localCharge.getUnitPrice());
        clone.setUnit(localCharge.getUnit());
        addCharges.add(clone);
      } else {
        Map<String, Double> quoteRate = localCharge.getQuoteRate();
        if (Objects.nonNull(quoteRate)) {
          for (String unit : quoteRate.keySet()) {
            ContainerType containerType = ContainerType.ContainerTypeUnit.match(unit);
            if (containerType == null) continue;
            QuotationAdditionalCharge clone = DataSerializer.JSON.clone(template);
            clone.setUnitPrice(quoteRate.get(unit));
            clone.setUnit(unit);
            addCharges.add(clone);
          }
        }
      }
    }
    return addCharges;
  }

  public static List<QuotationAdditionalCharge> computeFromLocalChargeSellingRates(List<SellingRate> sellingRates) {
    List<QuotationAdditionalCharge> addCharges = new ArrayList<>();
    for (SellingRate sellingRate : sellingRates) {
      // Handle LOCAL_CHARGE, TRUCKING, and CUSTOM types
      if (sellingRate.getType() != Type.LOCAL_CHARGE &&
          sellingRate.getType() != Type.TRUCKING &&
          sellingRate.getType() != Type.CUSTOM) {
        continue;
      }

      QuotationAdditionalCharge charge = new QuotationAdditionalCharge();
      charge.setTarget(sellingRate.getTarget());
      charge.setMode(sellingRate.getGroup());
      charge.setName(sellingRate.getCode());
      charge.setLabel(sellingRate.getName());
      charge.setCurrency(sellingRate.getCurrency());
      charge.setTaxRate(sellingRate.getTaxRate());
      charge.setQuantity(sellingRate.getQuantity());
      charge.setNote(sellingRate.getNote());
      charge.setUnitPrice(sellingRate.getUnitPrice());
      charge.setUnit(sellingRate.getUnit());

      // Set additional properties based on type
      if (sellingRate.getType() == Type.TRUCKING) {
        // Add trucking-specific handling if needed
        if (charge.getNote() == null) {
          charge.setNote("Trucking Service");
        }
      } else if (sellingRate.getType() == Type.CUSTOM) {
        // Add custom clearance-specific handling if needed
        if (charge.getNote() == null) {
          charge.setNote("Custom Clearance Service");
        }
      }

      addCharges.add(charge);
    }
    return addCharges;
  }

  public QuotationAdditionalCharge(CustomerAdditionalCharge charge) { super(charge); }

  public static List<QuotationAdditionalCharge> computeFromCharges(List<? extends CustomerAdditionalCharge> charges) {
    List<QuotationAdditionalCharge> result = new ArrayList<>();
    for (CustomerAdditionalCharge charge : charges) {
      QuotationAdditionalCharge quotationCharge = new QuotationAdditionalCharge(charge);
      result.add(quotationCharge);
    }
    return result;
  }

}