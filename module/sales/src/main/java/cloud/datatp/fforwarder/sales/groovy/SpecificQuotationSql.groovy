package cloud.datatp.fforwarder.sales.groovy

import net.datatp.lib.executable.ExecutableContext
import net.datatp.lib.executable.Executor
import net.datatp.module.data.db.ExecutableSqlBuilder
import net.datatp.util.ds.MapObject
import org.springframework.context.ApplicationContext

public class SpecificQuotationSql extends Executor {

    public class SearchSpecificQuotation extends ExecutableSqlBuilder {
        @Override
        public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
            MapObject sqlParams = ctx.getParam("sqlParams");
            String query = """
              WITH filtered_requests AS (
                  -- Existing lgc_price_inquiry_request data
                  SELECT
                    c.id,
                    c.company_id,
                    c.code,
                    c.request_date,
                    c.status,
                    c.mode,
                    c.purpose,
                    c.type_of_shipment,
                    c.service_mode,
                    c.client_partner_type,
                    c.client_partner_id,
                    c.client_label,
                    c.saleman_account_id,
                    c.saleman_label,
                    c.saleman_email,
                    c.saleman_phone,
                    c.saleman_job_title,
                    c.saleman_branch_name,
                    c.pricing_date,
                    c.pricing_account_id,
                    c.pricing_label,
                    c.term_of_service,
                    c.cargo_ready_date,
                    c.is_multi_route,
                    c.from_location_code,
                    c.from_location_label,
                    c.to_location_code,
                    c.to_location_label,
                    c.pickup_address,
                    c.delivery_address,
                    c.target_rate,
                    c.note,
                    c.feedback,
                    c.pricing_note,
                    c.total_new_prices_count,
                    c.total_analysis_prices_count,
                    c.step_tracking,
                    c.total_step_counting,
                    c.job_tracking_status,
                    c.mail_subject,
                    c.mail_to,
                    c.mail_cc,
                    -- ShipmentDetail fields
                    c.dimension_length,
                    c.dimension_width,
                    c.dimension_height,
                    c.stackable,
                    c.package_quantity,
                    c.volume_info,
                    c.volume_cbm,
                    c.gross_weight_kg,
                    c.report_volume,
                    c.report_volume_unit,
                    c.commodity,
                    c.desc_of_goods,
                    c.dg_liquid_cargo,
                    c.buy_insurance_request,
                    c.express_courier,
                    c.cross_border_trucking,
                    c.special_request_note,
                    c.free_time_terminal_request,
                    c.vn_border_gate_request,
                    -- Additional fields
                    inquiry_quotation.final_destination AS final_destination,
                    q.id AS quotation_id,
                    b.id AS booking_id,
                    'INQUIRY_REQUEST' AS type
                  FROM lgc_price_inquiry_request c
                  LEFT JOIN (
                      SELECT
                          reference_code,
                          final_destination,
                          company_id,
                          id,
                          ROW_NUMBER() OVER (PARTITION BY reference_code, company_id ORDER BY id DESC) AS rn
                      FROM lgc_sales_specific_service_inquiry
                      WHERE EXISTS (
                          SELECT 1
                          FROM lgc_sales_specific_quotation q
                          WHERE q.inquiry_id = lgc_sales_specific_service_inquiry.id
                      )
                  ) inquiry_quotation
                      ON inquiry_quotation.reference_code = c.code
                      AND inquiry_quotation.company_id = c.company_id
                      AND inquiry_quotation.rn = 1
                  LEFT JOIN lgc_sales_specific_quotation q
                      ON q.inquiry_id = inquiry_quotation.id
                  LEFT JOIN (
                      SELECT
                          reference_code,
                          company_id,
                          id,
                          ROW_NUMBER() OVER (PARTITION BY reference_code, company_id ORDER BY id DESC) AS rn
                      FROM lgc_sales_specific_service_inquiry
                      WHERE EXISTS (
                          SELECT 1
                          FROM lgc_sales_booking b
                          WHERE b.inquiry_id = lgc_sales_specific_service_inquiry.id
                      )
                  ) inquiry_booking
                      ON inquiry_booking.reference_code = c.code
                      AND inquiry_booking.company_id = c.company_id
                      AND inquiry_booking.rn = 1
                      AND (inquiry_booking.id != inquiry_quotation.id OR inquiry_quotation.id IS NULL)
                  LEFT JOIN lgc_sales_booking b
                      ON b.inquiry_id = inquiry_booking.id
                  WHERE ${FILTER_BY_STORAGE_STATE("c", sqlParams)}
                    ${AND_FILTER_BY_OPTION("c.status", "status", sqlParams)}
                    ${AND_FILTER_BY_RANGE("c.request_date", "requestDate", sqlParams)}
                    ${addAndClause(sqlParams, "isFavorite", "q.is_favorite = :isFavorite")}
                    AND (
                      'System' = :space
                      OR ('Company' = :space AND c.company_id = :companyId)
                      OR ('User' = :space AND c.saleman_account_id = :accessAccountId)
                    )

                  UNION ALL

                  -- Add lgc_price_bulk_cargo_inquiry_request data
                  SELECT
                    bc.id,
                    bc.company_id,
                    bc.code,
                    bc.request_date,
                    bc.status,
                    'Bulk Cargo' AS mode,                    -- Constant value                           -- Constant for bulk cargo
                    bc.purpose,                                -- BulkCargo has purpose field
                    NULL AS type_of_shipment,                -- Not available in bulk cargo
                    NULL AS service_mode,                     -- Not available in bulk cargo
                    'CUSTOMERS' AS client_partner_type,        -- Default value
                    bc.client_partner_id,
                    bc.client_label,
                    bc.saleman_account_id,
                    bc.saleman_label,
                    bc.saleman_email,
                    bc.saleman_phone,
                    NULL AS saleman_job_title,               -- Not available in bulk cargo
                    bc.saleman_branch_name,
                    bc.pricing_date,
                    bc.pricing_account_id,
                    bc.pricing_label,
                    bc.term_of_service,        -- From embedded BulkCargoShipmentDetail
                    NULL AS cargo_ready_date,                 -- Bulk cargo uses laydays_date instead
                    false AS is_multi_route,                   -- Default false
                    bc.from_location_code,
                    bc.from_location_label,
                    bc.to_location_code,
                    bc.to_location_label,
                    NULL AS pickup_address,                   -- Not available in bulk cargo
                    NULL AS delivery_address,                 -- Not available in bulk cargo
                    bc.target_rate,            -- From embedded BulkCargoShipmentDetail
                    bc.note,
                    bc.feedback,
                    bc.pricing_note,
                    NULL AS total_new_prices_count,            -- Not available in bulk cargo
                    NULL AS total_analysis_prices_count,       -- Not available in bulk cargo
                    NULL AS step_tracking,                    -- Not available in bulk cargo
                    NULL AS total_step_counting,               -- Not available in bulk cargo
                    NULL AS job_tracking_status,              -- Not available in bulk cargo
                    bc.mail_subject,
                    bc.mail_to,
                    bc.mail_cc,
                    -- ShipmentDetail fields (mostly N/A for bulk cargo)
                    NULL AS dimension_length,                 -- Bulk cargo doesn't have dimensions
                    NULL AS dimension_width,
                    NULL AS dimension_height,
                    bc.stackable,              -- BulkCargo has stackable
                    NULL AS package_quantity,                 -- Bulk cargo uses quantity instead
                    NULL AS volume_info,                      -- Bulk cargo doesn't use container info
                    bc.volume AS volume_cbm,   -- Map volume to volume_cbm
                    NULL AS gross_weight_kg,                  -- Bulk cargo uses quantity in metric tons
                    bc.volume AS report_volume, -- Use volume as report
                    bc.unit AS report_volume_unit, -- Use unit as report unit
                    bc.commodity,
                    bc.desc_of_goods,
                    false AS dg_liquid_cargo,                  -- Default false
                    false AS buy_insurance_request,            -- Default false
                    false AS express_courier,                  -- Default false
                    false AS cross_border_trucking,            -- Default false
                    bc.cargo_proceeding AS special_request_note, -- Map cargo_proceeding
                    NULL AS free_time_terminal_request,       -- Not available in bulk cargo
                    NULL AS vn_border_gate_request,           -- Not available in bulk cargo
                    -- Additional fields
                    NULL AS final_destination,                 -- Bulk cargo doesn't have this
                    NULL AS quotation_id,                      -- Bulk cargo doesn't have quotations
                    NULL AS booking_id,                        -- Bulk cargo doesn't have bookings
                    'BULK_CARGO_INQUIRY_REQUEST' AS type
                  FROM lgc_price_bulk_cargo_inquiry_request bc
                  WHERE ${FILTER_BY_STORAGE_STATE("bc", sqlParams)}
                    ${AND_FILTER_BY_RANGE("bc.request_date", "requestDate", sqlParams)}
                    AND (
                      'System' = :space
                      OR ('Company' = :space AND bc.company_id = :companyId)
                      OR ('User' = :space AND bc.saleman_account_id = :accessAccountId)
                    )
              )
              SELECT r.*
              FROM filtered_requests r
              ORDER BY r.request_date DESC
            """;
            return query;
        }
    }

    public class QuotationDeleteValidate extends ExecutableSqlBuilder {
        @Override
        public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
            MapObject sqlParams = ctx.getParam("sqlParams");
            String query = """
                SELECT count(q.id) AS valid_counting
                FROM lgc_sales_specific_quotation q
                  JOIN lgc_sales_specific_service_inquiry i
                    ON i.id = q.inquiry_id
                WHERE
                  ${FILTER_BY_PARAM('q.company_id', 'companyId', sqlParams)}
                  AND :writeCap IS TRUE
                  AND (
                    :dataScope IN ('Company', 'All')
                    ${OR_FILTER_BY_PARAM("i.saleman_account_id", "participants", sqlParams)}
                  )
                  AND q.id IN (:ids)
            """;
            return query;
        }
    }

    public SpecificQuotationSql() {
        register(new SearchSpecificQuotation());
        register(new QuotationDeleteValidate());
    }
}