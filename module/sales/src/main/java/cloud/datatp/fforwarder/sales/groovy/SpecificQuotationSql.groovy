package cloud.datatp.fforwarder.sales.groovy

import net.datatp.lib.executable.ExecutableContext
import net.datatp.lib.executable.Executor
import net.datatp.module.data.db.ExecutableSqlBuilder
import net.datatp.util.ds.MapObject
import org.springframework.context.ApplicationContext

public class SpecificQuotationSql extends Executor {

    public class SearchSpecificQuotation extends ExecutableSqlBuilder {
        @Override
        public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
            MapObject sqlParams = ctx.getParam("sqlParams");
            String query = """
              WITH filtered_requests AS (
                  -- Existing lgc_price_inquiry_request data
                  SELECT 
                      c.id,
                      c.company_id,
                      c.code,
                      c.request_date,
                      c.status,
                      c.mode,
                      c.purpose,
                      c.saleman_account_id,
                      c.saleman_label,
                      c.saleman_branch_name,
                      c.pricing_date,
                      c.pricing_account_id,
                      c.pricing_label,
                      c.from_location_code,
                      c.from_location_label,
                      c.to_location_code,
                      c.to_location_label,
                      c.note,
                      c.feedback,
                      c.pricing_note,
                      c.mail_subject,
                      c.mail_to,
                      c.mail_cc,
                      c.target_rate,
                      c.total_new_prices_count,
                      c.total_analysis_prices_count,
                      inquiry_quotation.final_destination AS final_destination,
                      q.id AS quotation_id,
                      b.id AS booking_id,
                      'INQUIRY_REQUEST' AS type
                  FROM lgc_price_inquiry_request c
                  LEFT JOIN (
                      SELECT 
                          reference_code,
                          final_destination,
                          company_id,
                          id,
                          ROW_NUMBER() OVER (PARTITION BY reference_code, company_id ORDER BY id DESC) AS rn
                      FROM lgc_sales_specific_service_inquiry
                      WHERE EXISTS (
                          SELECT 1 
                          FROM lgc_sales_specific_quotation q 
                          WHERE q.inquiry_id = lgc_sales_specific_service_inquiry.id
                      )
                  ) inquiry_quotation 
                      ON inquiry_quotation.reference_code = c.code 
                      AND inquiry_quotation.company_id = c.company_id
                      AND inquiry_quotation.rn = 1
                  LEFT JOIN lgc_sales_specific_quotation q 
                      ON q.inquiry_id = inquiry_quotation.id
                  LEFT JOIN (
                      SELECT 
                          reference_code,
                          company_id,
                          id,
                          ROW_NUMBER() OVER (PARTITION BY reference_code, company_id ORDER BY id DESC) AS rn
                      FROM lgc_sales_specific_service_inquiry
                      WHERE EXISTS (
                          SELECT 1 
                          FROM lgc_sales_booking b 
                          WHERE b.inquiry_id = lgc_sales_specific_service_inquiry.id
                      )
                  ) inquiry_booking 
                      ON inquiry_booking.reference_code = c.code 
                      AND inquiry_booking.company_id = c.company_id
                      AND inquiry_booking.rn = 1
                      AND (inquiry_booking.id != inquiry_quotation.id OR inquiry_quotation.id IS NULL)
                  LEFT JOIN lgc_sales_booking b 
                      ON b.inquiry_id = inquiry_booking.id
                  WHERE ${FILTER_BY_STORAGE_STATE("c", sqlParams)}
                    ${AND_FILTER_BY_OPTION("c.status", "status", sqlParams)}
                    ${AND_FILTER_BY_RANGE("c.request_date", "requestDate", sqlParams)}
                    ${addAndClause(sqlParams, "isFavorite", "q.is_favorite = :isFavorite")}
                    AND (
                      'System' = :space 
                      OR ('Company' = :space AND c.company_id = :companyId)
                      OR ('User' = :space AND c.saleman_account_id = :accessAccountId)
                    )
              
                  UNION ALL
              
                  -- Add lgc_price_bulk_cargo_inquiry_request data
                  SELECT 
                      bc.id,
                      bc.company_id,
                      bc.code,
                      bc.request_date,
                      bc.status,
                      'Bulk Cargo' as mode,                    -- Constant value
                      bc.cargo_type as purpose,           -- Map cargo_type to purpose
                      bc.saleman_account_id,
                      bc.saleman_label,
                      bc.saleman_branch_name,
                      bc.pricing_date,
                      bc.pricing_account_id,
                      bc.pricing_label,
                      bc.from_location_code,
                      bc.from_location_label,
                      bc.to_location_code,
                      bc.to_location_label,
                      bc.note,
                      bc.feedback,
                      bc.pricing_note,
                      bc.mail_subject,
                      bc.mail_to,
                      bc.mail_cc,
                      bc.target_rate,                     -- Bulk cargo has this field
                      NULL as total_new_prices_count,     -- Bulk cargo doesn't have this
                      NULL as total_analysis_prices_count,-- Bulk cargo doesn't have this
                      NULL as final_destination,          -- Bulk cargo doesn't have this
                      NULL as quotation_id,               -- Bulk cargo doesn't have quotations
                      NULL as booking_id,                 -- Bulk cargo doesn't have bookings
                      'BULK_CARGO_INQUIRY_REQUEST' AS type
                  FROM lgc_price_bulk_cargo_inquiry_request bc
                  WHERE ${FILTER_BY_STORAGE_STATE("bc", sqlParams)}
                    ${AND_FILTER_BY_RANGE("bc.request_date", "requestDate", sqlParams)}
                    AND (
                      'System' = :space 
                      OR ('Company' = :space AND bc.company_id = :companyId)
                      OR ('User' = :space AND bc.saleman_account_id = :accessAccountId)
                    )
              )
              SELECT r.*
              FROM filtered_requests r
              ORDER BY r.request_date DESC
            """;
            return query;
        }
    }

    public class QuotationDeleteValidate extends ExecutableSqlBuilder {
        @Override
        public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
            MapObject sqlParams = ctx.getParam("sqlParams");
            String query = """
                SELECT count(q.id) AS valid_counting
                FROM lgc_sales_specific_quotation q
                  JOIN lgc_sales_specific_service_inquiry i 
                    ON i.id = q.inquiry_id 
                WHERE 
                  ${FILTER_BY_PARAM('q.company_id', 'companyId', sqlParams)}
                  AND :writeCap IS TRUE
                  AND (
                    :dataScope IN ('Company', 'All') 
                    ${OR_FILTER_BY_PARAM("i.saleman_account_id", "participants", sqlParams)}
                  )                      
                  AND q.id IN (:ids)
            """;
            return query;
        }
    }

    public SpecificQuotationSql() {
        register(new SearchSpecificQuotation());
        register(new QuotationDeleteValidate());
    }
}