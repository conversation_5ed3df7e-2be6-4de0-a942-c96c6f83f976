package cloud.datatp.fforwarder.sales.groovy

import net.datatp.lib.executable.ExecutableContext
import net.datatp.lib.executable.Executor
import net.datatp.module.data.db.ExecutableSqlBuilder
import net.datatp.util.ds.MapObject
import org.springframework.context.ApplicationContext

public class UserCRMPartnerSql extends Executor {

    public class SearchUserCRMPartner extends ExecutableSqlBuilder {
        @Override
        public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
            MapObject sqlParams = ctx.getParam("sqlParams");

            String query = """
                SELECT
                    l.id                    AS id,
                    l.code                  AS partner_code,
                    l.tax_code              AS tax_code,
                    l."name"                AS partner_name,
                    l.personal_contact      AS personal_contact,
                    l.country_label         AS country_label,
                    l.address               AS address,
                    l.routing               AS routing,
                    l.email                 AS email,
                    l.type                  AS type
                FROM forwarder_customer_leads l
                WHERE 1 = 1
                    AND (
                        ('System' = :space)
                        OR (l.saleman_account_id = :accessAccountId)
                    )
                    ${AND_FILTER_BY_PARAM('l.type', 'type', sqlParams)}
                    ${AND_FILTER_BY_RANGE('l.created_time', 'createdTime', sqlParams)}
                    ${addAndClause(sqlParams, "searchPattern", "LOWER(REGEXP_REPLACE(l.name, '\\s+', '', 'g')) LIKE '%' || LOWER(:searchPattern)")}
                UNION ALL
                SELECT
                    p.id                    AS id,
                    p.partner_code          AS partner_code,
                    p.tax_code              AS tax_code,
                    p."name"                AS partner_name,
                    p.personal_contact      AS personal_contact,
                    p.country_label         AS country_label,   
                    p.address               AS address,
                    p.routing               AS routing,
                    p.email                 AS email,
                    p.partner_group         AS type
                FROM lgc_forwarder_crm_partner p
                WHERE 1 = 1
                  AND (
                    (p.shareable = 'ORGANIZATION' OR 'System' = :space)
                    OR EXISTS (
                      SELECT 1
                      FROM lgc_settings_saleman_partner_obligation ob
                      WHERE ob.partner_code = p.partner_code AND ob.saleman_account_id = :accessAccountId
                       )
                     )
                  ${AND_FILTER_BY_PARAM('p.partner_group', 'type', sqlParams)}
                  ${AND_FILTER_BY_RANGE('p.date_created', 'createdTime', sqlParams)}
                  ${addAndClause(sqlParams, "searchPattern", "LOWER(REGEXP_REPLACE(p.name, '\\s+', '', 'g')) LIKE '%' || LOWER(:searchPattern)")}
            """;
            return query;
        }
    }

    public UserCRMPartnerSql() {
        register(new SearchUserCRMPartner());
    }
}