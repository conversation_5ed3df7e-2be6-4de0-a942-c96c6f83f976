package cloud.datatp.fforwarder.sales.project.repository;

import cloud.datatp.fforwarder.sales.project.entity.TaskCalendar;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import net.datatp.module.data.db.entity.StorageState;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface TaskCalendarRepository extends JpaRepository<TaskCalendar, Serializable> {
  @Modifying
  @Query("DELETE SalesDailyTask s WHERE s.companyId = :companyId AND s.id IN (:ids)")
  void deleteByIds(@Param("companyId") Long companyId, @Param("ids") List<Long> ids);

  @Modifying
  @Query("UPDATE SalesDailyTask s SET s.storageState = :storageState WHERE s.id IN (:ids)")
  void setSaleDailyTaskState(@Param("storageState") StorageState state, @Param("ids") List<Long> id);

  @Query("SELECT t FROM SalesDailyTask t " +
      "WHERE t.companyId = :companyId " +
      "AND t.dueDate >= :startDate " +
      "AND t.dueDate < :endDate " +
      "AND t.status NOT IN ('COMPLETED', 'BLOCKED')")
  List<TaskCalendar> findYesterdayUncompletedTasks(
      @Param("companyId") Long companyId,
      @Param("startDate") Date startDate,
      @Param("endDate") Date endDate);

  @Query("SELECT t FROM SalesDailyTask t " +
    "WHERE t.notificationTime >= :startTime " +
    "AND t.notificationTime < :endTime " +
    "AND t.status NOT IN ('COMPLETED', 'BLOCKED') " +
    "AND t.storageState = 'ACTIVE' " +
    "AND (t.sendingZalo = true OR t.sendingEmail = true) " +
    "ORDER BY t.notificationTime ASC")
  List<TaskCalendar> findTasksInTimeRange(@Param("startTime") Date startTime, @Param("endTime") Date endTime);


  @Query("SELECT t FROM SalesDailyTask t " +
    "WHERE t.createdTime >= :startTime " +
    "AND t.createdTime <= :endTime")
  List<TaskCalendar> findTasksByCreatedTimeRange(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

}