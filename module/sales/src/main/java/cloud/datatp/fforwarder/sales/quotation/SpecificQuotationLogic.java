package cloud.datatp.fforwarder.sales.quotation;

import cloud.datatp.fforwarder.core.common.TransportationMode;
import cloud.datatp.fforwarder.core.db.CRMDaoService;
import cloud.datatp.fforwarder.price.AirPriceLogic;
import cloud.datatp.fforwarder.price.BulkCargoInquiryRequestLogic;
import cloud.datatp.fforwarder.price.InquiryRequestLogic;
import cloud.datatp.fforwarder.price.SeaPriceLogic;
import cloud.datatp.fforwarder.price.entity.AirTransportCharge;
import cloud.datatp.fforwarder.price.entity.BulkCargoInquiryRequest;
import cloud.datatp.fforwarder.price.entity.InquiryRequest;
import cloud.datatp.fforwarder.price.entity.SeaFclTransportCharge;
import cloud.datatp.fforwarder.price.entity.SeaLclTransportCharge;
import cloud.datatp.fforwarder.sales.common.CustomerChargeLogic;
import cloud.datatp.fforwarder.sales.common.entity.QuotationAdditionalCharge;
import cloud.datatp.fforwarder.sales.common.entity.QuotationCharge;
import cloud.datatp.fforwarder.sales.inquiry.InquiryLogic;
import cloud.datatp.fforwarder.sales.inquiry.entity.SpecificServiceInquiry;
import cloud.datatp.fforwarder.sales.quotation.dto.ConfirmQuotationModel;
import cloud.datatp.fforwarder.sales.quotation.dto.LocalCharge;
import cloud.datatp.fforwarder.sales.quotation.dto.Params;
import cloud.datatp.fforwarder.sales.quotation.entity.GenericQuotation;
import cloud.datatp.fforwarder.sales.quotation.entity.SpecificQuotation;
import cloud.datatp.fforwarder.sales.quotation.repository.SpecificQuotationRepository;
import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import lombok.Getter;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.entity.Account;
import net.datatp.module.communication.MailMessage;
import net.datatp.module.core.security.SecurityLogic;
import net.datatp.module.core.security.entity.AppPermission;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.data.db.seq.SeqService;
import net.datatp.module.data.db.util.DBConnectionUtil;
import net.datatp.module.data.db.util.DeleteGraphBuilder;
import net.datatp.module.graphapi.GraphApiService;
import net.datatp.module.hr.EmployeeLogic;
import net.datatp.module.hr.entity.Employee;
import net.datatp.security.client.Capability;
import net.datatp.security.client.ClientContext;
import net.datatp.util.dataformat.DataSerializer;
import net.datatp.util.ds.Arrays;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.Objects;
import net.datatp.util.error.ErrorType;
import net.datatp.util.error.RuntimeError;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Getter
public class SpecificQuotationLogic extends CRMDaoService {

  @Autowired
  private SpecificQuotationRepository sQuotationRepo;

  @Autowired
  private InquiryLogic inquiryLogic;

  @Autowired
  private SecurityLogic securityLogic;

  @Autowired
  private SeqService seqService;

  @Autowired
  private EmployeeLogic employeeLogic;

  @Autowired
  private AccountLogic accountLogic;

  @Autowired
  private GraphApiService graphApiService;

  @Autowired
  private InquiryRequestLogic inquiryRequestLogic;

  @Autowired
  private BulkCargoInquiryRequestLogic bulkCargoInquiryRequestLogic;

  @Autowired
  private CustomerChargeLogic customerChargeLogic;

  @Autowired
  private AirPriceLogic airPriceLogic;

  @Autowired
  private SeaPriceLogic seaPriceLogic;

  @PostConstruct
  private void onInit() {
    seqService.createIfNotExists(GenericQuotation.SEQUENCE, 10);
  }

  public List<SpecificQuotation> findByCompany(ClientContext client, ICompany company) {
    return sQuotationRepo.findByCompany(company.getId());
  }

  private SpecificQuotation computeSQuotationModel(ClientContext client, ICompany company, SpecificQuotation quotation) {
    List<QuotationCharge> quoteList = customerChargeLogic.findQuotationChargeBySQuotationId(company, quotation.getId());
    quotation.setQuoteList(quoteList);
    List<QuotationAdditionalCharge> addCharges = customerChargeLogic.findAddChargeBySQuotationId(company, quotation.getId());
    quotation.withAdditionalCharges(addCharges);
    return quotation;
  }

  public SpecificQuotation getById(ClientContext client, ICompany company, Long id) {
    SpecificQuotation quotation = sQuotationRepo.findById(id).get();
    return computeSQuotationModel(client, company, quotation);
  }

  public SpecificQuotation saveSpecificQuotation(ClientContext client, ICompany company, SpecificQuotation quotation) {
    final boolean isNewQuote = quotation.isNew();
    List<QuotationCharge> quoteList = quotation.getQuoteList();
    List<LocalCharge> localHandlingCharges = quotation.getLocalHandlingCharges();
    SpecificServiceInquiry inquiry = quotation.getInquiry();
    if (inquiry.getSalemanAccountId() == null) {
      Account saleman = accountLogic.getAccountById(client, client.getAccountId());
      Objects.assertNotNull(saleman, "Account {} is not found", client.getAccountId());
      inquiry.setSalemanAccountId(saleman.getId());
      inquiry.setSalemanLabel(saleman.getFullName());
    }
    String referenceCode = inquiry.getReferenceCode();
    InquiryRequest inquiryRequest = inquiryRequestLogic.getInquiryRequest(client, company, referenceCode);
    inquiryRequest = inquiry.toInquiryRequest(inquiryRequest);
    InquiryRequest savedRequest = inquiryRequestLogic.saveInquiryRequest(client, company, inquiryRequest);
    inquiry.setReferenceCode(savedRequest.getCode());
    inquiry.computeTypeOfService();
    /* save quotation */
    quotation.setInquiry(inquiry);
    quotation.set(client, company);
    SpecificQuotation saved = sQuotationRepo.save(quotation);

    Long sQuotationId = saved.getId();
    if (!isNewQuote) {
      customerChargeLogic.deleteAdditionalCharge(client, company, sQuotationId);
    }

    List<QuotationAdditionalCharge> charges = QuotationAdditionalCharge.computeFromLocalCharges(localHandlingCharges);
    customerChargeLogic.saveAdditionalCharges(client, company, sQuotationId, charges);

    List<QuotationCharge> holder = new ArrayList<>();
    for (QuotationCharge quote : quoteList) {
      //quote.computeFromInquiry(inquiry);
      quote.setSpecificQuotationId(sQuotationId);
      QuotationCharge updateQuote = customerChargeLogic.saveQuotationCharge(client, company, quote);
      holder.add(updateQuote);
    }
    saved.setQuoteList(holder);
    saved.setLocalHandlingCharges(localHandlingCharges);
    return saved;
  }

  public SpecificQuotation matchPriceSpecificQuotation(ClientContext client, ICompany company, Long sQuotationId, List<Long> priceReferenceIds) {
    SpecificQuotation quotation = getById(client, company, sQuotationId);
    Objects.assertNotNull(quotation, "Quotation {} is not found", sQuotationId);
    TransportationMode mode = quotation.getInquiry().getMode();

    if (TransportationMode.isAirTransport(mode)) {
      List<AirTransportCharge> foundPrices = airPriceLogic.findByIds(client, company, priceReferenceIds);
      quotation.withAirQuotes(foundPrices);
    } else if (TransportationMode.isSeaFCLTransport(mode)) {
      List<SeaFclTransportCharge> foundPrices = seaPriceLogic.findSeaFclTransportChargeByIds(client, company, priceReferenceIds);
      quotation.withFCLQuotes(foundPrices);
    } else if (TransportationMode.isSeaLCLTransport(mode)) {
      List<SeaLclTransportCharge> foundSeaCharges = seaPriceLogic.findSeaLclTransportChargeByIds(client, company, priceReferenceIds);
      quotation.withLCLQuotes(foundSeaCharges);
    } else {
      throw new RuntimeError(ErrorType.IllegalArgument, "Unsupported transportation mode: " + mode.name());
    }
    return saveSpecificQuotation(client, company, quotation);
  }

  public SpecificQuotation newSpecificQuotation(ClientContext client, ICompany company, Params.SQuotationCreation template) {
    SpecificServiceInquiry inquiry = template.getInquiry();
    List<Long> priceReferenceIds = template.getPriceReferenceIds();
    Long inquiryRequestId = template.getInquiryRequestId();
    SpecificQuotation quotation;

    if (inquiry != null) {
      quotation = new SpecificQuotation(template.getInquiry());
    } else if (inquiryRequestId != null) {
      InquiryRequest request = inquiryRequestLogic.getInquiryRequest(client, company, inquiryRequestId);
      Objects.assertNotNull(request, "Inquiry request not found: " + inquiryRequestId);
      inquiry = new SpecificServiceInquiry();
      inquiry.computeFromRequest(request);
      template.setInquiry(inquiry);
      quotation = new SpecificQuotation(template.getInquiry());
    } else {
      throw new RuntimeError(ErrorType.IllegalArgument, "Missing inquiry or request!!");
    }

    TransportationMode mode = inquiry.getMode();
    if (TransportationMode.isAirTransport(mode)) {
      List<AirTransportCharge> foundPrices = airPriceLogic.findByIds(client, company, priceReferenceIds);
      quotation.withAirQuotes(foundPrices);
    } else if (TransportationMode.isSeaFCLTransport(mode)) {
      List<SeaFclTransportCharge> foundPrices = seaPriceLogic.findSeaFclTransportChargeByIds(client, company, priceReferenceIds);
      quotation.withFCLQuotes(foundPrices);
    } else if (TransportationMode.isSeaLCLTransport(mode)) {
      List<SeaLclTransportCharge> foundSeaCharges = seaPriceLogic.findSeaLclTransportChargeByIds(client, company, priceReferenceIds);
      quotation.withLCLQuotes(foundSeaCharges);
    } else {

    }
    if(template.isAutoSaved()) {
      //return saveSpecificQuotation(client, company, quotation);
    }
    return quotation;
  }

  public SpecificQuotation newSpecificQuotationForBulkCargo(ClientContext client, ICompany company, Params.SQuotationCreation template) {
    SpecificServiceInquiry inquiry = template.getInquiry();
    List<Long> priceReferenceIds = template.getPriceReferenceIds();
    Long bulkCargoInquiryRequestId = template.getInquiryRequestId();
    SpecificQuotation quotation;

    if (inquiry != null) {
      quotation = new SpecificQuotation(template.getInquiry());
    } else if (bulkCargoInquiryRequestId != null) {
      BulkCargoInquiryRequest bulkCargo = bulkCargoInquiryRequestLogic.getBulkCargoInquiryRequest(client, bulkCargoInquiryRequestId);
      Objects.assertNotNull(bulkCargo, "BulkCargoInquiryRequest not found by id = " + bulkCargoInquiryRequestId);
      inquiry = new SpecificServiceInquiry();
      inquiry.computeFromRequest(bulkCargo);
      template.setInquiry(inquiry);
      quotation = new SpecificQuotation(template.getInquiry());
    } else {
      throw new RuntimeError(ErrorType.IllegalArgument, "Missing inquiry or request!!");
    }

    TransportationMode mode = inquiry.getMode();
    if (TransportationMode.isAirTransport(mode)) {
      List<AirTransportCharge> foundPrices = airPriceLogic.findByIds(client, company, priceReferenceIds);
      quotation.withAirQuotes(foundPrices);
    } else if (TransportationMode.isSeaFCLTransport(mode)) {
      List<SeaFclTransportCharge> foundPrices = seaPriceLogic.findSeaFclTransportChargeByIds(client, company, priceReferenceIds);
      quotation.withFCLQuotes(foundPrices);
    } else if (TransportationMode.isSeaLCLTransport(mode)) {
      List<SeaLclTransportCharge> foundSeaCharges = seaPriceLogic.findSeaLclTransportChargeByIds(client, company, priceReferenceIds);
      quotation.withLCLQuotes(foundSeaCharges);
    } else {

    }
    if(template.isAutoSaved()) {
      //return saveSpecificQuotation(client, company, quotation);
    }
    return quotation;
  }

  public SpecificQuotation copySpecificQuotation(ClientContext client, ICompany company, Long quotationId) {
    SpecificQuotation source = getById(client, company, quotationId);
    Objects.assertNotNull(source, "Quotation {} is not found", quotationId);

    List<QuotationCharge> quoteListSrc = source.getQuoteList();
    SpecificQuotation clone = source.clone().clearIds();
    SpecificServiceInquiry inquiry = clone.getInquiry();
    inquiry.setReferenceCode(null);
    inquiry.setRequestDate(new Date());
    inquiry.setCargoReadyDate(new Date());
    inquiry.setEstimatedTimeDeparture(new Date());
    inquiry.clearIds();
    clone.setInquiry(inquiry);

    List<QuotationCharge> quoteList = new ArrayList<>();
    if (Collections.isNotEmpty(quoteListSrc)) {
      for (QuotationCharge quote : quoteListSrc) {
        QuotationCharge newQuote = DataSerializer.JSON.clone(quote);
        newQuote.setId(null);
        newQuote.setValidity(new Date());
        quoteList.add(newQuote);
      }
    }

    clone.setQuoteList(quoteList);
    return clone;
  }

  public List<SqlMapRecord> searchSpecificQuotations(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    sqlParams.addParam("companyId", company.getId());
    sqlParams.addParam("accessAccountId", client.getAccountId());
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/sales/groovy/SpecificQuotationSql.groovy";
    return searchDbRecords(client, scriptDir, scriptFile, "SearchSpecificQuotation", sqlParams);
  }

  public boolean changeSpecificQuotationStorageState(ClientContext client, ChangeStorageStateRequest req) {
    sQuotationRepo.setStorageState(req.getNewStorageState(), req.getEntityIds());
    return true;
  }

  public int deleteByIds(ClientContext client, ICompany company, List<Long> ids) {
    SqlQueryParams sqlParams = new SqlQueryParams();
    sqlParams.addParam("ids", ids);
    sqlParams.addParam("companyId", company.getId());
    final Employee employee = employeeLogic.getEmployee(client, company, client.getRemoteUser());
    Objects.assertNotNull(employee, "Employee {} is not found", client.getRemoteUser());
    final AppPermission permission = securityLogic.getAppPermission(client, company.getId(), "logistics", "user-logistics-sales");
    final boolean writeCap = permission.getCapability().hasCapability(Capability.Write);
    sqlParams.addParam("writeCap", writeCap);
    sqlParams.addParam("dataScope", permission.getDataScope().name());
    List<Long> participants = Arrays.asList(client.getAccountId());
    if (permission.isGroupScope())
      participants = employeeLogic.findEmployeesByManagerId(client, company, employee.getId());
    sqlParams.addParam("participants", participants);
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/sales/groovy/SpecificQuotationSql.groovy";
    List<SqlMapRecord> recordsCounting = searchDbRecords(client, scriptDir, scriptFile, "QuotationDeleteValidate", sqlParams);
    Objects.assertTrue(recordsCounting.size() == 1, "Only one record is expected!!!");
    final SqlMapRecord record = recordsCounting.get(0);
    final Long validCount = record.getLong("validCounting", -1L);
    Objects.assertTrue(validCount - ids.size() == 0, "Only {} records can be deleted", validCount);
    DBConnectionUtil connectionUtil = getDBConnectionUtil();
    DeleteGraphBuilder graph = new DeleteGraphBuilder(connectionUtil, company.getId(), SpecificQuotation.class, ids);
    System.out.println("---------------------------------");
    graph.dumpQuery();
    System.out.println("---------------------------------");
    final int deleteCount = graph.runDelete();
    connectionUtil.commit();
    connectionUtil.close();
    return deleteCount;
  }

  public ConfirmQuotationModel sendQuotation(ClientContext client, ICompany company, ConfirmQuotationModel template) {
    MailMessage mailMessage = new MailMessage();
    mailMessage.setMessage(template.getMailMessage());
    mailMessage.setFrom(template.getSalemanEmail());
    mailMessage.setSubject(template.getMailSubject());
    mailMessage.setTo(template.getToList());
    mailMessage.setCc(template.getCcList());
    mailMessage.setAttachments(template.getAttachments());
    graphApiService.sendEmailWithHtmlFormat(client, company, mailMessage);
    return template;
  }

}