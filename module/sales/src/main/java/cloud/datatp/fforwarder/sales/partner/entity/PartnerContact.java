package cloud.datatp.fforwarder.sales.partner.entity;

import cloud.datatp.fforwarder.core.common.ClientPartnerType;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.io.Serial;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.data.db.entity.Persistable;

@Entity
@Table(name = PartnerContact.TABLE_NAME)
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor
@Getter
@Setter
public class PartnerContact extends Persistable<Long> {
  @Serial
  private static final long serialVersionUID = 1L;

  public static final String TABLE_NAME = "forwarder_partner_contact";

  @Enumerated(EnumType.STRING)
  @Column(name = "partner_type")
  private ClientPartnerType partnerType = ClientPartnerType.AGENTS_APPROACHED;

  @Column(name = "partner_id", nullable = false, updatable = false, insertable = false)
  private Long partnerId;

  @Column(name = "partner_label")
  private String partnerLabel;

  @Column(name = "personal_contact")
  private String personalContact;

  @Column(name = "position")
  private String position;

  @Column(name = "phone")
  private String phone;

  @Column(name = "email")
  private String email;

  @Column(name = "input_account_id")
  private Long inputAccountId;

  @Column(name = "input_account_label")
  private String inputAccountLabel;

  @Column(name = "note", length = 1024 * 32)
  private String note;

}