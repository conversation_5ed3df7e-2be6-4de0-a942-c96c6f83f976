package cloud.datatp.fforwarder.sales.project.entity;

import cloud.datatp.fforwarder.core.common.ClientPartnerType;
import cloud.datatp.fforwarder.price.entity.InquiryRequest;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.company.entity.CompanyEntity;
import net.datatp.module.wfms.entity.EntityTaskRequest;
import net.datatp.module.wfms.entity.ITaskableEntity;
import net.datatp.util.bean.BeanUtil;
import net.datatp.util.ds.MapObject;
import net.datatp.util.text.DateUtil;

@Slf4j
@Entity
@Table(
  name = TaskCalendar.TABLE_NAME,
  indexes = {
    @Index(
      name = TaskCalendar.TABLE_NAME + "_saleman_idx",
      columnList = "saleman_label"
    ),
    @Index(
      name = TaskCalendar.TABLE_NAME + "_due_date_idx",
      columnList = "due_date"
    ),
    @Index(
      name = TaskCalendar.TABLE_NAME + "_status_idx",
      columnList = "status"
    ),
    @Index(
      name = TaskCalendar.TABLE_NAME + "_task_type_idx",
      columnList = "task_type"
    ),
    @Index(
      name = TaskCalendar.TABLE_NAME + "_reference_idx",
      columnList = "reference_type, reference_id"
    )
  }
)
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
@Getter @Setter
public class TaskCalendar extends CompanyEntity implements ITaskableEntity {

  final static public String TABLE_NAME = "forwarder_sales_daily_task";

  public enum SalesDailyTaskType {
    CALL, MEET_CUSTOMER, QUOTATION, PRICE_ENTRY, TROUBLE_SHOOTING, BUSINESS_TRIP, MEETING, OTHER, REQUEST_INQUIRY;

    public static SalesDailyTaskType parse(String token) {
      if (token == null) return CALL;
      try {
        return valueOf(token.trim().toUpperCase());
      } catch (IllegalArgumentException e) {
        return CALL;
      }
    }

    public String getLabel() {
      return switch (this) {
        case CALL -> "Call";
        case MEET_CUSTOMER -> "Meet Customer";
        case QUOTATION -> "Quotation";
        case PRICE_ENTRY -> "Price Entry";
        case TROUBLE_SHOOTING -> "Trouble Shooting";
        case BUSINESS_TRIP -> "Business Trip";
        case MEETING -> "Meeting";
        case OTHER -> "Other";
        case REQUEST_INQUIRY -> "Request Inquiry";
      };
    }

  }

  public enum SalesTaskStatus {
    PENDING, IN_PROGRESS, COMPLETED, NEED_SUPPORT, BLOCKED;

    public static SalesTaskStatus parse(String token) {
      if (token == null) return IN_PROGRESS;
      try {
        return valueOf(token.trim().toUpperCase());
      } catch (IllegalArgumentException e) {
        return IN_PROGRESS;
      }
    }

    public static boolean isCompleted(SalesTaskStatus status) {
      return status == SalesTaskStatus.COMPLETED || status == SalesTaskStatus.BLOCKED;
    }

  }

  @Column(name = "created_date")
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  private Date createdDate;

  @Column(name = "due_date")
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  private Date dueDate;

  @Column(name = "creator_account_id")
  private Long creatorAccountId;

  @Column(name = "creator_label")
  private String creatorLabel;

  @Column(name = "saleman_account_id")
  private Long salemanAccountId;

  @Column(name = "saleman_label")
  private String salemanLabel;

  @Enumerated(EnumType.STRING)
  @Column(name = "task_type")
  private SalesDailyTaskType taskType;

  @Enumerated(EnumType.STRING)
  @Column(name = "status")
  private SalesTaskStatus status;

  @Column(name = "saleman_department_id")
  private Long salemanDepartmentId;

  @Column(name = "saleman_department_label")
  private String salemanDepartmentLabel;

  @Column(name = "label")
  private String label;

  @Column(name = "description", length = 1024 * 32)
  private String description;

  @Column(name = "suggested_support", length = 1024 * 32)
  private String suggestedSupport;

  @Column(name = "sending_email")
  private boolean sendingEmail = false;

  @Column(name = "sending_zalo")
  private Boolean sendingZalo = false;

  @Column(name = "notification_time")
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  private Date notificationTime;

  @Column(name = "reference_id")
  private Long referenceId;

  @Column(name = "reference_type")
  private String referenceType;

  public boolean isSendingZalo() {
    return sendingZalo != null && sendingZalo;
  }

  public void setSendingZalo(Boolean sendingZalo) {
    if (sendingZalo == null) sendingZalo = false;
    this.sendingZalo = sendingZalo;
  }

  // ---------------------------- Customer/ Lead Info ----------------------------
  @Enumerated(EnumType.STRING)
  @Column(name = "partner_type")
  private ClientPartnerType partnerType = ClientPartnerType.CUSTOMERS;

  @Column(name = "partner_id")
  private Long partnerId;

  @Column(name = "partner_label")
  private String partnerLabel;

  @Column(name = "partner_address", length = 9 * 1024)
  private String partnerAddress;

  @Column(name = "partner_shipping_route", length = 1024)
  private String partnerShippingRoute;

  @Column(name = "partner_volume_details", length = 1024)
  private String partnerVolumeDetails;

  @Column(name = "partner_onboarding_status", length = 1024)
  private String partnerOnboardingStatus;

  private EntityTaskRequest entityTaskRequest = new EntityTaskRequest();

  public TaskCalendar(InquiryRequest req) {
    label = req.getMailSubject();
    description = req.getMailSubject();
    createdDate = req.getRequestDate();
    creatorAccountId = req.getSalemanAccountId();
    creatorLabel = req.getSalemanLabel();
    salemanAccountId = req.getSalemanAccountId();
    salemanLabel = req.getSalemanLabel();
    if (req.getStatus().isChecking()) {
      status = SalesTaskStatus.IN_PROGRESS;
    } else {
      status = SalesTaskStatus.COMPLETED;
    }

    taskType = SalesDailyTaskType.REQUEST_INQUIRY;
    referenceId = req.getId();
    referenceType = InquiryRequest.TABLE_NAME;
  }

  public TaskCalendar computeFromMapObject(MapObject sel) {
    List<String> fields = List.of(
      "label", "description", "suggestedSupport", "sendingEmail", "sendingZalo",
      "notificationTime", "customerLeadPartnerId", "customerLeadLabel",
      "customerPartnerId", "customerLabel", "customerAddress",
      "shippingRoute", "volumeDetails", "onboardingStatus", "salemanAccountId", "salemanLabel",
      "createDate", "dueDate"
    );
    BeanUtil.updateFieldsFromMap(this, sel, fields);
    if (sel.containsKey("status")) {
      String statusStr = sel.getString("status", null);
      this.status = SalesTaskStatus.parse(statusStr);
    }
    if (sel.containsKey("taskType")) {
      String taskTypeStr = sel.getString("taskType", null);
      this.taskType = SalesDailyTaskType.parse(taskTypeStr);
    }
    return this;
  }

  public void initializeDueDate() {
    if (this.createdDate != null && this.dueDate == null) {
      Calendar cal = Calendar.getInstance();
      cal.setTime(this.createdDate);
      cal.set(Calendar.HOUR_OF_DAY, 23);
      cal.set(Calendar.MINUTE, 59);
      cal.set(Calendar.SECOND, 59);
      cal.set(Calendar.MILLISECOND, 999);
      this.dueDate = cal.getTime();
    }
  }

  public EntityTaskRequest getEntityTaskRequest() {
    return entityTaskRequest;
  }

}