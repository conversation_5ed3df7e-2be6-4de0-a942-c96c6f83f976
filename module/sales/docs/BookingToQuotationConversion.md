# Booking to SpecificQuotation Conversion

## Tổng quan

Tài liệu này mô tả cách tạo `SpecificQuotation` từ `Booking`, bao gồ<PERSON> vi<PERSON><PERSON> convert `SellingRate` thành `QuotationCharge` và `LocalHandlingCharges`.

## Kiến trúc

### Các thành phần chính

1. **BookingLogic**: Class chính chứa logic conversion
2. **SellingRate**: Đ<PERSON>i diện cho các rate trong booking
3. **QuotationCharge**: Đ<PERSON>i diện cho freight charges trong quotation
4. **LocalCharge**: Đ<PERSON>i diện cho local handling charges trong quotation
5. **SpecificQuotation**: Quotation được tạo từ booking

### Luồng xử lý

```
Booking (với SellingRates) 
    ↓
BookingLogic.createSpecificQuotationFromBooking()
    ↓
SpecificQuotation (với QuotationCharges + LocalCharges)
```

## C<PERSON><PERSON> sử dụng

### 1. Tạo SpecificQuotation từ Booking

```java
@Autowired
private BookingLogic bookingLogic;

public SpecificQuotation convertBookingToQuotation(ClientContext client, ICompany company, Booking booking) {
    // Tạo quotation từ booking
    SpecificQuotation quotation = bookingLogic.createSpecificQuotationFromBooking(client, company, booking);
    
    return quotation;
}
```

### 2. Tạo và lưu SpecificQuotation

```java
public SpecificQuotation convertAndSaveBookingToQuotation(ClientContext client, ICompany company, Booking booking) {
    // Tạo và lưu quotation từ booking
    SpecificQuotation savedQuotation = bookingLogic.createAndSaveSpecificQuotationFromBooking(client, company, booking);
    
    return savedQuotation;
}
```

## Chi tiết conversion

### SellingRate Types và cách xử lý

| SellingRate Type | Chuyển đổi thành | Mô tả |
|------------------|------------------|-------|
| `SEAFREIGHT` | `QuotationCharge` | Ocean freight rates |
| `AIRFREIGHT` | `QuotationCharge` | Air freight rates |
| `LOCAL_CHARGE` | `LocalCharge` | Local handling charges |
| `TRUCKING` | Không xử lý | Trucking charges (không convert) |
| `CUSTOM` | Không xử lý | Custom clearance (không convert) |

### QuotationCharge Creation

Khi tạo `QuotationCharge` từ freight rates:

1. **Sea FCL**: Tạo FCL price group với container rates
2. **Sea LCL**: Tạo LCL price group với selected price
3. **Air**: Tạo Air price group với selected price

### LocalCharge Creation

Local charges được tạo từ `LOCAL_CHARGE` type SellingRates:

1. Group charges theo container types
2. Convert thành `QuotationAdditionalCharge`
3. Convert thành `LocalCharge` format

## Ví dụ

### Sea FCL Booking

```java
// Tạo booking với Sea FCL rates
Booking booking = new Booking();
booking.setInquiry(seaFCLInquiry);

List<SellingRate> rates = new ArrayList<>();

// Ocean freight cho 20GP
SellingRate freight20GP = new SellingRate();
freight20GP.setType(Type.SEAFREIGHT);
freight20GP.setGroup(TransportationMode.SEA_FCL);
freight20GP.setUnitPrice(1500.0);
freight20GP.setUnit("20GP");
rates.add(freight20GP);

// Ocean freight cho 40GP
SellingRate freight40GP = new SellingRate();
freight40GP.setType(Type.SEAFREIGHT);
freight40GP.setGroup(TransportationMode.SEA_FCL);
freight40GP.setUnitPrice(2500.0);
freight40GP.setUnit("40GP");
rates.add(freight40GP);

// Terminal Handling Charge
SellingRate thc = new SellingRate();
thc.setType(Type.LOCAL_CHARGE);
thc.setTarget(ChargeTarget.ORIGIN);
thc.setCode("THC");
thc.setName("Terminal Handling Charge");
thc.setUnitPrice(200.0);
thc.setUnit("20GP");
rates.add(thc);

booking.setSellingRates(rates);

// Convert to quotation
SpecificQuotation quotation = bookingLogic.createSpecificQuotationFromBooking(client, company, booking);
```

### Air Booking

```java
// Tạo booking với Air rates
Booking booking = new Booking();
booking.setInquiry(airInquiry);

List<SellingRate> rates = new ArrayList<>();

// Air freight
SellingRate airFreight = new SellingRate();
airFreight.setType(Type.AIRFREIGHT);
airFreight.setGroup(TransportationMode.AIR);
airFreight.setUnitPrice(5.5);
airFreight.setUnit("KG");
rates.add(airFreight);

// Fuel surcharge
SellingRate fuelSurcharge = new SellingRate();
fuelSurcharge.setType(Type.LOCAL_CHARGE);
fuelSurcharge.setTarget(ChargeTarget.ORIGIN);
fuelSurcharge.setCode("FSC");
fuelSurcharge.setName("Fuel Surcharge");
fuelSurcharge.setUnitPrice(0.8);
fuelSurcharge.setUnit("KG");
rates.add(fuelSurcharge);

booking.setSellingRates(rates);

// Convert to quotation
SpecificQuotation quotation = bookingLogic.createSpecificQuotationFromBooking(client, company, booking);
```

## Lưu ý quan trọng

### 1. Validation

- Booking không được null
- Booking.inquiry không được null
- SellingRates có thể empty (sẽ tạo basic quotation)

### 2. Price Group Handling

- **Sea FCL**: Tạo price group với container rates (20GP, 40GP, 40HQ, 45HQ)
- **Sea LCL**: Tạo price group với selected price và quantity
- **Air**: Tạo price group với selected price

### 3. Currency và Exchange Rate

- Lấy từ freight rate đầu tiên
- Nếu không có freight rate, sử dụng default values

### 4. Local Charges Grouping

- Group charges theo code, name, target, mode
- Container charges được group theo container type
- Non-container charges giữ nguyên unit price

## Error Handling

```java
try {
    SpecificQuotation quotation = bookingLogic.createSpecificQuotationFromBooking(client, company, booking);
} catch (RuntimeException e) {
    // Handle validation errors
    if (e.getMessage().contains("Booking cannot be null")) {
        // Handle null booking
    } else if (e.getMessage().contains("inquiry cannot be null")) {
        // Handle null inquiry
    }
}
```

## Testing

Xem `BookingLogicTest.java` và `BookingToQuotationExample.java` để có ví dụ chi tiết về cách sử dụng và test logic này.

## Tương lai

Có thể mở rộng để hỗ trợ:

1. Trucking charges conversion
2. Custom clearance charges conversion
3. Commission distribution handling
4. Advanced price group calculations
