package cloud.datatp.fforwarder.price;

import cloud.datatp.fforwarder.core.db.CRMDaoService;
import cloud.datatp.fforwarder.core.message.CRMMessageLogic;
import cloud.datatp.fforwarder.core.message.entity.CRMMessageSystem;
import cloud.datatp.fforwarder.core.message.entity.MessageStatus;
import cloud.datatp.fforwarder.price.common.BulkCargoShipmentDetail;
import cloud.datatp.fforwarder.price.entity.BulkCargoInquiryRequest;
import cloud.datatp.fforwarder.price.entity.InquiryRequest;
import cloud.datatp.fforwarder.price.repository.BulkCargoInquiryRequestRepository;
import cloud.datatp.fforwarder.price.request.BulkCargoRemindMessagePlugin;
import jakarta.annotation.PostConstruct;

import java.util.Date;
import java.util.List;

import lombok.Getter;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.entity.Account;
import net.datatp.module.bot.BotService;
import net.datatp.module.communication.CommunicationMessageLogic;
import net.datatp.module.communication.MailMessage;
import net.datatp.module.communication.entity.CommunicationAccount;
import net.datatp.module.company.CompanyConfigLogic;
import net.datatp.module.company.entity.CompanyConfig;
import net.datatp.module.core.security.SecurityLogic;
import net.datatp.module.core.security.entity.AppPermission;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.data.db.seq.SeqService;
import net.datatp.module.graphapi.GraphApiService;
import net.datatp.module.hr.EmployeeLogic;
import net.datatp.module.zalo.ZaloLogic;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Getter
@Component
public class BulkCargoInquiryRequestLogic extends CRMDaoService {

  @Autowired
  private EmployeeLogic employeeLogic;

  @Autowired
  private AccountLogic accountLogic;

  @Autowired
  private CompanyConfigLogic companyConfigLogic;

  @Autowired
  private SecurityLogic securityLogic;

  @Autowired
  private CommunicationMessageLogic messageLogic;

  @Autowired
  private BulkCargoInquiryRequestRepository inquiryRequestRepo;

  @Autowired
  private GraphApiService graphApiService;

  @Autowired
  private SeqService seqService;

  @Autowired
  private CRMMessageLogic crmMessageLogic;

  @Autowired
  private ZaloLogic zaloLogic;

  @Autowired
  private BotService botService;

  @PostConstruct
  private void onInit() {
    seqService.createIfNotExists(BulkCargoInquiryRequest.SEQUENCE, 10);
  }

  public BulkCargoInquiryRequest getBulkCargoInquiryRequest(ClientContext client, Long requestId) {
    return inquiryRequestRepo.findById(requestId).get();
  }

  public BulkCargoInquiryRequest initBulkCargoInquiryRequest(ClientContext client, ICompany company, BulkCargoInquiryRequest request) {
    Account account = accountLogic.getAccountById(client, client.getAccountId());
    Objects.assertNotNull(account, "Account not found!!!, login id: " + client.getRemoteUser());
    CommunicationAccount messageAccount = messageLogic.getCommunicationAccount(client, client.getAccountId());
    if (messageAccount != null) {
      request.setSalemanEmail(messageAccount.getEmail());
      request.setSalemanPhone(messageAccount.getMobile());
    }
    request.setSalemanAccountId(account.getId());
    request.setSalemanLabel(account.getFullName());
    //set default mail to in request
    String builder = "mail.request.pricing.";
    CompanyConfig companyConfig = companyConfigLogic.getCompanyConfigByCompanyId(client, company.getId());
    String mailTo = companyConfig.getAttributeAsString(builder, null);
    if (StringUtil.isNotEmpty(mailTo)) request.withTo(mailTo);
    return request;
  }

  public BulkCargoInquiryRequest sendBulkCargoInquiryRequest(ClientContext client, ICompany company, BulkCargoInquiryRequest request) {
    BulkCargoInquiryRequest priceCheckRequest = saveBulkCargoInquiryRequest(client, request);
    MailMessage mailMessage = new MailMessage();
    mailMessage.setMessage(priceCheckRequest.getMailMessage());
    mailMessage.setFrom(priceCheckRequest.getSalemanEmail());
    mailMessage.setSubject(priceCheckRequest.getMailSubject());
    mailMessage.setTo(priceCheckRequest.getToList());
    mailMessage.setCc(priceCheckRequest.getCcList());
    mailMessage.setAttachments(request.getAttachments());
    graphApiService.sendEmailWithHtmlFormat(client, company, mailMessage);
    return priceCheckRequest;
  }

  public List<MapObject> saveBulkCargoInquiryRequestRecords(ClientContext client, List<MapObject> requests) {
    if (Collections.isNotEmpty(requests)) {
      for (MapObject request : requests) {
        BulkCargoInquiryRequest priceCheckRequest = new BulkCargoInquiryRequest();
        Long id = request.getLong("id", null);
        String oldPricingNote = null;
        String oldFeedback = null;
        Date oldRemindDate = null;
        if (id != null) {
          priceCheckRequest = getBulkCargoInquiryRequest(client, id);
          oldPricingNote = priceCheckRequest.getPricingNote();
          oldFeedback = priceCheckRequest.getFeedback();
          oldRemindDate = priceCheckRequest.getRemindDate();
        }
        priceCheckRequest = priceCheckRequest.mergeFromMapObject(request);

        final boolean ownerEdit = client.getAccountId() == priceCheckRequest.getSalemanAccountId();
        BulkCargoInquiryRequest.BulkCargoInquiryStatus status = BulkCargoInquiryRequest.BulkCargoInquiryStatus.parse(request.getString("status", null));

        if (StringUtil.isEmpty(priceCheckRequest.getTo())) {
          CommunicationAccount messageAccount = messageLogic.getCommunicationAccount(client, client.getAccountId());
          Objects.assertNotNull(messageAccount, "Message Account not found!!!, login id: " + client.getRemoteUser());
          priceCheckRequest.setTo(messageAccount.getEmail());
        }

        boolean pricingNoteChanged = StringUtil.isNotEmpty(priceCheckRequest.getPricingNote()) && !priceCheckRequest.getPricingNote().equals(oldPricingNote);
        boolean feedbackChanged = StringUtil.isNotEmpty(priceCheckRequest.getFeedback()) && !priceCheckRequest.getFeedback().equals(oldFeedback);
        boolean remindDateChanged = priceCheckRequest.getRemindDate() != null && !priceCheckRequest.getRemindDate().equals(oldRemindDate);
        Account accountById = accountLogic.getAccountById(client, client.getAccountId());

        if (!ownerEdit && !priceCheckRequest.isNew()) {
          String reason = request.getString("rejectReason", null);
          if (status.equals(BulkCargoInquiryRequest.BulkCargoInquiryStatus.REJECTED) && StringUtil.isNotEmpty(reason)) {
            CRMMessageSystem message = priceCheckRequest.toSalemanRejectMessage(client, accountById.getEmail(), reason);
            crmMessageLogic.scheduleMessage(client, message);
          } else if (pricingNoteChanged) {
            CRMMessageSystem message = priceCheckRequest.toSalemanPricingNoteMessage(client, accountById.getEmail());
            crmMessageLogic.scheduleMessage(client, message);
          }

          if (pricingNoteChanged || feedbackChanged || remindDateChanged) {
            List<CRMMessageSystem> pendingMessages = crmMessageLogic.findByReferenceAndPlugin(client,
              priceCheckRequest.getId(), BulkCargoInquiryRequest.TABLE_NAME, BulkCargoRemindMessagePlugin.PLUGIN_TYPE, MessageStatus.PENDING);

            if (Collections.isNotEmpty(pendingMessages)) {
              for (CRMMessageSystem m : pendingMessages) {
                m.setStatus(MessageStatus.CANCELLED);
                crmMessageLogic.saveMessage(client, m);
              }
            }
          }

          if (priceCheckRequest.getRemindDate() != null) {
            CRMMessageSystem message = priceCheckRequest.toSalemanRemindMessage(client, accountById.getEmail());
            crmMessageLogic.scheduleMessage(client, message);
          }
        }

        if (ownerEdit && !priceCheckRequest.isNew() && feedbackChanged) {
          CRMMessageSystem message = priceCheckRequest.toPricingFeedbackMessage(client, accountById.getEmail());
          crmMessageLogic.scheduleMessage(client, message);
        }

        BulkCargoInquiryRequest savedRequest = saveBulkCargoInquiryRequest(client, priceCheckRequest);
        request.put("id", savedRequest.getId());
      }
    }
    return requests;
  }

  public BulkCargoInquiryRequest saveBulkCargoInquiryRequest(ClientContext client, BulkCargoInquiryRequest request) {
    if (StringUtil.isEmpty(request.getCode())) {
      BulkCargoShipmentDetail shipmentDetail = request.getShipmentDetail();
      Objects.assertNotNull(shipmentDetail, "Shipment Detail must be not null!");
      String prefix = shipmentDetail.getCargoType().getCode() + DateUtil.asCompactMonthId(new Date());
      String code = prefix + String.format("%05d", seqService.nextSequence(InquiryRequest.SEQUENCE));
      request.setCode(code);
    }

    request.computeMailSubject();
    if (request.getRequestDate() == null) {
      request.setRequestDate(new Date());
    }
    if (request.getSalemanAccountId() == null) {
      CommunicationAccount messageAccount = messageLogic.getCommunicationAccount(client, client.getAccountId());
      Objects.assertNotNull(messageAccount, "Message Account not found!!!, login id: " + client.getRemoteUser());
      request.setSalemanEmail(messageAccount.getEmail());
      request.setSalemanPhone(messageAccount.getMobile());
    }
    if (request.isNew()) {
      request.set(client, client.getCompany());
    } else {
      request.set(client);
    }
    return inquiryRequestRepo.save(request);
  }

  public List<SqlMapRecord> searchBulkCargoInquiryRequests(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    sqlParams.addParam("accessAccountId", client.getAccountId());
    sqlParams.addParam("companyId", client.getCompanyId());
    AppPermission pricePermission = securityLogic.getAppPermission(client, company.getId(), "logistics", "user-logistics-prices");
    if (pricePermission == null) return java.util.Collections.emptyList();
    if (!sqlParams.hasParam("space")) sqlParams.addParam("space", "User");
    if (pricePermission.isOwnerScope()) {
      sqlParams.addParam("space", "User");
    }
    if (sqlParams.hasParam("filterMode") && StringUtil.isNotEmpty(sqlParams.getString("filterMode"))) {
      CommunicationAccount messageAccount = messageLogic.getCommunicationAccount(client, client.getAccountId());
      Objects.assertNotNull(messageAccount, "Message Account not found!!!, login id: " + client.getRemoteUser());
      sqlParams.addParam("mailPattern", messageAccount.getEmail());
    }
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/price/groovy/BulkCargoInquiryRequestSql.groovy";
    return searchDbRecords(client, scriptDir, scriptFile, "SearchBulkCargoInquiryRequest", sqlParams);
  }

  public List<SqlMapRecord> searchBulkCargoInquiryRequestSpace(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/price/groovy/BulkCargoInquiryRequestSql.groovy";
    sqlParams.addParam("accessAccountId", client.getAccountId());
    sqlParams.addParam("companyId", client.getCompanyId());
    AppPermission pricePermission = securityLogic.getAppPermission(client, company.getId(), "logistics", "user-logistics-prices");
    if (pricePermission == null) return java.util.Collections.emptyList();
    if (!sqlParams.hasParam("space")) sqlParams.addParam("space", "User");
    return searchDbRecords(client, scriptDir, scriptFile, "SearchBulkCargoInquiryRequestSpace", sqlParams);
  }

  public boolean deleteBulkCargoInquiryRequests(ClientContext client, ICompany company, List<Long> ids) {
    inquiryRequestRepo.deleteAllById(ids);
    return true;
  }

  public List<BulkCargoInquiryRequest> findByCompany(ClientContext client, ICompany company) {
    return inquiryRequestRepo.findByCompany(company.getId());
  }

  public List<BulkCargoInquiryRequest> findBulkCargoInquiryRequestsByDate(ClientContext client, Date startDate, Date endDate) {
    return inquiryRequestRepo.findBulkCargoInquiryRequestsByDate(client.getCompanyId(), startDate, endDate);
  }

}