package cloud.datatp.fforwarder.core.template;

import cloud.datatp.fforwarder.core.db.CRMDaoService;
import cloud.datatp.fforwarder.core.template.entity.CrmUserPermissionTemplate;
import cloud.datatp.fforwarder.core.template.entity.CrmUserRole;
import cloud.datatp.fforwarder.core.template.entity.PermissionScope;
import cloud.datatp.fforwarder.core.template.entity.ResourceType;
import cloud.datatp.fforwarder.core.template.repository.CrmUserPermissionTemplateRepository;
import cloud.datatp.fforwarder.core.template.repository.CrmUserRoleRepository;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.security.client.ClientContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class CRMPermissionTemplateLogic extends CRMDaoService {

  @Autowired
  private CrmUserPermissionTemplateRepository permissionRepo;

  @Autowired
  private CrmUserRoleRepository userRoleRepo;

  // Basic CRUD operations
  public CrmUserPermissionTemplate getById(ClientContext client, Long id) {
    return permissionRepo.findById(id).orElse(null);
  }

  public CrmUserPermissionTemplate getByAccountId(ClientContext client, Long accountId) {
    return permissionRepo.getByAccountId(accountId);
  }

  public List<CrmUserPermissionTemplate> findByCompanyId(ClientContext client, Long companyId) {
    return permissionRepo.findByCompanyId(companyId);
  }

  public List<CrmUserPermissionTemplate> findAllPermissions(ClientContext client) {
    return permissionRepo.findAll();
  }

  // Permission checking methods
  public boolean hasAgentViewPermission(ClientContext client, Long accountId, PermissionScope scope) {
    return permissionRepo.hasAgentViewPermission(accountId, scope);
  }

  public boolean hasCustomerViewPermission(ClientContext client, Long accountId, PermissionScope scope) {
    return permissionRepo.hasCustomerViewPermission(accountId, scope);
  }

  public boolean hasEditPermission(ClientContext client, Long accountId, ResourceType resourceType) {
    return permissionRepo.hasEditPermission(accountId, resourceType.name());
  }

  public boolean hasApprovePermission(ClientContext client, Long accountId, ResourceType resourceType) {
    return permissionRepo.hasApprovePermission(accountId, resourceType.name());
  }

  // Using entity methods for more complex permission checks
  public boolean canViewResource(ClientContext client, Long accountId, ResourceType resourceType, PermissionScope requiredScope) {
    CrmUserPermissionTemplate permission = getByAccountId(client, accountId);
    if (permission == null) return false;

    return permission.canView(resourceType, requiredScope);
  }

  public boolean canEditResource(ClientContext client, Long accountId, ResourceType resourceType, PermissionScope requiredScope) {
    CrmUserPermissionTemplate permission = getByAccountId(client, accountId);
    if (permission == null) return false;

    return permission.canEdit(resourceType, requiredScope);
  }

  public boolean canApproveResource(ClientContext client, Long accountId, ResourceType resourceType, PermissionScope requiredScope) {
    CrmUserPermissionTemplate permission = getByAccountId(client, accountId);
    if (permission == null) return false;

    return permission.canApprove(resourceType, requiredScope);
  }

  // User discovery methods
  public List<CrmUserPermissionTemplate> findUsersWithAgentViewScope(ClientContext client, PermissionScope scope) {
    return permissionRepo.findUsersWithAgentViewScope(scope);
  }

  public List<CrmUserPermissionTemplate> findUsersWithCustomerViewScope(ClientContext client, PermissionScope scope) {
    return permissionRepo.findUsersWithCustomerViewScope(scope);
  }

  public List<CrmUserPermissionTemplate> findUsersWithColoaderViewScope(ClientContext client, PermissionScope scope) {
    return permissionRepo.findUsersWithColoaderViewScope(scope);
  }
  
  public List<CrmUserPermissionTemplate> findUsersWithAgentApproveScope(ClientContext client, PermissionScope scope) {
    return permissionRepo.findUsersWithAgentApproveScope(scope);
  }
  
  public List<CrmUserPermissionTemplate> findUsersWithCustomerApproveScope(ClientContext client, PermissionScope scope) {
    return permissionRepo.findUsersWithCustomerApproveScope(scope);
  }
  
  public List<CrmUserPermissionTemplate> findUsersWithColoaderApproveScope(ClientContext client, PermissionScope scope) {
    return permissionRepo.findUsersWithColoaderApproveScope(scope);
  }

  public List<CrmUserPermissionTemplate> findUsersWithGroupAllPermissions(ClientContext client) {
    return permissionRepo.findUsersWithGroupAllViewPermissions();
  }

  public List<CrmUserPermissionTemplate> findUsersWithCompanyPermissions(ClientContext client, Long companyId) {
    return permissionRepo.findUsersWithCompanyViewPermissions(companyId);
  }

  public List<CrmUserPermissionTemplate> findUsersWhoCanViewCompanyData(ClientContext client, Long companyId) {
    return permissionRepo.findUsersWhoCanViewCompanyData(companyId);
  }

  // Permission template management
  public CrmUserPermissionTemplate createPermissionTemplate(ClientContext client, CrmUserRole userRole) {
    CrmUserPermissionTemplate existing = getByAccountId(client, userRole.getAccountId());
    if (existing != null) {
      log.warn("Permission template already exists for account: {}", userRole.getAccountId());
      return existing;
    }
    CrmUserPermissionTemplate permission = new CrmUserPermissionTemplate(userRole);
    return savePermissionTemplate(client, permission);
  }

  public CrmUserPermissionTemplate savePermissionTemplate(ClientContext client, CrmUserPermissionTemplate permission) {
    permission.set(client);
    return permissionRepo.save(permission);
  }

  public CrmUserPermissionTemplate updateResourcePermissions(ClientContext client, Long accountId, ResourceType resourceType,
    PermissionScope viewScope, PermissionScope editScope, PermissionScope approveScope) {
    CrmUserPermissionTemplate permission = getByAccountId(client, accountId);
    if (permission == null) {
      throw new RuntimeException("Permission template not found for account: " + accountId);
    }
    permission.setResourcePermissions(resourceType, viewScope, editScope, approveScope);
    return savePermissionTemplate(client, permission);
  }

  // Batch operations using entity methods
  public void grantFullPermissions(ClientContext client, Long accountId) {
    CrmUserPermissionTemplate permission = getByAccountId(client, accountId);
    if (permission == null) return;

    permission.initAllFullPermissions();
    savePermissionTemplate(client, permission);
  }

  public void grantCompanyPermissions(ClientContext client, Long accountId, ResourceType... resourceTypes) {
    CrmUserPermissionTemplate permission = getByAccountId(client, accountId);
    if (permission == null) return;

    if (resourceTypes.length == 0) {
      permission.initAllCompanyPermissions();
    } else {
      permission.initCompanyPermission(resourceTypes);
    }
    savePermissionTemplate(client, permission);
  }

  public void grantSelfOnlyPermissions(ClientContext client, Long accountId, ResourceType... resourceTypes) {
    CrmUserPermissionTemplate permission = getByAccountId(client, accountId);
    if (permission == null) return;

    if (resourceTypes.length == 0) {
      permission.initAllSelfOnlyPermissions();
    } else {
      permission.initSelfOnlyPermission(resourceTypes);
    }
    savePermissionTemplate(client, permission);
  }

  public void grantReadOnlyPermissions(ClientContext client, Long accountId, PermissionScope viewScope, ResourceType... resourceTypes) {
    CrmUserPermissionTemplate permission = getByAccountId(client, accountId);
    if (permission == null) return;

    for (ResourceType resourceType : resourceTypes) {
      permission.initReadOnlyPermission(resourceType, viewScope);
    }
    savePermissionTemplate(client, permission);
  }

  // Batch operations for multiple users
  public List<CrmUserPermissionTemplate> createPermissionTemplatesForAllUsers(ClientContext client) {
    List<CrmUserRole> allUsers = userRoleRepo.findAll();
    return allUsers.stream()
      .filter(userRole -> getByAccountId(client, userRole.getAccountId()) == null)
      .map(userRole -> createPermissionTemplate(client, userRole))
      .toList();
  }

  public List<CrmUserPermissionTemplate> findByAccountIds(ClientContext client, List<Long> accountIds) {
    return permissionRepo.findByAccountIds(accountIds);
  }

  // Search functionality
  public List<SqlMapRecord> searchPermissionTemplates(ClientContext client, SqlQueryParams sqlParams) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/core/groovy/CrmUserPermissionTemplateSql.groovy";
    return searchDbRecords(client, scriptDir, scriptFile, "SearchCrmUserPermissionTemplate", sqlParams);
  }

  // Delete operations
  public boolean deletePermissionTemplate(ClientContext client, Long id) {
    permissionRepo.deleteById(id);
    return true;
  }

  public boolean deleteByAccountId(ClientContext client, Long accountId) {
    CrmUserPermissionTemplate permission = getByAccountId(client, accountId);
    if (permission != null) {
      permissionRepo.deleteById(permission.getId());
      return true;
    }
    return false;
  }

  public boolean deleteByIds(ClientContext client, List<Long> ids) {
    permissionRepo.deleteAllById(ids);
    return true;
  }

  // Utility methods
  public boolean permissionExists(ClientContext client, Long accountId) {
    return getByAccountId(client, accountId) != null;
  }

  // Sync permissions with user roles
  public int syncPermissionsWithUserRoles(ClientContext client) {
    List<CrmUserRole> allUserRoles = userRoleRepo.findAll();
    int count = 0;

    for (CrmUserRole userRole : allUserRoles) {
      if (getByAccountId(client, userRole.getAccountId()) == null) {
        try {
          createPermissionTemplate(client, userRole);
          count++;
          log.info("Created permission template for user: {}", userRole.getFullName());
        } catch (Exception e) {
          log.error("Error creating permission template for user: {}", userRole.getFullName(), e);
        }
      }
    }

    return count;
  }

  // Helper methods for specific permission scenarios
  public void setupSalesAgentPermissions(ClientContext client, Long accountId) {
    CrmUserPermissionTemplate permission = getByAccountId(client, accountId);
    if (permission == null) return;

    // Sales agents typically need company-level customer and lead permissions
    permission.initCompanyPermission(ResourceType.CUSTOMER, ResourceType.CUSTOMER_LEAD);
    permission.initSelfOnlyPermission(ResourceType.AGENT, ResourceType.COLOADER, ResourceType.AGENT_POTENTIAL);
    savePermissionTemplate(client, permission);
  }

  public void setupManagerPermissions(ClientContext client, Long accountId) {
    CrmUserPermissionTemplate permission = getByAccountId(client, accountId);
    if (permission == null) return;

    // Managers typically need broader permissions
    permission.initCompanyPermission(ResourceType.CUSTOMER, ResourceType.CUSTOMER_LEAD, ResourceType.AGENT);
    permission.initSelfOnlyPermission(ResourceType.COLOADER, ResourceType.AGENT_POTENTIAL);
    savePermissionTemplate(client, permission);
  }

  public void setupAdminPermissions(ClientContext client, Long accountId) {
    CrmUserPermissionTemplate permission = getByAccountId(client, accountId);
    if (permission == null) return;

    // Admins get full permissions
    permission.initAllFullPermissions();
    savePermissionTemplate(client, permission);
  }

}