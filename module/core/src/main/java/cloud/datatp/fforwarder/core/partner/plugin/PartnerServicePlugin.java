package cloud.datatp.fforwarder.core.partner.plugin;

import cloud.datatp.fforwarder.core.partner.entity.CRMPartner;
import net.datatp.module.data.db.plugin.ServicePlugin;
import net.datatp.security.client.ClientContext;

abstract public class PartnerServicePlugin extends ServicePlugin {

  protected PartnerServicePlugin(String type) {
    super("crm", "CRMPartnerService", type);
  }

  public void onPreSave(ClientContext client, CRMPartner partner, boolean isNew) {
  }

  public void onPostSave(ClientContext client, CRMPartner partner, boolean isNew) {
  }

}