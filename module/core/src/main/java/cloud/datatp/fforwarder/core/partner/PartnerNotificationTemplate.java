package cloud.datatp.fforwarder.core.partner;

import cloud.datatp.fforwarder.core.partner.entity.BFSOnePartnerGroup;
import cloud.datatp.fforwarder.core.partner.entity.CRMPartner;
import cloud.datatp.fforwarder.core.partner.entity.PartnerRequest;
import cloud.datatp.fforwarder.core.partner.entity.PartnerRequest.PartnerRequestStatus;

import java.text.SimpleDateFormat;
import java.util.Date;

import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;

public class PartnerNotificationTemplate {

  public static String buildPartnerCodeSyncZaloMessage(
    CRMPartner partner,
    String oldPartnerCode,
    String updatedBy,
    String successMessage,
    String errorMessage) {

    String currentDate = new SimpleDateFormat("dd/MM/yyyy HH:mm").format(new Date());
    StringBuilder message = new StringBuilder();

    // Start with error message if present
    if (StringUtil.isNotEmpty(errorMessage)) {
      message.append(String.format("""
          ❌ THÔNG BÁO LỖI: Cập nhật Mã Đối tác BFSOne thất bại (%s)
          ━━━━━━━━━━━━━━━━━━━━
          
          ⚠️ Lỗi: %s
          
          """,
        currentDate,
        errorMessage
      ));
    } else {
      message.append(String.format("""
          ✅ THÔNG BÁO: Cập nhật Mã Đối tác BFSOne thành công (%s)
          ━━━━━━━━━━━━━━━━━━━━
          
          """,
        currentDate
      ));
    }

    // Add partner details
    message.append(String.format("""
        🏢 Tên đối tác: %s
        🔢 Mã cũ: %s
        🔢 Mã mới: %s
        """,
      StringUtil.isNotEmpty(partner.getName()) ? partner.getName() : "N/A",
      StringUtil.isNotEmpty(oldPartnerCode) ? oldPartnerCode : "Chưa có mã",
      StringUtil.isNotEmpty(partner.getPartnerCode()) ? partner.getPartnerCode() : "N/A"
    ));

    // Add additional partner information if available
    if (StringUtil.isNotEmpty(partner.getTaxCode())) {
      message.append(String.format("""
        
        📋 Mã số thuế: %s
        """, partner.getTaxCode()));
    }
    // Add success message if present and no error
    if (StringUtil.isNotEmpty(successMessage) && StringUtil.isEmpty(errorMessage)) {
      message.append(String.format("""
        
        📝 Thông báo: %s
        """, successMessage));
    }

    message.append(String.format("""
        
        👤 Người thực hiện: %s
        📅 Thời gian: %s
        """,
      updatedBy,
      currentDate
    ));

    // Add system message based on success/error
    if (StringUtil.isNotEmpty(errorMessage)) {
      message.append("""
        
        ⚠️ Vui lòng kiểm tra lại thông tin và thử lại.
        """);
    } else {
      message.append("""
        
        ⚡️ Hệ thống đã cập nhật mã đối tác thành công.
        """);
    }

    message.append("\n━━━━━━━━━━━━━━━━━━━━");
    return message.toString();
  }

  public static String buildPartnerSyncZaloMessage(CRMPartner partner, String updatedBy, String email, boolean isNewPartner) {
    String currentDate = new SimpleDateFormat("dd/MM/yyyy HH:mm").format(new Date());
    String action = isNewPartner ? "thêm mới" : "cập nhật";
    String emoji = isNewPartner ? "🆕" : "🔄";
    String actionPast = isNewPartner ? "tạo mới" : "cập nhật";

    StringBuilder message = new StringBuilder();

    message.append(String.format("""
        %s ĐỒNG BỘ %s ĐỐI TÁC BFSONE
        ━━━━━━━━━━━━━━━━━━━━
        
        📌 Hệ thống đã %s thông tin đối tác thành công
        
        🏢 Tên đối tác: %s
        🔢 Mã đối tác: %s
        """,
      emoji,
      action.toUpperCase(),
      actionPast,
      StringUtil.isNotEmpty(partner.getName()) ? partner.getName() : "N/A",
      StringUtil.isNotEmpty(partner.getPartnerCode()) ? partner.getPartnerCode() : "N/A"
    ));

    // Add additional partner information if available
    if (StringUtil.isNotEmpty(partner.getTaxCode())) {
      message.append(String.format("""
        
        📋 Mã số thuế: %s
        """, partner.getTaxCode()));
    }

    if (StringUtil.isNotEmpty(partner.getEmail())) {
      message.append(String.format("""
        
        📧 Email: %s
        """, partner.getEmail()));
    }

    message.append(String.format("""
        
        👤 Người thực hiện: %s
        📧 Email: %s
        📅 Thời gian: %s
        
        ⚡️ Hệ thống đã %s thông tin đối tác thành công.
        ━━━━━━━━━━━━━━━━━━━━
        """,
      updatedBy,
      email,
      currentDate,
      isNewPartner ? "tạo mới" : "cập nhật"
    ));

    return message.toString();
  }

  public static String buildPartnerRequestMailRequestMessage(PartnerRequest partnerRequest, BFSOnePartnerGroup partnerGroup) {
    String partnerGroupLabel;
    switch (partnerGroup) {
      case CUSTOMERS -> partnerGroupLabel = "Customer";
      case COLOADERS -> partnerGroupLabel = "Coloader";
      case AGENTS -> partnerGroupLabel = "Agent";
      default -> partnerGroupLabel = "Partner";
    }
    
    String partnerName = partnerRequest.getPartnerName();
    String approvedByLabel = StringUtil.isNotEmpty(partnerRequest.getApprovedByLabel()) ? partnerRequest.getApprovedByLabel() : "N/A";
    String requestByLabel = StringUtil.isNotEmpty(partnerRequest.getRequestByLabel()) ? partnerRequest.getRequestByLabel() : "N/A";
    String requestDate = partnerRequest.getRequestDate() != null ? DateUtil.asCompactDate(partnerRequest.getRequestDate()) : DateUtil.asCompactDate(new Date());
    
    return String.format("""
        <div style="font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,Helvetica,Arial,sans-serif;
                    max-width:600px;margin:0 auto;padding:20px;background-color:#f9fafb;">
          <div style="background-color:#ffffff;padding:30px;border-radius:12px;
                      box-shadow:0 4px 6px rgba(0,0,0,0.1);">
            <h1 style="color:#111827;font-size:20px;margin:0 0 20px 0;text-align:center;">
              %s Creation Request for <strong>%s</strong>
            </h1>

            <p style="color:#374151;font-size:16px;margin:0 0 20px 0;">
              Dear %s,
            </p>
            <p style="color:#374151;font-size:16px;margin:0 0 20px 0;line-height:1.6;">
              We would like to inform you that the %s creation request for
              <strong>%s</strong> has been received.
            </p>

            <div style="background-color:#F3F4F6;padding:20px;border-radius:8px;margin:20px 0;
                        border-left:4px solid #E5E7EB;">
              <h3 style="color:#111827;margin:0 0 15px 0;font-size:18px;">Request Details</h3>

              <div style="margin-bottom:12px;">
                <strong style="color:#6B7280;">%s Name:</strong>
                <span style="color:#374151;">%s</span>
              </div>

              <div style="margin-bottom:12px;">
                <strong style="color:#6B7280;">Requested By:</strong>
                <span style="color:#374151;">%s</span>
              </div>

              <div style="margin-bottom:12px;">
                <strong style="color:#6B7280;">Request Date:</strong>
                <span style="color:#374151;">%s</span>
              </div>
            </div>

            <!-- Hướng dẫn duyệt yêu cầu -->
            <div style="margin: 20px 0; text-align: center;">
                <p style="color:#374151;font-size:15px;margin-bottom:12px;">
                    Please access here to approve the request:
                </p>
                <a href="https://beelogistics.cloud" target="_blank"
                   style="display:inline-block;background-color:#2563eb;color:#ffffff;
                          padding:12px 24px;border-radius:6px;text-decoration:none;
                          font-weight:600;font-size:15px;">
                    Bee Logistics Cloud
                </a>
            </div>

            <div style="margin-top: 30px; text-align: center;">
                <p style="color: #374151; margin: 0;">
                    Best regards,<br>
                    <strong>CRM Management Team</strong>
                </p>
            </div>

            <div style="margin-top: 24px; padding-top: 16px; border-top: 1px solid #e5e7eb; text-align: center;">
                <p style="color: #6b7280; font-size: 14px; margin: 0;">
                    This is an automated notification from the CRM Task Management System.
                </p>
            </div>
          </div>
        </div>
        """, 
        partnerGroupLabel,
        partnerName,
        approvedByLabel,
        partnerGroupLabel,
        partnerName,
        partnerGroupLabel,
        partnerName,
        requestByLabel,
        requestDate
    );
  }
  
  public static String buildParterRequestMailApprovalMessage(PartnerRequest partnerRequest, CRMPartner partner) {
    boolean isApproved = partnerRequest.getStatus() == PartnerRequestStatus.APPROVED;
    String normalizedStatus = isApproved ? "Approved" : "Rejected";

    String titleColor = isApproved ? "#1f2937" : "#b91c1c";
    String badgeColor = isApproved ? "#1f2937" : "#b91c1c";
    String panelBg = isApproved ? "#f8fafc" : "#fef2f2";
    String panelBorder = isApproved ? "#3b82f6" : "#ef4444";
    String sectionTitle = isApproved ? "#1f2937" : "#b91c1c";

    String partnerID = "";
    if (isApproved) {
      partnerID = String.format("""
          <div style="margin-bottom:12px;">
            <strong style="color:%s;">Partner ID:</strong>
            <span style="color:#374151;">%s</span>
          </div>
          """, sectionTitle, partner.getPartnerCode());
    }

    String approvedByTitle = isApproved ? "Approved By" : "Reviewed By";
    String approvedDateTitle = isApproved ? "Approved Date" : "Reviewed Date";
    String noteTitle = isApproved ? "Note" : "Rejection Reason";
    
    String partnerName = partner.getName() != null ? partner.getName() : "N/A";
    String requestByLabel = partnerRequest.getRequestByLabel() != null ? partnerRequest.getRequestByLabel() : "N/A";
    String requestDate = partnerRequest.getRequestDate() != null ? DateUtil.asCompactDate(partnerRequest.getRequestDate()) : "N/A";
    String approvedByLabel = partnerRequest.getApprovedByLabel() != null ? partnerRequest.getApprovedByLabel() : "N/A";
    String approvedDate = partnerRequest.getApprovedDate() != null ? DateUtil.asCompactDate(partnerRequest.getApprovedDate()) : "TDB";
    String approvedNote = partnerRequest.getApprovedNote() != null ? partnerRequest.getApprovedNote() : "";
    
    return String.format("""
        <div style="font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,Helvetica,Arial,sans-serif;
                    max-width:600px;margin:0 auto;padding:20px;background-color:#f9fafb;">
          <div style="background-color:#ffffff;padding:30px;border-radius:12px;
                      box-shadow:0 4px 6px rgba(0,0,0,0.1);">
            <h1 style="color:%s;font-size:20px;margin:0 0 20px 0;text-align:center;">
              Partner Creation Request for <strong>%s</strong> –
              <strong style="color:%s;">%s</strong>
            </h1>

            <p style="color:#374151;font-size:16px;margin:0 0 20px 0;">
              Dear %s,
            </p>
            <p style="color:#374151;font-size:16px;margin:0 0 20px 0;line-height:1.6;">
              We would like to inform you that the partner creation request for
              <strong>%s</strong> has been <strong>%s</strong>.
            </p>

            <div style="background-color:%s;padding:20px;border-radius:8px;margin:20px 0;
                        border-left:4px solid %s;">
              <h3 style="color:%s;margin:0 0 15px 0;font-size:18px;">Request Details</h3>

              %s

              <div style="margin-bottom:12px;">
                <strong style="color:%s;">Partner Name:</strong>
                <span style="color:#374151;">%s</span>
              </div>

              <div style="margin-bottom:12px;">
                <strong style="color:%s;">Requested By:</strong>
                <span style="color:#374151;">%s</span>
              </div>

              <div style="margin-bottom:12px;">
                <strong style="color:%s;">Request Date:</strong>
                <span style="color:#374151;">%s</span>
              </div>

              <div style="margin-bottom:12px;">
                <strong style="color:%s;">%s:</strong>
                <span style="color:#374151;">%s</span>
              </div>

              <div style="margin-bottom:12px;">
                <strong style="color:%s;">%s:</strong>
                <span style="color:#374151;">%s</span>
              </div>

              <div style="margin-bottom:12px;">
                <strong style="color:%s;">%s:</strong>
                <span style="color:#374151;">%s</span>
              </div>
            </div>

            <div style="margin-top: 30px; text-align: center;">
                <p style="color: #374151; margin: 0;">
                    Best regards,<br>
                    <strong>CRM Management Team</strong>
                </p>
            </div>

            <div style="margin-top: 24px; padding-top: 16px; border-top: 1px solid #e5e7eb; text-align: center;">
                <p style="color: #6b7280; font-size: 14px; margin: 0;">
                    This is an automated notification from the CRM Task Management System.
                </p>
            </div>
          </div>
        </div>
        """,
        titleColor, partnerName, badgeColor, normalizedStatus, requestByLabel, partnerName, normalizedStatus,
        panelBg, panelBorder, sectionTitle,
        partnerID,
        sectionTitle, partnerName, sectionTitle, StringUtil.isNotEmpty(requestByLabel) ? requestByLabel : "N/A",
        sectionTitle, requestDate, sectionTitle, approvedByTitle,
        approvedByLabel, sectionTitle, approvedDateTitle,
        approvedDate, sectionTitle, noteTitle,
        approvedNote
    );
  }
}