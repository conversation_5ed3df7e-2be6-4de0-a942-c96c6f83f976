package cloud.datatp.fforwarder.core.partner;

import cloud.datatp.fforwarder.core.partner.entity.CRMPartner;
import cloud.datatp.fforwarder.core.template.entity.CrmUserRole;
import java.util.List;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.hr.entity.Employee;
import net.datatp.module.service.BaseComponent;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.MapObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("CRMPartnerService")
public class CRMPartnerService extends BaseComponent {

  @Autowired
  private CRMPartnerLogic crmPartnerLogic;

  @Transactional(readOnly = true)
  public CRMPartner getCRMPartner(ClientContext client, Long id) {
    return crmPartnerLogic.getById(client, id);
  }

  @Transactional(readOnly = true)
  public CRMPartner getCRMPartnerByCode(ClientContext client, String code) {
    return crmPartnerLogic.getByCode(client, code);
  }

  @Transactional(readOnly = true)
  public List<CRMPartner> findBFSOnePartnerByCodeTemp(ClientContext client, String codeTemp) {
    return crmPartnerLogic.findByBFSOneCodeTemp(client, codeTemp);
  }

  @Transactional(readOnly = true)
  public List<CRMPartner> findByTaxCode(ClientContext client, String taxCode) {
    return crmPartnerLogic.findByTaxCode(client, taxCode);
  }

  @Transactional
  public int deleteBFSOnePartners(ClientContext client, List<Long> targetIds) {
    return crmPartnerLogic.deleteCRMPartner(client, targetIds);
  }

  @Transactional(readOnly = true)
  public CRMPartner getByPartnerId(ClientContext client, Long partnerId) {
    return crmPartnerLogic.getByAccountId(client, partnerId);
  }

  @Transactional
  public CRMPartner saveCRMPartner(ClientContext client, CRMPartner partner) {
    return crmPartnerLogic.save(client, partner);
  }

  @Transactional
  public CRMPartner createBFSOnePartner(ClientContext client, CRMPartner partner) {
    return crmPartnerLogic.createBFSPartner(client, partner);
  }

  //TODO: Remove this method!!!
  @Deprecated
  @Transactional
  public List<CRMPartner> importPartners(ClientContext client, Employee saleman, List<CRMPartner> partners) {
    return crmPartnerLogic.importPartners(client, saleman, partners);
  }

  @Transactional
  public List<CRMPartner> importPartners(ClientContext client, CrmUserRole userRole, List<CRMPartner> partners) {
    return crmPartnerLogic.importPartners(client, userRole, partners);
  }

  @Transactional
  public List<CRMPartner> importPartners(ClientContext client, List<MapObject> partners) {
    return crmPartnerLogic.importPartners(client, partners);
  }

  @Transactional
  public List<CRMPartner> syncBFSOnePartnersBySaleman(ClientContext client, Long salemanAccountId) {
    return crmPartnerLogic.syncBFSOnePartnersBySaleman(client, salemanAccountId);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchBFSOnePartners(ClientContext client, SqlQueryParams params) {
    return crmPartnerLogic.searchCRMPartners(client, params);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchBFSOnePartnerCustomers(ClientContext client, SqlQueryParams params) {
    return crmPartnerLogic.searchCRMPartnerCustomers(client, params);
  }

  @Transactional
  public CRMPartner syncBFSOnePartnersByCode(ClientContext client, String partnerCode) {
    return crmPartnerLogic.syncBFSOnePartnersByCode(client, partnerCode);
  }

}