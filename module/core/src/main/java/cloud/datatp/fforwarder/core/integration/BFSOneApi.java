package cloud.datatp.fforwarder.core.integration;

import cloud.datatp.fforwarder.core.partner.CRMPartnerLogic;
import cloud.datatp.fforwarder.core.partner.entity.BFSOnePartnerGroup;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.httpclient.HttpClient;
import net.datatp.security.client.ClientContext;
import net.datatp.util.dataformat.DataSerializer;
import net.datatp.util.ds.MapObject;
import net.datatp.util.error.RuntimeError;
import net.datatp.util.text.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

@Getter
@Slf4j
@Component
public class BFSOneApi {
  private final String accessCode = "jWQ953gZwSg6FLfnfCtJmdAxZJcYEABSQX8HOjAguSgmLHtFxThGi6ZDuBmoo1hNNI3W3r6FGPDYuGacRxYOIxvQUfoTOt4LL6rzcrIBwyTBa77Nvigxwotj8Ay97iqyiQOZ51zFhvBpWmXTG6/l/gNW+LmQ8WmX6RRzLOzbRmD5RBRdl5HwE8O5EHwjaeHMSPHG+BO+L3zHO69XWN/b3TqO+M2xdsPF32/FlwYDNKxNjDA4uJIPSySnwXw2iph0Zf7t+Ws6oT5mRYvF30r1bRYuOvKOMAQBgPiJ9PO5JkPUwsOKF2BeNBG0Y84fH6DL3fxqhcVl6VmHeVmab+wE+g==";

  private final HttpClient client;

  @Autowired
  public BFSOneApi() {
    String BASE_URL = "https://api.beelogistics.com/ofone/";
    client = new HttpClient(BASE_URL);
  }

  private static HttpHeaders getHttpHeaders(String token) {
    HttpHeaders headers = new HttpHeaders();
    headers.add(HttpHeaders.CONTENT_TYPE, "application/json");
    headers.add(HttpHeaders.ACCEPT, "*/*");
    headers.add("AccessCode", token);
    return headers;
  }

  public String authenticate(String userid, String username) {
    log.info("Starting authentication process for user: {}", username);
    HttpHeaders headers = client.createHeaders();
    headers.add("AccessCode", this.accessCode);

    Map<String, String> reqBody = new HashMap<>();
    reqBody.put("userid", userid);
    reqBody.put("username", username);
    String reqBodyAsJson = DataSerializer.JSON.toString(reqBody);
    ResponseEntity<String> response = client.post("authorize/gettoken", headers, reqBodyAsJson, String.class);
    try {
      String responseBody = response.getBody();
      if (responseBody == null) log.error("API response is empty");
      MapObject mapObject = DataSerializer.JSON.fromString(responseBody, MapObject.class);
      Boolean error = mapObject.getBoolean("Error", false);
      if (!error) return mapObject.getMapObject("Data").getString("tokenid");
      throw RuntimeError.UnknownError("Authenticate BFSOne Api failed {0}", mapObject.getString("ErrorMessage"));
    } catch (Exception e) {
      throw RuntimeError.UnknownError("Authenticate BFSOne Api failed {0}", e.getMessage());
    }
  }

  /* ---------------------------API Internal Booking------------------------------ */
  public MapObject createIBooking(String token, MapObject bfsOneIBooking) {
    HttpHeaders headers = getHttpHeaders(token);
    String bookingModelJson = DataSerializer.JSON.toString(bfsOneIBooking);
    log.info("---------------- Create IBooking -----------------------");
    ResponseEntity<String> response = client.post("internalbooking/ibcreate", headers, bookingModelJson, String.class);
    try {
      MapObject mapObject = DataSerializer.JSON.fromString(response.getBody(), MapObject.class);
      Boolean error = mapObject.getBoolean("Error", false);
      if (!error) return mapObject.getMapObject("Data");
      String errorMgs = mapObject.getString("ErrorMessage");
      log.info("-----------Error Message From BFSOne Api:: {}\n\n", errorMgs);
      throw RuntimeError.UnknownError(errorMgs);
    } catch (Exception e) {
      log.error(e.getMessage());
      throw RuntimeError.UnknownError(e.getMessage());
    }
  }

  public MapObject loadIBooking(String token, String bookingCode) {
    final HttpHeaders headers = getHttpHeaders(token);
    MultiValueMap<String, String> queryParams = new LinkedMultiValueMap<>();
    queryParams.add("bkid", bookingCode);
    ResponseEntity<String> response = client.get("internalbooking/ibload", headers, queryParams, String.class);
    try {
      MapObject mapObject = DataSerializer.JSON.fromString(response.getBody(), MapObject.class);
      Boolean error = mapObject.getBoolean("Error", false);
      if (!error) return mapObject.getMapObject("Data");
      String errorMgs = mapObject.getString("ErrorMessage");
      log.info("-----------Error Message From BFSOne Api:: {}\n\n", errorMgs);
      throw RuntimeError.UnknownError(errorMgs);
    } catch (Exception e) {
      throw RuntimeError.UnknownError("Load BFSOne IBooking failed {0}", e.getMessage());
    }
  }

  public MapObject deleteInternalBooking(String token, String ibCode) {
    final HttpHeaders headers = getHttpHeaders(token);
    MapObject queryParam = new MapObject();
    queryParam.put("bkid", ibCode);
    String queryParamJson = DataSerializer.JSON.toString(queryParam);
    ResponseEntity<String> response = client.post("internalbooking/ibdelete", headers, queryParamJson, String.class);

    try {
      MapObject mapObject = DataSerializer.JSON.fromString(response.getBody(), MapObject.class);
      Boolean error = mapObject.getBoolean("Error", false);
      if (!error) return mapObject.getMapObject("Data");
      String errorMgs = mapObject.getString("ErrorMessage");
      log.info("-----------Error Message From BFSOne Api:: {}\n\n", errorMgs);
      throw RuntimeError.UnknownError(errorMgs);
    } catch (Exception e) {
      throw RuntimeError.UnknownError("Delete BFSOne IBooking failed {0}", e.getMessage());
    }
  }

  /* ---------------------------API BFSOne Partner------------------------------ */
  public MapObject loadPartnerTemp(String token, String partnerCode) {
    final HttpHeaders headers = getHttpHeaders(token);
    MultiValueMap<String, String> queryParams = new LinkedMultiValueMap<>();
    queryParams.add("pnid", partnerCode);
    ResponseEntity<String> response = client.get("partners/loadpartnertemplate", headers, queryParams, String.class);
    try {
      MapObject mapObject = DataSerializer.JSON.fromString(response.getBody(), MapObject.class);
      Boolean error = mapObject.getBoolean("Error", false);
      if (!error) return mapObject.getMapObject("Data");
      String errorMgs = mapObject.getString("ErrorMessage");
      log.info("-----------Error Message From BFSOne Api:: {}\n\n", errorMgs);
      throw RuntimeError.UnknownError(errorMgs);
    } catch (Exception e) {
      throw RuntimeError.UnknownError("Load BFSOne Partner failed {0}", e.getMessage());
    }
  }

  public MapObject createPartner(ClientContext clientCtx, CRMPartnerLogic partnerLogic, BFSOnePartnerGroup group, String token, MapObject customer) throws RuntimeError {
    log.info("\n-------------------------- Create Partner -----------------------");
    HttpHeaders headers = getHttpHeaders(token);
    DataSerializer.JSON.dump(customer);

    String customerAsJson = DataSerializer.JSON.toString(customer);
    String path;
    if (BFSOnePartnerGroup.CUSTOMERS.equals(group)) {
      path = "partners/cscreate";
    } else if (BFSOnePartnerGroup.AGENTS.equals(group)) {
      path = "partners/agcreate";
    } else if (BFSOnePartnerGroup.COLOADERS.equals(group)) {
      path = "partners/clcreate";
    } else {
      throw RuntimeError.IllegalArgument("Partner group is not valid");
    }
    ResponseEntity<String> response = client.post(path, headers, customerAsJson, String.class);
    MapObject mapObject = DataSerializer.JSON.fromString(response.getBody(), MapObject.class);
    Boolean error = mapObject.getBoolean("Error", false);
    if (!error) {
      DataSerializer.JSON.dump(mapObject);
      MapObject data = mapObject.getMapObject("Data");
      if (data == null) {
        log.error("-----------Error Message: Data is null in response from BFSOne API");
        throw RuntimeError.UnknownError("Data is null in response from BFSOne API");
      }
      String partnerCodeTemp = data.getString("PartnerCodeTemp");
      data.set("PartnerID", partnerCodeTemp);
      return data;
    }
    String errorMgs = mapObject.getString("ErrorMessage");

    String codeTemp = null;
    if (errorMgs != null && errorMgs.startsWith("This taxcode has existed on partner:")) {
      int colonIndex = errorMgs.indexOf(":");
      if (colonIndex != -1 && colonIndex + 1 < errorMgs.length()) {
        codeTemp = errorMgs.substring(colonIndex + 1).replaceAll("\\s+", "");
        MapObject partnerTemp = loadPartnerTemp(token, codeTemp);
        String partnerTempUserReq = partnerTemp.getString("RequestUser", "");
        String userReq = customer.getString("RequestUser", "");
        if (userReq.equalsIgnoreCase(partnerTempUserReq)) {
          log.info("-----------Partner Temp User Request matches: \n");
          DataSerializer.JSON.dump(partnerTemp);
          String message = String.format(
            "Request Partner: %s, Partner with code: %s %s",
            userReq, codeTemp, partnerTemp.getString("Taxcode", "")
          );
          partnerLogic.updateBFSOnePartner(clientCtx, customer, message);
          return partnerTemp;
        } else {
          String errorMessage = String.format(
            "Partner already exists in the system with salesman: %s (current: %s)",
            partnerTempUserReq, userReq
          );
          log.error(errorMessage);
          DataSerializer.JSON.dump(partnerTemp);
          throw RuntimeError.IllegalArgument(errorMessage);
        }
      }
    }

    log.error("-----------Partner Data-----------: \n");
    DataSerializer.JSON.dump(customer);
    String formatError = StringUtil.isNotEmpty(errorMgs) ? "-----------Error Message: " + errorMgs + " -------------" : "Error Message: Unknown error";
    log.error(formatError);
    throw RuntimeError.IllegalArgument(formatError);
  }

  public MapObject partnerApprove(BFSOnePartnerGroup group, String token, MapObject partner) throws RuntimeError {
    log.info("\n-------------------------- Partner Approve -----------------------");
    HttpHeaders headers = getHttpHeaders(token);
    DataSerializer.JSON.dump(partner);
    String partnerAsJson = DataSerializer.JSON.toString(partner);
    String path;
    if (BFSOnePartnerGroup.CUSTOMERS.equals(group)) {
      path = "partners/csapprove";
    } else if (BFSOnePartnerGroup.AGENTS.equals(group)) {
      path = "partners/agapprove";
    } else if (BFSOnePartnerGroup.COLOADERS.equals(group)) {
      path = "partners/clapprove";
    } else {
      throw RuntimeError.IllegalArgument("Partner group is not valid");
    }
    ResponseEntity<String> response = client.post(path, headers, partnerAsJson, String.class);
    MapObject mapObject = DataSerializer.JSON.fromString(response.getBody(), MapObject.class);
    Boolean error = mapObject.getBoolean("Error", false);
    if (!error) {
      DataSerializer.JSON.dump(mapObject);
      MapObject data = mapObject.getMapObject("Data");
      if (data == null) {
        log.error("-----------Error Message: Data is null in response from BFSOne API");
        throw RuntimeError.UnknownError("Data is null in response from BFSOne API");
      }
      String partnerCode = data.getString("PartnerCode");
      partner.set("PartnerID", partnerCode);
      return partner;
    }

    String errorMgs = mapObject.getString("ErrorMessage");
    log.error("-----------Partner Data-----------: \n");
    DataSerializer.JSON.dump(partner);
    String formatError = StringUtil.isNotEmpty(errorMgs) ? "-----------Error Message: " + errorMgs + " -------------" : "Error Message: Unknown error";
    log.error(formatError);
    throw RuntimeError.IllegalArgument(formatError);
  }

  /* ---------------------------API Customer Lead------------------------------ */
  public MapObject loadLead(String token, String leadCode) {
    final HttpHeaders headers = getHttpHeaders(token);
    MultiValueMap<String, String> queryParams = new LinkedMultiValueMap<>();
    queryParams.add("ContactID", leadCode);
    try {
      ResponseEntity<String> response = client.get("lead/leaload", headers, queryParams, String.class);
      MapObject mapObject = DataSerializer.JSON.fromString(response.getBody(), MapObject.class);
      Boolean error = mapObject.getBoolean("Error", false);
      if (!error) return mapObject.getMapObject("Data");
      String errorMgs = mapObject.getString("ErrorMessage");
      log.info("-----------Error Message From BFSOne Api:: {}\n\n", errorMgs);
      throw RuntimeError.IllegalArgument(errorMgs);
    } catch (Exception e) {
      throw RuntimeError.IllegalArgument("Load BFSOne Lead failed {0}", e.getMessage());
    }
  }

  public MapObject saveBFSOneCustomerLead(String token, MapObject customerLead) {
    HttpHeaders headers = getHttpHeaders(token);
    String leadModelJson = DataSerializer.JSON.toString(customerLead);

    String contactId = customerLead.getString("ContactID");
    String path = "lead/leaupdate";
    if (StringUtil.isEmpty(contactId)) path = "lead/leacreate";

    log.info("---------------- Create/Update BFSOne Lead -----------------------");
    ResponseEntity<String> response = client.post(path, headers, leadModelJson, String.class);
    try {
      MapObject mapObject = DataSerializer.JSON.fromString(response.getBody(), MapObject.class);
      Boolean error = mapObject.getBoolean("Error", false);
      if (!error) return mapObject.getMapObject("Data");
      String errorMgs = mapObject.getString("ErrorMessage");
      log.info("-----------Error Message From BFSOne Api:: {}\n\n", errorMgs);
      throw RuntimeError.UnknownError(errorMgs);
    } catch (Exception e) {
      log.error(e.getMessage());
      throw RuntimeError.UnknownError(e.getMessage());
    }
  }

  public MapObject deleteLead(String token, String leadCode) {
    HttpHeaders headers = getHttpHeaders(token);
    MapObject data = new MapObject();
    data.set("ContactID", leadCode);

    log.info("---------------- Delete BFSOne Lead {} -----------------------", leadCode);
    ResponseEntity<String> response = client.post("lead/leadelete", headers, DataSerializer.JSON.toString(data), String.class);
    try {
      MapObject mapObject = DataSerializer.JSON.fromString(response.getBody(), MapObject.class);
      Boolean error = mapObject.getBoolean("Error", false);
      if (!error) return mapObject.getMapObject("Data");
      String errorMgs = mapObject.getString("ErrorMessage");
      log.info("-----------Error Message From BFSOne Api:: {}\n\n", errorMgs);
      throw RuntimeError.UnknownError(errorMgs);
    } catch (Exception e) {
      log.error(e.getMessage());
      throw RuntimeError.UnknownError(e.getMessage());
    }
  }

  /* ---------------------------API Users Load------------------------------ */
  public List<MapObject> loadUsers(String token) {
    final HttpHeaders headers = getHttpHeaders(token);
    MultiValueMap<String, String> queryParams = new LinkedMultiValueMap<>();
    try {
      ResponseEntity<String> response = client.get("users/usload", headers, queryParams, String.class);
      MapObject mapObject = DataSerializer.JSON.fromString(response.getBody(), MapObject.class);
      Boolean error = mapObject.getBoolean("Error", false);
      if (!error) {
        List<?> rawList = (List<?>) mapObject.get("Data");
        return rawList.stream()
          .map(item -> new MapObject((Map<String, Object>) item))
          .toList();
      }
      String errorMgs = mapObject.getString("ErrorMessage");
      log.info("-----------Error Message From BFSOne Api:: {}\n\n", errorMgs);
      throw RuntimeError.IllegalArgument(errorMgs);
    } catch (Exception e) {
      throw RuntimeError.IllegalArgument("Load BFSOne Lead failed {0}", e.getMessage());
    }
  }

  public List<MapObject> loadUsers(ClientContext client) {
    String bfsoneEmployeeCode = "CT1613";
    String bfsoneUsername = "JESSE.VNHPH";
    String authenticate = authenticate(bfsoneEmployeeCode, bfsoneUsername);
    return loadUsers(authenticate);
  }

  /* ---------------------------API Source Load------------------------------ */
  
  public List<MapObject> loadSources(String token) {
    final HttpHeaders headers = getHttpHeaders(token);
    MultiValueMap<String, String> queryParams = new LinkedMultiValueMap<>();
    try {
      ResponseEntity<String> response = client.get("partners/loadsource", headers, queryParams, String.class);
      MapObject mapObject = DataSerializer.JSON.fromString(response.getBody(), MapObject.class);
      Boolean error = mapObject.getBoolean("Error", false);
      if (!error) {
        List<?> rawList = (List<?>) mapObject.get("Data");
        return rawList.stream()
          .map(item -> new MapObject((Map<String, Object>) item))
          .toList();
      }
      String errorMgs = mapObject.getString("ErrorMessage");
      log.info("-----------Error Message From BFSOne Api:: {}\n\n", errorMgs);
      throw RuntimeError.IllegalArgument(errorMgs);
    } catch (Exception e) {
      throw RuntimeError.IllegalArgument("Load BFSOne Sources failed {0}", e.getMessage());
    }
  }
  
  public List<MapObject> loadSources(ClientContext client) {
    String bfsoneEmployeeCode = "CT1613";
    String bfsoneUsername = "JESSE.VNHPH";
    String authenticate = authenticate(bfsoneEmployeeCode, bfsoneUsername);
    return loadSources(authenticate);
  }

}