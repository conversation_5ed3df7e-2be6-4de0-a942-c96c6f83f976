package cloud.datatp.fforwarder.core.bd;

import cloud.datatp.fforwarder.core.bd.entity.AgencyAgreementFollowUp;
import cloud.datatp.fforwarder.core.bd.entity.AnnualConference;
import cloud.datatp.fforwarder.core.bd.entity.NetworkMembershipFee;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;

public class MailMessageReminderTemplate {

  public static String buildAnnualConferenceReminderMgs(AnnualConference conference) {
    return String.format("""
      <div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9fafb;">
          <div style="background-color: #ffffff; padding: 30px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
              <h1 style="color: #1f2937; font-size: 24px; margin: 0 0 20px 0; text-align: center;">
                  📅 Reminder – Registration for %s Annual Conference
              </h1>
              
              <p style="color: #374151; font-size: 16px; margin: 0 0 20px 0;">
                  Dear Team,
              </p>
              
              <p style="color: #374151; font-size: 16px; margin: 0 0 20px 0; line-height: 1.6;">
                  This is a gentle reminder to register for the upcoming <strong>%s Annual Conference</strong>. 
                  Please find the event details below:
              </p>

              <div style="background-color: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #3b82f6;">
                  <h3 style="color: #1f2937; margin: 0 0 15px 0; font-size: 18px;">Conference Details</h3>
                  
                  <div style="margin-bottom: 12px;">
                      <strong style="color: #1f2937;">Event:</strong> 
                      <span style="color: #374151;">%s Annual Conference</span>
                  </div>
                  
                  <div style="margin-bottom: 12px;">
                      <strong style="color: #1f2937;">Network:</strong> 
                      <span style="color: #374151;">%s</span>
                  </div>
                  
                  <div style="margin-bottom: 12px;">
                      <strong style="color: #1f2937;">Start Date:</strong> 
                      <span style="color: #374151;">%s</span>
                  </div>
                  
                  <div style="margin-bottom: 12px;">
                      <strong style="color: #1f2937;">End Date:</strong> 
                      <span style="color: #374151;">%s</span>
                  </div>
                  
                  <div style="margin-bottom: 12px;">
                      <strong style="color: #1f2937;">Venue:</strong> 
                      <span style="color: #374151;">%s</span>
                  </div>
                  
                  <div style="margin-bottom: 12px;">
                      <strong style="color: #1f2937;">Delegate Fee:</strong> 
                      <span style="color: #374151;">%s %.2f</span>
                  </div>
                  
                  <div style="margin-bottom: 12px;">
                      <strong style="color: #1f2937;">Registration Period:</strong> 
                      <span style="color: #374151;">Until %s</span>
                  </div>
                  
                  %s
                  
                  %s
              </div>

              <div style="background-color: #fef3c7; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #f59e0b;">
                  <p style="color: #92400e; margin: 0; font-weight: 500;">
                      ⚠️ <strong>Important:</strong> Kindly complete your registration within the given period to secure your participation.
                  </p>
              </div>

              <div style="margin-top: 30px; text-align: center;">
                  <p style="color: #374151; margin: 0;">
                      Best regards,<br>
                      <strong>CRM Management Team</strong>
                  </p>
              </div>

              <div style="margin-top: 24px; padding-top: 16px; border-top: 1px solid #e5e7eb; text-align: center;">
                  <p style="color: #6b7280; font-size: 14px; margin: 0;">
                      This is an automated notification from the CRM Task Management System.
                  </p>
              </div>
          </div>
      </div>
      """,
      conference.getEvent() != null ? conference.getEvent() : "Conference",
      conference.getEvent() != null ? conference.getEvent() : "Conference",
      conference.getEvent() != null ? conference.getEvent() : "Conference",
      conference.getNetwork() != null ? conference.getNetwork() : "N/A",
      conference.getStartDate() != null ? DateUtil.asCompactDate(conference.getStartDate()) : "TBD",
      conference.getEndDate() != null ? DateUtil.asCompactDate(conference.getEndDate()) : "TBD",
      conference.getVenue() != null ? conference.getVenue() : "TBD",
      conference.getCurrency() != null ? conference.getCurrency() : "USD",
      conference.getDelegatesFee(),
      conference.getRegistrationStartDate() != null ? DateUtil.asCompactDate(conference.getRegistrationStartDate()) + " - " : "TBD - " +
      conference.getRegistrationEndDate() != null ? DateUtil.asCompactDate(conference.getRegistrationEndDate()) : "TBD",
      conference.getExpectedAttendance() != null ?
        String.format("""
          <div style="margin-bottom: 12px;">
              <strong style="color: #1f2937;">Expected Attendance:</strong> 
              <span style="color: #374151;">%d participants</span>
          </div>
          """, conference.getExpectedAttendance()) : "",
      StringUtil.isNotEmpty(conference.getNote()) ?
        String.format("""
          <div style="margin-bottom: 12px;">
              <strong style="color: #1f2937;">Note:</strong> 
              <span style="color: #374151;">%s</span>
          </div>
          """, conference.getNote()) : ""
    );
  }

  public static String buildNetworkMembershipFeeMailMsg(NetworkMembershipFee networkMembership) {
    return String.format("""
      <div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9fafb;">
          <div style="background-color: #ffffff; padding: 30px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
              <h1 style="color: #1f2937; font-size: 24px; margin: 0 0 20px 0; text-align: center;">
                  💰 Reminder – Annual Membership Fee Payment for %s
              </h1>
              
              <p style="color: #374151; font-size: 16px; margin: 0 0 20px 0;">
                  Dear %s,
              </p>
              
              <p style="color: #374151; font-size: 16px; margin: 0 0 20px 0; line-height: 1.6;">
                  This is a kind reminder regarding the annual membership fee for our network. 
                  Please find the details below:
              </p>

              <div style="background-color: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #3b82f6;">
                  <h3 style="color: #1f2937; margin: 0 0 15px 0; font-size: 18px;">Membership Fee Details</h3>
                  
                  <div style="margin-bottom: 12px;">
                      <strong style="color: #1f2937;">Network:</strong> 
                      <span style="color: #374151;">%s</span>
                  </div>
                  
                  <div style="margin-bottom: 12px;">
                      <strong style="color: #1f2937;">Representative Admin:</strong> 
                      <span style="color: #374151;">%s</span>
                  </div>
                  
                  <div style="margin-bottom: 12px;">
                      <strong style="color: #1f2937;">Email:</strong> 
                      <span style="color: #374151;">%s</span>
                  </div>
                  
                  <div style="margin-bottom: 12px;">
                      <strong style="color: #1f2937;">Membership Period:</strong> 
                      <span style="color: #374151;">%s - %s</span>
                  </div>
                  
                  <div style="margin-bottom: 12px;">
                      <strong style="color: #1f2937;">Expire Date:</strong> 
                      <span style="color: #dc2626; font-weight: 600;">%s</span>
                  </div>
                  
                  <div style="margin-bottom: 12px;">
                      <strong style="color: #1f2937;">Annual Membership Fee:</strong> 
                      <span style="color: #374151; font-weight: 600; font-size: 18px;">%s %.2f</span>
                  </div>
                  
                  %s
                  
                  %s
                  
                  %s
              </div>

              <div style="background-color: #fef3c7; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #f59e0b;">
                  <p style="color: #92400e; margin: 0; font-weight: 500;">
                      ⚠️ <strong>Important:</strong> Please ensure payment is completed before the expiry date to maintain your network membership.
                  </p>
              </div>

              <div style="margin-top: 30px; text-align: center;">
                  <p style="color: #374151; margin: 0;">
                      Best regards,<br>
                      <strong>CRM Management Team</strong>
                  </p>
              </div>

              <div style="margin-top: 24px; padding-top: 16px; border-top: 1px solid #e5e7eb; text-align: center;">
                  <p style="color: #6b7280; font-size: 14px; margin: 0;">
                      This is an automated notification from the CRM Task Management System.
                  </p>
              </div>
          </div>
      </div>
      """,
      networkMembership.getNetwork() != null ? networkMembership.getNetwork() : "Network",
      networkMembership.getRepresentativeAdminName() != null ? networkMembership.getRepresentativeAdminName() : "All",
      networkMembership.getNetwork() != null ? networkMembership.getNetwork() : "N/A",
      networkMembership.getRepresentativeAdminName() != null ? networkMembership.getRepresentativeAdminName() : "N/A",
      networkMembership.getRepresentativeAdminEmail() != null ? networkMembership.getRepresentativeAdminEmail() : "N/A",
      networkMembership.getStartDate() != null ? DateUtil.asCompactDate(networkMembership.getStartDate()) : "TBD",
      networkMembership.getExpireDate() != null ? DateUtil.asCompactDate(networkMembership.getExpireDate()): "TBD",
      networkMembership.getExpireDate() != null ? DateUtil.asCompactDate(networkMembership.getExpireDate()) : "TBD",
      networkMembership.getCurrency() != null ? networkMembership.getCurrency() : "USD",
      networkMembership.getAnnualMembershipFee(),
      networkMembership.getNumberOfMembers() != null ?
        String.format("""
          <div style="margin-bottom: 12px;">
              <strong style="color: #1f2937;">Number of Members:</strong> 
              <span style="color: #374151;">%d</span>
          </div>
          """, networkMembership.getNumberOfMembers()) : "",
      networkMembership.getWebsite() != null && !networkMembership.getWebsite().trim().isEmpty() ?
        String.format("""
          <div style="margin-bottom: 12px;">
              <strong style="color: #1f2937;">Website:</strong> 
              <span style="color: #374151;"><a href="%s" style="color: #3b82f6; text-decoration: none;">%s</a></span>
          </div>
          """, networkMembership.getWebsite(), networkMembership.getWebsite()) : "",
      networkMembership.isHasFinancialProtection() && networkMembership.getFinancialProtection() != null && !networkMembership.getFinancialProtection().trim().isEmpty() ?
        String.format("""
          <div style="margin-bottom: 12px;">
              <strong style="color: #1f2937;">Financial Protection:</strong> 
              <span style="color: #374151;">%s</span>
          </div>
          """, networkMembership.getFinancialProtection()) : ""
    );
  }

  public static String buildAgencyAgreementFollowUpMailMsg(AgencyAgreementFollowUp agencyAgreement) {
    return String.format("""
      <div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9fafb;">
          <div style="background-color: #ffffff; padding: 30px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
              <h1 style="color: #1f2937; font-size: 24px; margin: 0 0 20px 0; text-align: center;">
                  📋 Notification – New Agency Agreement Follow Up
              </h1>
              
              <p style="color: #374151; font-size: 16px; margin: 0 0 20px 0;">
                  Dear Team,
              </p>
              
              <p style="color: #374151; font-size: 16px; margin: 0 0 20px 0; line-height: 1.6;">
                  Please be reminded of the following Agency Agreement follow-up:
              </p>

              <div style="background-color: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #3b82f6;">
                  <h3 style="color: #1f2937; margin: 0 0 15px 0; font-size: 18px;">Agency Agreement Details</h3>
                  
                  <div style="margin-bottom: 12px;">
                      <strong style="color: #1f2937;">Agent Name:</strong> 
                      <span style="color: #374151;">%s</span>
                  </div>
                  
                  <div style="margin-bottom: 12px;">
                      <strong style="color: #1f2937;">Agent Code:</strong> 
                      <span style="color: #374151;">%s</span>
                  </div>
                  
                  <div style="margin-bottom: 12px;">
                      <strong style="color: #1f2937;">Country:</strong> 
                      <span style="color: #374151;">%s</span>
                  </div>
                  
                  <div style="margin-bottom: 12px;">
                      <strong style="color: #1f2937;">Date Created:</strong> 
                      <span style="color: #374151;">%s</span>
                  </div>
                  
                  <div style="margin-bottom: 12px;">
                      <strong style="color: #1f2937;">Signed Date:</strong> 
                      <span style="color: #374151;">%s</span>
                  </div>
                  
                  <div style="margin-bottom: 12px;">
                      <strong style="color: #1f2937;">Status:</strong> 
                      <span style="color: #374151; font-weight: 600;">%s</span>
                  </div>
                  
                  <div style="margin-bottom: 12px;">
                      <strong style="color: #1f2937;">Handled by:</strong> 
                      <span style="color: #374151;">%s</span>
                  </div>
                  
                  %s
                  
                  %s
                  
                  %s
              </div>

              <div style="background-color: #fef3c7; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #f59e0b;">
                  <p style="color: #92400e; margin: 0; font-weight: 500;">
                      ⚠️ <strong>Important:</strong> Kindly keep track and update the progress accordingly.
                  </p>
              </div>

              <div style="margin-top: 30px; text-align: center;">
                  <p style="color: #374151; margin: 0;">
                      Best regards,<br>
                      <strong>CRM Management Team</strong>
                  </p>
              </div>

              <div style="margin-top: 24px; padding-top: 16px; border-top: 1px solid #e5e7eb; text-align: center;">
                  <p style="color: #6b7280; font-size: 14px; margin: 0;">
                      This is an automated notification from the CRM Task Management System.
                  </p>
              </div>
          </div>
      </div>
      """,
      agencyAgreement.getAgentName() != null ? agencyAgreement.getAgentName() : "N/A",
      agencyAgreement.getAgentCode() != null ? agencyAgreement.getAgentCode() : "N/A",
      agencyAgreement.getCountryLabel() != null ? agencyAgreement.getCountryLabel() : "N/A",
      agencyAgreement.getDateCreated() != null ? DateUtil.asCompactDate(agencyAgreement.getDateCreated()) : "N/A",
      agencyAgreement.getSignedDate() != null ?DateUtil.asCompactDate(agencyAgreement.getSignedDate()) : "[Pending]",
      agencyAgreement.getStatus() != null ? agencyAgreement.getStatus().getLabel() : "N/A",
      agencyAgreement.getHandledByLabel() != null ? agencyAgreement.getHandledByLabel() : "N/A",
      agencyAgreement.getAddress() != null && !agencyAgreement.getAddress().trim().isEmpty() ?
        String.format("""
          <div style="margin-bottom: 12px;">
              <strong style="color: #1f2937;">Address:</strong> 
              <span style="color: #374151;">%s</span>
          </div>
          """, agencyAgreement.getAddress()) : "",
      agencyAgreement.getMemberOfNetwork() != null && !agencyAgreement.getMemberOfNetwork().trim().isEmpty() ?
        String.format("""
          <div style="margin-bottom: 12px;">
              <strong style="color: #1f2937;">Member of Network:</strong> 
              <span style="color: #374151;">%s</span>
          </div>
          """, agencyAgreement.getMemberOfNetwork()) : "",
      agencyAgreement.getNote() != null && !agencyAgreement.getNote().trim().isEmpty() ?
        String.format("""
          <div style="margin-bottom: 12px;">
              <strong style="color: #1f2937;">Note:</strong> 
              <span style="color: #374151;">%s</span>
          </div>
          """, agencyAgreement.getNote()) : ""
    );
  }

}