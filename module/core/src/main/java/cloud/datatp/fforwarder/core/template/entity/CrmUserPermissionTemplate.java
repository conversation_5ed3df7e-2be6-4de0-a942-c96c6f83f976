package cloud.datatp.fforwarder.core.template.entity;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.data.db.entity.PersistableEntity;

/**
 * <AUTHOR>
 * <PERSON>.<EMAIL>
 * @version 1.0
 * @since 2024
 */

@Entity
@Table(name = CrmUserPermissionTemplate.TABLE_NAME)
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class CrmUserPermissionTemplate extends PersistableEntity<Long> {

  final static public String TABLE_NAME = "lgc_forwarder_crm_user_permission_template";

  @Column(name = "account_id")
  private Long accountId;

  @Column(name = "bfsone_username")
  private String bfsoneUsername;

  @Column(name = "full_name")
  private String fullName;

  @Column(name = "target_company_code")
  private String targetCompanyCode;

  @Column(name = "company_id")
  private String companyId;

  // Agent Permissions
  @Enumerated(EnumType.STRING)
  @Column(name = "agent_view_scope")
  private PermissionScope agentViewScope = PermissionScope.SELF_ONLY;

  @Enumerated(EnumType.STRING)
  @Column(name = "agent_edit_scope")
  private PermissionScope agentEditScope = PermissionScope.NONE;

  @Enumerated(EnumType.STRING)
  @Column(name = "agent_approve_scope")
  private PermissionScope agentApproveScope = PermissionScope.NONE;

  // Coloader Permissions
  @Enumerated(EnumType.STRING)
  @Column(name = "coloader_view_scope")
  private PermissionScope coloaderViewScope = PermissionScope.SELF_ONLY;

  @Enumerated(EnumType.STRING)
  @Column(name = "coloader_edit_scope")
  private PermissionScope coloaderEditScope = PermissionScope.NONE;

  @Enumerated(EnumType.STRING)
  @Column(name = "coloader_approve_scope")
  private PermissionScope coloaderApproveScope = PermissionScope.NONE;

  // Customer Permissions
  @Enumerated(EnumType.STRING)
  @Column(name = "customer_view_scope")
  private PermissionScope customerViewScope = PermissionScope.SELF_ONLY;

  @Enumerated(EnumType.STRING)
  @Column(name = "customer_edit_scope")
  private PermissionScope customerEditScope = PermissionScope.NONE;

  @Enumerated(EnumType.STRING)
  @Column(name = "customer_approve_scope")
  private PermissionScope customerApproveScope = PermissionScope.NONE;

  // Customer Lead Permissions
  @Enumerated(EnumType.STRING)
  @Column(name = "customer_lead_view_scope")
  private PermissionScope customerLeadViewScope = PermissionScope.SELF_ONLY;

  @Enumerated(EnumType.STRING)
  @Column(name = "customer_lead_edit_scope")
  private PermissionScope customerLeadEditScope = PermissionScope.NONE;

  @Enumerated(EnumType.STRING)
  @Column(name = "customer_lead_approve_scope")
  private PermissionScope customerLeadApproveScope = PermissionScope.NONE;

  // Agent Potential Permissions
  @Enumerated(EnumType.STRING)
  @Column(name = "agent_potential_view_scope")
  private PermissionScope agentPotentialViewScope = PermissionScope.SELF_ONLY;

  @Enumerated(EnumType.STRING)
  @Column(name = "agent_potential_edit_scope")
  private PermissionScope agentPotentialEditScope = PermissionScope.NONE;

  @Enumerated(EnumType.STRING)
  @Column(name = "agent_potential_approve_scope")
  private PermissionScope agentPotentialApproveScope = PermissionScope.NONE;

  public CrmUserPermissionTemplate(CrmUserRole userRole) {
    this.accountId = userRole.getAccountId();
    this.targetCompanyCode = userRole.getCompanyBranchCode();
  }

  public void setResourcePermissions(ResourceType resourceType, PermissionScope viewScope, PermissionScope editScope,
                                     PermissionScope approveScope) {
    switch (resourceType) {
      case AGENT -> {
        this.agentViewScope = viewScope;
        this.agentEditScope = editScope;
        this.agentApproveScope = approveScope;
      }
      case COLOADER -> {
        this.coloaderViewScope = viewScope;
        this.coloaderEditScope = editScope;
        this.coloaderApproveScope = approveScope;
      }
      case CUSTOMER -> {
        this.customerViewScope = viewScope;
        this.customerEditScope = editScope;
        this.customerApproveScope = approveScope;
      }
      case CUSTOMER_LEAD -> {
        this.customerLeadViewScope = viewScope;
        this.customerLeadEditScope = editScope;
        this.customerLeadApproveScope = approveScope;
      }
      case AGENT_POTENTIAL -> {
        this.agentPotentialViewScope = viewScope;
        this.agentPotentialEditScope = editScope;
        this.agentPotentialApproveScope = approveScope;
      }
    }
  }

  // Convenient methods to set permission levels for specific resources
  public void initFullPermission(ResourceType resourceType) {
    setResourcePermissions(resourceType, PermissionScope.GROUP_ALL, PermissionScope.GROUP_ALL, PermissionScope.GROUP_ALL);
  }

  public void initCompanyPermission(ResourceType resourceType) {
    setResourcePermissions(resourceType, PermissionScope.COMPANY_ONLY, PermissionScope.COMPANY_ONLY, PermissionScope.COMPANY_ONLY);
  }

  public void initSelfOnlyPermission(ResourceType resourceType) {
    setResourcePermissions(resourceType, PermissionScope.SELF_ONLY, PermissionScope.SELF_ONLY, PermissionScope.NONE);
  }

  public void initReadOnlyPermission(ResourceType resourceType, PermissionScope viewScope) {
    setResourcePermissions(resourceType, viewScope, PermissionScope.NONE, PermissionScope.NONE);
  }

  public void initAllFullPermissions() {
    for (ResourceType resourceType : ResourceType.values()) {
      initFullPermission(resourceType);
    }
  }

  public void initAllCompanyPermissions() {
    for (ResourceType resourceType : ResourceType.values()) {
      initCompanyPermission(resourceType);
    }
  }

  public void initAllSelfOnlyPermissions() {
    for (ResourceType resourceType : ResourceType.values()) {
      initSelfOnlyPermission(resourceType);
    }
  }

  public void initFullPermission(ResourceType... resourceTypes) {
    for (ResourceType resourceType : resourceTypes) {
      initFullPermission(resourceType);
    }
  }

  public void initCompanyPermission(ResourceType... resourceTypes) {
    for (ResourceType resourceType : resourceTypes) {
      initCompanyPermission(resourceType);
    }
  }

  public void initSelfOnlyPermission(ResourceType... resourceTypes) {
    for (ResourceType resourceType : resourceTypes) {
      initSelfOnlyPermission(resourceType);
    }
  }

  // Helper methods to check permissions
  public boolean canView(ResourceType resourceType, PermissionScope requiredScope) {
    PermissionScope userScope = getViewScope(resourceType);
    return userScope.ordinal() >= requiredScope.ordinal();
  }

  public boolean canEdit(ResourceType resourceType, PermissionScope requiredScope) {
    PermissionScope userScope = getEditScope(resourceType);
    return userScope.ordinal() >= requiredScope.ordinal();
  }

  public boolean canApprove(ResourceType resourceType, PermissionScope requiredScope) {
    PermissionScope userScope = getApproveScope(resourceType);
    return userScope.ordinal() >= requiredScope.ordinal();
  }

  private PermissionScope getViewScope(ResourceType resourceType) {
    return switch (resourceType) {
      case AGENT -> agentViewScope;
      case COLOADER -> coloaderViewScope;
      case CUSTOMER -> customerViewScope;
      case CUSTOMER_LEAD -> customerLeadViewScope;
      case AGENT_POTENTIAL -> agentPotentialViewScope;
    };
  }

  private PermissionScope getEditScope(ResourceType resourceType) {
    return switch (resourceType) {
      case AGENT -> agentEditScope;
      case COLOADER -> coloaderEditScope;
      case CUSTOMER -> customerEditScope;
      case CUSTOMER_LEAD -> customerLeadEditScope;
      case AGENT_POTENTIAL -> agentPotentialEditScope;
    };
  }

  private PermissionScope getApproveScope(ResourceType resourceType) {
    return switch (resourceType) {
      case AGENT -> agentApproveScope;
      case COLOADER -> coloaderApproveScope;
      case CUSTOMER -> customerApproveScope;
      case CUSTOMER_LEAD -> customerLeadApproveScope;
      case AGENT_POTENTIAL -> agentPotentialApproveScope;
    };
  }

}