package cloud.datatp.fforwarder.core.integration;

import cloud.datatp.fforwarder.core.template.CRMUserRoleLogic;
import cloud.datatp.fforwarder.core.template.entity.CrmUserRole;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.msa.MSAClient;
import net.datatp.module.msa.MSAResponse;
import net.datatp.security.client.ClientContext;
import net.datatp.util.dataformat.DataSerializer;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Getter @Setter
@Component
public class MSAIntegrationService {

  private String msaApiUrl = "https://msa.beelogistics.cloud/api/secure/";
//  private String msaApiUrl = "http://localhost:8888/api/secure/";

  @Autowired
  private CRMUserRoleLogic crmUserRoleLogic;

  public void syncHouseBillWithSaleman(ClientContext clientCtx, MapObject params) {
    Long salemanAccountId = params.getLong("salemanAccountId", -1L);
    if(salemanAccountId != -1) {
      CrmUserRole saleman = crmUserRoleLogic.getByAccountId(clientCtx, salemanAccountId);
      Objects.assertNotNull(saleman, "Saleman is not found, username = " + clientCtx.getLoginId());
      params.put("contact_id", saleman.getBfsoneCode());
    }

    MSAClient client = new MSAClient(msaApiUrl);
    if(!params.containsKey("from_date")) {
      LocalDate firstDayOfMonth = LocalDate.now().withDayOfMonth(1);
      params.put("from_date", firstDayOfMonth.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
    }

    if(!params.containsKey("to_date")) {
      LocalDate lastDayOfMonth = LocalDate.now().withDayOfMonth(LocalDate.now().lengthOfMonth());
      params.put("to_date", lastDayOfMonth.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
    }
    MapObject userParams = new MapObject();
    userParams.set("params", params);
    MSAResponse resp  = client.call("HouseBillService", "sync_bfsone_house_bill", userParams);
    DataSerializer.JSON.dump(resp.getData());
  }

  public void syncIntegratedTransactions(ClientContext clientCtx, MapObject params) {
    Long salemanAccountId = params.getLong("salemanAccountId", -1L);
    if(salemanAccountId != -1) {
      CrmUserRole saleman = crmUserRoleLogic.getByAccountId(clientCtx, salemanAccountId);
      Objects.assertNotNull(saleman, "Saleman is not found, username = " + clientCtx.getLoginId());
      params.put("contact_id", saleman.getBfsoneCode());
    }

    MSAClient client = new MSAClient(msaApiUrl);
    if(!params.containsKey("from_date")) {
      LocalDate firstDayOfMonth = LocalDate.now().withDayOfMonth(1);
      params.put("from_date", firstDayOfMonth.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
    }

    if(!params.containsKey("to_date")) {
      LocalDate lastDayOfMonth = LocalDate.now().withDayOfMonth(LocalDate.now().lengthOfMonth());
      params.put("to_date", lastDayOfMonth.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
    }
    MapObject userParams = new MapObject();
    userParams.set("params", params);
    MSAResponse resp  = client.call("HouseBillService", "sync_bfsone_transaction", userParams);
    DataSerializer.JSON.dump(resp.getData());
  }

  public void enrichToBeelegacyPartners(ClientContext clientCtx, List<MapObject> partnersData) {
      /*
       partnersData = [{
        - partner_code
        - investment_origin
        - industry_code
        - industry_label
        - country_label
        - province_label
        - kcn_code
        - continent
        - last_update_by
       }]
     */
    for(MapObject partnerData : partnersData) {
      partnerData.add("last_update_by", clientCtx.getLoginId());
    }
    MSAClient client = new MSAClient(msaApiUrl);
    MapObject userParams = new MapObject();
    userParams.set("partners_data", partnersData);
    MSAResponse resp  = client.call("IntegratedPartnerService", "update_integrate_partners", userParams);
    DataSerializer.JSON.dump(resp.getData());
  }

}