package cloud.datatp.fforwarder.core.partner.plugin;

import cloud.datatp.fforwarder.core.partner.entity.CRMPartner;
import cloud.datatp.fforwarder.core.template.CRMUserRoleLogic;
import cloud.datatp.fforwarder.core.template.entity.CrmUserRole;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.communication.CommunicationMessageService;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class DomesticsPartnerServicePlugin extends PartnerServicePlugin {

  @Autowired
  CommunicationMessageService service;

  public DomesticsPartnerServicePlugin() {
    super("domestics-partner");
  }

  @Autowired
  private CRMUserRoleLogic crmUserRoleLogic;

  public void onPreSave(ClientContext client, CRMPartner partner, boolean isNew) {
    CrmUserRole saleman = crmUserRoleLogic.getByAccountId(client, client.getAccountId());
    Objects.assertNotNull(saleman, "Creator Employee {} is not found", client.getRemoteUser());

    if(!saleman.isOverseas()) {
      log.info("Domestics-partner: onPreSave called for partner: {}, isNew: {}", partner.getId(), isNew);

    }

  }

  public void onPostSave(ClientContext client, CRMPartner partner, boolean isNew) {
    CrmUserRole saleman = crmUserRoleLogic.getByAccountId(client, client.getAccountId());
    Objects.assertNotNull(saleman, "Creator Employee {} is not found", client.getRemoteUser());

    if(!saleman.isOverseas()) {
      log.info("Domestics-partner: onPostSave called for partner: {}, isNew: {}", partner.getId(), isNew);

    }

  }

}