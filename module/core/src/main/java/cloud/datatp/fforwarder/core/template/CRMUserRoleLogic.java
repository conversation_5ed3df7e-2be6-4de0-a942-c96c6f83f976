package cloud.datatp.fforwarder.core.template;

import cloud.datatp.fforwarder.core.db.CRMDaoService;
import cloud.datatp.fforwarder.core.integration.BFSOneApi;
import cloud.datatp.fforwarder.core.message.CRMMessageService;
import cloud.datatp.fforwarder.core.message.entity.CRMMessageSystem;
import cloud.datatp.fforwarder.core.template.entity.CrmUserRole;
import cloud.datatp.fforwarder.core.template.repository.CrmUserRoleRepository;

import java.util.*;

import lombok.extern.slf4j.Slf4j;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.entity.Account;
import net.datatp.module.company.CompanyLogic;
import net.datatp.module.company.entity.Company;
import net.datatp.module.core.security.SecurityLogic;
import net.datatp.module.core.security.entity.App;
import net.datatp.module.core.security.entity.AppPermission;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.hr.DepartmentLogic;
import net.datatp.module.hr.EmployeeCreationModel;
import net.datatp.module.hr.EmployeeLogic;
import net.datatp.module.hr.entity.Employee;
import net.datatp.module.hr.entity.HRDepartment;
import net.datatp.security.client.Capability;
import net.datatp.security.client.ClientContext;
import net.datatp.security.client.DataScope;
import net.datatp.util.dataformat.DataSerializer;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class CRMUserRoleLogic extends CRMDaoService {

  @Autowired
  private CrmUserRoleRepository repo;

  @Autowired
  private EmployeeLogic employeeLogic;

  @Autowired
  private AccountLogic accountLogic;

  @Autowired
  private DepartmentLogic departmentLogic;

  @Autowired
  private CompanyLogic companyLogic;

  @Autowired
  private SecurityLogic securityLogic;

  @Autowired
  private CRMMessageService crmMessageService;

  @Autowired
  private BFSOneApi bfsOneApi;

  public CrmUserRole getById(ClientContext client, Long id) {
    return repo.findById(id).get();
  }

  public CrmUserRole getByAccountId(ClientContext client, Long accountId) {
    return repo.getByAccountId(accountId);
  }

  public CrmUserRole getByBfsoneCode(ClientContext client, String bfsoneCode) {
    return repo.getByBfsoneCode(bfsoneCode);
  }

  public CrmUserRole getByBfsoneUsername(ClientContext client, String bfsoneUsername) {
    return repo.getByBFSOneUsername(bfsoneUsername);
  }

  public List<CrmUserRole> findByUserType(ClientContext client, CrmUserRole.UserType userType) {
    return repo.findByUserType(userType);
  }

  public List<CrmUserRole> findAllUserRoles(ClientContext client) {
    return repo.findAll();
  }

  public List<SqlMapRecord> searchCrmUserRoles(ClientContext client, SqlQueryParams sqlParams) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/core/groovy/CrmUserRoleSql.groovy";
    return searchDbRecords(client, scriptDir, scriptFile, "SearchCrmUserRole", sqlParams);
  }

  public List<CrmUserRole> addCrmUserRoles(ClientContext client, List<Long> ids, HRDepartment department, CrmUserRole.UserType type) {
    List<CrmUserRole> roles = new ArrayList<>();
    for (Long id : ids) {
      Employee employee = employeeLogic.getEmployee(client, client.getCompany(), id);

      if (StringUtil.isEmpty(employee.getBfsoneCode())) continue;
      CrmUserRole role = getByBfsoneCode(client, employee.getBfsoneCode());
      if (Objects.nonNull(role)) continue;
      Account account = accountLogic.getAccountById(client, employee.getAccountId());
      Objects.assertNotNull(account, "Account not found!!!, account id: " + employee.getAccountId());
      role = new CrmUserRole(employee);
      role.withAccount(account);
      role.withCompany(client.getCompany());
      role.withDepartment(department);
      role.withType(type);
      role = saveCrmUserRole(client, role);
      roles.add(role);
    }
    return roles;
  }

  public CrmUserRole saveCrmUserRole(ClientContext client, CrmUserRole customList) {
    customList.set(client);
    return repo.save(customList);
  }

  public boolean deleteByIds(ClientContext client, List<Long> ids) {
    repo.deleteAllById(ids);
    return true;
  }

  public int syncCrmUserRoles(ClientContext client) {
    List<MapObject> all = bfsOneApi.loadUsers(client).stream()
      .map(MapObject::new)
      .toList();

    Map<String, Company> companyMap = new HashMap<>();

    Company beehcm = companyLogic.getCompany(client, "beehcm");
    Company beehph = companyLogic.getCompany(client, "beehph");
    Company beehan = companyLogic.getCompany(client, "beehan");
    Company beedad = companyLogic.getCompany(client, "beedad");
    App spaceApp = securityLogic.getApp(client, "user", "my-employee-space");
    App userAsset = securityLogic.getApp(client, "company", "user-asset");
    App userSaleApp = securityLogic.getApp(client, "logistics", "user-logistics-sales");
    List<Long> appIds = Arrays.asList(spaceApp.getId(), userAsset.getId(), userSaleApp.getId());

    companyMap.put("BEEHCM", beehcm);
    companyMap.put("BEEHN", beehan);
    companyMap.put("BEEHP", beehph);
    companyMap.put("BEEDN", beedad);
    int count = 0;

    for (MapObject user : all) {
      String username = user.getString("Username", "").toUpperCase();
      String departmentLabel = user.getString("DepartmentName", "");
      String departmentName = user.getString("DeptID", "");
      String fullName = user.getString("FullName", "");
      String userId = user.getString("UserID", "");
      String cmpId = user.getString("CmpID", "");
      String position = user.getString("Position", "");
      String phone = user.getString("Phone", "");
      String email = user.getString("Email", "");
      Company company = companyMap.get(cmpId);

      if (Objects.isNull(company)) continue;
      CrmUserRole crmUserRole = getByBfsoneUsername(client, username);

      if (Objects.isNull(crmUserRole)) {
        Employee employee = employeeLogic.getEmployeeByBFSOneCode(client, company, userId);
        if (Objects.nonNull(employee)) {
          employee.setBfsoneUsername(username);
          employeeLogic.saveEmployee(client, company, employee);
        }
        List<Employee> employees = employeeLogic.findEmployeeByBFSOneUsername(client, username);
        if (employees.isEmpty()) {
          Account account = accountLogic.getAccountByLoginId(client, username);

          if (Objects.isNull(account) && !phone.isEmpty()) {
            account = accountLogic.getAccountByMobile(client, phone);
          }

          EmployeeCreationModel employeeCreationModel = new EmployeeCreationModel();
          if (Objects.nonNull(account)) employeeCreationModel.setAccountId(account.getId());
          employeeCreationModel.setLoginId(username);
          employeeCreationModel.setName(fullName);
          employeeCreationModel.setEmail(email);
          employeeCreationModel.setMobile(phone);
          employeeCreationModel.setBfsoneCode(userId);
          employeeCreationModel.setBfsoneUsername(username);
          employeeCreationModel.setPriority(5);
          HRDepartment department = departmentLogic.getHRDepartment(client, company, departmentName);
          if (Objects.isNull(department)) {
            department = new HRDepartment(departmentName, departmentLabel, departmentLabel);
            departmentLogic.saveHRDepartment(client, company, department);
          }
          employeeCreationModel.setDepartmentId(department.getId());
          Employee newEmployee = employeeLogic.newEmployee(client, company, employeeCreationModel);

          for (Long appId : appIds) {
            AppPermission existingPermission = securityLogic.getAppPermission(client, appId, company.getId(), newEmployee.getAccountId());
            if (Objects.nonNull(existingPermission)) continue;
            AppPermission permission = new AppPermission();
            permission.setCompanyId(company.getId());
            permission.setAccountId(newEmployee.getAccountId());
            permission.setAppId(appId);
            permission.setCapability(Capability.Write);
            permission.setDataScope(DataScope.Owner);
            securityLogic.saveAppPermission(client, permission);
          }
          employees.add(newEmployee);
        }

        try {
          crmUserRole = new CrmUserRole();
          crmUserRole.setFullName(fullName);
          crmUserRole.setAccountId(employees.get(0).getAccountId());
          crmUserRole.setBFSOneUsername(username);
          crmUserRole.setBFSOneCode(userId);
          crmUserRole.setCompanyBranchCode(company.getCode());
          crmUserRole.setCompanyBranchName(company.getLabel());
          crmUserRole.setCompanyBranchId(company.getId());
          crmUserRole.setDepartmentLabel(departmentLabel);
          crmUserRole.setDepartmentName(departmentName);
          crmUserRole.setPosition(position);
          crmUserRole.setPhone(phone);
          crmUserRole.setEmail(email);
          crmUserRole.setType(CrmUserRole.UserType.OTHER);
          crmUserRole.setActive(true);
          saveCrmUserRole(client, crmUserRole);

          String zaloMessage = CrmUserRoleNotificationTemplate.buildCrmUserRoleSyncZaloMessage(crmUserRole);
          CRMMessageSystem message = CrmUserRole.toUserRoleZaloMessage(zaloMessage);
          message.setReferenceId(crmUserRole.getId());
          crmMessageService.scheduleMessage(client, message);
          count++;
        } catch (Exception e) {
          DataSerializer.JSON.dump(user);
          log.error("Error processing user: {}", username, e);
        }
      }
    }
    return count;
  }

}