package cloud.datatp.fforwarder.core.partner;

import cloud.datatp.fforwarder.core.db.CRMDaoService;
import cloud.datatp.fforwarder.core.integration.BFSOneApi;
import cloud.datatp.fforwarder.core.message.CRMMessageLogic;
import cloud.datatp.fforwarder.core.message.entity.CRMMessageSystem;
import cloud.datatp.fforwarder.core.partner.entity.BFSOnePartnerGroup;
import cloud.datatp.fforwarder.core.partner.entity.CRMPartner;
import cloud.datatp.fforwarder.core.partner.entity.PartnerRequest;
import cloud.datatp.fforwarder.core.partner.entity.PartnerRequest.PartnerRequestStatus;
import cloud.datatp.fforwarder.core.partner.repository.PartnerRequestRepository;
import cloud.datatp.fforwarder.core.template.CRMUserRoleLogic;
import cloud.datatp.fforwarder.core.template.entity.CrmUserPermissionTemplate;
import cloud.datatp.fforwarder.core.template.entity.CrmUserRole;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.entity.Account;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.security.client.ClientContext;
import net.datatp.util.dataformat.DataSerializer;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class PartnerRequestLogic extends CRMDaoService {

  @Autowired
  private PartnerRequestRepository requestRepo;

  @Autowired
  private BFSOneApi bfsOneApi;

  @Autowired
  private CRMUserRoleLogic crmUserRoleLogic;

  @Autowired
  private CRMPartnerLogic partnerLogic;

  @Autowired
  private AccountLogic accountLogic;

  @Autowired
  private CRMMessageLogic crmMessageLogic;

  public PartnerRequest getById(ClientContext client, Long id) {
    return requestRepo.findById(id).get();
  }

  public List<PartnerRequest> findByPartnerId(ClientContext client, Long partnerId) {
    return requestRepo.findByPartnerId(partnerId);
  }

  public PartnerRequest savePartnerRequest(ClientContext client, PartnerRequest request) {
    request.set(client);
    return requestRepo.save(request);
  }

  public PartnerRequest updatePartnerRequestStatus(ClientContext client, PartnerRequest request) {
    PartnerRequest requestInDb = getById(client, request.getId());
    Objects.assertNotNull(requestInDb, "PartnerRequest is not found by id = {}", request.getId());

    Long salemanAccountId = requestInDb.getRequestByAccountId();
    Objects.assertNotNull(salemanAccountId, "RequestedByAccountId must not be null!");
    CrmUserRole saleman = crmUserRoleLogic.getByAccountId(client, salemanAccountId);
    Objects.assertNotNull(saleman, "Saleman is not found, account id = " + salemanAccountId);
    
    requestInDb.setStatus(request.getStatus());
    requestInDb.setApprovedByAccountId(request.getApprovedByAccountId());
    requestInDb.setApprovedByLabel(request.getApprovedByLabel());
    requestInDb.setApprovedDate(request.getApprovedDate());
    requestInDb.setApprovedNote(request.getApprovedNote());

    CRMPartner partner = partnerLogic.getById(client, request.getPartnerId());
    Objects.assertNotNull(partner, "CRMPartner is not found by id = {}", request.getPartnerId());
    BFSOnePartnerGroup group = partner.getPartnerGroup();
    PartnerRequestStatus newStatus = request.getStatus();

    if (newStatus == PartnerRequestStatus.APPROVED) {
      Long approvedByAccountId = request.getApprovedByAccountId();
      if (approvedByAccountId == null) approvedByAccountId = client.getAccountId();
      CrmUserRole approvedBy = crmUserRoleLogic.getByAccountId(client, approvedByAccountId);
      Objects.assertNotNull(approvedBy, "CrmUserRole is not found by accountId = {}", approvedByAccountId);
      String approvedByBfsoneEmployeeCode = approvedBy.getBfsoneCode();
      String approvedByBfsoneUsername = approvedBy.getBfsoneUsername();
      String authenticate = bfsOneApi.authenticate(approvedByBfsoneEmployeeCode, approvedByBfsoneUsername);

      String salemanBfsoneEmployeeCode = saleman.getBfsoneCode();
      MapObject bfsOnePartner = partner.isAgent() ? partner.toBFSOneAgent() : partner.toBFSOnePartner();

      //bfsOnePartner.put("Email_Request", saleman.getEmail());
      bfsOnePartner.put("ContactID", salemanBfsoneEmployeeCode);
      bfsOnePartner.put("InputPeople", approvedByBfsoneUsername);
      log.info("------------------------Partner---------------------------\n");
      DataSerializer.JSON.dump(bfsOnePartner);
      log.info("--------------------------------------------------------\n");

      MapObject bfsOnePartnerApproved = bfsOneApi.partnerApprove(group, authenticate, bfsOnePartner);
      String partnerCode = bfsOnePartnerApproved.getString("PartnerID", "");
      partner.setPartnerCode(partnerCode);
      partner.setPartnerCodeTemp(null);
      partner.setDateCreated(new Date());
      partner.setDateModified(new Date());
      partner.setStatus(requestInDb.getStatus());
      partner = partnerLogic.save(client, partner);

      requestInDb.withCRMPartner(partner);
      requestInDb = savePartnerRequest(client, requestInDb);

      CRMMessageSystem crmMessageSystem = requestInDb.toApprovalMailMessage(client, partner);
      crmMessageLogic.scheduleMessage(client, crmMessageSystem);
    } else {
      partner.setStatus(requestInDb.getStatus());
      partner = partnerLogic.save(client, partner);

      requestInDb.withCRMPartner(partner);

      if (newStatus == PartnerRequestStatus.REJECTED) {
        CRMMessageSystem crmMessageSystem = requestInDb.toApprovalMailMessage(client, partner);
        crmMessageLogic.scheduleMessage(client, crmMessageSystem);
      } else if (newStatus == PartnerRequestStatus.NEW) {
        //re-send request
        CrmUserPermissionTemplate approver = partnerLogic.getPartnerRequestApprover(client, group, saleman.getCompanyBranchCode());

        if (approver != null) {
          Account approverAccount = accountLogic.getAccountById(client, approver.getAccountId());
          Objects.assertNotNull(approverAccount, "Account is not found by id = {}", approver.getAccountId());
          requestInDb.withApprovedBy(approverAccount.getId(), approverAccount.getFullName());
          requestInDb.setTo(approverAccount.getEmail());
          CRMMessageSystem crmMessageSystem = requestInDb.toRequestMailMessage(client, group);
          crmMessageLogic.scheduleMessage(client, crmMessageSystem);
        }
      }

      requestInDb = savePartnerRequest(client, requestInDb);
    }

    return requestInDb;
  }

  public List<SqlMapRecord> searchPartnerRequests(ClientContext client, SqlQueryParams sqlParams) {
    sqlParams.addParam("accessAccountId", client.getAccountId());
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/core/groovy/PartnerRequestSql.groovy";
    return searchDbRecords(client, scriptDir, scriptFile, "SearchPartnerRequest", sqlParams);
  }

}