package cloud.datatp.fforwarder.core.template.repository;

import cloud.datatp.fforwarder.core.template.entity.CrmUserPermissionTemplate;
import cloud.datatp.fforwarder.core.template.entity.PermissionScope;
import java.io.Serializable;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface CrmUserPermissionTemplateRepository extends JpaRepository<CrmUserPermissionTemplate, Serializable> {

  @Query("SELECT p FROM CrmUserPermissionTemplate p WHERE p.accountId = :accountId")
  CrmUserPermissionTemplate getByAccountId(@Param("accountId") Long accountId);

  @Query("SELECT p FROM CrmUserPermissionTemplate p WHERE p.companyId = :companyId")
  List<CrmUserPermissionTemplate> findByCompanyId(@Param("companyId") Long companyId);

  // Check specific resource permissions
  @Query("SELECT CASE WHEN COUNT(p) > 0 THEN true ELSE false END " +
    "FROM CrmUserPermissionTemplate p " +
    "WHERE p.accountId = :accountId AND p.agentViewScope = :scope")
  boolean hasAgentViewPermission(@Param("accountId") Long accountId, @Param("scope") PermissionScope scope);

  @Query("SELECT CASE WHEN COUNT(p) > 0 THEN true ELSE false END " +
    "FROM CrmUserPermissionTemplate p " +
    "WHERE p.accountId = :accountId AND p.customerViewScope = :scope")
  boolean hasCustomerViewPermission(@Param("accountId") Long accountId, @Param("scope") PermissionScope scope);

  // Find users with specific permission levels
  @Query("SELECT p FROM CrmUserPermissionTemplate p WHERE p.agentViewScope = :scope")
  List<CrmUserPermissionTemplate> findUsersWithAgentViewScope(@Param("scope") PermissionScope scope);

  @Query("SELECT p FROM CrmUserPermissionTemplate p WHERE p.customerViewScope = :scope")
  List<CrmUserPermissionTemplate> findUsersWithCustomerViewScope(@Param("scope") PermissionScope scope);

  @Query("SELECT p FROM CrmUserPermissionTemplate p WHERE p.coloaderViewScope = :scope")
  List<CrmUserPermissionTemplate> findUsersWithColoaderViewScope(@Param("scope") PermissionScope scope);
  
  @Query("SELECT p FROM CrmUserPermissionTemplate p WHERE p.agentApproveScope = :scope")
  List<CrmUserPermissionTemplate> findUsersWithAgentApproveScope(@Param("scope") PermissionScope scope);
  
  @Query("SELECT p FROM CrmUserPermissionTemplate p WHERE p.customerApproveScope = :scope")
  List<CrmUserPermissionTemplate> findUsersWithCustomerApproveScope(@Param("scope") PermissionScope scope);
  
  @Query("SELECT p FROM CrmUserPermissionTemplate p WHERE p.coloaderApproveScope = :scope")
  List<CrmUserPermissionTemplate> findUsersWithColoaderApproveScope(@Param("scope") PermissionScope scope);

  // Find users with GROUP_ALL permissions for any resource
  @Query("SELECT p FROM CrmUserPermissionTemplate p WHERE " +
    "p.agentViewScope = 'GROUP_ALL' OR " +
    "p.coloaderViewScope = 'GROUP_ALL' OR " +
    "p.customerViewScope = 'GROUP_ALL' OR " +
    "p.customerLeadViewScope = 'GROUP_ALL' OR " +
    "p.agentPotentialViewScope = 'GROUP_ALL'")
  List<CrmUserPermissionTemplate> findUsersWithGroupAllViewPermissions();

  // Find users with COMPANY_ONLY permissions for specific resource
  @Query("SELECT p FROM CrmUserPermissionTemplate p WHERE " +
    "p.companyId = :companyId AND (" +
    "p.agentViewScope = 'COMPANY_ONLY' OR " +
    "p.customerViewScope = 'COMPANY_ONLY' OR " +
    "p.coloaderViewScope = 'COMPANY_ONLY')")
  List<CrmUserPermissionTemplate> findUsersWithCompanyViewPermissions(@Param("companyId") Long companyId);

  // Check if user has edit permission for specific resource
  @Query("SELECT CASE WHEN COUNT(p) > 0 THEN true ELSE false END " +
    "FROM CrmUserPermissionTemplate p " +
    "WHERE p.accountId = :accountId AND " +
    "CASE WHEN :resourceType = 'AGENT' THEN p.agentEditScope " +
    "     WHEN :resourceType = 'COLOADER' THEN p.coloaderEditScope " +
    "     WHEN :resourceType = 'CUSTOMER' THEN p.customerEditScope " +
    "     WHEN :resourceType = 'CUSTOMER_LEAD' THEN p.customerLeadEditScope " +
    "     WHEN :resourceType = 'AGENT_POTENTIAL' THEN p.agentPotentialEditScope " +
    "     ELSE 'NONE' END != 'NONE'")
  boolean hasEditPermission(@Param("accountId") Long accountId, @Param("resourceType") String resourceType);

  // Check if user has approve permission for specific resource
  @Query("SELECT CASE WHEN COUNT(p) > 0 THEN true ELSE false END " +
    "FROM CrmUserPermissionTemplate p " +
    "WHERE p.accountId = :accountId AND " +
    "CASE WHEN :resourceType = 'AGENT' THEN p.agentApproveScope " +
    "     WHEN :resourceType = 'COLOADER' THEN p.coloaderApproveScope " +
    "     WHEN :resourceType = 'CUSTOMER' THEN p.customerApproveScope " +
    "     WHEN :resourceType = 'CUSTOMER_LEAD' THEN p.customerLeadApproveScope " +
    "     WHEN :resourceType = 'AGENT_POTENTIAL' THEN p.agentPotentialApproveScope " +
    "     ELSE 'NONE' END != 'NONE'")
  boolean hasApprovePermission(@Param("accountId") Long accountId, @Param("resourceType") String resourceType);

  // Find users who can view specific company data
  @Query("SELECT DISTINCT p FROM CrmUserPermissionTemplate p WHERE " +
    "p.companyId = :companyId OR " +
    "(p.agentViewScope = 'GROUP_ALL' OR " +
    "p.customerViewScope = 'GROUP_ALL' OR " +
    "p.coloaderViewScope = 'GROUP_ALL' OR " +
    "p.customerLeadViewScope = 'GROUP_ALL' OR " +
    "p.agentPotentialViewScope = 'GROUP_ALL')")
  List<CrmUserPermissionTemplate> findUsersWhoCanViewCompanyData(@Param("companyId") Long companyId);

  // Bulk operations
  @Query("SELECT p FROM CrmUserPermissionTemplate p WHERE p.accountId IN :accountIds")
  List<CrmUserPermissionTemplate> findByAccountIds(@Param("accountIds") List<Long> accountIds);

}