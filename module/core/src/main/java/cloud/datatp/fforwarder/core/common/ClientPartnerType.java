package cloud.datatp.fforwarder.core.common;

public enum ClientPartnerType {
  CUSTOMER_LEAD, CUSTOMERS, AGENTS, AGENTS_APPROACHED;

  public static ClientPartnerType parse(String token) {
    if (token == null || token.trim().isEmpty()) return CUSTOMERS;
    for (ClientPartnerType status : values()) {
      if (status.name().equalsIgnoreCase(token.trim())) return status;
    }
    return CUSTOMERS;
  }

  public static boolean isAgent(ClientPartnerType type) {
    return type == AGENTS || type == AGENTS_APPROACHED;
  }

  public static boolean isCustomer(ClientPartnerType type) {
    return type == CUSTOMER_LEAD || type == CUSTOMERS;
  }

}