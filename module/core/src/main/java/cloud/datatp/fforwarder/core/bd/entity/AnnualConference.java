package cloud.datatp.fforwarder.core.bd.entity;

import java.io.Serial;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;

import cloud.datatp.fforwarder.core.bd.AnnualConferenceMessagePlugin;
import cloud.datatp.fforwarder.core.bd.MailMessageReminderTemplate;
import cloud.datatp.fforwarder.core.message.entity.CRMMessageSystem;
import cloud.datatp.fforwarder.core.message.entity.MessageType;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.data.db.entity.PersistableEntity;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.Arrays;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.DateUtil;

/**
 * <AUTHOR>
 */
@Entity
@Table(
  name = AnnualConference.TABLE_NAME,
  indexes = {
    @Index(
      name = AnnualConference.TABLE_NAME + "_network_idx",
      columnList = "network"
    ),
  }
)
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
@Getter @Setter
public class AnnualConference extends PersistableEntity<Long> {

  @Serial
  private static final long serialVersionUID = 1L;

  final static public String TABLE_NAME = "lgc_forwarder_crm_annual_conference";

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "date_created")
  private Date dateCreated = new Date();

  @Column(nullable = false)
  private String network;

  @Column(nullable = false, length = 1024)
  private String event;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "start_date")
  private Date startDate;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "end_date")
  private Date endDate;

  @Column(length = 1024)
  private String venue;

  // Phí tham dự
  @Column(name = "delegates_fee", length = 2 * 1024)
  private Double delegatesFee;
  
  private String currency;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "registration_start_date")
  private Date registrationStartDate;
  
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "registration_end_date")
  private Date registrationEndDate;

  @Column(name = "expected_attendance", length = 50)
  private Integer expectedAttendance;

  // Đại biểu được phân công (assigned delegates)
  @Column(name = "assigned_delegates")
  private String assignedDelegates;
  
  // Ghi chú
  @Column(name = "note", length = 4 * 1024)
  private String note;

  @Column(name = "auto_reminder")
  private Boolean autoReminder = true;

  @Column(name = "notification_time")
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  private Date notificationTime;
  
  @Column(name = "send_to_emails", length = 1024 * 2)
  @Convert(converter = StringSetConverter.class)
  private Set<String> sendToEmails = new HashSet<>();

  @Column(name = "input_by_account_id")
  private Long inputByAccountId;

  @Column(name = "input_by_account_label")
  private String inputByAccountLabel;

  public CRMMessageSystem toReminderMessage(ClientContext client) {
    Objects.assertNotNull(!isNew(), "New Annual Conference cannot be send message to handler");
    CRMMessageSystem msg = new CRMMessageSystem();
    msg.setContent(MailMessageReminderTemplate.buildAnnualConferenceReminderMgs(this));
    msg.setScheduledAt(notificationTime != null ? notificationTime : new Date());
    msg.setMessageType(MessageType.MAIL);
    msg.setReferenceId(id);
    msg.setReferenceType(TABLE_NAME);
    msg.setPluginName(AnnualConferenceMessagePlugin.PLUGIN_TYPE);
    msg.setRecipients(sendToEmails);

    MapObject metadata = new MapObject();
    metadata.put("fromEmail", "<EMAIL>");
    metadata.put("subject", String.format("CRM - Notification Message"));
    metadata.put("to", Arrays.asList(sendToEmails));
    msg.setMetadata(metadata);

    return msg;
  }
  
  public boolean isAutoReminder() {
    return autoReminder != null ? autoReminder : false;
  }

}