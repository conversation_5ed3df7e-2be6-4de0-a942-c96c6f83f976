package cloud.datatp.fforwarder.core.partner.entity;

import java.io.Serial;
import java.util.Date;
import java.util.HashSet;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import cloud.datatp.fforwarder.core.bd.AnnualConferenceMessagePlugin;
import cloud.datatp.fforwarder.core.message.entity.CRMMessageSystem;
import cloud.datatp.fforwarder.core.message.entity.MessageType;
import cloud.datatp.fforwarder.core.partner.PartnerNotificationTemplate;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.data.db.entity.PersistableEntity;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.Arrays;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.DateUtil;

@Entity
@Table(name = PartnerRequest.TABLE_NAME,
  uniqueConstraints = {
    @UniqueConstraint(
      name = PartnerRequest.TABLE_NAME + "_partner_id",
      columnNames = {"partner_id"}
    ),
  },
  indexes = {
    @Index(
      name = PartnerRequest.TABLE_NAME + "_partner_id_idx",
      columnList = "partner_id"
    ),
    @Index(
      name = PartnerRequest.TABLE_NAME + "_request_by_account_idx",
      columnList = "request_by_account_id"
    ),
    @Index(
      name = PartnerRequest.TABLE_NAME + "_approved_by_account_idx",
      columnList = "approved_by_account_id"
    ),
  }
)
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor @Getter @Setter
public class PartnerRequest extends PersistableEntity<Long> {

  @Serial
  private static final long serialVersionUID = 1L;

  public static final String TABLE_NAME = "lgc_forwarder_partner_request";

  public enum PartnerRequestStatus {
    NEW, PENDING, APPROVED, REJECTED, CANCELLED;

    public static PartnerRequestStatus parse(String token) {
      if (token == null)
        return PENDING;
      try {
        return valueOf(token.trim().toUpperCase());
      } catch (IllegalArgumentException e) {
        return PENDING;
      }
    }

    public static boolean isCompleted(PartnerRequestStatus status) {
      return status == PartnerRequestStatus.APPROVED || status == PartnerRequestStatus.REJECTED
        || status == PartnerRequestStatus.CANCELLED;
    }
  }

  @Column(name = "partner_id")
  private Long partnerId;

  @Column(name = "bfsone_partner_code_temp")
  private String bfsonePartnerCodeTemp;

  @Column(name = "partner_name")
  private String partnerName;

  @Column(name = "partner_label")
  private String partnerLabel;

  @Enumerated(EnumType.STRING)
  private PartnerRequestStatus status = PartnerRequestStatus.NEW;

  @Column(name = "request_date")
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  private Date requestDate;

  @Column(name = "request_by_account_id")
  private Long requestByAccountId;

  @Column(name = "request_by_label")
  private String requestByLabel;

  // from setup
  @Column(name = "approved_by_account_id")
  private Long approvedByAccountId;

  @Column(name = "approved_by_label")
  private String approvedByLabel;

  @Column(name = "approved_date")
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  private Date approvedDate;

  @Column(name = "approved_note", length = 1024 * 4)
  private String approvedNote;

  // from setup
  @Column(name = "mail_to")
  private String to;

  // from setup
  @Column(name = "mail_cc")
  private String cc;

  public PartnerRequest(CRMPartner partner) {
    this.status = PartnerRequestStatus.NEW;
    this.requestDate = new Date();
    withCRMPartner(partner);
  }

  public PartnerRequest withCRMPartner(CRMPartner partner) {
    this.partnerId = partner.getId();
    this.partnerName = partner.getName();
    this.partnerLabel = partner.getLabel();
    this.bfsonePartnerCodeTemp = partner.getPartnerCode() != null ? partner.getPartnerCode() : partner.getPartnerCodeTemp();
    return this;
  }

  public PartnerRequest withRequestBy(Long accountId, String label) {
    this.requestByAccountId = accountId;
    this.requestByLabel = label;
    return this;
  }

  public PartnerRequest withApprovedBy(Long accountId, String label) {
    this.approvedByAccountId = accountId;
    this.approvedByLabel = label;
    return this;
  }

  public CRMMessageSystem toApprovalMailMessage(ClientContext client, CRMPartner partner) {
    Objects.assertTrue(
        status == PartnerRequestStatus.APPROVED || status == PartnerRequestStatus.REJECTED,
        "New Status must be APPROVED or REJECTED");
    CRMMessageSystem msg = new CRMMessageSystem();
    msg.setContent(PartnerNotificationTemplate.buildParterRequestMailApprovalMessage(this, partner));
    msg.setScheduledAt(new Date());
    msg.setMessageType(MessageType.MAIL);
    msg.setReferenceId(id);
    msg.setReferenceType(TABLE_NAME);
    msg.setPluginName(AnnualConferenceMessagePlugin.PLUGIN_TYPE);
    msg.setRecipients(new HashSet<>(Arrays.asList("<EMAIL>")));
    MapObject metadata = new MapObject();
    metadata.put("fromEmail", "<EMAIL>");
    metadata.put("subject", "CRM - Notification Message");
    //TODO: [DAN] replace mail to
    metadata.put("to", Arrays.asList("<EMAIL>"));
//    metadata.put("ccList", Arrays.asList("<EMAIL>"));
    msg.setMetadata(metadata);

    return msg;
  }

  public CRMMessageSystem toRequestMailMessage(ClientContext client, BFSOnePartnerGroup partnerGroup) {
    Objects.assertTrue(status == PartnerRequestStatus.NEW, "PartnerRequest status must be NEW");
    CRMMessageSystem msg = new CRMMessageSystem();
    msg.setContent(PartnerNotificationTemplate.buildPartnerRequestMailRequestMessage(this, partnerGroup));
    msg.setScheduledAt(new Date());
    msg.setMessageType(MessageType.MAIL);
    msg.setReferenceId(id);
    msg.setReferenceType(TABLE_NAME);
    msg.setPluginName(AnnualConferenceMessagePlugin.PLUGIN_TYPE);
    msg.setRecipients(new HashSet<>(Arrays.asList("<EMAIL>")));
    MapObject metadata = new MapObject();
    metadata.put("fromEmail", "<EMAIL>");
    metadata.put("subject", "CRM - Notification Message");
    //TODO: [DAN] replace mail to
    metadata.put("to", Arrays.asList("<EMAIL>"));
//    metadata.put("ccList", Arrays.asList("<EMAIL>"));
    msg.setMetadata(metadata);

    return msg;
  }
}