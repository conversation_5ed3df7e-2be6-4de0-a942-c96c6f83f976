package cloud.datatp.fforwarder.core.template.entity;

import cloud.datatp.fforwarder.core.message.entity.CRMMessageSystem;
import cloud.datatp.fforwarder.core.message.entity.MessageType;
import cloud.datatp.fforwarder.core.template.CrmUserRoleMessagePlugin;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import jakarta.validation.constraints.NotNull;

import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.Locale;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.account.entity.Account;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.entity.PersistableEntity;
import net.datatp.module.hr.entity.Employee;
import net.datatp.module.hr.entity.HRDepartment;
import net.datatp.util.text.StringUtil;

/**
 * <AUTHOR>
 * <EMAIL>
 * @version 1.0
 * @since 2024
 */

@Entity
@Table(
  name = CrmUserRole.TABLE_NAME,
  uniqueConstraints = {
    @UniqueConstraint(
      name = CrmUserRole.TABLE_NAME + "_bfsone_username",
      columnNames = {"bfsone_username"}
    ),
    @UniqueConstraint(
      name = CrmUserRole.TABLE_NAME + "_bfsone_code",
      columnNames = {"bfsone_code"}
    ),
  },
  indexes = {
    @Index(
      name = CrmUserRole.TABLE_NAME + "_full_name_idx",
      columnList = "full_name"
    ),
    @Index(
      name = CrmUserRole.TABLE_NAME + "_account_id_idx",
      columnList = "account_id"
    ),
    @Index(
      name = CrmUserRole.TABLE_NAME + "_bfsone_username_idx",
      columnList = "bfsone_username"
    ),
    @Index(
      name = CrmUserRole.TABLE_NAME + "_bfsone_code_idx",
      columnList = "bfsone_code"
    )
  }
)
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
@AllArgsConstructor
@Setter @Getter
public class CrmUserRole extends PersistableEntity<Long> {

  final static public String TABLE_NAME = "lgc_forwarder_crm_user_roles";

  public enum UserType {
    SALE_FREEHAND,
    SALE_AGENT,
    OVERSEAS,
    BACKOFFICE,
    OTHER
  }

  @Enumerated(EnumType.STRING)
  @Column(nullable = false)
  private UserType type;

  @NotNull
  @Column(name = "full_name", nullable = false)
  private String fullName;

  @NotNull
  @Column(name = "account_id")
  private Long accountId;

  @Column(name = "bfsone_username")
  private String bfsoneUsername;

  @Column(name = "bfsone_code")
  private String bfsoneCode;

  @Column(name = "company_branch_name")
  private String companyBranchName;

  @Column(name = "company_branch_code")
  private String companyBranchCode;

  @Column(name = "company_branch_id")
  private Long companyBranchId;

  @Column(name = "department_name")
  private String departmentName;

  @Column(name = "department_label")
  private String departmentLabel;

  @Column(name = "team")
  private String team;

  @Column(name = "position")
  private String position;

  @Column(name = "phone")
  private String phone;

  @Column(name = "email")
  private String email;

  @Column(name = "is_active")
  private boolean isActive = true;

  public CrmUserRole(Long accountId) {
    this.accountId = accountId;
  }

  public CrmUserRole(Employee employee) {
    setFullName(employee.getLabel());
    setBFSOneUsername(employee.getBfsoneUsername());
    setBFSOneCode(employee.getBfsoneCode());
    this.accountId = employee.getAccountId();
  }

  public CrmUserRole withAccount(Account account) {
    setEmail(account.getEmail());
    this.phone = account.getMobile();
    return this;
  }

  public CrmUserRole withCompany(ICompany company) {
    this.companyBranchName = company.getLabel();
    this.companyBranchCode = company.getCode();
    this.companyBranchId = company.getId();
    return this;
  }

  public CrmUserRole withDepartment(HRDepartment department) {
    this.departmentName = department.getName();
    this.departmentLabel = department.getLabel();
    this.team = department.getLabel();
    return this;
  }

  public CrmUserRole withType(UserType type) {
    this.type = type;
    return this;
  }

  public void setFullName(String fullName) {
    if (StringUtil.isEmpty(fullName)) return;
    this.fullName = fullName.toUpperCase(Locale.ROOT);
  }

  public boolean isOverseas() {
    return this.type == UserType.OVERSEAS;
  }

  public void setBFSOneUsername(String userName) {
    if (StringUtil.isEmpty(userName)) return;
    this.bfsoneUsername = userName.toUpperCase(Locale.ROOT);
  }

  public void setBFSOneCode(String code) {
    if (StringUtil.isEmpty(code)) return;
    this.bfsoneCode = code.toUpperCase(Locale.ROOT);
  }

  public void setEmail(String email) {
    if (StringUtil.isEmpty(email)) return;
    this.email = email.toLowerCase(Locale.ROOT);
  }

  public static CRMMessageSystem toUserRoleZaloMessage(String content) {
    CRMMessageSystem message = new CRMMessageSystem();
    message.setContent(content);
    message.setScheduledAt(new Date());
    message.setMessageType(MessageType.ZALO);
    message.setReferenceId(null);
    message.setReferenceType(CrmUserRole.TABLE_NAME);
    message.setPluginName(CrmUserRoleMessagePlugin.PLUGIN_TYPE);
    message.setRecipients(new HashSet<>(Collections.singletonList("84842283596")));
    return message;
  }

}