package cloud.datatp.fforwarder.core.partner.entity;

import cloud.datatp.fforwarder.core.message.entity.CRMMessageSystem;
import cloud.datatp.fforwarder.core.message.entity.MessageType;
import cloud.datatp.fforwarder.core.partner.PartnerMonitorMessagePlugin;
import cloud.datatp.fforwarder.core.partner.entity.PartnerRequest.PartnerRequestStatus;
import cloud.datatp.fforwarder.core.template.entity.CrmUserRole;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Index;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import jakarta.persistence.UniqueConstraint;
import jakarta.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.StringJoiner;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.account.entity.Account;
import net.datatp.module.company.entity.ShareableScope;
import net.datatp.module.data.db.entity.PersistableEntity;
import net.datatp.util.ds.MapObject;
import net.datatp.util.error.ErrorType;
import net.datatp.util.error.RuntimeError;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;

/**
 * <AUTHOR>
 */
@Entity
@Table(
  name = CRMPartner.TABLE_NAME,
  uniqueConstraints = {
    @UniqueConstraint(
      name = CRMPartner.TABLE_NAME + "_account_id",
      columnNames = {"account_id"}
    ),
  },
  indexes = {
    @Index(
      name = CRMPartner.TABLE_NAME + "_name_idx",
      columnList = "name"
    ),
    @Index(
      name = CRMPartner.TABLE_NAME + "_partner_code_idx",
      columnList = "partner_code"
    ),
    @Index(
      name = CRMPartner.TABLE_NAME + "_tax_code_idx",
      columnList = "tax_code"
    ),
  }
)
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor
@Getter @Setter
public class CRMPartner extends PersistableEntity<Long> {

  public static final String TABLE_NAME = "lgc_forwarder_crm_partner";
  public static final String SEQUENCE = "lgc:lgc_forwarder_crm_partner";

  // Core identification
  @NotNull
  @Column(name = "account_id")
  private Long accountId;

  @Column(name = "partner_code")
  private String partnerCode;

  @Column(name = "partner_code_temp")
  private String partnerCodeTemp;

  @Column(name = "lead_code")
  private String leadCode;

  @Enumerated(EnumType.STRING)
  private PartnerRequestStatus status = PartnerRequestStatus.NEW;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "date_created")
  private Date dateCreated;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "date_modified")
  private Date dateModified;

  @Enumerated(EnumType.STRING)
  @Column(name = "partner_group", nullable = false)
  private BFSOnePartnerGroup partnerGroup;

  @Enumerated(EnumType.STRING)
  @Column(name = "category")
  private Category category = Category.CUSTOMER;

  // ----------- Basic information ---------------
  @Column(name = "name", length = 1024)
  private String name;

  @Column(name = "label", length = 2 * 1024)
  private String label;

  @Column(name = "localized_label", length = 2 * 1024)
  private String localizedLabel;

  // ----------------- Contact information ----------------
  @Column(name = "personal_contact")
  private String personalContact;

  @Column(name = "position")
  private String position;

  @Column(name = "email")
  private String email;

  @Column(name = "cell")
  private String cell;

  // ----------------- VIP Contact information ----------------
  @Column(name = "vip_contact")
  private String vipContact;

  @Column(name = "vip_position")
  private String vipPosition;

  @Column(name = "vip_cellphone")
  private String vipCellphone;

  @Column(name = "vip_email")
  private String vipEmail;

  @Column(name = "fax")
  private String fax;

  @Column(name = "home_phone")
  private String homePhone;

  @Column(name = "work_phone")
  private String workPhone;

  // ----------------  Business information ----------------
  @Column(name = "tax_code")
  private String taxCode;

  @Column(name = "source")
  private String source;

  @Column(name = "investment_origin")
  private String investmentOrigin;

  @Column(name = "industry_code")
  private String industryCode;

  @Column(name = "industry_label")
  private String industryLabel;

  @Column(name = "routing", length = 1024)
  private String routing;

  // ----------------  Location information ----------------
  @Column(name = "country_id")
  private Long countryId;

  @Column(name = "country_label")
  private String countryLabel;

  @Column(name = "province_id")
  private Long provinceId;

  @Column(name = "province_label")
  private String provinceLabel;

  @Column(name = "kcn_code")
  private String kcnCode;

  @Column(name = "kcn_label")
  private String kcnLabel;

  @Column(length = 9 * 1024)
  private String address;

  @Column(name = "localized_address", length = 9 * 1024)
  private String localizedAddress;

  @Column(name = "work_address", length = 9 * 1024)
  private String workAddress;

  // ----------------  Bank information ----------------
  @Column(name = "bank_accs_no")
  private String bankAccsNo;

  @Column(name = "bank_name", length = 64 * 1024)
  private String bankName;

  @Column(name = "bank_account")
  private String bankAccount;

  @Column(name = "bank_currency")
  private String bankCurrency;

  @Column(name = "bank_address", length = 64 * 1024)
  private String bankAddress;

  @Column(name = "swift_code")
  private String swiftCode;

  @Column(name = "print_custom_confirm_bill_info", length = 64 * 1024)
  private String printCustomConfirmBillInfo;

  @Column(length = 1024 * 32)
  private String note;

  @Column(length = 1024 * 32)
  private String suggestion;

  // Sales owner information
  @Column(name = "sale_owner_contact_code")
  private String saleOwnerContactCode;

  @Column(name = "sale_owner_username")
  private String saleOwnerUsername;

  @Column(name = "input_username")
  private String inputUsername;

  @Enumerated(EnumType.STRING)
  private ShareableScope shareable;

  @Enumerated(EnumType.STRING)
  private Scope scope = Scope.Domestic;

  @Column(name = "bfsone_group_id")
  private Integer bfsoneGroupId;

  @Column(name = "group_name")
  private String groupName;

  @Column(name = "is_refund")
  private boolean refund = false;

  @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
  @JoinColumn(name = "partner_id", referencedColumnName = "id")
  private List<CRMPartnerSource> sources = new ArrayList<>();

  @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
  @JoinColumn(name = "partner_id", referencedColumnName = "id")
  private List<CRMPartnerTransportGroup> transportGroup = new ArrayList<>();

  // ----------------  Saleman Request ----------------
  @Transient
  private String salemanObligationCode;

  @Transient
  private Long requestSalemanAccountId;

  @Transient
  private String requestSalemanLabel;

  public void setPartnerCodeTemp(String partnerCodeTemp) {
    this.partnerCodeTemp = partnerCodeTemp;
    if (StringUtil.isEmpty(partnerCode)) partnerCode = partnerCodeTemp;
  }

  public CRMPartner(MapObject record) {
    this.partnerCode = record.getString("partner_code");
    this.name = record.getString("name", "");
    this.label = record.getString("label", "");
    this.localizedLabel = record.getString("localized_label", "");
    this.address = record.getString("address", "");
    this.localizedAddress = record.getString("localized_address", "");
    this.personalContact = record.getString("personal_contact", "");
    this.email = record.getString("email", "");
    this.fax = record.getString("fax", "");
    this.cell = record.getString("cell", "");
    this.homePhone = record.getString("home_phone", "");
    this.workPhone = record.getString("work_phone", "");
    this.taxCode = record.getString("tax_code", null);
    this.countryLabel = record.getString("country_label", "");
    this.source = record.getString("source", "");
    this.bankAccsNo = record.getString("bank_accs_no", "");
    this.bankName = record.getString("bank_name", "");
    this.bankAccount = record.getString("bank_account", "");
    this.bankAddress = record.getString("bank_address", "");
    this.scope = Scope.parse(record.getString("scope", ""));
    this.industryCode = record.getString("industry_code");
    this.industryLabel = record.getString("industry_label");
    this.note = record.getString("note_1", "");
    this.category = Category.parse(record.getString("category", ""));

    this.saleOwnerContactCode = record.getString("sale_owner_code", "");
    this.saleOwnerUsername = record.getString("sale_owner_username", "");

    this.inputUsername = record.getString("input_username", "");
    this.salemanObligationCode = record.getString("saleman_obligation_code", "");

    this.dateCreated = record.getDate("date_created", null);
    this.dateModified = record.getDate("date_modified", null);

    String additionalNote = record.getString("note_2", "");
    if (!additionalNote.isEmpty()) this.note += "\n" + additionalNote;

    String customerTypeIdStr = record.getString("customer_type_id", null);
    if (StringUtil.isNotEmpty(customerTypeIdStr)) {
      int customerTypeId = Integer.parseInt(customerTypeIdStr);
      if (customerTypeId > 0) {
      }
      this.bfsoneGroupId = customerTypeId;

      if (customerTypeId == 1) {
        this.groupName = "NORMAL";
      } else if (customerTypeId == 2) {
        this.groupName = "FACTORY";
      } else if (customerTypeId == 3) {
        this.groupName = "CO-LOADER";
      } else if (customerTypeId == 4) {
        this.groupName = "OTHERS";
      }
    }

    withBillReference();
    computePartnerGroup();
    boolean isPublic = record.getBoolean("public", false);
    computeShareableScope(isPublic);
  }

  public void setName(String name) {
    if (StringUtil.isEmpty(name)) {
      throw new RuntimeError(ErrorType.IllegalArgument, "Name is empty!!!");
    }
    this.name = name.toUpperCase();
  }

  public CRMPartner computeShareableScope(boolean isPublic) {
    if (isPublic) shareable = ShareableScope.ORGANIZATION;
    else shareable = ShareableScope.PRIVATE;
    return this;
  }

  public CRMPartner withInputEmployee(CrmUserRole inputPerson) {
    this.inputUsername = inputPerson.getBfsoneUsername();
    return this;
  }

  public CRMPartner withAccount(Account account) {
    accountId = account.getId();
    if (StringUtil.isEmpty(partnerCode)) partnerCode = account.getLegacyLoginId();
    return this;
  }

  public void computePartnerGroup() {
    if (category != null) this.partnerGroup = category.getGroup();
  }

  public MapObject toBFSOnePartnerDep() {
    computePartnerGroup();
    MapObject rec = new MapObject();
    if (StringUtil.isNotEmpty(this.partnerCodeTemp)) {
      rec.put("PartnerID", this.partnerCodeTemp);
    } else {
      rec.put("PartnerID", this.partnerCode);
    }
    rec.put("PartnerName", this.name);
    rec.put("PartnerName2", this.label);
    rec.put("PartnerName3", this.localizedLabel);
    rec.put("PersonalContact", this.personalContact);
    if (StringUtil.isNotEmpty(this.address)) {
      if (this.address.trim().isEmpty()) this.address = "N/A";
    } else {
      this.address = "N/A";
    }
    rec.put("Address", this.address);

    if (StringUtil.isNotEmpty(this.localizedAddress)) {
      if (this.localizedAddress.trim().isEmpty()) this.localizedAddress = this.address;
    } else {
      this.localizedAddress = this.address;
    }
    rec.put("Address2", this.localizedAddress);
    rec.put("Fax", this.fax);
    rec.put("Cell", this.cell);
    rec.put("Taxcode", this.taxCode);
    rec.put("Email", this.email);
    rec.put("Country", this.countryLabel != null ? this.countryLabel : "VIETNAM");
    rec.put("Website", "");
    rec.put("ContactID", "");
    rec.put("Source", this.source);
    rec.put("BankAccsNo", this.bankAccsNo);
    rec.put("BankName", this.bankName);
    rec.put("BankAccount", this.bankAccount);
    rec.put("BankAddress", this.bankAddress);
    rec.put("SwiftCode", this.swiftCode);
    rec.put("Location", this.scope.toString());
    rec.put("Category", this.category.getName());
    rec.put("IndustryID", this.industryCode);
    rec.put("IndustryLabel", this.industryLabel);
    rec.put("DateToEstablish", null);
    rec.put("Notes", this.note);
    rec.put("RequestUser", "");
    rec.put("Email_Request", "");
    rec.put("isRefund", this.isRefund());

    if (this.partnerGroup.equals(BFSOnePartnerGroup.CUSTOMERS)) {
      if (Objects.nonNull(this.bfsoneGroupId)) {
        rec.put("PartnerGroupID", this.bfsoneGroupId);
      } else if (this.groupName.equalsIgnoreCase("NORMAL")) {
        rec.put("PartnerGroupID", 1.0);
      } else if (this.groupName.equalsIgnoreCase("FACTORY")) {
        rec.put("PartnerGroupID", 2.0);
      } else if (this.groupName.equalsIgnoreCase("CO-LOADER")) {
        rec.put("PartnerGroupID", 3.0);
      } else if (this.groupName.equalsIgnoreCase("OTHERS")) {
        rec.put("PartnerGroupID", 4.0);
      } else {
        throw RuntimeError.IllegalArgument("Partner group is not supported: " + this.groupName);
      }
    } else {
      rec.put("PartnerGroupID", "");
    }
    return rec;
  }

  public MapObject toBFSOneAgent() {
    computePartnerGroup();
    MapObject rec = new MapObject();
    rec.put("DateCreate", "");
    rec.put("DateModify", "");
    rec.put("PartnerID", this.partnerCode != null ? this.partnerCode : "");
    rec.put("PartnerName", this.name);
    rec.put("PartnerName2", this.label);
    rec.put("PartnerName3", this.localizedLabel);

    // Contact information
    rec.put("PersonalContact", this.personalContact);
    rec.put("JobTitle", this.position);
    rec.put("Cell", this.cell);
    rec.put("Email", this.email);

    // VIP Contact information
    rec.put("VIPContact", this.vipContact);
    rec.put("VIPPosition", this.vipPosition);
    rec.put("VIPCellPhone", this.vipCellphone);
    rec.put("VIPEmail", this.vipEmail);

    if (StringUtil.isNotEmpty(this.address)) {
      if (this.address.trim().isEmpty()) this.address = "N/A";
    } else {
      this.address = "N/A";
    }

    if (StringUtil.isNotEmpty(this.workAddress)) {
      if (this.workAddress.trim().isEmpty()) this.workAddress = this.address;
    } else {
      this.workAddress = this.address;
    }

    rec.put("Address", this.address);
    rec.put("WorkAddress", this.workAddress);

    if (StringUtil.isNotEmpty(this.localizedAddress)) {
      if (this.localizedAddress.trim().isEmpty()) this.localizedAddress = this.address;
    } else {
      this.localizedAddress = this.address;
    }
    rec.put("Address2", this.localizedAddress);

    // Location details
    rec.put("Country", this.countryLabel != null ? this.countryLabel : "VIETNAM");
    rec.put("WorkState", this.provinceLabel != null ? this.provinceLabel : "");
    rec.put("WorkCity", ""); // TODO: add field
    rec.put("WorkZipCode", ""); // Not need

    rec.put("ContactID", ""); //TODO: Nhat - set sale man contact id
    rec.put("WorkPhone", this.workPhone);
    rec.put("HomePhone", this.homePhone);
    rec.put("OnlineChat", "");
    rec.put("Fax", this.fax);

    // Business information
    rec.put("Location", this.scope.toString());
    rec.put("TaxCode", this.taxCode);
    rec.put("Category", this.category.getName());
    rec.put("Website", "");

    // Bank information - restructured
    rec.put("BankName", this.bankName);
    rec.put("SwiftCode", this.swiftCode);

    // Other fields
    rec.put("FieldInterested", "");
    rec.put("InputPeople", "JESSE.VNHPH");
    rec.put("NotesLess", this.note);
    rec.put("Notes", "");

    // Transport Group List
    rec.put("TransGroupList", this.transportGroup != null ?
      this.transportGroup.stream().map(tg -> {
        MapObject transport = new MapObject();
        transport.put("IDKey", "");
        transport.put("PartnerID", this.partnerCode);
        transport.put("ServiceName", tg.getServiceName());
        transport.put("isAllow", tg.isAllow());
        return transport;
      }).collect(Collectors.toList()) : new ArrayList<>());

    // Partner Source List - populated from sources field
    rec.put("PartnerSourceList", this.sources != null ?
      this.sources.stream().map(source -> {
        MapObject sourceMap = new MapObject();
        sourceMap.put("IDKey", "");
        sourceMap.put("PartnerID", this.partnerCode);
        sourceMap.put("SourceID", source.getSourceId());
        sourceMap.put("SourceName", source.getSourceName());
        return sourceMap;
      }).collect(Collectors.toList()) : new ArrayList<>());

    return rec;
  }

  public MapObject toBFSOnePartner() {
    computePartnerGroup();
    MapObject rec = new MapObject();

    rec.put("DateCreate", "");
    rec.put("DateModify", "");
    rec.put("PartnerID", this.partnerCode);
    rec.put("PartnerName", this.name);
    rec.put("PartnerName2", this.label);
    rec.put("PartnerName3", this.localizedLabel);
    rec.put("PersonalContact", this.personalContact);
    rec.put("JobTitle", this.position != null ? this.position : "");
    rec.put("Cell", this.cell);
    rec.put("Email", this.email);
    rec.put("isRefund", this.isRefund());

    if (StringUtil.isNotEmpty(this.address)) {
      if (this.address.trim().isEmpty()) this.address = "N/A";
    } else {
      this.address = "N/A";
    }

    rec.put("Address", this.address);
    rec.put("WorkAddress", this.workAddress);

    if (StringUtil.isNotEmpty(this.localizedAddress)) {
      if (this.localizedAddress.trim().isEmpty()) this.localizedAddress = this.address;
    } else {
      this.localizedAddress = this.address;
    }
    rec.put("Address2", this.localizedAddress);

    // Location information
    rec.put("Country", this.countryLabel != null ? this.countryLabel : "VIETNAM");
    rec.put("WorkState", this.provinceLabel != null ? this.provinceLabel : "");
    rec.put("WorkCity", "");
    rec.put("WorkZipCode", "");

    // Contact details
    rec.put("ContactID", "");
    rec.put("WorkPhone", this.workPhone);
    rec.put("HomePhone", this.homePhone);
    rec.put("OnlineChat", "");
    rec.put("Fax", this.fax);

    // Business information
    rec.put("Location", this.scope.toString());
    rec.put("TaxCode", this.taxCode);
    rec.put("Category", this.category.getName());
    rec.put("Website", "");

    // Bank information
    rec.put("BankName", this.bankName);
    rec.put("SwiftCode", this.swiftCode);
    rec.put("BankAddress", this.bankAddress);

    // User and notes
    rec.put("InputPeople", this.inputUsername != null ? this.inputUsername : "JESSE.VNHPH");
    rec.put("NotesLess", this.note);
    rec.put("Notes", "");
    rec.put("FieldInterested", this.suggestion);

    if (this.partnerGroup.equals(BFSOnePartnerGroup.CUSTOMERS)) {
      if (Objects.nonNull(this.bfsoneGroupId)) {
        rec.put("CustomertypeID", this.bfsoneGroupId.doubleValue());
      } else if (this.groupName != null) {
        if (this.groupName.equalsIgnoreCase("NORMAL")) {
          rec.put("CustomertypeID", 1.0);
        } else if (this.groupName.equalsIgnoreCase("FACTORY")) {
          rec.put("CustomertypeID", 2.0);
        } else if (this.groupName.equalsIgnoreCase("CO-LOADER")) {
          rec.put("CustomertypeID", 3.0);
        } else if (this.groupName.equalsIgnoreCase("OTHERS")) {
          rec.put("CustomertypeID", 4.0);
        } else {
          rec.put("CustomertypeID", 1.0);
        }
      } else {
        rec.put("CustomertypeID", 1.0);
      }
    } else {
      rec.put("CustomertypeID", "");
    }
    rec.put("Industry", this.industryCode != null ? this.industryCode : "");

    // Partner Source List - populated from sources field
    rec.put("PartnerSourceList", this.sources != null ?
      this.sources.stream().map(source -> {
        MapObject sourceMap = new MapObject();
        sourceMap.put("IDKey", source.getId() != null ? source.getId().toString() : "");
        sourceMap.put("PartnerID", this.partnerCode);
        sourceMap.put("SourceID", source.getSourceId());
        sourceMap.put("SourceName", source.getSourceName());
        return sourceMap;
      }).collect(Collectors.toList()) : new ArrayList<>());

    return rec;
  }

  public CRMPartner withBillReference() {
    if (StringUtil.isNotEmpty(this.printCustomConfirmBillInfo)) return this;
    StringJoiner joiner = new StringJoiner("\n");
    joiner.setEmptyValue("");
    joiner.add(name != null ? name : "");
    joiner.add(address != null ? address.trim() : "");
    joiner.add(personalContact != null ? personalContact.trim() : "");

    StringBuilder contact = new StringBuilder();
    if (StringUtil.isNotEmpty(cell)) contact.append("TEL :").append(cell.trim());
    if (StringUtil.isNotEmpty(fax)) {
      if (!contact.isEmpty()) contact.append(" ");
      contact.append("Fax:").append(fax.trim());
    }

    joiner.add(contact.toString());
    this.setPrintCustomConfirmBillInfo(joiner.toString());
    return this;
  }

  public boolean isAgent() {
    return this.partnerGroup == BFSOnePartnerGroup.AGENTS;
  }

  public static Map<String, List<CRMPartner>> groupBySaleObligation(List<? extends MapObject> records) {
    return records.stream().map(CRMPartner::new)
      .collect(Collectors.groupingBy(CRMPartner::getSalemanObligationCode));
  }

  public static Map<String, List<CRMPartner>> groupBySaleOwnerObligation(List<? extends MapObject> records) {
    return records.stream().map(CRMPartner::new)
      .collect(Collectors.groupingBy(CRMPartner::getSaleOwnerContactCode));
  }

  public static List<CRMPartner> computeToBFSOnePartner(List<MapObject> records) {
    return records.stream().map(CRMPartner::new).collect(Collectors.toList());
  }

  public static CRMMessageSystem toApprovePartnerZaloMessage(String content) {
    CRMMessageSystem message = new CRMMessageSystem();
    message.setContent(content);
    message.setScheduledAt(new Date());
    message.setMessageType(MessageType.ZALO);
    message.setReferenceId(null);
    message.setReferenceType(CRMPartner.TABLE_NAME);
    message.setPluginName(PartnerMonitorMessagePlugin.PLUGIN_TYPE);
    message.setRecipients(new HashSet<>(Collections.singletonList("84842283596")));
    return message;
  }

}