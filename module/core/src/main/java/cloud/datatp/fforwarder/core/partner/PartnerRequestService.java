package cloud.datatp.fforwarder.core.partner;

import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.service.BaseComponent;
import net.datatp.security.client.ClientContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cloud.datatp.fforwarder.core.partner.entity.PartnerRequest;

import java.util.List;

@Service("PartnerRequestService")
public class PartnerRequestService extends BaseComponent {

  @Autowired
  private PartnerRequestLogic partnerRequestLogic;

  @Transactional(readOnly = true)
  public PartnerRequest getById(ClientContext client, Long id) {
    return partnerRequestLogic.getById(client, id);
  }

  @Transactional(readOnly = true)
  public List<PartnerRequest> findByPartnerId(ClientContext client, Long partnerId) {
    return partnerRequestLogic.findByPartnerId(client, partnerId);
  }

  @Transactional
  public PartnerRequest savePartnerRequest(ClientContext client, PartnerRequest request) {
    return partnerRequestLogic.savePartnerRequest(client, request);
  }
  
  @Transactional
  public PartnerRequest updatePartnerRequestStatus(ClientContext client, PartnerRequest request) {
    return partnerRequestLogic.updatePartnerRequestStatus(client, request);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchPartnerRequests(ClientContext client, SqlQueryParams params) {
    return partnerRequestLogic.searchPartnerRequests(client, params);
  }
}