package cloud.datatp.fforwarder.core.partner;

import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cloud.datatp.fforwarder.core.db.CRMDaoService;
import cloud.datatp.fforwarder.core.integration.BFSOneApi;
import cloud.datatp.fforwarder.core.message.CRMMessageLogic;
import cloud.datatp.fforwarder.core.message.entity.CRMMessageSystem;
import cloud.datatp.fforwarder.core.partner.entity.BFSOnePartnerGroup;
import cloud.datatp.fforwarder.core.partner.entity.CRMPartner;
import cloud.datatp.fforwarder.core.partner.entity.PartnerRequest;
import cloud.datatp.fforwarder.core.partner.entity.SalemanPartnerObligation;
import cloud.datatp.fforwarder.core.partner.plugin.PartnerServicePlugin;
import cloud.datatp.fforwarder.core.partner.repository.CRMPartnerRepository;
import cloud.datatp.fforwarder.core.template.CRMPermissionTemplateLogic;
import cloud.datatp.fforwarder.core.template.CRMUserRoleLogic;
import cloud.datatp.fforwarder.core.template.entity.CrmUserPermissionTemplate;
import cloud.datatp.fforwarder.core.template.entity.CrmUserRole;
import cloud.datatp.fforwarder.core.template.entity.PermissionScope;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import net.datatp.lib.executable.ExecutableContext;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.NewAccountModel;
import net.datatp.module.account.entity.Account;
import net.datatp.module.account.entity.AccountType;
import net.datatp.module.company.CompanyConfigLogic;
import net.datatp.module.company.CompanyLogic;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.RangeFilter;
import net.datatp.module.data.db.query.SearchFilter;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.data.db.seq.SeqService;
import net.datatp.module.data.db.util.DBConnectionUtil;
import net.datatp.module.data.db.util.DeleteGraphBuilder;
import net.datatp.module.hr.entity.Employee;
import net.datatp.module.resource.ResourceLogic;
import net.datatp.module.resource.entity.OwnerType;
import net.datatp.module.resource.location.CountryLogic;
import net.datatp.module.resource.location.entity.Country;
import net.datatp.module.resource.misc.ContactLogic;
import net.datatp.module.resource.misc.entity.AccountContact;
import net.datatp.module.service.ExecutableUnitManager;
import net.datatp.security.client.ClientContext;
import net.datatp.util.dataformat.DataSerializer;
import net.datatp.util.ds.Arrays;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.StringUtil;

@Slf4j
@Getter
@Component
public class CRMPartnerLogic extends CRMDaoService {

  @Autowired
  private CRMPartnerRepository partnerRepo;

  @Autowired
  private SalemanPartnerObligationLogic obligationLogic;

  @Autowired
  private ContactLogic contactLogic;

  @Autowired
  private ResourceLogic resourceLogic;

  @Autowired
  private CompanyLogic companyLogic;

  @Autowired
  private CountryLogic countryLogic;

  @Autowired
  private AccountLogic accountLogic;

  @Autowired
  private BFSOneApi bfsOneApi;

  @Autowired
  private ExecutableUnitManager executableUnitManager;

  @Autowired
  private CompanyConfigLogic companyConfigLogic;

  @Autowired
  private CRMMessageLogic crmMessageLogic;

  @Autowired
  private CRMUserRoleLogic crmUserRoleLogic;

  @Autowired
  private PartnerRequestLogic partnerRequestLogic;

  @Autowired
  private CRMPermissionTemplateLogic permissionTemplateLogic;

  @Autowired
  protected SeqService seqService;

  @Autowired(required = false)
  List<PartnerServicePlugin> plugins = new ArrayList<>();

  public CRMPartner getById(ClientContext client, Long id) {
    return partnerRepo.getById(id);
  }

  public CRMPartner getByAccountId(ClientContext client, Long accountId) {
    return partnerRepo.getByAccountId(accountId);
  }

  public CRMPartner save(ClientContext client, CRMPartner partner) {
    final boolean isNew = partner.isNew();
    partner.computePartnerGroup();
    partner.withBillReference();
    if (isNew || partner.getAccountId() == null) {
      try {
        Account account = getOrCreateAccount(client, partner);
        CRMPartner existed = getByAccountId(client, account.getId());
        if (existed != null) {
          existed.merge(partner);
          existed.set(client);
          return partnerRepo.save(existed);
        } else {
          partner.withAccount(account);
        }
      } catch (Exception e) {
        log.error("Error while creating BFSOnePartner: {}", partner.getPartnerCode(), e);
        throw new RuntimeException("Failed to create BFSOnePartner: " + partner.getPartnerCode(), e);
      }
    }
    partner.set(client);
    return partnerRepo.save(partner);
  }

  public CRMPartner createBFSPartner(ClientContext client, CRMPartner partner) {
    final boolean isNew = partner.isNew();

    if (isNew && StringUtil.isNotEmpty(partner.getPartnerCode())) {
      //TODO: fetch BFSOnePartner from BFSOne system to update permission.
      CRMPartner bfsOnePartner = fetchCRMPartnersByCode(client, partner.getPartnerCode());
      Objects.assertNotNull(bfsOnePartner, "BFSOne Partner is not found, code = " + partner.getPartnerCode());
      CrmUserRole saleman = crmUserRoleLogic.getByBfsoneCode(client, bfsOnePartner.getSaleOwnerContactCode());
      Objects.assertNotNull(saleman, "Saleman is not found, code = " + bfsOnePartner.getSaleOwnerContactCode());
      List<CRMPartner> partners = importPartners(client, saleman, java.util.Collections.singletonList(partner));
      return partners.get(0);
    } else {
      CrmUserRole saleman = crmUserRoleLogic.getByAccountId(client, client.getAccountId());
      Objects.assertNotNull(saleman, "Creator Employee {} is not found", client.getRemoteUser());
      partner.withInputEmployee(saleman);
      partner.computePartnerGroup();
      partner.withBillReference();

      BFSOnePartnerGroup group = partner.getPartnerGroup();
      
      if (isNew) {
        String prefix = BFSOnePartnerGroup.getPartnerCodeTempPrefix(group);
        String partnerCodeTemp = prefix + String.format("%06d", seqService.nextSequence(CRMPartner.SEQUENCE)) + "_TEMP";
        partner.setPartnerCodeTemp(partnerCodeTemp);
      }

      for (PartnerServicePlugin plugin : plugins) {
        plugin.onPreSave(client, partner, false);
      }

      CRMPartner savedPartner = save(client, partner);
      obligationLogic.createIfNotExists(client, new SalemanPartnerObligation(saleman, savedPartner));

      for (PartnerServicePlugin plugin : plugins) {
        plugin.onPostSave(client, savedPartner, isNew);
      }
      
      PartnerRequest req = new PartnerRequest(savedPartner);
      req.withRequestBy(partner.getRequestSalemanAccountId(), partner.getRequestSalemanLabel());

      CrmUserPermissionTemplate approver = getPartnerRequestApprover(client, group);

      if (approver != null) {
        Account approverAccount = accountLogic.getAccountById(client, approver.getAccountId());
        Objects.assertNotNull(approverAccount, "Account is not found by id = {}", approver.getAccountId());

        req.withApprovedBy(approverAccount.getId(), approverAccount.getFullName());
        req.setTo(approverAccount.getEmail());

        CRMMessageSystem crmMessageSystem = req.toRequestMailMessage(client, group);
        crmMessageLogic.scheduleMessage(client, crmMessageSystem);

        req = partnerRequestLogic.savePartnerRequest(client, req);
      }

      return savedPartner;
    }
  }

  public CrmUserPermissionTemplate getPartnerRequestApprover(ClientContext client, BFSOnePartnerGroup group) {
    Set<CrmUserPermissionTemplate> approvers = new LinkedHashSet<>();

    List<PermissionScope> scopes = Arrays.asList(PermissionScope.GROUP_ALL, PermissionScope.COMPANY_ONLY);

    switch (group) {
      case AGENTS:
        for (PermissionScope scope : scopes) {
          List<CrmUserPermissionTemplate> list = permissionTemplateLogic.findUsersWithAgentApproveScope(client, scope);
          if (Collections.isNotEmpty(list)) approvers.addAll(list);
        }
        break;

      case CUSTOMERS:
        for (PermissionScope scope : scopes) {
          List<CrmUserPermissionTemplate> list = permissionTemplateLogic.findUsersWithCustomerApproveScope(client, scope);
          if (Collections.isNotEmpty(list)) approvers.addAll(list);
        }
        break;

      case COLOADERS:
        for (PermissionScope scope : scopes) {
          List<CrmUserPermissionTemplate> list = permissionTemplateLogic.findUsersWithColoaderApproveScope(client, scope);
          if (Collections.isNotEmpty(list)) approvers.addAll(list);
        }
        break;

      default:
        break;
    }

    return approvers.isEmpty() ? null : approvers.iterator().next();
  }

  public List<CRMPartner> importPartners(ClientContext client, Employee saleman, List<CRMPartner> partners) {
    List<CRMPartner> holder = new ArrayList<>();
    try {
      for (CRMPartner partner : partners) {
        CRMPartner partnerInDb = getByCode(client, partner.getPartnerCode());
        if (partnerInDb == null) {
          partnerInDb = save(client, partner);
        }
        obligationLogic.createIfNotExists(client, new SalemanPartnerObligation(saleman, partnerInDb));
        holder.add(partnerInDb);
      }
    } catch (Exception e) {
      log.error("-------------------- Create Partners Error, Saleman: {}-------------------------", saleman.getLabel());
      throw new RuntimeException(e);
    }
    return holder;
  }

  public List<CRMPartner> importPartners(ClientContext client, CrmUserRole userRole, List<CRMPartner> partners) {
    List<CRMPartner> holder = new ArrayList<>();
    try {
      for (CRMPartner partner : partners) {
        CRMPartner partnerInDb = getByCode(client, partner.getPartnerCode());
        if (partnerInDb == null) {
          partnerInDb = save(client, partner);
        }
        try {
          obligationLogic.createIfNotExists(client, new SalemanPartnerObligation(userRole, partnerInDb));
        } catch (Exception e) {
          log.error("Failed to create SalemanPartnerObligation for userRole: {}, partner: {}", userRole.getFullName(), partner.getPartnerCode());
          log.error("Error details: ", e);
        }
        holder.add(partnerInDb);
      }
    } catch (Exception e) {
      log.error("-------------------- Create Partners Error, Saleman: {}-------------------------", userRole.getFullName());
      throw new RuntimeException(e);
    }
    return holder;
  }

  public List<CRMPartner> importPartners(ClientContext client, List<MapObject> records) {
    List<CRMPartner> holder = new ArrayList<>();
    try {
      log.info("Fetch saleman authorized data -------------------------\n\n\n\n");
      Map<String, List<CRMPartner>> salePartnerGroup = CRMPartner.groupBySaleObligation(records);
      for (String bfsoneCode : salePartnerGroup.keySet()) {
        List<CRMPartner> partners = salePartnerGroup.get(bfsoneCode);
        CrmUserRole saleman = crmUserRoleLogic.getByBfsoneCode(client, bfsoneCode);
        Objects.assertNotNull(saleman, "Saleman is not found, bfsone code = " + bfsoneCode);
        List<CRMPartner> savedPartners = importPartners(client, saleman, partners);
        holder.addAll(savedPartners);
      }

      log.info("Fetch owner saleman partner data -------------------------\n\n\n\n");
      Map<String, List<CRMPartner>> firstSalePartnerGroup = CRMPartner.groupBySaleOwnerObligation(records);
      for (String bfsoneCode : firstSalePartnerGroup.keySet()) {
        List<CRMPartner> partners = firstSalePartnerGroup.get(bfsoneCode);
        CrmUserRole saleman = crmUserRoleLogic.getByBfsoneCode(client, bfsoneCode);
        Objects.assertNotNull(saleman, "Saleman is not found, bfsone code = " + bfsoneCode);
        List<CRMPartner> savedPartners = importPartners(client, saleman, partners);
        holder.addAll(savedPartners);
      }
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
    return holder;
  }

  public List<CRMPartner> syncBFSOnePartnersBySaleman(ClientContext client, Long salemanAccountId) {
    CrmUserRole saleman = crmUserRoleLogic.getByAccountId(client, salemanAccountId);
    Objects.assertNotNull(saleman, "Saleman is not found, account id = " + salemanAccountId);
    List<SqlMapRecord> records = searchBFSOnePartnersByContactIds(client, null, Arrays.asList(saleman.getBfsoneCode()));
    Map<String, List<CRMPartner>> allocateSalePartnerGroup = CRMPartner.groupBySaleObligation(records);
    Map<String, List<CRMPartner>> saleOwnerPartnerGroup = CRMPartner.groupBySaleOwnerObligation(records);
    List<CRMPartner> holder = new ArrayList<>();
    String bfsoneCode = saleman.getBfsoneCode();
    if (allocateSalePartnerGroup.containsKey(bfsoneCode)) {
      List<CRMPartner> bfsOnePartners = allocateSalePartnerGroup.get(bfsoneCode);
      holder.addAll(bfsOnePartners);
    }
    if (saleOwnerPartnerGroup.containsKey(bfsoneCode)) {
      List<CRMPartner> bfsOnePartners = saleOwnerPartnerGroup.get(bfsoneCode);
      holder.addAll(bfsOnePartners);
    }
    log.info("BFSOne Partners count = {}, Import for Saleman: {} ", holder.size(), saleman.getBfsoneUsername());
    return importPartners(client, saleman, holder);
  }

  public List<SqlMapRecord> searchCRMPartnersByModifiedTime(ClientContext client, Date fromTime, Date toTime) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/core/groovy/SearchCRMPartnerSql.groovy";
    SqlQueryParams sqlParams = new SqlQueryParams();
    sqlParams.add(new RangeFilter("modifiedTime").withinValue(fromTime, toTime));
    return searchDbRecords(client, scriptDir, scriptFile, "SearchCRMPartnersByModifiedTime", sqlParams);
  }

  public List<SqlMapRecord> searchCRMPartners(ClientContext client, SqlQueryParams sqlParams) {
    try {
      sqlParams.addParam("accessAccountId", client.getAccountId());
      sqlParams.addParam("companyId", client.getCompanyId());
      SearchFilter defaultFilter = sqlParams.getDefaultFilter();
      if (StringUtil.isEmpty(defaultFilter.getFilterValue())) sqlParams.addSearchTerm("search", "*");

      String scriptDir = appEnv.addonPath("logistics", "groovy");
      String scriptFile = "cloud/datatp/fforwarder/core/groovy/SearchCRMPartnerSql.groovy";
      return searchDbRecords(client, scriptDir, scriptFile, "SearchSalemanAuthorizeCRMPartner", sqlParams);
    } catch (Exception e) {
      log.error("-------------------- Search Saleman Authorize CRM Partners Error -------------------------");
      log.error(e.getMessage());
      return java.util.Collections.emptyList();
    }
  }

  public List<SqlMapRecord> searchCRMPartnerCustomers(ClientContext client, SqlQueryParams sqlParams) {
    try {
      sqlParams.addParam("companyId", client.getCompanyId());
      sqlParams.addParam("accessAccountId", client.getAccountId());
      String scriptDir = appEnv.addonPath("logistics", "groovy");
      String scriptFile = "cloud/datatp/fforwarder/core/groovy/SearchCRMPartnerSql.groovy";
      return searchDbRecords(client, scriptDir, scriptFile, "SearchCustomerAuthorizeCRMPartner", sqlParams);
    } catch (Exception e) {
      log.error("-------------------- Search Customer Authorize CRM Partners Error -------------------------");
      log.error(e.getMessage());
      return java.util.Collections.emptyList();
    }
  }

  public int deleteCRMPartner(ClientContext client, List<Long> targetIds) {
    DBConnectionUtil connectionUtil = getDBConnectionUtil();
    DeleteGraphBuilder deleteGraphBuilder = new DeleteGraphBuilder(connectionUtil, client.getCompanyId(), CRMPartner.class, targetIds);
    final int count = deleteGraphBuilder.runDelete();
    connectionUtil.commit();
    connectionUtil.close();
    return count;
  }

  public List<CRMPartner> findByTaxCode(ClientContext client, String taxCode) {
    return partnerRepo.findByTaxCode(taxCode);
  }

  public CRMPartner getByCode(ClientContext client, String code) {
    return partnerRepo.getByCode(code);
  }

  public List<CRMPartner> findByBFSOneCodeTemp(ClientContext client, String codeTemp) {
    return partnerRepo.findByBFSOneCodeTemp(codeTemp);
  }

  public Account getOrCreateAccount(ClientContext client, CRMPartner record) {
    String legacyLoginId = record.getPartnerCode();
    Account account = accountLogic.getAccountRepo().getByLegacyLoginId(legacyLoginId);
    if (account != null) return account;

    account = accountLogic.getAccountRepo().getByLoginId(legacyLoginId);
    if (account != null) return account;

    log.info("--- Create account: bfsone code = " + legacyLoginId);
    account = new Account();
    account.setLoginId(legacyLoginId);
    account.setAccountType(AccountType.ORGANIZATION);
    account.setFullName(record.getLabel());
    account.setLegacyLoginId(legacyLoginId);
    NewAccountModel model = accountLogic.createNewAccount(client, new NewAccountModel(account));
    account = model.getAccount();

    AccountContact contact = new AccountContact("Contact");
    List<Country> countries = countryLogic.findCountriesByPattern(client, record.getCountryLabel());
    if (Collections.isNotEmpty(countries)) {
      final Country country = countries.get(0);
      contact.setCountryName(country.getCode());
      contact.setCountryLabel(country.getLabel());
    }
    contact.setEmail(Arrays.asSet(account.getEmail()));
    contact.setMobile(Arrays.asSet(account.getMobile()));
    contact.setOwnerType(OwnerType.Account);
    contact.setOwnerId(account.getId());
    contact.setOwnerIdentifier(account.getLoginId());
    contactLogic.saveContact(client, contact);
    return account;
  }

  @SuppressWarnings("unchecked")
  @Deprecated
  public List<SqlMapRecord> searchBFSOnePartnersBySaleman(ClientContext client, ICompany company, List<Employee> employees) {
    SqlQueryParams sqlParams = new SqlQueryParams();
    sqlParams.addParam("contactIds", employees.stream()
      .map(Employee::getBfsoneCode)
      .filter(code -> code != null && !code.isEmpty())
      .collect(Collectors.toList()));

    String scriptDir = appEnv.addonPath("logistics", "groovy");
    ExecutableContext ctx =
      new ExecutableContext()
        .withScriptEnv(scriptDir, "cloud/datatp/fforwarder/core/partner/BFSOnePartnerLogicUnit.java", "FetchBFSOnePartnerBySaleman")
        .withParam(this).withParam(client).withParam(company).withParam(sqlParams);
    return (List<SqlMapRecord>) executableUnitManager.execute(ctx);
  }

  public List<SqlMapRecord> searchBFSOnePartnersByContactIds(ClientContext client, ICompany company, List<String> contactIds) {
    SqlQueryParams sqlParams = new SqlQueryParams();
    sqlParams.addParam("contactIds", contactIds);
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    ExecutableContext ctx =
      new ExecutableContext()
        .withScriptEnv(scriptDir, "cloud/datatp/fforwarder/core/partner/BFSOnePartnerLogicUnit.java", "FetchBFSOnePartnerBySaleman")
        .withParam(this).withParam(client).withParam(company).withParam(sqlParams);
    return (List<SqlMapRecord>) executableUnitManager.execute(ctx);
  }

  @SuppressWarnings("unchecked")
  public CRMPartner fetchCRMPartnersByCode(ClientContext client, String partnerCode) {
    SqlQueryParams sqlParams = new SqlQueryParams();
    sqlParams.addParam("partnerCode", partnerCode);

    String scriptDir = appEnv.addonPath("logistics", "groovy");
    ExecutableContext ctx =
      new ExecutableContext()
        .withScriptEnv(scriptDir, "cloud/datatp/fforwarder/core/partner/BFSOnePartnerLogicUnit.java", "FetchBFSOnePartnerBySaleman")
        .withParam(this).withParam(client).withParam(sqlParams);

    List<SqlMapRecord> records = (List<SqlMapRecord>) executableUnitManager.execute(ctx);
    if (Collections.isNotEmpty(records)) {
      SqlMapRecord record = records.get(0);
      return new CRMPartner(record);
    }
    return null;
  }

  public CRMPartner syncBFSOnePartnersByCode(ClientContext client, String partnerCode) {
    CRMPartner bfsOnePartner = fetchCRMPartnersByCode(client, partnerCode);
    if (bfsOnePartner != null) {
      CRMPartner partnerInDb = getByCode(client, bfsOnePartner.getPartnerCode());
      if (partnerInDb == null) {
        partnerInDb = save(client, bfsOnePartner);
      }

      String salemanObligationCode = bfsOnePartner.getSalemanObligationCode();
      if (StringUtil.isNotEmpty(salemanObligationCode)) {
        CrmUserRole allocatedSaleman = crmUserRoleLogic.getByBfsoneCode(client, salemanObligationCode);
        if (Objects.nonNull(allocatedSaleman)) {
          obligationLogic.createIfNotExists(client, new SalemanPartnerObligation(allocatedSaleman, partnerInDb));
        }
      }

      String saleOwnerContactCode = bfsOnePartner.getSaleOwnerContactCode();
      if (StringUtil.isNotEmpty(saleOwnerContactCode)) {
        CrmUserRole saleOwner = crmUserRoleLogic.getByBfsoneCode(client, saleOwnerContactCode);
        if (Objects.nonNull(saleOwner)) {
          obligationLogic.createIfNotExists(client, new SalemanPartnerObligation(saleOwner, partnerInDb));
        }
      }

      // Send notification for partner sync
      try {
        Account account = accountLogic.getAccountById(client, client.getAccountId());
        String updatedBy = account != null ? account.getFullName() : "System";
        String email = account != null ? account.getEmail() : "N/A";
        boolean isNewPartner = (partnerInDb.getDateCreated().getTime() > System.currentTimeMillis() - 60000); // Within 1 minute
        String zaloMessage = PartnerNotificationTemplate.buildPartnerSyncZaloMessage(partnerInDb, updatedBy, email, isNewPartner);
        CRMMessageSystem message = CRMPartner.toApprovePartnerZaloMessage(zaloMessage);
        message.setReferenceId(partnerInDb.getId());
        crmMessageLogic.scheduleMessage(client, message);
      } catch (Exception e) {
        log.error("Failed to send notification for partner sync: {}", partnerCode, e);
      }
      return partnerInDb;
    }
    return null;
  }

  // --------------- Integrate with BFSOne ----------------------------
  public MapObject createBFSOnePartner(ClientContext client, CRMPartner partner) {
    Long requestSalemanAccountId = partner.getRequestSalemanAccountId();
    if (requestSalemanAccountId == null) requestSalemanAccountId = client.getAccountId();
    CrmUserRole saleman = crmUserRoleLogic.getByAccountId(client, requestSalemanAccountId);
    Objects.assertNotNull(saleman, "Saleman is not found, account id = " + requestSalemanAccountId);
    String bfsoneEmployeeCode = saleman.getBfsoneCode();
    String bfsoneUsername = saleman.getBfsoneUsername();
    MapObject bfsOnePartner = partner.toBFSOnePartnerDep();
    bfsOnePartner.put("RequestUser", bfsoneUsername);
    bfsOnePartner.put("SalemanID", bfsoneEmployeeCode);
    bfsOnePartner.put("ContactID", bfsoneEmployeeCode);
    bfsOnePartner.put("Email_Request", saleman.getEmail());

    log.info("------------------------Partner---------------------------\n");
    DataSerializer.JSON.dump(bfsOnePartner);
    log.info("--------------------------------------------------------\n");

    BFSOnePartnerGroup group = partner.getPartnerGroup();
    String authenticate = bfsOneApi.authenticate(bfsoneEmployeeCode, bfsoneUsername);
    return bfsOneApi.createPartner(client, this, group, authenticate, bfsOnePartner);
  }

  public void updateBFSOnePartner(ClientContext client, MapObject partner, String errorMessage) {
    String zaloMessage = "";
    if (StringUtil.isNotEmpty(errorMessage)) {
      zaloMessage = errorMessage;
      DataSerializer.JSON.dump(partner);
    }
    CRMMessageSystem message = CRMPartner.toApprovePartnerZaloMessage(zaloMessage);
    crmMessageLogic.scheduleMessage(client, message);
  }

}