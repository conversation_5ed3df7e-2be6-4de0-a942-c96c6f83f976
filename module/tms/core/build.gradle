archivesBaseName = "datatp-logistics-module-tms-core"

eclipse {
  project {
    name = 'datatp-logistics-module-tms-core'
  }
}

dependencies {
  api group: 'net.datatp', name: 'datatp-lib-nlp', version: "$dataTPCore"
  api group: "net.datatp", name: "datatp-erp-module-odoo", version: "$dataTPErpVersion" ;
  api group: "net.datatp", name: "datatp-erp-module-asset-service", version: "$dataTPErpVersion" ;
//  api group: "net.datatp", name: "datatp-document-ie-document", version: "$dataTPErpVersion" ;

  api project(":datatp-logistics-module-bfsone");
  api project(":datatp-logistics-module-job-tracking");
  api project(":datatp-logistics-module-forwarder-settings");
  implementation 'net.datatp:datatp-document-ie-document:1.0.0';
//  implementation 'net.datatp:datatp-crm-module-forwarder-core:1.0.0';
}

