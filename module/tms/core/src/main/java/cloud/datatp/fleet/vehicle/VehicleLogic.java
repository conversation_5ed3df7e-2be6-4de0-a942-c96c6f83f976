package cloud.datatp.fleet.vehicle;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import cloud.datatp.fleet.vehicle.repository.*;
import net.datatp.module.resource.location.LocationLogic;
import net.datatp.module.resource.location.entity.Location;
import net.datatp.util.ds.MapObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cloud.datatp.fleet.vehicle.entity.Vehicle;
import cloud.datatp.fleet.vehicle.entity.VehicleFleet;
import cloud.datatp.fleet.vehicle.entity.VehicleFleetMembership;
import cloud.datatp.fleet.vehicle.entity.VehicleRefuel;
import cloud.datatp.fleet.vehicle.entity.VehicleTrip;
import cloud.datatp.fleet.vehicle.entity.VehicleTrip.TaskStatus;
import cloud.datatp.fleet.vehicle.entity.VehicleTripAttachment;
import cloud.datatp.fleet.vehicle.entity.VehicleTripGoodsTracking;
import cloud.datatp.fleet.vehicle.entity.VehicleTripRoute;
import cloud.datatp.fleet.vehicle.event.TMSBotTripTrackingEvent;
import cloud.datatp.fleet.vehicle.models.ConfirmTripByTransporterParams;
import cloud.datatp.gps.GPSConfigLogic;
import cloud.datatp.gps.GPSPluginManager;
import cloud.datatp.gps.GPSTrackingLogic;
import cloud.datatp.gps.entity.GPSConfig;
import cloud.datatp.gps.entity.GPSTrackingReport;
import cloud.datatp.gps.provider.entity.GPSInfo;
import cloud.datatp.gps.provider.entity.VehicleInfo;
import cloud.datatp.tms.bill.TMSBillLogic;
import cloud.datatp.tms.bill.entity.TMSBill;
import lombok.Getter;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.entity.Account;
import net.datatp.module.backend.Notification;
import net.datatp.module.bot.BotEvent.ProcessMode;
import net.datatp.module.bot.BotService;
import net.datatp.module.core.security.AuthorizedToken;
import net.datatp.module.core.security.api.ApiAuthorizationService;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.RecordGroupByMap;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.EditState;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.ClauseFilter;
import net.datatp.module.data.db.query.ConditionFilter;
import net.datatp.module.data.db.query.EntityTable;
import net.datatp.module.data.db.query.Join;
import net.datatp.module.data.db.query.OptionFilter;
import net.datatp.module.data.db.query.RangeFilter;
import net.datatp.module.data.db.query.SearchFilter;
import net.datatp.module.data.db.query.SqlQuery;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.data.db.seq.SeqService;
import net.datatp.module.monitor.SourceType;
import net.datatp.module.storage.CompanyStorage;
import net.datatp.module.storage.IStorageService;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.Collections;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.TokenUtil;

@Component
public class VehicleLogic extends DAOService {
  @Getter
  @Autowired
  private VehicleRepository          vehicleRepo;

  @Autowired
  private VehicleRefuelRepository    vehicleRefuelRepo;

  @Autowired
  private VehicleTripRepository      vehicleTripRepo;

  @Autowired
  private VehicleFleetMembershipRepository memberShipRepo;

  @Autowired
  private VehicleTripGoodsTrackingRepository goodsTrackingRepo;

  @Autowired
  private VehicleTripRouteRepository vehicleTripRouteRepo;

  @Autowired
  private TMSBillLogic               tmsBillLogic;

  @Autowired
  private AccountLogic               accountLogic;

  @Autowired
  private VehicleFleetLogic          vehicleFleetLogic;

  @Autowired
  private VehicleTripGoodsTrackingLogic trackingLogic;

  @Autowired
  private SeqService                 seqService;

  @Autowired
  private ApiAuthorizationService apiAuthorizationService;

  @Autowired
  private IStorageService             storageService;

  @Autowired
  private VehicleTripAttachmentRepository attachmentRepo;

  @Autowired
  private GPSPluginManager           gpsPluginManager;

  @Autowired
  private GPSConfigLogic             gpsConfigLogic;

  @Autowired
  private BotService                 botService;

  @Autowired
  private GPSTrackingLogic           gpsTrackingLogic;

  @Autowired
  private LocationLogic locationLogic;

  public Vehicle getVehicle(ClientContext client, ICompany company, String code) {
    return vehicleRepo.getByCode(company.getId(), code);
  }

  public Vehicle getVehicleByLicensePlate(ClientContext client, ICompany company, String licensePlate) {
    return vehicleRepo.getVehicleByLicensePlate(company.getId(), licensePlate);
  }

  public Vehicle getVehicleById(ClientContext client, ICompany company, Long id) {
    return vehicleRepo.getById(company.getId(), id);
  }

  public void removeVehicle(ClientContext client, ICompany company, Vehicle vehicle) {
    vehicleRepo.delete(vehicle);
    vehicleRepo.flush();
  }

  public List<SqlMapRecord> searchVehicles(ClientContext client, ICompany company, SqlQueryParams params) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fleet/vehicle/groovy/VehicleSql.groovy";
    params.addParam("companyId", company.getId());
    return searchDbRecords(client, scriptDir, scriptFile, "SearchVehicle", params);
  }

  public boolean changeVehicleStorageState(ClientContext client, ChangeStorageStateRequest req) {
    List<Vehicle> vehicles = vehicleRepo.findVehicles(req.getEntityIds());
    for (Vehicle vehicle : vehicles) {
      vehicleRepo.setVehiclesState(req.getNewStorageState(), vehicle.getCode());
    }
    return true;
  }

  public Vehicle saveVehicle(ClientContext client, ICompany company, Vehicle vehicle) {
    vehicle.set(client, company);
    return vehicleRepo.save(vehicle);
  }

  //VehicleRefuel
  public VehicleRefuel getVehicleRefuel(ClientContext client, ICompany company, Long id) {
    return vehicleRefuelRepo.getByCompany(company.getId(), id);
  }

  public VehicleRefuel getVehicleRefuel(ClientContext client, ICompany company, String code) {
    return vehicleRefuelRepo.getByCode(company.getId(), code);
  }

  public VehicleRefuel saveVehicleRefuel(ClientContext client, ICompany company, VehicleRefuel vehicleRefuel) {
    vehicleRefuel.set(client, company);
    return vehicleRefuelRepo.save(vehicleRefuel);
  }

  public boolean removeVehicleRefuel(ClientContext client, ICompany company, Vehicle vehicle) {
    vehicleRefuelRepo.removeByVehicleId(company.getId(), vehicle.getId());
    vehicleRefuelRepo.flush();
    return true;
  }

  public List<SqlMapRecord> searchVehicleRefuels(ClientContext client, ICompany company, SqlQueryParams params) {
    String[] SEARCH_FIELDS = new String[] {"code", "vehicleLabel"};
    params.addParam("companyId", company.getId());
    SqlQuery query = new SqlQuery().ADD_TABLE(new EntityTable(VehicleRefuel.class).selectAllFields())
        .JOIN(new Join("JOIN", Vehicle.class)
          .ON("id", VehicleRefuel.class, "vehicleId")
          .addSelectField("licensePlate", "licensePlate"))
        .FILTER(ClauseFilter.company(VehicleRefuel.class))
        .FILTER(new ConditionFilter(VehicleRefuel.class, "vehicleId", "= :vehicleId").hasVariableCondition("vehicleId"))
        .FILTER(SearchFilter.isearch(VehicleRefuel.class, SEARCH_FIELDS))
        .FILTER(OptionFilter.storageState(VehicleRefuel.class), RangeFilter.createdTime(VehicleRefuel.class),
            RangeFilter.modifiedTime(VehicleRefuel.class))
        .ORDERBY(new String[] { "modifiedTime" }, "modifiedTime", "DESC");
    return query(client, query, params).getSqlMapRecords();
  }

  //VehicleTrip
  public VehicleTrip getVehicleTrip(ClientContext client, ICompany company, String code) {
    return vehicleTripRepo.getByCode(company.getId(), code);
  }

  public VehicleTrip getVehicleTrip(ClientContext client, ICompany company, Long id) {
    return vehicleTripRepo.getById(company.getId(), id);
  }

  public boolean deleteVehicleTripById(ClientContext client, ICompany company, Long id) {
    vehicleTripRepo.deleteById(id);
    return true;
  }

  public boolean deleteVehicleTripByIds(ClientContext client, ICompany company, List<Long> ids) {
    vehicleTripRepo.deleteAllById(ids);
    return true;
  }

  public boolean deleteVehicleTrip(ClientContext client, ICompany company, VehicleTrip trip) {
    vehicleTripRepo.deleteById(trip.getId());
    return true;
  }

  public VehicleTrip saveVehicleTrip(ClientContext client, ICompany company, VehicleTrip trip) {
    if(trip.isNew()) {
      VehicleTrip checkExistTrip = getVehicleTrip(client, company, trip.getCode());
      if(checkExistTrip != null) trip.genCodeWithDateTimeId();
    }
    if(trip.getTaskStatus().equals(TaskStatus.SUBMITTED_PLAN) || trip.getTaskStatus().equals(TaskStatus.PLAN)) {
      trip.setActualEndTime(null);
      trip.setActualStartTime(null);
    }
    if(trip.getTaskStatus().equals(TaskStatus.TRANSPORTING)) {
      trip.setActualEndTime(null);
    }
    trip.set(client, company);

    TMSBotTripTrackingEvent botEvent = new TMSBotTripTrackingEvent(client, company, trip);
    botEvent.withProcessMode(ProcessMode.Queueable);
    botService.broadcast(SourceType.UserBot, botEvent);
    return vehicleTripRepo.save(trip);
  }

  public boolean removeVehicleTrip(ClientContext client, ICompany company, Vehicle vehicle) {
    List<VehicleTrip> trips = vehicleTripRepo.findByVehicleId(company.getId(), vehicle.getId());
    vehicleTripRepo.deleteAll(trips);
    return true;
  }

  public List<VehicleTrip> findVehicleTripByIds(ClientContext client, ICompany company, List<Long> ids) {
    return vehicleTripRepo.findByIds(company.getId(), ids);
  }

  public void flushRepositories() {
    vehicleTripRepo.flush();
    vehicleRefuelRepo.flush();
    vehicleRepo.flush();
  }

  public List<SqlMapRecord> searchVehicleTrips(ClientContext client, ICompany company, SqlQueryParams params) {
    Account account = accountLogic.getEditable(client, client.getRemoteUser());
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fleet/vehicle/groovy/VehicleSql.groovy";
    params.addParam("companyId", company.getId());
    params.addParam("accountId", account.getId());
    List<SqlMapRecord> results = searchDbRecords(client, scriptDir, scriptFile, "SearchVehicleTrip", params);
    List<Long> tripIds = new ArrayList<>();
    for(SqlMapRecord rec : results) {
      tripIds.add(rec.getLong("id"));
    }
    if(Collections.isEmpty(tripIds)) return results;

    RecordGroupByMap<Long, SqlMapRecord> trackingMap = findGoodsTrackingByTripIds(client, company, tripIds);
    for(SqlMapRecord rec : results) {
      Long vehicleTripId           = rec.getLong("id");
      List<SqlMapRecord> trackings = trackingMap.get(vehicleTripId);
      rec.add("trackings", trackings);
    }
    return results;
  }


  private Map<Long, Location> findLocationByIds(ClientContext client, List<Long> ids) {
    List<Location> locations = locationLogic.findLocations(client, ids);
    Map<Long, Location> map = new HashMap<>();
    for(Location location: locations) {
      map.put(location.getId(), location);
    }
    return map;
  }

  public RecordGroupByMap<Long, SqlMapRecord> findGoodsTrackingByTripIds(ClientContext client, ICompany company, List<Long> tripIds) {
    SqlQueryParams params = new SqlQueryParams();
    params.addParam("tripIds", tripIds);
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fleet/vehicle/groovy/VehicleTripGoodsTrackingSql.groovy";
    List<SqlMapRecord> trackings = searchDbRecords(client, scriptDir, scriptFile, "FindVehicleTripGoodsTrackingByTripIds", params);
    List<Long> locationIds = new ArrayList<>();
    if(Collections.isNotEmpty(trackings)) {
      for(SqlMapRecord tracking: trackings) {
        Long senderLocationId = tracking.getLong("senderLocationId");
        Long receiverLocationId = tracking.getLong("receiverLocationId");
        if (senderLocationId != null)  locationIds.add(senderLocationId);
        if (receiverLocationId != null)  locationIds.add(receiverLocationId);
      }

      Map<Long, Location> locationMap = findLocationByIds(client, locationIds);

      for(SqlMapRecord tracking: trackings) {
        Location senderLocation = locationMap.get(tracking.getLong("senderLocationId"));
        if(senderLocation != null) {
          tracking.add("senderAddress", senderLocation.getShortLabel());
        }

        Location receiverLocation = locationMap.get(tracking.getLong("receiverLocationId"));
        if(receiverLocation != null) {
          tracking.add("receiverAddress", receiverLocation.getShortLabel());
        }
      }
    }

    return new RecordGroupByMap<>(trackings, tracking -> (Long)tracking.getLong("vehicleTripId"));
  }

  public VehicleTrip newVehicleTrip(ClientContext client, ICompany company, Long trackingId) {
    VehicleTripGoodsTracking tracking = trackingLogic.getVehicleTripGoodsTrackingById(client, company, trackingId);
    TMSBill bill = tmsBillLogic.getTMSBillById(client, company, tracking.getTmsBillId());
    VehicleTrip trip = new VehicleTrip().createLocationTripInfo(bill, tracking);
    trip.setLabel(tracking.getLabel());
    trip.setCode("VT" + DateUtil.asCompactDateId(new Date()) + "/" + String.format("%04d", seqService.nextSequence(VehicleTrip.SEQUENCE)));
    return trip;
  }

  public VehicleTrip vehicleTripChangeStatus(ClientContext client, ICompany company, Long tripId, TaskStatus status) {
    VehicleTrip trip = getVehicleTrip(client, company, tripId);
    trip.setTaskStatus(status);
    if(trip.getTaskStatus().equals(TaskStatus.SUBMITTED_PLAN) || trip.getTaskStatus().equals(TaskStatus.PLAN)) {
      trip.setActualEndTime(null);
      trip.setActualStartTime(null);
    }
    if(trip.getTaskStatus().equals(TaskStatus.TRANSPORTING)) {
      trip.setActualEndTime(null);
     if(trip.getActualStartTime() == null) trip.setActualStartTime(new Date());
    }
    if(trip.getTaskStatus().equals(TaskStatus.DONE)) {
      Date dateNow = new Date();
      trip.setActualEndTime(dateNow);
      if(trip.getActualStartTime() == null) trip.setActualStartTime(dateNow);
    }
    trip.lockTrip(status);
    trip.setUpdateStatus(true);
    return saveVehicleTrip(client, company, trip);
  }

  public VehicleTrip vehicleTripTransporterChangeStatus(ClientContext client, ICompany company, ConfirmTripByTransporterParams params) {
    VehicleTrip trip = getVehicleTrip(client, company, params.getTripId());
    trip.setTransporterStatus(params.getStatus());
    trip.setRejectNote(params.getRejectNote());
    return saveVehicleTrip(client, company, trip);
  }
  //Attachments
  public List<VehicleTripAttachment> findVehicleTripAttachments(ClientContext client, ICompany company, Long vehicleTripId) {
    return attachmentRepo.findVehicleTripAttachments(company.getId(), vehicleTripId);
  }

  public List<VehicleTripAttachment> saveVehicleTripAttachments(
    ClientContext client, ICompany company, Long vehicleTripId, List<VehicleTripAttachment> attachments, boolean removeOrphan) {
    CompanyStorage storage = storageService.createCompanyStorage(client, company.getCode());
    String storagePath = VehicleTripAttachment.getVehicleTripAttachmentStoragePath(vehicleTripId);
    storage.saveAttachments(storagePath, attachments, removeOrphan);
    for (VehicleTripAttachment attachment : attachments) {
      attachment.setVehicleTripId(vehicleTripId);
      attachment.set(client, company.getId());
    }
    attachments = attachmentRepo.saveAll(attachments);
    if(removeOrphan) {
      List<Long> idSet = VehicleTripAttachment.getIds(attachments);
      if(Collections.isEmpty(idSet)) {
        attachmentRepo.deleteWithvehicleTripId(company.getId(), vehicleTripId);
      } else {
        attachmentRepo.deleteOrphan(company.getId(), vehicleTripId, idSet);
      }
    }
    return attachments;
  }

  public VehicleTrip updateRoute(ClientContext client, ICompany company, Long vehicleTripId) {
    VehicleTrip trip = getVehicleTrip(client, company, vehicleTripId);
    trip.updateRouteLabel();
    return saveVehicleTrip(client, company, trip);
  }

  //Vehicle Trip Route
  public VehicleTrip addVehicleTripRoute(ClientContext client, ICompany company, Long vehicelTripId, VehicleTripRoute tripRoute) {
    VehicleTrip trip = getVehicleTrip(client, company, vehicelTripId);
    trip.getTripRoutes().add(tripRoute);
    return saveVehicleTrip(client, company, trip);
  }

  public VehicleTripRoute saveVehicleTripRoute(ClientContext client, ICompany company, VehicleTripRoute vehicleTripRoute) {
    vehicleTripRoute.set(client, company.getId());
    return vehicleTripRouteRepo.save(vehicleTripRoute);
  }

  public List<VehicleTripRoute> saveVehicleTripRoutes(ClientContext client, ICompany company, List<VehicleTripRoute> vehicleTripRoutes) {
    for(VehicleTripRoute route : vehicleTripRoutes) {
      route = saveVehicleTripRoute(client, company, route);
    }
   return vehicleTripRoutes;
  }

  public VehicleTripRoute getVehicleTripRoute(ClientContext client, ICompany company, Long vehicleTripRouteId) {
   return vehicleTripRouteRepo.getById(vehicleTripRouteId);
  }

  public List<MapObject> saveVehicles(ClientContext client, ICompany company, List<MapObject> vehicles) {
    if (Collections.isEmpty(vehicles)) {
      return new ArrayList<>();
    }
    for (MapObject vehicleMapObject : vehicles) {
      Long id = vehicleMapObject.getLong("id", null);
      Long oldIdVehicleFleet = vehicleMapObject.getLong("oldVehicleFleetId", null);
      Long idVehicleFleet = vehicleMapObject.getLong("vehicleFleetId", null);
      Vehicle vehicle = new Vehicle();
      if (id != null) {
        vehicle = vehicleRepo.getById(client.getCompanyId(), id);
      }
      vehicle = vehicle.mergeVehicle(vehicleMapObject);
      this.saveVehicle(client, company, vehicle);

      if ((idVehicleFleet == null && oldIdVehicleFleet != null) || oldIdVehicleFleet != null ) {
        memberShipRepo.deleteByVehicleId(client.getCompanyId(), vehicle.getId(), oldIdVehicleFleet);
      }
      if (idVehicleFleet != null) {
        VehicleFleetMembership vehicleFleetMembership = memberShipRepo.getByVehicleIdAndFleetId(client.getCompanyId(), vehicle.getId(), idVehicleFleet);
        if (vehicleFleetMembership == null) {
          vehicleFleetMembership = new VehicleFleetMembership();
          vehicleFleetMembership.setVehicleId(vehicle.getId());
        }
        vehicleFleetMembership.setVehicleFleetId(idVehicleFleet);
        vehicleFleetMembership.set(client, company);
        memberShipRepo.save(vehicleFleetMembership);
      }

    }
    return vehicles;
  }
  public List<Vehicle> processVehicles(ClientContext client, ICompany company,VehicleFleet fleet, List<Vehicle> vehicles) {
    List<Vehicle> result = new ArrayList<>();
    List<Vehicle> vehiclesModified = vehicles.stream().filter(transporter -> transporter.getEditState() == EditState.MODIFIED).collect(
      Collectors.toList());
    List<Vehicle> vehiclesNew = vehicles.stream().filter(transporter -> transporter.getEditState() == EditState.NEW).collect(Collectors.toList());
    if (vehiclesModified.size() != 0 ) {
      vehiclesModified = vehicleRepo.saveAll(vehiclesModified);
      result.addAll(vehiclesModified);
    }
    if (vehiclesNew.size() != 0) {
      Collections.apply(vehicles, (vehicle)->{
        String code = TokenUtil.idWithDateTime("vehicle");
        vehicle.setCode(code);
        vehicle.set(client, company);
      });
      vehiclesNew = vehicleRepo.saveAll(vehiclesNew);
      result.addAll(vehiclesNew);
      List<VehicleFleetMembership> vehicleFleetMemberships = new ArrayList<>();
      for (Vehicle vehicle :vehiclesNew) {
        VehicleFleetMembership vehicleFleetMembership = new VehicleFleetMembership(vehicle, fleet);
        vehicleFleetMemberships.add(vehicleFleetMembership);
      }
      vehicleFleetLogic.addVehicleFleetMembershipList(client, company, vehicleFleetMemberships);
    }
    return result;
  }

  public boolean updateStatusVehicleTrips(ClientContext client, ICompany company, TaskStatus status, List<Long> ids) {
    if(ids != null && !ids.isEmpty()) {
      vehicleTripRepo.updateTaskStatusByIds(status, ids);
    }
    return true;
  }

  public List<Map<String, Object>> getDataTMSBillTripAttachment(ClientContext client, ICompany company, Long tmsBillId) {
    List<Map<String, Object>> result = new ArrayList<>();
    Set<Long> setTripIds = new HashSet<>();
    List<VehicleTripGoodsTracking> goodsTrackings = trackingLogic.findVehicleTripGoodTrackingByTMSBillId(client, company, tmsBillId);
    for(VehicleTripGoodsTracking goodsTracking: goodsTrackings) {
      Long vehicleTripId = goodsTracking.getVehicleTripId();
      setTripIds.add(vehicleTripId);
    }
    for(Long tripId : setTripIds) {
      Map<String, Object> hashMap = new HashMap<>();
      String vehicleLabel = null;
      VehicleTrip vehicleTrip = vehicleTripRepo.getById(company.getId(), tripId);
      if(vehicleTrip != null) {
        vehicleLabel = vehicleTrip.getVehicleLabel();
      }
      hashMap.put("vehicleTripId", tripId);
      hashMap.put("vehicleLabel", vehicleLabel);
      result.add(hashMap);
    }
    return result;
  }
  
  public List<SqlMapRecord> searchVehicleExpenseReport(ClientContext client, ICompany company, SqlQueryParams params) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fleet/vehicle/groovy/VehicleExpenseSql.groovy";
    params.addParam("companyId", company.getId());
    return searchDbRecords(client, scriptDir, scriptFile, "VehicleExpenseReport", params);
  }
  
  //GPS
  public String createAccessToken(ClientContext client, ICompany company, Long tokenId, String partnerName) {
    AuthorizedToken token = new AuthorizedToken();
    token.setResourceHandler("resource:gps-tracking");
    token.withAllowedResourceId("partnerName", partnerName);
    return apiAuthorizationService.createAuthorizedToken(client, company, tokenId, token);
  }
  
  private List<GPSInfo> findVehicleLocation(ClientContext client, ICompany company, List<Vehicle> vehicles) {
    RecordGroupByMap<Long, Vehicle> groupByGPSConfig = new RecordGroupByMap<>(vehicles, vehicle -> vehicle.getGpsConfigId());
    List<GPSInfo> allVehicleInfos = new ArrayList<>();
    for(Long gpsConfigId : groupByGPSConfig.getAll().keySet()) {
      if(gpsConfigId == null) continue;
      GPSConfig     config   = gpsConfigLogic.getGPSConfig(client, company, gpsConfigId);
      List<Vehicle> vehs     = groupByGPSConfig.get(gpsConfigId);
      List<String>  vehiclesPlates = Collections.transform(vehs, vehicle -> vehicle.getLicensePlate());
      List<GPSInfo> gpsInfos       = gpsPluginManager.getVehicleLocationByPlates(config, vehiclesPlates);
      allVehicleInfos.addAll(gpsInfos);
    }
    return allVehicleInfos;
  }
  
  public List<GPSInfo> findVehicleLocationByPlates(ClientContext client, ICompany company, List<String> vehiclePlates) {
    List<Vehicle> vehicles = vehicleRepo.findVehicleByLicensePlates(company.getId(), vehiclePlates);
    return findVehicleLocation(client, company, vehicles);
  }
  
  public GPSInfo getVehicleLocation(ClientContext client, ICompany company, String licensePlate) {
    Vehicle   vehicle = vehicleRepo.getVehicleByLicensePlate(company.getId(), licensePlate);
    if(vehicle.getGpsConfigId() == null) return null;
    GPSConfig  config = gpsConfigLogic.getGPSConfig(client, company, vehicle.getGpsConfigId());
    GPSInfo info = gpsPluginManager.getVehicleLocation(config, vehicle.getLicensePlate());
    return info;
  }
  
  public Notification getVehicleLocationNotification(ClientContext client, ICompany company, String licensePlate) {
    try {
      Notification notification = new Notification(Notification.Type.success, "Get Vehicle Location");
      GPSInfo info = getVehicleLocation(client, company, licensePlate);
      notification.withAttr("data", info);
      return notification;
    } catch (Exception e) {
      Notification notification = new Notification(Notification.Type.danger, "Connect GPS Fail!!!", e.getMessage());
      return notification;
    }
  }
  
  public  List<GPSInfo> reportVehicleLocation(ClientContext client, ICompany company, Long vehicleTripId) {
    VehicleTrip trip    = getVehicleTrip(client, company, vehicleTripId);
    GPSTrackingReport report = gpsTrackingLogic.getByEntityId(client, company, VehicleTrip.TABLE_NAME, vehicleTripId);
    if(report != null) {
      return report.getGpsInfos();
    } else if(TaskStatus.DONE.equals(trip.getTaskStatus())) {
      TMSBotTripTrackingEvent botEvent = new TMSBotTripTrackingEvent(client, company, trip);
      botEvent.withProcessMode(ProcessMode.Queueable);
      botService.broadcast(SourceType.UserBot, botEvent);
    }
    
    Vehicle     vehicle = vehicleRepo.getVehicleByLicensePlate(company.getId(), trip.getVehicleLabel());
    if(vehicle == null) return null;
    if(vehicle.getGpsConfigId() == null) return null;
    GPSConfig config = gpsConfigLogic.getGPSConfig(client, company, vehicle.getGpsConfigId());
    Date startTime = trip.getActualStartTime();
    Date endTime   = trip.getActualEndTime();
    List<GPSInfo>  gpsInfos = new ArrayList<>();
    if(startTime == null) {
      GPSInfo gpsInfo = gpsPluginManager.getVehicleLocation(config, vehicle.getLicensePlate());
      gpsInfos.add(gpsInfo);
    } else {
      if(endTime == null) endTime = new Date();
      gpsInfos = gpsPluginManager.reportVehicleLocations(config, vehicle.getLicensePlate(), startTime, endTime);
    }
    return gpsInfos;
  }
  
  public Notification reportVehicleLocationNotification(ClientContext client, ICompany company, Long vehicleTripId) {
    try {
      List<GPSInfo>  gpsInfos = reportVehicleLocation(client, company, vehicleTripId);
      Notification notification = new Notification(Notification.Type.success, "Report Vehicle Location");
      notification.withAttr("data", gpsInfos);
      return notification;
    } catch (Exception e) {
      Notification notification = new Notification(Notification.Type.danger, "Report Vehicle Location Fail!!!", e.getMessage());
      return notification;
    }
  }
  
  public List<Vehicle> connectGPS(ClientContext client, ICompany company, GPSConfig config) {
    List<VehicleInfo> vehicleInfos = gpsPluginManager.getAllVehiclePlate(config);
    List<Vehicle>     vehicles     = new ArrayList<>();
    for(VehicleInfo vInfo : vehicleInfos) {
      Vehicle vehicle = getVehicleByLicensePlate(client, company, vInfo.getNumberPlate());
      if(vehicle == null) {
        vehicle = new Vehicle();
        vehicle.setCode(vInfo.getFormattedNumberPlate());
        vehicle.setLabel(vInfo.getFormattedNumberPlate());
      }
      vehicle.setLicensePlate(vInfo.getNumberPlate());
      vehicle.setGpsConfigId(config.getId());
      vehicle.setGpsPluginName(config.getGpsPluginName());
      vehicle.set(client, company);;
      vehicles.add(vehicle);
    }
    return vehicleRepo.saveAll(vehicles);
  }
  
  public List<Vehicle> addGPSConfigToVehicles(ClientContext client, ICompany company, GPSConfig config, List<Long> vehicleIds) {
    List<Vehicle> vehicles = vehicleRepo.findVehicles(vehicleIds);
    for(Vehicle vehicle : vehicles) {
      vehicle.withGPSConfig(config);
      vehicle.set(client, company);
    }
    return vehicleRepo.saveAll(vehicles);
  }

  public List<VehicleTrip> findVehicleTripsByTMSBillId(ClientContext client, ICompany company, Long tmsBillId) {
    List<VehicleTripGoodsTracking> goodsTrackings = goodsTrackingRepo.findByTMSBillId(tmsBillId);
    List<VehicleTrip> vehicleTrips = new ArrayList<>();
    for(VehicleTripGoodsTracking goodsTracking: goodsTrackings) {
      VehicleTrip vehicleTrip = vehicleTripRepo.getById(company.getId(), goodsTracking.getVehicleTripId());
      if(vehicleTrip == null) {
        vehicleTrip = newVehicleTrip(client, company, goodsTracking.getId());
        saveVehicleTrip(client, company, vehicleTrip);
        goodsTracking.setVehicleTripId(vehicleTrip.getId());
        goodsTracking.set(client, company);
        goodsTrackingRepo.save(goodsTracking);
      }
      vehicleTrips.add(vehicleTrip);
    }
    return vehicleTrips;
  }

}