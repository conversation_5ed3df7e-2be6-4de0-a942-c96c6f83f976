package cloud.datatp.tms.partner;

import cloud.datatp.bfsone.BFSOneDataLogic;
import cloud.datatp.fforwarder.core.partner.CRMPartnerLogic;
import cloud.datatp.tms.partner.entity.TMSCarrier;
import cloud.datatp.tms.partner.entity.TMSPartner;
import cloud.datatp.tms.partner.model.TMSCarrierModel;
import cloud.datatp.tms.partner.repository.TMSCarrierRepository;
import cloud.datatp.tms.partner.repository.TMSPartnerRepository;
import java.util.ArrayList;
import java.util.List;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.MapObject;
import net.datatp.util.error.RuntimeError;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class TMSCarrierLogic extends DAOService {

  @Autowired
  private TMSCarrierRepository carrierRepo;

  @Autowired
  private TMSPartnerRepository partnerRepo;

  @Autowired
  private TMSPartnerLogic partnerLogic;

  @Autowired
  private BFSOneDataLogic bfsOneDataLogic;

  @Autowired
  private CRMPartnerLogic bfsOnePartnerLogic;

  public List<SqlMapRecord> searchTMSCarriers(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/tms/groovy/TMSPartnerSql.groovy";
    sqlParams.addParam("companyId", company.getId());
    List<SqlMapRecord> carriers = searchDbRecords(client, scriptDir, scriptFile, "SearchTMSCarriers", sqlParams);
    return carriers;
  }

  public TMSPartner loadTMSPartnerByCarrierId(ClientContext client, ICompany company, Long carrierId) {
    TMSCarrier carrier = carrierRepo.loadById(company.getId(), carrierId);
    TMSPartner partner = partnerRepo.getById(company.getId(), carrier.getTmsPartnerId());
    return partner;
  }

  public TMSCarrier loadTMSCarrierByPartnerId(ClientContext client, ICompany company, Long tmsPartnerId) {
    TMSCarrier carrier = carrierRepo.loadByTmsPartnerId(company.getId(), tmsPartnerId);
    return carrier;
  }

  public TMSCarrierModel loadTMSCarrierModel(ClientContext client, ICompany company, Long id) {
    TMSCarrier carrier = loadTMSCarrierById(client, company, id);
    if(carrier == null) return null;
    TMSPartner partner = partnerLogic.getTMSPartner(client, company, carrier.getTmsPartnerId());
    TMSCarrierModel model = new TMSCarrierModel();
    model.mergeTMSPartnerToModel(partner);
    return model;
  }

  public TMSCarrierModel saveTMSCarrierModel(ClientContext client, ICompany company, TMSCarrierModel model) {
    if(model.isNew()) {
      TMSPartner partner = new TMSPartner();
      partner.genCode();
      model.mergeModelToTMSPartner(partner);
      partner.set(client, company);
      partner = partnerRepo.save(partner);
      TMSCarrier carrier = new TMSCarrier(partner.getId());
      saveTMSCarrier(client, company, carrier);
      model.setId(carrier.getId());
      model.setTmsPartnerId(partner.getId());
      return model;
    }
    TMSCarrier carrier = loadTMSCarrierById(client, company, model.getId());
    if(carrier == null) {
      throw RuntimeError.EntityNotFoundError("Carrier with id = {0} is not found !!!", model.getId());
    }
    TMSPartner partner = partnerLogic.getTMSPartner(client, company, carrier.getTmsPartnerId());
    if(partner == null) {
      throw RuntimeError.EntityNotFoundError("Partner with id = {0} is not found !!!", carrier.getTmsPartnerId());
    }
    partner = model.mergeModelToTMSPartner(partner);
    partner.set(client, company);
    partnerRepo.save(partner);
    return model;
  }

  public TMSCarrier loadTMSCarrierById(ClientContext client, ICompany company, Long id) {
    TMSCarrier carrier = carrierRepo.loadById(company.getId(), id);
    return carrier;
  }

  public TMSCarrier saveTMSCarrier(ClientContext client, ICompany company, TMSCarrier carrier) {
    carrier.set(client, company);
    carrier = carrierRepo.save(carrier);
    return carrier;
  }

  public boolean changeTMSCarrierStorageState(ClientContext client, ICompany company, ChangeStorageStateRequest req) {
    carrierRepo.setStorageState(req.getNewStorageState(), req.getEntityIds());
    return true;
  }

  public boolean deleteTMSCarriers(ClientContext client, ICompany company, List<Long> ids) {
    carrierRepo.deleteAllById(ids);
    return true;
  }

  public List<TMSCarrierModel> saveTMSCarrierModels(ClientContext client, ICompany company, List<TMSCarrierModel> models) {
    for(TMSCarrierModel model: models) {
      saveTMSCarrierModel(client, company, model);
    }
    return models;
  }

  public TMSPartner saveTMSPartner(ClientContext client, ICompany company, TMSPartner tmsPartner) {
    tmsPartner.set(client, company);
    if(tmsPartner.isNew()) {
      if(tmsPartner.getCode() == null) {
        tmsPartner.genCode();
      }

      tmsPartner = partnerRepo.save(tmsPartner);
      TMSCarrier carrier = new TMSCarrier(tmsPartner.getId());
      saveTMSCarrier(client, company, carrier);
      return tmsPartner;
    }
    tmsPartner = partnerRepo.save(tmsPartner);
    return tmsPartner;
  }

  public MapObject createMapObjectPreview(ClientContext client, ICompany company, MapObject bfsOnePartner, String code) {
    MapObject result = new MapObject();
    result.add("bfsOnePartner", bfsOnePartner);

    List<TMSPartner> partners = partnerRepo.findByBFSOneCodeOrInvoiceCompanyTaxCode(company.getId(), code, code);
    List<TMSCarrierModel> carrierModels = new ArrayList<>();
    List<TMSPartner> tmsPartners = new ArrayList<>();
    for(TMSPartner partner: partners) {
      TMSCarrier carrier = carrierRepo.loadByTmsPartnerId(company.getId(), partner.getId());
      if(carrier == null) {
        tmsPartners.add(partner);
        continue;
      }
      TMSCarrierModel model = new TMSCarrierModel();
      model.mergeTMSPartnerToModel(partner);
      model.setId(carrier.getId());
      carrierModels.add(model);
    }
    result.add("tmsCarriers", carrierModels);
    result.add("tmsPartners", tmsPartners);
    return result;
  }

}