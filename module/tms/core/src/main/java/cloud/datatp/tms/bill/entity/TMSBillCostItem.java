package cloud.datatp.tms.bill.entity;

import java.io.Serial;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.accounting.settings.CashFlow;
import net.datatp.module.data.db.entity.PersistableEntity;

@Entity
@Table(
  name = TMSBillCostItem.TABLE_NAME,
  indexes = { @Index(name = TMSBillCostItem.TABLE_NAME + "_code_idx",columnList = "code") }
)
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor @Getter @Setter
public class TMSBillCostItem extends PersistableEntity<Long> {
  @Serial
  private static final long serialVersionUID = 1L;

  public static final String TABLE_NAME = "lgc_tms_bill_cost_item";
  
  public enum TMSBillCostItemType {
    GoodsInsurance, COD, ShipmentCharge, ExtraShipmentCharge, FixedCharge
  }
  
//  public enum Type { BUYING, SELLING }

  @NotNull
  @Column(length = 50)
  private String code;
  
  @Column(name="ref_code")
  private String refCode;
  
  @Column(name="ref_entity_id")
  private Long refEntityId;
  
  @Column(name="ref_entity_name")
  private String refEntityName;
  
  @NotNull
  @Column(length=1024)
  private String label;
  
  @Column(name="tms_partner_id")
  private Long tmsPartnerId;
  
  @Column(name="tms_partner_name")
  private String tmsPartnerName;
  
  private double quantity;
  
  private double price;
  
  private String unit;
  
  private double tax;
  
  @Column(name="total_tax")
  private double totalTax;
  
  private double cost;
  
  @Column(name = "total_cost")
  private double totalCost;
  
  @Column(name = "currency")
  private String currency;
  
  @Column(name = "total_domestic_cost")
  private double domesticTotalCost;
  
  @Column(name = "domestic_currency")
  private String domesticCurrency;
  
  @Column(length = 4 * 1024)
  private String description;
  
  @Enumerated(EnumType.STRING)
  private TMSBillCostItemType type;

  @Enumerated(EnumType.STRING)
  @Column(name = "cash_flow")
  private CashFlow cashFlow;
  
  private boolean paid;
  
  public TMSBillCostItem(String label, String code) {
    this.label = label;
    this.code = code;
    currency = "VND";
  }
  
  public static TMSBillCostItem createFixedPayment(String vendorFullName, Long vendorPartnerId, String truckType, double cost) {
    TMSBillCostItem fixedItem = new TMSBillCostItem("DOMESTIC TRUCKING FEE ", "B_TRUCK");
    fixedItem.setQuantity(1);
    fixedItem.setUnit(truckType);
    fixedItem.setType(TMSBillCostItemType.FixedCharge);
    fixedItem.setCashFlow(CashFlow.Outbound);
    fixedItem.setPrice(cost);
    fixedItem.setCost(cost);
    fixedItem.setTotalCost(fixedItem.getTotalTax() + fixedItem.getCost());
    fixedItem.setTmsPartnerId(vendorPartnerId);
    fixedItem.setTmsPartnerName(vendorFullName);
    return fixedItem;
  }
  
  public static TMSBillCostItem createExtraPayment(String vendorFullName, Long vendorPartnerId, String truckType, double cost) {
    TMSBillCostItem extraItem = new TMSBillCostItem("DOMESTIC TRUCKING FEE", "B_TRUCK");
    extraItem.setQuantity(1);
    extraItem.setUnit(truckType);
    extraItem.setType(TMSBillCostItemType.ExtraShipmentCharge);
    extraItem.setCashFlow(CashFlow.Outbound);
    extraItem.setPrice(cost);
    extraItem.setCost(cost);
    extraItem.setTotalCost(extraItem.getTotalTax() + extraItem.getCost());
    extraItem.setTmsPartnerId(vendorPartnerId);
    extraItem.setTmsPartnerName(vendorFullName);
    return extraItem;
  }
  
  public boolean isBuying() {
    if(CashFlow.isOutbound(cashFlow)) return true;
    return false;
  }
  
  public boolean isSelling() {
    if(CashFlow.isInbound(cashFlow)) return true;
    return false;
  }
   
  public TMSBillCostItem withPaid(boolean paid) {
    this.paid = paid;
    return this;
  }
  
  public TMSBillCostItem withDescription(String note) {
    this.description = note;
    return this;
  }
  
  public TMSBillCostItem withRefEntity(Long entityId, String entityName) {
    this.refEntityId     = entityId;
    this.refEntityName   = entityName;
    return this;
  }
}