package cloud.datatp.tms.event;

import net.datatp.module.bot.BotEvent;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.security.client.ClientContext;

import java.util.List;
import java.util.Map;

public class TMSBotCreateHouseBillFromTmsBillEvent extends BotEvent<List<Long>> {
  final static public String EVENT_NAME = "lgc:create-housebill";
      
  public TMSBotCreateHouseBillFromTmsBillEvent(ClientContext client, ICompany company, List<Long> tmsBillIds) {
    super(client, company, EVENT_NAME, tmsBillIds);
  }
}
