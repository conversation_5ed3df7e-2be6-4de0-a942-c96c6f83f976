package cloud.datatp.tms.event;

import cloud.datatp.fleet.vehicle.event.TMSBotSendZaloEvent;
import cloud.datatp.tms.TMSGeneralLogic;
import cloud.datatp.tms.housebill.TMSHouseBillService;
import net.datatp.module.bot.BotEvent;
import net.datatp.module.bot.BotEventHandler;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.monitor.SourceType;
import net.datatp.security.client.ClientContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class TMSBotCreateHouseBillFromTmsBillEventHandler extends BotEventHandler {
  public TMSBotCreateHouseBillFromTmsBillEventHandler() {
    super(TMSBotCreateHouseBillFromTmsBillEvent.EVENT_NAME);
  }

  @Autowired
  private TMSHouseBillService houseBillService;

   
  @SuppressWarnings("unchecked")
  public void handle(ClientContext client, ICompany company, SourceType sourceType, BotEvent<?> event) {
    List<Long> tmsBillIds = (List<Long>) event.getData();
    houseBillService.convertTMSBillToTMSHouseBill(client, company, tmsBillIds);
  }
}
