package cloud.datatp.tms.partner.entity;

import java.io.Serial;

import com.fasterxml.jackson.annotation.JsonInclude;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.company.entity.CompanyEntity;
import net.datatp.util.ds.MapObject;

@Entity
@Table(
  name = TMSPartnerAddress.TABLE_NAME
)
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor @Getter @Setter
public class TMSPartnerAddress extends CompanyEntity{
  public enum TMSAddressType {
    Export, Import, None
  }
  
  @Serial
  private static final long serialVersionUID = 1L;

  public static final String TABLE_NAME = "lgc_tms_partner_address";

  @Column(name = "tms_partner_id")
  private Long tmsPartnerId;
  
  @Enumerated(EnumType.STRING)
  private TMSAddressType type = TMSAddressType.None;
  
  @NotNull
  @Column(length = 1024*3)
  private String address;
  
  @Column(name = "note", length=1024*5)
  private String note;
  
  @NotNull
  @Column(name = "location_id")
  private Long   locationId;
  
  @Column(name = "location_label")
  private String locationLabel;
  
  @Column(name = "street_name")
  private String streetName;
  
  @Column(name = "inv_address")
  private String invAddress;

  @Column(name = "lat")
  private Double lat;

  @Column(name = "lng")
  private Double lng;
  
  public TMSPartnerAddress merge(MapObject ob) {
    Long   tmsPartnerId  = ob.getLong("tmsPartnerId", null);
    String typeStr       = ob.getString("type");
    String address       = ob.getString("address");
    String note          = ob.getString("note");
    Long   locationId    = ob.getLong("locationId", null);
    String locationLabel = ob.getString("locationLabel");
    String invAddress    = ob.getString("invAddress");
    String streetName    = ob.getString("streetName");
    Double lat             = ob.getDouble("lat",null);
    Double lng             = ob.getDouble("lng",null);
    
    this.tmsPartnerId = tmsPartnerId;
    if(typeStr != null) this.type = TMSAddressType.valueOf(typeStr);
    this.address = address;
    this.note    = note;
    this.locationId    = locationId;
    this.locationLabel = locationLabel;
    this.streetName    = streetName;
    this.invAddress    = invAddress;
    this.lat           = lat;
    this.lng           = lng;
    return this;
  }
}

