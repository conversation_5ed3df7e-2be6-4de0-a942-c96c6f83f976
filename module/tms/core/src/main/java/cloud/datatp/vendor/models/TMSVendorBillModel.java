package cloud.datatp.vendor.models;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

import cloud.datatp.vendor.entity.TMSVendorBill;
import cloud.datatp.vendor.entity.TMSVendorBillTracking;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.data.db.entity.EditState;
import net.datatp.util.ds.Collections;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;

@NoArgsConstructor
@Getter
@Setter
public class TMSVendorBillModel {
  private Long id;
  
  private Long tmsBillId;
  private Long responsibleAccountId;
  private String fileNo;
  
  private String mode;
  private String customerFullName;
  private String pickupAddress;
  private String deliveryAddress;
  private double quantity;
  private String quantityUnit;
  private String volumeAsText;
  private double weight;
  private String weightUnit;
  
  private double fixed;
  private double extra;
  private double cost;
  private String description;
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  private Date estimateTime;
  
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  private Date modifiedEstimateTime;
  
  private Integer delayedTime;
  
  private String containerNo = "";
  private String sealNo = "";
  
  private boolean updateContainer;
  private boolean updateTruckInfo;
  private boolean updateCosting;

  private String uikey;
  
  private List<TMSVendorBillTracking> trackings = new ArrayList<>();
  
  public List<TMSVendorBillTracking> updateVendorBillTrackings() {
    return trackings.stream().filter(sel -> EditState.MODIFIED.equals(sel.getEditState())).toList();
  }
  
  public List<TMSVendorBillTracking> deleteVendorBillTrackings() {
    return trackings.stream().filter(sel -> EditState.DELETED.equals(sel.getEditState())).toList();
  }
  
  public void update(TMSVendorBill vendorBill) {
    vendorBill.setEstimateTime(estimateTime);
    vendorBill.setModifiedEstimateTime(modifiedEstimateTime);
    vendorBill.setDelayedTime(delayedTime);
    vendorBill.setFixed(this.getFixed());
    vendorBill.setExtra(this.getExtra());
    vendorBill.setCost(this.getFixed() + this.getExtra());
    vendorBill.setDescription(this.getDescription());
    vendorBill.setContainerNo(containerNo);
    vendorBill.setSealNo(sealNo);
  }
  
  public boolean isChangeVendorBillInfo() {
    if(updateContainer || updateTruckInfo) return true;
    return false;
  }
  
  private String computeContainerNo() {
    if(StringUtil.isBlank(containerNo)) return "...";
    return containerNo;
  }
  
  private String computeSealNo() {
    if(StringUtil.isBlank(sealNo)) return "...";
    return sealNo;
  }
  
  private String buildNotification() {
    String info = "📢Cập Nhật Thông Tin: \n" + fileNo + " " + mode + " " + customerFullName;
    return info;
  }
  private String buildAddress() {
    String info = "";
    if(mode.contains("EXPORT")) {
      info += "\n Địa Chỉ: " + pickupAddress;
      info += "\n Kho      : " + deliveryAddress;
    } else if(mode.contains("IMPORT")) {
      info += "\n Địa Chỉ: " + deliveryAddress;
      info += "\n Kho      : " + pickupAddress;
    } else {
      info += "\n Địa Chỉ Lấy: " + pickupAddress;
      info += "\n Địa Chỉ Trả: " + deliveryAddress;
    }
    return info;
  }
  
  private String buildQuantityInfo() {
    String info = "\n Khối Lượng: ";
    if(quantity == 0 && weight == 0 && volumeAsText == null) return info += "..." ;
    if(quantity > 0) info += quantity + " " + quantityUnit + " ";
    if(weight > 0) info += weight + " " + weightUnit + " ";
    if(volumeAsText != null) info += volumeAsText;
    return info;
  }
  
  public String chargeInfo(TMSVendorBill vendorBill) {
    String info = buildNotification();
    info += buildAddress();
    info += buildQuantityInfo();
    if(updateContainer) {
      vendorBill.setModifiedContainerNo(new Date());
      vendorBill.setUpdateContainerNo(updateContainer);
      info += "\n📦 Cập Nhật Cont/Seal : " + computeContainerNo() + " - " + computeSealNo() + 
              "(" + vendorBill.computeContainerNo() + " - " + vendorBill.computeSealNo() + ")" ;
    }
    if(updateTruckInfo) {
//      vendorBill.setUpdateTruckInfo(updateTruckInfo);
      info += "\n🚚 Thông Tin Xe : ";
      info += "\n-Cont/Seal : " + computeContainerNo() + " - " + computeSealNo();
      for(TMSVendorBillTracking tracking : trackings) {
        String truckInfo = tracking.buildTruckInfo();
        if(StringUtil.isNotBlank(truckInfo)) info += "\n" + truckInfo;
      }
      if(Collections.isNotEmpty(vendorBill.getBillTrackings())) {
        info += "\n(Thông Tin Cũ : ";
        for(TMSVendorBillTracking tracking: vendorBill.getBillTrackings()) {
          String truckInfo = tracking.buildTruckInfo();
          if(StringUtil.isNotBlank(truckInfo)) info += "\n" + truckInfo;
        }
        info += " ...)";
      }
    }
    return info;
  }
  
  public String updateCosting(TMSVendorBill vendorBill) {
    String info = buildNotification();
    DecimalFormat formatter = new DecimalFormat("#,###");
    if(updateCosting) {
      vendorBill.setModifiedCosting(new Date());
      vendorBill.setUpdateCosting(updateCosting);
      info += "\n💲 Cập Nhật Cước: ";
      info += "\n Cước: " + formatter.format(fixed) + " đ" ;
      info += "\n Phát Sinh: " + formatter.format(extra) + " đ";
      info += "\n Tổng: " + formatter.format(fixed + extra) + " đ";
    }
    return info;
  }
}
