package cloud.datatp.tms.bill.entity;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.core.security.AuthorizationCipherTool;
import net.datatp.module.core.security.SessionData;
import net.datatp.module.storage.entity.CompanyEntityAttachment;
import net.datatp.module.storage.entity.EntityAttachment;

@Entity
@Table(
  name = TMSBillAttachment.TABLE_NAME,
  indexes = {
    @Index(name = TMSBillAttachment.TABLE_NAME + "_tms_bill_id_idx", columnList = "tms_bill_id"),
  }
)
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor @Getter @Setter
public class TMSBillAttachment extends CompanyEntityAttachment {
  private static final long serialVersionUID = 1L;
  final static public String TABLE_NAME = "lgc_tms_bill_attachment";
  
  @NotNull
  @Column(name="tms_bill_id")
  private Long tmsBillId;
  
  @Transient
  private String fileNo;
  
  @Column(name="entity_id")
  private Long entityId;
  
  @Column(name="entity_name")
  private String entityName;
  
  public TMSBillAttachment(String name, String label, String description) {
    super(name, label, description);
  }
  
  public static String getTMSBillAttachmentStoragePath(Long tmsBillId) {
    return "apps/logistics/tms/bill/" + tmsBillId.toString();
  }
  
  public static <T extends EntityAttachment> void setStoreInfo(
      AuthorizationCipherTool cipherTool, String sessionId, List<T> atts)  {
      for (T attachment : atts) {
        SessionData sessionData = new SessionData(sessionId, attachment.getResourceUri());
        attachment.setStoreInfo(cipherTool.encryptSessionData(sessionData));
      }
    }
}
