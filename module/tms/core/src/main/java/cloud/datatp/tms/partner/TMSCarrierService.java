package cloud.datatp.tms.partner;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cloud.datatp.tms.partner.entity.TMSCarrier;
import cloud.datatp.tms.partner.entity.TMSPartner;
import cloud.datatp.tms.partner.model.TMSCarrierModel;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.service.BaseComponent;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.MapObject;

@Service("TMSCarrierService")
public class TMSCarrierService extends BaseComponent {

  @Autowired
  private TMSCarrierLogic carrierLogic;

  @Transactional(readOnly=true)
  public List<SqlMapRecord> searchTMSCarriers(ClientContext client, ICompany company, SqlQueryParams params) {
    return carrierLogic.searchTMSCarriers(client, company, params);
  }

  @Transactional(readOnly=true)
  public TMSCarrier loadTMSCarrierById(ClientContext client, ICompany company, Long id) {
    return carrierLogic.loadTMSCarrierById(client, company, id);
  }

  @Transactional(readOnly=true)
  public TMSPartner loadTMSPartnerByCarrierId(ClientContext client, ICompany company, Long carrierId) {
    return carrierLogic.loadTMSPartnerByCarrierId(client, company, carrierId);
  }

  @Transactional(readOnly=true)
  public TMSCarrier loadTMSCarrierByPartnerId(ClientContext client, ICompany company, Long tmsPartnerId) {
    return carrierLogic.loadTMSCarrierByPartnerId(client, company, tmsPartnerId);
  }

  @Transactional(readOnly=true)
  public TMSCarrierModel loadTMSCarrierModel(ClientContext client, ICompany company, Long id) {
    return carrierLogic.loadTMSCarrierModel(client, company, id);
  }

  @Transactional
  public TMSCarrierModel saveTMSCarrierModel(ClientContext client, ICompany company, TMSCarrierModel model) {
    return carrierLogic.saveTMSCarrierModel(client, company, model);
  }

  @Transactional
  public TMSCarrier saveTMSCarrier(ClientContext client, ICompany company, TMSCarrier carrier) {
    return carrierLogic.saveTMSCarrier(client, company, carrier);
  }

  @Transactional
  public List<TMSCarrierModel> saveTMSCarrierModels(ClientContext client, ICompany company, List<TMSCarrierModel> models) {
    return carrierLogic.saveTMSCarrierModels(client, company, models);
  }

  @Transactional
  public boolean changeTMSCarrierStorageState(ClientContext client, ICompany company, ChangeStorageStateRequest req) {
    return carrierLogic.changeTMSCarrierStorageState(client, company, req);
  }

  @Transactional
  public boolean deleteTMSCarriers(ClientContext client, ICompany company, List<Long> ids) {
    return carrierLogic.deleteTMSCarriers(client, company, ids);
  }

  @Transactional
  public TMSPartner saveTMSPartner(ClientContext client, ICompany company, TMSPartner tmsPartner) {
    return carrierLogic.saveTMSPartner(client, company, tmsPartner);
  }

  @Transactional
  public MapObject createMapObjectPreview(ClientContext client, ICompany company, MapObject bfsOnePartner, String code) {
    return carrierLogic.createMapObjectPreview(client, company, bfsOnePartner, code);
  }
}
