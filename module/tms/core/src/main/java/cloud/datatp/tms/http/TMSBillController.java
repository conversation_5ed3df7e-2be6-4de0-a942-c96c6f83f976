package cloud.datatp.tms.http;

import java.util.List;
import java.util.concurrent.Callable;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import cloud.datatp.tms.TMSPrintService;
import cloud.datatp.tms.bill.TMSBillService;
import cloud.datatp.tms.bill.entity.TMSBill;
import cloud.datatp.tms.bill.entity.TMSBillActivity;
import cloud.datatp.tms.bill.entity.TMSBillAttachment;
import cloud.datatp.tms.bill.models.SubcontractorReportingParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jakarta.servlet.http.HttpServletRequest;
import net.datatp.module.backend.BackendResponse;
import net.datatp.module.company.http.BaseCompanyController;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.http.get.StoreInfo;
import net.datatp.module.session.HttpClientSessionService;
import net.datatp.security.client.ClientContext;

@ConditionalOnBean(HttpClientSessionService.class)
@Api(value = "openfreightone", tags = { "logistics/tms" })
@RestController
@RequestMapping("/rest/v1.0.0/logistics/tms")
public class TMSBillController extends BaseCompanyController {
  @Autowired
  private TMSBillService tmsBillService;
  
  @Autowired
  private TMSPrintService tmsPrintService;
  
  protected TMSBillController() {
    super("tms", "/logistics/tms");
  }
  
  // TMSBill

  @ApiOperation(value = "Save bill", response = TMSBill.class)
  @PutMapping("bill")
  public @ResponseBody BackendResponse insertTMSBill(HttpServletRequest httpReq, @RequestBody TMSBill TMSBill) {
    Callable<TMSBill> executor = () -> {
      ClientContext clientCtx = getAuthorizedClientContext(httpReq);
      return tmsBillService.saveBill(clientCtx, clientCtx.getCompany(), TMSBill);
    };
    return execute(Method.PUT, "bill", executor);
  }

  @ApiOperation(value = "Change TMS bills storage state", response = TMSBill.class)
  @PutMapping("bill/storage-state")
  public @ResponseBody BackendResponse changeTMSBillStorageState(HttpServletRequest httpReq, @RequestBody ChangeStorageStateRequest req) {
    Callable<Boolean> executor = () -> {
      ClientContext clientCtx = getAuthorizedClientContext(httpReq);
      return tmsBillService.changeTMSBillStorageState(clientCtx, clientCtx.getCompany(), req);
    };
    return execute(Method.PUT, "bill/storage-state", executor);
  }
  // TMSBill Activity
  @ApiOperation(value = "Save activity", response = TMSBillActivity.class)
  @PutMapping("bill-activity")
  public @ResponseBody BackendResponse saveTMSBillActivity(HttpServletRequest httpReq, @RequestBody TMSBillActivity pa) {
    Callable<TMSBillActivity> executor = () -> {
      ClientContext clientCtx = getAuthorizedClientContext(httpReq);
      return tmsBillService.saveTMSBillActivity(clientCtx, clientCtx.getCompany(), pa);
    };
    return execute(Method.PUT, "bill-activity", executor);
  }

  // PRINT
  
  @ApiOperation(value = "Create Receipt Of Delivery Print", response = StoreInfo.class)
  @PostMapping("bill/print/receipt-of-delivery/{format}")
  public @ResponseBody BackendResponse createReceiptOfDeliveryPrint(
      HttpServletRequest httpReq, @PathVariable("format") String format, @RequestBody Long [] ids) {
    Callable<StoreInfo> executor = () -> {
      ClientContext clientCtx = getAuthorizedClientContext(httpReq);
      return tmsPrintService.createReceiptOfDeliveryPrint(clientCtx, clientCtx.getCompany(), format, ids);
    };
    return execute(Method.POST, "bill/print/receipt-of-delivery/{format}", executor);
  }
  
  @ApiOperation(value = "Create Subcontractor Reporting Print", response = StoreInfo.class)
  @PostMapping("bill/print/subcontractor-reporting/{format}")
  public @ResponseBody BackendResponse createSubcontractorReportingPrint(
      HttpServletRequest httpReq, @PathVariable("format") String format, @RequestBody SubcontractorReportingParams params) {
    Callable<StoreInfo> executor = () -> {
      ClientContext clientCtx = getAuthorizedClientContext(httpReq);
      return tmsPrintService.createSubcontractorReportingPrint(clientCtx, clientCtx.getCompany(), format, params);
    };
    return execute(Method.POST, "bill/print/subcontractor-reporting/{format}", executor);
  }
  
  //Attachments
  @ApiOperation(value = "Find tms bill attachments by tms bill id", responseContainer = "List", response = TMSBillAttachment.class)
  @GetMapping("bill/{id}/attachments")
  public @ResponseBody BackendResponse findTMSBillAttachment( HttpServletRequest httpReq, @PathVariable("id") Long id) {
    Callable<List<TMSBillAttachment>> executor = () -> {
      ClientContext clientCtx = getAuthorizedClientContext(httpReq);
      return tmsBillService.findTMSBillAttachments(clientCtx, clientCtx.getCompany(), id);
    };
    return execute(Method.GET, "bill/{id}/attachments", executor);
  }
  
  @ApiOperation(value = "Save tms bill attachments", responseContainer = "List", response = TMSBillAttachment.class)
  @PutMapping("bill/{id}/{entityId}/{entityName}/attachments")
  public @ResponseBody BackendResponse saveTMSBillAttachment(
    HttpServletRequest httpReq, @PathVariable("id") Long tmsBillId, @PathVariable("entityId") Long entityId, @PathVariable("entityName") String entityName, @RequestBody List<TMSBillAttachment> attachments) {
    Callable<List<TMSBillAttachment>> executor = () -> {
      ClientContext clientCtx = getAuthorizedClientContext(httpReq);
      for(TMSBillAttachment attachment: attachments) {
        attachment.setEntityId(entityId);
        attachment.setEntityName(entityName);
      }
      return tmsBillService.saveTMSBillAttachments(clientCtx, clientCtx.getCompany(), tmsBillId, attachments);
    };
    return execute(Method.PUT, "bill/{id}/attachments", executor);
  }

  @ApiOperation(value = "Save tms bill attachments", responseContainer = "List", response = TMSBillAttachment.class)
  @PutMapping("bill/{id}/attachments")
  public @ResponseBody BackendResponse saveTMSBillAttachment(
    HttpServletRequest httpReq, @PathVariable("id") Long tmsBillId, @RequestBody List<TMSBillAttachment> attachments) {
    Callable<List<TMSBillAttachment>> executor = () -> {
      ClientContext clientCtx = getAuthorizedClientContext(httpReq);
      return tmsBillService.saveTMSBillAttachments(clientCtx, clientCtx.getCompany(), tmsBillId, attachments);
    };
    return execute(Method.PUT, "bill/{id}/attachments", executor);
  }
}