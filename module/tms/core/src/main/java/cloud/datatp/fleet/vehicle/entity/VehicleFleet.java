package cloud.datatp.fleet.vehicle.entity;

import java.io.Serial;
import java.util.HashSet;
import java.util.Set;

import cloud.datatp.tms.partner.entity.TMSWebhookConfig;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.account.entity.Account;
import net.datatp.module.company.entity.CompanyEntity;

@Entity
@Table(
  name = VehicleFleet.TABLE_NAME,
  uniqueConstraints = {
    @UniqueConstraint(
      name = VehicleFleet.TABLE_NAME + "_code",
      columnNames = {"company_id", "code"}),
  },
  indexes = { @Index(columnList = "code")}
)
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor
@Getter @Setter
public class VehicleFleet extends CompanyEntity {
  @Serial
  private static final long serialVersionUID = 1L;

  public static final String TABLE_NAME = "lgc_fleet_vehicle_fleet";
  public enum FleetType { Truck, Motorbike }
  public enum FleetResource { EXTERNAL_COMPANY, INTERNAL_COMPANY }
  
  @NotNull
  private String    code;
  
  @Column(length=50, name="bfs_one_code")
  private String bfsOneCode;

  @Column(name = "tax_code")
  private String taxCode;
  
  private String    label;
  private String    description;

  @Column(name = "localized_label")
  private String localizedLabel;
  
  @Column(name = "region_of_responsibility_code")
  private String regionOfResponsibilityCode;

  @Enumerated(EnumType.STRING)
  @Column(name="fleet_type")
  private FleetType fleetType = FleetType.Truck;
  
  @Enumerated(EnumType.STRING)
  @Column(name="fleet_resource")
  private FleetResource fleetResource;
  
  @Column(name = "owner_account_id")
  private Long ownerAccountId;
  
  @Column(name = "owner_full_name")
  private String ownerFullName;
  
  private Integer index;
  
  private Boolean gps;
  
  @Column(name = "container_shipping_support")
  private Boolean containerShippingSupport;
  
  @Column(name = "truck_shipping_support")
  private Boolean truckShippingSupport;

  @Column(name = "allow_request_tracking_app")
  private Boolean allowRequestTrackingApp;

  @Embedded
  private TMSWebhookConfig webhookConfig;

  @Column(length=1024 * 2)
  @Convert(converter = StringSetConverter.class)
  private Set<String> emails = new HashSet<>();
  
  public VehicleFleet withOwner(Account owner) {
    this.ownerAccountId = owner.getId();
    this.ownerFullName  = owner.getFullName();
    return this;
  }
}