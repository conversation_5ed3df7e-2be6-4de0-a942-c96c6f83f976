package cloud.datatp.tms.partner;

import java.util.List;

import cloud.datatp.tms.TMSGeneralLogic;
import net.datatp.module.resource.location.entity.Location;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cloud.datatp.tms.partner.entity.TMSCustomerMembership;
import cloud.datatp.tms.partner.entity.TMSPartner;
import cloud.datatp.tms.partner.entity.TMSPartnerAddress;
import lombok.Getter;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.service.BaseComponent;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.MapObject;

@Service("TMSPartnerService")
public class TMSPartnerService extends BaseComponent {
  @Autowired
  @Getter
  private TMSPartnerLogic        partnerLogic;

  @Autowired
  private TMSGeneralLogic generalLogic;
  
  @Autowired
  private TMSPartnerAddressLogic addressLogic;
 
  // TMS customer
  @Transactional(readOnly=true)
  public TMSPartner getTMSPartnerByCode(ClientContext client, ICompany company, String code) {
    return partnerLogic.getTMSPartnerByCode(client, company, code);
  }

  @Transactional(readOnly=true)
  public TMSPartner getTMSPartnerById(ClientContext client, ICompany company, Long id) {
    return partnerLogic.getTMSPartner(client, company, id);
  }
  
  @Transactional(readOnly=true)
  public List<TMSPartner> findByBFSOneCode(ClientContext client, ICompany company, String bfsOneCode) {
    return partnerLogic.findByBFSOneCode(client, company, bfsOneCode);
  }
  
  @Transactional(readOnly=true)
  public MapObject findBFSOnePartnerAndTMSPartner(ClientContext client, ICompany company, String bfsOneCode) {
    return partnerLogic.findBFSOnePartnerAndTMSPartner(client, company, bfsOneCode);
  }

  @Transactional
  public TMSPartner saveTMSPartner(ClientContext client, ICompany company, TMSPartner partner) {
    return partnerLogic.saveTMSPartner(client, company, partner);
  }

  @Transactional
  public List<TMSPartner> saveTMSPartners(ClientContext client, ICompany company, List<TMSPartner> partners) {
    return partnerLogic.saveTMSPartners(client, company, partners);
  }
  
  @Transactional
  public boolean deleteTMSPartner(ClientContext client, ICompany company, List<Long> ids) {
    return partnerLogic.deleteTMSPartner(client, company, ids);
  }

  @Transactional
  public boolean changeTMSPartnerStorageState(ClientContext client, ChangeStorageStateRequest req) {
    return partnerLogic.changeTMSPartnerStorageState(client, req);
  }
  
  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchTMSPartners(ClientContext client, ICompany company, SqlQueryParams params) {
    return partnerLogic.searchTMSPartners(client, company, params);
  }

  @Transactional
  public boolean addFollowerPartner(ClientContext client, ICompany company, Long customerId) {
    return partnerLogic.addFollowerPartner(client, company, customerId);
  }
  
  @Transactional
  public boolean updateBFSOneCode(ClientContext client, ICompany company, Long customerId, Long parentPartnerId, String bfsOneCode) {
    return partnerLogic.updateBFSOneCode(client, company, customerId, parentPartnerId, bfsOneCode);
  }

  @Transactional
  public TMSPartner mergePartners(ClientContext client, ICompany company, Long targetCustomerId, List<Long> ids, TMSPartner.TMSCustomerGroup type) {
    return partnerLogic.mergePartners(client, company, targetCustomerId, ids, type);
  }
  
  @Transactional
  public List<TMSPartner> syncPartnerPermissionSaleman(ClientContext client, ICompany company, List<Long> partnerIds) {
    return partnerLogic.syncPartnerPermissionSaleman(client, company, partnerIds);
  }
  
  // TMSCustomerMembership
  @Transactional
  public TMSCustomerMembership addCustomerMembership(ClientContext client, ICompany company, TMSCustomerMembership membership) {
    return partnerLogic.addCustomerMembership(client, company, membership);
  }
  
  @Transactional
  public boolean removeCustomerMemberships(ClientContext client, ICompany company, List<TMSCustomerMembership> memberships) {
    return partnerLogic.removeCustomerMemberships(client, company, memberships);
  }
  
  // Job Tracking Rule
  @Transactional
  public TMSPartner createTMSCustomerRuleFromDefault(ClientContext client, ICompany company, Long customerId) {
    return partnerLogic.createTMSCustomerRuleFromDefault(client, company, customerId);
  }
  
  @Transactional
  public TMSPartner updateTMSCustomerJobTrackingRuleRef(ClientContext client, ICompany company, Long customerId, Long jobTrackingProjectRuleId) {
    return partnerLogic.updateTMSCustomerJobTrackingRuleRef(client, company, customerId, jobTrackingProjectRuleId);
  }
  
  
  //Address
  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchPartnerAddresses(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    return addressLogic.searchPartnerAddresses(client, company, sqlParams);
  }
  
  @Transactional(readOnly = true)
  public TMSPartnerAddress loadPartnerAddress(ClientContext client, ICompany company, Long id) {
    return addressLogic.loadById(client, company, id);
  }

  @Transactional(readOnly = true)
  public Location getNewSubdistrictLocationByOldAddress(ClientContext client, String address) {
    return generalLogic.getNewSubdistrictLocationByOldAddress(client, address);
  }

  public TMSPartnerAddress createPartnerAddress(ClientContext client, String address) {
    return generalLogic.createPartnerAddress(client, address);
  }
  
  @Transactional
  public TMSPartnerAddress savePartnerAddress (ClientContext client, ICompany company, TMSPartnerAddress address) {
    return addressLogic.saveTMSPartnerAddress(client, company, address);
  }
  
  @Transactional
  public List<TMSPartnerAddress> bulkSaveTMSPartnerAddress (ClientContext client, ICompany company, List<MapObject> records) {
    return addressLogic.bulkSaveTMSPartnerAddresses(client, company, records);
  }
  
  @Transactional
  public boolean deletePartnerAddresses (ClientContext client, ICompany company, List<Long> ids) {
    return addressLogic.deleteByIds(client, company, ids);
  }
}
