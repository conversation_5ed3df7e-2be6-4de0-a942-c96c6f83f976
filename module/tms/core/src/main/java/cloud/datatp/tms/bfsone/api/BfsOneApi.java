package cloud.datatp.tms.bfsone.api;

import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.httpclient.HttpClient;
import net.datatp.util.dataformat.DataSerializer;
import net.datatp.util.ds.MapObject;
import net.datatp.util.error.RuntimeError;

@Slf4j
public class BfsOneApi {
  
  /*
      curl -X POST \
      https://api.beelogistics.com/ofone/tms/updatetrucktracking\
      -H "Content-Type: application/json" \
      -H "AccessCode: Tbv7XM77TF5eGrksgI9LOGpZai8SwZq3cHxBUVQTGrGK/HZphb3V97W+EAe7ZJMeCPIwFS796ZxTy7q6hqPm7e2qYF+1/0sGzMz8c2/le5BZQ+YTvGYdKE+ByaL9wOjJNlJbiC9EgjSc+f72sykS0MR0hAdTSeHzaMdfUY/8r8bZMyyWniwnNm/iK3chvIUejoq3P08ZXW3r7HfOk15oFsVcJVdx429c2aGxUpgZluSVK7cThoKq+Qw0/b7SiyLsPwuYmglqzBVytCCfdFlEimQsh4eZBOJsh7k8qbePh9NjtxvkYj429TiVbzxPiZ/UJJMlKogTiTtDS6AwUAyJNg==" \
      -d '[ {
    "HWBNO" : "SDZIK2507001",
    "TruckingFrom" : "VIP GREENPORT, Phường Đông Hải, Thành phố Hải Phòng",
    "TruckingTo" : "Phường Thượng Hồng, Tỉnh Hưng Yên",
    "TruckNo" : "15H01787",
    "ContNo" : "BHCU3160264",
    "ContType" : "20´DC",
    "DeliveryDate" : "11/08/2025",
    "Creator" : "Melinda.vnhph"
    } ]';
*/
  
  
    @Getter
    private HttpClient   client;
    private final String URL                           = "https://api.beelogistics.com/ofone";
    private final String ACCESS_CODE                   = "jWQ953gZwSg6FLfnfCtJmdAxZJcYEABSQX8HOjAguSgmLHtFxThGi6ZDuBmoo1hNNI3W3r6FGPDYuGacRxYOIxvQUfoTOt4LL6rzcrIBwyTBa77Nvigxwotj8Ay97iqyiQOZ51zFhvBpWmXTG6/l/gNW+LmQ8WmX6RRzLOzbRmD5RBRdl5HwE8O5EHwjaeHMSPHG+BO+L3zHO69XWN/b3TqO+M2xdsPF32/FlwYDNKxNjDA4uJIPSySnwXw2iph0Zf7t+Ws6oT5mRYvF30r1bRYuOvKOMAQBgPiJ9PO5JkPUwsOKF2BeNBG0Y84fH6DL3fxqhcVl6VmHeVmab+wE+g==";
    public BfsOneApi() {
      this.client = new HttpClient(URL);
    }
    
    private HttpHeaders createHeaders(String accessToken) {
      HttpHeaders headers = client.createHeaders();
      headers.add("AccessCode", accessToken);
      return headers;
    }
    
    public MapObject serverPost(String pathApi, String accessToken, Object body) {
      String reqBodyAsJson = DataSerializer.JSON.toString(body);
      ResponseEntity<String> response = client.post(pathApi, createHeaders(accessToken), reqBodyAsJson, String.class);
      try {
        MapObject mapObject = DataSerializer.JSON.fromString(response.getBody(), MapObject.class);
        return mapObject;
      } catch (Exception e) {
        log.error(e.getMessage());
        throw RuntimeError.UnknownError(e.getMessage());
      }
    }
    
    public String getAuthorizeToken(String userid, String username) {
      log.info("BFSOneApi getAuthorizeToken: {} {}", userid, username);
      MapObject body = new MapObject();
      body.put("userid", userid);
      body.put("username", username);
      MapObject result = serverPost("/authorize/gettoken", ACCESS_CODE, body);
      MapObject data   = result.getMapObject("Data");
      if(data == null) throw RuntimeError.UnknownError(result.getString("ErrorMessage"));
      String token = data.getString("tokenid");
      return token;
    }
    
    public MapObject updateTruckTracking(String token, Object body) {
      return serverPost("/tms/updatetrucktracking", token, body);
    }
    
    public MapObject updateTruckCosting(String token, Object body) {
      return serverPost("/tms/updatetruckcosting ", token, body);
    }
}
