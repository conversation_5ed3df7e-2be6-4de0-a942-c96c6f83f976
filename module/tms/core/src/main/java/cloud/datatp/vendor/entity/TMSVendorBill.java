package cloud.datatp.vendor.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import cloud.datatp.fleet.vehicle.entity.VehicleTrip;
import cloud.datatp.fleet.vehicle.entity.VehicleTripGoodsTracking;
import cloud.datatp.fleet.vehicle.entity.VehicleTripGoodsTracking.VehicleTripGoodsTrackingStatus;
import cloud.datatp.tms.bill.entity.TMSBill;
import cloud.datatp.tms.bill.entity.TMSBillCustomer;
import cloud.datatp.tms.bill.entity.TMSBillForwarderTransport;
import cloud.datatp.tms.bill.entity.TMSBillGoods;
import cloud.datatp.tms.bill.entity.TMSBillReceiver;
import cloud.datatp.tms.bill.entity.TMSBillSender;
import cloud.datatp.tms.ops.entity.TMSOperations;
import cloud.datatp.tms.ops.entity.TMSOperations.TMSOperationsStatus;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Index;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.company.entity.CompanyEntity;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.input.InputProcessStatus;
import net.datatp.security.client.ClientContext;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;
@Entity
@Table(
  name = TMSVendorBill.TABLE_NAME,
  uniqueConstraints = { @UniqueConstraint( name = TMSVendorBill.TABLE_NAME + "_tms_bill_id", columnNames = {"company_id", "tms_bill_id"}) },
  indexes = {
    @Index(name = TMSVendorBill.TABLE_NAME + "_tms_bill_id_idx", columnList = "tms_bill_id")
  }
)
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor @Getter @Setter
public class TMSVendorBill extends CompanyEntity {
  private static final long serialVersionUID = 1L;
  final static public String TABLE_NAME = "lgc_tms_vendor_bill";
  final static public String PROJECT    = "vendor-bill-reconcilation";
  
  public enum TMSVendorAttachStatus {
    SUCCESS, FAIL
  }

  public enum VendorCostStatus {
    NEED_CONFIRM , MANUAL_CONFIRM, AUTO_CONFIRM;
  }
  
  public enum VendorXlsxStatus {
    NOT_MATCH , WARNING, MATCH;
  }

  @Column(name = "tms_bill_id")
  private Long tmsBillId; 
  
  @Column(name = "vehicle_goods_tracking_id")
  private Long vehicleGoodsTrackingId; 
  
//  @Enumerated(EnumType.STRING)
//  private String mode;
  
  @Column(name = "task_id")
  private Long taskId;
  
  @Column(name = "last_task_commenter_account_id")
  private Long   lastTaskCommenterAccountId;
  
  @Column(name = "last_task_commenter_full_name")
  private String lastTaskCommenterFullName;
  
  @NotNull
  @Column(name = "file_no")
  private String fileNo;
  
  @NotNull
  private String code;

  @Column(name = "customer_full_name")
  private String customerFullName;

  @Column(name = "customer_id")
  private Long customerId;
  
  @Column(length = 16*1024)
  private String warehouse;

  @Column(name = "pickup_address", length = 16*1024)
  private String pickupAddress;

  @Column(name = "delivery_address", length = 16*1024)
  private String deliveryAddress;

  @Column(name="responsible_account_id")
  private Long responsibleAccountId;

  @Column(name="responsible_full_name")
  private String responsibleFullName;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "delivery_plan")
  private Date deliveryPlan;
 
//  private String time;
  @Column(name = "tms_bill_last_version")
  private Long tmsBillLastVersion;
  
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "estimate_time")
  private Date estimateTime;
  
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name="modified_estimate_time")
  private Date modifiedEstimateTime;
  
  @Column(name="delayed_time")
  private Integer delayedTime;
  
  @Column(name = "vendor_full_name")
  private String vendorFullName;
  
  private double quantity;
  
  @Column(name = "quantity_unit")
  private String quantityUnit;
  
  private double weight;
  
  private double volume;
  
  @Column(name = "volume_note")
  private String volumeNote;
  

//  @Column(name = "truck_no")
//  private String truckNo;
  
  //remove
//  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
//  @Column(name="modified_truck_no")
//  private Date modifiedTruckNo;
//  
//  @Column(name = "modified_truck_no_by")
//  private String modifiedTruckNoBy;
  //
  
  //rename
  @Column(name = "vendor_id")
  private Long vendorId;
  
  @Column(name = "container_no")
  private String containerNo;
  
  @Column(name = "seal_no")
  private String sealNo;
  
  @Column(name = "booking_code")
  private String bookingCode;
  
  private double fixed;
  private double extra;
  private double cost;

  @Enumerated(EnumType.STRING)
  private VehicleTripGoodsTrackingStatus status = VehicleTripGoodsTrackingStatus.NEED_CONFIRM;
  private String issue;

  @Column(length = 4 * 1024)
  private String description;
  
  @Enumerated(EnumType.STRING)
  @Column(name = "attach_status")
  private TMSVendorAttachStatus attachStatus;
  
  //OPS INFO
  @Column(name = "ops_account_id")
  private Long opsAccountId;

  @Column(name = "ops_account_full_name", length = 1024)
  private String opsAccountFullName;

  @Column(name = "ops_mobile")
  private String opsMobile;

  @Column(name = "ops_identification_no")
  private String opsIdentificationNo;

  @Column(name = "ops_note", length = 4 * 1024)
  private String opsNote;

  @Enumerated(EnumType.STRING)
  @Column(name = "ops_status")
  private TMSOperationsStatus opsStatus;
  
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "xlsx_last_update")
  private Date xlsxLastUpdate;
  
  @Column(name = "send_ops")
  private boolean sendOps;
  
  @Column(name = "send_vendor")
  private boolean sendVendor;

  @Column(name = "account_id_confirmed")
  private Long accountIdConfirmed;

  @Column(name = "account_full_name_confirmed")
  private String accountFullNameConfirmed;
  
  @Enumerated(EnumType.STRING)
  @Column(name = "xlsx_status")
  private VendorXlsxStatus xlsxStatus;
  
  @Enumerated(EnumType.STRING)
  @Column(name = "vendor_cost_status")
  private VendorCostStatus vendorCostStatus;
  
  @Column(name = "vendor_authorization", length = 4 * 1024)
  private String vendorAuthorization;
  
  @Column(name = "push_status")
  private InputProcessStatus pushStatus;

  @Column(name = "feedback", length = 4 * 1024)
  private String feedback;
  
  @Column(name = "upload_error", length = 4 * 1024)
  private String uploadError;
  
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name="modified_container_no")
  private Date modifiedContainerNo;
  
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name="modified_costing")
  private Date modifiedCosting;
  
  @Column(name = "update_container_no")
  private boolean updateContainerNo;
  
  @Column(name = "update_truck_info")
  private boolean updateTruckInfo;
  
  @Column(name = "update_costing")
  private boolean updateCosting;
  
  //TRACKING INFO
  @OneToMany(cascade = CascadeType.ALL)
  @JoinColumn(name = "vendor_bill_id", referencedColumnName = "id")
  private List<TMSVendorBillTracking> billTrackings = new ArrayList<>();
  
  public String computeContainerNo() {
    if(StringUtil.isBlank(containerNo)) return "...";
    return containerNo;
  }
  
  public String computeSealNo() {
    if(StringUtil.isBlank(sealNo)) return "...";
    return sealNo;
  }
  
  public TMSVendorBill updateByTMSBill(TMSBill bill) {
      TMSBillCustomer customer = bill.getCustomer();
      TMSBillForwarderTransport forwarderTransport = bill.getTmsBillForwarderTransport();
      TMSBillSender sender = bill.getSender();
      TMSBillReceiver receiver = bill.getReceiver();
      
      this.setFileNo(bill.getLabel());
      if(customer != null) {
        this.setCustomerId(customer.getCustomerId());
        this.setCustomerFullName(customer.getCustomerFullName());
      }
      Date deliveryPlan = bill.getDeliveryTime();
      if(deliveryPlan != null) {
        this.setDeliveryPlan(mergeTimeToDateTime(deliveryPlan, bill.getTime()));
      } else {
        this.setDeliveryPlan(null);
      }

      this.setTmsBillId(bill.getId());
      this.setTmsBillLastVersion(bill.getVersion());
      this.setCode(bill.getCode());
      if(sender != null) this.setPickupAddress(sender.getSenderAddress());
      if(receiver != null) this.setDeliveryAddress(receiver.getReceiverAddress());
      
      if(forwarderTransport != null) {
        this.setContainerNo(forwarderTransport.getContainerNo());
        this.setSealNo(forwarderTransport.getSealNo());
        this.setBookingCode(forwarderTransport.getBookingCode());
        this.setVendorId(forwarderTransport.getVendorId());
        this.setVendorFullName(forwarderTransport.getVendorFullName());
//        this.setMode(forwarderTransport.getMode());
        if(forwarderTransport.isImport()) {
          this.setPickupAddress(forwarderTransport.getWarehouseLabel());
        }

        if(forwarderTransport.isExport()) {
          this.setDeliveryAddress(forwarderTransport.getWarehouseLabel());
        }
        
        this.setWarehouse(forwarderTransport.getWarehouseLabel());
      }
      this.setResponsibleAccountId(bill.getResponsibleAccountId());
      this.setResponsibleFullName(bill.getResponsibleFullName());
      
      TMSBillGoods goods = bill.getTmsBillGoods();
      if(goods != null) {
        this.quantity     = goods.getQuantity();
        this.quantityUnit = goods.getQuantityUnit();
        this.volume       = goods.getVolume();
        this.weight       = goods.getWeight();
        this.volumeNote   = goods.getVolumeAsText();
      }
      return this;
  }
  private Date mergeTimeToDateTime(Date dateTime ,String time) {
    String sDate = DateUtil.asCompactDateTime(dateTime);
    if(time != null && time.length() == 5) {
      sDate = sDate.replace("00:00:00", time + ":00");
    }
    try {
      return DateUtil.parseCompactDateTime(sDate);
    } catch (Exception e) {
      return dateTime;
    }
  }
  
  public TMSVendorBill updateByGoodsTrackingAndTrip(VehicleTripGoodsTracking tracking, VehicleTrip trip) {
    if(trip == null) trip = new VehicleTrip();
    TMSVendorBillTracking vendorTracking = new TMSVendorBillTracking();
    vendorTracking.setTransportType(tracking.getTransportType());
    vendorTracking.setVehicleType(tracking.getVehicleType());
    vendorTracking.setDescription(tracking.getDescription());
    vendorTracking.setFixed(tracking.getTrackingCharge().getFixedCharge());
    vendorTracking.setExtra(tracking.getTrackingCharge().getExtraCharge());
    vendorTracking.setCost(tracking.getTrackingCharge().getTotalCharge());
    
    vendorTracking.setLicensePlate(trip.getVehicleLabel());
    vendorTracking.setDriverFullName(trip.getDriverFullName());
    vendorTracking.setDriverMobile(trip.getMobile());
    vendorTracking.setDriverIdentificationNo(trip.getIdentificationNo());
    vendorTracking.setStatus(tracking.getStatus());
    this.billTrackings.add(vendorTracking);
    this.setStatus(tracking.getStatus());
    return this;
  }
  
  public TMSVendorBill updateByTMSOperations(TMSOperations operations) {
    this.opsAccountId        = operations.getOpsAccountId();
    this.opsAccountFullName  = operations.getOpsAccountFullName();
    this.opsMobile           = operations.getOpsAccountMobile();
    this.opsIdentificationNo = operations.getIdentificationNo();
    this.opsNote             = operations.getNote();
    this.opsStatus           = operations.getStatus();
    return this;
  }
  
  public TMSVendorBill updateByOpsFiels(TMSVendorBill vendorBill) {
    this.opsAccountId        = vendorBill.getOpsAccountId();
    this.opsAccountFullName  = vendorBill.getOpsAccountFullName();
    this.opsMobile           = vendorBill.getOpsMobile();
    this.opsIdentificationNo = vendorBill.getOpsIdentificationNo();
    this.opsNote             = vendorBill.getOpsNote();
    this.opsStatus           = vendorBill.getOpsStatus();
    return this;
  }
  
  public TMSVendorBill updateByGoodsTrackingFiels(TMSVendorBill vendorBill) {
    this.billTrackings.clear();
    this.billTrackings.addAll(vendorBill.getBillTrackings());
    for(TMSVendorBillTracking sel : this.billTrackings) {
      this.status = sel.getStatus();
      break;
    }
    this.calculateTotalCost();
    return this;
  }
  
  public TMSVendorBill calculateTotalCost() {
    fixed = 0;
    extra = 0;
    cost  = 0;
    for(TMSVendorBillTracking sel :  this.billTrackings) {
      fixed += sel.getFixed();
      extra += sel.getExtra();
      cost  += sel.getCost();
    }
    
    return this;
  }
  
  public void set(ClientContext client, ICompany company) {
    super.set(client, company);
    set(client, this.billTrackings);
  }
}
