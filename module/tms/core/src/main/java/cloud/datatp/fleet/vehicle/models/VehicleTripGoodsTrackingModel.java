package cloud.datatp.fleet.vehicle.models;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;

import cloud.datatp.fleet.fuel.entity.FuelPrice;
import cloud.datatp.fleet.vehicle.entity.VehicelTripGoods;
import cloud.datatp.fleet.vehicle.entity.VehicleTrip;
import cloud.datatp.fleet.vehicle.entity.VehicleTrip.TaskStatus;
import cloud.datatp.fleet.vehicle.entity.VehicleTrip.VehicleTripTransporterStatus;
import cloud.datatp.fleet.vehicle.entity.VehicleTripGoodsTracking;
import cloud.datatp.fleet.vehicle.entity.VehicleTripGoodsTracking.VehicleTripGoodsTrackingStatus;
import cloud.datatp.fleet.vehicle.entity.VehicleTripGoodsTrackingChargeItem;
import cloud.datatp.fleet.vehicle.entity.VehicleTripProfitEstimate;
import cloud.datatp.fleet.vehicle.entity.VehicleTripRoute;
import cloud.datatp.tms.TMSUtils;
import cloud.datatp.tms.bill.entity.TMSBill;
import cloud.datatp.tms.bill.entity.TMSBillFee;
import cloud.datatp.tms.bill.entity.TMSBillForwarderTransport;
import cloud.datatp.tms.bill.entity.TMSBillGoods;
import cloud.datatp.tms.bill.entity.TMSBillType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.account.entity.Account;
import net.datatp.module.data.db.entity.EditMode;
import net.datatp.module.data.db.entity.EditState;
import net.datatp.util.ds.Collections;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;

@NoArgsConstructor @Getter @Setter
public class VehicleTripGoodsTrackingModel {
  private Long id;
  private boolean billOwner;
  private String uikey;
  private String label;
  private String billLabel;
  private String fileTrucking;
  private String routeType;
  private String cssClass;
  private String bfsHbl;
  private String responsibleFullName;
  private Long   responsibleAccountId;
  private String invoiceNote;
  // goods
  private double quantity;
  private String quantityUnit;
  private double weight;
  private double chargeableWeight;
  private String weightUnit;
  private double volume;
  private String volumeAsText;
  private double chargeableVolume;
  private String volumeUnit;
  private String goodsDescription;
  private String description;
  private String transportType;
  private String typeOfTransport;
  private String vehicleType;
  private double estimateDistanceInKm;
  
  //pickup
  private String pickupContact;
  private String pickupAddress;
  private String pickupInvAddress;
  private Long pickupLocationId;
  private Long pickupPartnerAddressId;
  
//delivery
  private String deliveryContact;
  private String deliveryAddress;
  private String deliveryInvAddress;
  private Long deliveryLocationId;
  private Long deliveryPartnerAddressId;
  
  protected String pickupContainerLocation;
  protected Long   pickupContainerLocationId;
  protected String returnContainerLocation;
  protected Long   returnContainerLocationId;
  //
  private double fixedCharge;
  private double extraCharge;
  private double totalCharge;
  //TOTAL
//  private double revenue;
  private double fixedCost;
  private double extraCost;
  private double liftOnLiftOffCharge;
  private double totalCost;
//  private double profit;
  //Revenue
  private double extraFuelCost;
  private double travelCost;
  private double driverSalary;
  
  @JsonIgnore
  List<VehicleTripGoodsTrackingChargeItem> items = new ArrayList<>();
  //
  private double fuelPrice;
  private Date   fuelPriceDate;
  private double fuelTax;
  private double fuelBeforeVat;
  private double fuelCost;
  private double litersOfFuel;
  private double extraLitersOfFuel;
  private double totalLitersOfFuel;
  
  //Bill Info
  private String      hblNo;
  private String      office;
  private String      tmsBillDescription;
  private Long        tmsBillId;
  private TMSBillType tmsBillType = TMSBillType.FORWARDER;
  private String      customerFullName;
  private Long        customerId;
  
  private String      senderFullName;
  private String      senderContact;
  private String      senderAddress;
  private Long        senderLocationId;
  private String      senderLocationAddress;
  private String      senderInvAddress;
  private Long        senderPartnerAddressId;
  private Double      senderMapLng;
  private Double      senderMapLat;

  
  private String      receiverFullName;
  private String      receiverContact;
  private String      receiverAddress;
  private Long        receiverLocationId;
  private String      receiverLocationAddress;
  private String      receiverInvAddress;
  private Long        receiverPartnerAddressId;
  private Double      receiverMapLng;
  private Double      receiverMapLat;
  
  
  private String      pickupLocation;
  private String      deliveryLocation;
  private String      containerNo;
  private String      sealNo;
  private String      bookingCode;
  private Long        carrierId;
  private String      carrierFullName;

  
  private double      tmsBillQuantity;
  private String      tmsBillQuantityUnit;
  private double      tmsBillWeight;
  private String      tmsBillVolumeAsText;
  private double      tmsBillVolume;
  
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  private Date        tripDeliveryPlan;
  
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  private Date        deliveryPlan;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  private Date        bookingDate;

  private String      mode;
  private Long        warehouseId;
  private String      warehouseLabel;
  private String      time;
  private String      etaCutOffTime;
  
  //Trip
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  private Date        planStartTime;
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  private Date        planEndTime;
  private Long        vehicleTripId;
  private String      fromAddress;
  private String      toAddress;
  private String      identificationNo;
  private Long        fleetId;
  private String      fleetLabel;
  private Long        vehicleId;
  private String      vehicleLabel;
  private String      trailerNumber;
  private double      oilConsumption;
  private Long        driverId;
  private String      driverFullName;
  private String      mobile;
  private boolean     twoWay;
  private Integer     totalFileOnTrip;
  //
  private boolean     combineFile;
  private boolean     mixModeTrip;
  
  private TaskStatus  taskStatus;
  private VehicleTripGoodsTrackingStatus status;
  
  private EditState editState = EditState.ORIGIN;
  
  private Long        warehouseLocationId;
  private String      warehouseLocationShortLabel;
  private String      warehouseLocationAddress;
  private String      warehouseInvAddress;
  private boolean     updateWarehouse;
  //
  private VehicleInfoModified vehicleInfoModified;
  
  private boolean updateTMSBill;
  private boolean updateVehicleTrip;
  private boolean updateFileTrucking;
  private boolean updateTracking;
  private boolean updateFees;
  private boolean updateCost;

  private boolean coordinationRequestProcessing;
  private Long   coordinatorAccountId;
  private String coordinatorFullName;

//User Report
  private VehicleTripProfitEstimate profitEstimate;
  private double averageDriverLaborRatio;
  
  public boolean isDeleted() {
    return EditState.DELETED.equals(editState) ? true : false;
  }
  
  public String getUikey() {
    if(StringUtil.isNotBlank(uikey)) return uikey;
    if(id != null) return id.toString();
    return null;
  }
  
  public String getCombineFileAsText() {
    return String.valueOf(combineFile);
  }
  
  public String getTwoWayAsText() {
    return String.valueOf(twoWay);
  }
  
  public VehicleTripGoodsTrackingModel withProfitEstimate(VehicleTripProfitEstimate profitEstimate) {
    this.profitEstimate = profitEstimate;
    return this;
  }
  
  public VehicleTripGoodsTrackingModel mergeVehicleTrip(VehicleTrip trip) {
    this.tripDeliveryPlan          = trip.getDeliveryPlan();
    this.planStartTime         = trip.getPlanStartTime();
    this.planEndTime           = trip.getPlanEndTime();
    this.vehicleTripId         = trip.getId();
    this.fromAddress           = trip.getFromAddress();
    this.toAddress             = trip.getToAddress();
    this.identificationNo      = trip.getIdentificationNo();
    this.fleetId               = trip.getFleetId();
    this.fleetLabel            = trip.getFleetLabel();
    this.vehicleId             = trip.getVehicleId();
    this.vehicleLabel          = trip.getVehicleLabel();
    this.trailerNumber          = trip.getTrailerNumber();
    this.driverId              = trip.getDriverId();
    this.driverFullName        = trip.getDriverFullName();
    this.mobile                = trip.getMobile();
    this.taskStatus            = trip.getTaskStatus();
    this.twoWay                = trip.isTwoWay();
    
    if(vehicleInfoModified == null) {
      vehicleInfoModified = new VehicleInfoModified();
    }
    vehicleInfoModified.setDriverFullName(trip.getDriverFullName());
    vehicleInfoModified.setIdentificationNo(trip.getIdentificationNo());
    vehicleInfoModified.setMobile(trip.getMobile());
    vehicleInfoModified.setVehicleLabel(trip.getVehicleLabel());
    
    return this;
  }

  public VehicleTrip createVehicleTrip(VehicleTrip trip) {
    taskStatus = TaskStatus.PLAN;
    trip.setFromLocationLabel(pickupLocation); 
    trip.setToLocationLabel(deliveryLocation);
    trip.setFleetId(fleetId);
    trip.setFleetLabel(fleetLabel);
    trip.setVehicleId(vehicleId);
    trip.setVehicleLabel(vehicleLabel);
    trip.setTrailerNumber(trailerNumber);
    trip.setTaskStatus(taskStatus);
    trip.setDriverId(driverId);
    trip.setDriverFullName(driverFullName);
    trip.setMobile(mobile);
    trip.setIdentificationNo(identificationNo);
    trip.setDeliveryPlan(deliveryPlan);
    trip.setPlanStartTime(planStartTime);
    trip.setPlanEndTime(planEndTime);
    trip.setFromAddress(senderAddress);
    trip.setToAddress(receiverAddress);
    if(isExport()) {
      trip.setToAddress(warehouseLabel);
    }
    if(isImport()) {
      trip.setFromAddress(warehouseLabel);
    }
    
    VehicleTripRoute route = new VehicleTripRoute();
    route.setFromLocationLabel(trip.getFromLocationLabel());
    route.setToLocationLabel(trip.getToLocationLabel());
    route.setFromAddress(trip.getFromAddress());
    route.setToAddress(trip.getToAddress());
    trip.getTripRoutes().add(route);
    trip.setEditMode(EditMode.DRAFT);
    trip.setTransporterStatus(VehicleTripTransporterStatus.NEED_CONFIRM);
    return trip;
  }
  
  public VehicleTrip updateVehicleTrip(VehicleTrip trip) {
    if(trip.isNew() && tripDeliveryPlan == null) {
      trip.setDeliveryPlan(deliveryPlan);
    } else {
      trip.setDeliveryPlan(tripDeliveryPlan);
    }
    trip.setFleetId(fleetId);
    trip.setFleetLabel(fleetLabel);
    trip.setVehicleId(vehicleId);
    trip.setVehicleLabel(vehicleLabel);
    trip.setTrailerNumber(trailerNumber);
    trip.setDriverId(driverId);
    trip.setDriverFullName(driverFullName);
    trip.setMobile(mobile);
    trip.setIdentificationNo(identificationNo);
    trip.setPlanStartTime(planStartTime);
    trip.setPlanEndTime(planEndTime);
    trip.setTaskStatus(taskStatus);
    trip.setTwoWay(twoWay);
    List<VehicleTripRoute> routes = trip.getTripRoutes();
    if((totalFileOnTrip == null || totalFileOnTrip <= 1) && Collections.isNotEmpty(routes) && routes.size() == 1) {
      VehicleTripRoute route = routes.get(0);
      route.setFromLocationLabel(pickupLocation);
      route.setToLocationLabel(deliveryLocation);
      trip.updateRouteLabel();
    }
    return trip;
  }
  
  public VehicleTripGoodsTracking merge(VehicleTripGoodsTracking tracking) {
    if(tracking.isNew()) {
      tracking.setTmsBillId(tmsBillId);
      updateCost         = true;
      updateFees         = true;
      updateTracking     = true;
      updateFileTrucking = true;
      
      if(Collections.isNotEmpty(items)) {
        tracking.getTrackingCharge().getItems().addAll(items);
      }
    }
    tracking.setBfsHbl(containerNo + "-" + billLabel);
    
    if(updateTracking) {
      if(label == null) {
        tracking.setLabel("/");
      } else {
        tracking.setLabel(label);
      }
      tracking.setDeliveryPlan(deliveryPlan);
      tracking.setRouteType(routeType);
      tracking.setCssClass(cssClass);
      tracking.setTime(time);
      tracking.setResponsibleAccountId(responsibleAccountId);
      tracking.setResponsibleFullName(responsibleFullName);
      tracking.setTypeOfTransport(typeOfTransport);
      
      VehicelTripGoods goods = tracking.getGoods();
      goods.setQuantity(quantity);
      goods.setQuantityUnit(quantityUnit);
      goods.setWeight(weight);
      goods.setChargeableWeight(chargeableWeight);
      goods.setWeightUnit(weightUnit);
      goods.setVolumeAsText(volumeAsText);
      goods.setVolume(volume);
      goods.setChargeableVolume(chargeableVolume);
      goods.setVolumeUnit(volumeUnit);
      goods.setGoodsDescription(goodsDescription);
      
      tracking.setDescription(description);
      tracking.setTransportType(transportType);
      tracking.setPickupContact(senderContact);
      tracking.setPickupAddress(pickupAddress);
      tracking.setPickupInvAddress(pickupInvAddress);
      tracking.setPickupLocationId(pickupLocationId);
      tracking.setPickupPartnerAddressId(pickupPartnerAddressId);
      
      tracking.setDeliveryContact(receiverContact);
      tracking.setDeliveryAddress(deliveryAddress);
      tracking.setDeliveryInvAddress(deliveryInvAddress);
      tracking.setDeliveryLocationId(deliveryLocationId);
      tracking.setDeliveryPartnerAddressId(deliveryPartnerAddressId);
      
      tracking.setPickupLocation(pickupLocation);
      tracking.setDeliveryLocation(deliveryLocation);
      tracking.setPickupContainerLocation(pickupContainerLocation);
      tracking.setPickupContainerLocationId(pickupContainerLocationId);
      tracking.setReturnContainerLocation(returnContainerLocation);
      tracking.setReturnContainerLocationId(returnContainerLocationId);
      
      tracking.setEstimateDistanceInKm(estimateDistanceInKm);
      
      tracking.setStatus(status);
      tracking.setVehicleType(vehicleType);
      
      this.litersOfFuel      = this.estimateDistanceInKm * this.oilConsumption;
      this.totalLitersOfFuel = this.litersOfFuel + this.extraLitersOfFuel;
      tracking.setLitersOfFuel(litersOfFuel);
      tracking.setExtraLitersOfFuel(extraLitersOfFuel);
      tracking.setTotalLitersOfFuel(totalLitersOfFuel);
      tracking.setCoordinatorAccountId(this.coordinatorAccountId);
      tracking.setCoordinatorFullName(this.coordinatorFullName);
      tracking.setInvoiceNote(this.invoiceNote);
    }
    
    if(updateFileTrucking) {
      tracking.setFileTrucking(fileTrucking);
    }
    
    if(updateFees) {
      this.litersOfFuel      = this.estimateDistanceInKm * this.oilConsumption;
      this.totalLitersOfFuel = this.litersOfFuel + this.extraLitersOfFuel;
      tracking.setLitersOfFuel(litersOfFuel);
      tracking.setExtraLitersOfFuel(extraLitersOfFuel);
      tracking.setTotalLitersOfFuel(totalLitersOfFuel);
      tracking.updateFixedCharge(fixedCharge);
      
      tracking.updateFixedCost(fixedCost);
      tracking.updateExtraCost(extraCost);
      tracking.updateLiftOnLiftOffCharge(liftOnLiftOffCharge);
      tracking.updateTravelCost(travelCost);
    }
    return tracking;
  }
  
  public VehicleTripGoodsTrackingModel updateExpense(VehicleTripGoodsTracking tracking) {
    this.fuelPrice      = tracking.getFuelPrice();
    this.litersOfFuel   = tracking.getLitersOfFuel();
    this.extraFuelCost  = tracking.getTrackingCharge().getExtraFuelCost();
    this.fuelCost       = tracking.getTrackingCharge().getFuelCost();
    this.travelCost     = tracking.getTrackingCharge().getTravelCost();
    this.driverSalary   = tracking.getTrackingCharge().getDriverSalary();
    this.totalCost      = tracking.getTrackingCharge().getTotalCost();
//    this.profit         = tracking.getTrackingCharge().getProfit();
    return this;
  }
  
  public TMSBill createTMSBill(Account owner) {
    TMSBill bill = new TMSBill(tmsBillType);
    if(responsibleAccountId == null) {
      bill.setResponsibleAccountId(owner.getId());
      bill.setResponsibleFullName(owner.getFullName());
    } else {
      bill.setResponsibleAccountId(responsibleAccountId);
      bill.setResponsibleFullName(responsibleFullName);
    }
    return updateTMSBill(bill);
  }
  public TMSBill updateTMSBill(TMSBill bill) {
    bill.setLabel(billLabel);
    bill.setOffice(office);
    
    bill.setDescription(tmsBillDescription);
    bill.setTmsBillFee(new TMSBillFee(bill.getCode()));
    bill.setDeliveryPlan(deliveryPlan);
    bill.setTime(time);

    bill.getCustomer().setCustomerId(customerId);
    bill.getCustomer().setCustomerFullName(customerFullName);
    
    bill.getSender().setSenderFullName(senderFullName);
    bill.getSender().setSenderContact(senderContact);
    bill.getSender().setSenderAddress(senderAddress);
    bill.getSender().setSenderInvAddress(senderInvAddress);
    bill.getSender().setSenderLocationId(senderLocationId);
    bill.getSender().setSenderPartnerAddressId(senderPartnerAddressId);
    bill.getSender().setSenderLat(senderMapLat);
    bill.getSender().setSenderLng(senderMapLng);
    
    bill.getReceiver().setReceiverFullName(receiverFullName);
    bill.getReceiver().setReceiverContact(receiverContact);
    bill.getReceiver().setReceiverAddress(receiverAddress);
    bill.getReceiver().setReceiverInvAddress(receiverInvAddress);
    bill.getReceiver().setReceiverLocationId(receiverLocationId);
    bill.getReceiver().setReceiverPartnerAddressId(receiverPartnerAddressId);
    bill.getReceiver().setReceiverLat(receiverMapLat);
    bill.getReceiver().setReceiverLng(receiverMapLng);

    TMSBillGoods goods = bill.getTmsBillGoods();
    goods.setQuantity(tmsBillQuantity);
    goods.setQuantityUnit(tmsBillQuantityUnit);
    goods.setWeight(tmsBillWeight);
    goods.setVolume(tmsBillVolume);
    goods.setVolumeAsText(tmsBillVolumeAsText);
    
    TMSBillForwarderTransport forwarder = new TMSBillForwarderTransport();
    if(bill.getTmsBillForwarderTransport() != null) forwarder = bill.getTmsBillForwarderTransport();
    forwarder.setMode(mode);
    forwarder.setWarehouseId(warehouseId);
    forwarder.setWarehouseLabel(warehouseLabel);
    forwarder.setEtaCutOffTime(etaCutOffTime);
    forwarder.setContainerNo(containerNo);
    forwarder.setSealNo(sealNo);
    forwarder.setBookingCode(bookingCode);
    forwarder.setCarrierId(carrierId);
    forwarder.setCarrierFullName(carrierFullName);
    forwarder.setTruckType(vehicleType);
    forwarder.setHwbNo(hblNo);
    bill.setTime(time);
    bill.setBookingDate(this.bookingDate);

    return bill;
  }
  
  public TMSBill updateWarehouse(TMSBill bill) {
    if(bill.getTmsBillForwarderTransport().isExport()) {
      bill.getReceiver().setReceiverLocationId(warehouseLocationId);
      bill.getReceiver().setReceiverAddress(warehouseLocationShortLabel);
      bill.getReceiver().setReceiverInvAddress(warehouseInvAddress);
    }
    
    if(bill.getTmsBillForwarderTransport().isImport()) {
      bill.getSender().setSenderLocationId(warehouseLocationId);
      bill.getSender().setSenderAddress(warehouseLocationShortLabel);
      bill.getSender().setSenderInvAddress(warehouseInvAddress);
    }
    return bill;
  }
  
  public boolean isExport() {
    if(mode != null && mode.toUpperCase().contains("EXPORT")) return true;
    return false;
  }
  
  public boolean isImport() {
    if(mode != null && mode.toUpperCase().contains("IMPORT")) return true;
    return false;
  }
  
  public boolean isFcl() {
    if(mode != null && mode.toUpperCase().contains("FCL")) return true;
    return false;
  }
  
  public String getModeLabel() {
    if (mode == null) return null;
    return java.util.Arrays.stream(mode.toLowerCase().split("_"))
        .map(word -> word.substring(0, 1).toUpperCase() + word.substring(1))
        .collect(Collectors.joining(" "));
  }
  
  public boolean verifyTrackingInfo() {
    if(StringUtil.isBlank(transportType)) return false;
    if(StringUtil.isBlank(vehicleType)) return false;
    return true;
  }
  
  public boolean verifyTripInfo() {
    if(StringUtil.isBlank(vehicleLabel)) return false;
    if(StringUtil.isBlank(driverFullName)) return false;
    if(StringUtil.isBlank(mobile)) return false;
    return true;
  }
  
  public String computeVehicleInfo() {
    if(!verifyTripInfo() || !verifyTrackingInfo() || this.vehicleInfoModified == null) return null;
    String content = "";
    content += computeBillInfo();
    content += computeTrackingInfo(computeTripInfo());
    if(Collections.isNotEmpty(getFieldsUpdate())) {
      content +=  "\n(UPDATE INFO: " + getFieldsUpdate().toString().replace("[", "").replace("]", "") + ")";
    }
    return content;
  }
  
  public String computeBillInfo() {
    String header  = "📢 NOTICE: TRUCKS\n";
//    header += "🇻🇳 🇻🇳 🇻🇳\n";
    String content = header + this.billLabel + " " + mode + " " + customerFullName;
    content += "\n- Date & Time: " + DateUtil.asCompactDate(deliveryPlan) + " " + time;
    if(isExport()) {
      content += "\n- Address: " + senderAddress;
    } else if(isImport()) {
      content += "\n- Address: " + receiverAddress;
    } else {
      content += "\n- Pickup: " + senderAddress;
      content += "\n- Delivery: " + receiverAddress;
    }
    content += "\n- Shipment: "+ transportType + " " + vehicleType;
    content += " " + tmsBillQuantity + "(" + tmsBillQuantityUnit + ") " + tmsBillWeight + "(KG) ";
    content += (StringUtil.isNotBlank(tmsBillVolumeAsText) ? tmsBillVolumeAsText + "(CBM) " : "");
    if(StringUtil.isNotBlank(this.tmsBillDescription)) {
      content += "\n- Bill Note: " + this.tmsBillDescription;
    }
    if(StringUtil.isNotBlank(this.description)) {
      content += "\n- Tracking Note: " + this.description;
    }
    return content;
  }
  
  public String computeTrackingInfo(String tripInfo) {
    String content = "\n🚚 🚚 🚚";
    content += tripInfo;
    return content;
  }
  
  public String computeTripInfo() {
    if(!verifyTripInfo()) return null;
    String content = "\n- Truck No: " + vehicleLabel + " " + driverFullName + " ID: " + identificationNo + " Mobile:" + mobile;
    content += "\n- Status: " + taskStatus;
    return content;
  }
  
  
  public String computeVehicleOldInfo() {
    String content = "";
    content += computeTrackingOldInfo();
    content += computeTripOldInfo();
    return content;
  }
  
  public String computeTrackingOldInfo() {
    String content = "";
    String transportType     = this.vehicleInfoModified.getTransportType();
    String vehicleType   = this.vehicleInfoModified.getVehicleType();
    if(StringUtil.isNotBlank(transportType)) content += transportType + " ";
    if(StringUtil.isNotBlank(vehicleType))   content += vehicleType + " ";
    return content;
  }
  
  public String computeTripOldInfo() {
    String content = "";
    String vehicleLabel     = this.vehicleInfoModified.getVehicleLabel();
    String driverFullName   = this.vehicleInfoModified.getDriverFullName();
    String identificationNo = this.vehicleInfoModified.getIdentificationNo();
    String mobile           = this.vehicleInfoModified.getMobile();
    if(StringUtil.isNotBlank(vehicleLabel))     content += vehicleLabel + " ";
    if(StringUtil.isNotBlank(driverFullName))   content += driverFullName + " ";
    if(StringUtil.isNotBlank(mobile))           content += "Mobile: " + mobile + " ";
    if(StringUtil.isNotBlank(identificationNo)) content += "ID: " + identificationNo + " ";
    return content;
  }
  
  public List<String> getFieldsUpdate() {
    List<String> fields = new ArrayList<>();
    fields.addAll(getTrackingFieldsUpdate());
    fields.addAll(getTripFieldsUpdate());
    return fields;
  }
  
  public List<String> getTrackingFieldsUpdate() {
    if(vehicleInfoModified == null || Collections.isEmpty(vehicleInfoModified.getTrackingFieldUpdate())) return new ArrayList<>();
    return vehicleInfoModified.getTrackingFieldUpdate();
  }
  
  public List<String> getTripFieldsUpdate() {
    if(vehicleInfoModified == null || Collections.isEmpty(vehicleInfoModified.getTripFieldUpdate())) return new ArrayList<>();
    return vehicleInfoModified.getTripFieldUpdate();
  }
  
  public void calculateFuel(FuelPrice fuelPrice) {
    this.fuelPrice     = fuelPrice.getTotal();
    this.fuelPriceDate = fuelPrice.getDate();
    this.litersOfFuel  = TMSUtils.ROUNDED("VND", this.oilConsumption * this.estimateDistanceInKm);
    this.fuelBeforeVat = TMSUtils.ROUNDED("VND", this.fuelPrice      * this.litersOfFuel);
    this.fuelCost      = TMSUtils.ROUNDED("VND", this.fuelBeforeVat / (1 + fuelPrice.getTaxRate()));
    this.fuelTax       = this.fuelBeforeVat - this.fuelCost;
  }
  
  @NoArgsConstructor @Getter @Setter
  public static class VehicleInfoModified {
    String transportType;
    String vehicleType;
    
    String vehicleLabel;
    String driverFullName;
    String identificationNo;
    String mobile;
    
    public boolean isUpdateTripInfo() {
      if(StringUtil.isNotBlank(vehicleLabel))     return true;
      if(StringUtil.isNotBlank(driverFullName))   return true;
      if(StringUtil.isNotBlank(identificationNo)) return true;
      if(StringUtil.isNotBlank(mobile))           return true;
      return false;
    }
    
    public boolean isUpdateTrackingInfo() {
      if(StringUtil.isNotBlank(transportType)) return true;
      if(StringUtil.isNotBlank(vehicleType))   return true;
      return false;
    }
    
    public List<String> getTrackingFieldUpdate() {
      List<String> fields = new ArrayList<>();
      if(StringUtil.isNotBlank(transportType)) fields.add("Transport Type");
      if(StringUtil.isNotBlank(vehicleType))   fields.add("Vehicle Type");
      return fields;
    }
    
    public List<String> getTripFieldUpdate() {
      List<String> fields = new ArrayList<>();
      if(StringUtil.isNotBlank(vehicleLabel))     fields.add("License plate");
      if(StringUtil.isNotBlank(driverFullName))   fields.add("Driver");
      if(StringUtil.isNotBlank(identificationNo)) fields.add("Driver ID");
      if(StringUtil.isNotBlank(mobile))           fields.add("Driver Mobile");
      return fields;
    }
  }
}
