package cloud.datatp.tms.bfsone.api;

import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cloud.datatp.fleet.vehicle.entity.VehicleFleet;
import cloud.datatp.tms.bill.TMSBillLogic;
import cloud.datatp.tms.bill.entity.TMSBill;
import cloud.datatp.tms.bill.entity.TMSBillFee;
import cloud.datatp.tms.bill.entity.TMSBillForwarderTransport;
import cloud.datatp.tms.event.TMSBotCreateHouseBillFromTmsBillEvent;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.bot.BotEvent;
import net.datatp.module.bot.BotService;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.RecordGroupByMap;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.hr.EmployeeLogic;
import net.datatp.module.hr.entity.Employee;
import net.datatp.module.monitor.SourceType;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.MapObject;
import net.datatp.util.error.RuntimeError;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;

@Slf4j
@Component
public class BFSOneApiLogic extends DAOService {
  @Autowired
  private EmployeeLogic employeeLogic;
  
  @Autowired
  private TMSBillLogic  tmsBillLogic;

  @Autowired
  private BotService botService;
  
  private BfsOneApi     bfsOneApi = new BfsOneApi();
  
  private static Map<String, String> bfsTruckUnitMap     = new LinkedHashMap<>();
  private static Map<String, String> bfsContainerUnitMap = new LinkedHashMap<>();
  
  static {
    bfsContainerUnitMap.put("20ISOTANK", "20´ ISO TANK");
    bfsContainerUnitMap.put("20DC", "20´DC");
    bfsContainerUnitMap.put("20FF", "20´FF");
    bfsContainerUnitMap.put("20FL", "20´FL");
    bfsContainerUnitMap.put("20FR", "20´FR");
    bfsContainerUnitMap.put("20GP", "20´GP");
    bfsContainerUnitMap.put("20OT", "20´OT");
    bfsContainerUnitMap.put("20RF", "20´RF");
    bfsContainerUnitMap.put("20RH", "20´RH");
    bfsContainerUnitMap.put("20TK", "20´TK");
    bfsContainerUnitMap.put("20TK", "20´TK");
    bfsContainerUnitMap.put("30DC", "30´DC");
    bfsContainerUnitMap.put("40DC", "40´DC");
    bfsContainerUnitMap.put("40FF", "40´FF");
    bfsContainerUnitMap.put("40FL", "40´FL");
    bfsContainerUnitMap.put("40FR", "40´FR");
    bfsContainerUnitMap.put("40GP", "40´GP");
    bfsContainerUnitMap.put("40HC", "40´HC");
    bfsContainerUnitMap.put("40HF", "40´HF");
    bfsContainerUnitMap.put("40HQ", "40´HQ");
    bfsContainerUnitMap.put("40HR", "40´HR");
    bfsContainerUnitMap.put("40OT", "40´OT");
    bfsContainerUnitMap.put("40RF", "40´RF");
    bfsContainerUnitMap.put("40RH", "40´RH");
    bfsContainerUnitMap.put("40RQ", "40´RQ");
    bfsContainerUnitMap.put("40TK", "40´TK");
    bfsContainerUnitMap.put("45RF", "45 RF");
    bfsContainerUnitMap.put("45DC", "45´DC");
    bfsContainerUnitMap.put("45HQ", "45´HQ");
    bfsContainerUnitMap.put("45HC", "40´HQ");
    bfsContainerUnitMap.put("48RF", "48 RF");
    bfsContainerUnitMap.put("50DC", "50´DC");

    bfsTruckUnitMap.put("0.5T" , "0.5T");
    bfsTruckUnitMap.put("1T", "1T");
    bfsTruckUnitMap.put("1.25T", "1.25T");
    bfsTruckUnitMap.put("1.5T", "1.5T");
    bfsTruckUnitMap.put("1.8T", "1.8T");
    bfsTruckUnitMap.put("10T", "10T");
    bfsTruckUnitMap.put("11T", "11T");
    bfsTruckUnitMap.put("12T", "12T");
    bfsTruckUnitMap.put("14T", "14T");
    bfsTruckUnitMap.put("15T", "15T");
    bfsTruckUnitMap.put("16T", "16T");
    bfsTruckUnitMap.put("17T", "17T");
    bfsTruckUnitMap.put("2T", "2T");
    bfsTruckUnitMap.put("2.5T,", "2.5T");
    bfsTruckUnitMap.put("3T", "3T");
    bfsTruckUnitMap.put("3.5T", "3.5T");
    bfsTruckUnitMap.put("5T", "5T");
    bfsTruckUnitMap.put("7T", "7T");
    bfsTruckUnitMap.put("8T", "8T");
    bfsTruckUnitMap.put("9T", "9T");
  }
  
  private String getBfsOneTokenByAccountId(ClientContext client, ICompany company) {
    Employee employee     = employeeLogic.getByAccount(client, company, client.getAccountId());
    String bfsOneCode     = employee.getBfsoneCode();
    String bfsOneUsername = employee.getBfsoneUsername();
    
    if(StringUtil.isBlank(bfsOneCode)) throw RuntimeError.UnknownError("BfsOneCode cannot be null!!!");
    if(StringUtil.isBlank(bfsOneUsername)) throw RuntimeError.UnknownError("BfsOneUsername cannot be null!!!");
    
    String token = bfsOneApi.getAuthorizeToken(bfsOneCode.toUpperCase(), bfsOneUsername.toUpperCase());
    return token;
  }
  
  public MapObject updateTruckTracking(ClientContext client, ICompany company, List<MapObject> records) {
    log.info("...BFSOne API: updating Truck Tracking!!!");
    Employee   employee     = employeeLogic.getByAccount(client, company, client.getAccountId());
    List<Long> tmsBillIds   = Collections.transform(records, rec-> rec.getLong("tmsBillId", null));
    String token = getBfsOneTokenByAccountId(client, company);
    List<BFSOneTruckTracking> trackings = new ArrayList<>();
    for(MapObject rec : records) {
      BFSOneTruckTracking tracking = new BFSOneTruckTracking(rec);
      trackings.add(tracking);
    }
    
    MapObject result = bfsOneApi.updateTruckTracking(token, trackings);
//    MapObject result = new MapObject("Error", false);
    boolean error = result.getBoolean("Error", false);
    if(error) {
      tmsBillLogic.getTmsBillRepo().updateVehicleInfoExporter(company.getId(), tmsBillIds, employee.getLabel(), false, null);
    } else {
      tmsBillLogic.getTmsBillRepo().updateVehicleInfoExporter(company.getId(), tmsBillIds, employee.getLabel(), true, new Date() );
    }
    log.info("...BFSOne API: updated truck tracking success!!!");
    return result;
  }
  
  @SuppressWarnings("unchecked")
  public MapObject updateTruckCosting(ClientContext client, ICompany company, List<MapObject> records) {
    log.info("...BFSOne API: updating truck costing!!!");
    Employee employee     = employeeLogic.getByAccount(client, company, client.getAccountId());
    List<Long> tmsBillIds = new ArrayList<>();
    for(MapObject rec : records) {
      List<Long> ids = (List<Long>) rec.get("mergedItems");
      Long id = rec.getLong("tmsBillId", null);
      if(Collections.isNotEmpty(ids)) {
        tmsBillIds.addAll(ids);
      } else {
        tmsBillIds.add(id);
      }
    }
    String token = getBfsOneTokenByAccountId(client, company);
    List<BFSOneTruckCosting> costings = new ArrayList<>();
    for(MapObject rec : records) {
      BFSOneTruckCosting costing = new BFSOneTruckCosting(rec);
      costings.add(costing);
    }
    MapObject result = bfsOneApi.updateTruckCosting(token, costings);
//    MapObject result = new MapObject("Error", false);
    boolean   error  = result.getBoolean("Error", false);
    if(error) {
      tmsBillLogic.getTmsBillRepo().updateFeeExporter(company.getId(), tmsBillIds, employee.getLabel(), false, null);
    } else {
      tmsBillLogic.getTmsBillRepo().updateFeeExporter(company.getId(), tmsBillIds, employee.getLabel(), true, new Date() );
    }
    log.info("...BFSOne API: updated truck costing {}!!!", error);
    return result;
  }
  
  private List<SqlMapRecord> findTruckTrackingByTMSBillIds(ClientContext client, ICompany company, List<Long> TMSBillIds) {
    SqlQueryParams params = new SqlQueryParams();
    params.addParam("tmsBillIds", TMSBillIds);
    params.addParam("companyId", company.getId());

    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/tms/groovy/TMSBillSql.groovy";
    return searchDbRecords(client, scriptDir, scriptFile, "FindTruckTrackingByTMSBillIds", params);
  }
  
  private List<SqlMapRecord> findVendorTrackingByTMSBillIds(ClientContext client, ICompany company, List<Long> TMSBillIds) {
    SqlQueryParams params = new SqlQueryParams();
    params.addParam("tmsBillIds", TMSBillIds);
    params.addParam("companyId", company.getId());
    
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/tms/groovy/TMSBillSql.groovy";
    return searchDbRecords(client, scriptDir, scriptFile, "FindVendorTrackingByTMSBillIds", params);
  }
  
  public List<MapObject> createBFSTruckTrackingTemplateByTMSBillIds(ClientContext client, ICompany company, List<Long> tmsBillIds) {
    List<SqlMapRecord> records    = findTruckTrackingByTMSBillIds(client, company, tmsBillIds);
    List<Long> ids = new ArrayList<>(tmsBillIds);
    for(SqlMapRecord rec : records) {
      Long id = rec.getLong("id");
      if(tmsBillIds.contains(id)) ids.removeIf(sel -> sel.equals(id));
    }
    
    if(Collections.isEmpty(records)) {
      records = findVendorTrackingByTMSBillIds(client, company, tmsBillIds);
    } else if(Collections.isNotEmpty(ids)) {
      records.addAll(findVendorTrackingByTMSBillIds(client, company, ids));
    }
    
    List<TMSBill> bills = tmsBillLogic.verifyTMSBillHblNos(client, company, tmsBillIds, false);
    Map<Long, Boolean> verifyTMSBillMap = new LinkedHashMap<>();
    for(TMSBill bill : bills) {
      verifyTMSBillMap.put(bill.getId(), bill.getTmsBillForwarderTransport().getVerifyHblNo());
    }
    
    List<MapObject> results = new ArrayList<>();
    for(SqlMapRecord record : records) {
      Long tmsBillId = record.getLong("id");
      MapObject bfsData = toBfsOneTemplateTruckingInfo(client, company, record);
      bfsData.add("verifyHblNo", verifyTMSBillMap.get(tmsBillId));
      results.add(bfsData);
    }
    
    List<MapObject> truckInfoRecords = checkTruckInfo(results);
    List<Long> billVerifyIds = new ArrayList<>();
    for(MapObject rec : truckInfoRecords) {
      Long    tmsBillId         = rec.getLong("tmsBillId", null);
      boolean verifyTruckInfo = rec.getBoolean("verifyTruckInfo", false);
      TMSBill bill = tmsBillLogic.getTMSBillById(client, company, tmsBillId);
      TMSBillForwarderTransport transport = bill.getTmsBillForwarderTransport();
      if(verifyTruckInfo) {
        transport.setVerifyVehicleInfoNote(null);
        transport.setVerifyVehicleInfo(true);
        billVerifyIds.add(tmsBillId);
      } else {
        transport.setVerifyVehicleInfoNote(rec.getString("missingFields"));
        transport.setVerifyVehicleInfo(false);
      }
      tmsBillLogic.saveTMSBill(client, company, bill);
    }
    if(billVerifyIds.size() > 0) {
      TMSBotCreateHouseBillFromTmsBillEvent event = new TMSBotCreateHouseBillFromTmsBillEvent(client, company, billVerifyIds);
      event.setProcessMode(BotEvent.ProcessMode.Queueable);
      botService.broadcast(SourceType.UserBot, event);
    }
    return truckInfoRecords;
  }
  
  private MapObject toBfsOneTemplateTruckingInfo(ClientContext client, ICompany company, SqlMapRecord rec) {
    String vehicleType    = rec.getString("vehicleType");
    Employee employee     = employeeLogic.getByAccount(client, company, client.getAccountId());
    String bfsOneCode     = employee.getBfsoneCode();
    String bfsOneUsername = employee.getBfsoneUsername();
    
    String truckType = vehicleType;
    if(!bfsTruckUnitMap.values().contains(vehicleType)) {
      truckType = bfsTruckUnitMap.get(vehicleType);
    }
    
    String contType = vehicleType;
    if(!bfsContainerUnitMap.values().contains(vehicleType)) {
      contType = bfsContainerUnitMap.get(vehicleType);
    }
    String deliveryPlanStr = null;
    Date deliveryPlan = rec.getDate("deliveryPlan", null);
    if(deliveryPlan != null) deliveryPlanStr = DateUtil.as_yyyy_mm_dd(deliveryPlan);
    
    String truckingFrom = rec.getString("senderInvAddress","");
    truckingFrom = vnLocationStandardization(truckingFrom);
    
    String truckingTo   = rec.getString("receiverInvAddress","");
    truckingTo = vnLocationStandardization(truckingTo);
    
    MapObject result = new MapObject();
    result.add("tmsBillId"   , rec.getString("id"));
    result.add("HWBNO"       , rec.getString("hwbNo"));
    result.add("TruckingFrom", truckingFrom);
    result.add("TruckingTo"  , truckingTo);
    result.add("TruckNo"     , rec.getString("vehicleLabel"));
    result.add("TruckType"   , truckType);
    result.add("DriverName"  , rec.getString("driverFullName"));
    result.add("DriverTel"   , rec.getString("mobile"));
    result.add("ContNo"      , rec.getString("containerNo"));
    result.add("ContType"    , contType);
    result.add("DeliveryDate", deliveryPlanStr);
    result.add("Creator"     , bfsOneUsername);
    result.add("bfsOneCode"  , bfsOneCode);
    result.add("bfsOneUsername", bfsOneUsername);
    return result;
  }
  
  private String vnLocationStandardization(String value) {
    return value.replace("Xã", "").replace("Phường", "").replace("Thị trấn", "").replace("Đặc khu", "")
                .replace("Thành Phố", "").replace("Thành phố", "").replace("Tỉnh","");
  }
  
  private List<MapObject> checkTruckInfo(List<MapObject> records) {
    String [] checkFields     =  new String [] {"TruckingFrom", "TruckingTo", "TruckNo"};
  for(MapObject rec : records) {
    List<String> missingFields = new ArrayList<>();
    boolean verifyHblNo = rec.getBoolean("verifyHblNo", false);
    if(!verifyHblNo) missingFields.add("HblNo chưa xác minh");
    for(String field : checkFields) {
      String value = rec.getString(field);
      if(StringUtil.isBlank(value)) missingFields.add(field + "trống");
    }
    if(Collections.isNotEmpty(missingFields)) {
      rec.add("missingFields", missingFields);
      rec.add("verifyTruckInfo", false);
    } else {
      rec.add("verifyTruckInfo", true);
    }
  }
  return records;
}
  
  private List<SqlMapRecord> findTMSBillFeeByTMSBillIds(ClientContext client, ICompany company, List<Long> TMSBillIds) {
    SqlQueryParams params = new SqlQueryParams();
    params.addParam("tmsBillIds", TMSBillIds);
    params.addParam("companyId", company.getId());
    
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/tms/groovy/TMSBillSql.groovy";
    return searchDbRecords(client, scriptDir, scriptFile, "FindTMSBillFeeByTMSBillIds", params);
  }
  
  public List<MapObject> createBFSCostTemplateByTMSBillIds(ClientContext client, ICompany company, List<Long> tmsBillIds) {
    List<TMSBill> bills = tmsBillLogic.verifyTMSBillHblNos(client, company, tmsBillIds, false);
    Map<Long, Boolean> verifyTMSBillMap = new LinkedHashMap<>();
    for(TMSBill bill : bills) {
      verifyTMSBillMap.put(bill.getId(), bill.getTmsBillForwarderTransport().getVerifyHblNo());
    }
    Employee           employee   = employeeLogic.getByAccount(client, company, client.getAccountId());
    List<SqlMapRecord> records    = findTMSBillFeeByTMSBillIds(client, company, tmsBillIds);
    List<MapObject> results = new ArrayList<>();
    for(SqlMapRecord record : records) {
      Long tmsBillId = record.getLong("id");
      MapObject bfsData = toBfsOneTemplateCost(client, company, employee, record);
      bfsData.add("verifyHblNo", verifyTMSBillMap.get(tmsBillId));
      results.add(bfsData);
    }
    List<MapObject> costingRecords = checkCostingInfo(groupFeeByVehicle(results));
    List<Long> billVerifyIds = new ArrayList<>();
    for(MapObject rec : costingRecords) {
      Long    tmsBillId         = rec.getLong("tmsBillId", null);
      boolean verifyPaymentInfo = rec.getBoolean("verifyPaymentInfo", false);
      TMSBill bill = tmsBillLogic.getTMSBillById(client, company, tmsBillId);
      TMSBillFee fee = bill.getTmsBillFee();
      if(verifyPaymentInfo) {
        fee.setVerifyPaymentNote(null);
        fee.setVerifyPaymentInfo(true);
        billVerifyIds.add(tmsBillId);
      } else {
        fee.setVerifyPaymentNote(rec.getString("missingFields"));
        fee.setVerifyPaymentInfo(false);
      }
      tmsBillLogic.saveTMSBill(client, company, bill);
    }
    if(billVerifyIds.size() > 0) {
      TMSBotCreateHouseBillFromTmsBillEvent event = new TMSBotCreateHouseBillFromTmsBillEvent(client, company, billVerifyIds);
      event.setProcessMode(BotEvent.ProcessMode.Queueable);
      botService.broadcast(SourceType.UserBot, event);
    }
    return costingRecords;

  }
  
  private List<MapObject> groupFeeByVehicle(List<MapObject> records) {
    RecordGroupByMap<Long, MapObject> billMap =new RecordGroupByMap<>(records, rec -> rec.getLong("tmsBillId", 0L));
    List<MapObject> results = new ArrayList<>();
    for(Long id : billMap.getAll().keySet()) {
      List<MapObject> bills =  billMap.get(id);
      RecordGroupByMap<Long, MapObject> trackingFeeMap =new RecordGroupByMap<>(bills, rec -> rec.getLong("trackingId", 0L));
      for(Long trackingId : trackingFeeMap.getAll().keySet()) {
        List<MapObject> recs = trackingFeeMap.get(trackingId);
        MapObject first = recs.get(0);
        double price = 0;
        double vat   = 0;
        double total = 0;
        for(MapObject rec : recs) {
          total += rec.getDouble("Total", 0D);
          price += rec.getDouble("UnitPrice", 0D);
          vat   = rec.getDouble("VAT", 0D);
        }
        first.add("VAT" , vat);
        first.add("Total" , total);
        first.add("UnitPrice" , price);
        results.add(first);
      }
    }
    return results;
  }
  
  private MapObject toBfsOneTemplateCost(ClientContext client, ICompany company,  Employee employee, SqlMapRecord rec) {
    String bfsOneCode     = employee.getBfsoneCode();
    String bfsOneUsername = employee.getBfsoneUsername();
    
    String unit    = rec.getString("unit");
    String bfsOneUnit = null;
    if(bfsTruckUnitMap.values().contains(unit)) {
      bfsOneUnit = unit;
    } else {
      bfsOneUnit = bfsTruckUnitMap.get(unit);
    }
    
    if(bfsOneUnit == null) {
      if(bfsContainerUnitMap.values().contains(unit)) {
        bfsOneUnit = unit;
      } else {
        bfsOneUnit = bfsContainerUnitMap.get(unit);
      }
    }
    
    double vat = 0;
    if(!VehicleFleet.FleetResource.INTERNAL_COMPANY.toString().equals(rec.getString("fleetResource"))) {
      vat = 0.08;
    }
    double quantity = rec.getDouble("quantity");
    double price    = rec.getDouble("price");
    double cost     = quantity*price;
    double total    = cost + cost*vat;
    
    MapObject result = new MapObject();
    result.add("tmsBillId"   , rec.getString("id"));
    result.add("HWBNO"       , rec.getString("hwbNo"));
    result.add("PartnerID", rec.getString("bfsOneCode"));
    result.add("PartnerName"  , rec.getString("tmsPartnerName"));
    result.add("FeeName"     , rec.getString("feeName"));
    result.add("FeeCode"     , rec.getString("feeCode"));
    result.add("Unit"     , bfsOneUnit);
    result.add("Quantity" , quantity);
    result.add("UnitPrice" , price);
    result.add("Currency" , rec.getString("currency"));
    result.add("ExchangeRate" , 0D);
    result.add("VAT" , vat * 100);
    result.add("Total" , total);
    result.add("Notes" , rec.getString("description"));
    result.add("Creator" , bfsOneUsername);
    result.add("bfsOneCode" , bfsOneCode);
    result.add("bfsOneUsername" , bfsOneUsername);
    result.add("trackingId" ,  rec.getLong("trackingId"));
    return result;
  }
  
  private List<MapObject> checkCostingInfo(List<MapObject> records) {
  for(MapObject rec : records) {
    List<String> missingFields = new ArrayList<>();
    double total         = rec.getDouble("Total", 0D);
    String unit          = rec.getString("Unit");
    boolean verifyHblNo  = rec.getBoolean("verifyHblNo", false);
    String partnerID     = rec.getString("PartnerID");
    if(total == 0)               missingFields.add("Cost = 0");
    if(StringUtil.isBlank(unit)) missingFields.add("Unit chưa xác minh");
    if(!verifyHblNo)             missingFields.add("House Bill chưa xác minh");
    if(StringUtil.isBlank(partnerID)) missingFields.add("BFSOneCode thầu phụ trống");
    
    if(Collections.isNotEmpty(missingFields)) {
      rec.add("missingFields", missingFields);
      rec.add("verifyPaymentInfo", false);
    } else {
      rec.add("verifyPaymentInfo", true);
    }
    
  }
  return records;
}
  
}