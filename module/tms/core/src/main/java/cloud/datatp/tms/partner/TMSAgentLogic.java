package cloud.datatp.tms.partner;

import cloud.datatp.bfsone.BFSOneDataLogic;
import cloud.datatp.fforwarder.core.partner.CRMPartnerLogic;
import cloud.datatp.tms.partner.entity.TMSAgent;
import cloud.datatp.tms.partner.entity.TMSPartner;
import cloud.datatp.tms.partner.entity.TMSPartnerPermission;
import cloud.datatp.tms.partner.model.TMSAgentModel;
import cloud.datatp.tms.partner.repository.TMSAgentRepository;
import cloud.datatp.tms.partner.repository.TMSPartnerRepository;
import java.util.ArrayList;
import java.util.List;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.entity.Account;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.RecordGroupByMap;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.MapObject;
import net.datatp.util.error.RuntimeError;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class TMSAgentLogic extends DAOService {

  @Autowired
  private TMSAgentRepository agentRep;

  @Autowired
  private TMSPartnerRepository partnerRepo;

  @Autowired
  private TMSPartnerLogic partnerLogic;

  @Autowired
  private BFSOneDataLogic bfsOneDataLogic;

  @Autowired
  private AccountLogic accountLogic;

  @Autowired
  private TMSPartnerPermissionLogic permissionLogic;

  @Autowired
  private CRMPartnerLogic bfsOnePartnerLogic;

  public List<SqlMapRecord> searchTMSAgents(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/tms/groovy/TMSPartnerSql.groovy";

    Account account = accountLogic.getEditable(client, client.getRemoteUser());
    sqlParams.addParam("userId", account.getId());
    sqlParams.addParam("companyId", company.getId());
//    sqlParams.addParam("role", TMSPartnerPermission.Role.MANAGEMENT_AGENT.toString());
    List<SqlMapRecord> agents =  searchDbRecords(client, scriptDir, scriptFile, "SearchTMSAgents", sqlParams);
    List<Long>  tmsPartnerIds = Collections.transform(agents, (agent) -> agent.getLong("tmsPartnerId"));
    List<SqlMapRecord> mapRecords = searchDbRecords(client, scriptDir, scriptFile, "FindPartnerPermission", sqlParams.addParam("tmsPartnerIds", tmsPartnerIds));
    RecordGroupByMap<Long, SqlMapRecord> recordGroupByMap = new RecordGroupByMap<>(mapRecords, mapRecord -> mapRecord.getLong("tmsPartnerId"));
    for(SqlMapRecord agent: agents) {
      Long id = agent.getLong("tmsPartnerId");
      List<SqlMapRecord> permissions = recordGroupByMap.get(id) == null ? new ArrayList<>(): recordGroupByMap.get(id);
      agent.put("permissions", permissions);
    }
    return agents;
  }

  public TMSPartner loadTMSPartnerByAgentId(ClientContext client, ICompany company, Long agentId) {
    TMSAgent agent = agentRep.loadById(company.getId(), agentId);
    TMSPartner partner = partnerRepo.getById(company.getId(), agent.getTmsPartnerId());
    return partner;
  }

  public TMSAgent loadTMSAgentByPartnerId(ClientContext client, ICompany company, Long tmsPartnerId) {
    TMSAgent agent = agentRep.loadByTmsPartnerId(company.getId(), tmsPartnerId);
    return agent;
  }

  public TMSAgentModel saveTMSAgentModel(ClientContext client, ICompany company, TMSAgentModel model) {
    if(model.isNew()) {
      TMSPartner partner = new TMSPartner();
      partner.genCode();
      model.mergeModelToTMSPartner(partner);
      partner.set(client, company);
      partner = partnerRepo.save(partner);
      TMSAgent agent = new TMSAgent(partner.getId());
      saveTMSAgent(client, company, agent);
      model.setId(agent.getId());
      model.setTmsPartnerId(partner.getId());
      return model;
    }
    TMSAgent agent = loadTMSAgentById(client, company, model.getId());
    if(agent == null) {
      throw RuntimeError.EntityNotFoundError("Agent with id = {0} is not found !!!", model.getId());
    }
    TMSPartner partner = partnerLogic.getTMSPartner(client, company, agent.getTmsPartnerId());
    if(partner == null) {
      throw RuntimeError.EntityNotFoundError("Partner with id = {0} is not found !!!", agent.getTmsPartnerId());
    }
    partner = model.mergeModelToTMSPartner(partner);
    partner.set(client, company);
    partnerRepo.save(partner);
    return model;
  }

  public TMSAgent loadTMSAgentById(ClientContext client, ICompany company, Long id) {
    TMSAgent agent = agentRep.loadById(company.getId(), id);
    return agent;
  }

  public TMSAgent saveTMSAgent(ClientContext client, ICompany company, TMSAgent agent) {
    agent.set(client, company);
    if(agent.isNew()) {
      agent = agentRep.save(agent);
      permissionLogic.addPartnerPermission(client, company, agent.getTmsPartnerId());
      return agent;
    }
    agent = agentRep.save(agent);
    return agent;
  }

  public boolean changeTMSAgentStorageState(ClientContext client, ICompany company, ChangeStorageStateRequest req) {
    agentRep.setStorageState(req.getNewStorageState(), req.getEntityIds());
    return true;
  }

  public boolean deleteTMSAgents(ClientContext client, ICompany company, List<Long> ids) {
    agentRep.deleteAllById(ids);
    return true;
  }

  public List<TMSAgentModel> saveTMSAgentModels(ClientContext client, ICompany company, List<TMSAgentModel> models) {
    for(TMSAgentModel model: models) {
      saveTMSAgentModel(client, company, model);
    }
    return models;
  }

  public TMSPartner saveTMSPartner(ClientContext client, ICompany company, TMSPartner tmsPartner) {
    tmsPartner.set(client, company);
    if(tmsPartner.isNew()) {
      if(tmsPartner.getCode() == null) {
        tmsPartner.genCode();
      }

      tmsPartner = partnerRepo.save(tmsPartner);
      TMSAgent agent = new TMSAgent(tmsPartner.getId());
      saveTMSAgent(client, company, agent);
      return tmsPartner;
    }
    tmsPartner = partnerRepo.save(tmsPartner);
    return tmsPartner;
  }

  public MapObject createMapObjectPreview(ClientContext client, ICompany company, MapObject bfsOnePartner, String code) {
    MapObject result = new MapObject();
    result.add("bfsOnePartner", bfsOnePartner);

    List<TMSPartner> partners = partnerRepo.findByBFSOneCodeOrInvoiceCompanyTaxCode(company.getId(), code, code);
    List<TMSAgentModel> agentModels = new ArrayList<>();
    List<TMSPartner> tmsPartners = new ArrayList<>();
    for(TMSPartner partner: partners) {
      TMSAgent agent = agentRep.loadByTmsPartnerId(company.getId(), partner.getId());
      if(agent == null) {
        tmsPartners.add(partner);
        continue;
      }
      List<TMSPartnerPermission> permissions = permissionLogic.findByPartnerId(client, company, agent.getTmsPartnerId());
      TMSAgentModel model = new TMSAgentModel();
      model.mergeTMSPartnerToModel(partner);
      model.setPermissions(permissions);
      model.setId(agent.getId());
      agentModels.add(model);
    }
    result.add("tmsAgents", agentModels);
    result.add("tmsPartners", tmsPartners);
    return result;
  }

}