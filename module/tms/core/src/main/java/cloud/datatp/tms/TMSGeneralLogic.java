package cloud.datatp.tms;

import java.util.List;

import cloud.datatp.tms.partner.entity.TMSPartnerAddress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cloud.datatp.gps.maps.api.MapsApi;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.entity.Account;
import net.datatp.module.communication.CommunicationMessageService;
import net.datatp.module.communication.entity.CommunicationAccount;
import net.datatp.module.communication.entity.Message;
import net.datatp.module.communication.entity.MessageDeliverType;
import net.datatp.module.communication.entity.TargetRecipient;
import net.datatp.module.company.CompanyConfigLogic;
import net.datatp.module.company.entity.CompanyConfig;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.resource.location.entity.Location;
import net.datatp.module.resource.location.entity.LocationType;
import net.datatp.module.resource.location.repo.LocationRepository;
import net.datatp.module.zalo.ZaloLogic;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.MapObject;
import net.datatp.util.error.RuntimeError;

@Component
public class TMSGeneralLogic extends DAOService {
  @Autowired
  private ZaloLogic                          zaloLogic;

  @Autowired
  private CommunicationMessageService        messageService;

  @Autowired
  private AccountLogic                       accountLogic;

  @Autowired
  private CompanyConfigLogic                 companyConfigLogic;

  @Autowired
  private LocationRepository locationRepository;
  
  public boolean sentZaloMessage(ClientContext client, ICompany company, Long toAccountId, String content, String subject) {
    Account toAccount = accountLogic.getAccountById(client, toAccountId);
    CommunicationAccount fromMessageAccount = messageService.getCommunicationAccount(client, client.getAccountId());
    CommunicationAccount toMessageAccount = messageService.getCommunicationAccount(client, toAccount.getId());

    Message message = new Message(fromMessageAccount.getAccountId());
    message.setSubject(subject);
    message.setContent(content);
    TargetRecipient targetRecipient = new TargetRecipient(MessageDeliverType.Private, toMessageAccount.getAccountId(), toMessageAccount.getFullName());
    targetRecipient.setForwardEmail(toMessageAccount.isAutoForward());
    message.withRecipient(targetRecipient);

    try {
      if(toMessageAccount.getZaloUserId() == null) throw RuntimeError.UnknownError("Zalo User Id Is Null!!!");
      zaloLogic.sendMessage(client, fromMessageAccount, toMessageAccount, content);
    } catch (Exception e) {
      content = content.replaceAll("\n", "<br/>");
      message.setContent(content);
      messageService.sendMessage(client, company, message);
    }
    return true;
  }

  public boolean sentZaloMessageToAccounts(ClientContext client, ICompany company, List<Long> toAccountIds, String content, String subject) {
    for (Long toAccountId: toAccountIds) {
      sentZaloMessage(client, company, toAccountId, content, subject);
    }
    return true;
  }

  public boolean autoSendMessage(ClientContext client, ICompany company, String companyConfigName) {
//    if("test".equals(appEnv.getAppEnv())) return false;
    CompanyConfig companyConfig = companyConfigLogic.getCompanyConfigByCompanyId(client, company.getId());
    Boolean autoForwardMessage = companyConfig.getAttributeAsBoolean(companyConfigName);
    return autoForwardMessage;
  }

  public Location getNewSubdistrictLocationByOldAddress(ClientContext client, String address) {
    MapsApi api = new MapsApi("https://rsapi.goong.io/v2/");
    List<MapObject> results = api.geocode(address);
    MapObject result = results.get(0);
    MapObject compound = result.getMapObject("compound");
    String commune = compound.getString("commune");
    String province = compound.getString("province");
    List<Location> locations = locationRepository.findLocations(LocationType.Subdistrict, "%"+commune+"%",  "%"+province+"%");
    if(locations.size() > 0) {
      return locations.get(0);
    }
    return null;
  }
  public TMSPartnerAddress createPartnerAddress(ClientContext client, String address) {
    MapsApi api = new MapsApi("https://rsapi.goong.io/v2/");
    List<MapObject> results = api.geocode(address);
    MapObject result = results.get(0);
    MapObject compound = result.getMapObject("compound");
    String commune = compound.getString("commune");
    String province = compound.getString("province");
    MapObject geometry = result.getMapObject("geometry");
    MapObject location = geometry.getMapObject("location");
    Double lat = location.getDouble("lat",null);
    Double lng = location.getDouble("lng",null);
    List<Location> locations = locationRepository.findLocations(LocationType.Subdistrict, "%"+commune+"%",  "%"+province+"%");
    TMSPartnerAddress partnerAddress = new TMSPartnerAddress();
    partnerAddress.setLng(lng);
    partnerAddress.setLat(lat);
    if(locations.size() > 0) {
      Location getlocation = locations.get(0);
      partnerAddress.setInvAddress(getlocation.getSubdistrictLabel() + " " +getlocation.getStateLabel());
      partnerAddress.setLocationId(getlocation.getId());
      partnerAddress.setLocationLabel(getlocation.getAddress());
      return partnerAddress;
    }
    return null;
  }
}