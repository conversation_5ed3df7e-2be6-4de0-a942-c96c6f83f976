package cloud.datatp.tms.partner;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cloud.datatp.tms.partner.entity.TMSAgent;
import cloud.datatp.tms.partner.entity.TMSPartner;
import cloud.datatp.tms.partner.model.TMSAgentModel;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.service.BaseComponent;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.MapObject;

@Service("TMSAgentService")
public class TMSAgentService extends BaseComponent {
  @Autowired
  private TMSAgentLogic agentLogic;

  @Transactional(readOnly=true)
  public List<SqlMapRecord> searchTMSAgents(ClientContext client, ICompany company, SqlQueryParams params) {
    return agentLogic.searchTMSAgents(client, company, params);
  }

  @Transactional(readOnly=true)
  public TMSAgent loadTMSAgentById(ClientContext client, ICompany company, Long id) {
    return agentLogic.loadTMSAgentById(client, company, id);
  }

  @Transactional(readOnly=true)
  public TMSPartner loadTMSPartnerByAgentId(ClientContext client, ICompany company, Long agentId) {
    return agentLogic.loadTMSPartnerByAgentId(client, company, agentId);
  }

  @Transactional(readOnly=true)
  public TMSAgent loadTMSAgentByPartnerId(ClientContext client, ICompany company, Long tmsPartnerId) {
    return agentLogic.loadTMSAgentByPartnerId(client, company, tmsPartnerId);
  }

  @Transactional
  public List<TMSAgentModel> saveTMSAgentModels(ClientContext client, ICompany company, List<TMSAgentModel> models) {
    return agentLogic.saveTMSAgentModels(client, company, models);
  }

  @Transactional
  public TMSAgent saveTMSAgent(ClientContext client, ICompany company, TMSAgent agent) {
    return agentLogic.saveTMSAgent(client, company, agent);
  }

  @Transactional
  public boolean changeTMSAgentStorageState(ClientContext client, ICompany company, ChangeStorageStateRequest req) {
    return agentLogic.changeTMSAgentStorageState(client, company, req);
  }

  @Transactional
  public boolean deleteTMSAgents(ClientContext client, ICompany company, List<Long> ids) {
    return agentLogic.deleteTMSAgents(client, company, ids);
  }

  @Transactional
  public TMSPartner saveTMSPartner(ClientContext client, ICompany company, TMSPartner tmsPartner) {
    return agentLogic.saveTMSPartner(client, company, tmsPartner);
  }

  @Transactional
  public MapObject createMapObjectPreview(ClientContext client, ICompany company, MapObject bfsOnePartner, String code) {
    return agentLogic.createMapObjectPreview(client, company,bfsOnePartner,  code);
  }
}
