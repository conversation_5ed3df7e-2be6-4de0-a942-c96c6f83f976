package cloud.datatp.tms.cron;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cloud.datatp.fleet.vehicle.event.TMSBotSendZaloEvent;
import cloud.datatp.tms.bfsone.api.BFSOneApiLogic;
import cloud.datatp.tms.bill.TMSBillLogic;
import cloud.datatp.tms.bill.entity.TMSBill;
import cloud.datatp.tms.bill.entity.TMSBillFee;
import cloud.datatp.tms.bill.entity.TMSBillForwarderTransport;
import cloud.datatp.tms.bill.repository.TMSBillRepository;
import jakarta.annotation.PostConstruct;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.entity.Account;
import net.datatp.module.app.AppEnv;
import net.datatp.module.bot.BotService;
import net.datatp.module.bot.cron.CronJob;
import net.datatp.module.bot.cron.CronJobFrequency;
import net.datatp.module.bot.cron.CronJobLogger;
import net.datatp.module.company.CompanyReadLogic;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.monitor.SourceType;
import net.datatp.security.client.ClientContext;
import net.datatp.security.client.DeviceInfo;
import net.datatp.security.client.DeviceType;
import net.datatp.util.ds.MapObject;
import net.datatp.util.text.DateUtil;

@Component
public class TMSBillCheckInfoCronJob extends CronJob {
  @Autowired
  private AppEnv appEnv;

  @Autowired
  private CompanyReadLogic companyLogic;
  
  @Autowired
  private AccountLogic accountLogic;
  
  @Autowired
  private TMSBillLogic tmsBillLogic;

  @Autowired
  private TMSBillRepository billRepository;
  
  @Autowired
  private BFSOneApiLogic bfsOneApiLogic;
  
  @Autowired 
  private BotService botService;
  
  @PostConstruct
  public void onInit() {
    if (appEnv.isDevEnv() || appEnv.isTestEnv()) {
//      setFrequencies(CronJobFrequency.EVERY_MINUTE);
    } else {
      setFrequencies(CronJobFrequency.EVERY_DAY_00_AM);
      setFrequencies(CronJobFrequency.EVERY_DAY_12_PM);
      setFrequencies(CronJobFrequency.EVERY_DAY_15_PM);
    }
  }
  
  protected TMSBillCheckInfoCronJob() {
    super("tms-bill:", "Automatically Check TMS Bill Information");
  }

  @Override
  protected List<ICompany> getTargetCompanies() {
    List<ICompany> companies = new ArrayList<>();
    ICompany iCompany = ICompany.SYSTEM;

    String[] companyCodes = new String[]{"beehph"};
    for (String companyCode : companyCodes) {
      ICompany company = companyLogic.getCompany(getClientContext(iCompany), companyCode);
      companies.add(company);
    }
    return companies;
  }

  @Override
  protected ClientContext getClientContext(ICompany company) {
    String        loginId = "chien.pham";
    ClientContext client  = new ClientContext("default", loginId, "localhost");
    Account       account = accountLogic.getAccountByLoginId(client, loginId);
    if (appEnv.isProdEnv()) {
      client.setDeviceInfo(new DeviceInfo(DeviceType.Server));
    } else {
      client.setDeviceInfo(new DeviceInfo(DeviceType.Computer));
    }
    client.setAccountId(account.getId());
    return client;
  }

  private List<Long> findTMSBillIds(ClientContext client, ICompany company,  Date fromDate, Date toDate) {
    List<Long> ids  = new ArrayList<>();
    List<TMSBill> tmsBills = billRepository.findTMSBills(company.getId(), fromDate, toDate);
    for(TMSBill bill : tmsBills) {
      if(!TMSBill.TABLE_NAME.equals(bill.getRefSource())) {
        ids.add(bill.getId());
      }
    }
    return ids;
  }

  private Calendar setStartDay(Calendar cal) {
    cal.set(Calendar.HOUR_OF_DAY, 0);
    cal.set(Calendar.MINUTE, 0);
    cal.set(Calendar.SECOND, 0);
    cal.set(Calendar.MILLISECOND, 0);
    return cal;
  }

  private Calendar setEndDay(Calendar cal) {
    cal.set(Calendar.HOUR_OF_DAY, 23);
    cal.set(Calendar.MINUTE, 59);
    cal.set(Calendar.SECOND, 59);
    cal.set(Calendar.MILLISECOND, 999);
    return cal;
  }

  @Override
  protected void run(ClientContext client, ICompany company, CronJobLogger logger) {
    Calendar calCost = Calendar.getInstance();
    calCost.add(Calendar.DAY_OF_MONTH, -4);
    setStartDay(calCost);
    Date fromDateCost = calCost.getTime();
    setEndDay(calCost);
    Date toDateCost = calCost.getTime();

    Calendar calTruck = Calendar.getInstance();
    calTruck.add(Calendar.DAY_OF_MONTH, -1);
    setStartDay(calTruck);
    Date fromDateTruckInfo = calTruck.getTime();
    setEndDay(calTruck);
    Date toDateTruckInfo = calTruck.getTime();


    List<Long> tmsBillIdCosts      = this.findTMSBillIds(client, company, fromDateCost, toDateCost);
    List<Long> tmsBillIdTruckInfos = this.findTMSBillIds(client, company, fromDateTruckInfo, toDateTruckInfo);
    List<MapObject> costingRecords   = bfsOneApiLogic.createBFSCostTemplateByTMSBillIds(client, company, tmsBillIdCosts);
    int costErorrCount = 0;
    for(MapObject rec : costingRecords) {
      Long    tmsBillId         = rec.getLong("tmsBillId", null);
      boolean verifyPaymentInfo = rec.getBoolean("verifyPaymentInfo", false);
      TMSBill bill = tmsBillLogic.getTMSBillById(client, company, tmsBillId);
      TMSBillFee fee = bill.getTmsBillFee();
      if(verifyPaymentInfo) {
        fee.setVerifyPaymentNote(null);
        fee.setVerifyPaymentInfo(true);
      } else {
        costErorrCount++;
        fee.setVerifyPaymentNote(rec.getString("missingFields"));
        fee.setVerifyPaymentInfo(false);
      }
      tmsBillLogic.saveTMSBill(client, company, bill);
    }
    
    int truckErorrCount = 0;
    List<MapObject> truckInfoRecords =bfsOneApiLogic.createBFSTruckTrackingTemplateByTMSBillIds(client, company, tmsBillIdTruckInfos);
    for(MapObject rec : truckInfoRecords) {
//      Long    tmsBillId         = rec.getLong("tmsBillId", null);
      boolean verifyTruckInfo = rec.getBoolean("verifyTruckInfo", false);
//      TMSBill bill = tmsBillLogic.getTMSBillById(client, company, tmsBillId);
//      TMSBillForwarderTransport transport = bill.getTmsBillForwarderTransport();
      if(verifyTruckInfo) {
//        transport.setVerifyVehicleInfoNote(null);
//        transport.setVerifyVehicleInfo(true);
      } else {
        truckErorrCount++;
//        transport.setVerifyVehicleInfoNote(rec.getString("missingFields"));
//        transport.setVerifyVehicleInfo(false);
      }
//      tmsBillLogic.saveTMSBill(client, company, bill);
    }
    
    String costingReport = "Report Costing:" +  DateUtil.asCompactDate(fromDateCost) + " - " + costErorrCount + "/" + costingRecords.size() + "FAIL";
    String truckReport   = "Report Truck:" +  DateUtil.asCompactDate(fromDateTruckInfo) + " - " + truckErorrCount + "/" + truckInfoRecords.size() + "FAIL";
    
    Map<Long, String> noti = new LinkedHashMap<>();
    noti.put(2087L, costingReport + "\n" + truckReport);
//    noti.put(2095L, costingReport + "\n" + truckReport);
    noti.put(2000L, costingReport + "\n" + truckReport);
    
    TMSBotSendZaloEvent botEvent = new TMSBotSendZaloEvent(client, company, noti);
    botService.broadcast(SourceType.UserBot, botEvent);
  }

}
