package cloud.datatp.fleet.vehicle.models;

import java.util.ArrayList;
import java.util.List;

import cloud.datatp.fleet.vehicle.entity.VehicleTrip.TaskStatus;
import cloud.datatp.fleet.vehicle.entity.VehicleTripGoodsTracking.VehicleTripGoodsTrackingStatus;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.data.db.SqlMapRecord;

@NoArgsConstructor @Getter @Setter
public class VehicleTripGoodsTrackingSplitModel {
  VehicleTripGoodsTrackingModel originTracking;
  List<VehicleTripGoodsTrackingModel> splitGoodsTrackings = new ArrayList<>();
  List<SqlMapRecord> records =  new ArrayList<>();
  
  public List<VehicleTripGoodsTrackingModel> getSplitGoodsTrackingsMerged () {
    for(VehicleTripGoodsTrackingModel tracking : splitGoodsTrackings) {
      tracking.setTmsBillId(originTracking.getTmsBillId());
      tracking.setUpdateVehicleTrip(true);
      tracking.setMode(originTracking.getMode());
      tracking.setStatus(VehicleTripGoodsTrackingStatus.CONFIRMED);
      tracking.setTaskStatus(TaskStatus.PLAN);
      tracking.setUpdateCost(true);
      tracking.setUpdateFees(true);
      tracking.setUpdateFileTrucking(true);
      tracking.setUpdateTracking(true);
      tracking.setTypeOfTransport(originTracking.getTypeOfTransport());
      tracking.setCoordinationRequestProcessing(originTracking.isCoordinationRequestProcessing());
    }
    return this.splitGoodsTrackings;
  }
}

