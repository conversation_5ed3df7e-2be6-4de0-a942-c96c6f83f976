package cloud.datatp.tms.partner;

import cloud.datatp.bfsone.BFSOneDataLogic;
import cloud.datatp.fforwarder.core.partner.CRMPartnerLogic;
import cloud.datatp.jobtracking.rule.JobTrackingRuleConfigLogic;
import cloud.datatp.jobtracking.rule.entity.JobTrackingProjectRuleConfig;
import cloud.datatp.tms.bill.TMSBillLogic;
import cloud.datatp.tms.bill.entity.TMSBill;
import cloud.datatp.tms.partner.entity.TMSCustomer;
import cloud.datatp.tms.partner.entity.TMSPartner;
import cloud.datatp.tms.partner.entity.TMSPartnerPermission;
import cloud.datatp.tms.partner.model.TMSCustomerModel;
import cloud.datatp.tms.partner.repository.TMSCustomerRepository;
import cloud.datatp.tms.partner.repository.TMSPartnerRepository;
import java.util.ArrayList;
import java.util.List;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.entity.Account;
import net.datatp.module.company.CompanyConfigLogic;
import net.datatp.module.company.entity.CompanyConfig;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.RecordGroupByMap;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.security.client.Capability;
import net.datatp.security.client.ClientContext;
import net.datatp.util.dataformat.DataSerializer;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import net.datatp.util.error.ErrorType;
import net.datatp.util.error.RuntimeError;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class TMSCustomerLogic extends DAOService {

  @Autowired
  private TMSCustomerRepository customerRepo;

  @Autowired
  private TMSPartnerRepository partnerRepo;

  @Autowired
  private TMSPartnerLogic partnerLogic;

  @Autowired
  private AccountLogic accountLogic;

  @Autowired
  private BFSOneDataLogic bfsOneDataLogic;

  @Autowired
  private CRMPartnerLogic bfsOnePartnerLogic;

  @Autowired
  private TMSPartnerPermissionLogic permissionLogic;

  @Autowired
  private JobTrackingRuleConfigLogic jobTrackingRuleConfigLogic;

  @Autowired
  private CompanyConfigLogic companyConfigLogic;

  @Autowired
  private TMSBillLogic tmsBillLogic;

  public TMSCustomer loadTMSCustomerById(ClientContext client, ICompany company, Long id) {
    TMSCustomer customer = customerRepo.loadById(company.getId(), id);
    return customer;
  }

  public TMSPartner loadTMSPartnerByCustomerId(ClientContext client, ICompany company, Long customerId) {
    TMSCustomer customer = customerRepo.loadById(company.getId(), customerId);
    if(customer == null) return null;
    TMSPartner partner = partnerRepo.getById(company.getId(), customer.getTmsPartnerId());
    return partner;
  }

  public TMSCustomer loadTMSCustomerByPartnerId(ClientContext client, ICompany company, Long tmsPartnerId) {
    TMSCustomer customer = customerRepo.loadByTmsPartnerId(company.getId(), tmsPartnerId);
    return customer;
  }

  public List<TMSCustomer> findTMSCustomerByPartnerIds(ClientContext client, ICompany company, List<Long> partnerIds) {
    List<TMSCustomer> customers = customerRepo.findByTMSPartnerIds(company.getId(), partnerIds);
    return customers;
  }

  public boolean updatePartnerId(ClientContext client, ICompany company, Long newPartnerId, List<Long> partnerIds) {
    customerRepo.updatePartnerId(company.getId(),newPartnerId, partnerIds);
    return true;
  }

  public TMSCustomerModel loadTMSCustomerModel(ClientContext client, ICompany company, Long id) {
    TMSCustomer customer = loadTMSCustomerById(client, company, id);
    if(customer == null) return null;
    TMSPartner partner = partnerLogic.getTMSPartner(client, company, customer.getTmsPartnerId());
    TMSCustomerModel model = new TMSCustomerModel();
    model.mergeTMSPartnerToModel(partner);
    model.mergeTMSCustomerToModel(customer);
    return model;
  }

  public TMSCustomerModel saveTMSCustomerModel(ClientContext client, ICompany company, TMSCustomerModel model) {
    TMSCustomer customer = loadTMSCustomerById(client, company, model.getId());
    if(customer == null || customer.isNew()) {
      TMSPartner partner = new TMSPartner();
      partner.genCode();
      model.mergeModelToTMSPartner(partner);
      partner.set(client, company);
      partner = partnerRepo.save(partner);
      customer = new TMSCustomer(partner.getId());
      customer = model.mergeModelToTMSCustomer(customer);
      saveTMSCustomer(client, company, customer);
      model.setId(customer.getId());
      model.setTmsPartnerId(partner.getId());
    }
    TMSPartner partner = partnerLogic.getTMSPartner(client, company, customer.getTmsPartnerId());
    if(partner == null) {
      throw RuntimeError.EntityNotFoundError("Partner with id = {0} is not found !!!", customer.getTmsPartnerId());
    }
    partner = model.mergeModelToTMSPartner(partner);
    partner.set(client, company);
    partner = partnerRepo.save(partner);
    customer.setTmsPartnerId(partner.getId());
    customer = model.mergeModelToTMSCustomer(customer);
    customer.set(client, company);
    customerRepo.save(customer);
    model.setTmsPartnerId(partner.getId());
    return model;
  }

  public List<SqlMapRecord> searchTMSCustomers(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/tms/groovy/TMSPartnerSql.groovy";

    Account account = accountLogic.getEditable(client, client.getRemoteUser());
    sqlParams.addParam("userId", account.getId());
    sqlParams.addParam("companyId", company.getId());

    List<SqlMapRecord> customers =  searchDbRecords(client, scriptDir, scriptFile, "SearchTMSCustomers", sqlParams);
    List<Long> tmsPartnerIds = Collections.transform(customers, (customer) -> customer.getLong("tmsPartnerId"));
    
    List<SqlMapRecord> mapRecords = searchDbRecords(client, scriptDir, scriptFile, "FindPartnerPermission", sqlParams.addParam("tmsPartnerIds", tmsPartnerIds));
    RecordGroupByMap<Long, SqlMapRecord> recordGroupByMap = new RecordGroupByMap<>(mapRecords, mapRecord -> mapRecord.getLong("tmsPartnerId"));
    for(SqlMapRecord customer: customers) {
      Long id        = customer.getLong("tmsPartnerId");
      List<SqlMapRecord> permissions = recordGroupByMap.get(id) == null ? new ArrayList<>(): recordGroupByMap.get(id);
      customer.put("permissions", permissions);
//      Long partnerId = customer.getLong("tmsPartnerId");
//      List<SqlMapRecord> addresses   = addressRecordGroupByMap.get(partnerId) == null ? new ArrayList<>(): addressRecordGroupByMap.get(partnerId);
//      customer.put("addresses", addresses);
    }
    
    return customers;
  }
  
  public TMSCustomer saveTMSCustomer(ClientContext client, ICompany company, TMSCustomer customer) {
    customer.set(client, company);
    if(customer.isNew()) {
      customer = customerRepo.save(customer);
      permissionLogic.addPartnerPermission(client, company, customer.getTmsPartnerId());
      return customer;
    }
    customer = customerRepo.save(customer);
    return customer;
  }

  public TMSPartner saveTMSPartner(ClientContext client, ICompany company, TMSPartner tmsPartner) {
    tmsPartner.set(client, company);
    if(tmsPartner.isNew()) {
      if(tmsPartner.getCode() == null) {
        tmsPartner.genCode();
      }
      tmsPartner = partnerRepo.save(tmsPartner);
      TMSCustomer customer = new TMSCustomer(tmsPartner.getId());
      saveTMSCustomer(client, company, customer);
      return tmsPartner;
    }
    tmsPartner = partnerRepo.save(tmsPartner);
    return tmsPartner;
  }

  public boolean changeTMSCustomerStorageState(ClientContext client, ICompany company, ChangeStorageStateRequest req) {
    customerRepo.setStorageState(req.getNewStorageState(), req.getEntityIds());
    return true;
  }

  public boolean deleteTMSCustomers(ClientContext client, ICompany company, List<Long> ids) {
    customerRepo.deleteAllById(ids);
    return true;
  }

  public List<TMSCustomerModel> saveTMSCustomers(ClientContext client, ICompany company, List<TMSCustomerModel> models) {
    for(TMSCustomerModel model: models) {
      saveTMSCustomerModel(client, company, model);
    }
    return models;
  }

  public MapObject createMapObjectPreview(ClientContext client, ICompany company, MapObject bfsOnePartner, String code) {
    MapObject result = new MapObject();
    result.add("bfsOnePartner", bfsOnePartner);
    List<TMSPartner> partners = partnerRepo.findByBFSOneCodeOrInvoiceCompanyTaxCode(company.getId(), code, code);
    List<TMSCustomerModel> customerModels = new ArrayList<>();
    List<TMSPartner> tmsPartners = new ArrayList<>();
    for(TMSPartner partner: partners) {
      TMSCustomer customer = customerRepo.loadByTmsPartnerId(company.getId(), partner.getId());
      if(customer == null) {
        tmsPartners.add(partner);
        continue;
      }
      List<TMSPartnerPermission> permissions = permissionLogic.findByPartnerId(client, company, customer.getTmsPartnerId());
      TMSCustomerModel model = new TMSCustomerModel();
      model.mergeTMSPartnerToModel(partner);
      model.setPermissions(permissions);
      model.setId(customer.getId());
      customerModels.add(model);
    }
    Account account = accountLogic.getAccountByLoginId(client, bfsOnePartner.getString("saleOwnerUsername"));
    if(account != null) {
      TMSPartnerPermission saleManPermission = new TMSPartnerPermission(account, Capability.Read,
        TMSPartnerPermission.Role.SALE_MAN, null);
      result.add("saleManPermission", saleManPermission);
    }
    result.add("tmsCustomers", customerModels);
    result.add("tmsPartners", tmsPartners);
    return result;
  }

  public TMSCustomerModel updateTMSCustomerJobTrackingRuleRef(ClientContext client, ICompany company, Long customerId, Long jobTrackingProjectRuleId) {
    TMSCustomer customer = loadTMSCustomerById(client, company, customerId);
    TMSPartner partner = partnerLogic.getTMSPartner(client, company, customer.getTmsPartnerId());
    TMSCustomerModel model = new TMSCustomerModel();
    if (Objects.isNull(model))
      throw new RuntimeError(ErrorType.IllegalArgument, "TMSCustomer is not found by id = {0}", customerId);

    JobTrackingProjectRuleConfig config = jobTrackingRuleConfigLogic.getProjectRuleConfig(client, company, jobTrackingProjectRuleId);
    if (Objects.isNull(config))
      throw new RuntimeError(ErrorType.EntityNotFound, "JobTrackingProjectRuleConfig is not found by id = {0}", jobTrackingProjectRuleId);

    customer.setTmsJobTrackingRuleId(config.getId());
    customer.setTmsJobTrackingRuleLabel(config.getLabel());
    customer = saveTMSCustomer(client, company, customer);

    model.mergeTMSPartnerToModel(partner);
    model.mergeTMSCustomerToModel(customer);
    return model;
  }

  public TMSCustomerModel createTMSCustomerRuleFromDefault(ClientContext client, ICompany company, Long customerId) {
    TMSCustomer customer = loadTMSCustomerById(client, company, customerId);
    TMSPartner partner = partnerLogic.getTMSPartner(client, company, customer.getTmsPartnerId());
    TMSCustomerModel model = new TMSCustomerModel();


    CompanyConfig companyConfig = companyConfigLogic.getCompanyConfigByCompanyId(client, company.getId());
    Long jobTrackingRuleDefaultId = companyConfig.getAttributeAsLong(TMSBill.TMS_BILL_JOB_TRACKING_DEFAULT_RULE_ID, null);
    if (Objects.isNull(jobTrackingRuleDefaultId))
      throw new RuntimeError(ErrorType.IllegalArgument, "Company config Attribute is not found by name = {}", TMSBill.TMS_BILL_JOB_TRACKING_DEFAULT_RULE_ID);

    JobTrackingProjectRuleConfig defaultConfig = jobTrackingRuleConfigLogic.getProjectRuleConfig(client, company, jobTrackingRuleDefaultId);
    if (Objects.isNull(defaultConfig))
      throw new RuntimeError(ErrorType.EntityNotFound, "JobTrackingProjectRuleConfig is not found by id = {}", jobTrackingRuleDefaultId);

    JobTrackingProjectRuleConfig newConfig = DataSerializer.JSON.clone(defaultConfig);
    newConfig.setLabel(partner.getShortName());
    newConfig.setCustomJobTrackingId(customerId);
    newConfig.setCustomJobTrackingType("tms-bill");
    newConfig = jobTrackingRuleConfigLogic.createJobTrackingProjectRuleConfig(client, company, newConfig);

    customer.setTmsJobTrackingRuleId(newConfig.getId());
    customer.setTmsJobTrackingRuleLabel(newConfig.getLabel());
    customer = saveTMSCustomer(client, company, customer);

    model.mergeTMSPartnerToModel(partner);
    model.mergeTMSCustomerToModel(customer);
    return model;
  }

  public boolean mergeTMSCustomers(ClientContext client, ICompany company, Long targetId, List<Long> partnerDelIds) {
    //TODO: review code
//    TMSCustomer targetCustomer = loadTMSCustomerByPartnerId(client, company, targetId);
//    TMSPartner targetPartner = partnerLogic.getTMSPartner(client, company,targetCustomer.getTmsPartnerId());
//    
//    List<TMSPartnerPermission> targetPermissions = permissionRepo.findByPartnerId(company.getId(), targetId);
//    List<Long> targetPermissionUserIds = Collections.transform(targetPermissions,(targetPermission)-> targetPermission.getUserId());
//    List<TMSPartnerPermission> permissionDels = permissionRepo.findByPartnerId(company.getId(), partnerDelIds);
//    List<TMSPartnerPermission> permissions = new ArrayList<>();
//    for(TMSPartnerPermission permission : permissionDels) {
//      if(!targetPermissionUserIds.contains(permission.getUserId())) {
//        TMSPartnerPermission clone = DataSerializer.JSON.clone(permission);
//        clone.setId(null);
//        clone.setTmsPartnerId(targetId);
//        clone.set(client, company.getId());
//        permissions.add(clone);
//      }
//    }
//    tmsBillLogic.getTmsBillRepo().updateTMSBillCustomer(targetPartner.getShortName(), targetId, partnerDelIds);
//    Set<String> emails = targetPartner.getEmails();
//    if(emails == null) emails = new HashSet<>();
//    permissionRepo.saveAll(permissions);
//    permissionRepo.deleteAll(permissionDels);
//    saveTMSPartner(client, company, targetPartner);
//    deleteTMSCustomers(client, company, partnerDelIds);
    return true;
  }
}