package cloud.datatp.tms.partner;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cloud.datatp.tms.partner.entity.TMSCustomer;
import cloud.datatp.tms.partner.entity.TMSPartner;
import cloud.datatp.tms.partner.model.TMSCustomerModel;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.service.BaseComponent;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.MapObject;

@Service("TMSCustomerService")
public class TMSCustomerService extends BaseComponent {

  @Autowired
  private TMSCustomerLogic customerLogic;

  @Transactional(readOnly=true)
  public TMSCustomer loadTMSCustomerById(ClientContext client, ICompany company, Long id) {
    return customerLogic.loadTMSCustomerById(client, company, id);
  }

  @Transactional(readOnly=true)
  public TMSCustomer loadTMSCustomerByPartnerId(ClientContext client, ICompany company, Long tmsPartnerId) {
    return customerLogic.loadTMSCustomerByPartnerId(client, company, tmsPartnerId);
  }

  @Transactional(readOnly=true)
  public TMSPartner loadTMSPartnerByCustomerId(ClientContext client, ICompany company, Long customerId) {
    return customerLogic.loadTMSPartnerByCustomerId(client, company, customerId);
  }

  @Transactional(readOnly=true)
  public TMSCustomerModel loadTMSCustomerModel(ClientContext client, ICompany company, Long id) {
    return customerLogic.loadTMSCustomerModel(client, company, id);
  }

  @Transactional
  public TMSCustomerModel saveTMSCustomerModel(ClientContext client, ICompany company, TMSCustomerModel model) {
    return customerLogic.saveTMSCustomerModel(client, company, model);
  }


  @Transactional(readOnly=true)
  public List<SqlMapRecord> searchTMSCustomers(ClientContext client, ICompany company, SqlQueryParams params) {
    return customerLogic.searchTMSCustomers(client, company, params);
  }

  @Transactional
  public TMSCustomer saveTMSCustomer(ClientContext client, ICompany company, TMSCustomer customer) {
    return customerLogic.saveTMSCustomer(client, company, customer);
  }

  @Transactional
  public TMSPartner saveTMSPartner(ClientContext client, ICompany company, TMSPartner partner) {
    return customerLogic.saveTMSPartner(client, company, partner);
  }

  @Transactional
  public List<TMSCustomerModel> saveTMSCustomers(ClientContext client, ICompany company, List<TMSCustomerModel> models) {
    return customerLogic.saveTMSCustomers(client, company, models);
  }

  @Transactional
  public boolean changeTMSCustomerStorageState(ClientContext client, ICompany company, ChangeStorageStateRequest req) {
    return customerLogic.changeTMSCustomerStorageState(client, company, req);
  }

  @Transactional
  public boolean deleteTMSCustomers(ClientContext client, ICompany company, List<Long> ids) {
    return customerLogic.deleteTMSCustomers(client, company, ids);
  }

  @Transactional
  public MapObject createMapObjectPreview(ClientContext client, ICompany company, MapObject bfsOnePartner, String code) {
    return customerLogic.createMapObjectPreview(client, company, bfsOnePartner,  code);
  }

  @Transactional
  public TMSCustomerModel createTMSCustomerRuleFromDefault(ClientContext client, ICompany company, Long customerId) {
    return customerLogic.createTMSCustomerRuleFromDefault(client, company, customerId);
  }

  @Transactional
  public TMSCustomerModel updateTMSCustomerJobTrackingRuleRef(ClientContext client, ICompany company, Long customerId, Long jobTrackingProjectRuleId) {
    return customerLogic.updateTMSCustomerJobTrackingRuleRef(client, company, customerId, jobTrackingProjectRuleId);
  }

  @Transactional
  public boolean mergeTMSCustomers(ClientContext client, ICompany company, Long targetId, List<Long> customerDelIds) {
    return customerLogic.mergeTMSCustomers(client, company, targetId, customerDelIds);
  }
}
