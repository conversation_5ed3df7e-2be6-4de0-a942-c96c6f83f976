package cloud.datatp.fleet.vehicle.entity;

import java.io.Serial;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import cloud.datatp.gps.entity.GPSConfig;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.security.client.ClientContext;
import net.datatp.module.company.entity.Company;
import net.datatp.module.company.entity.CompanyEntity;
import net.datatp.util.ds.MapObject;

@Entity
@Table(
  name = Vehicle.TABLE_NAME,
  uniqueConstraints = { @UniqueConstraint( name = Vehicle.TABLE_NAME + "_code", columnNames = {"company_id", "code"}), },
  indexes = { @Index(name = Vehicle.TABLE_NAME + "_code_idx", columnList = "code")}
)
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor
@Getter @Setter
public class Vehicle extends CompanyEntity {
  @Serial
  private static final long serialVersionUID = 1L;

  public static final String TABLE_NAME = "lgc_fleet_vehicle";
  
  public enum VehicleStatus {
    PLAN_ASSIGNED, NOT_BUSY, ON_LEAVE
  }

  @NotNull
  private String code;
  
  private String label;

  @Column(name = "license_plate")
  private String licensePlate;
  
  //Ro Mooc
  @Column(name = "trailer_number")
  private String trailerNumber;

  @Column(name = "owner_account_id")
  private Long ownerAccountId;
  
  @Column(name = "owner_account_full_name")
  private String ownerAccountFullName;
  
  @Column(name = "asset_id")
  private Long assetId;
  
  @Column(name = "asset_label")
  private String assetLabel;
  
  @Column(name = "vehicle_type", length = 50)
  private String vehicleType; // Temporary field
  
  @Column(name = "dimension_x")
  private double dimensionX;

  @Column(name = "dimension_y")
  private double dimensionY;

  @Column(name = "dimension_z")
  private double dimensionZ;

  @Column(name = "dimension_unit")
  private String dimensionUnit;
  
  @Column(name = "volume")
  private double volume;
  
  @Column(name = "volume_unit")
  private String volumeUnit;
  
  @Column(name = "gross_weigh")
  private double grossWeigh;
  
  @Column(name = "gross_weigh_unit")
  private String grossWeighUnit;
  
  @Column(name = "transporter_full_name")
  private String transporterFullName;
  
  @Column(name = "transporter_id")
  private Long transporterId;

  @Column(name = "oil_consumption")
  private double oilConsumption;
  
//  @Column(name = "internal_vehicle")
//  private boolean internalVehicle;
  
  @Column(name = "gps_config_id")
  private Long   gpsConfigId;
  
  @Column(name = "gps_plugin_name")
  private String gpsPluginName;

  private String note;
  
  private VehicleStatus status = VehicleStatus.NOT_BUSY;
  
  public Vehicle withGPSConfig(GPSConfig config) {
    this.gpsConfigId   = config.getId();
    this.gpsPluginName = config.getGpsPluginName();
    return this;
  }

  @Override
  public void set(ClientContext client, Company company) {
    super.set(client, company);
  }

  public Vehicle mergeVehicle(MapObject vehicle) {
    this.label = vehicle.getString("label");
    this.vehicleType = vehicle.getString("vehicleType");
    this.code = vehicle.getString("code");
    this.licensePlate = vehicle.getString("licensePlate");
    this.gpsConfigId = vehicle.getLong("gpsConfigId",null);
    this.ownerAccountId = vehicle.getLong("ownerAccountId",null);
    this.ownerAccountFullName = vehicle.getString("ownerAccountFullName");
    this.transporterId = vehicle.getLong("transporterId",null);
    this.transporterFullName = vehicle.getString("transporterFullName");
    this.vehicleType = vehicle.getString("vehicleType");
    this.dimensionX = vehicle.getDouble("dimensionX", 0.0);
    this.dimensionY = vehicle.getDouble("dimensionY", 0.0);
    this.dimensionZ = vehicle.getDouble("dimensionZ", 0.0);
    this.dimensionUnit = vehicle.getString("dimensionUnit");
    this.volume = vehicle.getDouble("volume",0D);
    this.volumeUnit = vehicle.getString("volumeUnit");
    this.grossWeigh = vehicle.getDouble("grossWeigh",0.0);
    this.grossWeighUnit = vehicle.getString("grossWeighUnit");
    this.gpsPluginName = vehicle.getString("gpsPluginName");
    this.oilConsumption = vehicle.getDouble("oilConsumption",0.0);
    return this;
  }
}