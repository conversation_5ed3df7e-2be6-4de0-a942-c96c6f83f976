package cloud.datatp.vendor.groovy;

import org.springframework.context.ApplicationContext

import net.datatp.lib.executable.ExecutableContext;
import net.datatp.lib.executable.Executor;
import net.datatp.module.data.db.ExecutableSqlBuilder;
import net.datatp.security.client.DataScope;
import net.datatp.util.ds.MapObject;

public class TMSVendorBillSql extends Executor {
  static public class SelectTMSVendor<PERSON>ill extends ExecutableSqlBuilder {
    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams = ctx.getParam("sqlParams");
      String dataScope    = sqlParams.getString("dataScope");
      String ownerFilter  = "";
      if(DataScope.Owner.toString().equals(dataScope)) {
        ownerFilter =  """AND (responsible_account_id = ${sqlParams.get("responsibleAccountId")} OR vendor_account_id = ${sqlParams.get("responsibleAccountId")})""";
      }
      int maxReturn       = sqlParams.getInteger('maxReturn', 3000);
      
      String query = """
        WITH tms_bill AS (
         SELECT 
           lgc_tms_bill.*, 
           lgc_tms_bill_fee.fixed_payment,
           lgc_tms_bill_fee.extra_payment, 
           lgc_tms_bill_fee.total_payment,
           lgc_tms_bill_forwarder_transport.vendor_full_name,
           lgc_tms_bill_forwarder_transport.vendor_id,
           lgc_tms_bill_forwarder_transport.container_no,
           lgc_tms_bill_forwarder_transport.seal_no,
           lgc_tms_bill_forwarder_transport.hwb_no,
           lgc_tms_bill_forwarder_transport.mode,
           lgc_tms_bill_forwarder_transport.truck_type,
           lgc_tms_bill_forwarder_transport.booking_code,
           lgc_tms_bill.delivery_plan                         AS date_time
         FROM lgc_tms_bill 
         LEFT JOIN lgc_tms_bill_forwarder_transport 
           ON lgc_tms_bill_forwarder_transport.id           = lgc_tms_bill.tms_bill_forwarder_transport_id
         LEFT JOIN lgc_tms_bill_fee 
           ON lgc_tms_bill_fee.id                           = lgc_tms_bill.tms_bill_fee_id
         WHERE 
           ${FILTER_BY_PARAM('companyId', sqlParams)}
        ),
        vendor_bill_attachment AS (
          SELECT
            entity_id AS vendor_bill_id,
            COUNT(entity_id) AS vendor_attach_file_total
          FROM lgc_tms_bill_attachment
          WHERE entity_name = 'TMSVendorBill'
          GROUP BY entity_id
        )
        SELECT * 
          FROM (
           SELECT
           vendor_bill.id                                         AS id,
           vendor_bill.vendor_authorization                       AS vendor_authorization,
           vendor_bill.task_id                                    AS task_id,
           vendor_bill.created_by                                 AS created_by,
           vendor_bill.created_time                               AS created_time,
           vendor_bill.modified_by                                AS modified_by,
           vendor_bill.modified_time                              AS modified_by,
           vendor_bill.container_no                               AS container_no,
           vendor_bill.seal_no                                    AS seal_no,
           vendor_bill.quantity                                   AS quantity,
           vendor_bill.quantity_unit                              AS quantity_unit,
           vendor_bill.weight                                     AS weight,
           vendor_bill.volume                                     AS volume,
           vendor_bill.volume_note                                AS volume_note,
           tms_bill.company_id                                    AS company_id,
           vendor_bill.feedback                                   AS feedback,
           vendor_bill.description                                AS description,
           --vendor_bill.pickup_address                             AS pickup_address,
           --vendor_bill.delivery_address                           AS delivery_address,
           CASE
             WHEN vendor_bill.last_task_commenter_account_id = ${sqlParams.getLong("responsibleAccountId", null)} THEN 'REPLIED'
             WHEN vendor_bill.task_id IS NULL THEN 'N/A'
             ELSE                                  'NOT_REPLIED'
           END                                                    AS comment_status,
           CASE
                WHEN tms_bill.version <> vendor_bill.tms_bill_last_version THEN TRUE
                ELSE FALSE
           END                                                    AS outdated,
           vendor_bill.estimate_time                              AS estimate_time,
           vendor_bill.modified_estimate_time                     AS modified_estimate_time,
           vendor_bill.delayed_time                               AS delayed_time,

           CASE
             WHEN vendor_bill.cost > 0 THEN 'true'
             ELSE                           'false'
           END                                                    AS cost_entered,
           vendor_bill.cost                                       AS cost,
           vendor_bill.extra                                      AS extra, 
           vendor_bill.fixed                                      AS fixed, 
           vendor_bill.status                                     AS status,
           vendor_bill.issue                                      AS issue,
           TO_CHAR(vendor_bill.xlsx_last_update, 'dd/MM/yyyy')    AS xlsx_last_update,
  
           lgc_fleet_vehicle_fleet.owner_account_id               AS vendor_account_id,
           tms_bill.label                                         AS file_no,
           tms_bill.container_no                                  AS tms_container_no,
           tms_bill.seal_no                                       AS tms_seal_no,
           tms_bill.label                                         AS label,
           tms_bill.mode,
           tms_bill.office,
           tms_bill.hwb_no,
           tms_bill.truck_type,
           tms_bill.vendor_full_name                              AS vendor_full_name,
           tms_bill.vendor_id                                     AS vendor_id, 
           tms_bill.date_time                                     AS delivery_plan,
           tms_bill.time                                          AS time,
           tms_bill.responsible_account_id                        AS responsible_account_id,
           tms_bill.responsible_full_name                         AS responsible_full_name,
           tms_bill.sender_address                                AS pickup_address,
           tms_bill.sender_inv_address                            AS pickup_inv_address,
           tms_bill.receiver_address                              AS delivery_address,
           tms_bill.receiver_inv_address                          AS delivery_inv_address,
           tms_bill.code                                          AS tms_bill_code,
           tms_bill.customer_full_name                            AS customer_full_name,
           tms_bill.customer_id                                   AS customer_id,
           --tms_bill.container_no                                  AS container_no,
           tms_bill.booking_code                                  AS booking_code,
           
           vendor_bill.ops_account_id                             AS ops_account_id,
           vendor_bill.ops_account_full_name                      AS ops_account_full_name,
           vendor_bill.ops_mobile                                 AS ops_mobile,
           vendor_bill.ops_identification_no                      AS ops_identification_no,
           vendor_bill.ops_note                                   AS ops_note,
           vendor_bill.ops_status                                 AS ops_status,
           vendor_bill.attach_status                              AS attach_status,
           vendor_bill_attachment.vendor_attach_file_total        AS vendor_attach_file_total,
           --vendor_bill.storage_state                            AS storage_state,
           vendor_bill.vendor_cost_status                         AS vendor_cost_status,
           tms_bill.storage_state                                 AS storage_state,
           tms_bill.id                                            AS tms_bill_id,
           tms_bill.fixed_payment                                 AS tms_bill_fixed_payment,
           tms_bill.extra_payment                                 AS tms_bill_extra_payment,
           tms_bill.total_payment                                 AS tms_bill_total_payment,
           tms_bill.description                                   AS tms_bill_description
        FROM lgc_tms_vendor_bill vendor_bill
        LEFT JOIN tms_bill ON vendor_bill.tms_bill_id = tms_bill.id
        LEFT JOIN lgc_fleet_vehicle_fleet ON lgc_fleet_vehicle_fleet.id = tms_bill.vendor_id
        LEFT JOIN vendor_bill_attachment ON vendor_bill_attachment.vendor_bill_id = vendor_bill.id
        
      ) AS lgc_tms_vendor_bill
      WHERE 
       ${FILTER_BY_STORAGE_STATE(sqlParams)}
       AND delivery_plan is not null
       ${AND_SEARCH_BY_PARAMS(['file_no'], 'search', sqlParams)}
       ${ownerFilter}
       ${AND_FILTER_BY_PARAM("id", "ids", sqlParams)}
       ${AND_FILTER_BY_PARAM("tms_bill_id", "tmsBillIds", sqlParams)}
       ${AND_FILTER_BY_RANGE("delivery_plan", "deliveryPlan", sqlParams)}
      ORDER BY delivery_plan DESC, vendor_full_name, booking_code
      LIMIT ${maxReturn}
      """
      return query;
    }
  }
  
  static public class SelectTMSVendorBillNotLinkTMSBill extends ExecutableSqlBuilder {
    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams = ctx.getParam("sqlParams");
      int maxReturn       = sqlParams.getInteger('maxReturn', 1000);
      
      String query = """
        SELECT *
        FROM lgc_tms_vendor_bill vendor_bill
        WHERE
         ${FILTER_BY_STORAGE_STATE(sqlParams)}
         ${AND_FILTER_BY_PARAM("companyId", sqlParams)}
         AND tms_bill_id IS NULL
         ${AND_SEARCH_BY_PARAMS(['file_no'], 'search', sqlParams)}
        ORDER BY delivery_plan DESC
        LIMIT ${maxReturn}
        """
      return query;
    }
  }
  
  static public class FindVendorBillTrackingByTrackingIds extends ExecutableSqlBuilder {
    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams = ctx.getParam("sqlParams");
      String query = """
        SELECT *
        FROM lgc_tms_vendor_bill_tracking tracking
        WHERE ${FILTER_BY_PARAM("vendor_bill_id", "vendorBillIds", sqlParams)}
        """
      return query;
    }
  }


  public TMSVendorBillSql() {
    register(new SelectTMSVendorBill());
    register(new SelectTMSVendorBillNotLinkTMSBill());
    register(new FindVendorBillTrackingByTrackingIds());
  }
}