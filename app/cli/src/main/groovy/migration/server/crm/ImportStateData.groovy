package migration.server.crm

import cloud.datatp.fforwarder.sales.partner.CustomerLeadsLogic
import lib.data.RunnableReporter
import lib.data.ServiceRunnable
import lib.data.ServiceRunnableSet
import net.datatp.module.data.xlsx.XSLXToMapObjectParser
import net.datatp.module.groovy.ServerScriptContext
import net.datatp.module.resource.location.LocationService
import net.datatp.module.resource.location.entity.Country
import net.datatp.module.resource.location.entity.State
import net.datatp.security.client.ClientContext
import net.datatp.util.dataformat.DataSerializer
import net.datatp.util.ds.MapObject
import net.datatp.util.io.IOUtil


public class ImportStateSet extends ServiceRunnableSet {
    public ImportStateSet() {
        super("""Import State Data""");

        ServiceRunnable dataInit = new ServiceRunnable("Import State Data") {
            @Override
            public void run(RunnableReporter reporter, ServerScriptContext scriptCtx) {
                ClientContext client = scriptCtx.getClientCtx();
                LocationService locationService = scriptCtx.getService(LocationService.class);
                final String dataDir = scriptCtx.getDataDir();

                String res = "file:" + dataDir + "/crm/data.xlsx";
                InputStream is = IOUtil.loadResource(res);
                final byte[] data = IOUtil.getStreamContentAsBytes(is);
                XSLXToMapObjectParser parser = new XSLXToMapObjectParser(data);
                parser.parse(parser.getFirstSheetName());
                List<MapObject> recordExcel = parser.getRows();

                Country country = locationService.loadCountryByCode(client, "IN");
                for (MapObject record : recordExcel) {
                    String code = record.getString("Code", "");
                    String name = record.getString("Name", "").toUpperCase(Locale.ROOT);
                    State state = new State(code, name);
                    state.withCountry(country);
                    locationService.saveState(client, state);
                }
            }
        };

        addRunnable(dataInit);
    }
}

ServerScriptContext scriptCtx = (ServerScriptContext) CONTEXT;
RunnableReporter reporter = new RunnableReporter("service", "V1_0_0_2024-10-09")
ImportStateSet migration = new ImportStateSet();
migration.run(reporter, scriptCtx);