package migration.server.crm

import cloud.datatp.fforwarder.core.template.CRMUserRoleService
import cloud.datatp.fforwarder.core.template.entity.CrmUserRole
import lib.data.RunnableReporter
import lib.data.ServiceRunnable
import lib.data.ServiceRunnableSet
import net.datatp.module.account.AccountService
import net.datatp.module.account.entity.Account
import net.datatp.module.company.CompanyService
import net.datatp.module.company.entity.Company
import net.datatp.module.core.security.SecurityService
import net.datatp.module.core.security.entity.App
import net.datatp.module.core.security.entity.AppPermission
import net.datatp.module.data.db.entity.ICompany
import net.datatp.module.groovy.ServerScriptContext
import net.datatp.module.hr.HRService
import net.datatp.module.hr.NewEmployeeModel
import net.datatp.module.hr.entity.Employee
import net.datatp.module.hr.entity.HRDepartment
import net.datatp.module.hr.http.Params
import net.datatp.security.client.Capability
import net.datatp.security.client.ClientContext
import net.datatp.security.client.DataScope

public class CreateCompanySet extends ServiceRunnableSet {
    public CreateCompanySet() {
        super("""Create Company""");

        ServiceRunnable dataInit = new ServiceRunnable("Create company") {
            @Override
            public void run(RunnableReporter reporter, ServerScriptContext scriptCtx) {
                AccountService accountService = scriptCtx.getService(AccountService.class)
                CompanyService companyService = scriptCtx.getService(CompanyService.class);
                SecurityService securityService = scriptCtx.getService(SecurityService.class);
                CRMUserRoleService crmUserRoleService = scriptCtx.getService(CRMUserRoleService.class);
                HRService hrService = scriptCtx.getService(HRService.class);
                ClientContext client = scriptCtx.getClientCtx();
                ICompany parentCompany = scriptCtx.getCompany();

                List<String> loginIds = List.of("dan", "tony.nguyen", "pricing3.del.in", "amardeep.del.in", "it-bee-india");


                App userSaleApp = securityService.getApp(client, 'logistics', 'user-logistics-sales');
                App userPriceApp = securityService.getApp(client, 'logistics', 'user-logistics-prices');
                App companySaleApp = securityService.getApp(client, 'logistics', 'company-logistics-sales');
                App companyPriceApp = securityService.getApp(client, 'logistics', 'company-logistics-prices');
                App partnerApp = securityService.getApp(client, 'logistics', 'user-partner-logistics-crm');
                App spaceApp = securityService.getApp(client, 'user', 'my-employee-space');
                App adminApp = securityService.getApp(client, 'admin', 'admin-space');
                App companyApp = securityService.getApp(client, 'company', 'company-space');
                App userAsset = securityService.getApp(client, 'company', 'user-asset');
                App userKpi = securityService.getApp(client, "hr", "user-kpi");

                Company company = new Company("bee-in", "BEE IN", "BEE INDIA");


                List<Long> employeeIds = new ArrayList<>();
                company.setParentId(parentCompany.getId())
                company.setParentLabel(parentCompany.getLabel())
                companyService.createCompany(client, company);
                HRDepartment hrDepartment = new HRDepartment("SALES", "SALES", "SALES");
                hrService.saveHRDepartment(client, company, hrDepartment);

                List<Long> deletePermissionIds = new ArrayList<>();
                List<Long> deleteEmployeeIds = new ArrayList<>();

                for (String loginId : loginIds) {
                    Account account = accountService.getAccount(client, loginId);
                    if (loginId == "dan" || loginId == "tony.nguyen") {
                        assignAppPermission(securityService, client, company, companySaleApp, account);
                        assignAppPermission(securityService, client, company, companyPriceApp, account);
                        assignAppPermission(securityService, client, company, adminApp, account);
                        assignAppPermission(securityService, client, company, companyApp, account);
                    } else {
                        AppPermission appPermissionSale = securityService.getAppPermission(client, userSaleApp.getId(), parentCompany.getId(), account.getId());
                        AppPermission appPermissionPrice = securityService.getAppPermission(client, userPriceApp.getId(), parentCompany.getId(), account.getId());
                        AppPermission appPermissionSpace = securityService.getAppPermission(client, spaceApp.getId(), parentCompany.getId(), account.getId());
                        AppPermission appPermissionAsset = securityService.getAppPermission(client, userAsset.getId(), parentCompany.getId(), account.getId());
                        AppPermission appPermissionKpi = securityService.getAppPermission(client, userKpi.getId(), parentCompany.getId(), account.getId());
                        AppPermission appPermissionPartner = securityService.getAppPermission(client, partnerApp.getId(), parentCompany.getId(), account.getId());
                        deletePermissionIds.add(appPermissionSale.getId())
                        deletePermissionIds.add(appPermissionPrice.getId());
                        deletePermissionIds.add(appPermissionSpace.getId());
                        if (appPermissionAsset != null) deletePermissionIds.add(appPermissionAsset.getId());
                        if (appPermissionKpi != null) deletePermissionIds.add(appPermissionKpi.getId());
                        if (appPermissionPartner != null) deletePermissionIds.add(appPermissionPartner.getId());
                        Employee employee = hrService.getEmployeeByLoginId(client, parentCompany, loginId);
                        deleteEmployeeIds.add(employee.getId());
                        CrmUserRole crmUserRole = crmUserRoleService.getByAccountId(client, account.getId());
                        crmUserRole.withCompany(company);
                        crmUserRole.withDepartment(hrDepartment);
                        crmUserRole.setType(CrmUserRole.UserType.OVERSEAS);
                        crmUserRoleService.saveCrmUserRole(client, crmUserRole);
                    };
                    NewEmployeeModel newEmployeeModel = new NewEmployeeModel();
                    newEmployeeModel.setAccount(account);
                    client.setCompany(company);
                    Employee employee = hrService.createEmployee(client, company, newEmployeeModel);
                    hrService.saveEmployee(client, company, employee);
                    employeeIds.add(employee.getId());
                    client.setCompany(parentCompany);

                    assignAppPermission(securityService, client, company, spaceApp, account);
                    assignAppPermission(securityService, client, company, userSaleApp, account);
                    assignAppPermission(securityService, client, company, userPriceApp, account);
                    if (loginId == "it-bee-india") {
                        assignAppPermission(securityService, client, company, partnerApp, account);
                    } else {
                        assignAppPermission(securityService, client, company, userKpi, account);
                        assignAppPermission(securityService, client, company, userAsset, account);
                    }
                }

                Params.HrMembershipRequest membershipRequest = new Params.HrMembershipRequest();
                membershipRequest.setEmployeeIds(employeeIds);
                membershipRequest.setDepartmentId(hrDepartment.getId());
                hrService.createHRDepartmentRelations(client, company, membershipRequest);

                securityService.deletePermissionsById(client, parentCompany, deletePermissionIds);
                HRDepartment department = hrService.getHRDepartmentByName(client, parentCompany, "indian");
                hrService.deleteHRDepartmentRelations(client, parentCompany, department.getId(), deleteEmployeeIds);
                hrService.deleteEmployees(client, parentCompany, deleteEmployeeIds);
                hrService.deleteHRDepartment(client, parentCompany, department.getId());
            }
        };

        addRunnable(dataInit);
    }

    private void assignAppPermission(SecurityService securityService, ClientContext client, Company company, App app, Account account) {
        AppPermission appPermission = securityService.getAppPermission(client, app.getId(), company.getId(), account.getId());
        if (appPermission == null) {
            appPermission = new AppPermission();
            appPermission.setCompanyId(company.getId());
            appPermission.setAccountId(account.getId());
            appPermission.setAppId(app.getId());
            if (account.getLoginId() == 'dan' || account.getLoginId() == 'tony.nguyen') {
                appPermission.setCapability(Capability.Admin);
                appPermission.setDataScope(DataScope.All);
            } else {
                appPermission.setCapability(Capability.Write);
                appPermission.setDataScope(DataScope.Owner);
            }
            appPermission.setCompanyId(company.getId());
            securityService.saveAppPermission(client, company, appPermission);
        }
    }
}

ServerScriptContext scriptCtx = (ServerScriptContext) CONTEXT;
RunnableReporter reporter = new RunnableReporter("service", "V1_0_0_2024-10-09")
CreateCompanySet migration = new CreateCompanySet();
migration.run(reporter, scriptCtx);