package migration.server.tms

import cloud.datatp.fleet.vehicle.VehicleFleetLogic
import cloud.datatp.fleet.vehicle.entity.VehicleFleet
import cloud.datatp.fleet.vehicle.repository.VehicleFleetRepository

import lib.data.RunnableReporter
import lib.data.ServiceRunnable
import lib.data.ServiceRunnableSet
import net.datatp.module.account.AccountService
import net.datatp.module.account.entity.Account
import net.datatp.module.core.security.entity.AppPermission
import net.datatp.module.data.db.entity.ICompany
import net.datatp.module.partner.entity.PartnerAccount
import net.datatp.module.partner.repository.PartnerAccountRepository
import net.datatp.security.client.ClientContext
import net.datatp.module.core.security.SecurityService
import net.datatp.module.data.xlsx.XSLXToMapObjectParser
import net.datatp.module.groovy.ServerScriptContext
import net.datatp.util.ds.MapObject
import net.datatp.util.io.IOUtil


public class MigrateTaxCodePartnerDataSet extends ServiceRunnableSet {
  public MigrateTaxCodePartnerDataSet() {
    super(
            """Account Vendor Migration"""
    );



    ServiceRunnable dataInit = new ServiceRunnable("Account Vendor Migration") {


      @Override
      public void run(RunnableReporter reporter, ServerScriptContext scriptCtx) {
        final String dataDir = scriptCtx.getDataDir();
        ClientContext clientContext = scriptCtx.getClientCtx();
        ICompany company = scriptCtx.getCompany();
        AccountService accountService = scriptCtx.getService(AccountService.class);
        PartnerAccountRepository partnerAccountRepository = scriptCtx.getService(PartnerAccountRepository.class);
        VehicleFleetLogic vehicleFleetLogic = scriptCtx.getService(VehicleFleetLogic.class);
        VehicleFleetRepository vehicleFleetRepo = scriptCtx.getService(VehicleFleetRepository.class);

        try {
          InputStream is = IOUtil.loadResource("file:" + dataDir + "/tms/vehicle-fleet-beehph_new.xlsx");
          final byte[] data = IOUtil.getStreamContentAsBytes(is);
          XSLXToMapObjectParser parser = new XSLXToMapObjectParser(data);
          parser.parse("Sheet1");
          List<MapObject> records = parser.getRows();
          List<PartnerAccount> partnerAccounts = new ArrayList<>();
          List<VehicleFleet> vehicleFleets = new ArrayList<>();

          for(MapObject record: records) {
            String idStr = record.getString("id");
            Long id = (long) Double.parseDouble(idStr);
            String bfsOneCode = record.getString("bfs_one_code");
            String MST = record.getString("MST");

            Account account = accountService.getAccountByLoginId(bfsOneCode);
            PartnerAccount partnerAcc = partnerAccountRepository.getByAccountId(company.getId(), account.getId());
            if(partnerAcc != null) {
              partnerAcc.setTaxCode(MST);
              partnerAcc.set(clientContext, company);
              partnerAccounts.add(partnerAcc);
            }
            VehicleFleet vehicleFleet = vehicleFleetLogic.getVehicleFleetById(clientContext, company, id);
            if(vehicleFleet != null) {
              vehicleFleet.setTaxCode(MST);
              vehicleFleet.set(clientContext, company);
              vehicleFleets.add(vehicleFleet);
            }
          }
//          partnerAccountRepository.saveAll(partnerAccounts);
//          vehicleFleetRepo.saveAll(vehicleFleets);
        } catch (Exception e) {
          throw new RuntimeException(e);
        }
      }
    };

    addRunnable(dataInit);
  }
}

ServerScriptContext scriptCtx = (ServerScriptContext) CONTEXT;
RunnableReporter reporter = new RunnableReporter("service", "V1_0_0_2024-10-09")
MigrateTaxCodePartnerDataSet migration = new MigrateTaxCodePartnerDataSet();
migration.run(reporter, scriptCtx);
