package migration.server.tms

import lib.data.RunnableReporter
import lib.data.ServiceRunnable
import lib.data.ServiceRunnableSet
import net.datatp.module.account.AccountService
import net.datatp.module.account.entity.Account
import net.datatp.module.core.security.entity.AppPermission
import net.datatp.module.data.db.entity.ICompany
import net.datatp.module.hr.HRService
import net.datatp.module.hr.entity.Employee
import net.datatp.module.partner.repository.PartnerAccountRepository
import net.datatp.module.partner.entity.PartnerAccount
import net.datatp.security.client.Capability
import net.datatp.security.client.ClientContext
import net.datatp.module.core.security.SecurityService
import net.datatp.module.data.xlsx.XSLXToMapObjectParser
import net.datatp.module.groovy.ServerScriptContext
import net.datatp.security.client.DataScope
import net.datatp.util.ds.MapObject
import net.datatp.util.io.IOUtil
import org.hibernate.cache.spi.access.AccessType


public class MigratePartnerAccountDataSet extends ServiceRunnableSet {
  public MigratePartnerAccountDataSet() {
    super(
            """Account Vendor Migration"""
    );



    ServiceRunnable dataInit = new ServiceRunnable("Account Vendor Migration") {

      private dropEmployee(RunnableReporter reporter, ServerScriptContext scriptCtx){
        final String dataDir = scriptCtx.getDataDir();
        ClientContext clientContext = scriptCtx.getClientCtx();
        ICompany company = scriptCtx.getCompany();
        AccountService accountService = scriptCtx.getService(AccountService.class);
        HRService hrService = scriptCtx.getService(HRService.class);
        SecurityService securityService = scriptCtx.getService(SecurityService.class);

        try {
          InputStream is = IOUtil.loadResource("file:" + dataDir + "/tms/vehicle-fleet-beehph_new.xlsx");
          final byte[] data = IOUtil.getStreamContentAsBytes(is);
          XSLXToMapObjectParser parser = new XSLXToMapObjectParser(data);
          parser.parse("Sheet1");
          List<MapObject> records = parser.getRows();
          List<Long> employeesIdDels = new ArrayList<>();
          List<Long> appPermissionIds = new ArrayList<>();
          for(MapObject record: records) {
            String idStr = record.getString("id");
            Long id = (long) Double.parseDouble(idStr);

            String bfsOneCode = record.getString("bfs_one_code");

            Employee employee = hrService.getEmployeeByLoginId(clientContext, company, bfsOneCode);
            Account account = accountService.getAccountById(clientContext, employee.getAccountId());
            AppPermission appPermission2 = securityService.getAppPermission(clientContext, 2, company.getId(), account.getId());
            AppPermission appPermission65 = securityService.getAppPermission(clientContext, 65,company.getId(), account.getId());
            if(appPermission2 != null) {
              appPermissionIds.add(appPermission2.getId());
            }
            if(appPermission65 != null) {
              appPermissionIds.add(appPermission65.getId());
            }
            if(employee != null) {
              employeesIdDels.add(employee.getId())
            }
          }
          hrService.deleteEmployees(clientContext, company, employeesIdDels);
          securityService.deletePermissionsById(clientContext, company, appPermissionIds);
        } catch (Exception e) {
          throw new RuntimeException(e);
        }
      }


      @Override
      public void run(RunnableReporter reporter, ServerScriptContext scriptCtx) {
        final String dataDir = scriptCtx.getDataDir();
        ClientContext clientContext = scriptCtx.getClientCtx();
        ICompany company = scriptCtx.getCompany();
        AccountService accountService = scriptCtx.getService(AccountService.class);
        PartnerAccountRepository partnerAccountRepository = scriptCtx.getService(PartnerAccountRepository.class);
        partnerAccountRepository.deleteById(1);
        dropEmployee(reporter, scriptCtx);

        try {
          InputStream is = IOUtil.loadResource("file:" + dataDir + "/tms/vehicle-fleet.xlsx");
          final byte[] data = IOUtil.getStreamContentAsBytes(is);
          XSLXToMapObjectParser parser = new XSLXToMapObjectParser(data);
          parser.parse("Sheet1");
          List<MapObject> records = parser.getRows();
          List<PartnerAccount> partnerAccounts = new ArrayList<>();
          List<AppPermission> appPermissionList = new ArrayList<>();
          SecurityService securityService = scriptCtx.getService(SecurityService.class);

          for(MapObject record: records) {
            String idStr = record.getString("id");
            Long id = (long) Double.parseDouble(idStr);
            String bfsOneCode = record.getString("bfs_one_code");
            Account account = accountService.getAccountByLoginId(clientContext, bfsOneCode);
            PartnerAccount partnerAccount = new PartnerAccount();
            partnerAccount.setBfsoneReference(bfsOneCode);
            partnerAccount.setName(account.getFullName());
            partnerAccount.setLabel(account.getFullName());
            partnerAccount.setAccountId(account.getId());
            partnerAccount.set(clientContext, company);
            partnerAccounts.add(partnerAccount);

            AppPermission appPermissionMySpace = new AppPermission();
            appPermissionMySpace.setCompanyId(company.getId());
            appPermissionMySpace.setAccountId(account.getId())
            appPermissionMySpace.setAppId(87);
            appPermissionMySpace.setCapability(Capability.Write);
            appPermissionMySpace.setDataScope(DataScope.Company);
            appPermissionMySpace.setAccessType(net.datatp.security.client.AccessType.Partner);
            appPermissionList.add(appPermissionMySpace);

            AppPermission appPermissionVendorBill = new AppPermission();
            appPermissionVendorBill.setCompanyId(company.getId());
            appPermissionVendorBill.setAccountId(account.getId())
            appPermissionVendorBill.setAppId(65);
            appPermissionVendorBill.setCapability(Capability.Write);
            appPermissionVendorBill.setDataScope(DataScope.Owner);
            appPermissionVendorBill.setAccessType(net.datatp.security.client.AccessType.Partner);
            appPermissionList.add(appPermissionVendorBill);

            AppPermission appPermissionPartner = new AppPermission();
            appPermissionPartner.setCompanyId(company.getId());
            appPermissionPartner.setAccountId(account.getId())
            appPermissionPartner.setAppId(10);
            appPermissionPartner.setCapability(Capability.Write);
            appPermissionPartner.setDataScope(DataScope.Company);
            appPermissionPartner.setAccessType(net.datatp.security.client.AccessType.Partner);
            appPermissionList.add(appPermissionPartner);
          }
          partnerAccountRepository.saveAll(partnerAccounts);
          securityService.saveAppPermissions(clientContext ,company, appPermissionList);

        } catch (Exception e) {
          throw new RuntimeException(e);
        }
      }
    };

    addRunnable(dataInit);
  }
}

ServerScriptContext scriptCtx = (ServerScriptContext) CONTEXT;
RunnableReporter reporter = new RunnableReporter("service", "V1_0_0_2024-10-09")
MigratePartnerAccountDataSet migration = new MigratePartnerAccountDataSet();
migration.run(reporter, scriptCtx);
