package migration.server.tms

import cloud.datatp.tms.TMSGeneralLogic
import cloud.datatp.tms.partner.entity.TMSPartnerAddress
import lib.data.RunnableReporter
import lib.data.ServiceRunnable
import lib.data.ServiceRunnableSet
import net.datatp.module.account.AccountService
import cloud.datatp.tms.partner.repository.TMSPartnerAddressRepository
import net.datatp.module.data.db.entity.ICompany
import net.datatp.module.groovy.ServerScriptContext
import net.datatp.module.resource.location.LocationLogic
import net.datatp.module.resource.location.entity.Location
import net.datatp.security.client.ClientContext

class MigratePartnerAddressDataset extends ServiceRunnableSet {

  public MigratePartnerAddressDataset() {
    super(
            """Account Vendor Migration"""
    );
    ServiceRunnable dataInit = new ServiceRunnable("Account Vendor Migration") {


      @Override
      public void run(RunnableReporter reporter, ServerScriptContext scriptCtx) {
        final String dataDir = scriptCtx.getDataDir();
        ClientContext clientContext = scriptCtx.getClientCtx();
        ICompany company = scriptCtx.getCompany();
        AccountService accountService = scriptCtx.getService(AccountService.class);
        LocationLogic locationLogic = scriptCtx.getService(LocationLogic.class);
        TMSGeneralLogic tmsGeneralLogic = scriptCtx.getService(TMSGeneralLogic.class);
        TMSPartnerAddressRepository tmsPartnerAddressRepository = scriptCtx.getService(TMSPartnerAddressRepository.class);

        List<TMSPartnerAddress> tmsPartnerAddresses = tmsPartnerAddressRepository.findAll();
        Set<Long> locationIds = new HashSet<>();
        for (TMSPartnerAddress partnerAddress : tmsPartnerAddresses) {
          Long locationId = partnerAddress.getLocationId();
          if (locationId != null) {
            locationIds.add(locationId);
          }
        }
        Map<Long, Location> locationMap = locationLogic.findMapLocationByIds(clientContext, new ArrayList<>(locationIds));
        List<TMSPartnerAddress> updatedPartnerAddresses = new ArrayList<>();
        for (TMSPartnerAddress partnerAddress : tmsPartnerAddresses) {
          Long locationId = partnerAddress.getLocationId();
          Location location = locationMap.get(locationId);
          if (location != null && "ACTIVE".equals(location.getStorageState().toString())) {
            TMSPartnerAddress address = tmsGeneralLogic.createPartnerAddress(clientContext, partnerAddress.getLocationLabel());
            if(address!=null){
              partnerAddress.setLat(address.getLat());
              partnerAddress.setLng(address.getLng());
              updatedPartnerAddresses.add(partnerAddress);
            }
          }
        }
        tmsPartnerAddressRepository.saveAll(updatedPartnerAddresses);
      }
    };

    addRunnable(dataInit);
  }
}
ServerScriptContext scriptCtx = (ServerScriptContext) CONTEXT;
RunnableReporter reporter = new RunnableReporter("service", "V1_0_0_2024-10-09")
MigratePartnerAddressDataset migration = new MigratePartnerAddressDataset();
migration.run(reporter, scriptCtx);