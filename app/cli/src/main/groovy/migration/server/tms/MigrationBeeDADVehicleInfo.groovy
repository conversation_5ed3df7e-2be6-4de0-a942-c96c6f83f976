package migration.server.tms

import cloud.datatp.fleet.vehicle.entity.Transporter
import cloud.datatp.fleet.vehicle.entity.Vehicle
import cloud.datatp.fleet.vehicle.entity.VehicleFleet
import cloud.datatp.fleet.vehicle.repository.TransporterRepository
import cloud.datatp.fleet.vehicle.repository.VehicleFleetRepository
import cloud.datatp.fleet.vehicle.repository.VehicleRepository
import lib.data.RunnableReporter
import lib.data.ServiceRunnable
import lib.data.ServiceRunnableSet
import net.datatp.module.account.AccountLogic
import net.datatp.module.account.AccountService
import net.datatp.module.account.entity.Account
import net.datatp.module.account.entity.AccountType
import net.datatp.module.account.repository.AccountRepository
import net.datatp.module.data.db.entity.ICompany
import net.datatp.nlp.util.VietnameseUtil
import net.datatp.security.client.Capability
import net.datatp.security.client.ClientContext
import net.datatp.module.core.security.SecurityService
import net.datatp.module.core.security.entity.AppPermission
import net.datatp.security.client.DataScope
import net.datatp.module.data.xlsx.XSLXToMapObjectParser
import net.datatp.module.groovy.ServerScriptContext
import net.datatp.module.hr.HRService
import net.datatp.module.hr.NewEmployeeModel
import net.datatp.module.hr.entity.Employee
import net.datatp.util.ds.MapObject
import net.datatp.util.io.IOUtil
import net.datatp.util.text.StringUtil
import net.datatp.util.text.TokenUtil


public class MigrationBeeDADVehicleInfoDataSet extends ServiceRunnableSet {
  public MigrationBeeDADVehicleInfoDataSet() {
    super(
            """Migration BeeDAD VehicleInfo DataSet"""
    );

    ServiceRunnable dataInit = new ServiceRunnable("Migration BeeDAD VehicleInfo DataSet") {

      private removeVehicle(ServerScriptContext scriptCtx) {
        ICompany company = scriptCtx.getCompany();
        VehicleFleetRepository fleetRepository = scriptCtx.getService(VehicleFleetRepository.class);
        VehicleRepository vehicleRepository = scriptCtx.getService(VehicleRepository.class);
        TransporterRepository transporterRepository = scriptCtx.getService(TransporterRepository.class);

        List<Long> vehicleIdDes = new ArrayList<>();
        List<Vehicle> vehicles = vehicleRepository.findAll();
        List<Long> fleetIdDes = new ArrayList<>();
        List<VehicleFleet> fleets = fleetRepository.findAll();

        List<Long> transporterIdDes = new ArrayList<>();
        List<Transporter> transporters = transporterRepository.findAll();


        for(Vehicle vehicle: vehicles) {
          if(vehicle.getCompanyId() == company.getId()) {
            vehicleIdDes.add(vehicle.getId());
          }
        }

        for(VehicleFleet fleet: fleets) {
          if(fleet.getCompanyId() == company.getId()) {
            fleetIdDes.add(fleet.getId());
          }
        }

        for(Transporter transporter: transporters) {
          if(transporter.getCompanyId() == company.getId()) {
            transporterIdDes.add(transporter.getId());
          }
        }

        fleetRepository.deleteAllById(fleetIdDes);
        vehicleRepository.deleteAllById(vehicleIdDes);
        transporterRepository.deleteAllById(transporterIdDes);
      }

      private void initFleet(ServerScriptContext scriptCtx) {
        final String dataDir = scriptCtx.getDataDir();
        ClientContext clientContext = scriptCtx.getClientCtx();
        ICompany company = scriptCtx.getCompany();
        try {
          InputStream is = IOUtil.loadResource("file:" + dataDir + "/tms/vehicle-beedad.xlsx");
          final byte[] data = IOUtil.getStreamContentAsBytes(is);
          XSLXToMapObjectParser parser = new XSLXToMapObjectParser(data);
          VehicleFleetRepository fleetRepository = scriptCtx.getService(VehicleFleetRepository.class);
          parser.parse("fleet");
          List<MapObject> records = parser.getRows();
          List<VehicleFleet> vehicleFleets = new ArrayList<>();
          for(MapObject record: records) {
            String tenTP = record.getString("TÊN TP");
            String maTP = record.getString("MÃ CS/ CL");
            VehicleFleet vehicleFleet = new VehicleFleet();
            vehicleFleet.setLabel(VietnameseUtil.removeVietnameseAccent(tenTP));
            vehicleFleet.setLocalizedLabel(tenTP);
            vehicleFleet.setBfsOneCode(maTP);
            vehicleFleet.setCode(TokenUtil.idWithDateTime("fleet"))
            vehicleFleet.set(clientContext, company);
            vehicleFleets.add(vehicleFleet);
          }
          fleetRepository.saveAll(vehicleFleets);
        } catch (Exception e) {
          throw new RuntimeException(e);
        }
      }

      private void initVehicle(ServerScriptContext scriptCtx) {
        final String dataDir = scriptCtx.getDataDir();
        ClientContext clientContext = scriptCtx.getClientCtx();
        ICompany company = scriptCtx.getCompany();
        try {
          InputStream is = IOUtil.loadResource("file:" + dataDir + "/tms/vehicle-beedad.xlsx");
          final byte[] data = IOUtil.getStreamContentAsBytes(is);
          XSLXToMapObjectParser parser = new XSLXToMapObjectParser(data);
          VehicleRepository vehicleRepository = scriptCtx.getService(VehicleRepository.class);
          TransporterRepository tranporterRepo = scriptCtx.getService(TransporterRepository.class);

          parser.parse("vehicle");
          List<MapObject> records = parser.getRows();
          for(MapObject record: records) {
            String bienSO = record.getString("Biển Số");
            bienSO = bienSO.replaceAll("[\\s\\-.]", "");
            String laiXe = record.getString("Lái Xe");
            String soCanCuoc = record.getString("Số căn cước");
            String soDT = record.getString("Số Điện Thoại");
            String gps = record.getString("GPS");
            Transporter transporter = new Transporter();
            transporter.setFullName(laiXe);
            if(StringUtil.isNotBlank(soCanCuoc)) {
              transporter.setIdCard(soCanCuoc);
            }
            if(StringUtil.isNotBlank(soDT)) {
              transporter.setMobile(soDT);
            }

            transporter.setCode(TokenUtil.idWithDateTime("transporter"));
            transporter.set(clientContext, company);
            tranporterRepo.save(transporter);

            Vehicle vehicle = new Vehicle();
            vehicle.setLabel(bienSO);
            vehicle.setLicensePlate(bienSO);
            vehicle.setCode(TokenUtil.idWithDateTime("vehicle"));
            vehicle.setNote(gps);
            vehicle.setTransporterId(transporter.getId());
            vehicle.setTransporterFullName(transporter.getFullName());
            vehicle.set(clientContext, company);
            vehicleRepository.save(vehicle);

          }
        } catch (Exception e) {
          throw new RuntimeException(e);
        }
      }

      @Override
      public void run(RunnableReporter reporter, ServerScriptContext scriptCtx) {


        this.removeVehicle(scriptCtx);
        this.initFleet(scriptCtx);
        this.initVehicle(scriptCtx);

      }
    };

    addRunnable(dataInit);
  }
}

ServerScriptContext scriptCtx = (ServerScriptContext) CONTEXT;
RunnableReporter reporter = new RunnableReporter("service", "V1_0_0_2024-10-09")
MigrationBeeDADVehicleInfoDataSet migration = new MigrationBeeDADVehicleInfoDataSet();
migration.run(reporter, scriptCtx);
