package migration.db.tms

import lib.data.DBMigrationRunnable
import lib.data.DBMigrationRunnableSet
import lib.data.RunnableReporter
import net.datatp.cli.ShellApplicationContext
import net.datatp.module.data.db.util.DBConnectionUtil

import java.sql.Connection

class AlterTable extends DBMigrationRunnableSet {
  public AlterTable() {
    super("""Alter Table""");

    String label = """Alter Table"""

    DBMigrationRunnable activityMigration = new DBMigrationRunnable(label) {
      @Override
      public void run(RunnableReporter reporter, DBConnectionUtil connUtil) {
        String query =
        """
          update lgc_fleet_vehicle_trip_goods_tracking set type_of_transport = 'truck'
          where company_id = 8 and type_of_transport is null;
          update lgc_tms_bill_cost_item set price = cost / quantity where price = 0 and cost > 0;
        
          ALTER TABLE public.lgc_tms_bill_attachment DROP COLUMN entity_id;
        """;
        connUtil.execute(query);
      }
    };
    addRunnable(activityMigration);
  }
}

ShellApplicationContext shellContext = (ShellApplicationContext) SHELL_CONTEXT;
Connection conn = shellContext.getPrimaryDBConnection();
DBConnectionUtil connUtil = new DBConnectionUtil(conn);
RunnableReporter reporter  = new RunnableReporter("dbmigration", "V1_0_0_2025-23-01")

AlterTable runnableSet = new AlterTable();
runnableSet.run(reporter, connUtil);

connUtil.close();
return "DONE!!!"

