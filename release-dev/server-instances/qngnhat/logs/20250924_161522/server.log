2025-09-24T16:15:23.979+07:00  INFO 80384 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 80384 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-24T16:15:23.980+07:00  INFO 80384 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-24T16:15:25.681+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:15:25.852+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 166 ms. Found 22 JPA repository interfaces.
2025-09-24T16:15:25.882+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:15:25.884+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-09-24T16:15:25.884+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:15:26.105+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 220 ms. Found 10 JPA repository interfaces.
2025-09-24T16:15:26.108+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:15:26.122+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 13 ms. Found 3 JPA repository interfaces.
2025-09-24T16:15:26.153+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:15:26.231+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 77 ms. Found 1 JPA repository interface.
2025-09-24T16:15:26.284+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:15:26.293+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 2 JPA repository interfaces.
2025-09-24T16:15:26.294+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:15:26.304+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 6 JPA repository interfaces.
2025-09-24T16:15:26.311+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:15:26.323+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 11 ms. Found 5 JPA repository interfaces.
2025-09-24T16:15:26.333+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:15:26.338+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 3 JPA repository interfaces.
2025-09-24T16:15:26.339+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:15:26.340+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 0 JPA repository interfaces.
2025-09-24T16:15:26.341+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:15:26.365+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 23 ms. Found 10 JPA repository interfaces.
2025-09-24T16:15:26.378+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:15:26.385+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 3 JPA repository interfaces.
2025-09-24T16:15:26.393+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:15:26.405+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 11 ms. Found 6 JPA repository interfaces.
2025-09-24T16:15:26.406+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:15:26.437+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 30 ms. Found 12 JPA repository interfaces.
2025-09-24T16:15:26.438+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:15:26.448+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 4 JPA repository interfaces.
2025-09-24T16:15:26.449+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:15:26.449+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-24T16:15:26.450+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:15:26.454+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 1 JPA repository interface.
2025-09-24T16:15:26.455+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:15:26.468+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 12 ms. Found 7 JPA repository interfaces.
2025-09-24T16:15:26.468+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:15:26.475+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 2 JPA repository interfaces.
2025-09-24T16:15:26.475+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:15:26.478+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 0 JPA repository interfaces.
2025-09-24T16:15:26.479+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:15:26.502+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 23 ms. Found 19 JPA repository interfaces.
2025-09-24T16:15:26.528+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:15:26.547+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 18 ms. Found 8 JPA repository interfaces.
2025-09-24T16:15:26.548+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:15:26.559+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 5 JPA repository interfaces.
2025-09-24T16:15:26.559+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:15:26.572+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 13 ms. Found 7 JPA repository interfaces.
2025-09-24T16:15:26.573+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:15:26.591+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 18 ms. Found 9 JPA repository interfaces.
2025-09-24T16:15:26.592+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:15:26.601+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 6 JPA repository interfaces.
2025-09-24T16:15:26.602+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:15:26.621+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 18 ms. Found 13 JPA repository interfaces.
2025-09-24T16:15:26.622+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:15:26.642+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 20 ms. Found 14 JPA repository interfaces.
2025-09-24T16:15:26.643+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:15:26.691+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 48 ms. Found 24 JPA repository interfaces.
2025-09-24T16:15:26.692+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:15:26.693+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-24T16:15:26.708+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:15:26.716+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 0 JPA repository interfaces.
2025-09-24T16:15:26.717+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:15:26.749+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 32 ms. Found 12 JPA repository interfaces.
2025-09-24T16:15:26.756+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:15:26.869+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 113 ms. Found 66 JPA repository interfaces.
2025-09-24T16:15:26.871+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:15:26.875+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 1 JPA repository interface.
2025-09-24T16:15:26.883+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:15:26.895+07:00  INFO 80384 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 11 ms. Found 4 JPA repository interfaces.
2025-09-24T16:15:27.359+07:00  INFO 80384 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-24T16:15:27.368+07:00  INFO 80384 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-24T16:15:28.081+07:00  WARN 80384 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-24T16:15:28.897+07:00  INFO 80384 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-24T16:15:28.902+07:00  INFO 80384 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-24T16:15:28.937+07:00  INFO 80384 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-24T16:15:28.937+07:00  INFO 80384 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 4755 ms
2025-09-24T16:15:29.119+07:00  WARN 80384 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-24T16:15:29.119+07:00  INFO 80384 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-24T16:15:29.328+07:00  INFO 80384 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@169f4152
2025-09-24T16:15:29.329+07:00  INFO 80384 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-24T16:15:29.342+07:00  WARN 80384 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-24T16:15:29.343+07:00  INFO 80384 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-24T16:15:29.352+07:00  INFO 80384 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@76a5a831
2025-09-24T16:15:29.352+07:00  INFO 80384 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-24T16:15:29.353+07:00  WARN 80384 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-24T16:15:29.353+07:00  INFO 80384 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-24T16:15:29.362+07:00  INFO 80384 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@23644367
2025-09-24T16:15:29.365+07:00  INFO 80384 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-24T16:15:29.365+07:00  WARN 80384 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-24T16:15:29.365+07:00  INFO 80384 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-24T16:15:29.393+07:00  INFO 80384 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@113b39e9
2025-09-24T16:15:29.393+07:00  INFO 80384 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-24T16:15:29.393+07:00  WARN 80384 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-24T16:15:29.394+07:00  INFO 80384 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-24T16:15:29.408+07:00  INFO 80384 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@60ee6f76
2025-09-24T16:15:29.408+07:00  INFO 80384 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-24T16:15:29.408+07:00  INFO 80384 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-24T16:15:29.537+07:00  INFO 80384 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-24T16:15:29.700+07:00  INFO 80384 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@42bd4a4c{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.13524361702211641490/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@3e07ccbf{STARTED}}
2025-09-24T16:15:29.701+07:00  INFO 80384 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@42bd4a4c{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.13524361702211641490/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@3e07ccbf{STARTED}}
2025-09-24T16:15:29.703+07:00  INFO 80384 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@28e12bc5{STARTING}[12.0.15,sto=0] @6687ms
2025-09-24T16:15:29.815+07:00  INFO 80384 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-24T16:15:29.898+07:00  INFO 80384 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-24T16:15:29.931+07:00  INFO 80384 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-24T16:15:30.214+07:00  INFO 80384 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-24T16:15:30.282+07:00  WARN 80384 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-24T16:15:32.001+07:00  INFO 80384 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-24T16:15:32.031+07:00  INFO 80384 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@78633092] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-24T16:15:32.426+07:00  INFO 80384 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-24T16:15:32.954+07:00  INFO 80384 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.wfms", "net.datatp.module.monitor.activity", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "cloud.datatp.module.fleet", "net.datatp.module.okr", "net.datatp.module.core.template", "cloud.datatp.module.pegasus.shipment", "cloud.datatp.gps", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "net.datatp.module.company.tmpl", "net.datatp.module.groovy", "cloud.datatp.tms", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "net.datatp.module.resource", "cloud.datatp.vendor", "net.datatp.module.hr", "net.datatp.module.company.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "cloud.datatp.module.odoo", "net.datatp.module.chatbot", "net.datatp.module.core.print", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-09-24T16:15:32.976+07:00  INFO 80384 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-24T16:15:33.007+07:00  INFO 80384 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-24T16:15:33.012+07:00  INFO 80384 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-24T16:15:33.050+07:00  INFO 80384 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-24T16:15:33.057+07:00  WARN 80384 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-24T16:15:37.200+07:00  INFO 80384 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-24T16:15:37.203+07:00  INFO 80384 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@513d2f2d] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-24T16:15:37.399+07:00  WARN 80384 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-24T16:15:37.399+07:00  WARN 80384 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-24T16:15:37.407+07:00  WARN 80384 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-24T16:15:37.408+07:00  WARN 80384 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-24T16:15:37.422+07:00  WARN 80384 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-24T16:15:37.422+07:00  WARN 80384 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-24T16:15:37.955+07:00  INFO 80384 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-24T16:15:37.969+07:00  INFO 80384 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-24T16:15:37.971+07:00  INFO 80384 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-24T16:15:38.009+07:00  INFO 80384 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-24T16:15:38.033+07:00  WARN 80384 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-24T16:15:38.645+07:00  INFO 80384 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-24T16:15:38.646+07:00  INFO 80384 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@50df5f6] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-24T16:15:38.710+07:00  WARN 80384 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-24T16:15:38.710+07:00  WARN 80384 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-24T16:15:39.054+07:00  INFO 80384 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-24T16:15:39.087+07:00  INFO 80384 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-24T16:15:39.091+07:00  INFO 80384 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-24T16:15:39.091+07:00  INFO 80384 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-24T16:15:39.097+07:00  WARN 80384 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-24T16:15:39.228+07:00  INFO 80384 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-24T16:15:39.703+07:00  INFO 80384 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-24T16:15:39.707+07:00  INFO 80384 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-24T16:15:39.742+07:00  INFO 80384 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-24T16:15:39.787+07:00  INFO 80384 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-24T16:15:39.903+07:00  INFO 80384 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-24T16:15:39.943+07:00  INFO 80384 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-24T16:15:39.967+07:00  INFO 80384 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 28383549ms : this is harmless.
2025-09-24T16:15:39.974+07:00  INFO 80384 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-24T16:15:39.977+07:00  INFO 80384 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-24T16:15:39.992+07:00  INFO 80384 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 28383538ms : this is harmless.
2025-09-24T16:15:39.995+07:00  INFO 80384 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-24T16:15:40.008+07:00  INFO 80384 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-24T16:15:40.009+07:00  INFO 80384 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-24T16:15:42.196+07:00  INFO 80384 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-24T16:15:42.196+07:00  INFO 80384 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-24T16:15:42.197+07:00  WARN 80384 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-24T16:15:42.493+07:00  INFO 80384 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 24/09/2025@16:15:00+0700 to 24/09/2025@16:30:00+0700
2025-09-24T16:15:42.494+07:00  INFO 80384 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 24/09/2025@16:15:00+0700 to 24/09/2025@16:30:00+0700
2025-09-24T16:15:43.033+07:00  INFO 80384 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-09-24T16:15:43.033+07:00  INFO 80384 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-24T16:15:43.034+07:00  WARN 80384 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-24T16:15:43.312+07:00  INFO 80384 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-09-24T16:15:43.312+07:00  INFO 80384 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-09-24T16:15:43.312+07:00  INFO 80384 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-09-24T16:15:43.312+07:00  INFO 80384 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-09-24T16:15:43.312+07:00  INFO 80384 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-09-24T16:15:45.099+07:00  WARN 80384 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 3cc82a4c-013e-4ef3-8a63-0d23bcff2ec2

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-24T16:15:45.102+07:00  INFO 80384 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-09-24T16:15:45.400+07:00  INFO 80384 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-24T16:15:45.401+07:00  INFO 80384 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-24T16:15:45.401+07:00  INFO 80384 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-24T16:15:45.401+07:00  INFO 80384 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-24T16:15:45.401+07:00  INFO 80384 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-09-24T16:15:45.401+07:00  INFO 80384 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-09-24T16:15:45.401+07:00  INFO 80384 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-24T16:15:45.401+07:00  INFO 80384 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-09-24T16:15:45.401+07:00  INFO 80384 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-09-24T16:15:45.401+07:00  INFO 80384 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-24T16:15:45.401+07:00  INFO 80384 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-09-24T16:15:45.401+07:00  INFO 80384 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-09-24T16:15:45.402+07:00  INFO 80384 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-24T16:15:45.402+07:00  INFO 80384 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-24T16:15:45.402+07:00  INFO 80384 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-24T16:15:45.510+07:00  INFO 80384 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-24T16:15:45.510+07:00  INFO 80384 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-24T16:15:45.512+07:00  INFO 80384 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-09-24T16:15:45.521+07:00  INFO 80384 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@721feb3e{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-24T16:15:45.522+07:00  INFO 80384 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-09-24T16:15:45.523+07:00  INFO 80384 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-09-24T16:15:45.579+07:00  INFO 80384 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-09-24T16:15:45.579+07:00  INFO 80384 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-09-24T16:15:45.585+07:00  INFO 80384 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 22.272 seconds (process running for 22.57)
2025-09-24T16:15:55.146+07:00  INFO 80384 --- [qtp2145053547-36] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01i37iqincuejod43aetfazy3f0
2025-09-24T16:15:55.568+07:00  INFO 80384 --- [qtp2145053547-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01i37iqincuejod43aetfazy3f0, token = 16276a81fc5974e87aeb014596981534
2025-09-24T16:15:55.959+07:00  INFO 80384 --- [qtp2145053547-36] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T16:16:06.450+07:00  INFO 80384 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T16:16:48.562+07:00  INFO 80384 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 12, expire count 0
2025-09-24T16:16:48.598+07:00  INFO 80384 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-24T16:16:53.569+07:00  INFO 80384 --- [qtp2145053547-40] n.d.module.session.ClientSessionManager  : Add a client session id = node01i37iqincuejod43aetfazy3f0, token = 16276a81fc5974e87aeb014596981534
2025-09-24T16:16:53.570+07:00  INFO 80384 --- [qtp2145053547-39] n.d.module.session.ClientSessionManager  : Add a client session id = node01i37iqincuejod43aetfazy3f0, token = 16276a81fc5974e87aeb014596981534
2025-09-24T16:16:53.582+07:00  INFO 80384 --- [qtp2145053547-40] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T16:16:53.583+07:00  INFO 80384 --- [qtp2145053547-39] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T16:17:02.622+07:00  INFO 80384 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T16:17:03.605+07:00  INFO 80384 --- [qtp2145053547-60] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph logout successfully 
2025-09-24T16:17:09.740+07:00  INFO 80384 --- [qtp2145053547-61] n.d.module.session.ClientSessionManager  : Add a client session id = node01i37iqincuejod43aetfazy3f0, token = e43564b2844c5ffb0845c9287467d129
2025-09-24T16:17:09.744+07:00  INFO 80384 --- [qtp2145053547-61] n.d.m.c.a.CompanyAuthenticationService   : User rio.vnsgn is logged in successfully system
2025-09-24T16:18:05.725+07:00  INFO 80384 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T16:18:47.829+07:00  INFO 80384 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 0
2025-09-24T16:18:47.845+07:00  INFO 80384 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T16:19:06.868+07:00  INFO 80384 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T16:20:04.979+07:00  INFO 80384 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T16:20:04.981+07:00  INFO 80384 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T16:20:52.088+07:00  INFO 80384 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-24T16:20:52.097+07:00  INFO 80384 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T16:21:06.118+07:00  INFO 80384 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T16:22:04.221+07:00  INFO 80384 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T16:22:52.305+07:00  INFO 80384 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-24T16:22:52.313+07:00  INFO 80384 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T16:23:06.336+07:00  INFO 80384 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T16:24:03.444+07:00  INFO 80384 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T16:24:52.549+07:00  INFO 80384 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-24T16:24:52.564+07:00  INFO 80384 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T16:25:06.582+07:00  INFO 80384 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T16:25:06.585+07:00  INFO 80384 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T16:26:02.678+07:00  INFO 80384 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T16:26:51.808+07:00  INFO 80384 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 9
2025-09-24T16:26:51.815+07:00  INFO 80384 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-24T16:27:05.830+07:00  INFO 80384 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T16:28:06.937+07:00  INFO 80384 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T16:28:51.031+07:00  INFO 80384 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 8
2025-09-24T16:28:51.044+07:00  INFO 80384 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T16:29:05.068+07:00  INFO 80384 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T16:30:06.168+07:00  INFO 80384 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-24T16:30:06.184+07:00  INFO 80384 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 24/09/2025@16:30:06+0700
2025-09-24T16:30:06.211+07:00  INFO 80384 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 24/09/2025@16:30:00+0700 to 24/09/2025@16:45:00+0700
2025-09-24T16:30:06.212+07:00  INFO 80384 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 24/09/2025@16:30:00+0700 to 24/09/2025@16:45:00+0700
2025-09-24T16:30:06.212+07:00  INFO 80384 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T16:30:06.212+07:00  INFO 80384 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T16:30:50.369+07:00  INFO 80384 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-24T16:30:50.380+07:00  INFO 80384 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T16:31:04.409+07:00  INFO 80384 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T16:32:06.499+07:00  INFO 80384 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T16:32:49.581+07:00  INFO 80384 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-24T16:32:49.589+07:00  INFO 80384 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T16:33:03.617+07:00  INFO 80384 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T16:34:06.719+07:00  INFO 80384 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T16:34:48.799+07:00  INFO 80384 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T16:34:48.811+07:00  INFO 80384 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T16:35:02.835+07:00  INFO 80384 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T16:35:02.836+07:00  INFO 80384 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T16:36:05.945+07:00  INFO 80384 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T16:36:48.091+07:00  INFO 80384 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 4
2025-09-24T16:36:48.104+07:00  INFO 80384 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T16:37:02.147+07:00  INFO 80384 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T16:38:05.316+07:00  INFO 80384 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T16:38:52.457+07:00  INFO 80384 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-24T16:38:52.466+07:00  INFO 80384 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T16:39:06.497+07:00  INFO 80384 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T16:40:04.667+07:00  INFO 80384 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T16:40:04.669+07:00  INFO 80384 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T16:40:51.794+07:00  INFO 80384 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-24T16:40:51.798+07:00  INFO 80384 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T16:41:06.854+07:00  INFO 80384 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T16:42:04.009+07:00  INFO 80384 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T16:42:52.186+07:00  INFO 80384 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-24T16:42:52.203+07:00  INFO 80384 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T16:43:06.249+07:00  INFO 80384 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T16:44:03.427+07:00  INFO 80384 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T16:44:52.539+07:00  INFO 80384 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-24T16:44:52.555+07:00  INFO 80384 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T16:45:06.579+07:00  INFO 80384 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T16:45:06.581+07:00  INFO 80384 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T16:45:06.581+07:00  INFO 80384 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 24/09/2025@16:45:06+0700
2025-09-24T16:45:06.624+07:00  INFO 80384 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 3 messages for session 24/09/2025@16:45:00+0700 to 24/09/2025@17:00:00+0700
2025-09-24T16:45:06.625+07:00  INFO 80384 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 📤 message : MAIL - inquiry-overdue-reminder - scheduled at 24/09/2025@16:49:44+0700
2025-09-24T16:45:06.625+07:00  INFO 80384 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 📤 message : MAIL - inquiry-overdue-reminder - scheduled at 24/09/2025@16:52:09+0700
2025-09-24T16:45:06.625+07:00  INFO 80384 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 📤 message : MAIL - inquiry-overdue-reminder - scheduled at 24/09/2025@16:52:25+0700
2025-09-24T16:45:06.625+07:00  INFO 80384 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 3 messages for session 24/09/2025@16:45:00+0700 to 24/09/2025@17:00:00+0700
2025-09-24T16:45:06.626+07:00  INFO 80384 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-24T16:46:02.726+07:00  INFO 80384 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T16:46:28.124+07:00  INFO 80384 --- [qtp2145053547-82] n.d.m.c.a.CompanyAuthenticationService   : User rio.vnsgn logout successfully 
2025-09-24T16:46:29.693+07:00  INFO 80384 --- [qtp2145053547-68] n.d.module.session.ClientSessionManager  : Add a client session id = node01i37iqincuejod43aetfazy3f0, token = a5ae647cbd9d84e0fcdaa515c08ae1ef
2025-09-24T16:46:29.806+07:00  INFO 80384 --- [qtp2145053547-68] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T16:46:46.686+07:00  INFO 80384 --- [qtp2145053547-62] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-24T16:46:46.686+07:00  INFO 80384 --- [qtp2145053547-36] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-24T16:46:46.804+07:00  INFO 80384 --- [qtp2145053547-36] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-24T16:46:46.804+07:00  INFO 80384 --- [qtp2145053547-62] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-24T16:46:51.838+07:00  INFO 80384 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 17, expire count 0
2025-09-24T16:46:51.868+07:00  INFO 80384 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-24T16:47:05.892+07:00  INFO 80384 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T16:47:48.437+07:00  INFO 80384 --- [qtp2145053547-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01i37iqincuejod43aetfazy3f0, token = a5ae647cbd9d84e0fcdaa515c08ae1ef
2025-09-24T16:47:48.561+07:00  INFO 80384 --- [qtp2145053547-118] n.d.module.session.ClientSessionManager  : Add a client session id = node01i37iqincuejod43aetfazy3f0, token = a5ae647cbd9d84e0fcdaa515c08ae1ef
2025-09-24T16:47:48.708+07:00  INFO 80384 --- [qtp2145053547-36] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T16:47:48.873+07:00  INFO 80384 --- [qtp2145053547-118] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T16:48:02.020+07:00  INFO 80384 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T16:48:51.158+07:00  INFO 80384 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 0
2025-09-24T16:48:51.164+07:00  INFO 80384 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T16:49:05.193+07:00  INFO 80384 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T16:49:17.639+07:00  INFO 80384 --- [qtp2145053547-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01i37iqincuejod43aetfazy3f0, token = a5ae647cbd9d84e0fcdaa515c08ae1ef
2025-09-24T16:49:17.776+07:00  INFO 80384 --- [qtp2145053547-36] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T16:49:17.784+07:00  INFO 80384 --- [qtp2145053547-119] n.d.module.session.ClientSessionManager  : Add a client session id = node01i37iqincuejod43aetfazy3f0, token = a5ae647cbd9d84e0fcdaa515c08ae1ef
2025-09-24T16:49:17.957+07:00  INFO 80384 --- [qtp2145053547-119] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T16:50:06.640+07:00  INFO 80384 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T16:50:06.644+07:00  INFO 80384 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T16:50:27.009+07:00  INFO 80384 --- [qtp2145053547-119] n.d.module.session.ClientSessionManager  : Add a client session id = node01i37iqincuejod43aetfazy3f0, token = a5ae647cbd9d84e0fcdaa515c08ae1ef
2025-09-24T16:50:27.046+07:00  INFO 80384 --- [qtp2145053547-118] n.d.module.session.ClientSessionManager  : Add a client session id = node01i37iqincuejod43aetfazy3f0, token = a5ae647cbd9d84e0fcdaa515c08ae1ef
2025-09-24T16:50:27.117+07:00  INFO 80384 --- [qtp2145053547-119] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T16:50:27.117+07:00  INFO 80384 --- [qtp2145053547-118] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T16:50:50.765+07:00  INFO 80384 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-24T16:50:50.771+07:00  INFO 80384 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T16:51:04.797+07:00  INFO 80384 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T16:51:59.241+07:00  INFO 80384 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@721feb3e{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-24T16:51:59.244+07:00  INFO 80384 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-24T16:51:59.244+07:00  INFO 80384 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-24T16:51:59.244+07:00  INFO 80384 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-24T16:51:59.245+07:00  INFO 80384 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-24T16:51:59.245+07:00  INFO 80384 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-24T16:51:59.245+07:00  INFO 80384 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-24T16:51:59.245+07:00  INFO 80384 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-24T16:51:59.245+07:00  INFO 80384 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-09-24T16:51:59.245+07:00  INFO 80384 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-09-24T16:51:59.245+07:00  INFO 80384 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-24T16:51:59.245+07:00  INFO 80384 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-09-24T16:51:59.245+07:00  INFO 80384 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-09-24T16:51:59.245+07:00  INFO 80384 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-24T16:51:59.245+07:00  INFO 80384 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-09-24T16:51:59.245+07:00  INFO 80384 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-09-24T16:51:59.270+07:00  INFO 80384 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T16:51:59.393+07:00  INFO 80384 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-24T16:51:59.399+07:00  INFO 80384 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-24T16:51:59.441+07:00  INFO 80384 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-24T16:51:59.467+07:00  INFO 80384 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-24T16:51:59.532+07:00  INFO 80384 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-24T16:51:59.556+07:00  INFO 80384 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-24T16:51:59.589+07:00  INFO 80384 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-24T16:51:59.589+07:00  INFO 80384 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-24T16:51:59.595+07:00  INFO 80384 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-24T16:51:59.596+07:00  INFO 80384 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-24T16:51:59.596+07:00  INFO 80384 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-24T16:51:59.597+07:00  INFO 80384 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-24T16:51:59.598+07:00  INFO 80384 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-24T16:51:59.598+07:00  INFO 80384 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-24T16:51:59.599+07:00  INFO 80384 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-24T16:51:59.601+07:00  INFO 80384 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@28e12bc5{STOPPING}[12.0.15,sto=0]
2025-09-24T16:51:59.606+07:00  INFO 80384 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-24T16:51:59.610+07:00  INFO 80384 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@42bd4a4c{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.13524361702211641490/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@3e07ccbf{STOPPED}}
