2025-09-22T16:33:04.761+07:00  INFO 44319 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 44319 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-22T16:33:04.763+07:00  INFO 44319 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-22T16:33:05.513+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:33:05.593+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 77 ms. Found 22 JPA repository interfaces.
2025-09-22T16:33:05.605+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:33:05.606+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-22T16:33:05.607+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:33:05.615+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 10 JPA repository interfaces.
2025-09-22T16:33:05.615+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:33:05.618+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-22T16:33:05.626+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:33:05.631+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-09-22T16:33:05.639+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:33:05.641+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-22T16:33:05.641+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:33:05.645+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-22T16:33:05.648+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:33:05.687+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 39 ms. Found 5 JPA repository interfaces.
2025-09-22T16:33:05.691+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:33:05.693+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-22T16:33:05.693+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:33:05.694+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-22T16:33:05.694+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:33:05.700+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 10 JPA repository interfaces.
2025-09-22T16:33:05.704+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:33:05.707+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-22T16:33:05.710+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:33:05.714+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-22T16:33:05.714+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:33:05.722+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-09-22T16:33:05.722+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:33:05.725+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-22T16:33:05.725+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:33:05.725+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-22T16:33:05.725+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:33:05.726+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-22T16:33:05.726+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:33:05.731+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-09-22T16:33:05.731+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:33:05.732+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-22T16:33:05.732+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:33:05.732+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-22T16:33:05.733+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:33:05.743+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 19 JPA repository interfaces.
2025-09-22T16:33:05.753+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:33:05.759+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 8 JPA repository interfaces.
2025-09-22T16:33:05.759+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:33:05.762+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-09-22T16:33:05.762+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:33:05.766+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-22T16:33:05.767+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:33:05.772+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-09-22T16:33:05.772+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:33:05.777+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-09-22T16:33:05.777+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:33:05.786+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 13 JPA repository interfaces.
2025-09-22T16:33:05.786+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:33:05.797+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 14 JPA repository interfaces.
2025-09-22T16:33:05.797+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:33:05.813+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 15 ms. Found 24 JPA repository interfaces.
2025-09-22T16:33:05.814+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:33:05.815+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-22T16:33:05.820+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:33:05.821+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-22T16:33:05.821+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:33:05.828+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-22T16:33:05.830+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:33:05.870+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 39 ms. Found 66 JPA repository interfaces.
2025-09-22T16:33:05.870+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:33:05.872+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-22T16:33:05.876+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:33:05.879+07:00  INFO 44319 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-22T16:33:06.064+07:00  INFO 44319 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-22T16:33:06.068+07:00  INFO 44319 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-22T16:33:06.352+07:00  WARN 44319 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-22T16:33:06.567+07:00  INFO 44319 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-22T16:33:06.569+07:00  INFO 44319 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-22T16:33:06.581+07:00  INFO 44319 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-22T16:33:06.581+07:00  INFO 44319 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1688 ms
2025-09-22T16:33:06.648+07:00  WARN 44319 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-22T16:33:06.648+07:00  INFO 44319 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-22T16:33:06.744+07:00  INFO 44319 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@398f6622
2025-09-22T16:33:06.745+07:00  INFO 44319 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-22T16:33:06.750+07:00  WARN 44319 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-22T16:33:06.750+07:00  INFO 44319 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-22T16:33:06.759+07:00  INFO 44319 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@12c1826
2025-09-22T16:33:06.759+07:00  INFO 44319 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-22T16:33:06.759+07:00  WARN 44319 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-22T16:33:06.759+07:00  INFO 44319 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-22T16:33:06.768+07:00  INFO 44319 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@6be8e6ee
2025-09-22T16:33:06.769+07:00  INFO 44319 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-22T16:33:06.769+07:00  WARN 44319 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-22T16:33:06.769+07:00  INFO 44319 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-22T16:33:06.779+07:00  INFO 44319 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@71669835
2025-09-22T16:33:06.779+07:00  INFO 44319 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-22T16:33:06.779+07:00  WARN 44319 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-22T16:33:06.779+07:00  INFO 44319 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-22T16:33:06.785+07:00  INFO 44319 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@19cb3f
2025-09-22T16:33:06.786+07:00  INFO 44319 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-22T16:33:06.786+07:00  INFO 44319 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-22T16:33:06.849+07:00  INFO 44319 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-22T16:33:06.853+07:00  INFO 44319 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@5de769c8{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.16712201054209271348/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@10ecdd85{STARTED}}
2025-09-22T16:33:06.854+07:00  INFO 44319 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@5de769c8{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.16712201054209271348/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@10ecdd85{STARTED}}
2025-09-22T16:33:06.857+07:00  INFO 44319 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@74940388{STARTING}[12.0.15,sto=0] @2765ms
2025-09-22T16:33:07.001+07:00  INFO 44319 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-22T16:33:07.029+07:00  INFO 44319 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-22T16:33:07.048+07:00  INFO 44319 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-22T16:33:07.192+07:00  INFO 44319 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-22T16:33:07.234+07:00  WARN 44319 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-22T16:33:07.914+07:00  INFO 44319 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-22T16:33:07.922+07:00  INFO 44319 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@7d8a546d] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-22T16:33:08.072+07:00  INFO 44319 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-22T16:33:08.260+07:00  INFO 44319 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.graphapi", "net.datatp.module.kpi", "net.datatp.module.wfms", "net.datatp.module.monitor.activity", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "cloud.datatp.module.fleet", "net.datatp.module.okr", "net.datatp.module.core.template", "cloud.datatp.gps", "cloud.datatp.module.pegasus.shipment", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "net.datatp.module.groovy", "net.datatp.module.company.tmpl", "cloud.datatp.tms", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "cloud.datatp.vendor", "net.datatp.module.resource", "net.datatp.module.hr", "net.datatp.module.company.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.chatbot", "cloud.datatp.module.odoo", "net.datatp.module.core.print", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.zalo", "net.datatp.module.accounting" ]
2025-09-22T16:33:08.262+07:00  INFO 44319 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-22T16:33:08.268+07:00  INFO 44319 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-22T16:33:08.270+07:00  INFO 44319 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-22T16:33:08.298+07:00  INFO 44319 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-22T16:33:08.305+07:00  WARN 44319 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-22T16:33:10.556+07:00  INFO 44319 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-22T16:33:10.557+07:00  INFO 44319 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@5c50c655] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-22T16:33:10.827+07:00  WARN 44319 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-22T16:33:10.827+07:00  WARN 44319 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-22T16:33:10.837+07:00  WARN 44319 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-22T16:33:10.837+07:00  WARN 44319 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-22T16:33:10.853+07:00  WARN 44319 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-22T16:33:10.853+07:00  WARN 44319 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-22T16:33:11.426+07:00  INFO 44319 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-22T16:33:11.433+07:00  INFO 44319 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-22T16:33:11.435+07:00  INFO 44319 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-22T16:33:11.459+07:00  INFO 44319 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-22T16:33:11.462+07:00  WARN 44319 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-22T16:33:12.056+07:00  INFO 44319 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-22T16:33:12.056+07:00  INFO 44319 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@7d2aa270] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-22T16:33:12.162+07:00  WARN 44319 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-22T16:33:12.162+07:00  WARN 44319 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-22T16:33:12.498+07:00  INFO 44319 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-22T16:33:12.533+07:00  INFO 44319 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-22T16:33:12.537+07:00  INFO 44319 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-22T16:33:12.538+07:00  INFO 44319 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T16:33:12.545+07:00  WARN 44319 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-22T16:33:12.700+07:00  INFO 44319 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-22T16:33:13.176+07:00  INFO 44319 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-22T16:33:13.179+07:00  INFO 44319 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-22T16:33:13.210+07:00  INFO 44319 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-22T16:33:13.257+07:00  INFO 44319 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-22T16:33:13.363+07:00  INFO 44319 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-22T16:33:13.392+07:00  INFO 44319 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-22T16:33:13.425+07:00  INFO 44319 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 600854874ms : this is harmless.
2025-09-22T16:33:13.436+07:00  INFO 44319 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-22T16:33:13.439+07:00  INFO 44319 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-22T16:33:13.451+07:00  INFO 44319 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 892360830ms : this is harmless.
2025-09-22T16:33:13.452+07:00  INFO 44319 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-22T16:33:13.465+07:00  INFO 44319 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-22T16:33:13.466+07:00  INFO 44319 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-22T16:33:15.472+07:00  INFO 44319 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-22T16:33:15.472+07:00  INFO 44319 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T16:33:15.472+07:00  WARN 44319 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-22T16:33:15.815+07:00  INFO 44319 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 22/09/2025@16:30:00+0700 to 22/09/2025@16:45:00+0700
2025-09-22T16:33:15.815+07:00  INFO 44319 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 22/09/2025@16:30:00+0700 to 22/09/2025@16:45:00+0700
2025-09-22T16:33:16.437+07:00  INFO 44319 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-09-22T16:33:16.437+07:00  INFO 44319 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T16:33:16.438+07:00  WARN 44319 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-22T16:33:16.712+07:00  INFO 44319 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-09-22T16:33:16.712+07:00  INFO 44319 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-09-22T16:33:16.712+07:00  INFO 44319 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-09-22T16:33:16.712+07:00  INFO 44319 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-09-22T16:33:16.712+07:00  INFO 44319 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-09-22T16:33:18.608+07:00  WARN 44319 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 9eb8dcec-a403-4364-a056-7db241e3f245

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-22T16:33:18.612+07:00  INFO 44319 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-09-22T16:33:19.044+07:00  INFO 44319 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-22T16:33:19.045+07:00  INFO 44319 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-22T16:33:19.045+07:00  INFO 44319 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-22T16:33:19.045+07:00  INFO 44319 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-22T16:33:19.045+07:00  INFO 44319 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-09-22T16:33:19.045+07:00  INFO 44319 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-09-22T16:33:19.045+07:00  INFO 44319 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-22T16:33:19.045+07:00  INFO 44319 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-09-22T16:33:19.045+07:00  INFO 44319 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-09-22T16:33:19.045+07:00  INFO 44319 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-22T16:33:19.045+07:00  INFO 44319 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-09-22T16:33:19.045+07:00  INFO 44319 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-09-22T16:33:19.048+07:00  INFO 44319 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-22T16:33:19.049+07:00  INFO 44319 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-22T16:33:19.049+07:00  INFO 44319 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-22T16:33:19.114+07:00  INFO 44319 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-22T16:33:19.114+07:00  INFO 44319 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-22T16:33:19.115+07:00  INFO 44319 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-09-22T16:33:19.124+07:00  INFO 44319 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@33b1c75f{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-22T16:33:19.125+07:00  INFO 44319 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-09-22T16:33:19.126+07:00  INFO 44319 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-09-22T16:33:19.164+07:00  INFO 44319 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-09-22T16:33:19.164+07:00  INFO 44319 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-09-22T16:33:19.171+07:00  INFO 44319 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 14.779 seconds (process running for 15.078)
2025-09-22T16:34:06.136+07:00  INFO 44319 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T16:34:15.469+07:00  INFO 44319 --- [qtp124451741-36] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-09-22T16:34:22.203+07:00  INFO 44319 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T16:34:22.227+07:00  INFO 44319 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T16:35:04.300+07:00  INFO 44319 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T16:35:04.303+07:00  INFO 44319 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T16:35:08.786+07:00  INFO 44319 --- [qtp124451741-40] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node08vy5tpgj8zel1lkq4dslaxmar0
2025-09-22T16:35:08.786+07:00  INFO 44319 --- [qtp124451741-35] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01wio5s9lp9ewuvldz580cu1d71
2025-09-22T16:35:08.868+07:00  INFO 44319 --- [qtp124451741-40] n.d.module.session.ClientSessionManager  : Add a client session id = node08vy5tpgj8zel1lkq4dslaxmar0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:35:08.868+07:00  INFO 44319 --- [qtp124451741-35] n.d.module.session.ClientSessionManager  : Add a client session id = node01wio5s9lp9ewuvldz580cu1d71, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:35:09.352+07:00  INFO 44319 --- [qtp124451741-40] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:35:09.361+07:00  INFO 44319 --- [qtp124451741-35] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:35:12.372+07:00  INFO 44319 --- [qtp124451741-36] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T16:35:12.372+07:00  INFO 44319 --- [qtp124451741-61] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T16:35:12.456+07:00  INFO 44319 --- [qtp124451741-61] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T16:35:12.459+07:00  INFO 44319 --- [qtp124451741-36] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T16:36:06.417+07:00  INFO 44319 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T16:36:06.539+07:00  INFO 44319 --- [qtp124451741-64] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T16:36:08.030+07:00  INFO 44319 --- [qtp124451741-40] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T16:36:09.957+07:00  INFO 44319 --- [qtp124451741-41] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T16:36:12.033+07:00  INFO 44319 --- [qtp124451741-60] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T16:36:21.478+07:00  INFO 44319 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 12, expire count 0
2025-09-22T16:36:21.498+07:00  INFO 44319 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-22T16:36:48.498+07:00  INFO 44319 --- [qtp124451741-64] n.d.module.session.ClientSessionManager  : Add a client session id = node01wio5s9lp9ewuvldz580cu1d71, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:36:48.502+07:00  INFO 44319 --- [qtp124451741-39] n.d.module.session.ClientSessionManager  : Add a client session id = node01wio5s9lp9ewuvldz580cu1d71, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:36:48.509+07:00  INFO 44319 --- [qtp124451741-64] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:36:48.513+07:00  INFO 44319 --- [qtp124451741-39] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:36:59.712+07:00  INFO 44319 --- [qtp124451741-60] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T16:36:59.712+07:00  INFO 44319 --- [qtp124451741-41] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T16:36:59.721+07:00  INFO 44319 --- [qtp124451741-60] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T16:36:59.731+07:00  INFO 44319 --- [qtp124451741-41] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T16:37:03.579+07:00  INFO 44319 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T16:37:30.989+07:00  INFO 44319 --- [qtp124451741-41] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T16:37:32.324+07:00  INFO 44319 --- [qtp124451741-34] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T16:37:34.322+07:00  INFO 44319 --- [qtp124451741-39] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T16:37:36.173+07:00  INFO 44319 --- [qtp124451741-36] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T16:38:04.132+07:00  INFO 44319 --- [qtp124451741-36] .d.f.c.p.p.DomesticsPartnerServicePlugin : Domestics-partner: onPreSave called for partner: null, isNew: false
2025-09-22T16:38:04.135+07:00  INFO 44319 --- [qtp124451741-36] c.d.f.s.i.WorkflowPartnerServicePlugin   : Workflow-partner: onPreSave called for partner: null, isNew: false
2025-09-22T16:38:04.159+07:00  INFO 44319 --- [qtp124451741-36] c.d.f.core.partner.CRMPartnerLogic       : --- Create account: bfsone code = AG000079_TEMP
2025-09-22T16:38:04.344+07:00  INFO 44319 --- [qtp124451741-36] .d.f.c.p.p.DomesticsPartnerServicePlugin : Domestics-partner: onPostSave called for partner: 56632, isNew: true
2025-09-22T16:38:04.345+07:00  INFO 44319 --- [qtp124451741-36] c.d.f.s.i.WorkflowPartnerServicePlugin   : Workflow-partner: onPostSave called for partner: 56632, isNew: true
2025-09-22T16:38:04.380+07:00  INFO 44319 --- [qtp124451741-36] c.d.f.core.message.CRMMessageLogic       : 📅 Scheduled message [id=22506] [company=bee] type=MAIL for 22/09/2025@16:38:04+0700
2025-09-22T16:38:04.381+07:00  INFO 44319 --- [qtp124451741-36] c.d.f.core.message.MessageQueueManager   : Added message [22506] - scheduled at 22/09/2025@16:38:04+0700 - current session (22/09/2025@16:30:00+0700 to 22/09/2025@16:45:00+0700)

2025-09-22T16:38:04.384+07:00  WARN 44319 --- [qtp124451741-36] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-22T16:38:04.384+07:00 ERROR 44319 --- [qtp124451741-36] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "lgc_forwarder_partner_request_partner_id"
  Detail: Key (partner_id)=(56632) already exists.
2025-09-22T16:38:04.402+07:00 ERROR 44319 --- [qtp124451741-36] n.d.m.monitor.call.EndpointCallContext   : Start call with component CRMPartnerService, method createBFSOnePartner, arguments
[ {
  "tenantId" : "default",
  "companyId" : 4,
  "companyParentId" : null,
  "companyCode" : "bee",
  "companyLabel" : "Bee Corp",
  "companyFullName" : "BEE LOGISTICS CORPORATION",
  "loginId" : "hanah.vnhph",
  "accountId" : 11557,
  "token" : "858f997ddf07ff57af7a0837d9dd5a42",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node01wio5s9lp9ewuvldz580cu1d71",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : {
    "bee" : {
      "companyId" : 4,
      "companyParentId" : 0,
      "companyCode" : "bee",
      "companyLabel" : "Bee Corp",
      "companyFullName" : null
    }
  },
  "featurePermissions" : [ {
    "id" : 15097,
    "appId" : 3,
    "appModule" : "project",
    "appName" : "project",
    "companyId" : 4,
    "loginId" : "hanah.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 2700,
    "appId" : 26,
    "appModule" : "okr",
    "appName" : "okr",
    "companyId" : 4,
    "loginId" : "hanah.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 3735,
    "appId" : 12,
    "appModule" : "spreadsheet",
    "appName" : "spreadsheet",
    "companyId" : 4,
    "loginId" : "hanah.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 11538,
    "appId" : 69,
    "appModule" : "hr",
    "appName" : "user-kpi",
    "companyId" : 4,
    "loginId" : "hanah.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 14757,
    "appId" : 79,
    "appModule" : "company",
    "appName" : "user-asset",
    "companyId" : 4,
    "loginId" : "hanah.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 10508,
    "appId" : 16,
    "appModule" : "logistics",
    "appName" : "user-logistics-sales",
    "companyId" : 4,
    "loginId" : "hanah.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 15103,
    "appId" : 6,
    "appModule" : "company",
    "appName" : "company-space",
    "companyId" : 4,
    "loginId" : "hanah.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Read"
  }, {
    "id" : 6210,
    "appId" : 2,
    "appModule" : "user",
    "appName" : "my-employee-space",
    "companyId" : 4,
    "loginId" : "hanah.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  } ],
  "attributes" : { },
  "clientId" : "default:hanah.vnhph"
}, {
  "editState" : "ORIGIN",
  "loadState" : "FromDB",
  "version" : 0,
  "storageState" : "ACTIVE",
  "partnerCode" : "AG000079_TEMP",
  "partnerCodeTemp" : "AG000079_TEMP",
  "status" : "NEW",
  "partnerGroup" : "AGENTS",
  "category" : "AGENT_OVERSEAS",
  "name" : "IT TEST ***********",
  "label" : "IT TEST ***********",
  "localizedLabel" : "IT TEST ***********",
  "personalContact" : "IT TEST ***********",
  "position" : "IT TEST ***********",
  "email" : "lsmdafgmail.com",
  "cell" : "IT TEST ***********",
  "vipContact" : "zxcvzxcvzxcv",
  "vipPosition" : "zxcvzxcvxzcv",
  "vipCellphone" : "zxcvzxcvzxcv",
  "vipEmail" : "<EMAIL>",
  "fax" : "xzcvasdfasdf",
  "homePhone" : "zxcvasdfasdf",
  "workPhone" : "zxcvsadfasdf",
  "industryCode" : "I018",
  "industryLabel" : "Shipping; ports; fisheries; inland waterways ",
  "routing" : "HAN-HVCH",
  "countryId" : 244,
  "countryLabel" : "VIETNAM",
  "address" : "xcvzxcvzxc",
  "localizedAddress" : "xcvzxcvzxc",
  "bankName" : "zxcvasdfxcv\nádzxcv\nádf",
  "printCustomConfirmBillInfo" : "IT TEST ***********\nxcvzxcvzxc\nIT TEST ***********\nTEL :IT TEST *********** Fax:xzcvasdfasdf",
  "note" : "ádfasxczv",
  "suggestion" : "zxcvasdfasd",
  "inputUsername" : "HANAH.VNHPH",
  "scope" : "Domestic",
  "refund" : false,
  "sources" : [ {
    "editState" : "ORIGIN",
    "loadState" : "FromDB",
    "version" : 0,
    "storageState" : "ACTIVE",
    "sourceId" : 1.0,
    "sourceName" : "WCA"
  }, {
    "editState" : "ORIGIN",
    "loadState" : "FromDB",
    "version" : 0,
    "storageState" : "ACTIVE",
    "sourceId" : 5.0,
    "sourceName" : "A2B (not a member of associations in this list, Agent has one-way nominations to Bee)"
  }, {
    "editState" : "ORIGIN",
    "loadState" : "FromDB",
    "version" : 0,
    "storageState" : "ACTIVE",
    "sourceId" : 16.0,
    "sourceName" : "EAA"
  } ],
  "transportGroup" : [ ],
  "requestSalemanAccountId" : 11557,
  "requestSalemanLabel" : "LÊ THÚY HẰNG",
  "agent" : true
} ]
2025-09-22T16:38:04.402+07:00 ERROR 44319 --- [qtp124451741-36] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:118)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.springframework.dao.DataIntegrityViolationException: could not execute statement [ERROR: duplicate key value violates unique constraint "lgc_forwarder_partner_request_partner_id"
  Detail: Key (partner_id)=(56632) already exists.] [insert into lgc_forwarder_partner_request (approved_by_account_id,approved_by_label,approved_date,approved_note,bfsone_partner_code_temp,mail_cc,created_by,created_time,modified_by,modified_time,partner_id,partner_label,partner_name,request_by_account_id,request_by_label,request_date,status,storage_state,mail_to,version) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into lgc_forwarder_partner_request (approved_by_account_id,approved_by_label,approved_date,approved_note,bfsone_partner_code_temp,mail_cc,created_by,created_time,modified_by,modified_time,partner_id,partner_label,partner_name,request_by_account_id,request_by_label,request_date,status,storage_state,mail_to,version) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [lgc_forwarder_partner_request_partner_id]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:290)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:560)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:343)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:160)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:164)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy329.save(Unknown Source)
	at cloud.datatp.fforwarder.core.partner.PartnerRequestLogic.savePartnerRequest(PartnerRequestLogic.java:60)
	at cloud.datatp.fforwarder.core.partner.CRMPartnerLogic.createBFSPartner(CRMPartnerLogic.java:199)
	at cloud.datatp.fforwarder.core.partner.CRMPartnerService.createBFSOnePartner(CRMPartnerService.java:60)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at cloud.datatp.fforwarder.core.partner.CRMPartnerService$$SpringCGLIB$$0.createBFSOnePartner(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted
Caused by: org.hibernate.exception.ConstraintViolationException: could not execute statement [ERROR: duplicate key value violates unique constraint "lgc_forwarder_partner_request_partner_id"
  Detail: Key (partner_id)=(56632) already exists.] [insert into lgc_forwarder_partner_request (approved_by_account_id,approved_by_label,approved_date,approved_note,bfsone_partner_code_temp,mail_cc,created_by,created_time,modified_by,modified_time,partner_id,partner_label,partner_name,request_by_account_id,request_by_label,request_date,status,storage_state,mail_to,version) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]
	at org.hibernate.exception.internal.SQLStateConversionDelegate.convert(SQLStateConversionDelegate.java:97)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:197)
	at org.hibernate.id.insert.GetGeneratedKeysDelegate.performInsert(GetGeneratedKeysDelegate.java:107)
	at org.hibernate.engine.jdbc.mutation.internal.MutationExecutorPostInsertSingleTable.execute(MutationExecutorPostInsertSingleTable.java:100)
	at org.hibernate.persister.entity.mutation.InsertCoordinator.doStaticInserts(InsertCoordinator.java:175)
	at org.hibernate.persister.entity.mutation.InsertCoordinator.coordinateInsert(InsertCoordinator.java:113)
	at org.hibernate.persister.entity.AbstractEntityPersister.insert(AbstractEntityPersister.java:2868)
	at org.hibernate.action.internal.EntityIdentityInsertAction.execute(EntityIdentityInsertAction.java:81)
	at org.hibernate.engine.spi.ActionQueue.execute(ActionQueue.java:670)
	at org.hibernate.engine.spi.ActionQueue.addResolvedEntityInsertAction(ActionQueue.java:291)
	at org.hibernate.engine.spi.ActionQueue.addInsertAction(ActionQueue.java:272)
	at org.hibernate.engine.spi.ActionQueue.addAction(ActionQueue.java:322)
	at org.hibernate.event.internal.AbstractSaveEventListener.addInsertAction(AbstractSaveEventListener.java:386)
	at org.hibernate.event.internal.AbstractSaveEventListener.performSaveOrReplicate(AbstractSaveEventListener.java:300)
	at org.hibernate.event.internal.AbstractSaveEventListener.performSave(AbstractSaveEventListener.java:219)
	at org.hibernate.event.internal.AbstractSaveEventListener.saveWithGeneratedId(AbstractSaveEventListener.java:134)
	at org.hibernate.event.internal.DefaultPersistEventListener.entityIsTransient(DefaultPersistEventListener.java:175)
	at org.hibernate.event.internal.DefaultPersistEventListener.persist(DefaultPersistEventListener.java:93)
	at org.hibernate.event.internal.DefaultPersistEventListener.onPersist(DefaultPersistEventListener.java:77)
	at org.hibernate.event.internal.DefaultPersistEventListener.onPersist(DefaultPersistEventListener.java:54)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.firePersist(SessionImpl.java:754)
	at org.hibernate.internal.SessionImpl.persist(SessionImpl.java:738)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler.invoke(SharedEntityManagerCreator.java:320)
	at jdk.proxy2/jdk.proxy2.$Proxy181.persist(Unknown Source)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.save(SimpleJpaRepository.java:618)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:277)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:516)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:285)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:628)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:168)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:143)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	... 119 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: duplicate key value violates unique constraint "lgc_forwarder_partner_request_partner_id"
  Detail: Key (partner_id)=(56632) already exists.
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2725)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2412)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:371)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:502)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:419)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:194)
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:155)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:194)
	... 164 common frames omitted

2025-09-22T16:38:04.406+07:00  INFO 44319 --- [qtp124451741-36] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint CRMPartnerService/createBFSOnePartner
2025-09-22T16:38:06.680+07:00  INFO 44319 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T16:38:25.721+07:00  INFO 44319 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 12, expire count 0
2025-09-22T16:38:25.729+07:00  INFO 44319 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-22T16:39:02.805+07:00  INFO 44319 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T16:40:05.917+07:00  INFO 44319 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T16:40:05.920+07:00  INFO 44319 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T16:40:25.982+07:00  INFO 44319 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-22T16:40:25.993+07:00  INFO 44319 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T16:40:55.605+07:00  INFO 44319 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@33b1c75f{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-22T16:40:55.606+07:00  INFO 44319 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-22T16:40:55.606+07:00  INFO 44319 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-22T16:40:55.606+07:00  INFO 44319 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-22T16:40:55.607+07:00  INFO 44319 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-22T16:40:55.607+07:00  INFO 44319 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-22T16:40:55.607+07:00  INFO 44319 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-22T16:40:55.607+07:00  INFO 44319 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-22T16:40:55.607+07:00  INFO 44319 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-09-22T16:40:55.607+07:00  INFO 44319 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-09-22T16:40:55.607+07:00  INFO 44319 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-22T16:40:55.607+07:00  INFO 44319 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-09-22T16:40:55.607+07:00  INFO 44319 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-09-22T16:40:55.607+07:00  INFO 44319 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-22T16:40:55.607+07:00  INFO 44319 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-09-22T16:40:55.607+07:00  INFO 44319 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-09-22T16:40:55.622+07:00  INFO 44319 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T16:40:55.692+07:00  INFO 44319 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-22T16:40:55.695+07:00  INFO 44319 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-22T16:40:55.716+07:00  INFO 44319 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-22T16:40:55.717+07:00  INFO 44319 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-22T16:40:55.718+07:00  INFO 44319 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-22T16:40:55.719+07:00  INFO 44319 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-22T16:40:55.722+07:00  INFO 44319 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-22T16:40:55.722+07:00  INFO 44319 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-22T16:40:55.723+07:00  INFO 44319 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-22T16:40:55.724+07:00  INFO 44319 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-22T16:40:55.725+07:00  INFO 44319 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-22T16:40:55.725+07:00  INFO 44319 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-22T16:40:55.727+07:00  INFO 44319 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-22T16:40:55.727+07:00  INFO 44319 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-22T16:40:55.728+07:00  INFO 44319 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-22T16:40:55.730+07:00  INFO 44319 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@74940388{STOPPING}[12.0.15,sto=0]
2025-09-22T16:40:55.734+07:00  INFO 44319 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-22T16:40:55.739+07:00  INFO 44319 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@5de769c8{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.16712201054209271348/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@10ecdd85{STOPPED}}
