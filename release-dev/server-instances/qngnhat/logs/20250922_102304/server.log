2025-09-22T10:23:05.145+07:00  INFO 6179 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 6179 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-22T10:23:05.146+07:00  INFO 6179 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-22T10:23:05.846+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T10:23:05.914+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 64 ms. Found 22 JPA repository interfaces.
2025-09-22T10:23:05.924+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T10:23:05.926+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-22T10:23:05.926+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T10:23:05.969+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 43 ms. Found 10 JPA repository interfaces.
2025-09-22T10:23:05.970+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T10:23:05.973+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-22T10:23:05.982+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T10:23:05.987+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-09-22T10:23:05.995+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T10:23:05.997+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 2 JPA repository interfaces.
2025-09-22T10:23:05.997+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T10:23:06.002+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-09-22T10:23:06.004+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T10:23:06.009+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 5 JPA repository interfaces.
2025-09-22T10:23:06.013+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T10:23:06.016+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 3 JPA repository interfaces.
2025-09-22T10:23:06.017+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T10:23:06.017+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-22T10:23:06.017+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T10:23:06.024+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 10 JPA repository interfaces.
2025-09-22T10:23:06.029+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T10:23:06.032+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 3 JPA repository interfaces.
2025-09-22T10:23:06.035+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T10:23:06.039+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-22T10:23:06.039+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T10:23:06.047+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-09-22T10:23:06.047+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T10:23:06.051+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-09-22T10:23:06.051+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T10:23:06.051+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-22T10:23:06.051+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T10:23:06.052+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-22T10:23:06.053+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T10:23:06.057+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-09-22T10:23:06.057+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T10:23:06.058+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-22T10:23:06.058+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T10:23:06.058+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-22T10:23:06.059+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T10:23:06.069+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 19 JPA repository interfaces.
2025-09-22T10:23:06.080+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T10:23:06.086+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 8 JPA repository interfaces.
2025-09-22T10:23:06.086+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T10:23:06.089+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-09-22T10:23:06.089+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T10:23:06.093+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-22T10:23:06.093+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T10:23:06.099+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-09-22T10:23:06.099+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T10:23:06.104+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-09-22T10:23:06.104+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T10:23:06.113+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 13 JPA repository interfaces.
2025-09-22T10:23:06.113+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T10:23:06.122+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 14 JPA repository interfaces.
2025-09-22T10:23:06.122+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T10:23:06.137+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 14 ms. Found 24 JPA repository interfaces.
2025-09-22T10:23:06.137+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T10:23:06.138+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-22T10:23:06.144+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T10:23:06.144+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-22T10:23:06.144+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T10:23:06.151+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-22T10:23:06.153+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T10:23:06.188+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 34 ms. Found 66 JPA repository interfaces.
2025-09-22T10:23:06.188+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T10:23:06.189+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-22T10:23:06.194+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T10:23:06.196+07:00  INFO 6179 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-22T10:23:06.399+07:00  INFO 6179 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-22T10:23:06.403+07:00  INFO 6179 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-22T10:23:06.675+07:00  WARN 6179 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-22T10:23:06.878+07:00  INFO 6179 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-22T10:23:06.880+07:00  INFO 6179 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-22T10:23:06.892+07:00  INFO 6179 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-22T10:23:06.892+07:00  INFO 6179 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1641 ms
2025-09-22T10:23:06.953+07:00  WARN 6179 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-22T10:23:06.953+07:00  INFO 6179 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-22T10:23:07.050+07:00  INFO 6179 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@16ea56ab
2025-09-22T10:23:07.050+07:00  INFO 6179 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-22T10:23:07.055+07:00  WARN 6179 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-22T10:23:07.055+07:00  INFO 6179 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-22T10:23:07.061+07:00  INFO 6179 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@413773e6
2025-09-22T10:23:07.061+07:00  INFO 6179 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-22T10:23:07.061+07:00  WARN 6179 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-22T10:23:07.061+07:00  INFO 6179 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-22T10:23:07.069+07:00  INFO 6179 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@6e057374
2025-09-22T10:23:07.069+07:00  INFO 6179 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-22T10:23:07.069+07:00  WARN 6179 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-22T10:23:07.069+07:00  INFO 6179 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-22T10:23:07.078+07:00  INFO 6179 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@511e0453
2025-09-22T10:23:07.078+07:00  INFO 6179 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-22T10:23:07.078+07:00  WARN 6179 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-22T10:23:07.078+07:00  INFO 6179 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-22T10:23:07.083+07:00  INFO 6179 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@130901fd
2025-09-22T10:23:07.084+07:00  INFO 6179 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-22T10:23:07.084+07:00  INFO 6179 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-22T10:23:07.129+07:00  INFO 6179 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-22T10:23:07.131+07:00  INFO 6179 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@78ded8c9{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.16766584520067340361/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@30926583{STARTED}}
2025-09-22T10:23:07.131+07:00  INFO 6179 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@78ded8c9{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.16766584520067340361/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@30926583{STARTED}}
2025-09-22T10:23:07.177+07:00  INFO 6179 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@72f86029{STARTING}[12.0.15,sto=0] @2695ms
2025-09-22T10:23:07.236+07:00  INFO 6179 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-22T10:23:07.262+07:00  INFO 6179 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-22T10:23:07.278+07:00  INFO 6179 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-22T10:23:07.401+07:00  INFO 6179 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-22T10:23:07.444+07:00  WARN 6179 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-22T10:23:08.074+07:00  INFO 6179 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-22T10:23:08.083+07:00  INFO 6179 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@29cd9511] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-22T10:23:08.289+07:00  INFO 6179 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-22T10:23:08.503+07:00  INFO 6179 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.monitor.activity", "net.datatp.module.wfms", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "cloud.datatp.module.fleet", "net.datatp.module.okr", "net.datatp.module.core.template", "cloud.datatp.module.pegasus.shipment", "cloud.datatp.gps", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "cloud.datatp.tms", "net.datatp.module.company.tmpl", "net.datatp.module.groovy", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "net.datatp.module.resource", "cloud.datatp.vendor", "net.datatp.module.company.hr", "net.datatp.module.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "cloud.datatp.module.odoo", "net.datatp.module.chatbot", "net.datatp.module.core.print", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.zalo", "net.datatp.module.accounting" ]
2025-09-22T10:23:08.505+07:00  INFO 6179 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-22T10:23:08.512+07:00  INFO 6179 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-22T10:23:08.513+07:00  INFO 6179 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-22T10:23:08.541+07:00  INFO 6179 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-22T10:23:08.551+07:00  WARN 6179 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-22T10:23:10.547+07:00  INFO 6179 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-22T10:23:10.548+07:00  INFO 6179 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@34a5633d] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-22T10:23:10.787+07:00  WARN 6179 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-22T10:23:10.787+07:00  WARN 6179 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-22T10:23:10.798+07:00  WARN 6179 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-22T10:23:10.798+07:00  WARN 6179 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-22T10:23:10.811+07:00  WARN 6179 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-22T10:23:10.811+07:00  WARN 6179 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-22T10:23:11.421+07:00  INFO 6179 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-22T10:23:11.428+07:00  INFO 6179 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-22T10:23:11.430+07:00  INFO 6179 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-22T10:23:11.455+07:00  INFO 6179 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-22T10:23:11.460+07:00  WARN 6179 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-22T10:23:11.995+07:00  INFO 6179 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-22T10:23:11.995+07:00  INFO 6179 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@31534081] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-22T10:23:12.111+07:00  WARN 6179 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-22T10:23:12.111+07:00  WARN 6179 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-22T10:23:12.466+07:00  INFO 6179 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-22T10:23:12.496+07:00  INFO 6179 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-22T10:23:12.501+07:00  INFO 6179 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-22T10:23:12.501+07:00  INFO 6179 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T10:23:12.508+07:00  WARN 6179 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-22T10:23:12.637+07:00  INFO 6179 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-22T10:23:13.094+07:00  INFO 6179 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-22T10:23:13.097+07:00  INFO 6179 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-22T10:23:13.132+07:00  INFO 6179 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-22T10:23:13.177+07:00  INFO 6179 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-22T10:23:13.295+07:00  INFO 6179 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-22T10:23:13.322+07:00  INFO 6179 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-22T10:23:13.348+07:00  INFO 6179 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 578649505ms : this is harmless.
2025-09-22T10:23:13.357+07:00  INFO 6179 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-22T10:23:13.360+07:00  INFO 6179 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-22T10:23:13.375+07:00  INFO 6179 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 870155460ms : this is harmless.
2025-09-22T10:23:13.376+07:00  INFO 6179 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-22T10:23:13.393+07:00  INFO 6179 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-22T10:23:13.394+07:00  INFO 6179 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-22T10:23:15.263+07:00  INFO 6179 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-22T10:23:15.263+07:00  INFO 6179 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T10:23:15.264+07:00  WARN 6179 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-22T10:23:15.560+07:00  INFO 6179 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 22/09/2025@10:15:00+0700 to 22/09/2025@10:30:00+0700
2025-09-22T10:23:15.560+07:00  INFO 6179 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 22/09/2025@10:15:00+0700 to 22/09/2025@10:30:00+0700
2025-09-22T10:23:16.129+07:00  INFO 6179 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-09-22T10:23:16.129+07:00  INFO 6179 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T10:23:16.129+07:00  WARN 6179 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-22T10:23:16.425+07:00  INFO 6179 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-09-22T10:23:16.425+07:00  INFO 6179 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-09-22T10:23:16.425+07:00  INFO 6179 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-09-22T10:23:16.425+07:00  INFO 6179 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-09-22T10:23:16.425+07:00  INFO 6179 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-09-22T10:23:18.290+07:00  WARN 6179 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 2049922a-67e9-4d12-b1da-4a2fa2bd3b8c

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-22T10:23:18.294+07:00  INFO 6179 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-09-22T10:23:18.797+07:00  INFO 6179 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-22T10:23:18.797+07:00  INFO 6179 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-22T10:23:18.797+07:00  INFO 6179 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-22T10:23:18.797+07:00  INFO 6179 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-22T10:23:18.797+07:00  INFO 6179 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-09-22T10:23:18.797+07:00  INFO 6179 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-09-22T10:23:18.797+07:00  INFO 6179 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-22T10:23:18.797+07:00  INFO 6179 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-09-22T10:23:18.797+07:00  INFO 6179 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-09-22T10:23:18.797+07:00  INFO 6179 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-22T10:23:18.797+07:00  INFO 6179 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-09-22T10:23:18.797+07:00  INFO 6179 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-09-22T10:23:18.800+07:00  INFO 6179 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-22T10:23:18.800+07:00  INFO 6179 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-22T10:23:18.800+07:00  INFO 6179 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-22T10:23:18.873+07:00  INFO 6179 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-22T10:23:18.873+07:00  INFO 6179 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-22T10:23:18.875+07:00  INFO 6179 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-09-22T10:23:18.884+07:00  INFO 6179 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@727f985f{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-22T10:23:18.885+07:00  INFO 6179 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-09-22T10:23:18.886+07:00  INFO 6179 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-09-22T10:23:18.984+07:00  INFO 6179 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-09-22T10:23:18.984+07:00  INFO 6179 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-09-22T10:23:18.992+07:00  INFO 6179 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 14.164 seconds (process running for 14.509)
2025-09-22T10:24:06.903+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T10:24:10.750+07:00  INFO 6179 --- [qtp722334906-36] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node02lidztnetukec6rqg5hz3ipu0
2025-09-22T10:24:11.141+07:00  INFO 6179 --- [qtp722334906-36] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = e431474960369f90dc37c00e47b50d9c
2025-09-22T10:24:11.549+07:00  INFO 6179 --- [qtp722334906-36] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-22T10:24:21.971+07:00  INFO 6179 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-22T10:24:21.985+07:00  INFO 6179 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T10:25:04.059+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T10:25:04.062+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T10:26:06.168+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T10:26:21.210+07:00  INFO 6179 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-22T10:26:21.228+07:00  INFO 6179 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T10:27:03.303+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T10:28:02.660+07:00  INFO 6179 --- [qtp722334906-60] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = e431474960369f90dc37c00e47b50d9c
2025-09-22T10:28:02.661+07:00  INFO 6179 --- [qtp722334906-61] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = e431474960369f90dc37c00e47b50d9c
2025-09-22T10:28:02.671+07:00  INFO 6179 --- [qtp722334906-61] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-22T10:28:02.671+07:00  INFO 6179 --- [qtp722334906-60] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-22T10:28:02.748+07:00 ERROR 6179 --- [qtp722334906-64] n.d.m.monitor.call.EndpointCallService   : Unknown Error when handle the endpoint service/[GET] get-company-node
2025-09-22T10:28:02.748+07:00 ERROR 6179 --- [qtp722334906-64] n.d.m.monitor.call.EndpointCallService   : Exception: 

net.datatp.util.error.RuntimeError: /companies/beehph/ is not existed!
	at net.datatp.module.storage.fs.FSStorageService.ensureSystemFileExists(FSStorageService.java:427)
	at net.datatp.module.storage.fs.FSStorageService.getNode(FSStorageService.java:111)
	at net.datatp.module.storage.Storage.getNode(Storage.java:42)
	at net.datatp.module.storage.http.StorageController.lambda$companyGetSNode$2(StorageController.java:90)
	at net.datatp.module.http.rest.v1.AbstractController$1.doCall(AbstractController.java:65)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.http.rest.v1.AbstractController.execute(AbstractController.java:70)
	at net.datatp.module.storage.http.StorageController.companyGetSNode(StorageController.java:92)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:500)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)

2025-09-22T10:28:02.761+07:00 ERROR 6179 --- [qtp722334906-60] n.d.m.monitor.call.EndpointCallService   : Unknown Error when handle the endpoint service/[GET] get-company-node
2025-09-22T10:28:02.761+07:00 ERROR 6179 --- [qtp722334906-60] n.d.m.monitor.call.EndpointCallService   : Exception: 

net.datatp.util.error.RuntimeError: /companies/beehph/ is not existed!
	at net.datatp.module.storage.fs.FSStorageService.ensureSystemFileExists(FSStorageService.java:427)
	at net.datatp.module.storage.fs.FSStorageService.getNode(FSStorageService.java:111)
	at net.datatp.module.storage.Storage.getNode(Storage.java:42)
	at net.datatp.module.storage.http.StorageController.lambda$companyGetSNode$2(StorageController.java:90)
	at net.datatp.module.http.rest.v1.AbstractController$1.doCall(AbstractController.java:65)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.http.rest.v1.AbstractController.execute(AbstractController.java:70)
	at net.datatp.module.storage.http.StorageController.companyGetSNode(StorageController.java:92)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:500)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)

2025-09-22T10:28:06.041+07:00  INFO 6179 --- [qtp722334906-61] n.d.m.c.a.CompanyAuthenticationService   : User minhtv logout successfully 
2025-09-22T10:28:06.435+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T10:28:08.933+07:00  INFO 6179 --- [qtp722334906-60] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:28:08.939+07:00  INFO 6179 --- [qtp722334906-60] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:28:12.152+07:00  INFO 6179 --- [qtp722334906-69] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T10:28:12.152+07:00  INFO 6179 --- [qtp722334906-61] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T10:28:12.239+07:00  INFO 6179 --- [qtp722334906-61] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T10:28:12.239+07:00  INFO 6179 --- [qtp722334906-69] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T10:28:25.511+07:00  INFO 6179 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 0
2025-09-22T10:28:25.524+07:00  INFO 6179 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T10:28:32.724+07:00  INFO 6179 --- [qtp722334906-40] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:28:32.725+07:00  INFO 6179 --- [qtp722334906-68] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:28:32.738+07:00  INFO 6179 --- [qtp722334906-68] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:28:32.738+07:00  INFO 6179 --- [qtp722334906-40] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:28:36.751+07:00  INFO 6179 --- [qtp722334906-61] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:28:36.778+07:00  INFO 6179 --- [qtp722334906-61] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:28:36.799+07:00  INFO 6179 --- [qtp722334906-64] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:28:36.809+07:00  INFO 6179 --- [qtp722334906-64] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:28:47.354+07:00  INFO 6179 --- [qtp722334906-61] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:28:47.355+07:00  INFO 6179 --- [qtp722334906-60] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:28:47.371+07:00  INFO 6179 --- [qtp722334906-60] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:28:47.371+07:00  INFO 6179 --- [qtp722334906-61] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:29:02.577+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T10:29:10.711+07:00  INFO 6179 --- [qtp722334906-61] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T10:29:10.711+07:00  INFO 6179 --- [qtp722334906-60] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T10:29:10.719+07:00  INFO 6179 --- [qtp722334906-60] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T10:29:10.719+07:00  INFO 6179 --- [qtp722334906-61] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T10:29:27.972+07:00  INFO 6179 --- [qtp722334906-60] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:29:27.982+07:00  INFO 6179 --- [qtp722334906-60] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:29:28.006+07:00  INFO 6179 --- [qtp722334906-69] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:29:28.012+07:00  INFO 6179 --- [qtp722334906-69] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:29:35.093+07:00  INFO 6179 --- [qtp722334906-64] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:29:35.094+07:00  INFO 6179 --- [qtp722334906-60] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:29:35.108+07:00  INFO 6179 --- [qtp722334906-60] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:29:35.108+07:00  INFO 6179 --- [qtp722334906-64] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:29:41.213+07:00  INFO 6179 --- [qtp722334906-61] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:29:41.215+07:00  INFO 6179 --- [qtp722334906-40] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:29:41.237+07:00  INFO 6179 --- [qtp722334906-40] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:29:41.238+07:00  INFO 6179 --- [qtp722334906-61] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:29:45.124+07:00  INFO 6179 --- [qtp722334906-70] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T10:29:45.129+07:00  INFO 6179 --- [qtp722334906-39] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T10:29:45.130+07:00  INFO 6179 --- [qtp722334906-70] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T10:29:45.134+07:00  INFO 6179 --- [qtp722334906-39] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T10:29:59.565+07:00  INFO 6179 --- [qtp722334906-61] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:29:59.569+07:00  INFO 6179 --- [qtp722334906-68] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:29:59.587+07:00  INFO 6179 --- [qtp722334906-61] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:29:59.587+07:00  INFO 6179 --- [qtp722334906-68] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:30:02.894+07:00  INFO 6179 --- [qtp722334906-69] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T10:30:02.898+07:00  INFO 6179 --- [qtp722334906-36] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T10:30:02.900+07:00  INFO 6179 --- [qtp722334906-69] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T10:30:02.902+07:00  INFO 6179 --- [qtp722334906-36] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T10:30:05.689+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T10:30:05.690+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T10:30:05.692+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-22T10:30:05.698+07:00  INFO 6179 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 22/09/2025@10:30:05+0700
2025-09-22T10:30:05.714+07:00  INFO 6179 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 22/09/2025@10:30:00+0700 to 22/09/2025@10:45:00+0700
2025-09-22T10:30:05.715+07:00  INFO 6179 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 22/09/2025@10:30:00+0700 to 22/09/2025@10:45:00+0700
2025-09-22T10:30:25.800+07:00  INFO 6179 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 11, expire count 0
2025-09-22T10:30:25.818+07:00  INFO 6179 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T10:30:39.445+07:00  INFO 6179 --- [qtp722334906-68] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:30:39.446+07:00  INFO 6179 --- [qtp722334906-69] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:30:39.465+07:00  INFO 6179 --- [qtp722334906-69] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:30:39.466+07:00  INFO 6179 --- [qtp722334906-68] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:30:53.988+07:00  INFO 6179 --- [qtp722334906-60] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:30:53.990+07:00  INFO 6179 --- [qtp722334906-40] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:30:54.002+07:00  INFO 6179 --- [qtp722334906-60] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:30:54.002+07:00  INFO 6179 --- [qtp722334906-40] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:30:59.128+07:00  INFO 6179 --- [qtp722334906-64] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T10:30:59.134+07:00  INFO 6179 --- [qtp722334906-36] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T10:30:59.137+07:00  INFO 6179 --- [qtp722334906-64] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T10:30:59.139+07:00  INFO 6179 --- [qtp722334906-36] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T10:31:06.880+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T10:32:04.986+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T10:32:25.066+07:00  INFO 6179 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-22T10:32:25.071+07:00  INFO 6179 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T10:33:06.129+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T10:34:04.244+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T10:34:25.323+07:00  INFO 6179 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 4
2025-09-22T10:34:25.348+07:00  INFO 6179 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-22T10:35:06.422+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T10:35:06.426+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T10:36:03.525+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T10:36:24.537+07:00  INFO 6179 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T10:36:24.544+07:00  INFO 6179 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T10:37:06.608+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T10:38:02.717+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T10:38:23.791+07:00  INFO 6179 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 7
2025-09-22T10:38:23.802+07:00  INFO 6179 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T10:39:05.859+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T10:40:06.948+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T10:40:06.953+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T10:40:23.001+07:00  INFO 6179 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 3
2025-09-22T10:40:23.008+07:00  INFO 6179 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T10:41:05.071+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T10:42:06.176+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T10:42:22.224+07:00  INFO 6179 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 8
2025-09-22T10:42:22.234+07:00  INFO 6179 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T10:43:04.313+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T10:43:05.036+07:00  INFO 6179 --- [qtp722334906-68] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:43:05.036+07:00  INFO 6179 --- [qtp722334906-60] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:43:05.053+07:00  INFO 6179 --- [qtp722334906-68] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:43:05.053+07:00  INFO 6179 --- [qtp722334906-60] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:43:18.539+07:00  INFO 6179 --- [qtp722334906-60] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:43:18.540+07:00  INFO 6179 --- [qtp722334906-68] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:43:18.551+07:00  INFO 6179 --- [qtp722334906-60] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:43:18.551+07:00  INFO 6179 --- [qtp722334906-68] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:43:29.930+07:00  INFO 6179 --- [qtp722334906-69] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:43:29.931+07:00  INFO 6179 --- [qtp722334906-36] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:43:29.939+07:00  INFO 6179 --- [qtp722334906-69] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:43:29.941+07:00  INFO 6179 --- [qtp722334906-36] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:44:06.463+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T10:44:21.504+07:00  INFO 6179 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-22T10:44:21.518+07:00  INFO 6179 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T10:45:03.577+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-22T10:45:03.583+07:00  INFO 6179 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 22/09/2025@10:45:03+0700
2025-09-22T10:45:03.617+07:00  INFO 6179 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 22/09/2025@10:45:00+0700 to 22/09/2025@11:00:00+0700
2025-09-22T10:45:03.617+07:00  INFO 6179 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 22/09/2025@10:45:00+0700 to 22/09/2025@11:00:00+0700
2025-09-22T10:45:03.618+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T10:45:03.618+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T10:45:17.833+07:00  INFO 6179 --- [qtp722334906-73] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T10:45:17.833+07:00  INFO 6179 --- [qtp722334906-61] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T10:45:17.846+07:00  INFO 6179 --- [qtp722334906-61] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T10:45:17.846+07:00  INFO 6179 --- [qtp722334906-73] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T10:45:57.356+07:00  INFO 6179 --- [qtp722334906-61] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:45:57.383+07:00  INFO 6179 --- [qtp722334906-61] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:45:57.398+07:00  INFO 6179 --- [qtp722334906-126] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:45:57.406+07:00  INFO 6179 --- [qtp722334906-126] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:46:06.727+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T10:46:13.128+07:00  INFO 6179 --- [qtp722334906-60] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T10:46:13.128+07:00  INFO 6179 --- [qtp722334906-68] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T10:46:13.140+07:00  INFO 6179 --- [qtp722334906-60] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T10:46:13.143+07:00  INFO 6179 --- [qtp722334906-68] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T10:46:25.778+07:00  INFO 6179 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 11, expire count 0
2025-09-22T10:46:25.805+07:00  INFO 6179 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-22T10:46:51.979+07:00  INFO 6179 --- [qtp722334906-61] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:46:51.993+07:00  INFO 6179 --- [qtp722334906-61] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:46:52.007+07:00  INFO 6179 --- [qtp722334906-129] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:46:52.050+07:00  INFO 6179 --- [qtp722334906-129] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:46:54.325+07:00  INFO 6179 --- [qtp722334906-69] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:46:54.336+07:00  INFO 6179 --- [qtp722334906-69] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:46:54.349+07:00  INFO 6179 --- [qtp722334906-73] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:46:54.353+07:00  INFO 6179 --- [qtp722334906-73] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:46:58.656+07:00  INFO 6179 --- [qtp722334906-60] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T10:46:58.656+07:00  INFO 6179 --- [qtp722334906-129] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T10:46:58.661+07:00  INFO 6179 --- [qtp722334906-60] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T10:46:58.661+07:00  INFO 6179 --- [qtp722334906-129] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T10:47:02.879+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T10:47:49.770+07:00  INFO 6179 --- [qtp722334906-129] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:47:49.773+07:00  INFO 6179 --- [qtp722334906-60] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:47:49.783+07:00  INFO 6179 --- [qtp722334906-60] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:47:49.783+07:00  INFO 6179 --- [qtp722334906-129] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:48:05.982+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T10:48:06.421+07:00  INFO 6179 --- [qtp722334906-126] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T10:48:06.421+07:00  INFO 6179 --- [qtp722334906-73] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T10:48:06.428+07:00  INFO 6179 --- [qtp722334906-126] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T10:48:06.436+07:00  INFO 6179 --- [qtp722334906-73] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T10:48:08.255+07:00  INFO 6179 --- [qtp722334906-61] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T10:48:08.256+07:00  INFO 6179 --- [qtp722334906-73] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T10:48:14.686+07:00  INFO 6179 --- [qtp722334906-121] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T10:48:14.687+07:00  INFO 6179 --- [qtp722334906-126] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T10:48:25.016+07:00  INFO 6179 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-22T10:48:25.019+07:00  INFO 6179 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T10:48:40.438+07:00  INFO 6179 --- [qtp722334906-129] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:48:40.440+07:00  INFO 6179 --- [qtp722334906-61] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:48:40.457+07:00  INFO 6179 --- [qtp722334906-129] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:48:40.457+07:00  INFO 6179 --- [qtp722334906-61] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:49:02.093+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T10:49:26.149+07:00  INFO 6179 --- [qtp722334906-36] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T10:49:26.149+07:00  INFO 6179 --- [qtp722334906-73] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T10:49:26.155+07:00  INFO 6179 --- [qtp722334906-36] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T10:49:26.155+07:00  INFO 6179 --- [qtp722334906-73] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T10:49:27.654+07:00  INFO 6179 --- [qtp722334906-121] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T10:49:27.654+07:00  INFO 6179 --- [qtp722334906-60] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T10:49:45.420+07:00  INFO 6179 --- [qtp722334906-68] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:49:45.421+07:00  INFO 6179 --- [qtp722334906-73] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:49:45.426+07:00  INFO 6179 --- [qtp722334906-68] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:49:45.426+07:00  INFO 6179 --- [qtp722334906-73] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:50:05.202+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T10:50:05.204+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T10:50:25.250+07:00  INFO 6179 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 12, expire count 0
2025-09-22T10:50:25.298+07:00  INFO 6179 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-22T10:50:50.769+07:00  INFO 6179 --- [qtp722334906-73] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:50:50.772+07:00  INFO 6179 --- [qtp722334906-126] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:50:50.780+07:00  INFO 6179 --- [qtp722334906-73] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:50:50.780+07:00  INFO 6179 --- [qtp722334906-126] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:50:58.799+07:00  INFO 6179 --- [qtp722334906-37] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:50:58.807+07:00  INFO 6179 --- [qtp722334906-37] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:50:58.818+07:00  INFO 6179 --- [qtp722334906-130] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:50:58.820+07:00  INFO 6179 --- [qtp722334906-130] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:51:03.778+07:00  INFO 6179 --- [qtp722334906-73] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:51:03.783+07:00  INFO 6179 --- [qtp722334906-73] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:51:03.813+07:00  INFO 6179 --- [qtp722334906-121] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:51:03.817+07:00  INFO 6179 --- [qtp722334906-121] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:51:06.368+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T10:51:12.763+07:00  INFO 6179 --- [qtp722334906-37] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:51:12.764+07:00  INFO 6179 --- [qtp722334906-121] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:51:12.771+07:00  INFO 6179 --- [qtp722334906-121] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:51:12.771+07:00  INFO 6179 --- [qtp722334906-37] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:51:36.768+07:00  INFO 6179 --- [qtp722334906-68] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:51:36.769+07:00  INFO 6179 --- [qtp722334906-73] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:51:36.775+07:00  INFO 6179 --- [qtp722334906-68] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:51:36.775+07:00  INFO 6179 --- [qtp722334906-73] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:51:44.763+07:00  INFO 6179 --- [qtp722334906-73] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:51:44.767+07:00  INFO 6179 --- [qtp722334906-121] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:51:44.771+07:00  INFO 6179 --- [qtp722334906-73] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:51:44.778+07:00  INFO 6179 --- [qtp722334906-121] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:51:52.694+07:00  INFO 6179 --- [qtp722334906-121] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T10:51:52.694+07:00  INFO 6179 --- [qtp722334906-73] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T10:51:52.701+07:00  INFO 6179 --- [qtp722334906-121] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T10:51:52.702+07:00  INFO 6179 --- [qtp722334906-73] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T10:52:04.457+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T10:52:25.500+07:00  INFO 6179 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 10, expire count 1
2025-09-22T10:52:25.515+07:00  INFO 6179 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-22T10:52:32.927+07:00  INFO 6179 --- [qtp722334906-68] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:52:32.933+07:00  INFO 6179 --- [qtp722334906-68] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:52:32.963+07:00  INFO 6179 --- [qtp722334906-122] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:52:32.967+07:00  INFO 6179 --- [qtp722334906-122] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:52:40.305+07:00  INFO 6179 --- [qtp722334906-69] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:52:40.306+07:00  INFO 6179 --- [qtp722334906-68] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:52:40.310+07:00  INFO 6179 --- [qtp722334906-69] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:52:40.310+07:00  INFO 6179 --- [qtp722334906-68] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:52:49.429+07:00  INFO 6179 --- [qtp722334906-37] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:52:49.430+07:00  INFO 6179 --- [qtp722334906-130] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:52:49.441+07:00  INFO 6179 --- [qtp722334906-37] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:52:49.441+07:00  INFO 6179 --- [qtp722334906-130] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:53:06.582+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T10:53:16.316+07:00  INFO 6179 --- [qtp722334906-36] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T10:53:16.316+07:00  INFO 6179 --- [qtp722334906-130] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T10:53:16.343+07:00  INFO 6179 --- [qtp722334906-36] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T10:53:16.343+07:00  INFO 6179 --- [qtp722334906-130] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T10:53:26.424+07:00  INFO 6179 --- [qtp722334906-130] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T10:53:26.424+07:00  INFO 6179 --- [qtp722334906-36] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T10:53:26.787+07:00  INFO 6179 --- [qtp722334906-68] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T10:53:26.787+07:00  INFO 6179 --- [qtp722334906-121] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T10:53:28.611+07:00  INFO 6179 --- [qtp722334906-36] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T10:53:28.611+07:00  INFO 6179 --- [qtp722334906-130] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T10:54:03.683+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T10:54:24.811+07:00  INFO 6179 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-09-22T10:54:24.822+07:00  INFO 6179 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T10:55:06.890+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T10:55:06.894+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T10:56:02.981+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T10:56:24.054+07:00  INFO 6179 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 9, expire count 1
2025-09-22T10:56:24.075+07:00  INFO 6179 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-22T10:57:06.149+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T10:58:02.234+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T10:58:23.295+07:00  INFO 6179 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 2
2025-09-22T10:58:23.307+07:00  INFO 6179 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T10:58:38.814+07:00  INFO 6179 --- [qtp722334906-37] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:58:38.817+07:00  INFO 6179 --- [qtp722334906-121] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:58:38.949+07:00  INFO 6179 --- [qtp722334906-121] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:58:38.985+07:00  INFO 6179 --- [qtp722334906-37] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:59:05.384+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T10:59:41.772+07:00  INFO 6179 --- [qtp722334906-37] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:59:41.798+07:00  INFO 6179 --- [qtp722334906-37] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T10:59:41.852+07:00  INFO 6179 --- [qtp722334906-74] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T10:59:41.918+07:00  INFO 6179 --- [qtp722334906-74] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:00:04.783+07:00  INFO 6179 --- [qtp722334906-73] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:00:04.784+07:00  INFO 6179 --- [qtp722334906-121] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:00:04.794+07:00  INFO 6179 --- [qtp722334906-73] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:00:04.798+07:00  INFO 6179 --- [qtp722334906-121] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:00:06.485+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-09-22T11:00:06.486+07:00  INFO 6179 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 22/09/2025@11:00:06+0700
2025-09-22T11:00:06.536+07:00  INFO 6179 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 22/09/2025@11:00:00+0700 to 22/09/2025@11:15:00+0700
2025-09-22T11:00:06.536+07:00  INFO 6179 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 22/09/2025@11:00:00+0700 to 22/09/2025@11:15:00+0700
2025-09-22T11:00:06.537+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-22T11:00:06.537+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:00:06.537+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T11:00:22.585+07:00  INFO 6179 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 1
2025-09-22T11:00:22.602+07:00  INFO 6179 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T11:01:04.673+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:01:28.779+07:00  INFO 6179 --- [qtp722334906-69] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:01:28.786+07:00  INFO 6179 --- [qtp722334906-60] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:01:28.796+07:00  INFO 6179 --- [qtp722334906-69] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:01:28.797+07:00  INFO 6179 --- [qtp722334906-60] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:02:06.768+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:02:21.801+07:00  INFO 6179 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 2
2025-09-22T11:02:21.809+07:00  INFO 6179 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T11:03:03.869+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:03:43.597+07:00  INFO 6179 --- [qtp722334906-183] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T11:03:43.597+07:00  INFO 6179 --- [qtp722334906-68] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T11:03:43.743+07:00  INFO 6179 --- [qtp722334906-68] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T11:03:43.743+07:00  INFO 6179 --- [qtp722334906-183] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T11:04:06.986+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:04:21.049+07:00  INFO 6179 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 10, expire count 3
2025-09-22T11:04:21.065+07:00  INFO 6179 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-22T11:04:36.182+07:00  INFO 6179 --- [qtp722334906-60] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:04:36.183+07:00  INFO 6179 --- [qtp722334906-37] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:04:36.203+07:00  INFO 6179 --- [qtp722334906-60] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:04:36.205+07:00  INFO 6179 --- [qtp722334906-37] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:05:03.128+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:05:03.130+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T11:05:06.650+07:00  INFO 6179 --- [qtp722334906-61] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T11:05:06.650+07:00  INFO 6179 --- [qtp722334906-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T11:05:06.655+07:00  INFO 6179 --- [qtp722334906-37] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T11:05:06.655+07:00  INFO 6179 --- [qtp722334906-61] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T11:06:00.431+07:00  INFO 6179 --- [qtp722334906-60] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:06:00.449+07:00  INFO 6179 --- [qtp722334906-60] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:06:00.475+07:00  INFO 6179 --- [qtp722334906-127] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:06:00.482+07:00  INFO 6179 --- [qtp722334906-127] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:06:03.403+07:00  INFO 6179 --- [qtp722334906-127] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T11:06:03.403+07:00  INFO 6179 --- [qtp722334906-140] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T11:06:03.410+07:00  INFO 6179 --- [qtp722334906-127] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T11:06:03.415+07:00  INFO 6179 --- [qtp722334906-140] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T11:06:06.227+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:06:25.269+07:00  INFO 6179 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 10, expire count 0
2025-09-22T11:06:25.276+07:00  INFO 6179 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-22T11:07:02.342+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:07:29.638+07:00  INFO 6179 --- [qtp722334906-61] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:07:29.652+07:00  INFO 6179 --- [qtp722334906-61] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:07:29.759+07:00  INFO 6179 --- [qtp722334906-183] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:07:29.767+07:00  INFO 6179 --- [qtp722334906-183] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:07:37.025+07:00  INFO 6179 --- [qtp722334906-183] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:07:37.026+07:00  INFO 6179 --- [qtp722334906-61] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:07:37.032+07:00  INFO 6179 --- [qtp722334906-183] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:07:37.032+07:00  INFO 6179 --- [qtp722334906-61] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:07:39.680+07:00  INFO 6179 --- [qtp722334906-61] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T11:07:39.681+07:00  INFO 6179 --- [qtp722334906-183] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T11:07:39.684+07:00  INFO 6179 --- [qtp722334906-61] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T11:07:39.687+07:00  INFO 6179 --- [qtp722334906-183] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T11:08:05.453+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:08:25.523+07:00  INFO 6179 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 11, expire count 0
2025-09-22T11:08:25.590+07:00  INFO 6179 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-22T11:09:06.670+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:10:04.771+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:10:04.774+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T11:10:25.820+07:00  INFO 6179 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-22T11:10:25.830+07:00  INFO 6179 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T11:11:06.888+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:12:03.989+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:12:25.037+07:00  INFO 6179 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T11:12:25.044+07:00  INFO 6179 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T11:13:06.107+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:13:19.761+07:00  INFO 6179 --- [qtp722334906-127] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:13:19.762+07:00  INFO 6179 --- [qtp722334906-36] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:13:19.792+07:00  INFO 6179 --- [qtp722334906-36] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:13:19.792+07:00  INFO 6179 --- [qtp722334906-127] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:13:22.791+07:00  INFO 6179 --- [qtp722334906-69] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T11:13:22.794+07:00  INFO 6179 --- [qtp722334906-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T11:13:22.841+07:00  INFO 6179 --- [qtp722334906-69] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T11:13:22.841+07:00  INFO 6179 --- [qtp722334906-37] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T11:14:03.200+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:14:24.278+07:00  INFO 6179 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 10, expire count 1
2025-09-22T11:14:24.290+07:00  INFO 6179 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-22T11:14:30.447+07:00  INFO 6179 --- [qtp722334906-60] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:14:30.448+07:00  INFO 6179 --- [qtp722334906-37] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:14:30.460+07:00  INFO 6179 --- [qtp722334906-60] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:14:30.460+07:00  INFO 6179 --- [qtp722334906-37] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:14:36.260+07:00  INFO 6179 --- [qtp722334906-122] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:14:36.261+07:00  INFO 6179 --- [qtp722334906-127] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:14:36.267+07:00  INFO 6179 --- [qtp722334906-122] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:14:36.267+07:00  INFO 6179 --- [qtp722334906-127] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:15:06.375+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:15:06.378+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T11:15:06.378+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-22T11:15:06.379+07:00  INFO 6179 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 22/09/2025@11:15:06+0700
2025-09-22T11:15:06.404+07:00  INFO 6179 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 22/09/2025@11:15:00+0700 to 22/09/2025@11:30:00+0700
2025-09-22T11:15:06.404+07:00  INFO 6179 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 22/09/2025@11:15:00+0700 to 22/09/2025@11:30:00+0700
2025-09-22T11:16:02.505+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:16:23.563+07:00  INFO 6179 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T11:16:23.571+07:00  INFO 6179 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T11:17:05.643+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:18:06.752+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:18:22.817+07:00  INFO 6179 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 1
2025-09-22T11:18:22.826+07:00  INFO 6179 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T11:19:04.888+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:20:06.985+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:20:06.989+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T11:20:22.017+07:00  INFO 6179 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T11:20:22.025+07:00  INFO 6179 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T11:21:04.094+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:22:06.196+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:22:21.245+07:00  INFO 6179 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T11:22:21.254+07:00  INFO 6179 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T11:23:03.321+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:24:06.442+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:24:25.504+07:00  INFO 6179 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 6
2025-09-22T11:24:25.516+07:00  INFO 6179 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T11:25:02.583+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:25:02.586+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T11:26:05.692+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:26:25.740+07:00  INFO 6179 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 4
2025-09-22T11:26:25.751+07:00  INFO 6179 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T11:27:06.828+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:28:04.915+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:28:21.541+07:00  INFO 6179 --- [qtp722334906-130] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:28:21.542+07:00  INFO 6179 --- [qtp722334906-36] n.d.module.session.ClientSessionManager  : Add a client session id = node02lidztnetukec6rqg5hz3ipu0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:28:21.557+07:00  INFO 6179 --- [qtp722334906-36] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:28:21.557+07:00  INFO 6179 --- [qtp722334906-130] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:28:25.969+07:00  INFO 6179 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-22T11:28:25.979+07:00  INFO 6179 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T11:29:06.050+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:30:04.160+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:30:04.163+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T11:30:04.164+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-22T11:30:04.165+07:00  INFO 6179 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 22/09/2025@11:30:04+0700
2025-09-22T11:30:04.190+07:00  INFO 6179 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 22/09/2025@11:30:00+0700 to 22/09/2025@11:45:00+0700
2025-09-22T11:30:04.191+07:00  INFO 6179 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 22/09/2025@11:30:00+0700 to 22/09/2025@11:45:00+0700
2025-09-22T11:30:25.219+07:00  INFO 6179 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T11:30:25.225+07:00  INFO 6179 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T11:31:06.284+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:32:03.383+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:32:24.426+07:00  INFO 6179 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T11:32:24.434+07:00  INFO 6179 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T11:33:06.508+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:34:02.613+07:00  INFO 6179 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:34:23.662+07:00  INFO 6179 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T11:34:23.672+07:00  INFO 6179 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T11:34:43.583+07:00  INFO 6179 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@727f985f{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-22T11:34:43.585+07:00  INFO 6179 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-22T11:34:43.585+07:00  INFO 6179 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-22T11:34:43.585+07:00  INFO 6179 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-22T11:34:43.587+07:00  INFO 6179 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-22T11:34:43.587+07:00  INFO 6179 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-22T11:34:43.587+07:00  INFO 6179 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-22T11:34:43.587+07:00  INFO 6179 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-22T11:34:43.587+07:00  INFO 6179 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-09-22T11:34:43.587+07:00  INFO 6179 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-09-22T11:34:43.587+07:00  INFO 6179 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-22T11:34:43.587+07:00  INFO 6179 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-09-22T11:34:43.587+07:00  INFO 6179 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-09-22T11:34:43.587+07:00  INFO 6179 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-22T11:34:43.587+07:00  INFO 6179 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-09-22T11:34:43.587+07:00  INFO 6179 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-09-22T11:34:43.630+07:00  INFO 6179 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T11:34:43.795+07:00  INFO 6179 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-22T11:34:43.802+07:00  INFO 6179 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-22T11:34:43.839+07:00  INFO 6179 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-22T11:34:43.852+07:00  INFO 6179 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-22T11:34:43.879+07:00  INFO 6179 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-22T11:34:43.882+07:00  INFO 6179 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-22T11:34:43.883+07:00  INFO 6179 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-22T11:34:43.883+07:00  INFO 6179 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-22T11:34:43.884+07:00  INFO 6179 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-22T11:34:43.884+07:00  INFO 6179 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-22T11:34:43.884+07:00  INFO 6179 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-22T11:34:43.884+07:00  INFO 6179 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-22T11:34:43.885+07:00  INFO 6179 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-22T11:34:43.885+07:00  INFO 6179 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-22T11:34:43.886+07:00  INFO 6179 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-22T11:34:43.895+07:00  INFO 6179 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@72f86029{STOPPING}[12.0.15,sto=0]
2025-09-22T11:34:43.901+07:00  INFO 6179 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-22T11:34:43.904+07:00  INFO 6179 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@78ded8c9{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.16766584520067340361/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@30926583{STOPPED}}
