2025-09-18T10:19:37.156+07:00  INFO 89795 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 89795 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-18T10:19:37.157+07:00  INFO 89795 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-18T10:19:37.880+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T10:19:37.947+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 64 ms. Found 22 JPA repository interfaces.
2025-09-18T10:19:37.957+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T10:19:37.958+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-18T10:19:37.958+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T10:19:37.968+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 10 JPA repository interfaces.
2025-09-18T10:19:37.969+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T10:19:38.007+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 38 ms. Found 3 JPA repository interfaces.
2025-09-18T10:19:38.017+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T10:19:38.021+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-09-18T10:19:38.031+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T10:19:38.033+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-18T10:19:38.033+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T10:19:38.037+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-18T10:19:38.040+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T10:19:38.044+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-09-18T10:19:38.048+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T10:19:38.050+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-18T10:19:38.050+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T10:19:38.051+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-18T10:19:38.051+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T10:19:38.058+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 10 JPA repository interfaces.
2025-09-18T10:19:38.064+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T10:19:38.098+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 33 ms. Found 3 JPA repository interfaces.
2025-09-18T10:19:38.113+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T10:19:38.119+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 6 JPA repository interfaces.
2025-09-18T10:19:38.120+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T10:19:38.130+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 12 JPA repository interfaces.
2025-09-18T10:19:38.131+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T10:19:38.134+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-09-18T10:19:38.135+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T10:19:38.135+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-18T10:19:38.135+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T10:19:38.137+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-18T10:19:38.137+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T10:19:38.142+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-09-18T10:19:38.142+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T10:19:38.144+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-18T10:19:38.144+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T10:19:38.144+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-18T10:19:38.144+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T10:19:38.155+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 19 JPA repository interfaces.
2025-09-18T10:19:38.165+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T10:19:38.172+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 8 JPA repository interfaces.
2025-09-18T10:19:38.172+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T10:19:38.175+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-09-18T10:19:38.176+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T10:19:38.180+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-09-18T10:19:38.181+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T10:19:38.186+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-09-18T10:19:38.186+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T10:19:38.193+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 6 JPA repository interfaces.
2025-09-18T10:19:38.194+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T10:19:38.206+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 11 ms. Found 13 JPA repository interfaces.
2025-09-18T10:19:38.206+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T10:19:38.217+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 11 ms. Found 14 JPA repository interfaces.
2025-09-18T10:19:38.218+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T10:19:38.239+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 21 ms. Found 24 JPA repository interfaces.
2025-09-18T10:19:38.240+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T10:19:38.241+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-18T10:19:38.247+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T10:19:38.248+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-18T10:19:38.248+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T10:19:38.256+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 12 JPA repository interfaces.
2025-09-18T10:19:38.259+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T10:19:38.296+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 37 ms. Found 66 JPA repository interfaces.
2025-09-18T10:19:38.297+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T10:19:38.298+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-18T10:19:38.303+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T10:19:38.307+07:00  INFO 89795 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-09-18T10:19:38.492+07:00  INFO 89795 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-18T10:19:38.496+07:00  INFO 89795 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-18T10:19:38.802+07:00  WARN 89795 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-18T10:19:39.030+07:00  INFO 89795 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-18T10:19:39.032+07:00  INFO 89795 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-18T10:19:39.044+07:00  INFO 89795 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-18T10:19:39.044+07:00  INFO 89795 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1771 ms
2025-09-18T10:19:39.094+07:00  WARN 89795 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-18T10:19:39.094+07:00  INFO 89795 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-18T10:19:39.221+07:00  INFO 89795 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@6e18bc2f
2025-09-18T10:19:39.222+07:00  INFO 89795 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-18T10:19:39.227+07:00  WARN 89795 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-18T10:19:39.227+07:00  INFO 89795 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-18T10:19:39.234+07:00  INFO 89795 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@1b13e4fb
2025-09-18T10:19:39.234+07:00  INFO 89795 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-18T10:19:39.234+07:00  WARN 89795 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-18T10:19:39.234+07:00  INFO 89795 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-18T10:19:39.246+07:00  INFO 89795 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@194feb27
2025-09-18T10:19:39.246+07:00  INFO 89795 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-18T10:19:39.246+07:00  WARN 89795 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-18T10:19:39.246+07:00  INFO 89795 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-18T10:19:39.254+07:00  INFO 89795 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@7dbf92aa
2025-09-18T10:19:39.254+07:00  INFO 89795 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-18T10:19:39.254+07:00  WARN 89795 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-18T10:19:39.255+07:00  INFO 89795 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-18T10:19:39.263+07:00  INFO 89795 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@7c1a5092
2025-09-18T10:19:39.263+07:00  INFO 89795 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-18T10:19:39.263+07:00  INFO 89795 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-18T10:19:39.357+07:00  INFO 89795 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-18T10:19:39.359+07:00  INFO 89795 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@5c452eb5{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.10555924053463811125/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@1a2bcce1{STARTED}}
2025-09-18T10:19:39.359+07:00  INFO 89795 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@5c452eb5{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.10555924053463811125/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@1a2bcce1{STARTED}}
2025-09-18T10:19:39.413+07:00  INFO 89795 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@744d0cfb{STARTING}[12.0.15,sto=0] @2838ms
2025-09-18T10:19:39.467+07:00  INFO 89795 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-18T10:19:39.496+07:00  INFO 89795 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-18T10:19:39.512+07:00  INFO 89795 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-18T10:19:39.640+07:00  INFO 89795 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-18T10:19:39.670+07:00  WARN 89795 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-18T10:19:40.274+07:00  INFO 89795 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-18T10:19:40.282+07:00  INFO 89795 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@44c164e5] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-18T10:19:40.415+07:00  INFO 89795 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-18T10:19:40.608+07:00  INFO 89795 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.graphapi", "net.datatp.module.kpi", "net.datatp.module.monitor.activity", "net.datatp.module.wfms", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "cloud.datatp.module.fleet", "net.datatp.module.okr", "net.datatp.module.core.template", "cloud.datatp.gps", "cloud.datatp.module.pegasus.shipment", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "net.datatp.module.company.tmpl", "cloud.datatp.tms", "net.datatp.module.groovy", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "net.datatp.module.resource", "cloud.datatp.vendor", "net.datatp.module.hr", "net.datatp.module.company.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.chatbot", "cloud.datatp.module.odoo", "net.datatp.module.core.print", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-09-18T10:19:40.610+07:00  INFO 89795 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-18T10:19:40.617+07:00  INFO 89795 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-18T10:19:40.619+07:00  INFO 89795 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-18T10:19:40.645+07:00  INFO 89795 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-18T10:19:40.653+07:00  WARN 89795 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-18T10:19:42.783+07:00  INFO 89795 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-18T10:19:42.784+07:00  INFO 89795 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@63c9070c] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-18T10:19:42.985+07:00  WARN 89795 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-18T10:19:42.985+07:00  WARN 89795 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-18T10:19:42.995+07:00  WARN 89795 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-18T10:19:42.995+07:00  WARN 89795 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-18T10:19:43.008+07:00  WARN 89795 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-18T10:19:43.008+07:00  WARN 89795 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-18T10:19:43.464+07:00  INFO 89795 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-18T10:19:43.471+07:00  INFO 89795 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-18T10:19:43.472+07:00  INFO 89795 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-18T10:19:43.494+07:00  INFO 89795 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-18T10:19:43.498+07:00  WARN 89795 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-18T10:19:43.953+07:00  INFO 89795 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-18T10:19:43.953+07:00  INFO 89795 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@48669a8] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-18T10:19:44.021+07:00  WARN 89795 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-18T10:19:44.021+07:00  WARN 89795 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-18T10:19:44.389+07:00  INFO 89795 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-18T10:19:44.418+07:00  INFO 89795 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-18T10:19:44.422+07:00  INFO 89795 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-18T10:19:44.422+07:00  INFO 89795 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T10:19:44.428+07:00  WARN 89795 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-18T10:19:44.575+07:00  INFO 89795 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-18T10:19:45.019+07:00  INFO 89795 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-18T10:19:45.022+07:00  INFO 89795 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-18T10:19:45.055+07:00  INFO 89795 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-18T10:19:45.100+07:00  INFO 89795 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-18T10:19:45.194+07:00  INFO 89795 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-18T10:19:45.222+07:00  INFO 89795 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-18T10:19:45.241+07:00  INFO 89795 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 231792600ms : this is harmless.
2025-09-18T10:19:45.250+07:00  INFO 89795 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-18T10:19:45.253+07:00  INFO 89795 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-18T10:19:45.264+07:00  INFO 89795 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 523298554ms : this is harmless.
2025-09-18T10:19:45.266+07:00  INFO 89795 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-18T10:19:45.281+07:00  INFO 89795 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-18T10:19:45.282+07:00  INFO 89795 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-18T10:19:47.177+07:00  INFO 89795 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-18T10:19:47.177+07:00  INFO 89795 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T10:19:47.178+07:00  WARN 89795 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-18T10:19:47.456+07:00  INFO 89795 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 18/09/2025@10:15:00+0700 to 18/09/2025@10:30:00+0700
2025-09-18T10:19:47.457+07:00  INFO 89795 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 18/09/2025@10:15:00+0700 to 18/09/2025@10:30:00+0700
2025-09-18T10:19:47.956+07:00  INFO 89795 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-09-18T10:19:47.956+07:00  INFO 89795 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T10:19:47.957+07:00  WARN 89795 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-18T10:19:48.233+07:00  INFO 89795 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-09-18T10:19:48.234+07:00  INFO 89795 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-09-18T10:19:48.234+07:00  INFO 89795 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-09-18T10:19:48.234+07:00  INFO 89795 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-09-18T10:19:48.234+07:00  INFO 89795 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-09-18T10:19:50.145+07:00  WARN 89795 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 260e78d3-a81e-4712-a6fe-eb5099157d75

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-18T10:19:50.149+07:00  INFO 89795 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-09-18T10:19:50.450+07:00  INFO 89795 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-18T10:19:50.450+07:00  INFO 89795 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-18T10:19:50.451+07:00  INFO 89795 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-18T10:19:50.451+07:00  INFO 89795 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-18T10:19:50.451+07:00  INFO 89795 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-09-18T10:19:50.451+07:00  INFO 89795 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-09-18T10:19:50.451+07:00  INFO 89795 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-18T10:19:50.451+07:00  INFO 89795 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-09-18T10:19:50.451+07:00  INFO 89795 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-09-18T10:19:50.451+07:00  INFO 89795 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-18T10:19:50.451+07:00  INFO 89795 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-09-18T10:19:50.451+07:00  INFO 89795 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-09-18T10:19:50.454+07:00  INFO 89795 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-18T10:19:50.454+07:00  INFO 89795 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-18T10:19:50.454+07:00  INFO 89795 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-18T10:19:50.516+07:00  INFO 89795 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-18T10:19:50.516+07:00  INFO 89795 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-18T10:19:50.518+07:00  INFO 89795 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-09-18T10:19:50.526+07:00  INFO 89795 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@74e4af6c{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-18T10:19:50.527+07:00  INFO 89795 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-09-18T10:19:50.528+07:00  INFO 89795 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-09-18T10:19:50.553+07:00  INFO 89795 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-09-18T10:19:50.553+07:00  INFO 89795 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-09-18T10:19:50.559+07:00  INFO 89795 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 13.754 seconds (process running for 13.985)
2025-09-18T10:20:02.928+07:00  INFO 89795 --- [qtp241358679-35] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0cz2u7x0plewzdhefkzkficq91
2025-09-18T10:20:02.928+07:00  INFO 89795 --- [qtp241358679-37] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node05si5o2aovv581qa80fk3w6den0
2025-09-18T10:20:03.123+07:00  INFO 89795 --- [qtp241358679-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0cz2u7x0plewzdhefkzkficq91, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T10:20:03.123+07:00  INFO 89795 --- [qtp241358679-37] n.d.module.session.ClientSessionManager  : Add a client session id = node05si5o2aovv581qa80fk3w6den0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T10:20:03.561+07:00  INFO 89795 --- [qtp241358679-35] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T10:20:03.566+07:00  INFO 89795 --- [qtp241358679-37] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T10:20:04.495+07:00  INFO 89795 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T10:20:04.496+07:00  INFO 89795 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T10:20:51.746+07:00  INFO 89795 --- [qtp241358679-35] n.d.module.session.ClientSessionManager  : Add a client session id = node05si5o2aovv581qa80fk3w6den0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T10:20:51.748+07:00  INFO 89795 --- [qtp241358679-37] n.d.module.session.ClientSessionManager  : Add a client session id = node05si5o2aovv581qa80fk3w6den0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T10:20:51.756+07:00  INFO 89795 --- [qtp241358679-37] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T10:20:51.756+07:00  INFO 89795 --- [qtp241358679-35] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T10:20:53.623+07:00  INFO 89795 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-18T10:20:53.644+07:00  INFO 89795 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T10:21:06.669+07:00  INFO 89795 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T10:22:03.756+07:00  INFO 89795 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T10:22:52.849+07:00  INFO 89795 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T10:22:52.854+07:00  INFO 89795 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T10:23:06.881+07:00  INFO 89795 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T10:23:58.526+07:00  INFO 89795 --- [qtp241358679-39] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T10:23:58.526+07:00  INFO 89795 --- [qtp241358679-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T10:23:58.665+07:00  INFO 89795 --- [qtp241358679-39] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T10:23:58.676+07:00  INFO 89795 --- [qtp241358679-37] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T10:24:02.980+07:00  INFO 89795 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T10:24:57.103+07:00  INFO 89795 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 0
2025-09-18T10:24:57.133+07:00  INFO 89795 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-18T10:25:06.157+07:00  INFO 89795 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T10:25:06.160+07:00  INFO 89795 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T10:25:42.668+07:00  INFO 89795 --- [qtp241358679-37] n.d.module.session.ClientSessionManager  : Add a client session id = node05si5o2aovv581qa80fk3w6den0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T10:25:42.678+07:00  INFO 89795 --- [qtp241358679-37] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T10:25:42.695+07:00  INFO 89795 --- [qtp241358679-39] n.d.module.session.ClientSessionManager  : Add a client session id = node05si5o2aovv581qa80fk3w6den0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T10:25:42.701+07:00  INFO 89795 --- [qtp241358679-39] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T10:25:42.879+07:00  INFO 89795 --- [qtp241358679-41] n.d.module.session.ClientSessionManager  : Add a client session id = node05si5o2aovv581qa80fk3w6den0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T10:25:42.883+07:00  INFO 89795 --- [qtp241358679-41] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T10:25:42.884+07:00  INFO 89795 --- [qtp241358679-34] n.d.module.session.ClientSessionManager  : Add a client session id = node05si5o2aovv581qa80fk3w6den0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T10:25:42.901+07:00  INFO 89795 --- [qtp241358679-34] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T10:26:02.263+07:00  INFO 89795 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T10:26:57.388+07:00  INFO 89795 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-18T10:26:57.403+07:00  INFO 89795 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T10:27:05.418+07:00  INFO 89795 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T10:27:20.294+07:00  INFO 89795 --- [qtp241358679-34] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T10:27:20.294+07:00  INFO 89795 --- [qtp241358679-35] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T10:27:20.319+07:00  INFO 89795 --- [qtp241358679-34] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T10:27:20.320+07:00  INFO 89795 --- [qtp241358679-35] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T10:27:36.566+07:00  INFO 89795 --- [qtp241358679-35] n.d.module.session.ClientSessionManager  : Add a client session id = node05si5o2aovv581qa80fk3w6den0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T10:27:36.599+07:00  INFO 89795 --- [qtp241358679-35] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T10:27:36.608+07:00  INFO 89795 --- [qtp241358679-64] n.d.module.session.ClientSessionManager  : Add a client session id = node05si5o2aovv581qa80fk3w6den0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T10:27:36.636+07:00  INFO 89795 --- [qtp241358679-64] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T10:27:36.764+07:00  INFO 89795 --- [qtp241358679-69] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T10:27:36.785+07:00  INFO 89795 --- [qtp241358679-69] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T10:27:36.787+07:00  INFO 89795 --- [qtp241358679-70] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T10:27:36.799+07:00  INFO 89795 --- [qtp241358679-70] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T10:27:36.895+07:00  INFO 89795 --- [qtp241358679-69] n.d.module.session.ClientSessionManager  : Add a client session id = node05si5o2aovv581qa80fk3w6den0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T10:27:36.896+07:00  INFO 89795 --- [qtp241358679-70] n.d.module.session.ClientSessionManager  : Add a client session id = node05si5o2aovv581qa80fk3w6den0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T10:27:36.902+07:00  INFO 89795 --- [qtp241358679-69] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T10:27:36.903+07:00  INFO 89795 --- [qtp241358679-70] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T10:27:37.907+07:00  INFO 89795 --- [qtp241358679-70] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T10:27:37.907+07:00  INFO 89795 --- [qtp241358679-69] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T10:27:37.919+07:00  INFO 89795 --- [qtp241358679-69] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T10:27:37.931+07:00  INFO 89795 --- [qtp241358679-70] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T10:27:56.792+07:00  INFO 89795 --- [qtp241358679-37] n.d.module.session.ClientSessionManager  : Add a client session id = node05si5o2aovv581qa80fk3w6den0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T10:27:56.800+07:00  INFO 89795 --- [qtp241358679-37] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T10:27:56.812+07:00  INFO 89795 --- [qtp241358679-62] n.d.module.session.ClientSessionManager  : Add a client session id = node05si5o2aovv581qa80fk3w6den0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T10:27:56.816+07:00  INFO 89795 --- [qtp241358679-62] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T10:27:56.862+07:00  INFO 89795 --- [qtp241358679-64] n.d.module.session.ClientSessionManager  : Add a client session id = node05si5o2aovv581qa80fk3w6den0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T10:27:56.862+07:00  INFO 89795 --- [qtp241358679-34] n.d.module.session.ClientSessionManager  : Add a client session id = node05si5o2aovv581qa80fk3w6den0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T10:27:56.867+07:00  INFO 89795 --- [qtp241358679-64] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T10:27:56.869+07:00  INFO 89795 --- [qtp241358679-34] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T10:27:56.936+07:00  INFO 89795 --- [qtp241358679-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T10:27:56.936+07:00  INFO 89795 --- [qtp241358679-62] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T10:27:56.969+07:00  INFO 89795 --- [qtp241358679-62] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T10:27:56.971+07:00  INFO 89795 --- [qtp241358679-37] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T10:27:57.889+07:00  INFO 89795 --- [qtp241358679-34] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T10:27:57.889+07:00  INFO 89795 --- [qtp241358679-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T10:27:57.896+07:00  INFO 89795 --- [qtp241358679-37] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T10:27:57.897+07:00  INFO 89795 --- [qtp241358679-34] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T10:28:04.903+07:00  INFO 89795 --- [qtp241358679-34] n.d.module.session.ClientSessionManager  : Add a client session id = node05si5o2aovv581qa80fk3w6den0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T10:28:04.912+07:00  INFO 89795 --- [qtp241358679-62] n.d.module.session.ClientSessionManager  : Add a client session id = node05si5o2aovv581qa80fk3w6den0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T10:28:04.915+07:00  INFO 89795 --- [qtp241358679-70] n.d.module.session.ClientSessionManager  : Add a client session id = node05si5o2aovv581qa80fk3w6den0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T10:28:04.953+07:00  INFO 89795 --- [qtp241358679-70] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T10:28:04.953+07:00  INFO 89795 --- [qtp241358679-62] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T10:28:04.954+07:00  INFO 89795 --- [qtp241358679-34] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T10:28:05.084+07:00  INFO 89795 --- [qtp241358679-61] n.d.module.session.ClientSessionManager  : Add a client session id = node05si5o2aovv581qa80fk3w6den0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T10:28:05.129+07:00  INFO 89795 --- [qtp241358679-61] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T10:28:05.289+07:00  INFO 89795 --- [qtp241358679-61] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T10:28:05.396+07:00  INFO 89795 --- [qtp241358679-64] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T10:28:05.530+07:00  INFO 89795 --- [qtp241358679-64] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T10:28:05.531+07:00  INFO 89795 --- [qtp241358679-61] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T10:28:05.897+07:00  INFO 89795 --- [qtp241358679-35] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T10:28:05.897+07:00  INFO 89795 --- [qtp241358679-70] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T10:28:05.903+07:00  INFO 89795 --- [qtp241358679-35] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T10:28:05.907+07:00  INFO 89795 --- [qtp241358679-70] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T10:28:06.525+07:00  INFO 89795 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T10:28:08.183+07:00  INFO 89795 --- [qtp241358679-70] n.d.module.session.ClientSessionManager  : Add a client session id = node05si5o2aovv581qa80fk3w6den0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T10:28:08.195+07:00  INFO 89795 --- [qtp241358679-70] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T10:28:08.214+07:00  INFO 89795 --- [qtp241358679-66] n.d.module.session.ClientSessionManager  : Add a client session id = node05si5o2aovv581qa80fk3w6den0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T10:28:08.225+07:00  INFO 89795 --- [qtp241358679-66] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T10:28:08.332+07:00  INFO 89795 --- [qtp241358679-62] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T10:28:08.332+07:00  INFO 89795 --- [qtp241358679-66] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T10:28:08.348+07:00  INFO 89795 --- [qtp241358679-62] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T10:28:08.348+07:00  INFO 89795 --- [qtp241358679-66] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T10:28:08.933+07:00  INFO 89795 --- [qtp241358679-34] n.d.module.session.ClientSessionManager  : Add a client session id = node05si5o2aovv581qa80fk3w6den0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T10:28:08.936+07:00  INFO 89795 --- [qtp241358679-62] n.d.module.session.ClientSessionManager  : Add a client session id = node05si5o2aovv581qa80fk3w6den0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T10:28:08.949+07:00  INFO 89795 --- [qtp241358679-34] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T10:28:08.976+07:00  INFO 89795 --- [qtp241358679-62] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T10:28:09.902+07:00  INFO 89795 --- [qtp241358679-70] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T10:28:09.902+07:00  INFO 89795 --- [qtp241358679-66] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T10:28:09.909+07:00  INFO 89795 --- [qtp241358679-70] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T10:28:09.909+07:00  INFO 89795 --- [qtp241358679-66] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T10:28:14.950+07:00  INFO 89795 --- [qtp241358679-70] n.d.module.session.ClientSessionManager  : Add a client session id = node05si5o2aovv581qa80fk3w6den0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T10:28:14.958+07:00  INFO 89795 --- [qtp241358679-34] n.d.module.session.ClientSessionManager  : Add a client session id = node05si5o2aovv581qa80fk3w6den0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T10:28:14.991+07:00  INFO 89795 --- [qtp241358679-70] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T10:28:15.006+07:00  INFO 89795 --- [qtp241358679-34] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T10:28:15.220+07:00  INFO 89795 --- [qtp241358679-70] n.d.module.session.ClientSessionManager  : Add a client session id = node05si5o2aovv581qa80fk3w6den0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T10:28:15.220+07:00  INFO 89795 --- [qtp241358679-64] n.d.module.session.ClientSessionManager  : Add a client session id = node05si5o2aovv581qa80fk3w6den0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T10:28:15.223+07:00  INFO 89795 --- [qtp241358679-70] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T10:28:15.225+07:00  INFO 89795 --- [qtp241358679-64] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T10:28:15.355+07:00  INFO 89795 --- [qtp241358679-61] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T10:28:15.355+07:00  INFO 89795 --- [qtp241358679-66] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T10:28:15.364+07:00  INFO 89795 --- [qtp241358679-61] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T10:28:15.364+07:00  INFO 89795 --- [qtp241358679-66] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T10:28:15.944+07:00  INFO 89795 --- [qtp241358679-69] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T10:28:15.949+07:00  INFO 89795 --- [qtp241358679-34] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T10:28:15.964+07:00  INFO 89795 --- [qtp241358679-69] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T10:28:15.974+07:00  INFO 89795 --- [qtp241358679-34] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T10:28:21.868+07:00  INFO 89795 --- [qtp241358679-69] n.d.module.session.ClientSessionManager  : Add a client session id = node05si5o2aovv581qa80fk3w6den0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T10:28:21.872+07:00  INFO 89795 --- [qtp241358679-62] n.d.module.session.ClientSessionManager  : Add a client session id = node05si5o2aovv581qa80fk3w6den0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T10:28:21.872+07:00  INFO 89795 --- [qtp241358679-39] n.d.module.session.ClientSessionManager  : Add a client session id = node05si5o2aovv581qa80fk3w6den0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T10:28:21.873+07:00  INFO 89795 --- [qtp241358679-34] n.d.module.session.ClientSessionManager  : Add a client session id = node05si5o2aovv581qa80fk3w6den0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T10:28:21.887+07:00  INFO 89795 --- [qtp241358679-62] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T10:28:21.887+07:00  INFO 89795 --- [qtp241358679-39] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T10:28:21.888+07:00  INFO 89795 --- [qtp241358679-34] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T10:28:21.888+07:00  INFO 89795 --- [qtp241358679-69] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T10:28:22.035+07:00  INFO 89795 --- [qtp241358679-61] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T10:28:22.035+07:00  INFO 89795 --- [qtp241358679-66] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T10:28:22.061+07:00  INFO 89795 --- [qtp241358679-66] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T10:28:22.061+07:00  INFO 89795 --- [qtp241358679-61] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T10:28:22.903+07:00  INFO 89795 --- [qtp241358679-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T10:28:22.903+07:00  INFO 89795 --- [qtp241358679-70] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T10:28:22.914+07:00  INFO 89795 --- [qtp241358679-70] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T10:28:22.914+07:00  INFO 89795 --- [qtp241358679-37] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T10:28:56.679+07:00  INFO 89795 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-18T10:28:56.694+07:00  INFO 89795 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-18T10:29:04.716+07:00  INFO 89795 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T10:29:39.378+07:00  INFO 89795 --- [Scheduler-769336846-1] n.d.m.session.AppHttpSessionListener     : The session node0cz2u7x0plewzdhefkzkficq91 is destroyed.
2025-09-18T10:30:06.817+07:00  INFO 89795 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T10:30:06.820+07:00  INFO 89795 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-18T10:30:06.823+07:00  INFO 89795 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 18/09/2025@10:30:06+0700
2025-09-18T10:30:06.841+07:00  INFO 89795 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 18/09/2025@10:30:00+0700 to 18/09/2025@10:45:00+0700
2025-09-18T10:30:06.842+07:00  INFO 89795 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 18/09/2025@10:30:00+0700 to 18/09/2025@10:45:00+0700
2025-09-18T10:30:06.842+07:00  INFO 89795 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T10:30:23.906+07:00  INFO 89795 --- [qtp241358679-69] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-09-18T10:30:24.865+07:00  INFO 89795 --- [qtp241358679-70] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-09-18T10:30:56.979+07:00  INFO 89795 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 0
2025-09-18T10:30:56.987+07:00  INFO 89795 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T10:31:03.999+07:00  INFO 89795 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T10:31:26.899+07:00  INFO 89795 --- [qtp241358679-37] n.d.module.session.ClientSessionManager  : Add a client session id = node05si5o2aovv581qa80fk3w6den0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T10:31:26.900+07:00  INFO 89795 --- [qtp241358679-61] n.d.module.session.ClientSessionManager  : Add a client session id = node05si5o2aovv581qa80fk3w6den0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T10:31:26.901+07:00  INFO 89795 --- [qtp241358679-66] n.d.module.session.ClientSessionManager  : Add a client session id = node05si5o2aovv581qa80fk3w6den0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T10:31:26.918+07:00  INFO 89795 --- [qtp241358679-61] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T10:31:26.918+07:00  INFO 89795 --- [qtp241358679-66] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T10:31:26.918+07:00  INFO 89795 --- [qtp241358679-37] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T10:31:26.946+07:00  INFO 89795 --- [qtp241358679-74] n.d.module.session.ClientSessionManager  : Add a client session id = node05si5o2aovv581qa80fk3w6den0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T10:31:26.970+07:00  INFO 89795 --- [qtp241358679-74] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T10:31:27.912+07:00  INFO 89795 --- [qtp241358679-74] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T10:31:27.912+07:00  INFO 89795 --- [qtp241358679-69] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T10:31:27.912+07:00  INFO 89795 --- [qtp241358679-61] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T10:31:27.938+07:00  INFO 89795 --- [qtp241358679-68] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T10:31:27.944+07:00  INFO 89795 --- [qtp241358679-61] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T10:31:27.944+07:00  INFO 89795 --- [qtp241358679-74] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T10:31:27.944+07:00  INFO 89795 --- [qtp241358679-69] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T10:31:27.952+07:00  INFO 89795 --- [qtp241358679-68] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T10:32:06.084+07:00  INFO 89795 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T10:32:56.184+07:00  INFO 89795 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T10:32:56.192+07:00  INFO 89795 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T10:33:03.200+07:00  INFO 89795 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T10:34:06.316+07:00  INFO 89795 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T10:34:55.436+07:00  INFO 89795 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 3
2025-09-18T10:34:55.454+07:00  INFO 89795 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T10:35:02.468+07:00  INFO 89795 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T10:35:02.470+07:00  INFO 89795 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T10:36:05.550+07:00  INFO 89795 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T10:36:54.628+07:00  INFO 89795 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-18T10:36:54.631+07:00  INFO 89795 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T10:37:06.644+07:00  INFO 89795 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T10:38:04.740+07:00  INFO 89795 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T10:38:53.834+07:00  INFO 89795 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 4
2025-09-18T10:38:53.842+07:00  INFO 89795 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T10:39:06.866+07:00  INFO 89795 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T10:40:03.964+07:00  INFO 89795 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T10:40:03.966+07:00  INFO 89795 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T10:40:53.113+07:00  INFO 89795 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 2
2025-09-18T10:40:53.117+07:00  INFO 89795 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T10:41:06.151+07:00  INFO 89795 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T10:42:03.311+07:00  INFO 89795 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T10:42:57.466+07:00  INFO 89795 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 3
2025-09-18T10:42:57.471+07:00  INFO 89795 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T10:43:06.496+07:00  INFO 89795 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T10:44:02.669+07:00  INFO 89795 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T10:44:56.834+07:00  INFO 89795 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T10:44:56.838+07:00  INFO 89795 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T10:45:05.863+07:00  INFO 89795 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-18T10:45:05.866+07:00  INFO 89795 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 18/09/2025@10:45:05+0700
2025-09-18T10:45:05.877+07:00  INFO 89795 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 18/09/2025@10:45:00+0700 to 18/09/2025@11:00:00+0700
2025-09-18T10:45:05.877+07:00  INFO 89795 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 18/09/2025@10:45:00+0700 to 18/09/2025@11:00:00+0700
2025-09-18T10:45:05.877+07:00  INFO 89795 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T10:45:05.877+07:00  INFO 89795 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T10:46:07.012+07:00  INFO 89795 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T10:46:57.159+07:00  INFO 89795 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-18T10:46:57.167+07:00  INFO 89795 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T10:47:05.192+07:00  INFO 89795 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T10:48:06.370+07:00  INFO 89795 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T10:48:57.528+07:00  INFO 89795 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T10:48:57.537+07:00  INFO 89795 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T10:49:04.554+07:00  INFO 89795 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T10:50:06.696+07:00  INFO 89795 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T10:50:06.700+07:00  INFO 89795 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T10:50:56.792+07:00  INFO 89795 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T10:50:56.805+07:00  INFO 89795 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T10:51:03.811+07:00  INFO 89795 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T10:51:46.579+07:00  INFO 89795 --- [qtp241358679-77] c.d.f.core.partner.CRMPartnerLogic       : --- Create account: bfsone code = CS000049
2025-09-18T10:51:47.745+07:00  WARN 89795 --- [qtp241358679-77] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-18T10:51:47.745+07:00 ERROR 89795 --- [qtp241358679-77] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "crm_partner_partner_code"
  Detail: Key (partner_code)=(CS000049) already exists.
2025-09-18T10:51:47.845+07:00 ERROR 89795 --- [qtp241358679-77] c.d.f.core.partner.CRMPartnerLogic       : Error while creating BFSOnePartner: CS000049

org.springframework.dao.DataIntegrityViolationException: could not execute statement [ERROR: duplicate key value violates unique constraint "crm_partner_partner_code"
  Detail: Key (partner_code)=(CS000049) already exists.] [update lgc_forwarder_crm_partner set account_id=?,address=?,bank_account=?,bank_accs_no=?,bank_address=?,bank_currency=?,bank_name=?,bfsone_group_id=?,category=?,cell=?,country_id=?,country_label=?,created_by=?,created_time=?,date_created=?,date_modified=?,email=?,fax=?,group_name=?,home_phone=?,industry_code=?,industry_label=?,input_username=?,investment_origin=?,kcn_code=?,kcn_label=?,label=?,lead_code=?,localized_address=?,localized_label=?,modified_by=?,modified_time=?,name=?,note=?,partner_code=?,partner_code_temp=?,partner_group=?,personal_contact=?,print_custom_confirm_bill_info=?,province_id=?,province_label=?,is_refund=?,routing=?,sale_owner_contact_code=?,sale_owner_username=?,scope=?,shareable=?,source=?,status=?,storage_state=?,swift_code=?,tax_code=?,version=?,work_phone=? where id=? and version=?]; SQL [update lgc_forwarder_crm_partner set account_id=?,address=?,bank_account=?,bank_accs_no=?,bank_address=?,bank_currency=?,bank_name=?,bfsone_group_id=?,category=?,cell=?,country_id=?,country_label=?,created_by=?,created_time=?,date_created=?,date_modified=?,email=?,fax=?,group_name=?,home_phone=?,industry_code=?,industry_label=?,input_username=?,investment_origin=?,kcn_code=?,kcn_label=?,label=?,lead_code=?,localized_address=?,localized_label=?,modified_by=?,modified_time=?,name=?,note=?,partner_code=?,partner_code_temp=?,partner_group=?,personal_contact=?,print_custom_confirm_bill_info=?,province_id=?,province_label=?,is_refund=?,routing=?,sale_owner_contact_code=?,sale_owner_username=?,scope=?,shareable=?,source=?,status=?,storage_state=?,swift_code=?,tax_code=?,version=?,work_phone=? where id=? and version=?]; constraint [crm_partner_partner_code]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:290)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.JpaTransactionManager.doCommit(JpaTransactionManager.java:566)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.processCommit(AbstractPlatformTransactionManager.java:795)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.commit(AbstractPlatformTransactionManager.java:758)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.commitTransactionAfterReturning(TransactionAspectSupport.java:698)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:416)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:164)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy302.save(Unknown Source)
	at cloud.datatp.fforwarder.core.partner.CRMPartnerLogic.save(CRMPartnerLogic.java:111)
	at cloud.datatp.fforwarder.sales.integration.BFSOneCRMLogic.createBFSPartner(BFSOneCRMLogic.java:174)
	at cloud.datatp.fforwarder.sales.integration.BFSOneCRMService.createBFSOnePartner(BFSOneCRMService.java:29)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at cloud.datatp.fforwarder.sales.integration.BFSOneCRMService$$SpringCGLIB$$0.createBFSOnePartner(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.hibernate.exception.ConstraintViolationException: could not execute statement [ERROR: duplicate key value violates unique constraint "crm_partner_partner_code"
  Detail: Key (partner_code)=(CS000049) already exists.] [update lgc_forwarder_crm_partner set account_id=?,address=?,bank_account=?,bank_accs_no=?,bank_address=?,bank_currency=?,bank_name=?,bfsone_group_id=?,category=?,cell=?,country_id=?,country_label=?,created_by=?,created_time=?,date_created=?,date_modified=?,email=?,fax=?,group_name=?,home_phone=?,industry_code=?,industry_label=?,input_username=?,investment_origin=?,kcn_code=?,kcn_label=?,label=?,lead_code=?,localized_address=?,localized_label=?,modified_by=?,modified_time=?,name=?,note=?,partner_code=?,partner_code_temp=?,partner_group=?,personal_contact=?,print_custom_confirm_bill_info=?,province_id=?,province_label=?,is_refund=?,routing=?,sale_owner_contact_code=?,sale_owner_username=?,scope=?,shareable=?,source=?,status=?,storage_state=?,swift_code=?,tax_code=?,version=?,work_phone=? where id=? and version=?]
	at org.hibernate.exception.internal.SQLStateConversionDelegate.convert(SQLStateConversionDelegate.java:97)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:197)
	at org.hibernate.engine.jdbc.mutation.internal.AbstractMutationExecutor.performNonBatchedMutation(AbstractMutationExecutor.java:108)
	at org.hibernate.engine.jdbc.mutation.internal.MutationExecutorSingleNonBatched.performNonBatchedOperations(MutationExecutorSingleNonBatched.java:40)
	at org.hibernate.engine.jdbc.mutation.internal.AbstractMutationExecutor.execute(AbstractMutationExecutor.java:52)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.doStaticUpdate(UpdateCoordinatorStandard.java:778)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.performUpdate(UpdateCoordinatorStandard.java:324)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.coordinateUpdate(UpdateCoordinatorStandard.java:242)
	at org.hibernate.persister.entity.AbstractEntityPersister.update(AbstractEntityPersister.java:2810)
	at org.hibernate.action.internal.EntityUpdateAction.execute(EntityUpdateAction.java:168)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:632)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:499)
	at org.hibernate.event.internal.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:363)
	at org.hibernate.event.internal.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:41)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.doFlush(SessionImpl.java:1403)
	at org.hibernate.internal.SessionImpl.managedFlush(SessionImpl.java:484)
	at org.hibernate.internal.SessionImpl.flushBeforeTransactionCompletion(SessionImpl.java:2319)
	at org.hibernate.internal.SessionImpl.beforeTransactionCompletion(SessionImpl.java:1976)
	at org.hibernate.engine.jdbc.internal.JdbcCoordinatorImpl.beforeTransactionCompletion(JdbcCoordinatorImpl.java:439)
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcResourceLocalTransactionCoordinatorImpl.beforeCompletionCallback(JdbcResourceLocalTransactionCoordinatorImpl.java:169)
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcResourceLocalTransactionCoordinatorImpl$TransactionDriverControlImpl.commit(JdbcResourceLocalTransactionCoordinatorImpl.java:267)
	at org.hibernate.engine.transaction.internal.TransactionImpl.commit(TransactionImpl.java:101)
	at org.springframework.orm.jpa.JpaTransactionManager.doCommit(JpaTransactionManager.java:562)
	... 126 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: duplicate key value violates unique constraint "crm_partner_partner_code"
  Detail: Key (partner_code)=(CS000049) already exists.
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2725)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2412)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:371)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:502)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:419)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:194)
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:155)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:194)
	... 148 common frames omitted

2025-09-18T10:51:54.363+07:00 ERROR 89795 --- [qtp241358679-77] n.d.m.monitor.call.EndpointCallContext   : Start call with component BFSOneCRMService, method createBFSOnePartner, arguments
[ {
  "tenantId" : "default",
  "companyId" : 8,
  "companyParentId" : 4,
  "companyCode" : "beehph",
  "companyLabel" : "Bee HPH",
  "companyFullName" : "BEE LOGISTICS CORPORATION - HAI PHONG BRANCH OFFICE",
  "loginId" : "kai.vnhph",
  "accountId" : 1994,
  "token" : "4a758e39123fe8b5a2364bcefc38f355",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node05si5o2aovv581qa80fk3w6den0",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : {
    "beehph" : {
      "companyId" : 8,
      "companyParentId" : 4,
      "companyCode" : "beehph",
      "companyLabel" : "Bee HPH",
      "companyFullName" : null
    }
  },
  "featurePermissions" : [ {
    "id" : 14630,
    "appId" : 3,
    "appModule" : "project",
    "appName" : "project",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 2116,
    "appId" : 26,
    "appModule" : "okr",
    "appName" : "okr",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 12452,
    "appId" : 66,
    "appModule" : "tms",
    "appName" : "tms-bill-company",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Read"
  }, {
    "id" : 11672,
    "appId" : 69,
    "appModule" : "hr",
    "appName" : "user-kpi",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 13280,
    "appId" : 79,
    "appModule" : "company",
    "appName" : "user-asset",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 8564,
    "appId" : 16,
    "appModule" : "logistics",
    "appName" : "user-logistics-sales",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 8562,
    "appId" : 2,
    "appModule" : "user",
    "appName" : "my-employee-space",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 18464,
    "appId" : 88,
    "appModule" : "user",
    "appName" : "my-space",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  } ],
  "attributes" : { },
  "clientId" : "default:kai.vnhph"
}, {
  "editState" : "ORIGIN",
  "loadState" : "FromDB",
  "version" : 0,
  "storageState" : "ACTIVE",
  "partnerCode" : "CS000049",
  "partnerCodeTemp" : "CS000049",
  "status" : "NEW",
  "partnerGroup" : "CUSTOMERS",
  "category" : "CUSTOMER",
  "name" : "TEST34342432",
  "label" : "TEST34342432",
  "localizedLabel" : "TEST34342432",
  "personalContact" : "cvzxcv",
  "email" : "<EMAIL>",
  "cell" : "xzcvzxcvzxc",
  "source" : "WCA",
  "industryCode" : "I001",
  "industryLabel" : "Agriculture; plantations;other rural sectors ",
  "countryId" : 244,
  "countryLabel" : "VIETNAM",
  "provinceId" : 103,
  "provinceLabel" : "Thành phố Hà Nội",
  "kcnCode" : "41161",
  "kcnLabel" : "Cụm công nghiệp An Khánh",
  "address" : "Xã An Khánh, Huyện Hoài Đức, Thành phố Hà Nội",
  "localizedAddress" : "Xã An Khánh, Huyện Hoài Đức, Thành phố Hà Nội",
  "printCustomConfirmBillInfo" : "TEST34342432\nXã An Khánh, Huyện Hoài Đức, Thành phố Hà Nội\ncvzxcv\nTEL :xzcvzxcvzxc",
  "note" : "zxcvzxcv",
  "inputUsername" : "KAI.VNHPH",
  "scope" : "Domestic",
  "groupName" : "NORMAL",
  "refund" : false,
  "requestSalemanAccountId" : 1994,
  "requestSalemanLabel" : "NGUYỄN VĂN HƯNG"
} ]
2025-09-18T10:51:54.364+07:00 ERROR 89795 --- [qtp241358679-77] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:118)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.RuntimeException: Failed to create BFSOnePartner: CS000049
	at cloud.datatp.fforwarder.core.partner.CRMPartnerLogic.save(CRMPartnerLogic.java:117)
	at cloud.datatp.fforwarder.sales.integration.BFSOneCRMLogic.createBFSPartner(BFSOneCRMLogic.java:174)
	at cloud.datatp.fforwarder.sales.integration.BFSOneCRMService.createBFSOnePartner(BFSOneCRMService.java:29)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at cloud.datatp.fforwarder.sales.integration.BFSOneCRMService$$SpringCGLIB$$0.createBFSOnePartner(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted
Caused by: org.springframework.dao.DataIntegrityViolationException: could not execute statement [ERROR: duplicate key value violates unique constraint "crm_partner_partner_code"
  Detail: Key (partner_code)=(CS000049) already exists.] [update lgc_forwarder_crm_partner set account_id=?,address=?,bank_account=?,bank_accs_no=?,bank_address=?,bank_currency=?,bank_name=?,bfsone_group_id=?,category=?,cell=?,country_id=?,country_label=?,created_by=?,created_time=?,date_created=?,date_modified=?,email=?,fax=?,group_name=?,home_phone=?,industry_code=?,industry_label=?,input_username=?,investment_origin=?,kcn_code=?,kcn_label=?,label=?,lead_code=?,localized_address=?,localized_label=?,modified_by=?,modified_time=?,name=?,note=?,partner_code=?,partner_code_temp=?,partner_group=?,personal_contact=?,print_custom_confirm_bill_info=?,province_id=?,province_label=?,is_refund=?,routing=?,sale_owner_contact_code=?,sale_owner_username=?,scope=?,shareable=?,source=?,status=?,storage_state=?,swift_code=?,tax_code=?,version=?,work_phone=? where id=? and version=?]; SQL [update lgc_forwarder_crm_partner set account_id=?,address=?,bank_account=?,bank_accs_no=?,bank_address=?,bank_currency=?,bank_name=?,bfsone_group_id=?,category=?,cell=?,country_id=?,country_label=?,created_by=?,created_time=?,date_created=?,date_modified=?,email=?,fax=?,group_name=?,home_phone=?,industry_code=?,industry_label=?,input_username=?,investment_origin=?,kcn_code=?,kcn_label=?,label=?,lead_code=?,localized_address=?,localized_label=?,modified_by=?,modified_time=?,name=?,note=?,partner_code=?,partner_code_temp=?,partner_group=?,personal_contact=?,print_custom_confirm_bill_info=?,province_id=?,province_label=?,is_refund=?,routing=?,sale_owner_contact_code=?,sale_owner_username=?,scope=?,shareable=?,source=?,status=?,storage_state=?,swift_code=?,tax_code=?,version=?,work_phone=? where id=? and version=?]; constraint [crm_partner_partner_code]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:290)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.JpaTransactionManager.doCommit(JpaTransactionManager.java:566)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.processCommit(AbstractPlatformTransactionManager.java:795)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.commit(AbstractPlatformTransactionManager.java:758)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.commitTransactionAfterReturning(TransactionAspectSupport.java:698)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:416)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:164)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy302.save(Unknown Source)
	at cloud.datatp.fforwarder.core.partner.CRMPartnerLogic.save(CRMPartnerLogic.java:111)
	... 111 common frames omitted
Caused by: org.hibernate.exception.ConstraintViolationException: could not execute statement [ERROR: duplicate key value violates unique constraint "crm_partner_partner_code"
  Detail: Key (partner_code)=(CS000049) already exists.] [update lgc_forwarder_crm_partner set account_id=?,address=?,bank_account=?,bank_accs_no=?,bank_address=?,bank_currency=?,bank_name=?,bfsone_group_id=?,category=?,cell=?,country_id=?,country_label=?,created_by=?,created_time=?,date_created=?,date_modified=?,email=?,fax=?,group_name=?,home_phone=?,industry_code=?,industry_label=?,input_username=?,investment_origin=?,kcn_code=?,kcn_label=?,label=?,lead_code=?,localized_address=?,localized_label=?,modified_by=?,modified_time=?,name=?,note=?,partner_code=?,partner_code_temp=?,partner_group=?,personal_contact=?,print_custom_confirm_bill_info=?,province_id=?,province_label=?,is_refund=?,routing=?,sale_owner_contact_code=?,sale_owner_username=?,scope=?,shareable=?,source=?,status=?,storage_state=?,swift_code=?,tax_code=?,version=?,work_phone=? where id=? and version=?]
	at org.hibernate.exception.internal.SQLStateConversionDelegate.convert(SQLStateConversionDelegate.java:97)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:197)
	at org.hibernate.engine.jdbc.mutation.internal.AbstractMutationExecutor.performNonBatchedMutation(AbstractMutationExecutor.java:108)
	at org.hibernate.engine.jdbc.mutation.internal.MutationExecutorSingleNonBatched.performNonBatchedOperations(MutationExecutorSingleNonBatched.java:40)
	at org.hibernate.engine.jdbc.mutation.internal.AbstractMutationExecutor.execute(AbstractMutationExecutor.java:52)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.doStaticUpdate(UpdateCoordinatorStandard.java:778)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.performUpdate(UpdateCoordinatorStandard.java:324)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.coordinateUpdate(UpdateCoordinatorStandard.java:242)
	at org.hibernate.persister.entity.AbstractEntityPersister.update(AbstractEntityPersister.java:2810)
	at org.hibernate.action.internal.EntityUpdateAction.execute(EntityUpdateAction.java:168)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:632)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:499)
	at org.hibernate.event.internal.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:363)
	at org.hibernate.event.internal.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:41)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.doFlush(SessionImpl.java:1403)
	at org.hibernate.internal.SessionImpl.managedFlush(SessionImpl.java:484)
	at org.hibernate.internal.SessionImpl.flushBeforeTransactionCompletion(SessionImpl.java:2319)
	at org.hibernate.internal.SessionImpl.beforeTransactionCompletion(SessionImpl.java:1976)
	at org.hibernate.engine.jdbc.internal.JdbcCoordinatorImpl.beforeTransactionCompletion(JdbcCoordinatorImpl.java:439)
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcResourceLocalTransactionCoordinatorImpl.beforeCompletionCallback(JdbcResourceLocalTransactionCoordinatorImpl.java:169)
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcResourceLocalTransactionCoordinatorImpl$TransactionDriverControlImpl.commit(JdbcResourceLocalTransactionCoordinatorImpl.java:267)
	at org.hibernate.engine.transaction.internal.TransactionImpl.commit(TransactionImpl.java:101)
	at org.springframework.orm.jpa.JpaTransactionManager.doCommit(JpaTransactionManager.java:562)
	... 126 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: duplicate key value violates unique constraint "crm_partner_partner_code"
  Detail: Key (partner_code)=(CS000049) already exists.
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2725)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2412)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:371)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:502)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:419)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:194)
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:155)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:194)
	... 148 common frames omitted

2025-09-18T10:51:54.368+07:00  INFO 89795 --- [qtp241358679-77] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint BFSOneCRMService/createBFSOnePartner
2025-09-18T10:52:06.913+07:00  INFO 89795 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T10:52:51.553+07:00  INFO 89795 --- [qtp241358679-77] c.d.f.core.partner.CRMPartnerLogic       : --- Create account: bfsone code = CS000050
2025-09-18T10:52:52.483+07:00  WARN 89795 --- [qtp241358679-77] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-18T10:52:52.484+07:00 ERROR 89795 --- [qtp241358679-77] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "crm_partner_partner_code"
  Detail: Key (partner_code)=(CS000050) already exists.
2025-09-18T10:52:52.486+07:00 ERROR 89795 --- [qtp241358679-77] c.d.f.core.partner.CRMPartnerLogic       : Error while creating BFSOnePartner: CS000050

org.springframework.dao.DataIntegrityViolationException: could not execute statement [ERROR: duplicate key value violates unique constraint "crm_partner_partner_code"
  Detail: Key (partner_code)=(CS000050) already exists.] [update lgc_forwarder_crm_partner set account_id=?,address=?,bank_account=?,bank_accs_no=?,bank_address=?,bank_currency=?,bank_name=?,bfsone_group_id=?,category=?,cell=?,country_id=?,country_label=?,created_by=?,created_time=?,date_created=?,date_modified=?,email=?,fax=?,group_name=?,home_phone=?,industry_code=?,industry_label=?,input_username=?,investment_origin=?,kcn_code=?,kcn_label=?,label=?,lead_code=?,localized_address=?,localized_label=?,modified_by=?,modified_time=?,name=?,note=?,partner_code=?,partner_code_temp=?,partner_group=?,personal_contact=?,print_custom_confirm_bill_info=?,province_id=?,province_label=?,is_refund=?,routing=?,sale_owner_contact_code=?,sale_owner_username=?,scope=?,shareable=?,source=?,status=?,storage_state=?,swift_code=?,tax_code=?,version=?,work_phone=? where id=? and version=?]; SQL [update lgc_forwarder_crm_partner set account_id=?,address=?,bank_account=?,bank_accs_no=?,bank_address=?,bank_currency=?,bank_name=?,bfsone_group_id=?,category=?,cell=?,country_id=?,country_label=?,created_by=?,created_time=?,date_created=?,date_modified=?,email=?,fax=?,group_name=?,home_phone=?,industry_code=?,industry_label=?,input_username=?,investment_origin=?,kcn_code=?,kcn_label=?,label=?,lead_code=?,localized_address=?,localized_label=?,modified_by=?,modified_time=?,name=?,note=?,partner_code=?,partner_code_temp=?,partner_group=?,personal_contact=?,print_custom_confirm_bill_info=?,province_id=?,province_label=?,is_refund=?,routing=?,sale_owner_contact_code=?,sale_owner_username=?,scope=?,shareable=?,source=?,status=?,storage_state=?,swift_code=?,tax_code=?,version=?,work_phone=? where id=? and version=?]; constraint [crm_partner_partner_code]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:290)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.JpaTransactionManager.doCommit(JpaTransactionManager.java:566)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.processCommit(AbstractPlatformTransactionManager.java:795)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.commit(AbstractPlatformTransactionManager.java:758)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.commitTransactionAfterReturning(TransactionAspectSupport.java:698)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:416)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:164)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy302.save(Unknown Source)
	at cloud.datatp.fforwarder.core.partner.CRMPartnerLogic.save(CRMPartnerLogic.java:111)
	at cloud.datatp.fforwarder.sales.integration.BFSOneCRMLogic.createBFSPartner(BFSOneCRMLogic.java:174)
	at cloud.datatp.fforwarder.sales.integration.BFSOneCRMService.createBFSOnePartner(BFSOneCRMService.java:29)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at cloud.datatp.fforwarder.sales.integration.BFSOneCRMService$$SpringCGLIB$$0.createBFSOnePartner(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.hibernate.exception.ConstraintViolationException: could not execute statement [ERROR: duplicate key value violates unique constraint "crm_partner_partner_code"
  Detail: Key (partner_code)=(CS000050) already exists.] [update lgc_forwarder_crm_partner set account_id=?,address=?,bank_account=?,bank_accs_no=?,bank_address=?,bank_currency=?,bank_name=?,bfsone_group_id=?,category=?,cell=?,country_id=?,country_label=?,created_by=?,created_time=?,date_created=?,date_modified=?,email=?,fax=?,group_name=?,home_phone=?,industry_code=?,industry_label=?,input_username=?,investment_origin=?,kcn_code=?,kcn_label=?,label=?,lead_code=?,localized_address=?,localized_label=?,modified_by=?,modified_time=?,name=?,note=?,partner_code=?,partner_code_temp=?,partner_group=?,personal_contact=?,print_custom_confirm_bill_info=?,province_id=?,province_label=?,is_refund=?,routing=?,sale_owner_contact_code=?,sale_owner_username=?,scope=?,shareable=?,source=?,status=?,storage_state=?,swift_code=?,tax_code=?,version=?,work_phone=? where id=? and version=?]
	at org.hibernate.exception.internal.SQLStateConversionDelegate.convert(SQLStateConversionDelegate.java:97)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:197)
	at org.hibernate.engine.jdbc.mutation.internal.AbstractMutationExecutor.performNonBatchedMutation(AbstractMutationExecutor.java:108)
	at org.hibernate.engine.jdbc.mutation.internal.MutationExecutorSingleNonBatched.performNonBatchedOperations(MutationExecutorSingleNonBatched.java:40)
	at org.hibernate.engine.jdbc.mutation.internal.AbstractMutationExecutor.execute(AbstractMutationExecutor.java:52)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.doStaticUpdate(UpdateCoordinatorStandard.java:778)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.performUpdate(UpdateCoordinatorStandard.java:324)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.coordinateUpdate(UpdateCoordinatorStandard.java:242)
	at org.hibernate.persister.entity.AbstractEntityPersister.update(AbstractEntityPersister.java:2810)
	at org.hibernate.action.internal.EntityUpdateAction.execute(EntityUpdateAction.java:168)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:632)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:499)
	at org.hibernate.event.internal.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:363)
	at org.hibernate.event.internal.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:41)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.doFlush(SessionImpl.java:1403)
	at org.hibernate.internal.SessionImpl.managedFlush(SessionImpl.java:484)
	at org.hibernate.internal.SessionImpl.flushBeforeTransactionCompletion(SessionImpl.java:2319)
	at org.hibernate.internal.SessionImpl.beforeTransactionCompletion(SessionImpl.java:1976)
	at org.hibernate.engine.jdbc.internal.JdbcCoordinatorImpl.beforeTransactionCompletion(JdbcCoordinatorImpl.java:439)
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcResourceLocalTransactionCoordinatorImpl.beforeCompletionCallback(JdbcResourceLocalTransactionCoordinatorImpl.java:169)
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcResourceLocalTransactionCoordinatorImpl$TransactionDriverControlImpl.commit(JdbcResourceLocalTransactionCoordinatorImpl.java:267)
	at org.hibernate.engine.transaction.internal.TransactionImpl.commit(TransactionImpl.java:101)
	at org.springframework.orm.jpa.JpaTransactionManager.doCommit(JpaTransactionManager.java:562)
	... 126 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: duplicate key value violates unique constraint "crm_partner_partner_code"
  Detail: Key (partner_code)=(CS000050) already exists.
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2725)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2412)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:371)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:502)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:419)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:194)
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:155)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:194)
	... 148 common frames omitted

2025-09-18T10:52:53.417+07:00 ERROR 89795 --- [qtp241358679-77] n.d.m.monitor.call.EndpointCallContext   : Start call with component BFSOneCRMService, method createBFSOnePartner, arguments
[ {
  "tenantId" : "default",
  "companyId" : 8,
  "companyParentId" : 4,
  "companyCode" : "beehph",
  "companyLabel" : "Bee HPH",
  "companyFullName" : "BEE LOGISTICS CORPORATION - HAI PHONG BRANCH OFFICE",
  "loginId" : "kai.vnhph",
  "accountId" : 1994,
  "token" : "4a758e39123fe8b5a2364bcefc38f355",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node05si5o2aovv581qa80fk3w6den0",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : {
    "beehph" : {
      "companyId" : 8,
      "companyParentId" : 4,
      "companyCode" : "beehph",
      "companyLabel" : "Bee HPH",
      "companyFullName" : null
    }
  },
  "featurePermissions" : [ {
    "id" : 14630,
    "appId" : 3,
    "appModule" : "project",
    "appName" : "project",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 2116,
    "appId" : 26,
    "appModule" : "okr",
    "appName" : "okr",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 12452,
    "appId" : 66,
    "appModule" : "tms",
    "appName" : "tms-bill-company",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Read"
  }, {
    "id" : 11672,
    "appId" : 69,
    "appModule" : "hr",
    "appName" : "user-kpi",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 13280,
    "appId" : 79,
    "appModule" : "company",
    "appName" : "user-asset",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 8564,
    "appId" : 16,
    "appModule" : "logistics",
    "appName" : "user-logistics-sales",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 8562,
    "appId" : 2,
    "appModule" : "user",
    "appName" : "my-employee-space",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 18464,
    "appId" : 88,
    "appModule" : "user",
    "appName" : "my-space",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  } ],
  "attributes" : { },
  "clientId" : "default:kai.vnhph"
}, {
  "editState" : "ORIGIN",
  "loadState" : "FromDB",
  "version" : 0,
  "storageState" : "ACTIVE",
  "partnerCode" : "CS000050",
  "partnerCodeTemp" : "CS000050",
  "status" : "NEW",
  "partnerGroup" : "CUSTOMERS",
  "category" : "CUSTOMER",
  "name" : "TEST343424323423423",
  "label" : "TEST343424323423423",
  "localizedLabel" : "TEST343424323423423",
  "personalContact" : "cvzxcv",
  "email" : "<EMAIL>",
  "cell" : "xzcvzxcvzxc",
  "source" : "WCA",
  "industryCode" : "I001",
  "industryLabel" : "Agriculture; plantations;other rural sectors ",
  "countryId" : 244,
  "countryLabel" : "VIETNAM",
  "provinceId" : 103,
  "provinceLabel" : "Thành phố Hà Nội",
  "kcnCode" : "41161",
  "kcnLabel" : "Cụm công nghiệp An Khánh",
  "address" : "Xã An Khánh, Huyện Hoài Đức, Thành phố Hà Nội",
  "localizedAddress" : "Xã An Khánh, Huyện Hoài Đức, Thành phố Hà Nội",
  "printCustomConfirmBillInfo" : "TEST343424323423423\nXã An Khánh, Huyện Hoài Đức, Thành phố Hà Nội\ncvzxcv\nTEL :xzcvzxcvzxc",
  "note" : "zxcvzxcv",
  "inputUsername" : "KAI.VNHPH",
  "scope" : "Domestic",
  "groupName" : "NORMAL",
  "refund" : false,
  "requestSalemanAccountId" : 1994,
  "requestSalemanLabel" : "NGUYỄN VĂN HƯNG"
} ]
2025-09-18T10:52:53.420+07:00 ERROR 89795 --- [qtp241358679-77] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:118)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.RuntimeException: Failed to create BFSOnePartner: CS000050
	at cloud.datatp.fforwarder.core.partner.CRMPartnerLogic.save(CRMPartnerLogic.java:117)
	at cloud.datatp.fforwarder.sales.integration.BFSOneCRMLogic.createBFSPartner(BFSOneCRMLogic.java:174)
	at cloud.datatp.fforwarder.sales.integration.BFSOneCRMService.createBFSOnePartner(BFSOneCRMService.java:29)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at cloud.datatp.fforwarder.sales.integration.BFSOneCRMService$$SpringCGLIB$$0.createBFSOnePartner(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted
Caused by: org.springframework.dao.DataIntegrityViolationException: could not execute statement [ERROR: duplicate key value violates unique constraint "crm_partner_partner_code"
  Detail: Key (partner_code)=(CS000050) already exists.] [update lgc_forwarder_crm_partner set account_id=?,address=?,bank_account=?,bank_accs_no=?,bank_address=?,bank_currency=?,bank_name=?,bfsone_group_id=?,category=?,cell=?,country_id=?,country_label=?,created_by=?,created_time=?,date_created=?,date_modified=?,email=?,fax=?,group_name=?,home_phone=?,industry_code=?,industry_label=?,input_username=?,investment_origin=?,kcn_code=?,kcn_label=?,label=?,lead_code=?,localized_address=?,localized_label=?,modified_by=?,modified_time=?,name=?,note=?,partner_code=?,partner_code_temp=?,partner_group=?,personal_contact=?,print_custom_confirm_bill_info=?,province_id=?,province_label=?,is_refund=?,routing=?,sale_owner_contact_code=?,sale_owner_username=?,scope=?,shareable=?,source=?,status=?,storage_state=?,swift_code=?,tax_code=?,version=?,work_phone=? where id=? and version=?]; SQL [update lgc_forwarder_crm_partner set account_id=?,address=?,bank_account=?,bank_accs_no=?,bank_address=?,bank_currency=?,bank_name=?,bfsone_group_id=?,category=?,cell=?,country_id=?,country_label=?,created_by=?,created_time=?,date_created=?,date_modified=?,email=?,fax=?,group_name=?,home_phone=?,industry_code=?,industry_label=?,input_username=?,investment_origin=?,kcn_code=?,kcn_label=?,label=?,lead_code=?,localized_address=?,localized_label=?,modified_by=?,modified_time=?,name=?,note=?,partner_code=?,partner_code_temp=?,partner_group=?,personal_contact=?,print_custom_confirm_bill_info=?,province_id=?,province_label=?,is_refund=?,routing=?,sale_owner_contact_code=?,sale_owner_username=?,scope=?,shareable=?,source=?,status=?,storage_state=?,swift_code=?,tax_code=?,version=?,work_phone=? where id=? and version=?]; constraint [crm_partner_partner_code]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:290)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.JpaTransactionManager.doCommit(JpaTransactionManager.java:566)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.processCommit(AbstractPlatformTransactionManager.java:795)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.commit(AbstractPlatformTransactionManager.java:758)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.commitTransactionAfterReturning(TransactionAspectSupport.java:698)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:416)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:164)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy302.save(Unknown Source)
	at cloud.datatp.fforwarder.core.partner.CRMPartnerLogic.save(CRMPartnerLogic.java:111)
	... 111 common frames omitted
Caused by: org.hibernate.exception.ConstraintViolationException: could not execute statement [ERROR: duplicate key value violates unique constraint "crm_partner_partner_code"
  Detail: Key (partner_code)=(CS000050) already exists.] [update lgc_forwarder_crm_partner set account_id=?,address=?,bank_account=?,bank_accs_no=?,bank_address=?,bank_currency=?,bank_name=?,bfsone_group_id=?,category=?,cell=?,country_id=?,country_label=?,created_by=?,created_time=?,date_created=?,date_modified=?,email=?,fax=?,group_name=?,home_phone=?,industry_code=?,industry_label=?,input_username=?,investment_origin=?,kcn_code=?,kcn_label=?,label=?,lead_code=?,localized_address=?,localized_label=?,modified_by=?,modified_time=?,name=?,note=?,partner_code=?,partner_code_temp=?,partner_group=?,personal_contact=?,print_custom_confirm_bill_info=?,province_id=?,province_label=?,is_refund=?,routing=?,sale_owner_contact_code=?,sale_owner_username=?,scope=?,shareable=?,source=?,status=?,storage_state=?,swift_code=?,tax_code=?,version=?,work_phone=? where id=? and version=?]
	at org.hibernate.exception.internal.SQLStateConversionDelegate.convert(SQLStateConversionDelegate.java:97)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:197)
	at org.hibernate.engine.jdbc.mutation.internal.AbstractMutationExecutor.performNonBatchedMutation(AbstractMutationExecutor.java:108)
	at org.hibernate.engine.jdbc.mutation.internal.MutationExecutorSingleNonBatched.performNonBatchedOperations(MutationExecutorSingleNonBatched.java:40)
	at org.hibernate.engine.jdbc.mutation.internal.AbstractMutationExecutor.execute(AbstractMutationExecutor.java:52)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.doStaticUpdate(UpdateCoordinatorStandard.java:778)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.performUpdate(UpdateCoordinatorStandard.java:324)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.coordinateUpdate(UpdateCoordinatorStandard.java:242)
	at org.hibernate.persister.entity.AbstractEntityPersister.update(AbstractEntityPersister.java:2810)
	at org.hibernate.action.internal.EntityUpdateAction.execute(EntityUpdateAction.java:168)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:632)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:499)
	at org.hibernate.event.internal.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:363)
	at org.hibernate.event.internal.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:41)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.doFlush(SessionImpl.java:1403)
	at org.hibernate.internal.SessionImpl.managedFlush(SessionImpl.java:484)
	at org.hibernate.internal.SessionImpl.flushBeforeTransactionCompletion(SessionImpl.java:2319)
	at org.hibernate.internal.SessionImpl.beforeTransactionCompletion(SessionImpl.java:1976)
	at org.hibernate.engine.jdbc.internal.JdbcCoordinatorImpl.beforeTransactionCompletion(JdbcCoordinatorImpl.java:439)
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcResourceLocalTransactionCoordinatorImpl.beforeCompletionCallback(JdbcResourceLocalTransactionCoordinatorImpl.java:169)
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcResourceLocalTransactionCoordinatorImpl$TransactionDriverControlImpl.commit(JdbcResourceLocalTransactionCoordinatorImpl.java:267)
	at org.hibernate.engine.transaction.internal.TransactionImpl.commit(TransactionImpl.java:101)
	at org.springframework.orm.jpa.JpaTransactionManager.doCommit(JpaTransactionManager.java:562)
	... 126 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: duplicate key value violates unique constraint "crm_partner_partner_code"
  Detail: Key (partner_code)=(CS000050) already exists.
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2725)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2412)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:371)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:502)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:419)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:194)
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:155)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:194)
	... 148 common frames omitted

2025-09-18T10:52:53.425+07:00  INFO 89795 --- [qtp241358679-77] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint BFSOneCRMService/createBFSOnePartner
2025-09-18T10:52:56.022+07:00  INFO 89795 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-09-18T10:52:56.031+07:00  INFO 89795 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T10:53:03.045+07:00  INFO 89795 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T10:54:06.166+07:00  INFO 89795 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T10:54:46.938+07:00  WARN 89795 --- [rw housekeeper] com.zaxxer.hikari.pool.ProxyLeakTask     : Connection leak detection triggered for org.postgresql.jdbc.PgConnection@2e5a3627 on thread qtp241358679-77, stack trace follows

java.lang.Exception: Apparent connection leak detected
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:127)
	at org.hibernate.engine.jdbc.connections.internal.DatasourceConnectionProviderImpl.getConnection(DatasourceConnectionProviderImpl.java:122)
	at org.hibernate.internal.NonContextualJdbcConnectionAccess.obtainConnection(NonContextualJdbcConnectionAccess.java:46)
	at org.hibernate.resource.jdbc.internal.LogicalConnectionManagedImpl.acquireConnectionIfNeeded(LogicalConnectionManagedImpl.java:113)
	at org.hibernate.resource.jdbc.internal.LogicalConnectionManagedImpl.getPhysicalConnection(LogicalConnectionManagedImpl.java:143)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl.connection(StatementPreparerImpl.java:54)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$5.doPrepare(StatementPreparerImpl.java:153)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:183)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl.prepareQueryStatement(StatementPreparerImpl.java:155)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.lambda$list$0(JdbcSelectExecutor.java:85)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.executeQuery(DeferredResultSetAccess.java:231)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.getResultSet(DeferredResultSetAccess.java:167)
	at org.hibernate.sql.results.jdbc.internal.JdbcValuesResultSetImpl.advanceNext(JdbcValuesResultSetImpl.java:218)
	at org.hibernate.sql.results.jdbc.internal.JdbcValuesResultSetImpl.processNext(JdbcValuesResultSetImpl.java:98)
	at org.hibernate.sql.results.jdbc.internal.AbstractJdbcValues.next(AbstractJdbcValues.java:19)
	at org.hibernate.sql.results.internal.RowProcessingStateStandardImpl.next(RowProcessingStateStandardImpl.java:66)
	at org.hibernate.sql.results.spi.ListResultsConsumer.consume(ListResultsConsumer.java:202)
	at org.hibernate.sql.results.spi.ListResultsConsumer.consume(ListResultsConsumer.java:33)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.doExecuteQuery(JdbcSelectExecutorStandardImpl.java:209)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.executeQuery(JdbcSelectExecutorStandardImpl.java:83)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.list(JdbcSelectExecutor.java:76)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.list(JdbcSelectExecutor.java:65)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.lambda$new$2(ConcreteSqmSelectQueryPlan.java:137)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.withCacheableSqmInterpretation(ConcreteSqmSelectQueryPlan.java:362)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.performList(ConcreteSqmSelectQueryPlan.java:303)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doList(QuerySqmImpl.java:509)
	at org.hibernate.query.spi.AbstractSelectionQuery.list(AbstractSelectionQuery.java:427)
	at org.hibernate.query.spi.AbstractSelectionQuery.getSingleResult(AbstractSelectionQuery.java:564)
	at org.springframework.data.jpa.repository.query.JpaQueryExecution$SingleEntityExecution.doExecute(JpaQueryExecution.java:223)
	at org.springframework.data.jpa.repository.query.JpaQueryExecution.execute(JpaQueryExecution.java:92)
	at org.springframework.data.jpa.repository.query.AbstractJpaQuery.doExecute(AbstractJpaQuery.java:149)
	at org.springframework.data.jpa.repository.query.AbstractJpaQuery.execute(AbstractJpaQuery.java:137)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:143)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:135)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy325.getByAccountId(Unknown Source)
	at cloud.datatp.fforwarder.core.template.CRMUserRoleLogic.getByAccountId(CRMUserRoleLogic.java:70)
	at cloud.datatp.fforwarder.sales.integration.BFSOneCRMLogic.createBFSPartner(BFSOneCRMLogic.java:162)
	at cloud.datatp.fforwarder.sales.integration.BFSOneCRMService.createBFSOnePartner(BFSOneCRMService.java:29)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at cloud.datatp.fforwarder.sales.integration.BFSOneCRMService$$SpringCGLIB$$0.createBFSOnePartner(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)

2025-09-18T10:54:55.251+07:00  INFO 89795 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T10:54:55.252+07:00  INFO 89795 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T10:54:58.680+07:00  INFO 89795 --- [qtp241358679-77] c.d.f.core.partner.CRMPartnerLogic       : --- Create account: bfsone code = CS000051
2025-09-18T10:54:59.645+07:00  WARN 89795 --- [qtp241358679-77] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-18T10:54:59.646+07:00 ERROR 89795 --- [qtp241358679-77] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "crm_partner_partner_code"
  Detail: Key (partner_code)=(CS000051) already exists.
2025-09-18T10:54:59.648+07:00 ERROR 89795 --- [qtp241358679-77] c.d.f.core.partner.CRMPartnerLogic       : Error while creating BFSOnePartner: CS000051

org.springframework.dao.DataIntegrityViolationException: could not execute statement [ERROR: duplicate key value violates unique constraint "crm_partner_partner_code"
  Detail: Key (partner_code)=(CS000051) already exists.] [update lgc_forwarder_crm_partner set account_id=?,address=?,bank_account=?,bank_accs_no=?,bank_address=?,bank_currency=?,bank_name=?,bfsone_group_id=?,category=?,cell=?,country_id=?,country_label=?,created_by=?,created_time=?,date_created=?,date_modified=?,email=?,fax=?,group_name=?,home_phone=?,industry_code=?,industry_label=?,input_username=?,investment_origin=?,kcn_code=?,kcn_label=?,label=?,lead_code=?,localized_address=?,localized_label=?,modified_by=?,modified_time=?,name=?,note=?,partner_code=?,partner_code_temp=?,partner_group=?,personal_contact=?,print_custom_confirm_bill_info=?,province_id=?,province_label=?,is_refund=?,routing=?,sale_owner_contact_code=?,sale_owner_username=?,scope=?,shareable=?,source=?,status=?,storage_state=?,swift_code=?,tax_code=?,version=?,work_phone=? where id=? and version=?]; SQL [update lgc_forwarder_crm_partner set account_id=?,address=?,bank_account=?,bank_accs_no=?,bank_address=?,bank_currency=?,bank_name=?,bfsone_group_id=?,category=?,cell=?,country_id=?,country_label=?,created_by=?,created_time=?,date_created=?,date_modified=?,email=?,fax=?,group_name=?,home_phone=?,industry_code=?,industry_label=?,input_username=?,investment_origin=?,kcn_code=?,kcn_label=?,label=?,lead_code=?,localized_address=?,localized_label=?,modified_by=?,modified_time=?,name=?,note=?,partner_code=?,partner_code_temp=?,partner_group=?,personal_contact=?,print_custom_confirm_bill_info=?,province_id=?,province_label=?,is_refund=?,routing=?,sale_owner_contact_code=?,sale_owner_username=?,scope=?,shareable=?,source=?,status=?,storage_state=?,swift_code=?,tax_code=?,version=?,work_phone=? where id=? and version=?]; constraint [crm_partner_partner_code]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:290)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.JpaTransactionManager.doCommit(JpaTransactionManager.java:566)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.processCommit(AbstractPlatformTransactionManager.java:795)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.commit(AbstractPlatformTransactionManager.java:758)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.commitTransactionAfterReturning(TransactionAspectSupport.java:698)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:416)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:164)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy302.save(Unknown Source)
	at cloud.datatp.fforwarder.core.partner.CRMPartnerLogic.save(CRMPartnerLogic.java:111)
	at cloud.datatp.fforwarder.sales.integration.BFSOneCRMLogic.createBFSPartner(BFSOneCRMLogic.java:174)
	at cloud.datatp.fforwarder.sales.integration.BFSOneCRMService.createBFSOnePartner(BFSOneCRMService.java:29)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at cloud.datatp.fforwarder.sales.integration.BFSOneCRMService$$SpringCGLIB$$0.createBFSOnePartner(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.hibernate.exception.ConstraintViolationException: could not execute statement [ERROR: duplicate key value violates unique constraint "crm_partner_partner_code"
  Detail: Key (partner_code)=(CS000051) already exists.] [update lgc_forwarder_crm_partner set account_id=?,address=?,bank_account=?,bank_accs_no=?,bank_address=?,bank_currency=?,bank_name=?,bfsone_group_id=?,category=?,cell=?,country_id=?,country_label=?,created_by=?,created_time=?,date_created=?,date_modified=?,email=?,fax=?,group_name=?,home_phone=?,industry_code=?,industry_label=?,input_username=?,investment_origin=?,kcn_code=?,kcn_label=?,label=?,lead_code=?,localized_address=?,localized_label=?,modified_by=?,modified_time=?,name=?,note=?,partner_code=?,partner_code_temp=?,partner_group=?,personal_contact=?,print_custom_confirm_bill_info=?,province_id=?,province_label=?,is_refund=?,routing=?,sale_owner_contact_code=?,sale_owner_username=?,scope=?,shareable=?,source=?,status=?,storage_state=?,swift_code=?,tax_code=?,version=?,work_phone=? where id=? and version=?]
	at org.hibernate.exception.internal.SQLStateConversionDelegate.convert(SQLStateConversionDelegate.java:97)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:197)
	at org.hibernate.engine.jdbc.mutation.internal.AbstractMutationExecutor.performNonBatchedMutation(AbstractMutationExecutor.java:108)
	at org.hibernate.engine.jdbc.mutation.internal.MutationExecutorSingleNonBatched.performNonBatchedOperations(MutationExecutorSingleNonBatched.java:40)
	at org.hibernate.engine.jdbc.mutation.internal.AbstractMutationExecutor.execute(AbstractMutationExecutor.java:52)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.doStaticUpdate(UpdateCoordinatorStandard.java:778)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.performUpdate(UpdateCoordinatorStandard.java:324)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.coordinateUpdate(UpdateCoordinatorStandard.java:242)
	at org.hibernate.persister.entity.AbstractEntityPersister.update(AbstractEntityPersister.java:2810)
	at org.hibernate.action.internal.EntityUpdateAction.execute(EntityUpdateAction.java:168)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:632)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:499)
	at org.hibernate.event.internal.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:363)
	at org.hibernate.event.internal.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:41)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.doFlush(SessionImpl.java:1403)
	at org.hibernate.internal.SessionImpl.managedFlush(SessionImpl.java:484)
	at org.hibernate.internal.SessionImpl.flushBeforeTransactionCompletion(SessionImpl.java:2319)
	at org.hibernate.internal.SessionImpl.beforeTransactionCompletion(SessionImpl.java:1976)
	at org.hibernate.engine.jdbc.internal.JdbcCoordinatorImpl.beforeTransactionCompletion(JdbcCoordinatorImpl.java:439)
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcResourceLocalTransactionCoordinatorImpl.beforeCompletionCallback(JdbcResourceLocalTransactionCoordinatorImpl.java:169)
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcResourceLocalTransactionCoordinatorImpl$TransactionDriverControlImpl.commit(JdbcResourceLocalTransactionCoordinatorImpl.java:267)
	at org.hibernate.engine.transaction.internal.TransactionImpl.commit(TransactionImpl.java:101)
	at org.springframework.orm.jpa.JpaTransactionManager.doCommit(JpaTransactionManager.java:562)
	... 126 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: duplicate key value violates unique constraint "crm_partner_partner_code"
  Detail: Key (partner_code)=(CS000051) already exists.
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2725)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2412)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:371)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:502)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:419)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:194)
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:155)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:194)
	... 148 common frames omitted

2025-09-18T10:54:59.656+07:00  INFO 89795 --- [qtp241358679-77] com.zaxxer.hikari.pool.ProxyLeakTask     : Previously reported leaked connection org.postgresql.jdbc.PgConnection@2e5a3627 on thread qtp241358679-77 was returned to the pool (unleaked)
2025-09-18T10:54:59.657+07:00 ERROR 89795 --- [qtp241358679-77] n.d.m.monitor.call.EndpointCallContext   : Start call with component BFSOneCRMService, method createBFSOnePartner, arguments
[ {
  "tenantId" : "default",
  "companyId" : 8,
  "companyParentId" : 4,
  "companyCode" : "beehph",
  "companyLabel" : "Bee HPH",
  "companyFullName" : "BEE LOGISTICS CORPORATION - HAI PHONG BRANCH OFFICE",
  "loginId" : "kai.vnhph",
  "accountId" : 1994,
  "token" : "4a758e39123fe8b5a2364bcefc38f355",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node05si5o2aovv581qa80fk3w6den0",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : {
    "beehph" : {
      "companyId" : 8,
      "companyParentId" : 4,
      "companyCode" : "beehph",
      "companyLabel" : "Bee HPH",
      "companyFullName" : null
    }
  },
  "featurePermissions" : [ {
    "id" : 14630,
    "appId" : 3,
    "appModule" : "project",
    "appName" : "project",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 2116,
    "appId" : 26,
    "appModule" : "okr",
    "appName" : "okr",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 12452,
    "appId" : 66,
    "appModule" : "tms",
    "appName" : "tms-bill-company",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Read"
  }, {
    "id" : 11672,
    "appId" : 69,
    "appModule" : "hr",
    "appName" : "user-kpi",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 13280,
    "appId" : 79,
    "appModule" : "company",
    "appName" : "user-asset",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 8564,
    "appId" : 16,
    "appModule" : "logistics",
    "appName" : "user-logistics-sales",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 8562,
    "appId" : 2,
    "appModule" : "user",
    "appName" : "my-employee-space",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 18464,
    "appId" : 88,
    "appModule" : "user",
    "appName" : "my-space",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  } ],
  "attributes" : { },
  "clientId" : "default:kai.vnhph"
}, {
  "editState" : "ORIGIN",
  "loadState" : "FromDB",
  "version" : 0,
  "storageState" : "ACTIVE",
  "partnerCode" : "CS000051",
  "partnerCodeTemp" : "CS000051",
  "status" : "NEW",
  "partnerGroup" : "CUSTOMERS",
  "category" : "CUSTOMER",
  "name" : "TEST343424323423423",
  "label" : "TEST343424323423423",
  "localizedLabel" : "TEST343424323423423",
  "personalContact" : "cvzxcv",
  "email" : "<EMAIL>",
  "cell" : "xzcvzxcvzxc",
  "source" : "WCA",
  "industryCode" : "I001",
  "industryLabel" : "Agriculture; plantations;other rural sectors ",
  "countryId" : 244,
  "countryLabel" : "VIETNAM",
  "provinceId" : 103,
  "provinceLabel" : "Thành phố Hà Nội",
  "kcnCode" : "41161",
  "kcnLabel" : "Cụm công nghiệp An Khánh",
  "address" : "Xã An Khánh, Huyện Hoài Đức, Thành phố Hà Nội",
  "localizedAddress" : "Xã An Khánh, Huyện Hoài Đức, Thành phố Hà Nội",
  "printCustomConfirmBillInfo" : "TEST343424323423423\nXã An Khánh, Huyện Hoài Đức, Thành phố Hà Nội\ncvzxcv\nTEL :xzcvzxcvzxc",
  "note" : "zxcvzxcv",
  "inputUsername" : "KAI.VNHPH",
  "scope" : "Domestic",
  "groupName" : "NORMAL",
  "refund" : false,
  "requestSalemanAccountId" : 1994,
  "requestSalemanLabel" : "NGUYỄN VĂN HƯNG"
} ]
2025-09-18T10:54:59.658+07:00 ERROR 89795 --- [qtp241358679-77] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:118)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.RuntimeException: Failed to create BFSOnePartner: CS000051
	at cloud.datatp.fforwarder.core.partner.CRMPartnerLogic.save(CRMPartnerLogic.java:117)
	at cloud.datatp.fforwarder.sales.integration.BFSOneCRMLogic.createBFSPartner(BFSOneCRMLogic.java:174)
	at cloud.datatp.fforwarder.sales.integration.BFSOneCRMService.createBFSOnePartner(BFSOneCRMService.java:29)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at cloud.datatp.fforwarder.sales.integration.BFSOneCRMService$$SpringCGLIB$$0.createBFSOnePartner(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted
Caused by: org.springframework.dao.DataIntegrityViolationException: could not execute statement [ERROR: duplicate key value violates unique constraint "crm_partner_partner_code"
  Detail: Key (partner_code)=(CS000051) already exists.] [update lgc_forwarder_crm_partner set account_id=?,address=?,bank_account=?,bank_accs_no=?,bank_address=?,bank_currency=?,bank_name=?,bfsone_group_id=?,category=?,cell=?,country_id=?,country_label=?,created_by=?,created_time=?,date_created=?,date_modified=?,email=?,fax=?,group_name=?,home_phone=?,industry_code=?,industry_label=?,input_username=?,investment_origin=?,kcn_code=?,kcn_label=?,label=?,lead_code=?,localized_address=?,localized_label=?,modified_by=?,modified_time=?,name=?,note=?,partner_code=?,partner_code_temp=?,partner_group=?,personal_contact=?,print_custom_confirm_bill_info=?,province_id=?,province_label=?,is_refund=?,routing=?,sale_owner_contact_code=?,sale_owner_username=?,scope=?,shareable=?,source=?,status=?,storage_state=?,swift_code=?,tax_code=?,version=?,work_phone=? where id=? and version=?]; SQL [update lgc_forwarder_crm_partner set account_id=?,address=?,bank_account=?,bank_accs_no=?,bank_address=?,bank_currency=?,bank_name=?,bfsone_group_id=?,category=?,cell=?,country_id=?,country_label=?,created_by=?,created_time=?,date_created=?,date_modified=?,email=?,fax=?,group_name=?,home_phone=?,industry_code=?,industry_label=?,input_username=?,investment_origin=?,kcn_code=?,kcn_label=?,label=?,lead_code=?,localized_address=?,localized_label=?,modified_by=?,modified_time=?,name=?,note=?,partner_code=?,partner_code_temp=?,partner_group=?,personal_contact=?,print_custom_confirm_bill_info=?,province_id=?,province_label=?,is_refund=?,routing=?,sale_owner_contact_code=?,sale_owner_username=?,scope=?,shareable=?,source=?,status=?,storage_state=?,swift_code=?,tax_code=?,version=?,work_phone=? where id=? and version=?]; constraint [crm_partner_partner_code]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:290)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.JpaTransactionManager.doCommit(JpaTransactionManager.java:566)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.processCommit(AbstractPlatformTransactionManager.java:795)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.commit(AbstractPlatformTransactionManager.java:758)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.commitTransactionAfterReturning(TransactionAspectSupport.java:698)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:416)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:164)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy302.save(Unknown Source)
	at cloud.datatp.fforwarder.core.partner.CRMPartnerLogic.save(CRMPartnerLogic.java:111)
	... 111 common frames omitted
Caused by: org.hibernate.exception.ConstraintViolationException: could not execute statement [ERROR: duplicate key value violates unique constraint "crm_partner_partner_code"
  Detail: Key (partner_code)=(CS000051) already exists.] [update lgc_forwarder_crm_partner set account_id=?,address=?,bank_account=?,bank_accs_no=?,bank_address=?,bank_currency=?,bank_name=?,bfsone_group_id=?,category=?,cell=?,country_id=?,country_label=?,created_by=?,created_time=?,date_created=?,date_modified=?,email=?,fax=?,group_name=?,home_phone=?,industry_code=?,industry_label=?,input_username=?,investment_origin=?,kcn_code=?,kcn_label=?,label=?,lead_code=?,localized_address=?,localized_label=?,modified_by=?,modified_time=?,name=?,note=?,partner_code=?,partner_code_temp=?,partner_group=?,personal_contact=?,print_custom_confirm_bill_info=?,province_id=?,province_label=?,is_refund=?,routing=?,sale_owner_contact_code=?,sale_owner_username=?,scope=?,shareable=?,source=?,status=?,storage_state=?,swift_code=?,tax_code=?,version=?,work_phone=? where id=? and version=?]
	at org.hibernate.exception.internal.SQLStateConversionDelegate.convert(SQLStateConversionDelegate.java:97)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:197)
	at org.hibernate.engine.jdbc.mutation.internal.AbstractMutationExecutor.performNonBatchedMutation(AbstractMutationExecutor.java:108)
	at org.hibernate.engine.jdbc.mutation.internal.MutationExecutorSingleNonBatched.performNonBatchedOperations(MutationExecutorSingleNonBatched.java:40)
	at org.hibernate.engine.jdbc.mutation.internal.AbstractMutationExecutor.execute(AbstractMutationExecutor.java:52)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.doStaticUpdate(UpdateCoordinatorStandard.java:778)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.performUpdate(UpdateCoordinatorStandard.java:324)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.coordinateUpdate(UpdateCoordinatorStandard.java:242)
	at org.hibernate.persister.entity.AbstractEntityPersister.update(AbstractEntityPersister.java:2810)
	at org.hibernate.action.internal.EntityUpdateAction.execute(EntityUpdateAction.java:168)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:632)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:499)
	at org.hibernate.event.internal.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:363)
	at org.hibernate.event.internal.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:41)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.doFlush(SessionImpl.java:1403)
	at org.hibernate.internal.SessionImpl.managedFlush(SessionImpl.java:484)
	at org.hibernate.internal.SessionImpl.flushBeforeTransactionCompletion(SessionImpl.java:2319)
	at org.hibernate.internal.SessionImpl.beforeTransactionCompletion(SessionImpl.java:1976)
	at org.hibernate.engine.jdbc.internal.JdbcCoordinatorImpl.beforeTransactionCompletion(JdbcCoordinatorImpl.java:439)
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcResourceLocalTransactionCoordinatorImpl.beforeCompletionCallback(JdbcResourceLocalTransactionCoordinatorImpl.java:169)
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcResourceLocalTransactionCoordinatorImpl$TransactionDriverControlImpl.commit(JdbcResourceLocalTransactionCoordinatorImpl.java:267)
	at org.hibernate.engine.transaction.internal.TransactionImpl.commit(TransactionImpl.java:101)
	at org.springframework.orm.jpa.JpaTransactionManager.doCommit(JpaTransactionManager.java:562)
	... 126 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: duplicate key value violates unique constraint "crm_partner_partner_code"
  Detail: Key (partner_code)=(CS000051) already exists.
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2725)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2412)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:371)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:502)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:419)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:194)
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:155)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:194)
	... 148 common frames omitted

2025-09-18T10:54:59.660+07:00  INFO 89795 --- [qtp241358679-77] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint BFSOneCRMService/createBFSOnePartner
2025-09-18T10:55:02.261+07:00  INFO 89795 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T10:55:02.262+07:00  INFO 89795 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T10:55:05.532+07:00  INFO 89795 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@74e4af6c{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-18T10:55:05.533+07:00  INFO 89795 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-18T10:55:05.533+07:00  INFO 89795 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-18T10:55:05.533+07:00  INFO 89795 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-18T10:55:05.533+07:00  INFO 89795 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-18T10:55:05.533+07:00  INFO 89795 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-18T10:55:05.534+07:00  INFO 89795 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-18T10:55:05.534+07:00  INFO 89795 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-18T10:55:05.534+07:00  INFO 89795 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-09-18T10:55:05.534+07:00  INFO 89795 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-09-18T10:55:05.534+07:00  INFO 89795 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-18T10:55:05.534+07:00  INFO 89795 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-09-18T10:55:05.534+07:00  INFO 89795 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-09-18T10:55:05.534+07:00  INFO 89795 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-18T10:55:05.534+07:00  INFO 89795 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-09-18T10:55:05.534+07:00  INFO 89795 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-09-18T10:55:05.548+07:00  INFO 89795 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T10:55:05.605+07:00  INFO 89795 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-18T10:55:05.611+07:00  INFO 89795 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-18T10:55:05.627+07:00  INFO 89795 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-18T10:55:05.628+07:00  INFO 89795 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-18T10:55:05.629+07:00  INFO 89795 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-18T10:55:05.629+07:00  INFO 89795 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-18T10:55:05.630+07:00  INFO 89795 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-18T10:55:05.630+07:00  INFO 89795 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-18T10:55:05.631+07:00  INFO 89795 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-18T10:55:05.631+07:00  INFO 89795 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-18T10:55:05.631+07:00  INFO 89795 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-18T10:55:05.631+07:00  INFO 89795 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-18T10:55:05.632+07:00  INFO 89795 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-18T10:55:05.632+07:00  INFO 89795 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-18T10:55:05.632+07:00  INFO 89795 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-18T10:55:05.633+07:00  INFO 89795 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@744d0cfb{STOPPING}[12.0.15,sto=0]
2025-09-18T10:55:05.635+07:00  INFO 89795 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-18T10:55:05.636+07:00  INFO 89795 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@5c452eb5{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.10555924053463811125/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@1a2bcce1{STOPPED}}
