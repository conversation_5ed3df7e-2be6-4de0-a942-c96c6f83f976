2025-09-18T13:37:54.275+07:00  INFO 9253 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 9253 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-18T13:37:54.276+07:00  INFO 9253 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-18T13:37:55.465+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T13:37:55.563+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 93 ms. Found 22 JPA repository interfaces.
2025-09-18T13:37:55.575+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T13:37:55.577+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-18T13:37:55.578+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T13:37:55.589+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 11 ms. Found 10 JPA repository interfaces.
2025-09-18T13:37:55.590+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T13:37:55.641+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 50 ms. Found 3 JPA repository interfaces.
2025-09-18T13:37:55.654+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T13:37:55.663+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 1 JPA repository interface.
2025-09-18T13:37:55.673+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T13:37:55.678+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 2 JPA repository interfaces.
2025-09-18T13:37:55.678+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T13:37:55.682+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-09-18T13:37:55.685+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T13:37:55.690+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 5 JPA repository interfaces.
2025-09-18T13:37:55.695+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T13:37:55.699+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 3 JPA repository interfaces.
2025-09-18T13:37:55.700+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T13:37:55.704+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 0 JPA repository interfaces.
2025-09-18T13:37:55.704+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T13:37:55.714+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 10 JPA repository interfaces.
2025-09-18T13:37:55.720+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T13:37:55.723+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-18T13:37:55.727+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T13:37:55.734+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 6 JPA repository interfaces.
2025-09-18T13:37:55.734+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T13:37:55.743+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 12 JPA repository interfaces.
2025-09-18T13:37:55.744+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T13:37:55.748+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 4 JPA repository interfaces.
2025-09-18T13:37:55.748+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T13:37:55.749+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-18T13:37:55.749+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T13:37:55.750+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-18T13:37:55.750+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T13:37:55.755+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-09-18T13:37:55.756+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T13:37:55.758+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 2 JPA repository interfaces.
2025-09-18T13:37:55.758+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T13:37:55.759+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-18T13:37:55.759+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T13:37:55.774+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 14 ms. Found 19 JPA repository interfaces.
2025-09-18T13:37:55.785+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T13:37:55.799+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 13 ms. Found 8 JPA repository interfaces.
2025-09-18T13:37:55.799+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T13:37:55.803+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-09-18T13:37:55.803+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T13:37:55.807+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-09-18T13:37:55.808+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T13:37:55.830+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 21 ms. Found 9 JPA repository interfaces.
2025-09-18T13:37:55.830+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T13:37:55.836+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 6 JPA repository interfaces.
2025-09-18T13:37:55.837+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T13:37:55.851+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 13 ms. Found 13 JPA repository interfaces.
2025-09-18T13:37:55.852+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T13:37:55.866+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 14 ms. Found 14 JPA repository interfaces.
2025-09-18T13:37:55.866+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T13:37:55.885+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 18 ms. Found 24 JPA repository interfaces.
2025-09-18T13:37:55.886+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T13:37:55.888+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-09-18T13:37:55.896+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T13:37:55.897+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-18T13:37:55.898+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T13:37:55.906+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 12 JPA repository interfaces.
2025-09-18T13:37:55.909+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T13:37:55.968+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 58 ms. Found 66 JPA repository interfaces.
2025-09-18T13:37:55.968+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T13:37:55.969+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-18T13:37:55.977+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T13:37:55.981+07:00  INFO 9253 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-09-18T13:37:56.270+07:00  INFO 9253 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-18T13:37:56.274+07:00  INFO 9253 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-18T13:37:56.615+07:00  WARN 9253 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-18T13:37:56.852+07:00  INFO 9253 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-18T13:37:56.854+07:00  INFO 9253 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-18T13:37:56.867+07:00  INFO 9253 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-18T13:37:56.868+07:00  INFO 9253 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2427 ms
2025-09-18T13:37:56.944+07:00  WARN 9253 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-18T13:37:56.944+07:00  INFO 9253 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-18T13:37:57.113+07:00  INFO 9253 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@6e18bc2f
2025-09-18T13:37:57.119+07:00  INFO 9253 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-18T13:37:57.125+07:00  WARN 9253 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-18T13:37:57.125+07:00  INFO 9253 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-18T13:37:57.146+07:00  INFO 9253 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@1b13e4fb
2025-09-18T13:37:57.147+07:00  INFO 9253 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-18T13:37:57.148+07:00  WARN 9253 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-18T13:37:57.148+07:00  INFO 9253 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-18T13:37:57.160+07:00  INFO 9253 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@194feb27
2025-09-18T13:37:57.160+07:00  INFO 9253 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-18T13:37:57.160+07:00  WARN 9253 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-18T13:37:57.160+07:00  INFO 9253 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-18T13:37:57.167+07:00  INFO 9253 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@7dbf92aa
2025-09-18T13:37:57.168+07:00  INFO 9253 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-18T13:37:57.168+07:00  WARN 9253 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-18T13:37:57.168+07:00  INFO 9253 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-18T13:37:57.175+07:00  INFO 9253 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@7c1a5092
2025-09-18T13:37:57.175+07:00  INFO 9253 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-18T13:37:57.176+07:00  INFO 9253 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-18T13:37:57.257+07:00  INFO 9253 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-18T13:37:57.259+07:00  INFO 9253 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@5c452eb5{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.9162318785609809297/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@1a2bcce1{STARTED}}
2025-09-18T13:37:57.260+07:00  INFO 9253 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@5c452eb5{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.9162318785609809297/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@1a2bcce1{STARTED}}
2025-09-18T13:37:57.262+07:00  INFO 9253 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@744d0cfb{STARTING}[12.0.15,sto=0] @3902ms
2025-09-18T13:37:57.391+07:00  INFO 9253 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-18T13:37:57.417+07:00  INFO 9253 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-18T13:37:57.431+07:00  INFO 9253 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-18T13:37:57.615+07:00  INFO 9253 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-18T13:37:57.664+07:00  WARN 9253 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-18T13:37:58.560+07:00  INFO 9253 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-18T13:37:58.569+07:00  INFO 9253 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@44c164e5] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-18T13:37:58.779+07:00  INFO 9253 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-18T13:37:58.990+07:00  INFO 9253 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.wfms", "net.datatp.module.monitor.activity", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "cloud.datatp.module.fleet", "net.datatp.module.okr", "net.datatp.module.core.template", "cloud.datatp.gps", "cloud.datatp.module.pegasus.shipment", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "cloud.datatp.tms", "net.datatp.module.company.tmpl", "net.datatp.module.groovy", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "cloud.datatp.vendor", "net.datatp.module.resource", "net.datatp.module.hr", "net.datatp.module.company.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.core.print", "net.datatp.module.chatbot", "cloud.datatp.module.odoo", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-09-18T13:37:58.993+07:00  INFO 9253 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-18T13:37:58.999+07:00  INFO 9253 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-18T13:37:59.001+07:00  INFO 9253 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-18T13:37:59.027+07:00  INFO 9253 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-18T13:37:59.035+07:00  WARN 9253 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-18T13:38:01.099+07:00  INFO 9253 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-18T13:38:01.100+07:00  INFO 9253 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@2cc4685a] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-18T13:38:01.362+07:00  WARN 9253 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-18T13:38:01.362+07:00  WARN 9253 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-18T13:38:01.376+07:00  WARN 9253 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-18T13:38:01.377+07:00  WARN 9253 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-18T13:38:01.389+07:00  WARN 9253 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-18T13:38:01.389+07:00  WARN 9253 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-18T13:38:01.900+07:00  INFO 9253 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-18T13:38:01.906+07:00  INFO 9253 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-18T13:38:01.907+07:00  INFO 9253 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-18T13:38:01.927+07:00  INFO 9253 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-18T13:38:01.931+07:00  WARN 9253 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-18T13:38:02.386+07:00  INFO 9253 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-18T13:38:02.386+07:00  INFO 9253 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@54aa5403] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-18T13:38:02.479+07:00  WARN 9253 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-18T13:38:02.479+07:00  WARN 9253 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-18T13:38:02.822+07:00  INFO 9253 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-18T13:38:02.852+07:00  INFO 9253 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-18T13:38:02.856+07:00  INFO 9253 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-18T13:38:02.856+07:00  INFO 9253 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T13:38:02.862+07:00  WARN 9253 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-18T13:38:02.994+07:00  INFO 9253 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-18T13:38:03.436+07:00  INFO 9253 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-18T13:38:03.439+07:00  INFO 9253 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-18T13:38:03.471+07:00  INFO 9253 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-18T13:38:03.516+07:00  INFO 9253 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-18T13:38:03.638+07:00  INFO 9253 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-18T13:38:03.665+07:00  INFO 9253 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-18T13:38:03.687+07:00  INFO 9253 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 239079172ms : this is harmless.
2025-09-18T13:38:03.696+07:00  INFO 9253 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-18T13:38:03.699+07:00  INFO 9253 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-18T13:38:03.723+07:00  INFO 9253 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 530585139ms : this is harmless.
2025-09-18T13:38:03.725+07:00  INFO 9253 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-18T13:38:03.737+07:00  INFO 9253 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-18T13:38:03.738+07:00  INFO 9253 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-18T13:38:05.805+07:00  INFO 9253 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-18T13:38:05.805+07:00  INFO 9253 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T13:38:05.807+07:00  WARN 9253 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-18T13:38:06.140+07:00  INFO 9253 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 18/09/2025@13:30:00+0700 to 18/09/2025@13:45:00+0700
2025-09-18T13:38:06.140+07:00  INFO 9253 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 18/09/2025@13:30:00+0700 to 18/09/2025@13:45:00+0700
2025-09-18T13:38:06.712+07:00  INFO 9253 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-09-18T13:38:06.712+07:00  INFO 9253 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T13:38:06.713+07:00  WARN 9253 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-18T13:38:07.055+07:00  INFO 9253 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-09-18T13:38:07.055+07:00  INFO 9253 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-09-18T13:38:07.055+07:00  INFO 9253 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-09-18T13:38:07.055+07:00  INFO 9253 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-09-18T13:38:07.055+07:00  INFO 9253 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-09-18T13:38:09.085+07:00  WARN 9253 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 28e21c82-0b40-44cb-96d0-12a024040c04

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-18T13:38:09.089+07:00  INFO 9253 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-09-18T13:38:09.411+07:00  INFO 9253 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-18T13:38:09.411+07:00  INFO 9253 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-18T13:38:09.411+07:00  INFO 9253 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-18T13:38:09.411+07:00  INFO 9253 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-18T13:38:09.411+07:00  INFO 9253 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-09-18T13:38:09.411+07:00  INFO 9253 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-09-18T13:38:09.411+07:00  INFO 9253 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-18T13:38:09.411+07:00  INFO 9253 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-09-18T13:38:09.411+07:00  INFO 9253 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-09-18T13:38:09.412+07:00  INFO 9253 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-18T13:38:09.412+07:00  INFO 9253 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-09-18T13:38:09.412+07:00  INFO 9253 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-09-18T13:38:09.412+07:00  INFO 9253 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-18T13:38:09.412+07:00  INFO 9253 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-18T13:38:09.412+07:00  INFO 9253 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-18T13:38:09.483+07:00  INFO 9253 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-18T13:38:09.483+07:00  INFO 9253 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-18T13:38:09.484+07:00  INFO 9253 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-09-18T13:38:09.492+07:00  INFO 9253 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@d90fb97{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-18T13:38:09.493+07:00  INFO 9253 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-09-18T13:38:09.494+07:00  INFO 9253 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-09-18T13:38:09.531+07:00  INFO 9253 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-09-18T13:38:09.531+07:00  INFO 9253 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-09-18T13:38:09.537+07:00  INFO 9253 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 15.692 seconds (process running for 16.177)
2025-09-18T13:39:05.519+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T13:39:12.535+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T13:39:12.539+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T13:40:06.623+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T13:40:06.627+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T13:40:09.937+07:00  INFO 9253 --- [qtp241358679-36] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0vhfuf64v8a511bpbxs8jh4mmk0
2025-09-18T13:40:10.304+07:00  INFO 9253 --- [qtp241358679-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0vhfuf64v8a511bpbxs8jh4mmk0, token = b6dcbae30a8ca4593b57a98cdfe0663d
2025-09-18T13:40:10.706+07:00  INFO 9253 --- [qtp241358679-36] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T13:40:13.680+07:00  INFO 9253 --- [qtp241358679-60] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T13:40:13.696+07:00  INFO 9253 --- [qtp241358679-62] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T13:40:13.799+07:00  INFO 9253 --- [qtp241358679-60] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T13:40:13.800+07:00  INFO 9253 --- [qtp241358679-62] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T13:41:04.725+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T13:41:11.810+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 10, expire count 0
2025-09-18T13:41:11.837+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-18T13:42:06.926+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T13:43:04.008+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T13:43:16.046+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T13:43:16.058+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T13:44:06.137+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T13:45:03.233+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T13:45:03.235+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T13:45:03.236+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-18T13:45:03.241+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 18/09/2025@13:45:03+0700
2025-09-18T13:45:03.252+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 18/09/2025@13:45:00+0700 to 18/09/2025@14:00:00+0700
2025-09-18T13:45:03.252+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 18/09/2025@13:45:00+0700 to 18/09/2025@14:00:00+0700
2025-09-18T13:45:16.288+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-18T13:45:16.295+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T13:46:06.361+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T13:46:23.909+07:00  INFO 9253 --- [qtp241358679-35] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0f2c84yzusp7t7ujas420pud31
2025-09-18T13:46:24.157+07:00  INFO 9253 --- [qtp241358679-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0f2c84yzusp7t7ujas420pud31, token = 321d53f485ab70a1da740dddc7e95b2c
2025-09-18T13:46:24.166+07:00  INFO 9253 --- [qtp241358679-35] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T13:46:26.804+07:00  INFO 9253 --- [qtp241358679-39] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T13:46:26.804+07:00  INFO 9253 --- [qtp241358679-41] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T13:46:27.039+07:00  INFO 9253 --- [qtp241358679-39] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-18T13:46:27.039+07:00  INFO 9253 --- [qtp241358679-41] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-18T13:47:02.458+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T13:47:16.525+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 16, expire count 0
2025-09-18T13:47:16.551+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-18T13:48:05.665+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T13:49:06.754+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T13:49:15.778+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T13:49:15.783+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T13:50:04.856+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T13:50:04.856+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T13:50:31.325+07:00  INFO 9253 --- [qtp241358679-60] c.d.f.core.partner.PartnerRequestLogic   : ------------------------Partner---------------------------

2025-09-18T13:50:31.330+07:00  INFO 9253 --- [qtp241358679-60] c.d.f.core.partner.PartnerRequestLogic   : --------------------------------------------------------

2025-09-18T13:50:31.362+07:00  INFO 9253 --- [qtp241358679-60] c.d.f.core.message.CRMMessageLogic       : 📅 Scheduled message [id=22080] [company=bee] type=MAIL for 18/09/2025@13:50:31+0700
2025-09-18T13:50:31.363+07:00  INFO 9253 --- [qtp241358679-60] c.d.f.core.message.MessageQueueManager   : Added message [22080] - scheduled at 18/09/2025@13:50:31+0700 - current session (18/09/2025@13:45:00+0700 to 18/09/2025@14:00:00+0700)

2025-09-18T13:51:06.963+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T13:51:15.005+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 7
2025-09-18T13:51:15.014+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T13:51:34.900+07:00  INFO 9253 --- [qtp241358679-41] c.d.f.core.partner.CRMPartnerLogic       : --- Create account: bfsone code = CS000055_TEMP
2025-09-18T13:52:04.102+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T13:53:06.196+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T13:53:14.233+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 0
2025-09-18T13:53:14.246+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T13:54:03.322+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T13:55:06.420+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T13:55:06.422+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T13:55:11.233+07:00  INFO 9253 --- [qtp241358679-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0vhfuf64v8a511bpbxs8jh4mmk0, token = b6dcbae30a8ca4593b57a98cdfe0663d
2025-09-18T13:55:11.234+07:00  INFO 9253 --- [qtp241358679-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0vhfuf64v8a511bpbxs8jh4mmk0, token = b6dcbae30a8ca4593b57a98cdfe0663d
2025-09-18T13:55:11.249+07:00  INFO 9253 --- [qtp241358679-69] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T13:55:11.249+07:00  INFO 9253 --- [qtp241358679-60] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T13:55:11.979+07:00  INFO 9253 --- [qtp241358679-65] n.d.module.session.ClientSessionManager  : Add a client session id = node0f2c84yzusp7t7ujas420pud31, token = 321d53f485ab70a1da740dddc7e95b2c
2025-09-18T13:55:11.980+07:00  INFO 9253 --- [qtp241358679-39] n.d.module.session.ClientSessionManager  : Add a client session id = node0f2c84yzusp7t7ujas420pud31, token = 321d53f485ab70a1da740dddc7e95b2c
2025-09-18T13:55:11.984+07:00  INFO 9253 --- [qtp241358679-39] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T13:55:11.984+07:00  INFO 9253 --- [qtp241358679-65] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T13:55:13.476+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 11, expire count 1
2025-09-18T13:55:13.486+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-18T13:56:02.569+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T13:56:29.799+07:00  INFO 9253 --- [qtp241358679-65] n.d.module.session.ClientSessionManager  : Add a client session id = node0f2c84yzusp7t7ujas420pud31, token = 321d53f485ab70a1da740dddc7e95b2c
2025-09-18T13:56:29.800+07:00  INFO 9253 --- [qtp241358679-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0f2c84yzusp7t7ujas420pud31, token = 321d53f485ab70a1da740dddc7e95b2c
2025-09-18T13:56:30.048+07:00  INFO 9253 --- [qtp241358679-60] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T13:56:30.051+07:00  INFO 9253 --- [qtp241358679-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0vhfuf64v8a511bpbxs8jh4mmk0, token = b6dcbae30a8ca4593b57a98cdfe0663d
2025-09-18T13:56:30.051+07:00  INFO 9253 --- [qtp241358679-65] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T13:56:30.059+07:00  INFO 9253 --- [qtp241358679-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0vhfuf64v8a511bpbxs8jh4mmk0, token = b6dcbae30a8ca4593b57a98cdfe0663d
2025-09-18T13:56:30.064+07:00  INFO 9253 --- [qtp241358679-69] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T13:56:30.082+07:00  INFO 9253 --- [qtp241358679-36] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T13:57:05.689+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T13:57:12.753+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 8
2025-09-18T13:57:12.771+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T13:58:06.848+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T13:58:26.530+07:00  INFO 9253 --- [qtp241358679-65] n.d.module.session.ClientSessionManager  : Add a client session id = node0f2c84yzusp7t7ujas420pud31, token = 321d53f485ab70a1da740dddc7e95b2c
2025-09-18T13:58:26.613+07:00  INFO 9253 --- [qtp241358679-65] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T13:58:26.621+07:00  INFO 9253 --- [qtp241358679-104] n.d.module.session.ClientSessionManager  : Add a client session id = node0f2c84yzusp7t7ujas420pud31, token = 321d53f485ab70a1da740dddc7e95b2c
2025-09-18T13:58:26.628+07:00  INFO 9253 --- [qtp241358679-104] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T13:58:26.955+07:00  INFO 9253 --- [qtp241358679-39] n.d.module.session.ClientSessionManager  : Add a client session id = node0vhfuf64v8a511bpbxs8jh4mmk0, token = b6dcbae30a8ca4593b57a98cdfe0663d
2025-09-18T13:58:26.969+07:00  INFO 9253 --- [qtp241358679-39] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T13:58:26.998+07:00  INFO 9253 --- [qtp241358679-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0vhfuf64v8a511bpbxs8jh4mmk0, token = b6dcbae30a8ca4593b57a98cdfe0663d
2025-09-18T13:58:27.010+07:00  INFO 9253 --- [qtp241358679-37] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T13:59:04.949+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T13:59:11.997+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 8, expire count 0
2025-09-18T13:59:12.019+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-18T14:00:06.104+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:00:06.108+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T14:00:06.109+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 18/09/2025@14:00:06+0700
2025-09-18T14:00:06.142+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 18/09/2025@14:00:00+0700 to 18/09/2025@14:15:00+0700
2025-09-18T14:00:06.142+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 18/09/2025@14:00:00+0700 to 18/09/2025@14:15:00+0700
2025-09-18T14:00:06.142+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-18T14:00:06.145+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at 14 PM every day
2025-09-18T14:00:06.145+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-09-18T14:01:04.224+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:01:08.600+07:00  INFO 9253 --- [qtp241358679-104] n.d.module.session.ClientSessionManager  : Add a client session id = node0f2c84yzusp7t7ujas420pud31, token = 321d53f485ab70a1da740dddc7e95b2c
2025-09-18T14:01:08.610+07:00  INFO 9253 --- [qtp241358679-104] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T14:01:08.619+07:00  INFO 9253 --- [qtp241358679-71] n.d.module.session.ClientSessionManager  : Add a client session id = node0f2c84yzusp7t7ujas420pud31, token = 321d53f485ab70a1da740dddc7e95b2c
2025-09-18T14:01:08.625+07:00  INFO 9253 --- [qtp241358679-71] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T14:01:08.947+07:00  INFO 9253 --- [qtp241358679-39] n.d.module.session.ClientSessionManager  : Add a client session id = node0vhfuf64v8a511bpbxs8jh4mmk0, token = b6dcbae30a8ca4593b57a98cdfe0663d
2025-09-18T14:01:08.952+07:00  INFO 9253 --- [qtp241358679-39] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T14:01:08.976+07:00  INFO 9253 --- [qtp241358679-105] n.d.module.session.ClientSessionManager  : Add a client session id = node0vhfuf64v8a511bpbxs8jh4mmk0, token = b6dcbae30a8ca4593b57a98cdfe0663d
2025-09-18T14:01:08.979+07:00  INFO 9253 --- [qtp241358679-105] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T14:01:16.267+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 12, expire count 3
2025-09-18T14:01:16.297+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-18T14:02:06.403+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:03:03.477+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:03:16.514+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 8
2025-09-18T14:03:16.524+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T14:03:34.338+07:00  INFO 9253 --- [qtp241358679-104] c.d.f.core.message.CRMMessageLogic       : 📅 Scheduled message [id=22081] [company=bee] type=MAIL for 18/09/2025@14:03:34+0700
2025-09-18T14:03:34.339+07:00  INFO 9253 --- [qtp241358679-104] c.d.f.core.message.MessageQueueManager   : Added message [22081] - scheduled at 18/09/2025@14:03:34+0700 - current session (18/09/2025@14:00:00+0700 to 18/09/2025@14:15:00+0700)

2025-09-18T14:04:06.610+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:04:11.615+07:00  INFO 9253 --- [qtp241358679-105] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T14:04:11.615+07:00  INFO 9253 --- [qtp241358679-137] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T14:04:11.943+07:00  INFO 9253 --- [qtp241358679-105] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T14:04:11.943+07:00  INFO 9253 --- [qtp241358679-137] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T14:04:50.633+07:00  INFO 9253 --- [qtp241358679-105] c.d.f.core.partner.PartnerRequestLogic   : ------------------------Partner---------------------------

2025-09-18T14:04:51.547+07:00  INFO 9253 --- [qtp241358679-105] c.d.f.core.partner.PartnerRequestLogic   : --------------------------------------------------------

2025-09-18T14:04:51.618+07:00  INFO 9253 --- [qtp241358679-105] c.d.f.core.message.CRMMessageLogic       : 📅 Scheduled message [id=22082] [company=bee] type=MAIL for 18/09/2025@14:04:51+0700
2025-09-18T14:04:51.619+07:00  INFO 9253 --- [qtp241358679-105] c.d.f.core.message.MessageQueueManager   : Added message [22082] - scheduled at 18/09/2025@14:04:51+0700 - current session (18/09/2025@14:00:00+0700 to 18/09/2025@14:15:00+0700)

2025-09-18T14:05:02.709+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:05:02.712+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T14:05:15.771+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 11, expire count 1
2025-09-18T14:05:15.784+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T14:06:05.858+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:06:50.375+07:00  INFO 9253 --- [qtp241358679-65] n.d.module.session.ClientSessionManager  : Add a client session id = node0f2c84yzusp7t7ujas420pud31, token = 321d53f485ab70a1da740dddc7e95b2c
2025-09-18T14:06:50.386+07:00  INFO 9253 --- [qtp241358679-65] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T14:06:50.393+07:00  INFO 9253 --- [qtp241358679-137] n.d.module.session.ClientSessionManager  : Add a client session id = node0f2c84yzusp7t7ujas420pud31, token = 321d53f485ab70a1da740dddc7e95b2c
2025-09-18T14:06:50.400+07:00  INFO 9253 --- [qtp241358679-137] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T14:06:50.964+07:00  INFO 9253 --- [qtp241358679-105] n.d.module.session.ClientSessionManager  : Add a client session id = node0vhfuf64v8a511bpbxs8jh4mmk0, token = b6dcbae30a8ca4593b57a98cdfe0663d
2025-09-18T14:06:50.979+07:00  INFO 9253 --- [qtp241358679-105] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T14:06:51.030+07:00  INFO 9253 --- [qtp241358679-106] n.d.module.session.ClientSessionManager  : Add a client session id = node0vhfuf64v8a511bpbxs8jh4mmk0, token = b6dcbae30a8ca4593b57a98cdfe0663d
2025-09-18T14:06:51.045+07:00  INFO 9253 --- [qtp241358679-106] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T14:06:51.125+07:00 ERROR 9253 --- [qtp241358679-65] n.d.m.monitor.call.EndpointCallContext   : Start call with component EntityTaskService, method searchEntityTasks, arguments
[ {
  "tenantId" : "default",
  "companyId" : 8,
  "companyParentId" : 4,
  "companyCode" : "beehph",
  "companyLabel" : "Bee HPH",
  "companyFullName" : "BEE LOGISTICS CORPORATION - HAI PHONG BRANCH OFFICE",
  "loginId" : "kai.vnhph",
  "accountId" : 1994,
  "token" : "b6dcbae30a8ca4593b57a98cdfe0663d",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node0vhfuf64v8a511bpbxs8jh4mmk0",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : {
    "beehph" : {
      "companyId" : 8,
      "companyParentId" : 4,
      "companyCode" : "beehph",
      "companyLabel" : "Bee HPH",
      "companyFullName" : null
    }
  },
  "featurePermissions" : [ {
    "id" : 14630,
    "appId" : 3,
    "appModule" : "project",
    "appName" : "project",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 2116,
    "appId" : 26,
    "appModule" : "okr",
    "appName" : "okr",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 12452,
    "appId" : 66,
    "appModule" : "tms",
    "appName" : "tms-bill-company",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Read"
  }, {
    "id" : 11672,
    "appId" : 69,
    "appModule" : "hr",
    "appName" : "user-kpi",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 13280,
    "appId" : 79,
    "appModule" : "company",
    "appName" : "user-asset",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 8564,
    "appId" : 16,
    "appModule" : "logistics",
    "appName" : "user-logistics-sales",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 8562,
    "appId" : 2,
    "appModule" : "user",
    "appName" : "my-employee-space",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 18464,
    "appId" : 88,
    "appModule" : "user",
    "appName" : "my-space",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  } ],
  "attributes" : { },
  "clientId" : "default:kai.vnhph"
}, null, {
  "params" : {
    "accountId" : 1994
  },
  "filters" : [ {
    "name" : "search",
    "filterType" : "String",
    "filterValue" : "",
    "required" : true
  } ],
  "optionFilters" : [ {
    "name" : "storageState",
    "filterType" : "NotSet",
    "required" : true,
    "multiple" : true,
    "options" : [ "ACTIVE", "ARCHIVED" ],
    "selectOptions" : [ "ACTIVE" ]
  } ],
  "rangeFilters" : [ {
    "name" : "createdTime",
    "filterType" : "NotSet",
    "required" : true
  }, {
    "name" : "modifiedTime",
    "filterType" : "NotSet",
    "required" : true
  } ],
  "orderBy" : {
    "fields" : [ "modifiedTime" ],
    "selectFields" : [ ],
    "sort" : "DESC"
  },
  "maxReturn" : 5000
} ]
2025-09-18T14:06:51.126+07:00 ERROR 9253 --- [qtp241358679-65] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:115)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Cannot invoke "net.datatp.module.data.db.entity.ICompany.getId()" because "company" is null
	at net.datatp.module.wfms.EntityTaskService.searchEntityTasks(EntityTaskService.java:34)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.wfms.EntityTaskService$$SpringCGLIB$$0.searchEntityTasks(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted

2025-09-18T14:06:51.134+07:00  INFO 9253 --- [qtp241358679-65] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint EntityTaskService/searchEntityTasks
2025-09-18T14:07:06.957+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:07:14.865+07:00  INFO 9253 --- [qtp241358679-137] n.d.module.session.ClientSessionManager  : Add a client session id = node0f2c84yzusp7t7ujas420pud31, token = 321d53f485ab70a1da740dddc7e95b2c
2025-09-18T14:07:14.915+07:00  INFO 9253 --- [qtp241358679-137] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T14:07:14.953+07:00  INFO 9253 --- [qtp241358679-109] n.d.module.session.ClientSessionManager  : Add a client session id = node0f2c84yzusp7t7ujas420pud31, token = 321d53f485ab70a1da740dddc7e95b2c
2025-09-18T14:07:14.969+07:00  INFO 9253 --- [qtp241358679-109] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T14:07:15.062+07:00  INFO 9253 --- [qtp241358679-141] n.d.module.session.ClientSessionManager  : Add a client session id = node0vhfuf64v8a511bpbxs8jh4mmk0, token = b6dcbae30a8ca4593b57a98cdfe0663d
2025-09-18T14:07:15.072+07:00  INFO 9253 --- [qtp241358679-72] n.d.module.session.ClientSessionManager  : Add a client session id = node0vhfuf64v8a511bpbxs8jh4mmk0, token = b6dcbae30a8ca4593b57a98cdfe0663d
2025-09-18T14:07:15.084+07:00  INFO 9253 --- [qtp241358679-141] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T14:07:15.090+07:00  INFO 9253 --- [qtp241358679-72] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T14:07:16.116+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 10, expire count 0
2025-09-18T14:07:16.136+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T14:08:05.232+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:08:14.185+07:00  INFO 9253 --- [qtp241358679-104] n.d.module.session.ClientSessionManager  : Add a client session id = node0f2c84yzusp7t7ujas420pud31, token = 321d53f485ab70a1da740dddc7e95b2c
2025-09-18T14:08:14.196+07:00  INFO 9253 --- [qtp241358679-104] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T14:08:14.207+07:00  INFO 9253 --- [qtp241358679-145] n.d.module.session.ClientSessionManager  : Add a client session id = node0f2c84yzusp7t7ujas420pud31, token = 321d53f485ab70a1da740dddc7e95b2c
2025-09-18T14:08:14.212+07:00  INFO 9253 --- [qtp241358679-145] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T14:08:14.962+07:00  INFO 9253 --- [qtp241358679-141] n.d.module.session.ClientSessionManager  : Add a client session id = node0vhfuf64v8a511bpbxs8jh4mmk0, token = b6dcbae30a8ca4593b57a98cdfe0663d
2025-09-18T14:08:14.971+07:00  INFO 9253 --- [qtp241358679-141] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T14:08:14.993+07:00  INFO 9253 --- [qtp241358679-114] n.d.module.session.ClientSessionManager  : Add a client session id = node0vhfuf64v8a511bpbxs8jh4mmk0, token = b6dcbae30a8ca4593b57a98cdfe0663d
2025-09-18T14:08:14.998+07:00  INFO 9253 --- [qtp241358679-114] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T14:09:06.348+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:09:15.384+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-18T14:09:15.395+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T14:10:04.476+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:10:04.479+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T14:11:06.570+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:11:14.650+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 8, expire count 4
2025-09-18T14:11:14.662+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T14:11:41.879+07:00  INFO 9253 --- [qtp241358679-105] n.d.module.session.ClientSessionManager  : Add a client session id = node0f2c84yzusp7t7ujas420pud31, token = 321d53f485ab70a1da740dddc7e95b2c
2025-09-18T14:11:41.881+07:00  INFO 9253 --- [qtp241358679-106] n.d.module.session.ClientSessionManager  : Add a client session id = node0f2c84yzusp7t7ujas420pud31, token = 321d53f485ab70a1da740dddc7e95b2c
2025-09-18T14:11:41.962+07:00  INFO 9253 --- [qtp241358679-137] n.d.module.session.ClientSessionManager  : Add a client session id = node0vhfuf64v8a511bpbxs8jh4mmk0, token = b6dcbae30a8ca4593b57a98cdfe0663d
2025-09-18T14:11:41.964+07:00  INFO 9253 --- [qtp241358679-145] n.d.module.session.ClientSessionManager  : Add a client session id = node0vhfuf64v8a511bpbxs8jh4mmk0, token = b6dcbae30a8ca4593b57a98cdfe0663d
2025-09-18T14:11:41.987+07:00  INFO 9253 --- [qtp241358679-105] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T14:11:42.053+07:00  INFO 9253 --- [qtp241358679-145] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T14:11:42.140+07:00  INFO 9253 --- [qtp241358679-137] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T14:11:42.174+07:00  INFO 9253 --- [qtp241358679-106] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T14:11:47.782+07:00  INFO 9253 --- [qtp241358679-106] n.d.module.session.ClientSessionManager  : Add a client session id = node0f2c84yzusp7t7ujas420pud31, token = 321d53f485ab70a1da740dddc7e95b2c
2025-09-18T14:11:47.784+07:00  INFO 9253 --- [qtp241358679-109] n.d.module.session.ClientSessionManager  : Add a client session id = node0f2c84yzusp7t7ujas420pud31, token = 321d53f485ab70a1da740dddc7e95b2c
2025-09-18T14:11:47.789+07:00  INFO 9253 --- [qtp241358679-106] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T14:11:47.790+07:00  INFO 9253 --- [qtp241358679-109] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T14:11:47.961+07:00  INFO 9253 --- [qtp241358679-104] n.d.module.session.ClientSessionManager  : Add a client session id = node0vhfuf64v8a511bpbxs8jh4mmk0, token = b6dcbae30a8ca4593b57a98cdfe0663d
2025-09-18T14:11:47.968+07:00  INFO 9253 --- [qtp241358679-104] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T14:11:47.983+07:00  INFO 9253 --- [qtp241358679-147] n.d.module.session.ClientSessionManager  : Add a client session id = node0vhfuf64v8a511bpbxs8jh4mmk0, token = b6dcbae30a8ca4593b57a98cdfe0663d
2025-09-18T14:11:48.012+07:00  INFO 9253 --- [qtp241358679-147] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T14:11:59.106+07:00  INFO 9253 --- [qtp241358679-145] n.d.module.session.ClientSessionManager  : Add a client session id = node0f2c84yzusp7t7ujas420pud31, token = 321d53f485ab70a1da740dddc7e95b2c
2025-09-18T14:11:59.113+07:00  INFO 9253 --- [qtp241358679-145] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T14:11:59.125+07:00  INFO 9253 --- [qtp241358679-113] n.d.module.session.ClientSessionManager  : Add a client session id = node0f2c84yzusp7t7ujas420pud31, token = 321d53f485ab70a1da740dddc7e95b2c
2025-09-18T14:11:59.128+07:00  INFO 9253 --- [qtp241358679-113] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T14:11:59.962+07:00  INFO 9253 --- [qtp241358679-137] n.d.module.session.ClientSessionManager  : Add a client session id = node0vhfuf64v8a511bpbxs8jh4mmk0, token = b6dcbae30a8ca4593b57a98cdfe0663d
2025-09-18T14:11:59.965+07:00  INFO 9253 --- [qtp241358679-105] n.d.module.session.ClientSessionManager  : Add a client session id = node0vhfuf64v8a511bpbxs8jh4mmk0, token = b6dcbae30a8ca4593b57a98cdfe0663d
2025-09-18T14:11:59.966+07:00  INFO 9253 --- [qtp241358679-137] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T14:11:59.974+07:00  INFO 9253 --- [qtp241358679-105] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T14:12:03.766+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:13:06.862+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:13:13.884+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T14:13:13.890+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T14:14:02.974+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:15:06.064+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:15:06.067+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T14:15:06.067+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-18T14:15:06.068+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 18/09/2025@14:15:06+0700
2025-09-18T14:15:06.106+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 18/09/2025@14:15:00+0700 to 18/09/2025@14:30:00+0700
2025-09-18T14:15:06.106+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 18/09/2025@14:15:00+0700 to 18/09/2025@14:30:00+0700
2025-09-18T14:15:13.121+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 9, expire count 11
2025-09-18T14:15:13.125+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T14:16:02.197+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:17:05.312+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:17:12.327+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 2
2025-09-18T14:17:12.335+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T14:17:57.366+07:00  INFO 9253 --- [Scheduler-769336846-1] n.d.m.session.AppHttpSessionListener     : The session node0f2c84yzusp7t7ujas420pud31 is destroyed.
2025-09-18T14:18:06.412+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:19:04.529+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:19:16.575+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T14:19:16.585+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T14:20:06.658+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:20:06.660+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T14:21:03.728+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:21:15.744+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T14:21:15.748+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T14:22:06.845+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:23:02.949+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:23:15.988+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 7
2025-09-18T14:23:16.003+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T14:24:06.092+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:25:02.192+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:25:02.194+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T14:25:16.242+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 1
2025-09-18T14:25:16.250+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T14:26:05.331+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:27:06.426+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:27:15.456+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T14:27:15.462+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T14:28:04.538+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:29:06.651+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:29:14.694+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T14:29:14.707+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T14:30:03.790+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:30:03.793+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T14:30:03.793+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-18T14:30:03.795+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 18/09/2025@14:30:03+0700
2025-09-18T14:30:03.825+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 18/09/2025@14:30:00+0700 to 18/09/2025@14:45:00+0700
2025-09-18T14:30:03.826+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 18/09/2025@14:30:00+0700 to 18/09/2025@14:45:00+0700
2025-09-18T14:31:06.938+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:31:13.973+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T14:31:13.980+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T14:32:03.071+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:33:06.192+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:33:13.243+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-18T14:33:13.255+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T14:34:02.341+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:35:05.449+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:35:05.451+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T14:35:12.467+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T14:35:12.472+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T14:36:06.561+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:37:04.672+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:37:11.717+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T14:37:11.735+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T14:38:06.809+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:39:03.904+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:39:15.957+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T14:39:15.975+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T14:40:06.071+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:40:06.072+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T14:41:03.176+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:41:16.231+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-18T14:41:16.236+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T14:42:06.330+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:43:02.426+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:43:16.460+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T14:43:16.472+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T14:44:05.550+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:45:06.641+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:45:06.643+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T14:45:06.643+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 18/09/2025@14:45:06+0700
2025-09-18T14:45:06.667+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 18/09/2025@14:45:00+0700 to 18/09/2025@15:00:00+0700
2025-09-18T14:45:06.667+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 18/09/2025@14:45:00+0700 to 18/09/2025@15:00:00+0700
2025-09-18T14:45:06.668+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-18T14:45:15.705+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T14:45:15.712+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T14:46:04.809+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:47:06.901+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:47:14.938+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T14:47:14.954+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T14:48:04.032+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:49:06.135+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:49:14.155+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T14:49:14.162+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T14:49:35.483+07:00  INFO 9253 --- [qtp241358679-285] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T14:49:35.497+07:00  INFO 9253 --- [qtp241358679-340] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T14:49:35.916+07:00  INFO 9253 --- [qtp241358679-285] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T14:49:35.916+07:00  INFO 9253 --- [qtp241358679-340] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T14:50:03.232+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T14:50:03.233+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:51:06.330+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:51:13.365+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 8, expire count 0
2025-09-18T14:51:13.383+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-18T14:52:02.454+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:53:05.588+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:53:12.607+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T14:53:12.611+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T14:54:06.684+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:55:04.787+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:55:04.789+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T14:55:11.830+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 1
2025-09-18T14:55:11.841+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T14:56:06.919+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:57:04.009+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:57:16.074+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T14:57:16.084+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T14:57:22.027+07:00  INFO 9253 --- [qtp241358679-106] n.d.module.session.ClientSessionManager  : Add a client session id = node0vhfuf64v8a511bpbxs8jh4mmk0, token = b6dcbae30a8ca4593b57a98cdfe0663d
2025-09-18T14:57:22.052+07:00  INFO 9253 --- [qtp241358679-341] n.d.module.session.ClientSessionManager  : Add a client session id = node0vhfuf64v8a511bpbxs8jh4mmk0, token = b6dcbae30a8ca4593b57a98cdfe0663d
2025-09-18T14:57:22.146+07:00  INFO 9253 --- [qtp241358679-341] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T14:57:22.153+07:00  INFO 9253 --- [qtp241358679-106] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T14:57:38.091+07:00  INFO 9253 --- [qtp241358679-109] n.d.module.session.ClientSessionManager  : Add a client session id = node0vhfuf64v8a511bpbxs8jh4mmk0, token = b6dcbae30a8ca4593b57a98cdfe0663d
2025-09-18T14:57:38.098+07:00  INFO 9253 --- [qtp241358679-109] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T14:57:38.124+07:00  INFO 9253 --- [qtp241358679-387] n.d.module.session.ClientSessionManager  : Add a client session id = node0vhfuf64v8a511bpbxs8jh4mmk0, token = b6dcbae30a8ca4593b57a98cdfe0663d
2025-09-18T14:57:38.130+07:00  INFO 9253 --- [qtp241358679-387] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T14:58:06.173+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:58:22.086+07:00  INFO 9253 --- [qtp241358679-342] n.d.module.session.ClientSessionManager  : Add a client session id = node0vhfuf64v8a511bpbxs8jh4mmk0, token = b6dcbae30a8ca4593b57a98cdfe0663d
2025-09-18T14:58:22.087+07:00  INFO 9253 --- [qtp241358679-391] n.d.module.session.ClientSessionManager  : Add a client session id = node0vhfuf64v8a511bpbxs8jh4mmk0, token = b6dcbae30a8ca4593b57a98cdfe0663d
2025-09-18T14:58:22.104+07:00  INFO 9253 --- [qtp241358679-391] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T14:58:22.104+07:00  INFO 9253 --- [qtp241358679-342] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T14:58:55.036+07:00  INFO 9253 --- [qtp241358679-341] n.d.module.session.ClientSessionManager  : Add a client session id = node0vhfuf64v8a511bpbxs8jh4mmk0, token = b6dcbae30a8ca4593b57a98cdfe0663d
2025-09-18T14:58:55.047+07:00  INFO 9253 --- [qtp241358679-341] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T14:58:55.064+07:00  INFO 9253 --- [qtp241358679-344] n.d.module.session.ClientSessionManager  : Add a client session id = node0vhfuf64v8a511bpbxs8jh4mmk0, token = b6dcbae30a8ca4593b57a98cdfe0663d
2025-09-18T14:58:55.080+07:00  INFO 9253 --- [qtp241358679-344] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T14:59:03.288+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T14:59:16.339+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-18T14:59:16.351+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T15:00:06.447+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-18T15:00:06.450+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 18/09/2025@15:00:06+0700
2025-09-18T15:00:06.476+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 18/09/2025@15:00:00+0700 to 18/09/2025@15:15:00+0700
2025-09-18T15:00:06.476+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 18/09/2025@15:00:00+0700 to 18/09/2025@15:15:00+0700
2025-09-18T15:00:06.477+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:00:06.477+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T15:00:06.477+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-09-18T15:00:06.483+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at 15 PM every day
2025-09-18T15:00:06.483+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 3 hour
2025-09-18T15:01:02.575+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:01:16.615+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 6
2025-09-18T15:01:16.623+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T15:02:05.702+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:03:06.805+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:03:15.837+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-18T15:03:15.841+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T15:04:04.882+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:05:05.972+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T15:05:06.978+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:05:14.992+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T15:05:14.994+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T15:06:04.082+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:07:06.188+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:07:14.208+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T15:07:14.211+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T15:07:57.393+07:00  INFO 9253 --- [Scheduler-769336846-1] n.d.m.session.AppHttpSessionListener     : The session node0vhfuf64v8a511bpbxs8jh4mmk0 is destroyed.
2025-09-18T15:08:03.300+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:09:06.414+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:09:13.429+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 4
2025-09-18T15:09:13.434+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T15:10:02.518+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:10:02.520+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T15:11:05.628+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:11:12.643+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-18T15:11:12.653+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T15:12:06.737+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:13:04.843+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:13:11.854+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T15:13:11.856+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T15:14:06.940+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:15:04.040+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-18T15:15:04.045+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 18/09/2025@15:15:04+0700
2025-09-18T15:15:04.074+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 18/09/2025@15:15:00+0700 to 18/09/2025@15:30:00+0700
2025-09-18T15:15:04.074+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 18/09/2025@15:15:00+0700 to 18/09/2025@15:30:00+0700
2025-09-18T15:15:04.075+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:15:04.075+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T15:15:16.099+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T15:15:16.103+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T15:16:06.185+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:17:03.295+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:17:16.322+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T15:17:16.324+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T15:18:06.416+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:19:02.499+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:19:16.529+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T15:19:16.532+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T15:20:05.607+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:20:05.610+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T15:21:06.709+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:21:15.735+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T15:21:15.743+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T15:22:04.833+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:23:06.961+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:23:14.985+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T15:23:14.992+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T15:24:04.076+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:25:06.176+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:25:06.178+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T15:25:14.196+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-18T15:25:14.209+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T15:26:03.279+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:27:06.377+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:27:13.384+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T15:27:13.390+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T15:28:02.465+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:29:05.564+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:29:12.581+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T15:29:12.586+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T15:30:04.404+07:00  INFO 9253 --- [qtp241358679-391] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0spxt9vj65ql5kjm6nq1tu2pi2
2025-09-18T15:30:04.411+07:00  INFO 9253 --- [qtp241358679-109] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01psfzqj2990ja2l8wc87754vh3
2025-09-18T15:30:04.471+07:00 ERROR 9253 --- [qtp241358679-109] n.d.m.monitor.call.EndpointCallContext   : Start call with component CompanyAuthenticationService, method validate, arguments
[ {
  "tenantId" : "default",
  "companyId" : null,
  "companyParentId" : null,
  "companyCode" : null,
  "companyLabel" : null,
  "companyFullName" : null,
  "loginId" : "anon",
  "accountId" : 0,
  "token" : "anon",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node01psfzqj2990ja2l8wc87754vh3",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : null,
  "allowAccessCompanies" : null,
  "featurePermissions" : null,
  "attributes" : { },
  "clientId" : "default:anon"
}, null, {
  "loginId" : null,
  "authorization" : "MW36LHYH4BTT2J6UPSF46QHSABP3RHHWHJB6L55DTMOEV3HOHWMZG3FWTBGQFPI4LBT35AN6XQEFK4SGDET7XWNEDUHGWDFBSYTNF3I522ZNGJRFSNSPSU27IUJLGXNSIAWMUJOGEDWJLCRJO7XAV7UDZ3LHQMQOEXFHDQNQIM3KQEJS7R5STXDDPLJDCQLXOYF24ZBYIX6367HQ7QEGTK7ND2HTTUUVY7ANYEIFN2KQADSOSAY6QJNYFWZ46WGLJ7SN54Y3GWYZL62KA3XFBXMVPQXIH7AJQB2RJBVYBUB42NCY64DWB4MV6XNWEDNX4U6QFYVZWGJPXMIGZMQG4CQ4CXD3MP5BPTWRKI6W4A36IHSPFGSWXKL4MSTTJZJN2R442VTGDVIAWOKYNYLNPHSBYU======",
  "company" : "bee",
  "password" : null,
  "timeToLiveInMin" : 0,
  "accessType" : "Employee"
} ]
2025-09-18T15:30:04.471+07:00 ERROR 9253 --- [qtp241358679-391] n.d.m.monitor.call.EndpointCallContext   : Start call with component CompanyAuthenticationService, method validate, arguments
[ {
  "tenantId" : "default",
  "companyId" : null,
  "companyParentId" : null,
  "companyCode" : null,
  "companyLabel" : null,
  "companyFullName" : null,
  "loginId" : "anon",
  "accountId" : 0,
  "token" : "anon",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node0spxt9vj65ql5kjm6nq1tu2pi2",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : null,
  "allowAccessCompanies" : null,
  "featurePermissions" : null,
  "attributes" : { },
  "clientId" : "default:anon"
}, null, {
  "loginId" : null,
  "authorization" : "MW36LHYH4BTT2J6UPSF46QHSABP3RHHWHJB6L55DTMOEV3HOHWMZG3FWTBGQFPI4LBT35AN6XQEFK4SGDET7XWNEDUHGWDFBSYTNF3I522ZNGJRFSNSPSU27IUJLGXNSIAWMUJOGEDWJLCRJO7XAV7UDZ3LHQMQOEXFHDQNQIM3KQEJS7R5STXDDPLJDCQLXOYF24ZBYIX6367HQ7QEGTK7ND2HTTUUVY7ANYEIFN2KQADSOSAY6QJNYFWZ46WGLJ7SN54Y3GWYZL62KA3XFBXMVPQXIH7AJQB2RJBVYBUB42NCY64DWB4MV6XNWEDNX4U6QFYVZWGJPXMIGZMQG4CQ4CXD3MP5BPTWRKI6W4A36IHSPFGSWXKL4MSTTJZJN2R442VTGDVIAWOKYNYLNPHSBYU======",
  "company" : "bee",
  "password" : null,
  "timeToLiveInMin" : 0,
  "accessType" : "Employee"
} ]
2025-09-18T15:30:04.472+07:00 ERROR 9253 --- [qtp241358679-109] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:118)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.authenticateCall(RPCController.java:61)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.RuntimeException: java.lang.NullPointerException: Cannot invoke "java.lang.Long.longValue()" because the return value of "net.datatp.module.core.security.AuthorizationInfo.getAccountId()" is null
	at net.datatp.module.company.auth.CompanyAuthenticationService.validate(CompanyAuthenticationService.java:110)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.company.auth.CompanyAuthenticationService$$SpringCGLIB$$0.validate(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted
Caused by: java.lang.NullPointerException: Cannot invoke "java.lang.Long.longValue()" because the return value of "net.datatp.module.core.security.AuthorizationInfo.getAccountId()" is null
	at net.datatp.module.company.auth.CompanyAuthenticationService.doLogin(CompanyAuthenticationService.java:145)
	at net.datatp.module.company.auth.CompanyAuthenticationService.validate(CompanyAuthenticationService.java:108)
	... 109 common frames omitted

2025-09-18T15:30:04.472+07:00 ERROR 9253 --- [qtp241358679-391] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:118)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.authenticateCall(RPCController.java:61)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.RuntimeException: java.lang.NullPointerException: Cannot invoke "java.lang.Long.longValue()" because the return value of "net.datatp.module.core.security.AuthorizationInfo.getAccountId()" is null
	at net.datatp.module.company.auth.CompanyAuthenticationService.validate(CompanyAuthenticationService.java:110)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.company.auth.CompanyAuthenticationService$$SpringCGLIB$$0.validate(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted
Caused by: java.lang.NullPointerException: Cannot invoke "java.lang.Long.longValue()" because the return value of "net.datatp.module.core.security.AuthorizationInfo.getAccountId()" is null
	at net.datatp.module.company.auth.CompanyAuthenticationService.doLogin(CompanyAuthenticationService.java:145)
	at net.datatp.module.company.auth.CompanyAuthenticationService.validate(CompanyAuthenticationService.java:108)
	... 109 common frames omitted

2025-09-18T15:30:04.514+07:00  INFO 9253 --- [qtp241358679-109] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint CompanyAuthenticationService/validate
2025-09-18T15:30:04.521+07:00  INFO 9253 --- [qtp241358679-391] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint CompanyAuthenticationService/validate
2025-09-18T15:30:06.672+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:30:06.672+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T15:30:06.674+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 18/09/2025@15:30:06+0700
2025-09-18T15:30:06.690+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 18/09/2025@15:30:00+0700 to 18/09/2025@15:45:00+0700
2025-09-18T15:30:06.690+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 18/09/2025@15:30:00+0700 to 18/09/2025@15:45:00+0700
2025-09-18T15:30:06.691+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-18T15:31:04.784+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:31:11.826+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T15:31:11.833+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T15:32:06.916+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:33:03.995+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:33:16.033+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T15:33:16.036+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T15:34:06.131+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:35:03.222+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:35:03.225+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T15:35:16.253+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T15:35:16.274+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T15:36:06.354+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:37:02.459+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:37:16.483+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T15:37:16.489+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T15:37:57.459+07:00  INFO 9253 --- [Scheduler-769336846-1] n.d.m.session.AppHttpSessionListener     : The session node0spxt9vj65ql5kjm6nq1tu2pi2 is destroyed.
2025-09-18T15:37:57.461+07:00  INFO 9253 --- [Scheduler-769336846-1] n.d.m.session.AppHttpSessionListener     : The session node01psfzqj2990ja2l8wc87754vh3 is destroyed.
2025-09-18T15:38:05.551+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:39:06.654+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:39:15.679+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T15:39:15.687+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T15:39:40.929+07:00  INFO 9253 --- [qtp241358679-391] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0145r6ohqsyhpl467bakt4x4344
2025-09-18T15:39:41.158+07:00 ERROR 9253 --- [qtp241358679-391] net.datatp.module.account.AccountLogic   : User dan try to login into system, but fail
2025-09-18T15:39:41.161+07:00 ERROR 9253 --- [qtp241358679-391] n.d.m.monitor.call.EndpointCallContext   : Start call with component CompanyAuthenticationService, method authenticate, arguments
[ {
  "tenantId" : "",
  "companyId" : null,
  "companyParentId" : null,
  "companyCode" : null,
  "companyLabel" : null,
  "companyFullName" : null,
  "loginId" : "anon",
  "accountId" : 0,
  "token" : "anon",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node0145r6ohqsyhpl467bakt4x4344",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : null,
  "allowAccessCompanies" : null,
  "featurePermissions" : null,
  "attributes" : { },
  "clientId" : ":anon"
}, null, {
  "loginId" : "dan",
  "authorization" : null,
  "company" : "",
  "password" : "Dan!@#@@",
  "timeToLiveInMin" : 10080,
  "accessType" : "Employee"
} ]
2025-09-18T15:39:41.161+07:00 ERROR 9253 --- [qtp241358679-391] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:115)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.authenticateCall(RPCController.java:61)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Cannot invoke "java.lang.Long.longValue()" because the return value of "net.datatp.module.core.security.entity.AccessToken.getAccountId()" is null
	at net.datatp.module.company.auth.CompanyAuthenticationService.authenticate(CompanyAuthenticationService.java:82)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.company.auth.CompanyAuthenticationService$$SpringCGLIB$$0.authenticate(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted

2025-09-18T15:39:41.168+07:00  INFO 9253 --- [qtp241358679-391] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint CompanyAuthenticationService/authenticate
2025-09-18T15:39:51.248+07:00  INFO 9253 --- [qtp241358679-106] n.d.module.session.ClientSessionManager  : Add a client session id = node0145r6ohqsyhpl467bakt4x4344, token = ********************************
2025-09-18T15:39:51.330+07:00  INFO 9253 --- [qtp241358679-106] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-18T15:40:05.187+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:40:05.190+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T15:41:06.247+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:41:15.268+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 2
2025-09-18T15:41:15.271+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T15:42:04.349+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:43:06.428+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:43:14.469+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-09-18T15:43:14.470+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T15:44:03.547+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:45:06.648+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T15:45:06.650+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-18T15:45:06.651+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 18/09/2025@15:45:06+0700
2025-09-18T15:45:06.667+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 18/09/2025@15:45:00+0700 to 18/09/2025@16:00:00+0700
2025-09-18T15:45:06.667+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 18/09/2025@15:45:00+0700 to 18/09/2025@16:00:00+0700
2025-09-18T15:45:06.667+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:45:13.696+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-09-18T15:45:13.701+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T15:46:02.786+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:47:05.885+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:47:12.917+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T15:47:12.923+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T15:48:07.000+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:49:05.099+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:49:12.112+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T15:49:12.115+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T15:50:06.188+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:50:06.189+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T15:51:04.284+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:51:16.327+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 3
2025-09-18T15:51:16.334+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T15:52:06.409+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:53:03.507+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:53:16.529+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 3
2025-09-18T15:53:16.532+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T15:54:06.605+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:55:02.699+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:55:02.700+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T15:55:15.720+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-18T15:55:15.725+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T15:56:05.798+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:57:06.895+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:57:15.937+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T15:57:15.946+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T15:58:05.023+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:59:06.123+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T15:59:15.140+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T15:59:15.142+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T16:00:04.212+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-09-18T16:00:04.214+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:00:04.214+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T16:00:04.214+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-18T16:00:04.215+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 18/09/2025@16:00:04+0700
2025-09-18T16:00:04.228+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 18/09/2025@16:00:00+0700 to 18/09/2025@16:15:00+0700
2025-09-18T16:00:04.229+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 18/09/2025@16:00:00+0700 to 18/09/2025@16:15:00+0700
2025-09-18T16:01:06.306+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:01:14.345+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-18T16:01:14.354+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T16:02:03.443+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:03:06.549+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:03:13.568+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T16:03:13.580+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T16:04:02.663+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:05:05.770+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:05:05.772+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T16:05:12.801+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T16:05:12.808+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T16:06:06.898+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:07:04.980+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:07:11.994+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T16:07:11.998+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T16:08:06.070+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:09:04.150+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:09:16.184+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T16:09:16.192+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T16:10:06.265+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:10:06.266+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T16:11:03.357+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:11:16.413+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-18T16:11:16.420+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T16:12:06.511+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:13:02.597+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:13:16.626+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T16:13:16.631+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T16:14:05.720+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:15:06.828+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:15:06.831+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T16:15:06.831+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-18T16:15:06.832+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 18/09/2025@16:15:06+0700
2025-09-18T16:15:06.865+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 18/09/2025@16:15:00+0700 to 18/09/2025@16:30:00+0700
2025-09-18T16:15:06.865+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 18/09/2025@16:15:00+0700 to 18/09/2025@16:30:00+0700
2025-09-18T16:15:15.888+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T16:15:15.892+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T16:16:04.971+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:17:06.061+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:17:15.095+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T16:17:15.101+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T16:18:04.161+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:19:06.250+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:19:14.269+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T16:19:14.273+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T16:20:03.355+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:20:03.359+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T16:21:06.455+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:21:13.487+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T16:21:13.495+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T16:22:02.565+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:23:05.670+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:23:12.682+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T16:23:12.685+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T16:24:06.758+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:25:04.857+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:25:04.859+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T16:25:11.876+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-18T16:25:11.881+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T16:26:06.970+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:27:04.049+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:27:16.079+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T16:27:16.088+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T16:28:06.163+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:29:03.266+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:29:16.317+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T16:29:16.322+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T16:30:06.391+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:30:06.394+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T16:30:06.394+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-18T16:30:06.395+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 18/09/2025@16:30:06+0700
2025-09-18T16:30:06.429+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 18/09/2025@16:30:00+0700 to 18/09/2025@16:45:00+0700
2025-09-18T16:30:06.429+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 18/09/2025@16:30:00+0700 to 18/09/2025@16:45:00+0700
2025-09-18T16:31:02.510+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:31:16.547+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-18T16:31:16.553+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T16:32:05.621+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:33:06.706+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:33:15.725+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T16:33:15.738+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T16:34:04.794+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:35:06.890+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:35:06.894+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T16:35:14.924+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-18T16:35:14.931+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T16:36:04.009+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:37:06.083+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:37:14.123+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T16:37:14.126+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T16:38:03.197+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:39:06.269+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:39:13.317+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T16:39:13.327+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T16:40:02.384+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:40:02.386+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T16:41:05.501+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:41:12.520+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-18T16:41:12.521+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T16:42:06.592+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:43:04.698+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:43:11.732+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T16:43:11.738+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T16:44:06.818+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:45:03.911+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:45:03.913+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T16:45:03.913+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-18T16:45:03.913+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 18/09/2025@16:45:03+0700
2025-09-18T16:45:03.932+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 18/09/2025@16:45:00+0700 to 18/09/2025@17:00:00+0700
2025-09-18T16:45:03.932+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 18/09/2025@16:45:00+0700 to 18/09/2025@17:00:00+0700
2025-09-18T16:45:15.958+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 4
2025-09-18T16:45:15.963+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T16:46:06.039+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:47:03.120+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:47:16.153+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T16:47:16.160+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T16:48:06.241+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:49:02.331+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:49:16.363+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T16:49:16.368+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T16:50:05.433+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:50:05.434+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T16:51:06.504+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:51:15.521+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T16:51:15.522+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T16:52:04.597+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:53:06.686+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:53:14.699+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T16:53:14.705+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T16:54:03.761+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:55:06.896+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:55:06.897+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T16:55:13.918+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-18T16:55:13.919+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T16:56:03.056+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:57:06.189+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:57:13.219+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T16:57:13.224+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T16:58:02.283+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:59:05.363+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T16:59:12.378+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T16:59:12.381+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T17:00:06.460+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T17:00:06.464+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:00:06.464+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-18T17:00:06.470+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 18/09/2025@17:00:06+0700
2025-09-18T17:00:06.505+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 18/09/2025@17:00:00+0700 to 18/09/2025@17:15:00+0700
2025-09-18T17:00:06.505+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 18/09/2025@17:00:00+0700 to 18/09/2025@17:15:00+0700
2025-09-18T17:00:06.505+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-09-18T17:01:04.584+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:01:16.599+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-18T17:01:16.603+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T17:02:06.676+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:03:03.743+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:03:15.762+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T17:03:15.778+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T17:04:06.862+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:05:02.936+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:05:02.939+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T17:05:15.987+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T17:05:15.998+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T17:06:06.065+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:07:02.150+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:07:16.184+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T17:07:16.193+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T17:08:05.273+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:09:06.352+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:09:15.375+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T17:09:15.380+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T17:10:04.454+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:10:04.459+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T17:11:06.597+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:11:14.646+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-18T17:11:14.656+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T17:12:03.845+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:13:07.016+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:13:14.043+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T17:13:14.046+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T17:14:03.175+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:15:06.343+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T17:15:06.345+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:15:06.346+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 18/09/2025@17:15:06+0700
2025-09-18T17:15:06.372+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 18/09/2025@17:15:00+0700 to 18/09/2025@17:30:00+0700
2025-09-18T17:15:06.372+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 18/09/2025@17:15:00+0700 to 18/09/2025@17:30:00+0700
2025-09-18T17:15:06.373+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-18T17:15:13.401+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-18T17:15:13.407+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T17:16:02.533+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:17:05.713+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:17:12.734+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T17:17:12.739+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T17:18:06.819+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:19:04.915+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:19:11.953+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T17:19:11.960+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T17:20:06.047+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:20:06.051+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T17:21:04.157+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:21:16.205+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T17:21:16.211+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T17:22:06.306+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:23:03.390+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:23:16.433+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T17:23:16.440+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T17:24:06.511+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:25:02.607+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:25:02.618+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T17:25:16.664+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-18T17:25:16.675+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T17:26:05.740+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:27:06.823+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:27:15.851+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T17:27:15.859+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T17:28:04.923+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:29:06.008+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:29:15.029+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T17:29:15.035+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T17:30:04.113+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:30:04.117+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T17:30:04.118+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-18T17:30:04.120+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 18/09/2025@17:30:04+0700
2025-09-18T17:30:04.155+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 18/09/2025@17:30:00+0700 to 18/09/2025@17:45:00+0700
2025-09-18T17:30:04.155+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 18/09/2025@17:30:00+0700 to 18/09/2025@17:45:00+0700
2025-09-18T17:31:06.255+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:31:14.297+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T17:31:14.313+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T17:32:03.391+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:33:06.484+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:33:13.505+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T17:33:13.515+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T17:34:02.588+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:35:05.705+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:35:05.707+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T17:35:12.716+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-18T17:35:12.721+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T17:36:06.793+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:37:04.895+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:37:11.916+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T17:37:11.925+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T17:37:57.615+07:00  INFO 9253 --- [Scheduler-769336846-1] n.d.m.session.AppHttpSessionListener     : The session node0145r6ohqsyhpl467bakt4x4344 is destroyed.
2025-09-18T17:38:07.003+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:39:04.104+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:39:16.125+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T17:39:16.134+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T17:40:06.213+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:40:06.215+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T17:41:03.312+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:41:16.339+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-18T17:41:16.347+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T17:42:06.422+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:43:02.522+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:43:16.547+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T17:43:16.552+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T17:44:05.621+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:45:06.704+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:45:06.708+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T17:45:06.709+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 18/09/2025@17:45:06+0700
2025-09-18T17:45:06.734+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 18/09/2025@17:45:00+0700 to 18/09/2025@18:00:00+0700
2025-09-18T17:45:06.735+07:00  INFO 9253 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 18/09/2025@17:45:00+0700 to 18/09/2025@18:00:00+0700
2025-09-18T17:45:06.735+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-18T17:45:15.759+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T17:45:15.763+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T17:46:04.836+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:47:06.945+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:47:14.966+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T17:47:14.973+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T17:48:04.042+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:49:06.140+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:49:14.160+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T17:49:14.166+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T17:50:03.242+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:50:03.243+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T17:51:06.340+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:51:13.351+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T17:51:13.353+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T17:52:02.425+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:53:05.533+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:53:12.553+07:00  INFO 9253 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T17:53:12.558+07:00  INFO 9253 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T17:54:06.643+07:00  INFO 9253 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T17:54:54.146+07:00  INFO 9253 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@d90fb97{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-18T17:54:54.148+07:00  INFO 9253 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-18T17:54:54.148+07:00  INFO 9253 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-18T17:54:54.148+07:00  INFO 9253 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-18T17:54:54.149+07:00  INFO 9253 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-18T17:54:54.149+07:00  INFO 9253 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-18T17:54:54.149+07:00  INFO 9253 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-18T17:54:54.149+07:00  INFO 9253 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-18T17:54:54.149+07:00  INFO 9253 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-09-18T17:54:54.149+07:00  INFO 9253 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-09-18T17:54:54.149+07:00  INFO 9253 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-18T17:54:54.149+07:00  INFO 9253 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-09-18T17:54:54.149+07:00  INFO 9253 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-09-18T17:54:54.149+07:00  INFO 9253 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-18T17:54:54.149+07:00  INFO 9253 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-09-18T17:54:54.149+07:00  INFO 9253 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-09-18T17:54:54.169+07:00  INFO 9253 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T17:54:54.240+07:00  INFO 9253 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-18T17:54:54.245+07:00  INFO 9253 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-18T17:54:54.270+07:00  INFO 9253 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-18T17:54:54.271+07:00  INFO 9253 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-18T17:54:54.273+07:00  INFO 9253 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-18T17:54:54.273+07:00  INFO 9253 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-18T17:54:54.274+07:00  INFO 9253 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-18T17:54:54.274+07:00  INFO 9253 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-18T17:54:54.274+07:00  INFO 9253 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-18T17:54:54.274+07:00  INFO 9253 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-18T17:54:54.274+07:00  INFO 9253 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-18T17:54:54.274+07:00  INFO 9253 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-18T17:54:54.275+07:00  INFO 9253 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-18T17:54:54.275+07:00  INFO 9253 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-18T17:54:54.275+07:00  INFO 9253 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-18T17:54:54.276+07:00  INFO 9253 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@744d0cfb{STOPPING}[12.0.15,sto=0]
2025-09-18T17:54:54.278+07:00  INFO 9253 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-18T17:54:54.281+07:00  INFO 9253 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@5c452eb5{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.9162318785609809297/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@1a2bcce1{STOPPED}}
