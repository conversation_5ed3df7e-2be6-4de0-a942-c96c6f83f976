2025-09-19T15:14:11.814+07:00  INFO 85388 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 85388 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-19T15:14:11.815+07:00  INFO 85388 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-19T15:14:12.501+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T15:14:12.568+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 63 ms. Found 22 JPA repository interfaces.
2025-09-19T15:14:12.577+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T15:14:12.579+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-19T15:14:12.579+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T15:14:12.622+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 42 ms. Found 10 JPA repository interfaces.
2025-09-19T15:14:12.623+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T15:14:12.626+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-19T15:14:12.635+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T15:14:12.640+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 1 JPA repository interface.
2025-09-19T15:14:12.649+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T15:14:12.651+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-19T15:14:12.651+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T15:14:12.654+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-19T15:14:12.657+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T15:14:12.661+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 5 JPA repository interfaces.
2025-09-19T15:14:12.666+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T15:14:12.668+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-19T15:14:12.669+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T15:14:12.669+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-19T15:14:12.669+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T15:14:12.675+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 10 JPA repository interfaces.
2025-09-19T15:14:12.680+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T15:14:12.682+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-19T15:14:12.685+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T15:14:12.689+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-19T15:14:12.689+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T15:14:12.696+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-19T15:14:12.696+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T15:14:12.699+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-09-19T15:14:12.699+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T15:14:12.699+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-19T15:14:12.700+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T15:14:12.701+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-19T15:14:12.701+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T15:14:12.705+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-19T15:14:12.705+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T15:14:12.706+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-19T15:14:12.706+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T15:14:12.706+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-19T15:14:12.706+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T15:14:12.716+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 19 JPA repository interfaces.
2025-09-19T15:14:12.726+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T15:14:12.732+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 8 JPA repository interfaces.
2025-09-19T15:14:12.732+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T15:14:12.735+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-09-19T15:14:12.736+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T15:14:12.739+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-19T15:14:12.740+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T15:14:12.745+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 9 JPA repository interfaces.
2025-09-19T15:14:12.745+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T15:14:12.748+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-19T15:14:12.749+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T15:14:12.757+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 13 JPA repository interfaces.
2025-09-19T15:14:12.757+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T15:14:12.766+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 14 JPA repository interfaces.
2025-09-19T15:14:12.767+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T15:14:12.781+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 13 ms. Found 24 JPA repository interfaces.
2025-09-19T15:14:12.781+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T15:14:12.782+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-19T15:14:12.787+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T15:14:12.788+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-19T15:14:12.788+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T15:14:12.795+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-19T15:14:12.797+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T15:14:12.832+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 35 ms. Found 66 JPA repository interfaces.
2025-09-19T15:14:12.832+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T15:14:12.834+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-19T15:14:12.838+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T15:14:12.841+07:00  INFO 85388 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-19T15:14:13.077+07:00  INFO 85388 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-19T15:14:13.081+07:00  INFO 85388 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-19T15:14:13.356+07:00  WARN 85388 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-19T15:14:13.547+07:00  INFO 85388 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-19T15:14:13.549+07:00  INFO 85388 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-19T15:14:13.561+07:00  INFO 85388 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-19T15:14:13.561+07:00  INFO 85388 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1627 ms
2025-09-19T15:14:13.634+07:00  WARN 85388 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-19T15:14:13.635+07:00  INFO 85388 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-19T15:14:13.735+07:00  INFO 85388 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@105d2c27
2025-09-19T15:14:13.735+07:00  INFO 85388 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-19T15:14:13.740+07:00  WARN 85388 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-19T15:14:13.740+07:00  INFO 85388 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-19T15:14:13.746+07:00  INFO 85388 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@25767bd9
2025-09-19T15:14:13.747+07:00  INFO 85388 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-19T15:14:13.747+07:00  WARN 85388 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-19T15:14:13.747+07:00  INFO 85388 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-19T15:14:13.756+07:00  INFO 85388 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@66d0d5ee
2025-09-19T15:14:13.756+07:00  INFO 85388 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-19T15:14:13.756+07:00  WARN 85388 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-19T15:14:13.756+07:00  INFO 85388 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-19T15:14:13.764+07:00  INFO 85388 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@73c5b676
2025-09-19T15:14:13.765+07:00  INFO 85388 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-19T15:14:13.765+07:00  WARN 85388 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-19T15:14:13.765+07:00  INFO 85388 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-19T15:14:13.773+07:00  INFO 85388 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@b7526a9
2025-09-19T15:14:13.773+07:00  INFO 85388 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-19T15:14:13.773+07:00  INFO 85388 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-19T15:14:13.817+07:00  INFO 85388 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-19T15:14:13.868+07:00  INFO 85388 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@70b9a553{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.12043930214894957574/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@591dd039{STARTED}}
2025-09-19T15:14:13.869+07:00  INFO 85388 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@70b9a553{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.12043930214894957574/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@591dd039{STARTED}}
2025-09-19T15:14:13.871+07:00  INFO 85388 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@7d2d7a85{STARTING}[12.0.15,sto=0] @2707ms
2025-09-19T15:14:13.931+07:00  INFO 85388 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-19T15:14:13.957+07:00  INFO 85388 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-19T15:14:13.971+07:00  INFO 85388 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-19T15:14:14.092+07:00  INFO 85388 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-19T15:14:14.119+07:00  WARN 85388 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-19T15:14:14.779+07:00  INFO 85388 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-19T15:14:14.791+07:00  INFO 85388 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@57f28893] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-19T15:14:14.911+07:00  INFO 85388 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T15:14:15.098+07:00  INFO 85388 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.graphapi", "net.datatp.module.kpi", "net.datatp.module.monitor.activity", "net.datatp.module.wfms", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "net.datatp.module.okr", "cloud.datatp.module.fleet", "net.datatp.module.core.template", "cloud.datatp.module.pegasus.shipment", "cloud.datatp.gps", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "net.datatp.module.groovy", "cloud.datatp.tms", "net.datatp.module.company.tmpl", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "cloud.datatp.vendor", "net.datatp.module.resource", "net.datatp.module.company.hr", "net.datatp.module.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.chatbot", "net.datatp.module.core.print", "cloud.datatp.module.odoo", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-09-19T15:14:15.100+07:00  INFO 85388 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-19T15:14:15.107+07:00  INFO 85388 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-19T15:14:15.108+07:00  INFO 85388 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-19T15:14:15.132+07:00  INFO 85388 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-19T15:14:15.136+07:00  WARN 85388 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-19T15:14:17.153+07:00  INFO 85388 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-19T15:14:17.154+07:00  INFO 85388 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@67d7f8bc] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-19T15:14:17.325+07:00  WARN 85388 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-19T15:14:17.325+07:00  WARN 85388 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-19T15:14:17.330+07:00  WARN 85388 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-19T15:14:17.330+07:00  WARN 85388 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-19T15:14:17.344+07:00  WARN 85388 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-19T15:14:17.344+07:00  WARN 85388 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-19T15:14:17.724+07:00  INFO 85388 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T15:14:17.730+07:00  INFO 85388 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-19T15:14:17.731+07:00  INFO 85388 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-19T15:14:17.750+07:00  INFO 85388 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-19T15:14:17.753+07:00  WARN 85388 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-19T15:14:18.283+07:00  INFO 85388 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-19T15:14:18.284+07:00  INFO 85388 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@3042f34] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-19T15:14:18.354+07:00  WARN 85388 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-19T15:14:18.354+07:00  WARN 85388 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-19T15:14:18.720+07:00  INFO 85388 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T15:14:18.748+07:00  INFO 85388 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-19T15:14:18.753+07:00  INFO 85388 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-19T15:14:18.753+07:00  INFO 85388 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T15:14:18.759+07:00  WARN 85388 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-19T15:14:18.889+07:00  INFO 85388 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-19T15:14:19.359+07:00  INFO 85388 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-19T15:14:19.362+07:00  INFO 85388 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-19T15:14:19.397+07:00  INFO 85388 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-19T15:14:19.444+07:00  INFO 85388 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-19T15:14:19.548+07:00  INFO 85388 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-19T15:14:19.576+07:00  INFO 85388 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-19T15:14:19.597+07:00  INFO 85388 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 336920808ms : this is harmless.
2025-09-19T15:14:19.606+07:00  INFO 85388 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-19T15:14:19.609+07:00  INFO 85388 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-19T15:14:19.639+07:00  INFO 85388 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 628426766ms : this is harmless.
2025-09-19T15:14:19.640+07:00  INFO 85388 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-19T15:14:19.651+07:00  INFO 85388 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-19T15:14:19.652+07:00  INFO 85388 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-19T15:14:21.693+07:00  INFO 85388 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-19T15:14:21.693+07:00  INFO 85388 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T15:14:21.694+07:00  WARN 85388 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-19T15:14:21.993+07:00  INFO 85388 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 19/09/2025@15:00:00+0700 to 19/09/2025@15:15:00+0700
2025-09-19T15:14:21.993+07:00  INFO 85388 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 19/09/2025@15:00:00+0700 to 19/09/2025@15:15:00+0700
2025-09-19T15:14:22.553+07:00  INFO 85388 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-09-19T15:14:22.553+07:00  INFO 85388 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T15:14:22.553+07:00  WARN 85388 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-19T15:14:22.856+07:00  INFO 85388 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-09-19T15:14:22.856+07:00  INFO 85388 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-09-19T15:14:22.856+07:00  INFO 85388 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-09-19T15:14:22.856+07:00  INFO 85388 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-09-19T15:14:22.856+07:00  INFO 85388 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-09-19T15:14:24.793+07:00  WARN 85388 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: ba00367b-6297-4779-b78a-a0ac95f93b73

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-19T15:14:24.797+07:00  INFO 85388 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-09-19T15:14:25.092+07:00  INFO 85388 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-19T15:14:25.092+07:00  INFO 85388 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-19T15:14:25.092+07:00  INFO 85388 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-19T15:14:25.092+07:00  INFO 85388 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-19T15:14:25.092+07:00  INFO 85388 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-09-19T15:14:25.092+07:00  INFO 85388 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-09-19T15:14:25.092+07:00  INFO 85388 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-19T15:14:25.093+07:00  INFO 85388 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-09-19T15:14:25.093+07:00  INFO 85388 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-09-19T15:14:25.093+07:00  INFO 85388 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-19T15:14:25.093+07:00  INFO 85388 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-09-19T15:14:25.093+07:00  INFO 85388 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-09-19T15:14:25.093+07:00  INFO 85388 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-19T15:14:25.094+07:00  INFO 85388 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-19T15:14:25.094+07:00  INFO 85388 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-19T15:14:25.209+07:00  INFO 85388 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-19T15:14:25.209+07:00  INFO 85388 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-19T15:14:25.211+07:00  INFO 85388 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-09-19T15:14:25.220+07:00  INFO 85388 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@20c43d7f{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-19T15:14:25.221+07:00  INFO 85388 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-09-19T15:14:25.222+07:00  INFO 85388 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-09-19T15:14:25.250+07:00  INFO 85388 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-09-19T15:14:25.251+07:00  INFO 85388 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-09-19T15:14:25.257+07:00  INFO 85388 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 13.803 seconds (process running for 14.097)
2025-09-19T15:15:02.321+07:00  INFO 85388 --- [qtp704611578-36] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-09-19T15:15:06.151+07:00  INFO 85388 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-19T15:15:06.155+07:00  INFO 85388 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-19T15:15:06.156+07:00  INFO 85388 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T15:15:06.156+07:00  INFO 85388 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 19/09/2025@15:15:06+0700
2025-09-19T15:15:06.163+07:00  INFO 85388 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 19/09/2025@15:15:00+0700 to 19/09/2025@15:30:00+0700
2025-09-19T15:15:06.163+07:00  INFO 85388 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 19/09/2025@15:15:00+0700 to 19/09/2025@15:30:00+0700
2025-09-19T15:15:28.234+07:00  INFO 85388 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-19T15:15:28.237+07:00  INFO 85388 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T15:16:03.300+07:00  INFO 85388 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T15:16:03.328+07:00  INFO 85388 --- [qtp704611578-35] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node09wbocm8zf57jxye0f1s8azyv1
2025-09-19T15:16:03.328+07:00  INFO 85388 --- [qtp704611578-40] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node011q6zvj90sb7wq53dcdfv4f5f0
2025-09-19T15:16:03.422+07:00  INFO 85388 --- [qtp704611578-35] n.d.module.session.ClientSessionManager  : Add a client session id = node09wbocm8zf57jxye0f1s8azyv1, token = 042faabb8f29c79ce1fb035307eca609
2025-09-19T15:16:03.438+07:00  INFO 85388 --- [qtp704611578-40] n.d.module.session.ClientSessionManager  : Add a client session id = node011q6zvj90sb7wq53dcdfv4f5f0, token = 042faabb8f29c79ce1fb035307eca609
2025-09-19T15:16:03.545+07:00  INFO 85388 --- [qtp704611578-35] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T15:16:03.569+07:00  INFO 85388 --- [qtp704611578-40] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T15:16:47.830+07:00  INFO 85388 --- [qtp704611578-36] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T15:16:47.830+07:00  INFO 85388 --- [qtp704611578-61] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T15:16:47.903+07:00  INFO 85388 --- [qtp704611578-61] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-19T15:16:47.919+07:00  INFO 85388 --- [qtp704611578-36] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-19T15:17:06.400+07:00  INFO 85388 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T15:17:27.483+07:00  INFO 85388 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 15, expire count 0
2025-09-19T15:17:27.497+07:00  INFO 85388 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-19T15:18:02.560+07:00  INFO 85388 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T15:19:05.682+07:00  INFO 85388 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T15:19:31.751+07:00  INFO 85388 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-19T15:19:31.760+07:00  INFO 85388 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T15:20:06.820+07:00  INFO 85388 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T15:20:06.822+07:00  INFO 85388 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-19T15:20:59.348+07:00  INFO 85388 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@20c43d7f{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-19T15:20:59.350+07:00  INFO 85388 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-19T15:20:59.351+07:00  INFO 85388 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-19T15:20:59.351+07:00  INFO 85388 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-19T15:20:59.351+07:00  INFO 85388 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-19T15:20:59.351+07:00  INFO 85388 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-19T15:20:59.352+07:00  INFO 85388 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-19T15:20:59.352+07:00  INFO 85388 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-19T15:20:59.352+07:00  INFO 85388 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-09-19T15:20:59.352+07:00  INFO 85388 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-09-19T15:20:59.352+07:00  INFO 85388 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-19T15:20:59.352+07:00  INFO 85388 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-09-19T15:20:59.352+07:00  INFO 85388 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-09-19T15:20:59.352+07:00  INFO 85388 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-19T15:20:59.352+07:00  INFO 85388 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-09-19T15:20:59.352+07:00  INFO 85388 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-09-19T15:20:59.394+07:00  INFO 85388 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T15:20:59.505+07:00  INFO 85388 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-19T15:20:59.535+07:00  INFO 85388 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-19T15:20:59.556+07:00  INFO 85388 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T15:20:59.557+07:00  INFO 85388 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T15:20:59.558+07:00  INFO 85388 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T15:20:59.558+07:00  INFO 85388 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-19T15:20:59.560+07:00  INFO 85388 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-19T15:20:59.560+07:00  INFO 85388 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-19T15:20:59.560+07:00  INFO 85388 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-19T15:20:59.560+07:00  INFO 85388 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-19T15:20:59.560+07:00  INFO 85388 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-19T15:20:59.561+07:00  INFO 85388 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-19T15:20:59.561+07:00  INFO 85388 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-19T15:20:59.561+07:00  INFO 85388 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-19T15:20:59.561+07:00  INFO 85388 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-19T15:20:59.567+07:00  INFO 85388 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@7d2d7a85{STOPPING}[12.0.15,sto=0]
2025-09-19T15:20:59.570+07:00  INFO 85388 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-19T15:20:59.571+07:00  INFO 85388 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@70b9a553{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.12043930214894957574/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@591dd039{STOPPED}}
