2025-09-22T11:35:01.349+07:00  INFO 16665 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 16665 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-22T11:35:01.350+07:00  INFO 16665 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-22T11:35:02.044+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T11:35:02.107+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 60 ms. Found 22 JPA repository interfaces.
2025-09-22T11:35:02.117+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T11:35:02.119+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-22T11:35:02.119+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T11:35:02.127+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 10 JPA repository interfaces.
2025-09-22T11:35:02.128+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T11:35:02.131+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-22T11:35:02.140+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T11:35:02.145+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 1 JPA repository interface.
2025-09-22T11:35:02.153+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T11:35:02.155+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-22T11:35:02.156+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T11:35:02.159+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-22T11:35:02.162+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T11:35:02.167+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 5 JPA repository interfaces.
2025-09-22T11:35:02.171+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T11:35:02.173+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-22T11:35:02.174+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T11:35:02.174+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-22T11:35:02.174+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T11:35:02.180+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 10 JPA repository interfaces.
2025-09-22T11:35:02.185+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T11:35:02.187+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-22T11:35:02.190+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T11:35:02.234+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 43 ms. Found 6 JPA repository interfaces.
2025-09-22T11:35:02.234+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T11:35:02.241+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-09-22T11:35:02.241+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T11:35:02.244+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-22T11:35:02.244+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T11:35:02.245+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-22T11:35:02.245+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T11:35:02.245+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-22T11:35:02.246+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T11:35:02.250+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-22T11:35:02.250+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T11:35:02.251+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-22T11:35:02.251+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T11:35:02.251+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-22T11:35:02.251+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T11:35:02.261+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 19 JPA repository interfaces.
2025-09-22T11:35:02.270+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T11:35:02.276+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 8 JPA repository interfaces.
2025-09-22T11:35:02.276+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T11:35:02.279+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-09-22T11:35:02.279+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T11:35:02.283+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-22T11:35:02.283+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T11:35:02.288+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 9 JPA repository interfaces.
2025-09-22T11:35:02.288+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T11:35:02.292+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-09-22T11:35:02.293+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T11:35:02.302+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 13 JPA repository interfaces.
2025-09-22T11:35:02.302+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T11:35:02.311+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 14 JPA repository interfaces.
2025-09-22T11:35:02.311+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T11:35:02.326+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 14 ms. Found 24 JPA repository interfaces.
2025-09-22T11:35:02.326+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T11:35:02.327+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-22T11:35:02.331+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T11:35:02.332+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-22T11:35:02.332+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T11:35:02.339+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-22T11:35:02.340+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T11:35:02.377+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 36 ms. Found 66 JPA repository interfaces.
2025-09-22T11:35:02.377+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T11:35:02.378+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-22T11:35:02.382+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T11:35:02.385+07:00  INFO 16665 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-22T11:35:02.584+07:00  INFO 16665 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-22T11:35:02.588+07:00  INFO 16665 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-22T11:35:02.854+07:00  WARN 16665 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-22T11:35:03.055+07:00  INFO 16665 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-22T11:35:03.057+07:00  INFO 16665 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-22T11:35:03.068+07:00  INFO 16665 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-22T11:35:03.068+07:00  INFO 16665 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1608 ms
2025-09-22T11:35:03.118+07:00  WARN 16665 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-22T11:35:03.118+07:00  INFO 16665 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-22T11:35:03.228+07:00  INFO 16665 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@5a318745
2025-09-22T11:35:03.228+07:00  INFO 16665 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-22T11:35:03.233+07:00  WARN 16665 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-22T11:35:03.233+07:00  INFO 16665 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-22T11:35:03.238+07:00  INFO 16665 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@6e057374
2025-09-22T11:35:03.238+07:00  INFO 16665 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-22T11:35:03.238+07:00  WARN 16665 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-22T11:35:03.238+07:00  INFO 16665 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-22T11:35:03.248+07:00  INFO 16665 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@511e0453
2025-09-22T11:35:03.249+07:00  INFO 16665 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-22T11:35:03.249+07:00  WARN 16665 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-22T11:35:03.249+07:00  INFO 16665 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-22T11:35:03.261+07:00  INFO 16665 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@130901fd
2025-09-22T11:35:03.261+07:00  INFO 16665 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-22T11:35:03.261+07:00  WARN 16665 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-22T11:35:03.261+07:00  INFO 16665 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-22T11:35:03.270+07:00  INFO 16665 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@73ec417b
2025-09-22T11:35:03.270+07:00  INFO 16665 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-22T11:35:03.270+07:00  INFO 16665 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-22T11:35:03.313+07:00  INFO 16665 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-22T11:35:03.316+07:00  INFO 16665 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@10ecdd85{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.17329618869251456460/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@47ffce5d{STARTED}}
2025-09-22T11:35:03.316+07:00  INFO 16665 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@10ecdd85{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.17329618869251456460/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@47ffce5d{STARTED}}
2025-09-22T11:35:03.318+07:00  INFO 16665 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@4f5080ea{STARTING}[12.0.15,sto=0] @2527ms
2025-09-22T11:35:03.370+07:00  INFO 16665 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-22T11:35:03.450+07:00  INFO 16665 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-22T11:35:03.463+07:00  INFO 16665 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-22T11:35:03.584+07:00  INFO 16665 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-22T11:35:03.621+07:00  WARN 16665 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-22T11:35:04.241+07:00  INFO 16665 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-22T11:35:04.252+07:00  INFO 16665 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@336bb88c] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-22T11:35:04.413+07:00  INFO 16665 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-22T11:35:04.626+07:00  INFO 16665 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.wfms", "net.datatp.module.monitor.activity", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "net.datatp.module.okr", "cloud.datatp.module.fleet", "net.datatp.module.core.template", "cloud.datatp.module.pegasus.shipment", "cloud.datatp.gps", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "cloud.datatp.tms", "net.datatp.module.company.tmpl", "net.datatp.module.groovy", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "net.datatp.module.resource", "cloud.datatp.vendor", "net.datatp.module.company.hr", "net.datatp.module.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.core.print", "cloud.datatp.module.odoo", "net.datatp.module.chatbot", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.zalo", "net.datatp.module.accounting" ]
2025-09-22T11:35:04.628+07:00  INFO 16665 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-22T11:35:04.633+07:00  INFO 16665 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-22T11:35:04.634+07:00  INFO 16665 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-22T11:35:04.660+07:00  INFO 16665 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-22T11:35:04.668+07:00  WARN 16665 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-22T11:35:07.022+07:00  INFO 16665 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-22T11:35:07.023+07:00  INFO 16665 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@9f1c82] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-22T11:35:07.222+07:00  WARN 16665 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-22T11:35:07.222+07:00  WARN 16665 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-22T11:35:07.237+07:00  WARN 16665 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-22T11:35:07.237+07:00  WARN 16665 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-22T11:35:07.254+07:00  WARN 16665 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-22T11:35:07.254+07:00  WARN 16665 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-22T11:35:07.941+07:00  INFO 16665 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-22T11:35:07.947+07:00  INFO 16665 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-22T11:35:07.949+07:00  INFO 16665 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-22T11:35:07.972+07:00  INFO 16665 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-22T11:35:07.975+07:00  WARN 16665 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-22T11:35:08.565+07:00  INFO 16665 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-22T11:35:08.565+07:00  INFO 16665 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@31534081] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-22T11:35:08.652+07:00  WARN 16665 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-22T11:35:08.652+07:00  WARN 16665 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-22T11:35:09.052+07:00  INFO 16665 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-22T11:35:09.114+07:00  INFO 16665 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-22T11:35:09.139+07:00  INFO 16665 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-22T11:35:09.140+07:00  INFO 16665 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T11:35:09.147+07:00  WARN 16665 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-22T11:35:09.347+07:00  INFO 16665 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-22T11:35:09.892+07:00  INFO 16665 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-22T11:35:09.895+07:00  INFO 16665 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-22T11:35:09.930+07:00  INFO 16665 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-22T11:35:09.975+07:00  INFO 16665 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-22T11:35:10.069+07:00  INFO 16665 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-22T11:35:10.098+07:00  INFO 16665 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-22T11:35:10.133+07:00  INFO 16665 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 582969346ms : this is harmless.
2025-09-22T11:35:10.142+07:00  INFO 16665 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-22T11:35:10.145+07:00  INFO 16665 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-22T11:35:10.157+07:00  INFO 16665 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 874475308ms : this is harmless.
2025-09-22T11:35:10.159+07:00  INFO 16665 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-22T11:35:10.176+07:00  INFO 16665 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-22T11:35:10.177+07:00  INFO 16665 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-22T11:35:12.144+07:00  INFO 16665 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-22T11:35:12.144+07:00  INFO 16665 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T11:35:12.145+07:00  WARN 16665 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-22T11:35:12.482+07:00  INFO 16665 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 22/09/2025@11:30:00+0700 to 22/09/2025@11:45:00+0700
2025-09-22T11:35:12.482+07:00  INFO 16665 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 22/09/2025@11:30:00+0700 to 22/09/2025@11:45:00+0700
2025-09-22T11:35:13.055+07:00  INFO 16665 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-09-22T11:35:13.055+07:00  INFO 16665 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T11:35:13.056+07:00  WARN 16665 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-22T11:35:13.357+07:00  INFO 16665 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-09-22T11:35:13.358+07:00  INFO 16665 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-09-22T11:35:13.358+07:00  INFO 16665 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-09-22T11:35:13.358+07:00  INFO 16665 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-09-22T11:35:13.358+07:00  INFO 16665 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-09-22T11:35:15.122+07:00  WARN 16665 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 6fa9a486-711f-4def-b85b-cb7fe90fc078

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-22T11:35:15.126+07:00  INFO 16665 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-09-22T11:35:15.468+07:00  INFO 16665 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-22T11:35:15.468+07:00  INFO 16665 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-22T11:35:15.468+07:00  INFO 16665 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-22T11:35:15.468+07:00  INFO 16665 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-22T11:35:15.468+07:00  INFO 16665 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-09-22T11:35:15.468+07:00  INFO 16665 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-09-22T11:35:15.468+07:00  INFO 16665 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-22T11:35:15.469+07:00  INFO 16665 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-09-22T11:35:15.469+07:00  INFO 16665 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-09-22T11:35:15.469+07:00  INFO 16665 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-22T11:35:15.469+07:00  INFO 16665 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-09-22T11:35:15.469+07:00  INFO 16665 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-09-22T11:35:15.472+07:00  INFO 16665 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-22T11:35:15.472+07:00  INFO 16665 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-22T11:35:15.472+07:00  INFO 16665 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-22T11:35:15.546+07:00  INFO 16665 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-22T11:35:15.546+07:00  INFO 16665 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-22T11:35:15.547+07:00  INFO 16665 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-09-22T11:35:15.555+07:00  INFO 16665 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@19218ca8{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-22T11:35:15.556+07:00  INFO 16665 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-09-22T11:35:15.557+07:00  INFO 16665 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-09-22T11:35:15.594+07:00  INFO 16665 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-09-22T11:35:15.594+07:00  INFO 16665 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-09-22T11:35:15.600+07:00  INFO 16665 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 14.55 seconds (process running for 14.811)
2025-09-22T11:35:26.020+07:00  INFO 16665 --- [qtp1987166253-36] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-09-22T11:36:04.575+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:36:18.645+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T11:36:18.694+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T11:36:27.769+07:00  INFO 16665 --- [qtp1987166253-40] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01sru0ujzpp4jfeufwqsd92hw81
2025-09-22T11:36:27.769+07:00  INFO 16665 --- [qtp1987166253-35] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node019jm1eustvc3v1sgqot1tvcabt0
2025-09-22T11:36:27.874+07:00  INFO 16665 --- [qtp1987166253-35] n.d.module.session.ClientSessionManager  : Add a client session id = node019jm1eustvc3v1sgqot1tvcabt0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:36:27.875+07:00  INFO 16665 --- [qtp1987166253-40] n.d.module.session.ClientSessionManager  : Add a client session id = node01sru0ujzpp4jfeufwqsd92hw81, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:36:28.376+07:00  INFO 16665 --- [qtp1987166253-35] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:36:28.379+07:00  INFO 16665 --- [qtp1987166253-40] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:37:00.802+07:00  INFO 16665 --- [qtp1987166253-61] n.d.module.session.ClientSessionManager  : Add a client session id = node019jm1eustvc3v1sgqot1tvcabt0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:37:00.805+07:00  INFO 16665 --- [qtp1987166253-39] n.d.module.session.ClientSessionManager  : Add a client session id = node019jm1eustvc3v1sgqot1tvcabt0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:37:00.824+07:00  INFO 16665 --- [qtp1987166253-61] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:37:00.824+07:00  INFO 16665 --- [qtp1987166253-39] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:37:06.792+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:37:09.794+07:00  INFO 16665 --- [qtp1987166253-37] n.d.module.session.ClientSessionManager  : Add a client session id = node019jm1eustvc3v1sgqot1tvcabt0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:37:09.802+07:00  INFO 16665 --- [qtp1987166253-37] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:37:09.816+07:00  INFO 16665 --- [qtp1987166253-60] n.d.module.session.ClientSessionManager  : Add a client session id = node019jm1eustvc3v1sgqot1tvcabt0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:37:09.837+07:00  INFO 16665 --- [qtp1987166253-60] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:37:29.762+07:00  INFO 16665 --- [qtp1987166253-61] n.d.module.session.ClientSessionManager  : Add a client session id = node019jm1eustvc3v1sgqot1tvcabt0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:37:29.763+07:00  INFO 16665 --- [qtp1987166253-40] n.d.module.session.ClientSessionManager  : Add a client session id = node019jm1eustvc3v1sgqot1tvcabt0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:37:29.774+07:00  INFO 16665 --- [qtp1987166253-61] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:37:29.774+07:00  INFO 16665 --- [qtp1987166253-40] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:37:33.777+07:00  INFO 16665 --- [qtp1987166253-40] n.d.module.session.ClientSessionManager  : Add a client session id = node019jm1eustvc3v1sgqot1tvcabt0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:37:33.778+07:00  INFO 16665 --- [qtp1987166253-37] n.d.module.session.ClientSessionManager  : Add a client session id = node019jm1eustvc3v1sgqot1tvcabt0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:37:33.792+07:00  INFO 16665 --- [qtp1987166253-40] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:37:33.792+07:00  INFO 16665 --- [qtp1987166253-37] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:38:03.777+07:00  INFO 16665 --- [qtp1987166253-35] n.d.module.session.ClientSessionManager  : Add a client session id = node019jm1eustvc3v1sgqot1tvcabt0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:38:03.786+07:00  INFO 16665 --- [qtp1987166253-35] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:38:03.813+07:00  INFO 16665 --- [qtp1987166253-64] n.d.module.session.ClientSessionManager  : Add a client session id = node019jm1eustvc3v1sgqot1tvcabt0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:38:03.823+07:00  INFO 16665 --- [qtp1987166253-64] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:38:03.908+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:38:17.946+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-22T11:38:17.960+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T11:39:06.043+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:39:29.769+07:00  INFO 16665 --- [qtp1987166253-35] n.d.module.session.ClientSessionManager  : Add a client session id = node019jm1eustvc3v1sgqot1tvcabt0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:39:29.770+07:00  INFO 16665 --- [qtp1987166253-61] n.d.module.session.ClientSessionManager  : Add a client session id = node019jm1eustvc3v1sgqot1tvcabt0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:39:29.777+07:00  INFO 16665 --- [qtp1987166253-61] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:39:29.777+07:00  INFO 16665 --- [qtp1987166253-35] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:40:03.136+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T11:40:03.137+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:40:22.189+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-22T11:40:22.204+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T11:40:46.840+07:00  INFO 16665 --- [qtp1987166253-41] n.d.module.session.ClientSessionManager  : Add a client session id = node019jm1eustvc3v1sgqot1tvcabt0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:40:46.862+07:00  INFO 16665 --- [qtp1987166253-41] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:40:46.862+07:00  INFO 16665 --- [qtp1987166253-72] n.d.module.session.ClientSessionManager  : Add a client session id = node019jm1eustvc3v1sgqot1tvcabt0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:40:46.881+07:00  INFO 16665 --- [qtp1987166253-72] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:40:53.804+07:00  INFO 16665 --- [qtp1987166253-73] n.d.module.session.ClientSessionManager  : Add a client session id = node019jm1eustvc3v1sgqot1tvcabt0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:40:53.805+07:00  INFO 16665 --- [qtp1987166253-68] n.d.module.session.ClientSessionManager  : Add a client session id = node019jm1eustvc3v1sgqot1tvcabt0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:40:53.812+07:00  INFO 16665 --- [qtp1987166253-73] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:40:53.812+07:00  INFO 16665 --- [qtp1987166253-68] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:41:06.280+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:41:54.776+07:00  INFO 16665 --- [qtp1987166253-40] n.d.module.session.ClientSessionManager  : Add a client session id = node019jm1eustvc3v1sgqot1tvcabt0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:41:54.778+07:00  INFO 16665 --- [qtp1987166253-60] n.d.module.session.ClientSessionManager  : Add a client session id = node019jm1eustvc3v1sgqot1tvcabt0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:41:54.789+07:00  INFO 16665 --- [qtp1987166253-40] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:41:54.789+07:00  INFO 16665 --- [qtp1987166253-60] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:42:02.379+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:42:05.771+07:00  INFO 16665 --- [qtp1987166253-73] n.d.module.session.ClientSessionManager  : Add a client session id = node019jm1eustvc3v1sgqot1tvcabt0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:42:05.773+07:00  INFO 16665 --- [qtp1987166253-72] n.d.module.session.ClientSessionManager  : Add a client session id = node019jm1eustvc3v1sgqot1tvcabt0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:42:05.781+07:00  INFO 16665 --- [qtp1987166253-73] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:42:05.781+07:00  INFO 16665 --- [qtp1987166253-72] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:42:22.425+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-22T11:42:22.436+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T11:42:26.768+07:00  INFO 16665 --- [qtp1987166253-68] n.d.module.session.ClientSessionManager  : Add a client session id = node019jm1eustvc3v1sgqot1tvcabt0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:42:26.769+07:00  INFO 16665 --- [qtp1987166253-39] n.d.module.session.ClientSessionManager  : Add a client session id = node019jm1eustvc3v1sgqot1tvcabt0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:42:26.776+07:00  INFO 16665 --- [qtp1987166253-39] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:42:26.776+07:00  INFO 16665 --- [qtp1987166253-68] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:43:05.517+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:43:09.783+07:00  INFO 16665 --- [qtp1987166253-72] n.d.module.session.ClientSessionManager  : Add a client session id = node019jm1eustvc3v1sgqot1tvcabt0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:43:09.783+07:00  INFO 16665 --- [qtp1987166253-73] n.d.module.session.ClientSessionManager  : Add a client session id = node019jm1eustvc3v1sgqot1tvcabt0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:43:09.799+07:00  INFO 16665 --- [qtp1987166253-73] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:43:09.799+07:00  INFO 16665 --- [qtp1987166253-72] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:43:24.778+07:00  INFO 16665 --- [qtp1987166253-60] n.d.module.session.ClientSessionManager  : Add a client session id = node019jm1eustvc3v1sgqot1tvcabt0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:43:24.779+07:00  INFO 16665 --- [qtp1987166253-40] n.d.module.session.ClientSessionManager  : Add a client session id = node019jm1eustvc3v1sgqot1tvcabt0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:43:24.793+07:00  INFO 16665 --- [qtp1987166253-40] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:43:24.795+07:00  INFO 16665 --- [qtp1987166253-60] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:43:29.773+07:00  INFO 16665 --- [qtp1987166253-37] n.d.module.session.ClientSessionManager  : Add a client session id = node019jm1eustvc3v1sgqot1tvcabt0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:43:29.774+07:00  INFO 16665 --- [qtp1987166253-40] n.d.module.session.ClientSessionManager  : Add a client session id = node019jm1eustvc3v1sgqot1tvcabt0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:43:29.781+07:00  INFO 16665 --- [qtp1987166253-37] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:43:29.781+07:00  INFO 16665 --- [qtp1987166253-40] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:44:06.652+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:44:21.682+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T11:44:21.687+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T11:45:04.757+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:45:04.759+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-22T11:45:04.765+07:00  INFO 16665 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 22/09/2025@11:45:04+0700
2025-09-22T11:45:04.802+07:00  INFO 16665 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 22/09/2025@11:45:00+0700 to 22/09/2025@12:00:00+0700
2025-09-22T11:45:04.802+07:00  INFO 16665 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 22/09/2025@11:45:00+0700 to 22/09/2025@12:00:00+0700
2025-09-22T11:45:04.803+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T11:46:03.325+07:00  INFO 16665 --- [Scheduler-661316124-1] n.d.m.session.AppHttpSessionListener     : The session node01sru0ujzpp4jfeufwqsd92hw81 is destroyed.
2025-09-22T11:46:06.911+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:46:21.959+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 1
2025-09-22T11:46:21.971+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T11:46:28.611+07:00  INFO 16665 --- [qtp1987166253-60] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint SeaPriceService/searchSeaLclTransportCharge
2025-09-22T11:46:28.611+07:00  INFO 16665 --- [qtp1987166253-72] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint SeaPriceService/searchSeaLclTransportCharge
2025-09-22T11:46:44.541+07:00  INFO 16665 --- [qtp1987166253-40] n.d.module.session.ClientSessionManager  : Add a client session id = node019jm1eustvc3v1sgqot1tvcabt0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:46:44.556+07:00  INFO 16665 --- [qtp1987166253-40] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:46:44.577+07:00  INFO 16665 --- [qtp1987166253-36] n.d.module.session.ClientSessionManager  : Add a client session id = node019jm1eustvc3v1sgqot1tvcabt0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:46:44.584+07:00  INFO 16665 --- [qtp1987166253-36] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:46:49.324+07:00  INFO 16665 --- [qtp1987166253-60] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T11:46:49.324+07:00  INFO 16665 --- [qtp1987166253-62] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T11:46:49.489+07:00  INFO 16665 --- [qtp1987166253-60] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T11:46:49.489+07:00  INFO 16665 --- [qtp1987166253-62] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T11:47:00.185+07:00  INFO 16665 --- [qtp1987166253-62] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T11:47:00.185+07:00  INFO 16665 --- [qtp1987166253-60] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T11:47:00.521+07:00  INFO 16665 --- [qtp1987166253-60] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T11:47:00.521+07:00  INFO 16665 --- [qtp1987166253-72] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T11:47:04.040+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:47:18.260+07:00 ERROR 16665 --- [qtp1987166253-65] n.d.m.monitor.call.EndpointCallContext   : Cannot find the component = class cloud.datatp.fforwarder.core.partner.CRMPartnerService, mehthod = searchBFSOnePartnerSources, userParams = 
 {
  "params" : {
    "filters" : [ {
      "name" : "search",
      "fields" : [ ],
      "filterOp" : "ilike",
      "filterValue" : "v*",
      "required" : true
    } ],
    "maxReturn" : 100
  }
}
2025-09-22T11:47:18.262+07:00 ERROR 16665 --- [qtp1987166253-65] n.d.m.monitor.call.EndpointCallService   : Unknown Error when handle the endpoint CRMPartnerService/searchBFSOnePartnerSources
2025-09-22T11:47:18.262+07:00 ERROR 16665 --- [qtp1987166253-65] n.d.m.monitor.call.EndpointCallService   : Exception: 

net.datatp.util.error.RuntimeError: Cannot find the component class cloud.datatp.fforwarder.core.partner.CRMPartnerService, mehtod searchBFSOnePartnerSources
	at net.datatp.util.error.RuntimeError.IllegalArgument(RuntimeError.java:61)
	at net.datatp.module.monitor.call.EndpointCallContext.computeArguments(EndpointCallContext.java:96)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:150)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)

2025-09-22T11:47:21.836+07:00 ERROR 16665 --- [qtp1987166253-62] n.d.m.monitor.call.EndpointCallContext   : Cannot find the component = class cloud.datatp.fforwarder.core.partner.CRMPartnerService, mehthod = searchBFSOnePartnerSources, userParams = 
 {
  "params" : {
    "filters" : [ {
      "name" : "search",
      "type" : "STRING_LIKE",
      "required" : true,
      "filterValue" : ""
    } ],
    "maxReturn" : 250
  }
}
2025-09-22T11:47:21.838+07:00 ERROR 16665 --- [qtp1987166253-62] n.d.m.monitor.call.EndpointCallService   : Unknown Error when handle the endpoint CRMPartnerService/searchBFSOnePartnerSources
2025-09-22T11:47:21.839+07:00 ERROR 16665 --- [qtp1987166253-62] n.d.m.monitor.call.EndpointCallService   : Exception: 

net.datatp.util.error.RuntimeError: Cannot find the component class cloud.datatp.fforwarder.core.partner.CRMPartnerService, mehtod searchBFSOnePartnerSources
	at net.datatp.util.error.RuntimeError.IllegalArgument(RuntimeError.java:61)
	at net.datatp.module.monitor.call.EndpointCallContext.computeArguments(EndpointCallContext.java:96)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:150)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)

2025-09-22T11:48:06.130+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:48:07.644+07:00  INFO 16665 --- [qtp1987166253-60] n.d.module.session.ClientSessionManager  : Add a client session id = node019jm1eustvc3v1sgqot1tvcabt0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:48:07.657+07:00  INFO 16665 --- [qtp1987166253-60] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:48:07.725+07:00  INFO 16665 --- [qtp1987166253-76] n.d.module.session.ClientSessionManager  : Add a client session id = node019jm1eustvc3v1sgqot1tvcabt0, token = 452afaa4f2ca51571a6931f75d2722f8
2025-09-22T11:48:07.731+07:00  INFO 16665 --- [qtp1987166253-76] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T11:48:21.184+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 8, expire count 0
2025-09-22T11:48:21.207+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-22T11:49:03.308+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:50:06.484+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:50:06.485+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T11:50:20.567+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 0
2025-09-22T11:50:20.575+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T11:51:02.716+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:52:05.914+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:52:19.961+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T11:52:19.965+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T11:53:02.095+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:54:05.275+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:54:19.325+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T11:54:19.331+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T11:55:06.476+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:55:06.478+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T11:56:04.634+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:56:18.669+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-22T11:56:18.671+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T11:57:06.790+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:58:03.968+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T11:58:18.027+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 13
2025-09-22T11:58:18.034+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T11:59:06.187+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:00:03.349+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:00:03.350+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T12:00:03.350+07:00  INFO 16665 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 22/09/2025@12:00:03+0700
2025-09-22T12:00:03.367+07:00  INFO 16665 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 22/09/2025@12:00:00+0700 to 22/09/2025@12:15:00+0700
2025-09-22T12:00:03.367+07:00  INFO 16665 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 22/09/2025@12:00:00+0700 to 22/09/2025@12:15:00+0700
2025-09-22T12:00:03.368+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-22T12:00:03.372+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-09-22T12:00:03.372+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 3 hour
2025-09-22T12:00:03.373+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at 12 PM every day
2025-09-22T12:00:22.439+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-22T12:00:22.445+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T12:01:06.580+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:02:02.722+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:02:21.780+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T12:02:21.783+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T12:03:05.904+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:04:02.082+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:04:22.136+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T12:04:22.142+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T12:05:05.285+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:05:05.287+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T12:06:06.457+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:06:22.534+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T12:06:22.541+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T12:07:04.664+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:08:06.857+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:08:21.913+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T12:08:21.915+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T12:09:04.029+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:10:06.216+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:10:06.218+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T12:10:21.268+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-22T12:10:21.274+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T12:11:03.423+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:12:06.620+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:12:20.655+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T12:12:20.658+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T12:13:02.798+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:14:05.995+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:14:20.045+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T12:14:20.052+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T12:15:02.147+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:15:02.149+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T12:15:02.149+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-22T12:15:02.150+07:00  INFO 16665 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 22/09/2025@12:15:02+0700
2025-09-22T12:15:02.162+07:00  INFO 16665 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 22/09/2025@12:15:00+0700 to 22/09/2025@12:30:00+0700
2025-09-22T12:15:02.162+07:00  INFO 16665 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 22/09/2025@12:15:00+0700 to 22/09/2025@12:30:00+0700
2025-09-22T12:16:05.347+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:16:19.392+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T12:16:19.398+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T12:17:06.526+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:18:04.717+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:18:18.760+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T12:18:18.766+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T12:19:03.694+07:00  WARN 16665 --- [jdbc housekeeper] com.zaxxer.hikari.pool.HikariPool        : jdbc - Retrograde clock change detected (housekeeper delta=29s859ms), soft-evicting connections from pool.
2025-09-22T12:19:03.716+07:00  WARN 16665 --- [rw housekeeper] com.zaxxer.hikari.pool.HikariPool        : rw - Retrograde clock change detected (housekeeper delta=29s859ms), soft-evicting connections from pool.
2025-09-22T12:19:03.739+07:00  WARN 16665 --- [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Retrograde clock change detected (housekeeper delta=29s861ms), soft-evicting connections from pool.
2025-09-22T12:19:03.755+07:00  WARN 16665 --- [HikariPool-2 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Retrograde clock change detected (housekeeper delta=29s862ms), soft-evicting connections from pool.
2025-09-22T12:19:03.857+07:00  WARN 16665 --- [rw housekeeper] com.zaxxer.hikari.pool.HikariPool        : rw - Retrograde clock change detected (housekeeper delta=29s862ms), soft-evicting connections from pool.
2025-09-22T12:19:06.758+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:20:03.938+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T12:20:03.939+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:20:17.987+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T12:20:17.989+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T12:21:06.130+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:22:03.316+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:22:22.377+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T12:22:22.384+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T12:23:06.529+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:24:02.677+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:24:21.722+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T12:24:21.724+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T12:25:05.839+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T12:25:05.840+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:26:07.015+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:26:22.063+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-22T12:26:22.070+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T12:27:05.199+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:28:06.380+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:28:22.439+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T12:28:22.445+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T12:29:04.570+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:30:06.746+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-22T12:30:06.748+07:00  INFO 16665 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 22/09/2025@12:30:06+0700
2025-09-22T12:30:06.764+07:00  INFO 16665 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 22/09/2025@12:30:00+0700 to 22/09/2025@12:45:00+0700
2025-09-22T12:30:06.764+07:00  INFO 16665 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 22/09/2025@12:30:00+0700 to 22/09/2025@12:45:00+0700
2025-09-22T12:30:06.765+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T12:30:06.765+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:30:21.820+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T12:30:21.825+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T12:31:03.937+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:32:06.106+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:32:21.174+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T12:32:21.180+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T12:33:03.324+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:34:06.502+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:34:20.550+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T12:34:20.552+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T12:35:02.669+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T12:35:02.670+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:36:05.854+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:36:19.906+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T12:36:19.911+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T12:37:02.048+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:38:05.231+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:38:19.275+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T12:38:19.277+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T12:39:06.514+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:40:04.666+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:40:04.668+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T12:40:18.713+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-22T12:40:18.722+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T12:41:06.857+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:42:04.033+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:42:18.083+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T12:42:18.086+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T12:43:06.221+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:44:03.393+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:44:22.454+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T12:44:22.459+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T12:45:06.588+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T12:45:06.589+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:45:06.590+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-22T12:45:06.590+07:00  INFO 16665 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 22/09/2025@12:45:06+0700
2025-09-22T12:45:06.602+07:00  INFO 16665 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 22/09/2025@12:45:00+0700 to 22/09/2025@13:00:00+0700
2025-09-22T12:45:06.602+07:00  INFO 16665 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 22/09/2025@12:45:00+0700 to 22/09/2025@13:00:00+0700
2025-09-22T12:46:02.771+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:46:21.809+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T12:46:21.814+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T12:47:05.953+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:48:02.103+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:48:22.145+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T12:48:22.152+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T12:49:05.269+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:50:06.447+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T12:50:06.449+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:50:22.508+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T12:50:22.516+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T12:51:04.628+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:52:06.795+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:52:21.838+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T12:52:21.842+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T12:53:03.976+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:54:06.159+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:54:21.207+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T12:54:21.212+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T12:55:03.327+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T12:55:03.328+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:56:06.533+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:56:20.567+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-22T12:56:20.569+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T12:57:02.725+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:58:05.935+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T12:58:19.985+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T12:58:19.990+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T12:59:02.134+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:00:05.336+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-09-22T13:00:05.338+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:00:05.339+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-22T13:00:05.339+07:00  INFO 16665 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 22/09/2025@13:00:05+0700
2025-09-22T13:00:05.353+07:00  INFO 16665 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 22/09/2025@13:00:00+0700 to 22/09/2025@13:15:00+0700
2025-09-22T13:00:05.353+07:00  INFO 16665 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 22/09/2025@13:00:00+0700 to 22/09/2025@13:15:00+0700
2025-09-22T13:00:05.354+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T13:00:19.397+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T13:00:19.403+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T13:01:06.544+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:02:04.759+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:02:18.805+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T13:02:18.810+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T13:03:06.944+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:04:04.118+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:04:18.155+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T13:04:18.157+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T13:05:06.302+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:05:06.303+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T13:06:03.486+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:06:22.545+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T13:06:22.549+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T13:07:06.707+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:08:02.860+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:08:21.910+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T13:08:21.912+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T13:09:06.037+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:10:02.214+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:10:02.216+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T13:10:22.278+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-22T13:10:22.287+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T13:11:05.445+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:12:06.618+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:12:21.673+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T13:12:21.675+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T13:13:04.835+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:14:06.997+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:14:22.061+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T13:14:22.068+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T13:15:04.273+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:15:04.274+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T13:15:04.275+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-22T13:15:04.275+07:00  INFO 16665 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 22/09/2025@13:15:04+0700
2025-09-22T13:15:04.285+07:00  INFO 16665 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 22/09/2025@13:15:00+0700 to 22/09/2025@13:30:00+0700
2025-09-22T13:15:04.286+07:00  INFO 16665 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 22/09/2025@13:15:00+0700 to 22/09/2025@13:30:00+0700
2025-09-22T13:16:06.499+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:16:21.554+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T13:16:21.559+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T13:17:03.702+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:18:06.897+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:18:20.954+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T13:18:20.960+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T13:19:03.066+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:20:06.243+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:20:06.244+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T13:20:20.282+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T13:20:20.284+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T13:21:02.425+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:22:05.628+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:22:19.684+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T13:22:19.690+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T13:23:06.804+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:24:04.990+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:24:19.042+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T13:24:19.044+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T13:25:06.192+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:25:06.192+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T13:26:04.390+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:26:18.427+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-22T13:26:18.431+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T13:27:06.580+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:28:03.775+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:28:17.822+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T13:28:17.824+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T13:29:06.996+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:30:03.155+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:30:03.157+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T13:30:03.157+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-22T13:30:03.158+07:00  INFO 16665 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 22/09/2025@13:30:03+0700
2025-09-22T13:30:03.171+07:00  INFO 16665 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 22/09/2025@13:30:00+0700 to 22/09/2025@13:45:00+0700
2025-09-22T13:30:03.171+07:00  INFO 16665 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 22/09/2025@13:30:00+0700 to 22/09/2025@13:45:00+0700
2025-09-22T13:30:22.219+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-22T13:30:22.224+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T13:31:06.338+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:32:02.512+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:32:22.577+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T13:32:22.582+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T13:33:05.693+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:34:06.884+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:34:21.927+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T13:34:21.929+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T13:35:05.087+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:35:05.089+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T13:36:06.270+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:36:22.322+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T13:36:22.326+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T13:37:04.470+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:38:06.649+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:38:21.713+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T13:38:21.715+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T13:39:04.267+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:40:06.364+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:40:06.368+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T13:40:21.419+07:00  INFO 16665 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-22T13:40:21.431+07:00  INFO 16665 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T13:41:03.502+07:00  INFO 16665 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:41:14.020+07:00  INFO 16665 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@19218ca8{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-22T13:41:14.021+07:00  INFO 16665 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-22T13:41:14.021+07:00  INFO 16665 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-22T13:41:14.021+07:00  INFO 16665 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-22T13:41:14.021+07:00  INFO 16665 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-22T13:41:14.021+07:00  INFO 16665 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-22T13:41:14.021+07:00  INFO 16665 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-22T13:41:14.021+07:00  INFO 16665 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-22T13:41:14.021+07:00  INFO 16665 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-09-22T13:41:14.021+07:00  INFO 16665 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-09-22T13:41:14.021+07:00  INFO 16665 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-22T13:41:14.021+07:00  INFO 16665 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-09-22T13:41:14.021+07:00  INFO 16665 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-09-22T13:41:14.021+07:00  INFO 16665 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-22T13:41:14.021+07:00  INFO 16665 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-09-22T13:41:14.021+07:00  INFO 16665 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-09-22T13:41:14.040+07:00  INFO 16665 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T13:41:14.158+07:00  INFO 16665 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-22T13:41:14.163+07:00  INFO 16665 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-22T13:41:14.181+07:00  INFO 16665 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-22T13:41:14.182+07:00  INFO 16665 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-22T13:41:14.183+07:00  INFO 16665 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-22T13:41:14.183+07:00  INFO 16665 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-22T13:41:14.184+07:00  INFO 16665 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-22T13:41:14.184+07:00  INFO 16665 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-22T13:41:14.184+07:00  INFO 16665 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-22T13:41:14.184+07:00  INFO 16665 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-22T13:41:14.184+07:00  INFO 16665 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-22T13:41:14.184+07:00  INFO 16665 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-22T13:41:14.184+07:00  INFO 16665 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-22T13:41:14.184+07:00  INFO 16665 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-22T13:41:14.185+07:00  INFO 16665 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-22T13:41:14.186+07:00  INFO 16665 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@4f5080ea{STOPPING}[12.0.15,sto=0]
2025-09-22T13:41:14.188+07:00  INFO 16665 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-22T13:41:14.190+07:00  INFO 16665 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@10ecdd85{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.17329618869251456460/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@47ffce5d{STOPPED}}
