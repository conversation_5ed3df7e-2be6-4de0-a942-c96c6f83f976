2025-09-22T13:42:55.343+07:00  INFO 28833 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 28833 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-22T13:42:55.343+07:00  INFO 28833 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-22T13:42:56.084+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T13:42:56.154+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 67 ms. Found 22 JPA repository interfaces.
2025-09-22T13:42:56.164+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T13:42:56.166+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-22T13:42:56.166+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T13:42:56.211+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 44 ms. Found 10 JPA repository interfaces.
2025-09-22T13:42:56.212+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T13:42:56.215+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 3 JPA repository interfaces.
2025-09-22T13:42:56.224+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T13:42:56.229+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-09-22T13:42:56.238+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T13:42:56.240+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-22T13:42:56.241+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T13:42:56.244+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-22T13:42:56.247+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T13:42:56.252+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 5 JPA repository interfaces.
2025-09-22T13:42:56.256+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T13:42:56.259+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-22T13:42:56.259+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T13:42:56.260+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-22T13:42:56.260+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T13:42:56.266+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 10 JPA repository interfaces.
2025-09-22T13:42:56.271+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T13:42:56.274+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-22T13:42:56.277+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T13:42:56.281+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-22T13:42:56.281+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T13:42:56.288+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-22T13:42:56.288+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T13:42:56.291+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-22T13:42:56.291+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T13:42:56.291+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-22T13:42:56.291+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T13:42:56.292+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-22T13:42:56.293+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T13:42:56.297+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-09-22T13:42:56.297+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T13:42:56.299+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-22T13:42:56.299+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T13:42:56.299+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-22T13:42:56.299+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T13:42:56.310+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 19 JPA repository interfaces.
2025-09-22T13:42:56.321+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T13:42:56.327+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 8 JPA repository interfaces.
2025-09-22T13:42:56.328+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T13:42:56.330+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-09-22T13:42:56.331+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T13:42:56.335+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-09-22T13:42:56.335+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T13:42:56.341+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-09-22T13:42:56.341+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T13:42:56.346+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-09-22T13:42:56.346+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T13:42:56.355+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 13 JPA repository interfaces.
2025-09-22T13:42:56.356+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T13:42:56.365+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 14 JPA repository interfaces.
2025-09-22T13:42:56.366+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T13:42:56.382+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 15 ms. Found 24 JPA repository interfaces.
2025-09-22T13:42:56.382+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T13:42:56.383+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-22T13:42:56.388+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T13:42:56.389+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-22T13:42:56.389+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T13:42:56.396+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-22T13:42:56.398+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T13:42:56.435+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 36 ms. Found 66 JPA repository interfaces.
2025-09-22T13:42:56.435+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T13:42:56.437+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-22T13:42:56.442+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T13:42:56.445+07:00  INFO 28833 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-09-22T13:42:56.645+07:00  INFO 28833 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-22T13:42:56.649+07:00  INFO 28833 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-22T13:42:56.930+07:00  WARN 28833 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-22T13:42:57.136+07:00  INFO 28833 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-22T13:42:57.138+07:00  INFO 28833 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-22T13:42:57.150+07:00  INFO 28833 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-22T13:42:57.150+07:00  INFO 28833 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1692 ms
2025-09-22T13:42:57.208+07:00  WARN 28833 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-22T13:42:57.208+07:00  INFO 28833 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-22T13:42:57.317+07:00  INFO 28833 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@5a318745
2025-09-22T13:42:57.318+07:00  INFO 28833 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-22T13:42:57.322+07:00  WARN 28833 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-22T13:42:57.323+07:00  INFO 28833 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-22T13:42:57.329+07:00  INFO 28833 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@6e057374
2025-09-22T13:42:57.329+07:00  INFO 28833 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-22T13:42:57.330+07:00  WARN 28833 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-22T13:42:57.330+07:00  INFO 28833 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-22T13:42:57.337+07:00  INFO 28833 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@511e0453
2025-09-22T13:42:57.337+07:00  INFO 28833 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-22T13:42:57.338+07:00  WARN 28833 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-22T13:42:57.338+07:00  INFO 28833 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-22T13:42:57.346+07:00  INFO 28833 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@130901fd
2025-09-22T13:42:57.346+07:00  INFO 28833 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-22T13:42:57.347+07:00  WARN 28833 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-22T13:42:57.347+07:00  INFO 28833 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-22T13:42:57.355+07:00  INFO 28833 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@73ec417b
2025-09-22T13:42:57.356+07:00  INFO 28833 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-22T13:42:57.356+07:00  INFO 28833 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-22T13:42:57.401+07:00  INFO 28833 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-22T13:42:57.453+07:00  INFO 28833 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@10ecdd85{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.13257594048207443015/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@47ffce5d{STARTED}}
2025-09-22T13:42:57.453+07:00  INFO 28833 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@10ecdd85{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.13257594048207443015/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@47ffce5d{STARTED}}
2025-09-22T13:42:57.456+07:00  INFO 28833 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@4f5080ea{STARTING}[12.0.15,sto=0] @2731ms
2025-09-22T13:42:57.511+07:00  INFO 28833 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-22T13:42:57.539+07:00  INFO 28833 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-22T13:42:57.556+07:00  INFO 28833 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-22T13:42:57.681+07:00  INFO 28833 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-22T13:42:57.720+07:00  WARN 28833 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-22T13:42:58.360+07:00  INFO 28833 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-22T13:42:58.369+07:00  INFO 28833 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@6ed68a2e] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-22T13:42:58.491+07:00  INFO 28833 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-22T13:42:58.692+07:00  INFO 28833 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.graphapi", "net.datatp.module.kpi", "net.datatp.module.monitor.activity", "net.datatp.module.wfms", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "cloud.datatp.module.fleet", "net.datatp.module.okr", "net.datatp.module.core.template", "cloud.datatp.module.pegasus.shipment", "cloud.datatp.gps", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "cloud.datatp.tms", "net.datatp.module.company.tmpl", "net.datatp.module.groovy", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "net.datatp.module.resource", "cloud.datatp.vendor", "net.datatp.module.hr", "net.datatp.module.company.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "cloud.datatp.module.odoo", "net.datatp.module.core.print", "net.datatp.module.chatbot", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.zalo", "net.datatp.module.accounting" ]
2025-09-22T13:42:58.694+07:00  INFO 28833 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-22T13:42:58.700+07:00  INFO 28833 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-22T13:42:58.702+07:00  INFO 28833 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-22T13:42:58.728+07:00  INFO 28833 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-22T13:42:58.733+07:00  WARN 28833 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-22T13:43:00.920+07:00  INFO 28833 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-22T13:43:00.920+07:00  INFO 28833 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@6bc2d0c4] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-22T13:43:01.090+07:00  WARN 28833 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-22T13:43:01.091+07:00  WARN 28833 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-22T13:43:01.098+07:00  WARN 28833 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-22T13:43:01.098+07:00  WARN 28833 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-22T13:43:01.112+07:00  WARN 28833 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-22T13:43:01.112+07:00  WARN 28833 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-22T13:43:01.489+07:00  INFO 28833 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-22T13:43:01.495+07:00  INFO 28833 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-22T13:43:01.496+07:00  INFO 28833 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-22T13:43:01.516+07:00  INFO 28833 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-22T13:43:01.520+07:00  WARN 28833 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-22T13:43:02.078+07:00  INFO 28833 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-22T13:43:02.079+07:00  INFO 28833 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@8dd564b] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-22T13:43:02.134+07:00  WARN 28833 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-22T13:43:02.134+07:00  WARN 28833 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-22T13:43:02.435+07:00  INFO 28833 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-22T13:43:02.466+07:00  INFO 28833 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-22T13:43:02.471+07:00  INFO 28833 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-22T13:43:02.471+07:00  INFO 28833 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T13:43:02.478+07:00  WARN 28833 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-22T13:43:02.624+07:00  INFO 28833 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-22T13:43:03.127+07:00  INFO 28833 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-22T13:43:03.130+07:00  INFO 28833 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-22T13:43:03.171+07:00  INFO 28833 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-22T13:43:03.221+07:00  INFO 28833 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-22T13:43:03.292+07:00  INFO 28833 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-22T13:43:03.325+07:00  INFO 28833 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-22T13:43:03.349+07:00  INFO 28833 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 590646523ms : this is harmless.
2025-09-22T13:43:03.359+07:00  INFO 28833 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-22T13:43:03.362+07:00  INFO 28833 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-22T13:43:03.382+07:00  INFO 28833 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 882152477ms : this is harmless.
2025-09-22T13:43:03.383+07:00  INFO 28833 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-22T13:43:03.396+07:00  INFO 28833 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-22T13:43:03.397+07:00  INFO 28833 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-22T13:43:05.795+07:00  INFO 28833 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-22T13:43:05.795+07:00  INFO 28833 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T13:43:05.796+07:00  WARN 28833 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-22T13:43:06.134+07:00  INFO 28833 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 22/09/2025@13:30:00+0700 to 22/09/2025@13:45:00+0700
2025-09-22T13:43:06.134+07:00  INFO 28833 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 22/09/2025@13:30:00+0700 to 22/09/2025@13:45:00+0700
2025-09-22T13:43:06.852+07:00  INFO 28833 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-09-22T13:43:06.853+07:00  INFO 28833 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T13:43:06.853+07:00  WARN 28833 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-22T13:43:07.181+07:00  INFO 28833 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-09-22T13:43:07.182+07:00  INFO 28833 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-09-22T13:43:07.182+07:00  INFO 28833 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-09-22T13:43:07.182+07:00  INFO 28833 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-09-22T13:43:07.182+07:00  INFO 28833 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-09-22T13:43:09.667+07:00  WARN 28833 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: c372dc95-4be5-41f5-9850-3203793a88d5

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-22T13:43:09.671+07:00  INFO 28833 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-09-22T13:43:10.042+07:00  INFO 28833 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-22T13:43:10.042+07:00  INFO 28833 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-22T13:43:10.042+07:00  INFO 28833 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-22T13:43:10.042+07:00  INFO 28833 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-22T13:43:10.042+07:00  INFO 28833 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-09-22T13:43:10.042+07:00  INFO 28833 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-09-22T13:43:10.042+07:00  INFO 28833 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-22T13:43:10.042+07:00  INFO 28833 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-09-22T13:43:10.042+07:00  INFO 28833 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-09-22T13:43:10.042+07:00  INFO 28833 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-22T13:43:10.042+07:00  INFO 28833 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-09-22T13:43:10.042+07:00  INFO 28833 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-09-22T13:43:10.045+07:00  INFO 28833 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-22T13:43:10.045+07:00  INFO 28833 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-22T13:43:10.045+07:00  INFO 28833 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-22T13:43:10.094+07:00  INFO 28833 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-22T13:43:10.094+07:00  INFO 28833 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-22T13:43:10.096+07:00  INFO 28833 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-09-22T13:43:10.103+07:00  INFO 28833 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@71cd4087{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-22T13:43:10.104+07:00  INFO 28833 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-09-22T13:43:10.105+07:00  INFO 28833 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-09-22T13:43:10.136+07:00  INFO 28833 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-09-22T13:43:10.136+07:00  INFO 28833 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-09-22T13:43:10.143+07:00  INFO 28833 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 15.098 seconds (process running for 15.418)
2025-09-22T13:43:10.947+07:00  INFO 28833 --- [qtp1*********-36] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node018rvyhkcteusg84p7s799m5fv0
2025-09-22T13:43:11.419+07:00  INFO 28833 --- [qtp1*********-36] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T13:43:11.784+07:00  INFO 28833 --- [qtp1*********-36] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T13:43:15.741+07:00  INFO 28833 --- [qtp1*********-36] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T13:43:15.744+07:00  INFO 28833 --- [qtp1*********-36] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T13:43:15.768+07:00  INFO 28833 --- [qtp1*********-60] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T13:43:15.788+07:00  INFO 28833 --- [qtp1*********-60] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T13:43:17.964+07:00  INFO 28833 --- [qtp1*********-61] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T13:43:17.964+07:00  INFO 28833 --- [qtp1*********-36] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T13:43:18.042+07:00  INFO 28833 --- [qtp1*********-36] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T13:43:18.057+07:00  INFO 28833 --- [qtp1*********-61] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T13:43:24.639+07:00  INFO 28833 --- [qtp1*********-61] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T13:43:28.419+07:00  INFO 28833 --- [qtp1*********-61] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T13:43:32.075+07:00  INFO 28833 --- [qtp1*********-61] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T13:44:00.812+07:00  INFO 28833 --- [qtp1*********-60] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T13:44:00.831+07:00  INFO 28833 --- [qtp1*********-60] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T13:44:00.870+07:00  INFO 28833 --- [qtp1*********-39] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T13:44:00.885+07:00  INFO 28833 --- [qtp1*********-39] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T13:44:02.879+07:00  INFO 28833 --- [qtp1*********-60] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T13:44:02.896+07:00  INFO 28833 --- [qtp1*********-60] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T13:44:02.993+07:00  INFO 28833 --- [qtp1*********-62] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T13:44:02.998+07:00  INFO 28833 --- [qtp1*********-62] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T13:44:06.151+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:44:13.209+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-22T13:44:13.246+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T13:44:15.113+07:00  INFO 28833 --- [qtp1*********-39] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T13:44:15.120+07:00  INFO 28833 --- [qtp1*********-36] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T13:44:15.133+07:00  INFO 28833 --- [qtp1*********-39] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T13:44:15.136+07:00  INFO 28833 --- [qtp1*********-36] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T13:44:24.433+07:00  INFO 28833 --- [qtp1*********-60] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T13:44:24.440+07:00  INFO 28833 --- [qtp1*********-60] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T13:44:24.459+07:00  INFO 28833 --- [qtp1*********-61] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T13:44:24.462+07:00  INFO 28833 --- [qtp1*********-61] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T13:44:39.323+07:00  INFO 28833 --- [qtp1*********-40] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T13:44:39.324+07:00  INFO 28833 --- [qtp1*********-41] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T13:44:39.339+07:00  INFO 28833 --- [qtp1*********-40] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T13:44:39.339+07:00  INFO 28833 --- [qtp1*********-41] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T13:45:02.346+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:45:02.353+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-22T13:45:02.362+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T13:45:02.365+07:00  INFO 28833 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 22/09/2025@13:45:02+0700
2025-09-22T13:45:02.382+07:00  INFO 28833 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 22/09/2025@13:45:00+0700 to 22/09/2025@14:00:00+0700
2025-09-22T13:45:02.382+07:00  INFO 28833 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 22/09/2025@13:45:00+0700 to 22/09/2025@14:00:00+0700
2025-09-22T13:45:38.341+07:00  INFO 28833 --- [qtp1*********-39] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T13:45:38.343+07:00  INFO 28833 --- [qtp1*********-61] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T13:45:38.353+07:00  INFO 28833 --- [qtp1*********-61] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T13:45:38.353+07:00  INFO 28833 --- [qtp1*********-39] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T13:45:44.489+07:00  INFO 28833 --- [qtp1*********-61] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T13:45:44.504+07:00  INFO 28833 --- [qtp1*********-61] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T13:45:44.523+07:00  INFO 28833 --- [qtp1*********-66] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T13:45:44.540+07:00  INFO 28833 --- [qtp1*********-66] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T13:46:02.513+07:00  INFO 28833 --- [qtp1*********-61] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T13:46:02.531+07:00  INFO 28833 --- [qtp1*********-61] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T13:46:02.549+07:00  INFO 28833 --- [qtp1*********-35] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T13:46:02.556+07:00  INFO 28833 --- [qtp1*********-35] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T13:46:05.547+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:46:12.583+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 9, expire count 0
2025-09-22T13:46:12.637+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-22T13:46:30.621+07:00  INFO 28833 --- [qtp1*********-61] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T13:46:30.623+07:00  INFO 28833 --- [qtp1*********-39] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T13:46:30.635+07:00  INFO 28833 --- [qtp1*********-61] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T13:46:30.634+07:00  INFO 28833 --- [qtp1*********-39] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T13:46:34.604+07:00  INFO 28833 --- [qtp1*********-41] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T13:46:34.606+07:00  INFO 28833 --- [qtp1*********-62] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T13:46:34.618+07:00  INFO 28833 --- [qtp1*********-62] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T13:46:34.618+07:00  INFO 28833 --- [qtp1*********-41] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T13:46:48.539+07:00  INFO 28833 --- [qtp1*********-60] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T13:46:48.558+07:00  INFO 28833 --- [qtp1*********-60] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T13:46:48.584+07:00  INFO 28833 --- [qtp1*********-71] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T13:46:48.589+07:00  INFO 28833 --- [qtp1*********-71] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T13:47:06.725+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:47:09.630+07:00  INFO 28833 --- [qtp1*********-36] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T13:47:09.641+07:00  INFO 28833 --- [qtp1*********-36] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T13:47:09.671+07:00  INFO 28833 --- [qtp1*********-37] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T13:47:09.689+07:00  INFO 28833 --- [qtp1*********-37] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T13:47:16.498+07:00  INFO 28833 --- [qtp1*********-40] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T13:47:16.499+07:00  INFO 28833 --- [qtp1*********-37] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T13:47:16.507+07:00  INFO 28833 --- [qtp1*********-40] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T13:47:16.507+07:00  INFO 28833 --- [qtp1*********-37] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T13:47:23.527+07:00  INFO 28833 --- [qtp1*********-71] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T13:47:23.529+07:00  INFO 28833 --- [qtp1*********-36] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T13:47:23.544+07:00  INFO 28833 --- [qtp1*********-71] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T13:47:23.544+07:00  INFO 28833 --- [qtp1*********-36] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T13:47:35.529+07:00  INFO 28833 --- [qtp1*********-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T13:47:35.529+07:00  INFO 28833 --- [qtp1*********-39] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T13:47:35.541+07:00  INFO 28833 --- [qtp1*********-37] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T13:47:35.562+07:00  INFO 28833 --- [qtp1*********-39] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T13:47:37.638+07:00  INFO 28833 --- [qtp1*********-39] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T13:47:37.640+07:00  INFO 28833 --- [qtp1*********-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T13:47:38.702+07:00  INFO 28833 --- [qtp1*********-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T13:47:38.737+07:00  INFO 28833 --- [qtp1*********-39] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T13:47:53.803+07:00  INFO 28833 --- [qtp1*********-62] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T13:47:53.806+07:00  INFO 28833 --- [qtp1*********-66] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T13:47:53.816+07:00  INFO 28833 --- [qtp1*********-66] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T13:47:53.816+07:00  INFO 28833 --- [qtp1*********-62] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T13:48:04.847+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:48:08.159+07:00  INFO 28833 --- [qtp1*********-40] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T13:48:08.159+07:00  INFO 28833 --- [qtp1*********-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T13:48:08.165+07:00  INFO 28833 --- [qtp1*********-40] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T13:48:08.165+07:00  INFO 28833 --- [qtp1*********-37] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T13:48:13.254+07:00  INFO 28833 --- [qtp1*********-37] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T13:48:15.755+07:00  INFO 28833 --- [qtp1*********-61] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T13:48:16.890+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 13, expire count 0
2025-09-22T13:48:16.900+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-22T13:48:24.506+07:00  INFO 28833 --- [qtp1*********-61] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T13:49:04.764+07:00  INFO 28833 --- [qtp1*********-61] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T13:49:06.979+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:49:10.921+07:00  INFO 28833 --- [qtp1*********-73] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T13:49:13.799+07:00  INFO 28833 --- [qtp1*********-37] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T13:49:46.989+07:00  INFO 28833 --- [qtp1*********-36] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T13:49:46.990+07:00  INFO 28833 --- [qtp1*********-61] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T13:49:47.000+07:00  INFO 28833 --- [qtp1*********-61] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T13:49:47.000+07:00  INFO 28833 --- [qtp1*********-36] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T13:49:49.688+07:00  INFO 28833 --- [qtp1*********-60] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T13:49:49.694+07:00  INFO 28833 --- [qtp1*********-60] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T13:49:49.746+07:00  INFO 28833 --- [qtp1*********-73] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T13:49:49.757+07:00  INFO 28833 --- [qtp1*********-73] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T13:49:49.842+07:00 ERROR 28833 --- [qtp1*********-61] n.d.m.monitor.call.EndpointCallContext   : Start call with component EntityTaskService, method searchEntityTasks, arguments
[ {
  "tenantId" : "default",
  "companyId" : 4,
  "companyParentId" : null,
  "companyCode" : "bee",
  "companyLabel" : "Bee Corp",
  "companyFullName" : "BEE LOGISTICS CORPORATION",
  "loginId" : "hanah.vnhph",
  "accountId" : 11557,
  "token" : "858f997ddf07ff57af7a0837d9dd5a42",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node018rvyhkcteusg84p7s799m5fv0",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : {
    "bee" : {
      "companyId" : 4,
      "companyParentId" : 0,
      "companyCode" : "bee",
      "companyLabel" : "Bee Corp",
      "companyFullName" : null
    }
  },
  "featurePermissions" : [ {
    "id" : 15097,
    "appId" : 3,
    "appModule" : "project",
    "appName" : "project",
    "companyId" : 4,
    "loginId" : "hanah.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 2700,
    "appId" : 26,
    "appModule" : "okr",
    "appName" : "okr",
    "companyId" : 4,
    "loginId" : "hanah.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 3735,
    "appId" : 12,
    "appModule" : "spreadsheet",
    "appName" : "spreadsheet",
    "companyId" : 4,
    "loginId" : "hanah.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 11538,
    "appId" : 69,
    "appModule" : "hr",
    "appName" : "user-kpi",
    "companyId" : 4,
    "loginId" : "hanah.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 14757,
    "appId" : 79,
    "appModule" : "company",
    "appName" : "user-asset",
    "companyId" : 4,
    "loginId" : "hanah.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 10508,
    "appId" : 16,
    "appModule" : "logistics",
    "appName" : "user-logistics-sales",
    "companyId" : 4,
    "loginId" : "hanah.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 15103,
    "appId" : 6,
    "appModule" : "company",
    "appName" : "company-space",
    "companyId" : 4,
    "loginId" : "hanah.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Read"
  }, {
    "id" : 6210,
    "appId" : 2,
    "appModule" : "user",
    "appName" : "my-employee-space",
    "companyId" : 4,
    "loginId" : "hanah.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  } ],
  "attributes" : { },
  "clientId" : "default:hanah.vnhph"
}, null, {
  "params" : {
    "accountId" : 11557
  },
  "filters" : [ {
    "name" : "search",
    "filterType" : "String",
    "filterValue" : "",
    "required" : true
  } ],
  "optionFilters" : [ {
    "name" : "storageState",
    "filterType" : "NotSet",
    "required" : true,
    "multiple" : true,
    "options" : [ "ACTIVE", "ARCHIVED" ],
    "selectOptions" : [ "ACTIVE" ]
  } ],
  "rangeFilters" : [ {
    "name" : "createdTime",
    "filterType" : "NotSet",
    "required" : true
  }, {
    "name" : "modifiedTime",
    "filterType" : "NotSet",
    "required" : true
  } ],
  "orderBy" : {
    "fields" : [ "modifiedTime" ],
    "selectFields" : [ ],
    "sort" : "DESC"
  },
  "maxReturn" : 5000
} ]
2025-09-22T13:49:49.844+07:00 ERROR 28833 --- [qtp1*********-61] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:115)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Cannot invoke "net.datatp.module.data.db.entity.ICompany.getId()" because "company" is null
	at net.datatp.module.wfms.EntityTaskService.searchEntityTasks(EntityTaskService.java:34)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.wfms.EntityTaskService$$SpringCGLIB$$0.searchEntityTasks(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted

2025-09-22T13:49:49.893+07:00  INFO 28833 --- [qtp1*********-61] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint EntityTaskService/searchEntityTasks
2025-09-22T13:49:57.796+07:00  INFO 28833 --- [qtp1*********-73] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T13:49:57.797+07:00  INFO 28833 --- [qtp1*********-60] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T13:49:57.802+07:00  INFO 28833 --- [qtp1*********-73] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T13:49:57.802+07:00  INFO 28833 --- [qtp1*********-60] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T13:49:59.901+07:00  INFO 28833 --- [qtp1*********-41] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T13:49:59.901+07:00  INFO 28833 --- [qtp1*********-36] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T13:49:59.910+07:00  INFO 28833 --- [qtp1*********-41] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T13:49:59.910+07:00  INFO 28833 --- [qtp1*********-36] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T13:50:04.069+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:50:04.070+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T13:50:08.517+07:00  INFO 28833 --- [qtp1*********-37] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T13:50:11.001+07:00  INFO 28833 --- [qtp1*********-69] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T13:50:14.227+07:00  INFO 28833 --- [qtp1*********-41] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T13:50:17.103+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 11, expire count 0
2025-09-22T13:50:17.112+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-22T13:50:30.099+07:00  INFO 28833 --- [qtp1*********-41] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T13:50:30.100+07:00  INFO 28833 --- [qtp1*********-39] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T13:50:30.112+07:00  INFO 28833 --- [qtp1*********-41] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T13:50:30.112+07:00  INFO 28833 --- [qtp1*********-39] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T13:50:36.344+07:00  INFO 28833 --- [qtp1*********-41] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T13:50:36.344+07:00  INFO 28833 --- [qtp1*********-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T13:50:36.348+07:00  INFO 28833 --- [qtp1*********-37] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T13:50:36.349+07:00  INFO 28833 --- [qtp1*********-41] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T13:50:42.425+07:00  INFO 28833 --- [qtp1*********-60] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T13:50:48.400+07:00  INFO 28833 --- [qtp1*********-37] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T13:51:06.203+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:52:03.308+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:52:16.370+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-22T13:52:16.378+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T13:53:06.477+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:54:02.674+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:54:16.756+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 9, expire count 1
2025-09-22T13:54:16.772+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-22T13:55:05.900+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:55:05.902+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T13:56:02.092+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:56:16.129+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-22T13:56:16.132+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T13:57:05.287+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:58:06.465+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T13:58:15.514+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-22T13:58:15.520+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T13:59:04.609+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:00:06.766+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T14:00:06.768+07:00  INFO 28833 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 22/09/2025@14:00:06+0700
2025-09-22T14:00:06.818+07:00  INFO 28833 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 22/09/2025@14:00:00+0700 to 22/09/2025@14:15:00+0700
2025-09-22T14:00:06.819+07:00  INFO 28833 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 22/09/2025@14:00:00+0700 to 22/09/2025@14:15:00+0700
2025-09-22T14:00:06.819+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-22T14:00:06.821+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at 14 PM every day
2025-09-22T14:00:06.821+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-09-22T14:00:06.822+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:00:14.852+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 3
2025-09-22T14:00:14.857+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T14:01:04.010+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:02:06.165+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:02:14.204+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 9
2025-09-22T14:02:14.209+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T14:03:03.332+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:04:06.549+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:04:13.574+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T14:04:13.577+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T14:05:02.731+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:05:02.732+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T14:06:05.926+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:06:12.955+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T14:06:12.963+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T14:07:02.128+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:08:05.323+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:08:12.334+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T14:08:12.336+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T14:09:06.495+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:10:04.705+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:10:04.707+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T14:10:16.741+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-22T14:10:16.753+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T14:11:06.911+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:12:04.102+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:12:16.123+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T14:12:16.125+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T14:13:06.266+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:14:03.440+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:14:16.481+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T14:14:16.488+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T14:15:06.647+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:15:06.648+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T14:15:06.648+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-22T14:15:06.649+07:00  INFO 28833 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 22/09/2025@14:15:06+0700
2025-09-22T14:15:06.661+07:00  INFO 28833 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 22/09/2025@14:15:00+0700 to 22/09/2025@14:30:00+0700
2025-09-22T14:15:06.662+07:00  INFO 28833 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 22/09/2025@14:15:00+0700 to 22/09/2025@14:30:00+0700
2025-09-22T14:16:02.834+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:16:16.893+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-22T14:16:16.899+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T14:17:06.135+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:18:02.299+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:18:16.342+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T14:18:16.344+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T14:19:05.499+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:20:06.678+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:20:06.680+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T14:20:15.719+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T14:20:15.724+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T14:21:04.865+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:22:06.039+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:22:15.079+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T14:22:15.082+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T14:23:04.217+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:24:06.415+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:24:14.457+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T14:24:14.462+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T14:25:03.608+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:25:03.610+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T14:26:06.829+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:26:13.853+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-22T14:26:13.855+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T14:27:03.039+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:28:06.260+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:28:13.305+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T14:28:13.312+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T14:29:02.459+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:30:05.643+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:30:05.645+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T14:30:05.646+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-22T14:30:05.646+07:00  INFO 28833 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 22/09/2025@14:30:05+0700
2025-09-22T14:30:05.658+07:00  INFO 28833 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 22/09/2025@14:30:00+0700 to 22/09/2025@14:45:00+0700
2025-09-22T14:30:05.658+07:00  INFO 28833 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 22/09/2025@14:30:00+0700 to 22/09/2025@14:45:00+0700
2025-09-22T14:30:12.670+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T14:30:12.675+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T14:31:06.834+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:32:05.016+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:32:17.056+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T14:32:17.063+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T14:33:06.223+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:34:04.401+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:34:16.436+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T14:34:16.438+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T14:35:06.621+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:35:06.623+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T14:36:03.774+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:36:16.825+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T14:36:16.830+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T14:37:06.988+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:38:03.139+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:38:17.194+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T14:38:17.198+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T14:39:06.350+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:40:02.532+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:40:02.534+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T14:40:16.567+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-22T14:40:16.569+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T14:41:05.733+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:42:06.904+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:42:15.957+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T14:42:15.962+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T14:43:05.123+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:44:06.307+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:44:15.359+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T14:44:15.360+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T14:45:04.514+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T14:45:04.515+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-22T14:45:04.516+07:00  INFO 28833 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 22/09/2025@14:45:04+0700
2025-09-22T14:45:04.531+07:00  INFO 28833 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 22/09/2025@14:45:00+0700 to 22/09/2025@15:00:00+0700
2025-09-22T14:45:04.532+07:00  INFO 28833 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 22/09/2025@14:45:00+0700 to 22/09/2025@15:00:00+0700
2025-09-22T14:45:04.532+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:46:06.761+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:46:14.793+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-22T14:46:14.801+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T14:47:03.973+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:48:06.143+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:48:14.179+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T14:48:14.181+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T14:49:03.333+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:50:06.519+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:50:06.521+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T14:50:13.551+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T14:50:13.555+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T14:51:02.695+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:52:05.895+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:52:12.921+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T14:52:12.924+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T14:53:02.080+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:54:05.272+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:54:12.307+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T14:54:12.318+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T14:55:06.489+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:55:06.490+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T14:56:04.676+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:56:16.709+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-22T14:56:16.714+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T14:57:06.856+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:58:04.068+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T14:58:17.129+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T14:58:17.134+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T14:59:06.287+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:00:03.459+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:00:03.460+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T15:00:03.460+07:00  INFO 28833 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 22/09/2025@15:00:03+0700
2025-09-22T15:00:03.473+07:00  INFO 28833 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 22/09/2025@15:00:00+0700 to 22/09/2025@15:15:00+0700
2025-09-22T15:00:03.474+07:00  INFO 28833 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 22/09/2025@15:00:00+0700 to 22/09/2025@15:15:00+0700
2025-09-22T15:00:03.474+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-22T15:00:03.477+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 3 hour
2025-09-22T15:00:03.477+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at 15 PM every day
2025-09-22T15:00:03.478+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-09-22T15:00:16.512+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T15:00:16.519+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T15:01:06.709+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:02:02.881+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:02:16.931+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T15:02:16.937+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T15:03:06.102+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:04:02.241+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:04:16.288+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T15:04:16.291+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T15:05:05.451+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:05:05.453+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T15:06:06.631+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:06:15.674+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T15:06:15.681+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T15:07:04.834+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:08:07.017+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:08:15.044+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T15:08:15.046+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T15:09:04.211+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:10:06.397+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:10:06.399+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T15:10:14.430+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-22T15:10:14.435+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T15:11:03.577+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:12:06.768+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:12:13.788+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T15:12:13.790+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T15:13:02.922+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:14:06.133+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:14:13.161+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T15:14:13.166+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T15:15:02.324+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:15:02.326+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T15:15:02.327+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-22T15:15:02.327+07:00  INFO 28833 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 22/09/2025@15:15:02+0700
2025-09-22T15:15:02.346+07:00  INFO 28833 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 22/09/2025@15:15:00+0700 to 22/09/2025@15:30:00+0700
2025-09-22T15:15:02.346+07:00  INFO 28833 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 22/09/2025@15:15:00+0700 to 22/09/2025@15:30:00+0700
2025-09-22T15:16:05.546+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:16:12.578+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T15:16:12.582+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T15:17:06.729+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:18:04.944+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:18:16.986+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T15:18:16.991+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T15:19:06.130+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:20:04.299+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:20:04.301+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T15:20:16.330+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T15:20:16.335+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T15:21:06.456+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:22:03.649+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:22:16.712+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T15:22:16.721+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T15:23:06.861+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:24:03.036+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:24:17.087+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T15:24:17.093+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T15:25:06.217+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:25:06.219+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T15:26:02.397+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:26:16.455+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-22T15:26:16.457+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T15:27:05.625+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:28:06.824+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:28:15.870+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T15:28:15.878+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T15:29:05.028+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:30:06.238+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:30:06.239+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T15:30:06.239+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-22T15:30:06.240+07:00  INFO 28833 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 22/09/2025@15:30:06+0700
2025-09-22T15:30:06.256+07:00  INFO 28833 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 22/09/2025@15:30:00+0700 to 22/09/2025@15:45:00+0700
2025-09-22T15:30:06.256+07:00  INFO 28833 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 22/09/2025@15:30:00+0700 to 22/09/2025@15:45:00+0700
2025-09-22T15:30:15.653+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T15:30:15.724+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T15:31:04.804+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:31:42.678+07:00  INFO 28833 --- [qtp1*********-34] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T15:31:42.692+07:00  INFO 28833 --- [qtp1*********-388] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T15:31:42.806+07:00  INFO 28833 --- [qtp1*********-34] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T15:31:42.817+07:00  INFO 28833 --- [qtp1*********-388] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T15:32:06.914+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:32:14.948+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-22T15:32:14.957+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T15:33:04.035+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:34:06.109+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:34:14.124+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T15:34:14.138+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T15:35:03.211+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T15:35:03.212+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:36:06.310+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:36:13.341+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T15:36:13.348+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T15:37:02.420+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:38:05.509+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:38:12.527+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T15:38:12.532+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T15:39:06.628+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:39:52.268+07:00  INFO 28833 --- [qtp1*********-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T15:39:52.300+07:00  INFO 28833 --- [qtp1*********-469] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T15:39:52.344+07:00  INFO 28833 --- [qtp1*********-37] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T15:39:52.351+07:00  INFO 28833 --- [qtp1*********-469] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T15:40:00.366+07:00  INFO 28833 --- [qtp1*********-477] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T15:40:03.622+07:00  INFO 28833 --- [qtp1*********-37] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T15:40:04.737+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:40:04.738+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T15:40:16.764+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 8, expire count 1
2025-09-22T15:40:16.770+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-22T15:41:06.857+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:42:03.951+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:42:16.992+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 3
2025-09-22T15:42:17.001+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T15:43:06.082+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:43:59.796+07:00  INFO 28833 --- [qtp1*********-469] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T15:43:59.800+07:00  INFO 28833 --- [qtp1*********-442] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T15:43:59.878+07:00  INFO 28833 --- [qtp1*********-469] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T15:43:59.879+07:00  INFO 28833 --- [qtp1*********-442] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T15:44:03.168+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:44:11.711+07:00  INFO 28833 --- [qtp1*********-37] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T15:44:11.732+07:00  INFO 28833 --- [qtp1*********-37] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T15:44:11.757+07:00  INFO 28833 --- [qtp1*********-465] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T15:44:11.763+07:00  INFO 28833 --- [qtp1*********-465] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T15:44:17.195+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-22T15:44:17.204+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T15:44:45.680+07:00  INFO 28833 --- [qtp1*********-465] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T15:44:45.694+07:00  INFO 28833 --- [qtp1*********-465] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T15:44:45.703+07:00  INFO 28833 --- [qtp1*********-470] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T15:44:45.707+07:00  INFO 28833 --- [qtp1*********-470] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T15:44:51.542+07:00  INFO 28833 --- [qtp1*********-442] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T15:44:51.548+07:00  INFO 28833 --- [qtp1*********-442] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T15:44:51.566+07:00  INFO 28833 --- [qtp1*********-477] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T15:44:51.570+07:00  INFO 28833 --- [qtp1*********-477] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T15:45:06.285+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-22T15:45:06.290+07:00  INFO 28833 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 22/09/2025@15:45:06+0700
2025-09-22T15:45:06.311+07:00  INFO 28833 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 22/09/2025@15:45:00+0700 to 22/09/2025@16:00:00+0700
2025-09-22T15:45:06.311+07:00  INFO 28833 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 22/09/2025@15:45:00+0700 to 22/09/2025@16:00:00+0700
2025-09-22T15:45:06.311+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:45:06.311+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T15:46:02.408+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:46:16.438+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T15:46:16.442+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T15:47:05.520+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:48:06.630+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:48:15.670+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-22T15:48:15.679+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T15:49:04.766+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:50:06.863+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:50:06.864+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T15:50:14.887+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 7
2025-09-22T15:50:14.890+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T15:51:03.967+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:52:06.079+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:52:14.119+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T15:52:14.130+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T15:53:03.215+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:53:35.894+07:00  INFO 28833 --- [qtp1*********-442] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T15:53:35.896+07:00  INFO 28833 --- [qtp1*********-469] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T15:53:35.923+07:00  INFO 28833 --- [qtp1*********-469] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T15:53:35.923+07:00  INFO 28833 --- [qtp1*********-442] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T15:54:06.313+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:54:13.340+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-09-22T15:54:13.346+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T15:55:02.415+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:55:02.417+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T15:56:05.519+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:56:12.538+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-22T15:56:12.546+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T15:57:06.627+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:58:04.732+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:58:16.759+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T15:58:16.765+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T15:59:06.852+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T15:59:08.055+07:00  INFO 28833 --- [qtp1*********-466] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T15:59:08.055+07:00  INFO 28833 --- [qtp1*********-210] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T15:59:08.071+07:00  INFO 28833 --- [qtp1*********-466] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T15:59:08.071+07:00  INFO 28833 --- [qtp1*********-210] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T15:59:25.711+07:00  INFO 28833 --- [qtp1*********-558] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T16:00:03.955+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T16:00:03.965+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T16:00:03.966+07:00  INFO 28833 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 22/09/2025@16:00:03+0700
2025-09-22T16:00:03.991+07:00  INFO 28833 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 22/09/2025@16:00:00+0700 to 22/09/2025@16:15:00+0700
2025-09-22T16:00:03.991+07:00  INFO 28833 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 22/09/2025@16:00:00+0700 to 22/09/2025@16:15:00+0700
2025-09-22T16:00:03.992+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-22T16:00:03.992+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-09-22T16:00:17.023+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 9, expire count 0
2025-09-22T16:00:17.031+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-22T16:00:21.901+07:00  INFO 28833 --- [qtp1*********-465] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:00:21.941+07:00  INFO 28833 --- [qtp1*********-210] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:00:21.950+07:00  INFO 28833 --- [qtp1*********-465] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:00:21.952+07:00  INFO 28833 --- [qtp1*********-210] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:00:35.425+07:00  INFO 28833 --- [qtp1*********-510] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T16:00:35.427+07:00  INFO 28833 --- [qtp1*********-471] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T16:00:35.431+07:00  INFO 28833 --- [qtp1*********-471] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T16:00:35.431+07:00  INFO 28833 --- [qtp1*********-510] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T16:00:39.796+07:00  INFO 28833 --- [qtp1*********-210] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T16:00:46.206+07:00  INFO 28833 --- [qtp1*********-465] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T16:00:46.980+07:00  INFO 28833 --- [qtp1*********-441] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T16:00:49.449+07:00  INFO 28833 --- [qtp1*********-210] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T16:00:52.176+07:00  INFO 28833 --- [qtp1*********-442] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T16:00:52.416+07:00  INFO 28833 --- [qtp1*********-210] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T16:00:52.764+07:00  INFO 28833 --- [qtp1*********-442] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T16:00:52.934+07:00  INFO 28833 --- [qtp1*********-210] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T16:00:54.721+07:00  INFO 28833 --- [qtp1*********-210] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T16:00:54.721+07:00  INFO 28833 --- [qtp1*********-473] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T16:00:55.202+07:00  INFO 28833 --- [qtp1*********-441] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T16:00:55.374+07:00  INFO 28833 --- [qtp1*********-473] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T16:01:06.106+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T16:01:38.673+07:00  INFO 28833 --- [qtp1*********-558] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:01:38.674+07:00  INFO 28833 --- [qtp1*********-473] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:01:38.686+07:00  INFO 28833 --- [qtp1*********-558] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:01:38.686+07:00  INFO 28833 --- [qtp1*********-473] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:02:03.206+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T16:02:17.257+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 11, expire count 0
2025-09-22T16:02:17.287+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-22T16:03:06.353+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T16:03:18.834+07:00  INFO 28833 --- [qtp1*********-442] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:03:18.877+07:00  INFO 28833 --- [qtp1*********-563] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:03:18.923+07:00  INFO 28833 --- [qtp1*********-442] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:03:18.942+07:00  INFO 28833 --- [qtp1*********-563] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:03:26.928+07:00  INFO 28833 --- [qtp1*********-563] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T16:03:26.928+07:00  INFO 28833 --- [qtp1*********-442] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T16:03:26.942+07:00  INFO 28833 --- [qtp1*********-563] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T16:03:26.942+07:00  INFO 28833 --- [qtp1*********-442] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T16:03:31.788+07:00  INFO 28833 --- [qtp1*********-465] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T16:03:33.681+07:00  INFO 28833 --- [qtp1*********-473] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T16:04:02.436+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T16:04:16.464+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T16:04:16.471+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T16:05:05.547+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T16:05:05.550+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T16:06:06.639+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T16:06:15.685+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 10, expire count 0
2025-09-22T16:06:15.706+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-22T16:07:04.785+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T16:08:06.884+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T16:08:14.910+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T16:08:14.919+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T16:08:51.555+07:00  INFO 28833 --- [qtp1*********-210] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:08:51.556+07:00  INFO 28833 --- [qtp1*********-469] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:08:51.560+07:00  INFO 28833 --- [qtp1*********-210] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:08:51.563+07:00  INFO 28833 --- [qtp1*********-469] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:09:03.993+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T16:09:14.503+07:00  INFO 28833 --- [qtp1*********-210] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:09:14.529+07:00  INFO 28833 --- [qtp1*********-210] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:09:14.545+07:00  INFO 28833 --- [qtp1*********-557] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:09:14.549+07:00  INFO 28833 --- [qtp1*********-557] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:09:30.487+07:00  INFO 28833 --- [qtp1*********-558] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:09:30.488+07:00  INFO 28833 --- [qtp1*********-442] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:09:30.500+07:00  INFO 28833 --- [qtp1*********-558] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:09:30.502+07:00  INFO 28833 --- [qtp1*********-442] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:09:37.556+07:00  INFO 28833 --- [qtp1*********-562] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T16:09:37.556+07:00  INFO 28833 --- [qtp1*********-560] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T16:09:37.562+07:00  INFO 28833 --- [qtp1*********-560] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T16:09:37.579+07:00  INFO 28833 --- [qtp1*********-562] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T16:09:45.612+07:00  INFO 28833 --- [qtp1*********-210] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T16:09:48.892+07:00  INFO 28833 --- [qtp1*********-442] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T16:09:50.557+07:00 ERROR 28833 --- [qtp1*********-442] n.datatp.module.httpclient.HttpClient    : HTTP error during POST request: I/O error on POST request for "https://api.beelogistics.com/ofone/authorize/gettoken": Unexpected end of file from server
2025-09-22T16:09:50.559+07:00 ERROR 28833 --- [qtp1*********-442] n.d.m.monitor.call.EndpointCallContext   : Start call with component CRMPartnerService, method searchBFSOnePartnerSources, arguments
[ {
  "tenantId" : "default",
  "companyId" : 4,
  "companyParentId" : null,
  "companyCode" : "bee",
  "companyLabel" : "Bee Corp",
  "companyFullName" : "BEE LOGISTICS CORPORATION",
  "loginId" : "hanah.vnhph",
  "accountId" : 11557,
  "token" : "858f997ddf07ff57af7a0837d9dd5a42",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node018rvyhkcteusg84p7s799m5fv0",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : {
    "bee" : {
      "companyId" : 4,
      "companyParentId" : 0,
      "companyCode" : "bee",
      "companyLabel" : "Bee Corp",
      "companyFullName" : null
    }
  },
  "featurePermissions" : [ {
    "id" : 15097,
    "appId" : 3,
    "appModule" : "project",
    "appName" : "project",
    "companyId" : 4,
    "loginId" : "hanah.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 2700,
    "appId" : 26,
    "appModule" : "okr",
    "appName" : "okr",
    "companyId" : 4,
    "loginId" : "hanah.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 3735,
    "appId" : 12,
    "appModule" : "spreadsheet",
    "appName" : "spreadsheet",
    "companyId" : 4,
    "loginId" : "hanah.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 11538,
    "appId" : 69,
    "appModule" : "hr",
    "appName" : "user-kpi",
    "companyId" : 4,
    "loginId" : "hanah.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 14757,
    "appId" : 79,
    "appModule" : "company",
    "appName" : "user-asset",
    "companyId" : 4,
    "loginId" : "hanah.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 10508,
    "appId" : 16,
    "appModule" : "logistics",
    "appName" : "user-logistics-sales",
    "companyId" : 4,
    "loginId" : "hanah.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 15103,
    "appId" : 6,
    "appModule" : "company",
    "appName" : "company-space",
    "companyId" : 4,
    "loginId" : "hanah.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Read"
  }, {
    "id" : 6210,
    "appId" : 2,
    "appModule" : "user",
    "appName" : "my-employee-space",
    "companyId" : 4,
    "loginId" : "hanah.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  } ],
  "attributes" : { },
  "clientId" : "default:hanah.vnhph"
}, {
  "filters" : [ {
    "name" : "search",
    "filterType" : "String",
    "filterValue" : "",
    "required" : true
  } ],
  "rangeFilters" : [ ],
  "maxReturn" : 250
} ]
2025-09-22T16:09:50.559+07:00 ERROR 28833 --- [qtp1*********-442] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:118)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.springframework.web.client.ResourceAccessException: I/O error on POST request for "https://api.beelogistics.com/ofone/authorize/gettoken": Unexpected end of file from server
	at org.springframework.web.client.RestTemplate.createResourceAccessException(RestTemplate.java:926)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:906)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:801)
	at org.springframework.web.client.RestTemplate.postForEntity(RestTemplate.java:549)
	at net.datatp.module.httpclient.HttpClient.post(HttpClient.java:34)
	at cloud.datatp.fforwarder.core.integration.BFSOneApi.authenticate(BFSOneApi.java:54)
	at cloud.datatp.fforwarder.core.integration.BFSOneApi.loadSources(BFSOneApi.java:397)
	at cloud.datatp.fforwarder.core.partner.CRMPartnerLogic.searchBFSOnePartnerSources(CRMPartnerLogic.java:545)
	at cloud.datatp.fforwarder.core.partner.CRMPartnerService.searchBFSOnePartnerSources(CRMPartnerService.java:102)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at cloud.datatp.fforwarder.core.partner.CRMPartnerService$$SpringCGLIB$$0.searchBFSOnePartnerSources(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted
Caused by: java.net.SocketException: Unexpected end of file from server
	at java.base/sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:955)
	at java.base/sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:759)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1705)
	at java.base/sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1614)
	at java.base/java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:531)
	at java.base/sun.net.www.protocol.https.HttpsURLConnectionImpl.getResponseCode(HttpsURLConnectionImpl.java:307)
	at org.springframework.http.client.SimpleClientHttpResponse.getStatusCode(SimpleClientHttpResponse.java:55)
	at org.springframework.web.client.DefaultResponseErrorHandler.hasError(DefaultResponseErrorHandler.java:85)
	at org.springframework.web.client.RestTemplate.handleResponse(RestTemplate.java:942)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:902)
	... 116 common frames omitted

2025-09-22T16:09:50.572+07:00  INFO 28833 --- [qtp1*********-442] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint CRMPartnerService/searchBFSOnePartnerSources
2025-09-22T16:10:06.093+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T16:10:06.096+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T16:10:14.134+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 11, expire count 1
2025-09-22T16:10:14.143+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-22T16:11:03.219+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T16:11:16.933+07:00  INFO 28833 --- [qtp1*********-560] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:11:16.937+07:00  INFO 28833 --- [qtp1*********-562] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:11:16.953+07:00  INFO 28833 --- [qtp1*********-562] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:11:16.953+07:00  INFO 28833 --- [qtp1*********-560] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:11:27.377+07:00  INFO 28833 --- [qtp1*********-442] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T16:11:27.377+07:00  INFO 28833 --- [qtp1*********-560] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T16:11:27.439+07:00  INFO 28833 --- [qtp1*********-442] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T16:11:27.456+07:00  INFO 28833 --- [qtp1*********-560] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T16:11:34.484+07:00  INFO 28833 --- [qtp1*********-469] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T16:11:45.409+07:00  INFO 28833 --- [qtp1*********-442] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T16:11:57.479+07:00  INFO 28833 --- [qtp1*********-469] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T16:12:01.766+07:00  INFO 28833 --- [qtp1*********-469] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T16:12:01.766+07:00  INFO 28833 --- [qtp1*********-478] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T16:12:02.259+07:00  INFO 28833 --- [qtp1*********-558] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T16:12:02.521+07:00  INFO 28833 --- [qtp1*********-478] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T16:12:05.837+07:00  INFO 28833 --- [qtp1*********-442] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T16:12:06.333+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T16:12:06.779+07:00  INFO 28833 --- [qtp1*********-210] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T16:12:13.353+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-22T16:12:13.356+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T16:13:02.439+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T16:13:39.640+07:00  INFO 28833 --- [qtp1*********-469] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:13:39.641+07:00  INFO 28833 --- [qtp1*********-210] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:13:39.652+07:00  INFO 28833 --- [qtp1*********-210] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:13:39.652+07:00  INFO 28833 --- [qtp1*********-469] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:13:45.900+07:00  INFO 28833 --- [qtp1*********-210] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:13:45.902+07:00  INFO 28833 --- [qtp1*********-560] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:13:45.911+07:00  INFO 28833 --- [qtp1*********-210] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:13:45.911+07:00  INFO 28833 --- [qtp1*********-560] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:13:48.514+07:00  INFO 28833 --- [qtp1*********-210] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:13:48.525+07:00  INFO 28833 --- [qtp1*********-210] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:13:48.570+07:00  INFO 28833 --- [qtp1*********-645] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:13:48.584+07:00  INFO 28833 --- [qtp1*********-645] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:14:05.537+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T16:14:12.653+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 9, expire count 0
2025-09-22T16:14:12.689+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-22T16:14:16.950+07:00  INFO 28833 --- [qtp1*********-469] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:14:16.968+07:00  INFO 28833 --- [qtp1*********-469] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:14:16.983+07:00  INFO 28833 --- [qtp1*********-613] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:14:16.990+07:00  INFO 28833 --- [qtp1*********-613] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:15:02.131+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T16:15:02.134+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T16:15:02.134+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-22T16:15:02.135+07:00  INFO 28833 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 22/09/2025@16:15:02+0700
2025-09-22T16:15:02.165+07:00  INFO 28833 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 22/09/2025@16:15:00+0700 to 22/09/2025@16:30:00+0700
2025-09-22T16:15:02.166+07:00  INFO 28833 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 22/09/2025@16:15:00+0700 to 22/09/2025@16:30:00+0700
2025-09-22T16:16:05.272+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T16:16:17.300+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-09-22T16:16:17.309+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T16:17:06.389+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T16:18:04.503+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T16:18:16.524+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T16:18:16.529+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T16:19:06.615+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T16:20:03.716+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T16:20:03.718+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T16:20:16.750+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 2
2025-09-22T16:20:16.757+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T16:21:06.859+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T16:22:02.966+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T16:22:17.016+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 6
2025-09-22T16:22:17.032+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T16:23:06.111+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T16:24:02.201+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T16:24:16.227+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T16:24:16.229+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T16:25:05.308+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T16:25:05.312+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T16:26:06.411+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T16:26:15.458+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 4
2025-09-22T16:26:15.465+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T16:27:04.537+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T16:28:06.637+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T16:28:14.659+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T16:28:14.665+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T16:28:24.505+07:00  INFO 28833 --- [qtp1*********-442] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T16:28:24.505+07:00  INFO 28833 --- [qtp1*********-644] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T16:28:24.580+07:00  INFO 28833 --- [qtp1*********-644] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T16:28:24.580+07:00  INFO 28833 --- [qtp1*********-442] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T16:28:28.481+07:00  INFO 28833 --- [qtp1*********-210] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T16:28:28.481+07:00  INFO 28833 --- [qtp1*********-645] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T16:28:37.465+07:00  INFO 28833 --- [qtp1*********-645] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T16:28:40.661+07:00  INFO 28833 --- [qtp1*********-442] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T16:28:43.735+07:00  INFO 28833 --- [qtp1*********-210] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T16:29:03.720+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T16:29:19.699+07:00  INFO 28833 --- [qtp1*********-469] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:29:19.714+07:00  INFO 28833 --- [qtp1*********-469] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:29:19.728+07:00  INFO 28833 --- [qtp1*********-612] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:29:19.734+07:00  INFO 28833 --- [qtp1*********-612] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:29:43.673+07:00  INFO 28833 --- [qtp1*********-442] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:29:43.674+07:00  INFO 28833 --- [qtp1*********-210] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:29:43.688+07:00  INFO 28833 --- [qtp1*********-442] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:29:43.688+07:00  INFO 28833 --- [qtp1*********-210] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:29:49.855+07:00  INFO 28833 --- [qtp1*********-612] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:29:49.857+07:00  INFO 28833 --- [qtp1*********-442] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:29:49.864+07:00  INFO 28833 --- [qtp1*********-442] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:29:49.866+07:00  INFO 28833 --- [qtp1*********-612] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:30:06.827+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T16:30:06.829+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T16:30:06.829+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-22T16:30:06.829+07:00  INFO 28833 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 22/09/2025@16:30:06+0700
2025-09-22T16:30:06.841+07:00  INFO 28833 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 22/09/2025@16:30:00+0700 to 22/09/2025@16:45:00+0700
2025-09-22T16:30:06.841+07:00  INFO 28833 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 22/09/2025@16:30:00+0700 to 22/09/2025@16:45:00+0700
2025-09-22T16:30:13.868+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 12, expire count 0
2025-09-22T16:30:13.876+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-22T16:30:14.219+07:00  INFO 28833 --- [qtp1*********-707] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:30:14.220+07:00  INFO 28833 --- [qtp1*********-711] n.d.module.session.ClientSessionManager  : Add a client session id = node018rvyhkcteusg84p7s799m5fv0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:30:14.224+07:00  INFO 28833 --- [qtp1*********-707] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:30:14.224+07:00  INFO 28833 --- [qtp1*********-711] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:30:16.954+07:00  INFO 28833 --- [qtp1*********-646] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T16:30:16.958+07:00  INFO 28833 --- [qtp1*********-442] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T16:30:17.001+07:00  INFO 28833 --- [qtp1*********-646] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T16:30:17.001+07:00  INFO 28833 --- [qtp1*********-442] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T16:30:23.991+07:00  INFO 28833 --- [qtp1*********-612] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T16:30:26.035+07:00  INFO 28833 --- [qtp1*********-644] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T16:30:27.286+07:00  INFO 28833 --- [qtp1*********-612] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T16:30:46.686+07:00  INFO 28833 --- [qtp1*********-612] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T16:30:50.270+07:00  INFO 28833 --- [qtp1*********-644] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-22T16:31:02.947+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T16:32:06.054+07:00  INFO 28833 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T16:32:13.097+07:00  INFO 28833 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-22T16:32:13.105+07:00  INFO 28833 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T16:32:49.240+07:00  INFO 28833 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@71cd4087{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-22T16:32:49.241+07:00  INFO 28833 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-22T16:32:49.241+07:00  INFO 28833 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-22T16:32:49.241+07:00  INFO 28833 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-22T16:32:49.242+07:00  INFO 28833 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-22T16:32:49.242+07:00  INFO 28833 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-22T16:32:49.242+07:00  INFO 28833 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-22T16:32:49.242+07:00  INFO 28833 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-22T16:32:49.242+07:00  INFO 28833 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-09-22T16:32:49.242+07:00  INFO 28833 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-09-22T16:32:49.242+07:00  INFO 28833 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-22T16:32:49.242+07:00  INFO 28833 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-09-22T16:32:49.242+07:00  INFO 28833 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-09-22T16:32:49.242+07:00  INFO 28833 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-22T16:32:49.242+07:00  INFO 28833 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-09-22T16:32:49.242+07:00  INFO 28833 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-09-22T16:32:49.264+07:00  INFO 28833 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-22T16:32:49.318+07:00  INFO 28833 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-22T16:32:49.325+07:00  INFO 28833 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-22T16:32:49.345+07:00  INFO 28833 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-22T16:32:49.347+07:00  INFO 28833 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-22T16:32:49.348+07:00  INFO 28833 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-22T16:32:49.348+07:00  INFO 28833 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-22T16:32:49.349+07:00  INFO 28833 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-22T16:32:49.349+07:00  INFO 28833 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-22T16:32:49.349+07:00  INFO 28833 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-22T16:32:49.349+07:00  INFO 28833 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-22T16:32:49.349+07:00  INFO 28833 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-22T16:32:49.349+07:00  INFO 28833 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-22T16:32:49.350+07:00  INFO 28833 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-22T16:32:49.350+07:00  INFO 28833 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-22T16:32:49.350+07:00  INFO 28833 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-22T16:32:49.351+07:00  INFO 28833 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@4f5080ea{STOPPING}[12.0.15,sto=0]
2025-09-22T16:32:49.354+07:00  INFO 28833 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-22T16:32:49.356+07:00  INFO 28833 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@10ecdd85{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.13257594048207443015/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@47ffce5d{STOPPED}}
