2025-09-24T11:31:10.079+07:00  INFO 52076 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 52076 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-24T11:31:10.080+07:00  INFO 52076 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-24T11:31:10.772+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T11:31:10.836+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 60 ms. Found 22 JPA repository interfaces.
2025-09-24T11:31:10.848+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T11:31:10.849+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-24T11:31:10.850+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T11:31:10.895+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 45 ms. Found 10 JPA repository interfaces.
2025-09-24T11:31:10.896+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T11:31:10.900+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 3 JPA repository interfaces.
2025-09-24T11:31:10.908+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T11:31:10.913+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-09-24T11:31:10.922+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T11:31:10.924+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 2 JPA repository interfaces.
2025-09-24T11:31:10.924+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T11:31:10.928+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-24T11:31:10.931+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T11:31:10.935+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-09-24T11:31:10.939+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T11:31:10.941+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-24T11:31:10.941+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T11:31:10.942+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-24T11:31:10.942+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T11:31:10.948+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 10 JPA repository interfaces.
2025-09-24T11:31:10.953+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T11:31:10.956+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-24T11:31:10.959+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T11:31:10.963+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-24T11:31:10.963+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T11:31:10.970+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-24T11:31:10.970+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T11:31:10.973+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-24T11:31:10.973+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T11:31:10.973+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-24T11:31:10.973+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T11:31:10.974+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-24T11:31:10.974+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T11:31:10.979+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-09-24T11:31:10.979+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T11:31:10.980+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-24T11:31:10.981+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T11:31:10.981+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-24T11:31:10.981+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T11:31:10.991+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 19 JPA repository interfaces.
2025-09-24T11:31:11.001+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T11:31:11.008+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 8 JPA repository interfaces.
2025-09-24T11:31:11.008+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T11:31:11.011+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-09-24T11:31:11.011+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T11:31:11.015+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-24T11:31:11.015+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T11:31:11.021+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-09-24T11:31:11.021+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T11:31:11.025+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-09-24T11:31:11.026+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T11:31:11.034+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 13 JPA repository interfaces.
2025-09-24T11:31:11.034+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T11:31:11.043+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 14 JPA repository interfaces.
2025-09-24T11:31:11.043+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T11:31:11.057+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 13 ms. Found 24 JPA repository interfaces.
2025-09-24T11:31:11.058+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T11:31:11.059+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-24T11:31:11.064+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T11:31:11.065+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-24T11:31:11.065+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T11:31:11.072+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-24T11:31:11.073+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T11:31:11.110+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 36 ms. Found 66 JPA repository interfaces.
2025-09-24T11:31:11.110+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T11:31:11.112+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-24T11:31:11.116+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T11:31:11.119+07:00  INFO 52076 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-24T11:31:11.335+07:00  INFO 52076 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-24T11:31:11.339+07:00  INFO 52076 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-24T11:31:11.666+07:00  WARN 52076 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-24T11:31:11.873+07:00  INFO 52076 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-24T11:31:11.875+07:00  INFO 52076 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-24T11:31:11.886+07:00  INFO 52076 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-24T11:31:11.886+07:00  INFO 52076 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1696 ms
2025-09-24T11:31:11.939+07:00  WARN 52076 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-24T11:31:11.939+07:00  INFO 52076 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-24T11:31:12.033+07:00  INFO 52076 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@76603c44
2025-09-24T11:31:12.034+07:00  INFO 52076 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-24T11:31:12.038+07:00  WARN 52076 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-24T11:31:12.038+07:00  INFO 52076 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-24T11:31:12.043+07:00  INFO 52076 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@61b3de5b
2025-09-24T11:31:12.044+07:00  INFO 52076 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-24T11:31:12.044+07:00  WARN 52076 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-24T11:31:12.044+07:00  INFO 52076 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-24T11:31:12.053+07:00  INFO 52076 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@35408036
2025-09-24T11:31:12.054+07:00  INFO 52076 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-24T11:31:12.054+07:00  WARN 52076 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-24T11:31:12.054+07:00  INFO 52076 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-24T11:31:12.060+07:00  INFO 52076 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@5304ac92
2025-09-24T11:31:12.061+07:00  INFO 52076 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-24T11:31:12.061+07:00  WARN 52076 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-24T11:31:12.061+07:00  INFO 52076 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-24T11:31:12.065+07:00  INFO 52076 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@210b4145
2025-09-24T11:31:12.065+07:00  INFO 52076 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-24T11:31:12.065+07:00  INFO 52076 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-24T11:31:12.109+07:00  INFO 52076 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-24T11:31:12.111+07:00  INFO 52076 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@642aa8ea{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.7111389555567101871/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@13668c8b{STARTED}}
2025-09-24T11:31:12.156+07:00  INFO 52076 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@642aa8ea{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.7111389555567101871/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@13668c8b{STARTED}}
2025-09-24T11:31:12.157+07:00  INFO 52076 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@74940388{STARTING}[12.0.15,sto=0] @2596ms
2025-09-24T11:31:12.212+07:00  INFO 52076 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-24T11:31:12.240+07:00  INFO 52076 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-24T11:31:12.255+07:00  INFO 52076 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-24T11:31:12.399+07:00  INFO 52076 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-24T11:31:12.436+07:00  WARN 52076 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-24T11:31:13.075+07:00  INFO 52076 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-24T11:31:13.084+07:00  INFO 52076 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@d748c1a] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-24T11:31:13.222+07:00  INFO 52076 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-24T11:31:13.419+07:00  INFO 52076 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.wfms", "net.datatp.module.monitor.activity", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "net.datatp.module.okr", "cloud.datatp.module.fleet", "net.datatp.module.core.template", "cloud.datatp.gps", "cloud.datatp.module.pegasus.shipment", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "cloud.datatp.tms", "net.datatp.module.groovy", "net.datatp.module.company.tmpl", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "net.datatp.module.resource", "cloud.datatp.vendor", "net.datatp.module.company.hr", "net.datatp.module.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.chatbot", "cloud.datatp.module.odoo", "net.datatp.module.core.print", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-09-24T11:31:13.421+07:00  INFO 52076 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-24T11:31:13.427+07:00  INFO 52076 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-24T11:31:13.428+07:00  INFO 52076 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-24T11:31:13.456+07:00  INFO 52076 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-24T11:31:13.460+07:00  WARN 52076 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-24T11:31:15.625+07:00  INFO 52076 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-24T11:31:15.625+07:00  INFO 52076 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@55c7b8e9] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-24T11:31:15.807+07:00  WARN 52076 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-24T11:31:15.808+07:00  WARN 52076 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-24T11:31:15.816+07:00  WARN 52076 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-24T11:31:15.816+07:00  WARN 52076 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-24T11:31:15.829+07:00  WARN 52076 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-24T11:31:15.829+07:00  WARN 52076 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-24T11:31:16.266+07:00  INFO 52076 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-24T11:31:16.272+07:00  INFO 52076 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-24T11:31:16.273+07:00  INFO 52076 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-24T11:31:16.294+07:00  INFO 52076 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-24T11:31:16.296+07:00  WARN 52076 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-24T11:31:16.847+07:00  INFO 52076 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-24T11:31:16.847+07:00  INFO 52076 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@2b3fdbfa] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-24T11:31:16.996+07:00  WARN 52076 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-24T11:31:16.997+07:00  WARN 52076 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-24T11:31:17.328+07:00  INFO 52076 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-24T11:31:17.368+07:00  INFO 52076 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-24T11:31:17.373+07:00  INFO 52076 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-24T11:31:17.373+07:00  INFO 52076 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-24T11:31:17.380+07:00  WARN 52076 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-24T11:31:17.527+07:00  INFO 52076 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-24T11:31:17.983+07:00  INFO 52076 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-24T11:31:17.985+07:00  INFO 52076 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-24T11:31:18.018+07:00  INFO 52076 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-24T11:31:18.056+07:00  INFO 52076 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-24T11:31:18.108+07:00  INFO 52076 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-24T11:31:18.152+07:00  INFO 52076 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-24T11:31:18.173+07:00  INFO 52076 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 11317975ms : this is harmless.
2025-09-24T11:31:18.181+07:00  INFO 52076 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-24T11:31:18.185+07:00  INFO 52076 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-24T11:31:18.202+07:00  INFO 52076 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 11317964ms : this is harmless.
2025-09-24T11:31:18.204+07:00  INFO 52076 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-24T11:31:18.216+07:00  INFO 52076 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-24T11:31:18.216+07:00  INFO 52076 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-24T11:31:20.246+07:00  INFO 52076 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-24T11:31:20.247+07:00  INFO 52076 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-24T11:31:20.247+07:00  WARN 52076 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-24T11:31:20.546+07:00  INFO 52076 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 24/09/2025@11:30:00+0700 to 24/09/2025@11:45:00+0700
2025-09-24T11:31:20.546+07:00  INFO 52076 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 24/09/2025@11:30:00+0700 to 24/09/2025@11:45:00+0700
2025-09-24T11:31:21.105+07:00  INFO 52076 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-09-24T11:31:21.105+07:00  INFO 52076 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-24T11:31:21.106+07:00  WARN 52076 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-24T11:31:21.415+07:00  INFO 52076 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-09-24T11:31:21.415+07:00  INFO 52076 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-09-24T11:31:21.415+07:00  INFO 52076 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-09-24T11:31:21.415+07:00  INFO 52076 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-09-24T11:31:21.415+07:00  INFO 52076 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-09-24T11:31:23.264+07:00  WARN 52076 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 67dc3f21-9af4-4242-9022-557f8f8e3b77

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-24T11:31:23.268+07:00  INFO 52076 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-09-24T11:31:23.580+07:00  INFO 52076 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-24T11:31:23.580+07:00  INFO 52076 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-24T11:31:23.580+07:00  INFO 52076 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-24T11:31:23.580+07:00  INFO 52076 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-24T11:31:23.580+07:00  INFO 52076 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-09-24T11:31:23.580+07:00  INFO 52076 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-09-24T11:31:23.580+07:00  INFO 52076 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-24T11:31:23.580+07:00  INFO 52076 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-09-24T11:31:23.580+07:00  INFO 52076 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-09-24T11:31:23.580+07:00  INFO 52076 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-24T11:31:23.580+07:00  INFO 52076 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-09-24T11:31:23.580+07:00  INFO 52076 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-09-24T11:31:23.583+07:00  INFO 52076 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-24T11:31:23.583+07:00  INFO 52076 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-24T11:31:23.583+07:00  INFO 52076 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-24T11:31:23.662+07:00  INFO 52076 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-24T11:31:23.662+07:00  INFO 52076 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-24T11:31:23.664+07:00  INFO 52076 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-09-24T11:31:23.672+07:00  INFO 52076 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@2f25e9c1{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-24T11:31:23.672+07:00  INFO 52076 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-09-24T11:31:23.673+07:00  INFO 52076 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-09-24T11:31:23.708+07:00  INFO 52076 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-09-24T11:31:23.708+07:00  INFO 52076 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-09-24T11:31:23.714+07:00  INFO 52076 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 13.969 seconds (process running for 14.152)
2025-09-24T11:31:53.158+07:00  INFO 52076 --- [qtp124451741-35] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node06s77y1uq2nvyxafrsqicrbku0
2025-09-24T11:31:53.571+07:00  INFO 52076 --- [qtp124451741-35] n.d.module.session.ClientSessionManager  : Add a client session id = node06s77y1uq2nvyxafrsqicrbku0, token = 81caec0b544206d0851c7849a5f06656
2025-09-24T11:31:53.997+07:00  INFO 52076 --- [qtp124451741-35] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-24T11:32:05.669+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T11:32:26.750+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 9, expire count 0
2025-09-24T11:32:26.790+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-24T11:33:06.854+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T11:34:04.944+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T11:34:26.009+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-24T11:34:26.017+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T11:34:41.466+07:00  INFO 52076 --- [qtp124451741-60] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-24T11:34:41.468+07:00  INFO 52076 --- [qtp124451741-34] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-24T11:34:41.856+07:00  INFO 52076 --- [qtp124451741-34] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-24T11:34:41.856+07:00  INFO 52076 --- [qtp124451741-60] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-24T11:34:51.567+07:00  INFO 52076 --- [qtp124451741-40] n.d.module.session.ClientSessionManager  : Add a client session id = node06s77y1uq2nvyxafrsqicrbku0, token = 81caec0b544206d0851c7849a5f06656
2025-09-24T11:34:51.575+07:00  INFO 52076 --- [qtp124451741-72] n.d.module.session.ClientSessionManager  : Add a client session id = node06s77y1uq2nvyxafrsqicrbku0, token = 81caec0b544206d0851c7849a5f06656
2025-09-24T11:34:51.578+07:00  INFO 52076 --- [qtp124451741-40] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-24T11:34:51.582+07:00  INFO 52076 --- [qtp124451741-72] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-24T11:35:06.080+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T11:35:06.083+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T11:35:11.901+07:00  INFO 52076 --- [qtp124451741-40] n.d.module.session.ClientSessionManager  : Add a client session id = node06s77y1uq2nvyxafrsqicrbku0, token = 81caec0b544206d0851c7849a5f06656
2025-09-24T11:35:11.909+07:00  INFO 52076 --- [qtp124451741-40] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-24T11:35:11.924+07:00  INFO 52076 --- [qtp124451741-41] n.d.module.session.ClientSessionManager  : Add a client session id = node06s77y1uq2nvyxafrsqicrbku0, token = 81caec0b544206d0851c7849a5f06656
2025-09-24T11:35:11.934+07:00  INFO 52076 --- [qtp124451741-41] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-24T11:35:24.531+07:00  INFO 52076 --- [qtp124451741-34] n.d.module.session.ClientSessionManager  : Add a client session id = node06s77y1uq2nvyxafrsqicrbku0, token = 81caec0b544206d0851c7849a5f06656
2025-09-24T11:35:24.532+07:00  INFO 52076 --- [qtp124451741-68] n.d.module.session.ClientSessionManager  : Add a client session id = node06s77y1uq2nvyxafrsqicrbku0, token = 81caec0b544206d0851c7849a5f06656
2025-09-24T11:35:24.546+07:00  INFO 52076 --- [qtp124451741-34] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-24T11:35:24.546+07:00  INFO 52076 --- [qtp124451741-68] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-24T11:36:04.184+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T11:36:30.269+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 10, expire count 0
2025-09-24T11:36:30.283+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-24T11:37:06.347+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T11:38:03.461+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T11:38:26.432+07:00  INFO 52076 --- [qtp124451741-68] n.d.module.session.ClientSessionManager  : Add a client session id = node06s77y1uq2nvyxafrsqicrbku0, token = 81caec0b544206d0851c7849a5f06656
2025-09-24T11:38:26.470+07:00  INFO 52076 --- [qtp124451741-68] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-24T11:38:26.470+07:00  INFO 52076 --- [qtp124451741-73] n.d.module.session.ClientSessionManager  : Add a client session id = node06s77y1uq2nvyxafrsqicrbku0, token = 81caec0b544206d0851c7849a5f06656
2025-09-24T11:38:26.476+07:00  INFO 52076 --- [qtp124451741-73] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-24T11:38:30.535+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-24T11:38:30.549+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T11:38:34.235+07:00  INFO 52076 --- [qtp124451741-69] n.d.module.session.ClientSessionManager  : Add a client session id = node06s77y1uq2nvyxafrsqicrbku0, token = 81caec0b544206d0851c7849a5f06656
2025-09-24T11:38:34.236+07:00  INFO 52076 --- [qtp124451741-60] n.d.module.session.ClientSessionManager  : Add a client session id = node06s77y1uq2nvyxafrsqicrbku0, token = 81caec0b544206d0851c7849a5f06656
2025-09-24T11:38:34.246+07:00  INFO 52076 --- [qtp124451741-69] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-24T11:38:34.248+07:00  INFO 52076 --- [qtp124451741-60] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-24T11:39:06.611+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T11:40:02.712+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T11:40:02.715+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T11:40:18.706+07:00  INFO 52076 --- [qtp124451741-72] n.d.module.session.ClientSessionManager  : Add a client session id = node06s77y1uq2nvyxafrsqicrbku0, token = 81caec0b544206d0851c7849a5f06656
2025-09-24T11:40:18.722+07:00  INFO 52076 --- [qtp124451741-72] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-24T11:40:18.879+07:00  INFO 52076 --- [qtp124451741-36] n.d.module.session.ClientSessionManager  : Add a client session id = node06s77y1uq2nvyxafrsqicrbku0, token = 81caec0b544206d0851c7849a5f06656
2025-09-24T11:40:18.886+07:00  INFO 52076 --- [qtp124451741-36] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-24T11:40:18.922+07:00 ERROR 52076 --- [qtp124451741-72] n.d.m.monitor.call.EndpointCallContext   : Start call with component QuotationService, method searchSpecificQuotations, arguments
[ {
  "tenantId" : "default",
  "companyId" : 4,
  "companyParentId" : null,
  "companyCode" : "bee",
  "companyLabel" : "Bee Corp",
  "companyFullName" : "BEE LOGISTICS CORPORATION",
  "loginId" : "sandy.vnhph",
  "accountId" : 11174,
  "token" : "81caec0b544206d0851c7849a5f06656",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node06s77y1uq2nvyxafrsqicrbku0",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : {
    "bee" : {
      "companyId" : 4,
      "companyParentId" : 0,
      "companyCode" : "bee",
      "companyLabel" : "Bee Corp",
      "companyFullName" : null
    }
  },
  "featurePermissions" : [ {
    "id" : 15075,
    "appId" : 3,
    "appModule" : "project",
    "appName" : "project",
    "companyId" : 4,
    "loginId" : "sandy.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 3052,
    "appId" : 26,
    "appModule" : "okr",
    "appName" : "okr",
    "companyId" : 4,
    "loginId" : "sandy.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 4374,
    "appId" : 12,
    "appModule" : "spreadsheet",
    "appName" : "spreadsheet",
    "companyId" : 4,
    "loginId" : "sandy.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 12287,
    "appId" : 70,
    "appModule" : "hr",
    "appName" : "company-kpi",
    "companyId" : 4,
    "loginId" : "sandy.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 12322,
    "appId" : 69,
    "appModule" : "hr",
    "appName" : "user-kpi",
    "companyId" : 4,
    "loginId" : "sandy.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 14733,
    "appId" : 79,
    "appModule" : "company",
    "appName" : "user-asset",
    "companyId" : 4,
    "loginId" : "sandy.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 4481,
    "appId" : 15,
    "appModule" : "logistics",
    "appName" : "user-logistics-prices",
    "companyId" : 4,
    "loginId" : "sandy.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 10434,
    "appId" : 16,
    "appModule" : "logistics",
    "appName" : "user-logistics-sales",
    "companyId" : 4,
    "loginId" : "sandy.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Group",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 15101,
    "appId" : 80,
    "appModule" : "logistics",
    "appName" : "company-logistics-sales",
    "companyId" : 4,
    "loginId" : "sandy.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 12971,
    "appId" : 5,
    "appModule" : "admin",
    "appName" : "admin-space",
    "companyId" : 4,
    "loginId" : "sandy.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 12279,
    "appId" : 6,
    "appModule" : "company",
    "appName" : "company-space",
    "companyId" : 4,
    "loginId" : "sandy.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Read"
  }, {
    "id" : 6124,
    "appId" : 2,
    "appModule" : "user",
    "appName" : "my-employee-space",
    "companyId" : 4,
    "loginId" : "sandy.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  } ],
  "attributes" : { },
  "clientId" : "default:sandy.vnhph"
}, null, {
  "params" : {
    "space" : "User"
  },
  "filters" : [ {
    "name" : "search",
    "filterType" : "String",
    "filterValue" : "",
    "required" : true
  } ],
  "optionFilters" : [ {
    "name" : "storageState",
    "filterType" : "NotSet",
    "required" : true,
    "multiple" : true,
    "options" : [ "ACTIVE", "ARCHIVED" ],
    "selectOptions" : [ "ACTIVE" ]
  } ],
  "rangeFilters" : [ {
    "name" : "requestDate",
    "filterType" : "NotSet",
    "required" : true,
    "fromValue" : "01/07/2025@00:00:00+0700",
    "toValue" : "30/09/2025@23:59:59+0700"
  } ],
  "maxReturn" : 1000
} ]
2025-09-24T11:40:18.923+07:00 ERROR 52076 --- [qtp124451741-72] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:115)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Cannot invoke "net.datatp.module.data.db.entity.ICompany.getId()" because "company" is null
	at cloud.datatp.fforwarder.sales.quotation.SpecificQuotationLogic.searchSpecificQuotations(SpecificQuotationLogic.java:242)
	at cloud.datatp.fforwarder.sales.quotation.QuotationService.searchSpecificQuotations(QuotationService.java:69)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at cloud.datatp.fforwarder.sales.quotation.QuotationService$$SpringCGLIB$$0.searchSpecificQuotations(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted

2025-09-24T11:40:18.933+07:00  INFO 52076 --- [qtp124451741-72] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint QuotationService/searchSpecificQuotations
2025-09-24T11:40:29.811+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-24T11:40:29.830+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T11:41:05.886+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T11:41:26.923+07:00  INFO 52076 --- [qtp124451741-72] n.d.module.session.ClientSessionManager  : Add a client session id = node06s77y1uq2nvyxafrsqicrbku0, token = 81caec0b544206d0851c7849a5f06656
2025-09-24T11:41:26.938+07:00  INFO 52076 --- [qtp124451741-72] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-24T11:41:26.948+07:00  INFO 52076 --- [qtp124451741-63] n.d.module.session.ClientSessionManager  : Add a client session id = node06s77y1uq2nvyxafrsqicrbku0, token = 81caec0b544206d0851c7849a5f06656
2025-09-24T11:41:26.961+07:00  INFO 52076 --- [qtp124451741-63] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-24T11:41:38.805+07:00  INFO 52076 --- [qtp124451741-66] n.d.module.session.ClientSessionManager  : Add a client session id = node06s77y1uq2nvyxafrsqicrbku0, token = 81caec0b544206d0851c7849a5f06656
2025-09-24T11:41:38.807+07:00  INFO 52076 --- [qtp124451741-71] n.d.module.session.ClientSessionManager  : Add a client session id = node06s77y1uq2nvyxafrsqicrbku0, token = 81caec0b544206d0851c7849a5f06656
2025-09-24T11:41:38.821+07:00  INFO 52076 --- [qtp124451741-66] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-24T11:41:38.823+07:00  INFO 52076 --- [qtp124451741-71] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-24T11:42:06.974+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T11:42:13.531+07:00  INFO 52076 --- [qtp124451741-72] n.d.module.session.ClientSessionManager  : Add a client session id = node06s77y1uq2nvyxafrsqicrbku0, token = 81caec0b544206d0851c7849a5f06656
2025-09-24T11:42:13.533+07:00  INFO 52076 --- [qtp124451741-73] n.d.module.session.ClientSessionManager  : Add a client session id = node06s77y1uq2nvyxafrsqicrbku0, token = 81caec0b544206d0851c7849a5f06656
2025-09-24T11:42:13.544+07:00  INFO 52076 --- [qtp124451741-72] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-24T11:42:13.544+07:00  INFO 52076 --- [qtp124451741-73] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-24T11:42:22.992+07:00  INFO 52076 --- [qtp124451741-68] n.d.module.session.ClientSessionManager  : Add a client session id = node06s77y1uq2nvyxafrsqicrbku0, token = 81caec0b544206d0851c7849a5f06656
2025-09-24T11:42:22.995+07:00  INFO 52076 --- [qtp124451741-63] n.d.module.session.ClientSessionManager  : Add a client session id = node06s77y1uq2nvyxafrsqicrbku0, token = 81caec0b544206d0851c7849a5f06656
2025-09-24T11:42:23.008+07:00  INFO 52076 --- [qtp124451741-63] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-24T11:42:23.007+07:00  INFO 52076 --- [qtp124451741-68] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-24T11:42:30.065+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 8, expire count 6
2025-09-24T11:42:30.084+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T11:43:05.155+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T11:44:02.031+07:00  INFO 52076 --- [qtp124451741-72] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-24T11:44:02.031+07:00  INFO 52076 --- [qtp124451741-73] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-24T11:44:02.056+07:00  INFO 52076 --- [qtp124451741-72] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-24T11:44:02.056+07:00  INFO 52076 --- [qtp124451741-73] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-24T11:44:06.247+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T11:44:29.299+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-24T11:44:29.309+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T11:45:04.367+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T11:45:04.369+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T11:45:04.372+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-24T11:45:04.394+07:00  INFO 52076 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 24/09/2025@11:45:04+0700
2025-09-24T11:45:04.420+07:00  INFO 52076 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 24/09/2025@11:45:00+0700 to 24/09/2025@12:00:00+0700
2025-09-24T11:45:04.420+07:00  INFO 52076 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 24/09/2025@11:45:00+0700 to 24/09/2025@12:00:00+0700
2025-09-24T11:46:06.548+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T11:46:28.645+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 0
2025-09-24T11:46:28.714+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-24T11:47:01.340+07:00 ERROR 52076 --- [qtp124451741-68] n.d.m.monitor.call.EndpointCallContext   : Start call with component QuotationService, method newSpecificQuotation, arguments
[ {
  "tenantId" : "default",
  "companyId" : 4,
  "companyParentId" : null,
  "companyCode" : "bee",
  "companyLabel" : "Bee Corp",
  "companyFullName" : "BEE LOGISTICS CORPORATION",
  "loginId" : "sandy.vnhph",
  "accountId" : 11174,
  "token" : "81caec0b544206d0851c7849a5f06656",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node06s77y1uq2nvyxafrsqicrbku0",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : {
    "bee" : {
      "companyId" : 4,
      "companyParentId" : 0,
      "companyCode" : "bee",
      "companyLabel" : "Bee Corp",
      "companyFullName" : null
    }
  },
  "featurePermissions" : [ {
    "id" : 15075,
    "appId" : 3,
    "appModule" : "project",
    "appName" : "project",
    "companyId" : 4,
    "loginId" : "sandy.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 3052,
    "appId" : 26,
    "appModule" : "okr",
    "appName" : "okr",
    "companyId" : 4,
    "loginId" : "sandy.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 4374,
    "appId" : 12,
    "appModule" : "spreadsheet",
    "appName" : "spreadsheet",
    "companyId" : 4,
    "loginId" : "sandy.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 12287,
    "appId" : 70,
    "appModule" : "hr",
    "appName" : "company-kpi",
    "companyId" : 4,
    "loginId" : "sandy.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 12322,
    "appId" : 69,
    "appModule" : "hr",
    "appName" : "user-kpi",
    "companyId" : 4,
    "loginId" : "sandy.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 14733,
    "appId" : 79,
    "appModule" : "company",
    "appName" : "user-asset",
    "companyId" : 4,
    "loginId" : "sandy.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 4481,
    "appId" : 15,
    "appModule" : "logistics",
    "appName" : "user-logistics-prices",
    "companyId" : 4,
    "loginId" : "sandy.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 10434,
    "appId" : 16,
    "appModule" : "logistics",
    "appName" : "user-logistics-sales",
    "companyId" : 4,
    "loginId" : "sandy.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Group",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 15101,
    "appId" : 80,
    "appModule" : "logistics",
    "appName" : "company-logistics-sales",
    "companyId" : 4,
    "loginId" : "sandy.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 12971,
    "appId" : 5,
    "appModule" : "admin",
    "appName" : "admin-space",
    "companyId" : 4,
    "loginId" : "sandy.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 12279,
    "appId" : 6,
    "appModule" : "company",
    "appName" : "company-space",
    "companyId" : 4,
    "loginId" : "sandy.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Read"
  }, {
    "id" : 6124,
    "appId" : 2,
    "appModule" : "user",
    "appName" : "my-employee-space",
    "companyId" : 4,
    "loginId" : "sandy.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  } ],
  "attributes" : { },
  "clientId" : "default:sandy.vnhph"
}, {
  "parentId" : 0,
  "id" : 4,
  "code" : "bee",
  "label" : "Bee Corp",
  "fullName" : "BEE LOGISTICS CORPORATION"
}, {
  "priceReferenceIds" : [ ],
  "inquiryRequestId" : 165,
  "inquiry" : null,
  "autoSaved" : true
} ]
2025-09-24T11:47:01.341+07:00 ERROR 52076 --- [qtp124451741-68] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:118)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.NoSuchElementException: No value present
	at java.base/java.util.Optional.get(Optional.java:143)
	at cloud.datatp.fforwarder.price.InquiryRequestLogic.getInquiryRequest(InquiryRequestLogic.java:85)
	at cloud.datatp.fforwarder.sales.quotation.SpecificQuotationLogic.newSpecificQuotation(SpecificQuotationLogic.java:184)
	at cloud.datatp.fforwarder.sales.quotation.QuotationService.newSpecificQuotation(QuotationService.java:54)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at cloud.datatp.fforwarder.sales.quotation.QuotationService$$SpringCGLIB$$0.newSpecificQuotation(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted

2025-09-24T11:47:01.345+07:00  INFO 52076 --- [qtp124451741-68] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint QuotationService/newSpecificQuotation
2025-09-24T11:47:03.786+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T11:48:06.913+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T11:48:27.946+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T11:48:27.954+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T11:49:03.019+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T11:50:06.126+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T11:50:06.128+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T11:50:27.190+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 2
2025-09-24T11:50:27.201+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-24T11:51:02.278+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T11:52:05.394+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T11:52:26.431+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 2
2025-09-24T11:52:26.440+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T11:53:06.543+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T11:54:04.646+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T11:54:30.736+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 3
2025-09-24T11:54:30.745+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T11:55:06.817+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T11:55:06.822+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T11:56:03.913+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T11:56:29.951+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 2
2025-09-24T11:56:29.958+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T11:57:06.034+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T11:58:03.119+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T11:58:30.174+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 5
2025-09-24T11:58:30.183+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T11:59:06.247+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T12:00:02.337+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T12:00:02.349+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T12:00:02.350+07:00  INFO 52076 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 24/09/2025@12:00:02+0700
2025-09-24T12:00:02.393+07:00  INFO 52076 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 24/09/2025@12:00:00+0700 to 24/09/2025@12:15:00+0700
2025-09-24T12:00:02.393+07:00  INFO 52076 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 24/09/2025@12:00:00+0700 to 24/09/2025@12:15:00+0700
2025-09-24T12:00:02.393+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-24T12:00:02.398+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at 12 PM every day
2025-09-24T12:00:02.399+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 3 hour
2025-09-24T12:00:02.400+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-09-24T12:00:30.449+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-24T12:00:30.460+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T12:01:05.521+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T12:02:06.663+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T12:02:29.725+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T12:02:29.739+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T12:03:04.809+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T12:04:00.069+07:00  WARN 52076 --- [rw housekeeper] com.zaxxer.hikari.pool.HikariPool        : rw - Thread starvation or clock leap detected (housekeeper delta=47s607ms).
2025-09-24T12:04:00.069+07:00  WARN 52076 --- [jdbc housekeeper] com.zaxxer.hikari.pool.HikariPool        : jdbc - Thread starvation or clock leap detected (housekeeper delta=47s607ms).
2025-09-24T12:04:00.072+07:00  WARN 52076 --- [rw housekeeper] com.zaxxer.hikari.pool.HikariPool        : rw - Thread starvation or clock leap detected (housekeeper delta=47s607ms).
2025-09-24T12:04:00.069+07:00  WARN 52076 --- [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=47s608ms).
2025-09-24T12:04:00.073+07:00  WARN 52076 --- [HikariPool-2 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Thread starvation or clock leap detected (housekeeper delta=47s603ms).
2025-09-24T12:04:24.545+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T12:04:46.688+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-24T12:04:46.708+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T12:05:06.768+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T12:05:21.817+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T12:06:05.859+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T12:06:48.049+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-24T12:06:48.059+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T12:07:02.102+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T12:08:11.921+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T12:08:57.185+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-24T12:08:57.196+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T12:09:09.210+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T12:10:06.358+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T12:10:14.384+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T12:11:11.251+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-24T12:11:11.263+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T12:11:18.284+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T12:26:56.194+07:00  WARN 52076 --- [rw housekeeper] com.zaxxer.hikari.pool.HikariPool        : rw - Thread starvation or clock leap detected (housekeeper delta=15m29s467ms).
2025-09-24T12:26:56.194+07:00  WARN 52076 --- [jdbc housekeeper] com.zaxxer.hikari.pool.HikariPool        : jdbc - Thread starvation or clock leap detected (housekeeper delta=15m29s473ms).
2025-09-24T12:26:56.219+07:00  WARN 52076 --- [HikariPool-2 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Thread starvation or clock leap detected (housekeeper delta=15m29s473ms).
2025-09-24T12:26:56.219+07:00  WARN 52076 --- [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=15m29s467ms).
2025-09-24T12:26:56.275+07:00  WARN 52076 --- [rw housekeeper] com.zaxxer.hikari.pool.HikariPool        : rw - Thread starvation or clock leap detected (housekeeper delta=15m29s473ms).
2025-09-24T12:27:05.492+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T12:27:16.611+07:00  INFO 52076 --- [qtp124451741-68] n.d.m.session.AppHttpSessionListener     : The session node06s77y1uq2nvyxafrsqicrbku0 is destroyed.
2025-09-24T12:27:16.629+07:00  INFO 52076 --- [qtp124451741-68] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-09-24T12:28:03.663+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T12:28:10.696+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 1
2025-09-24T12:28:10.717+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T12:28:19.568+07:00  INFO 52076 --- [qtp124451741-153] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01r1jzr4bwqgfl6ldwk51dgeq11
2025-09-24T12:28:19.573+07:00  INFO 52076 --- [qtp124451741-68] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01emfgdyo73nea1xhadjbfez76d2
2025-09-24T12:28:19.640+07:00  INFO 52076 --- [qtp124451741-68] n.d.module.session.ClientSessionManager  : Add a client session id = node01emfgdyo73nea1xhadjbfez76d2, token = 81caec0b544206d0851c7849a5f06656
2025-09-24T12:28:19.670+07:00  INFO 52076 --- [qtp124451741-153] n.d.module.session.ClientSessionManager  : Add a client session id = node01r1jzr4bwqgfl6ldwk51dgeq11, token = 81caec0b544206d0851c7849a5f06656
2025-09-24T12:28:19.948+07:00  INFO 52076 --- [qtp124451741-153] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-24T12:28:19.948+07:00  INFO 52076 --- [qtp124451741-68] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-24T12:29:06.865+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T12:30:03.012+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T12:30:10.040+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-09-24T12:30:10.054+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T12:30:17.077+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T12:30:50.140+07:00  INFO 52076 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 24/09/2025@12:30:50+0700
2025-09-24T12:30:50.152+07:00  INFO 52076 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 24/09/2025@12:30:00+0700 to 24/09/2025@12:45:00+0700
2025-09-24T12:30:50.152+07:00  INFO 52076 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 24/09/2025@12:30:00+0700 to 24/09/2025@12:45:00+0700
2025-09-24T12:30:50.152+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-24T12:31:06.212+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T12:32:02.373+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T12:32:09.404+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-24T12:32:09.409+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T12:33:05.559+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T12:34:06.720+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T12:34:13.738+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-24T12:34:13.745+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T12:35:04.912+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T12:35:04.914+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T12:36:06.096+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T12:36:14.125+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-24T12:36:14.134+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T12:37:04.279+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T12:38:06.434+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T12:38:13.451+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-24T12:38:13.453+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T12:39:03.567+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T12:40:06.748+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T12:40:06.750+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T12:40:13.781+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 3
2025-09-24T12:40:13.788+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T12:41:02.925+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T12:41:55.785+07:00  INFO 52076 --- [Scheduler-466491867-1] n.d.m.session.AppHttpSessionListener     : The session node01emfgdyo73nea1xhadjbfez76d2 is destroyed.
2025-09-24T12:42:06.088+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T12:42:13.101+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-24T12:42:13.105+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T12:42:28.665+07:00  INFO 52076 --- [qtp124451741-185] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-09-24T12:43:02.250+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T12:43:31.600+07:00  INFO 52076 --- [qtp124451741-153] n.d.module.session.ClientSessionManager  : Add a client session id = node01r1jzr4bwqgfl6ldwk51dgeq11, token = 81caec0b544206d0851c7849a5f06656
2025-09-24T12:43:31.616+07:00  INFO 52076 --- [qtp124451741-153] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-24T12:43:31.649+07:00  INFO 52076 --- [qtp124451741-65] n.d.module.session.ClientSessionManager  : Add a client session id = node01r1jzr4bwqgfl6ldwk51dgeq11, token = 81caec0b544206d0851c7849a5f06656
2025-09-24T12:43:31.658+07:00  INFO 52076 --- [qtp124451741-65] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-24T12:44:05.456+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T12:44:12.485+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-09-24T12:44:12.499+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T12:45:06.648+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T12:45:06.648+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T12:45:06.649+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-24T12:45:06.649+07:00  INFO 52076 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 24/09/2025@12:45:06+0700
2025-09-24T12:45:06.661+07:00  INFO 52076 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 24/09/2025@12:45:00+0700 to 24/09/2025@13:00:00+0700
2025-09-24T12:45:06.662+07:00  INFO 52076 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 24/09/2025@12:45:00+0700 to 24/09/2025@13:00:00+0700
2025-09-24T12:46:04.860+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T12:46:11.898+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-24T12:46:11.905+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T12:47:06.037+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T12:48:04.239+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T12:48:11.265+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-24T12:48:11.272+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T12:49:06.442+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T12:50:03.592+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T12:50:03.593+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T12:50:10.621+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T12:50:10.623+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T12:51:06.827+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T12:52:02.999+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T12:52:10.021+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-24T12:52:10.028+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T12:53:06.201+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T12:54:03.007+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T12:54:10.045+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 4
2025-09-24T12:54:10.054+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T12:55:06.141+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T12:55:06.143+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T12:56:02.241+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T12:56:14.271+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-24T12:56:14.277+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T12:57:05.383+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T12:58:06.473+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T12:58:13.487+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T12:58:13.491+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T12:59:04.579+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:00:06.693+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:00:06.696+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T13:00:06.699+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-24T13:00:06.702+07:00  INFO 52076 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 24/09/2025@13:00:06+0700
2025-09-24T13:00:06.710+07:00  INFO 52076 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 24/09/2025@13:00:00+0700 to 24/09/2025@13:15:00+0700
2025-09-24T13:00:06.710+07:00  INFO 52076 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 24/09/2025@13:00:00+0700 to 24/09/2025@13:15:00+0700
2025-09-24T13:00:13.746+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-24T13:00:13.756+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T13:01:03.839+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:02:06.947+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:02:13.965+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-24T13:02:13.970+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T13:03:03.043+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:04:06.198+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:04:13.214+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T13:04:13.219+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T13:05:02.383+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:05:02.385+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T13:06:05.557+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:06:12.590+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-24T13:06:12.600+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T13:07:06.738+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:08:04.881+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:08:11.911+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T13:08:11.918+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T13:09:06.055+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:10:04.228+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:10:04.229+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T13:10:11.273+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-24T13:10:11.283+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T13:11:06.482+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:12:03.625+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:12:10.648+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T13:12:10.650+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T13:13:06.807+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:14:02.979+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:14:10.007+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-24T13:14:10.011+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T13:15:06.162+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T13:15:06.163+07:00  INFO 52076 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 24/09/2025@13:15:06+0700
2025-09-24T13:15:06.180+07:00  INFO 52076 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 24/09/2025@13:15:00+0700 to 24/09/2025@13:30:00+0700
2025-09-24T13:15:06.181+07:00  INFO 52076 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 24/09/2025@13:15:00+0700 to 24/09/2025@13:30:00+0700
2025-09-24T13:15:06.181+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-24T13:15:06.182+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:15:48.313+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-09-24T13:16:02.365+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:16:09.403+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-24T13:16:09.410+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T13:17:05.595+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:18:06.792+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:18:13.816+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-24T13:18:13.823+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T13:19:04.969+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:20:06.168+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:20:06.168+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T13:20:14.206+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-24T13:20:14.211+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T13:21:04.376+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:22:06.602+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:22:13.613+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T13:22:13.616+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T13:23:03.763+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:24:06.944+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:24:13.973+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-24T13:24:13.979+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T13:25:03.119+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:25:03.120+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T13:26:06.322+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:26:13.346+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-24T13:26:13.348+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T13:27:02.516+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:28:05.680+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:28:12.709+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-24T13:28:12.716+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T13:29:06.837+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:30:05.022+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T13:30:05.023+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:30:05.023+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-24T13:30:05.024+07:00  INFO 52076 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 24/09/2025@13:30:05+0700
2025-09-24T13:30:05.045+07:00  INFO 52076 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 1 messages for session 24/09/2025@13:30:00+0700 to 24/09/2025@13:45:00+0700
2025-09-24T13:30:05.046+07:00  INFO 52076 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 📤 message : MAIL - inquiry-overdue-reminder - scheduled at 24/09/2025@13:43:01+0700
2025-09-24T13:30:05.046+07:00  INFO 52076 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 1 messages for session 24/09/2025@13:30:00+0700 to 24/09/2025@13:45:00+0700
2025-09-24T13:30:12.071+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-24T13:30:12.076+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T13:31:06.233+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:32:04.632+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:32:11.661+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-24T13:32:11.670+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T13:33:06.770+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:34:03.852+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:34:10.866+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T13:34:10.888+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T13:35:06.971+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:35:06.974+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T13:35:55.750+07:00  INFO 52076 --- [qtp124451741-409] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph logout successfully 
2025-09-24T13:36:01.727+07:00  INFO 52076 --- [qtp124451741-421] n.d.module.session.ClientSessionManager  : Add a client session id = node01r1jzr4bwqgfl6ldwk51dgeq11, token = 7cb50e1bf27346de004e8924be3d57cb
2025-09-24T13:36:01.833+07:00  INFO 52076 --- [qtp124451741-421] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-24T13:36:03.076+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:36:10.108+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-24T13:36:10.123+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T13:36:41.561+07:00  INFO 52076 --- [qtp124451741-422] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph logout successfully 
2025-09-24T13:36:43.447+07:00  INFO 52076 --- [qtp124451741-351] n.d.module.session.ClientSessionManager  : Add a client session id = node01r1jzr4bwqgfl6ldwk51dgeq11, token = 5935f52c9fddb56e9b76246cabe26ea3
2025-09-24T13:36:43.460+07:00  INFO 52076 --- [qtp124451741-351] n.d.m.c.a.CompanyAuthenticationService   : User rio.vnsgn is logged in successfully system
2025-09-24T13:37:06.219+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:38:02.323+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:38:14.350+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-24T13:38:14.354+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T13:39:05.432+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:40:06.512+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:40:06.514+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T13:40:13.571+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 13, expire count 1
2025-09-24T13:40:13.593+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-24T13:41:04.685+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:42:06.784+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:42:13.823+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-24T13:42:13.831+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T13:43:03.912+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:44:06.976+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:44:14.001+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-24T13:44:14.008+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-24T13:45:03.084+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T13:45:03.090+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-24T13:45:03.091+07:00  INFO 52076 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 24/09/2025@13:45:03+0700
2025-09-24T13:45:03.115+07:00  INFO 52076 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 24/09/2025@13:45:00+0700 to 24/09/2025@14:00:00+0700
2025-09-24T13:45:03.115+07:00  INFO 52076 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 24/09/2025@13:45:00+0700 to 24/09/2025@14:00:00+0700
2025-09-24T13:45:03.115+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:46:06.222+07:00  INFO 52076 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T13:46:13.253+07:00  INFO 52076 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 4
2025-09-24T13:46:13.259+07:00  INFO 52076 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T13:46:32.639+07:00  INFO 52076 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@2f25e9c1{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-24T13:46:32.641+07:00  INFO 52076 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-24T13:46:32.641+07:00  INFO 52076 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-24T13:46:32.641+07:00  INFO 52076 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-24T13:46:32.643+07:00  INFO 52076 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-24T13:46:32.643+07:00  INFO 52076 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-24T13:46:32.643+07:00  INFO 52076 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-24T13:46:32.643+07:00  INFO 52076 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-24T13:46:32.643+07:00  INFO 52076 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-09-24T13:46:32.643+07:00  INFO 52076 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-09-24T13:46:32.643+07:00  INFO 52076 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-24T13:46:32.643+07:00  INFO 52076 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-09-24T13:46:32.643+07:00  INFO 52076 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-09-24T13:46:32.643+07:00  INFO 52076 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-24T13:46:32.643+07:00  INFO 52076 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-09-24T13:46:32.643+07:00  INFO 52076 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-09-24T13:46:32.667+07:00  INFO 52076 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T13:46:32.765+07:00  INFO 52076 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-24T13:46:32.770+07:00  INFO 52076 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-24T13:46:32.806+07:00  INFO 52076 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-24T13:46:32.810+07:00  INFO 52076 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-24T13:46:32.820+07:00  INFO 52076 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-24T13:46:32.820+07:00  INFO 52076 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-24T13:46:32.822+07:00  INFO 52076 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-24T13:46:32.822+07:00  INFO 52076 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-24T13:46:32.822+07:00  INFO 52076 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-24T13:46:32.823+07:00  INFO 52076 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-24T13:46:32.823+07:00  INFO 52076 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-24T13:46:32.823+07:00  INFO 52076 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-24T13:46:32.823+07:00  INFO 52076 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-24T13:46:32.823+07:00  INFO 52076 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-24T13:46:32.823+07:00  INFO 52076 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-24T13:46:32.827+07:00  INFO 52076 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@74940388{STOPPING}[12.0.15,sto=0]
2025-09-24T13:46:32.831+07:00  INFO 52076 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-24T13:46:32.834+07:00  INFO 52076 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@642aa8ea{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.7111389555567101871/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@13668c8b{STOPPED}}
