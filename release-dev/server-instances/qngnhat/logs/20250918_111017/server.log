2025-09-18T11:10:18.263+07:00  INFO 95902 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 95902 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-18T11:10:18.263+07:00  INFO 95902 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-18T11:10:18.999+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T11:10:19.067+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 64 ms. Found 22 JPA repository interfaces.
2025-09-18T11:10:19.076+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T11:10:19.077+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-18T11:10:19.078+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T11:10:19.120+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 41 ms. Found 10 JPA repository interfaces.
2025-09-18T11:10:19.121+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T11:10:19.124+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-18T11:10:19.134+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T11:10:19.139+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-09-18T11:10:19.147+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T11:10:19.149+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-18T11:10:19.150+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T11:10:19.153+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-18T11:10:19.156+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T11:10:19.160+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 5 JPA repository interfaces.
2025-09-18T11:10:19.164+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T11:10:19.167+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-18T11:10:19.167+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T11:10:19.167+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-18T11:10:19.167+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T11:10:19.174+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 10 JPA repository interfaces.
2025-09-18T11:10:19.179+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T11:10:19.181+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-18T11:10:19.184+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T11:10:19.188+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-18T11:10:19.188+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T11:10:19.196+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 12 JPA repository interfaces.
2025-09-18T11:10:19.197+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T11:10:19.200+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-18T11:10:19.200+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T11:10:19.200+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-18T11:10:19.200+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T11:10:19.201+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-18T11:10:19.201+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T11:10:19.205+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-09-18T11:10:19.205+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T11:10:19.207+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-18T11:10:19.207+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T11:10:19.207+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-18T11:10:19.207+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T11:10:19.218+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 19 JPA repository interfaces.
2025-09-18T11:10:19.228+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T11:10:19.234+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 8 JPA repository interfaces.
2025-09-18T11:10:19.234+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T11:10:19.237+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-09-18T11:10:19.238+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T11:10:19.241+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-18T11:10:19.242+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T11:10:19.248+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 9 JPA repository interfaces.
2025-09-18T11:10:19.248+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T11:10:19.252+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-18T11:10:19.252+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T11:10:19.261+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 13 JPA repository interfaces.
2025-09-18T11:10:19.261+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T11:10:19.270+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 14 JPA repository interfaces.
2025-09-18T11:10:19.271+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T11:10:19.285+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 13 ms. Found 24 JPA repository interfaces.
2025-09-18T11:10:19.285+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T11:10:19.286+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-18T11:10:19.291+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T11:10:19.292+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-18T11:10:19.292+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T11:10:19.299+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-18T11:10:19.300+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T11:10:19.337+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 36 ms. Found 66 JPA repository interfaces.
2025-09-18T11:10:19.337+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T11:10:19.338+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-18T11:10:19.343+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T11:10:19.345+07:00  INFO 95902 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-18T11:10:19.564+07:00  INFO 95902 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-18T11:10:19.567+07:00  INFO 95902 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-18T11:10:19.842+07:00  WARN 95902 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-18T11:10:20.049+07:00  INFO 95902 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-18T11:10:20.051+07:00  INFO 95902 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-18T11:10:20.063+07:00  INFO 95902 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-18T11:10:20.063+07:00  INFO 95902 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1692 ms
2025-09-18T11:10:20.121+07:00  WARN 95902 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-18T11:10:20.121+07:00  INFO 95902 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-18T11:10:20.220+07:00  INFO 95902 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@6abdead7
2025-09-18T11:10:20.220+07:00  INFO 95902 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-18T11:10:20.225+07:00  WARN 95902 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-18T11:10:20.225+07:00  INFO 95902 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-18T11:10:20.229+07:00  INFO 95902 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@11261fc9
2025-09-18T11:10:20.229+07:00  INFO 95902 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-18T11:10:20.229+07:00  WARN 95902 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-18T11:10:20.229+07:00  INFO 95902 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-18T11:10:20.236+07:00  INFO 95902 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@1b13e4fb
2025-09-18T11:10:20.236+07:00  INFO 95902 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-18T11:10:20.236+07:00  WARN 95902 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-18T11:10:20.236+07:00  INFO 95902 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-18T11:10:20.244+07:00  INFO 95902 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@194feb27
2025-09-18T11:10:20.244+07:00  INFO 95902 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-18T11:10:20.244+07:00  WARN 95902 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-18T11:10:20.244+07:00  INFO 95902 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-18T11:10:20.250+07:00  INFO 95902 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@7dbf92aa
2025-09-18T11:10:20.250+07:00  INFO 95902 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-18T11:10:20.250+07:00  INFO 95902 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-18T11:10:20.293+07:00  INFO 95902 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-18T11:10:20.295+07:00  INFO 95902 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@49a2e86e{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.10759617939487991537/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@69959ea5{STARTED}}
2025-09-18T11:10:20.295+07:00  INFO 95902 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@49a2e86e{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.10759617939487991537/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@69959ea5{STARTED}}
2025-09-18T11:10:20.344+07:00  INFO 95902 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@16908f89{STARTING}[12.0.15,sto=0] @2669ms
2025-09-18T11:10:20.401+07:00  INFO 95902 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-18T11:10:20.426+07:00  INFO 95902 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-18T11:10:20.442+07:00  INFO 95902 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-18T11:10:20.569+07:00  INFO 95902 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-18T11:10:20.601+07:00  WARN 95902 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-18T11:10:21.329+07:00  INFO 95902 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-18T11:10:21.348+07:00  INFO 95902 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@184c6764] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-18T11:10:21.555+07:00  INFO 95902 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-18T11:10:21.788+07:00  INFO 95902 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.graphapi", "net.datatp.module.kpi", "net.datatp.module.wfms", "net.datatp.module.monitor.activity", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "net.datatp.module.okr", "cloud.datatp.module.fleet", "net.datatp.module.core.template", "cloud.datatp.gps", "cloud.datatp.module.pegasus.shipment", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "cloud.datatp.tms", "net.datatp.module.groovy", "net.datatp.module.company.tmpl", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "net.datatp.module.resource", "cloud.datatp.vendor", "net.datatp.module.company.hr", "net.datatp.module.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "cloud.datatp.module.odoo", "net.datatp.module.chatbot", "net.datatp.module.core.print", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.zalo", "net.datatp.module.accounting" ]
2025-09-18T11:10:21.790+07:00  INFO 95902 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-18T11:10:21.796+07:00  INFO 95902 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-18T11:10:21.798+07:00  INFO 95902 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-18T11:10:21.827+07:00  INFO 95902 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-18T11:10:21.852+07:00  WARN 95902 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-18T11:10:24.114+07:00  INFO 95902 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-18T11:10:24.114+07:00  INFO 95902 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@44529c8e] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-18T11:10:24.298+07:00  WARN 95902 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-18T11:10:24.298+07:00  WARN 95902 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-18T11:10:24.306+07:00  WARN 95902 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-18T11:10:24.306+07:00  WARN 95902 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-18T11:10:24.320+07:00  WARN 95902 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-18T11:10:24.320+07:00  WARN 95902 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-18T11:10:24.750+07:00  INFO 95902 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-18T11:10:24.755+07:00  INFO 95902 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-18T11:10:24.757+07:00  INFO 95902 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-18T11:10:24.776+07:00  INFO 95902 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-18T11:10:24.778+07:00  WARN 95902 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-18T11:10:25.294+07:00  INFO 95902 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-18T11:10:25.295+07:00  INFO 95902 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@9be9cfc] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-18T11:10:25.363+07:00  WARN 95902 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-18T11:10:25.363+07:00  WARN 95902 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-18T11:10:25.724+07:00  INFO 95902 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-18T11:10:25.761+07:00  INFO 95902 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-18T11:10:25.766+07:00  INFO 95902 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-18T11:10:25.772+07:00  INFO 95902 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T11:10:25.783+07:00  WARN 95902 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-18T11:10:25.914+07:00  INFO 95902 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-18T11:10:26.375+07:00  INFO 95902 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-18T11:10:26.378+07:00  INFO 95902 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-18T11:10:26.413+07:00  INFO 95902 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-18T11:10:26.459+07:00  INFO 95902 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-18T11:10:26.515+07:00  INFO 95902 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-18T11:10:26.543+07:00  INFO 95902 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-18T11:10:26.565+07:00  INFO 95902 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 235891268ms : this is harmless.
2025-09-18T11:10:26.573+07:00  INFO 95902 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-18T11:10:26.576+07:00  INFO 95902 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-18T11:10:26.598+07:00  INFO 95902 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 527397222ms : this is harmless.
2025-09-18T11:10:26.599+07:00  INFO 95902 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-18T11:10:26.623+07:00  INFO 95902 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-18T11:10:26.624+07:00  INFO 95902 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-18T11:10:28.773+07:00  INFO 95902 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-18T11:10:28.773+07:00  INFO 95902 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T11:10:28.773+07:00  WARN 95902 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-18T11:10:29.117+07:00  INFO 95902 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 18/09/2025@11:00:00+0700 to 18/09/2025@11:15:00+0700
2025-09-18T11:10:29.117+07:00  INFO 95902 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 18/09/2025@11:00:00+0700 to 18/09/2025@11:15:00+0700
2025-09-18T11:10:29.755+07:00  INFO 95902 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-09-18T11:10:29.755+07:00  INFO 95902 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T11:10:29.756+07:00  WARN 95902 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-18T11:10:30.053+07:00  INFO 95902 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-09-18T11:10:30.053+07:00  INFO 95902 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-09-18T11:10:30.053+07:00  INFO 95902 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-09-18T11:10:30.053+07:00  INFO 95902 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-09-18T11:10:30.053+07:00  INFO 95902 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-09-18T11:10:31.893+07:00  WARN 95902 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 6dbfd5dc-f9b5-41c9-849d-99edfa630874

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-18T11:10:31.897+07:00  INFO 95902 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-09-18T11:10:32.214+07:00  INFO 95902 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-18T11:10:32.215+07:00  INFO 95902 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-18T11:10:32.215+07:00  INFO 95902 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-18T11:10:32.215+07:00  INFO 95902 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-18T11:10:32.215+07:00  INFO 95902 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-09-18T11:10:32.215+07:00  INFO 95902 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-09-18T11:10:32.215+07:00  INFO 95902 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-18T11:10:32.226+07:00  INFO 95902 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-09-18T11:10:32.226+07:00  INFO 95902 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-09-18T11:10:32.226+07:00  INFO 95902 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-18T11:10:32.226+07:00  INFO 95902 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-09-18T11:10:32.226+07:00  INFO 95902 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-09-18T11:10:32.228+07:00  INFO 95902 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-18T11:10:32.228+07:00  INFO 95902 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-18T11:10:32.228+07:00  INFO 95902 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-18T11:10:32.331+07:00  INFO 95902 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-18T11:10:32.331+07:00  INFO 95902 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-18T11:10:32.333+07:00  INFO 95902 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-09-18T11:10:32.340+07:00  INFO 95902 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@c496c76{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-18T11:10:32.341+07:00  INFO 95902 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-09-18T11:10:32.342+07:00  INFO 95902 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-09-18T11:10:32.376+07:00  INFO 95902 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-09-18T11:10:32.376+07:00  INFO 95902 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-09-18T11:10:32.382+07:00  INFO 95902 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 14.498 seconds (process running for 14.708)
2025-09-18T11:10:35.070+07:00  INFO 95902 --- [qtp2101609336-35] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01cs9u1owkii1f12z7ha9hg5udx0
2025-09-18T11:10:35.070+07:00  INFO 95902 --- [qtp2101609336-37] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0gn6ou6pin23n1kg7hsms76fec1
2025-09-18T11:10:35.415+07:00  INFO 95902 --- [qtp2101609336-35] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T11:10:35.416+07:00  INFO 95902 --- [qtp2101609336-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0gn6ou6pin23n1kg7hsms76fec1, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T11:10:35.815+07:00  INFO 95902 --- [qtp2101609336-35] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T11:10:35.820+07:00  INFO 95902 --- [qtp2101609336-37] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T11:10:38.036+07:00  INFO 95902 --- [qtp2101609336-40] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T11:10:38.052+07:00  INFO 95902 --- [qtp2101609336-39] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T11:10:38.275+07:00  INFO 95902 --- [qtp2101609336-39] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T11:10:38.275+07:00  INFO 95902 --- [qtp2101609336-40] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T11:11:02.143+07:00  INFO 95902 --- [qtp2101609336-39] c.d.f.core.partner.CRMPartnerLogic       : --- Create account: bfsone code = CS000049_TEMP
2025-09-18T11:11:06.318+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:11:08.331+07:00  INFO 95902 --- [qtp2101609336-40] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T11:11:08.341+07:00  INFO 95902 --- [qtp2101609336-40] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T11:11:08.353+07:00  INFO 95902 --- [qtp2101609336-68] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T11:11:08.359+07:00  INFO 95902 --- [qtp2101609336-68] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T11:11:35.412+07:00  INFO 95902 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 14, expire count 0
2025-09-18T11:11:35.444+07:00  INFO 95902 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-18T11:12:03.492+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:13:06.575+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:13:12.209+07:00  INFO 95902 --- [qtp2101609336-37] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T11:13:12.225+07:00  INFO 95902 --- [qtp2101609336-37] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T11:13:12.241+07:00  INFO 95902 --- [qtp2101609336-63] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T11:13:12.259+07:00  INFO 95902 --- [qtp2101609336-63] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T11:13:34.661+07:00  INFO 95902 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-18T11:13:34.666+07:00  INFO 95902 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T11:13:54.598+07:00  INFO 95902 --- [qtp2101609336-62] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T11:13:54.599+07:00  INFO 95902 --- [qtp2101609336-61] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T11:13:54.626+07:00  INFO 95902 --- [qtp2101609336-61] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T11:13:54.626+07:00  INFO 95902 --- [qtp2101609336-62] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T11:14:02.705+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:15:05.831+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T11:15:05.834+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-18T11:15:05.841+07:00  INFO 95902 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 18/09/2025@11:15:05+0700
2025-09-18T11:15:05.857+07:00  INFO 95902 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 18/09/2025@11:15:00+0700 to 18/09/2025@11:30:00+0700
2025-09-18T11:15:05.857+07:00  INFO 95902 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 18/09/2025@11:15:00+0700 to 18/09/2025@11:30:00+0700
2025-09-18T11:15:05.858+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:15:29.787+07:00  INFO 95902 --- [qtp2101609336-63] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T11:15:29.798+07:00  INFO 95902 --- [qtp2101609336-63] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T11:15:29.822+07:00  INFO 95902 --- [qtp2101609336-41] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T11:15:29.871+07:00  INFO 95902 --- [qtp2101609336-41] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T11:15:38.997+07:00  INFO 95902 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 9, expire count 0
2025-09-18T11:15:39.012+07:00  INFO 95902 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-18T11:16:02.059+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:16:22.804+07:00  INFO 95902 --- [qtp2101609336-68] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T11:16:22.804+07:00  INFO 95902 --- [qtp2101609336-64] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T11:16:22.821+07:00  INFO 95902 --- [qtp2101609336-68] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T11:16:22.821+07:00  INFO 95902 --- [qtp2101609336-64] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T11:17:05.169+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:17:39.247+07:00  INFO 95902 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 8, expire count 0
2025-09-18T11:17:39.333+07:00  INFO 95902 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-18T11:18:06.375+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:19:04.484+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:19:38.538+07:00  INFO 95902 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T11:19:38.576+07:00  INFO 95902 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T11:20:06.615+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:20:06.617+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T11:20:20.310+07:00  INFO 95902 --- [Scheduler-298330153-1] n.d.m.session.AppHttpSessionListener     : The session node0gn6ou6pin23n1kg7hsms76fec1 is destroyed.
2025-09-18T11:20:30.879+07:00  INFO 95902 --- [qtp2101609336-36] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-09-18T11:20:33.027+07:00  INFO 95902 --- [qtp2101609336-63] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T11:20:33.041+07:00  INFO 95902 --- [qtp2101609336-63] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T11:20:33.068+07:00  INFO 95902 --- [qtp2101609336-65] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T11:20:33.080+07:00  INFO 95902 --- [qtp2101609336-65] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T11:21:03.711+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:21:38.804+07:00  INFO 95902 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 6
2025-09-18T11:21:38.819+07:00  INFO 95902 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T11:22:06.857+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:23:02.947+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:23:38.001+07:00  INFO 95902 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T11:23:38.009+07:00  INFO 95902 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T11:24:06.047+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:25:02.136+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:25:02.142+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T11:25:37.214+07:00  INFO 95902 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-18T11:25:37.227+07:00  INFO 95902 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T11:25:57.953+07:00  INFO 95902 --- [qtp2101609336-64] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T11:25:57.953+07:00  INFO 95902 --- [qtp2101609336-39] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T11:25:57.991+07:00  INFO 95902 --- [qtp2101609336-64] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T11:25:57.991+07:00  INFO 95902 --- [qtp2101609336-39] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T11:26:05.252+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:27:06.332+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:27:36.431+07:00  INFO 95902 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 2
2025-09-18T11:27:36.450+07:00  INFO 95902 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-18T11:28:04.498+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:29:06.599+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:29:35.668+07:00  INFO 95902 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T11:29:35.678+07:00  INFO 95902 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T11:30:03.720+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T11:30:03.723+07:00  INFO 95902 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 18/09/2025@11:30:03+0700
2025-09-18T11:30:03.747+07:00  INFO 95902 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 18/09/2025@11:30:00+0700 to 18/09/2025@11:45:00+0700
2025-09-18T11:30:03.748+07:00  INFO 95902 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 18/09/2025@11:30:00+0700 to 18/09/2025@11:45:00+0700
2025-09-18T11:30:03.748+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-18T11:30:03.749+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:31:06.849+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:31:34.913+07:00  INFO 95902 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 4
2025-09-18T11:31:34.923+07:00  INFO 95902 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T11:32:02.970+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:33:06.047+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:33:39.111+07:00  INFO 95902 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T11:33:39.120+07:00  INFO 95902 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T11:34:02.167+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:35:05.282+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T11:35:05.286+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:35:39.367+07:00  INFO 95902 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T11:35:39.378+07:00  INFO 95902 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T11:36:06.447+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:37:04.543+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:37:38.604+07:00  INFO 95902 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 5
2025-09-18T11:37:38.617+07:00  INFO 95902 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T11:38:06.669+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:39:03.753+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:39:38.827+07:00  INFO 95902 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-18T11:39:38.837+07:00  INFO 95902 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T11:40:06.877+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T11:40:06.878+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:40:13.268+07:00  INFO 95902 --- [qtp2101609336-77] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T11:40:13.271+07:00  INFO 95902 --- [qtp2101609336-66] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T11:40:13.296+07:00  INFO 95902 --- [qtp2101609336-66] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T11:40:13.296+07:00  INFO 95902 --- [qtp2101609336-77] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T11:40:28.505+07:00  INFO 95902 --- [qtp2101609336-66] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T11:40:28.527+07:00  INFO 95902 --- [qtp2101609336-66] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T11:40:28.533+07:00  INFO 95902 --- [qtp2101609336-133] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T11:40:28.540+07:00  INFO 95902 --- [qtp2101609336-133] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T11:41:02.971+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:41:38.077+07:00  INFO 95902 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 11, expire count 1
2025-09-18T11:41:38.098+07:00  INFO 95902 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-18T11:42:06.156+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:42:10.804+07:00  INFO 95902 --- [qtp2101609336-133] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T11:42:10.805+07:00  INFO 95902 --- [qtp2101609336-134] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T11:42:10.917+07:00  INFO 95902 --- [qtp2101609336-133] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T11:42:10.942+07:00  INFO 95902 --- [qtp2101609336-134] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T11:43:02.256+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:43:04.582+07:00  INFO 95902 --- [qtp2101609336-66] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T11:43:04.583+07:00  INFO 95902 --- [qtp2101609336-41] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T11:43:04.597+07:00  INFO 95902 --- [qtp2101609336-41] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T11:43:04.597+07:00  INFO 95902 --- [qtp2101609336-66] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T11:43:37.333+07:00  INFO 95902 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-18T11:43:37.348+07:00  INFO 95902 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T11:44:05.389+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:44:34.301+07:00  INFO 95902 --- [qtp2101609336-163] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T11:44:34.302+07:00  INFO 95902 --- [qtp2101609336-133] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T11:44:34.369+07:00  INFO 95902 --- [qtp2101609336-163] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T11:44:34.388+07:00  INFO 95902 --- [qtp2101609336-133] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T11:45:06.484+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-18T11:45:06.487+07:00  INFO 95902 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 18/09/2025@11:45:06+0700
2025-09-18T11:45:06.504+07:00  INFO 95902 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 18/09/2025@11:45:00+0700 to 18/09/2025@12:00:00+0700
2025-09-18T11:45:06.505+07:00  INFO 95902 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 18/09/2025@11:45:00+0700 to 18/09/2025@12:00:00+0700
2025-09-18T11:45:06.505+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T11:45:06.506+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:45:36.589+07:00  INFO 95902 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 11, expire count 0
2025-09-18T11:45:36.607+07:00  INFO 95902 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-18T11:46:04.647+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:47:06.741+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:47:20.458+07:00  INFO 95902 --- [qtp2101609336-66] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T11:47:20.471+07:00  INFO 95902 --- [qtp2101609336-66] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T11:47:20.486+07:00  INFO 95902 --- [qtp2101609336-101] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T11:47:20.508+07:00  INFO 95902 --- [qtp2101609336-101] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T11:47:35.824+07:00  INFO 95902 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T11:47:35.837+07:00  INFO 95902 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T11:48:03.885+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:48:40.466+07:00  INFO 95902 --- [qtp2101609336-162] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T11:48:40.469+07:00  INFO 95902 --- [qtp2101609336-159] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T11:48:40.486+07:00  INFO 95902 --- [qtp2101609336-159] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T11:48:40.486+07:00  INFO 95902 --- [qtp2101609336-162] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T11:48:48.266+07:00  INFO 95902 --- [qtp2101609336-159] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T11:48:48.282+07:00  INFO 95902 --- [qtp2101609336-159] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T11:48:48.324+07:00  INFO 95902 --- [qtp2101609336-164] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T11:48:48.341+07:00  INFO 95902 --- [qtp2101609336-164] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T11:49:06.028+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:49:35.110+07:00  INFO 95902 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-18T11:49:35.127+07:00  INFO 95902 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T11:50:03.172+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T11:50:03.175+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:50:28.910+07:00  INFO 95902 --- [qtp2101609336-159] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T11:50:28.938+07:00  INFO 95902 --- [qtp2101609336-159] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T11:50:28.955+07:00  INFO 95902 --- [qtp2101609336-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T11:50:28.980+07:00  INFO 95902 --- [qtp2101609336-36] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T11:50:57.949+07:00  INFO 95902 --- [qtp2101609336-164] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T11:50:57.951+07:00  INFO 95902 --- [qtp2101609336-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T11:50:57.957+07:00  INFO 95902 --- [qtp2101609336-36] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T11:50:57.957+07:00  INFO 95902 --- [qtp2101609336-164] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T11:51:06.389+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:51:07.425+07:00  INFO 95902 --- [qtp2101609336-164] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T11:51:07.425+07:00  INFO 95902 --- [qtp2101609336-159] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T11:51:07.431+07:00  INFO 95902 --- [qtp2101609336-164] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T11:51:07.432+07:00  INFO 95902 --- [qtp2101609336-159] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T11:51:28.432+07:00  INFO 95902 --- [qtp2101609336-164] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T11:51:28.433+07:00  INFO 95902 --- [qtp2101609336-162] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T11:51:28.450+07:00  INFO 95902 --- [qtp2101609336-164] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T11:51:28.451+07:00  INFO 95902 --- [qtp2101609336-162] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T11:51:34.473+07:00  INFO 95902 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 3
2025-09-18T11:51:34.481+07:00  INFO 95902 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T11:51:50.338+07:00  INFO 95902 --- [qtp2101609336-66] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T11:51:50.339+07:00  INFO 95902 --- [qtp2101609336-159] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T11:51:50.347+07:00  INFO 95902 --- [qtp2101609336-159] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T11:51:50.347+07:00  INFO 95902 --- [qtp2101609336-66] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T11:52:02.527+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:52:15.823+07:00  INFO 95902 --- [qtp2101609336-162] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T11:52:15.824+07:00  INFO 95902 --- [qtp2101609336-164] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T11:52:15.832+07:00  INFO 95902 --- [qtp2101609336-162] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T11:52:15.832+07:00  INFO 95902 --- [qtp2101609336-164] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T11:53:05.640+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:53:38.722+07:00  INFO 95902 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-18T11:53:38.736+07:00  INFO 95902 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T11:54:06.779+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:55:04.876+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T11:55:04.880+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:55:38.968+07:00  INFO 95902 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 8
2025-09-18T11:55:38.977+07:00  INFO 95902 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T11:55:43.848+07:00  INFO 95902 --- [qtp2101609336-66] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T11:55:43.854+07:00  INFO 95902 --- [qtp2101609336-162] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T11:55:43.864+07:00  INFO 95902 --- [qtp2101609336-162] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T11:55:43.864+07:00  INFO 95902 --- [qtp2101609336-66] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T11:56:06.025+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:56:13.856+07:00  INFO 95902 --- [qtp2101609336-77] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T11:56:13.857+07:00  INFO 95902 --- [qtp2101609336-41] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T11:56:13.884+07:00  INFO 95902 --- [qtp2101609336-77] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T11:56:13.884+07:00  INFO 95902 --- [qtp2101609336-41] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T11:56:58.860+07:00  INFO 95902 --- [qtp2101609336-163] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T11:56:58.861+07:00  INFO 95902 --- [qtp2101609336-177] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T11:56:58.868+07:00  INFO 95902 --- [qtp2101609336-163] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T11:56:58.869+07:00  INFO 95902 --- [qtp2101609336-177] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T11:57:04.139+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:57:39.239+07:00  INFO 95902 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-18T11:57:39.256+07:00  INFO 95902 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T11:58:04.850+07:00  INFO 95902 --- [qtp2101609336-163] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T11:58:04.864+07:00  INFO 95902 --- [qtp2101609336-163] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T11:58:04.964+07:00  INFO 95902 --- [qtp2101609336-215] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T11:58:05.031+07:00  INFO 95902 --- [qtp2101609336-215] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T11:58:05.081+07:00 ERROR 95902 --- [qtp2101609336-163] n.d.m.monitor.call.EndpointCallContext   : Start call with component EntityTaskService, method searchEntityTasks, arguments
[ {
  "tenantId" : "default",
  "companyId" : 8,
  "companyParentId" : 4,
  "companyCode" : "beehph",
  "companyLabel" : "Bee HPH",
  "companyFullName" : "BEE LOGISTICS CORPORATION - HAI PHONG BRANCH OFFICE",
  "loginId" : "kai.vnhph",
  "accountId" : 1994,
  "token" : "4a758e39123fe8b5a2364bcefc38f355",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node01cs9u1owkii1f12z7ha9hg5udx0",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : {
    "beehph" : {
      "companyId" : 8,
      "companyParentId" : 4,
      "companyCode" : "beehph",
      "companyLabel" : "Bee HPH",
      "companyFullName" : null
    }
  },
  "featurePermissions" : [ {
    "id" : 14630,
    "appId" : 3,
    "appModule" : "project",
    "appName" : "project",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 2116,
    "appId" : 26,
    "appModule" : "okr",
    "appName" : "okr",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 12452,
    "appId" : 66,
    "appModule" : "tms",
    "appName" : "tms-bill-company",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Read"
  }, {
    "id" : 11672,
    "appId" : 69,
    "appModule" : "hr",
    "appName" : "user-kpi",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 13280,
    "appId" : 79,
    "appModule" : "company",
    "appName" : "user-asset",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 8564,
    "appId" : 16,
    "appModule" : "logistics",
    "appName" : "user-logistics-sales",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 8562,
    "appId" : 2,
    "appModule" : "user",
    "appName" : "my-employee-space",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 18464,
    "appId" : 88,
    "appModule" : "user",
    "appName" : "my-space",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  } ],
  "attributes" : { },
  "clientId" : "default:kai.vnhph"
}, null, {
  "params" : {
    "accountId" : 1994
  },
  "filters" : [ {
    "name" : "search",
    "filterType" : "String",
    "filterValue" : "",
    "required" : true
  } ],
  "optionFilters" : [ {
    "name" : "storageState",
    "filterType" : "NotSet",
    "required" : true,
    "multiple" : true,
    "options" : [ "ACTIVE", "ARCHIVED" ],
    "selectOptions" : [ "ACTIVE" ]
  } ],
  "rangeFilters" : [ {
    "name" : "createdTime",
    "filterType" : "NotSet",
    "required" : true
  }, {
    "name" : "modifiedTime",
    "filterType" : "NotSet",
    "required" : true
  } ],
  "orderBy" : {
    "fields" : [ "modifiedTime" ],
    "selectFields" : [ ],
    "sort" : "DESC"
  },
  "maxReturn" : 5000
} ]
2025-09-18T11:58:05.081+07:00 ERROR 95902 --- [qtp2101609336-167] n.d.m.monitor.call.EndpointCallContext   : Start call with component EntityTaskService, method searchEntityTasks, arguments
[ {
  "tenantId" : "default",
  "companyId" : 8,
  "companyParentId" : 4,
  "companyCode" : "beehph",
  "companyLabel" : "Bee HPH",
  "companyFullName" : "BEE LOGISTICS CORPORATION - HAI PHONG BRANCH OFFICE",
  "loginId" : "kai.vnhph",
  "accountId" : 1994,
  "token" : "4a758e39123fe8b5a2364bcefc38f355",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node01cs9u1owkii1f12z7ha9hg5udx0",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : {
    "beehph" : {
      "companyId" : 8,
      "companyParentId" : 4,
      "companyCode" : "beehph",
      "companyLabel" : "Bee HPH",
      "companyFullName" : null
    }
  },
  "featurePermissions" : [ {
    "id" : 14630,
    "appId" : 3,
    "appModule" : "project",
    "appName" : "project",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 2116,
    "appId" : 26,
    "appModule" : "okr",
    "appName" : "okr",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 12452,
    "appId" : 66,
    "appModule" : "tms",
    "appName" : "tms-bill-company",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Read"
  }, {
    "id" : 11672,
    "appId" : 69,
    "appModule" : "hr",
    "appName" : "user-kpi",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 13280,
    "appId" : 79,
    "appModule" : "company",
    "appName" : "user-asset",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 8564,
    "appId" : 16,
    "appModule" : "logistics",
    "appName" : "user-logistics-sales",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 8562,
    "appId" : 2,
    "appModule" : "user",
    "appName" : "my-employee-space",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 18464,
    "appId" : 88,
    "appModule" : "user",
    "appName" : "my-space",
    "companyId" : 8,
    "loginId" : "kai.vnhph",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  } ],
  "attributes" : { },
  "clientId" : "default:kai.vnhph"
}, null, {
  "params" : {
    "accountId" : 1994
  },
  "filters" : [ {
    "name" : "search",
    "filterType" : "String",
    "filterValue" : "",
    "required" : true
  } ],
  "optionFilters" : [ {
    "name" : "storageState",
    "filterType" : "NotSet",
    "required" : true,
    "multiple" : true,
    "options" : [ "ACTIVE", "ARCHIVED" ],
    "selectOptions" : [ "ACTIVE" ]
  } ],
  "rangeFilters" : [ {
    "name" : "createdTime",
    "filterType" : "NotSet",
    "required" : true
  }, {
    "name" : "modifiedTime",
    "filterType" : "NotSet",
    "required" : true
  } ],
  "orderBy" : {
    "fields" : [ "modifiedTime" ],
    "selectFields" : [ ],
    "sort" : "DESC"
  },
  "maxReturn" : 5000
} ]
2025-09-18T11:58:05.082+07:00 ERROR 95902 --- [qtp2101609336-167] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:115)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Cannot invoke "net.datatp.module.data.db.entity.ICompany.getId()" because "company" is null
	at net.datatp.module.wfms.EntityTaskService.searchEntityTasks(EntityTaskService.java:34)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.wfms.EntityTaskService$$SpringCGLIB$$0.searchEntityTasks(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted

2025-09-18T11:58:05.094+07:00  INFO 95902 --- [qtp2101609336-167] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint EntityTaskService/searchEntityTasks
2025-09-18T11:58:05.081+07:00 ERROR 95902 --- [qtp2101609336-163] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:115)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Cannot invoke "net.datatp.module.data.db.entity.ICompany.getId()" because "company" is null
	at net.datatp.module.wfms.EntityTaskService.searchEntityTasks(EntityTaskService.java:34)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.wfms.EntityTaskService$$SpringCGLIB$$0.searchEntityTasks(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted

2025-09-18T11:58:05.095+07:00  INFO 95902 --- [qtp2101609336-163] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint EntityTaskService/searchEntityTasks
2025-09-18T11:58:06.299+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:58:14.826+07:00  INFO 95902 --- [qtp2101609336-162] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T11:58:14.827+07:00  INFO 95902 --- [qtp2101609336-215] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T11:58:14.835+07:00  INFO 95902 --- [qtp2101609336-215] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T11:58:14.835+07:00  INFO 95902 --- [qtp2101609336-162] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T11:59:03.416+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T11:59:38.527+07:00  INFO 95902 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T11:59:38.544+07:00  INFO 95902 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T11:59:46.869+07:00  INFO 95902 --- [qtp2101609336-215] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T11:59:46.900+07:00  INFO 95902 --- [qtp2101609336-215] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T11:59:46.939+07:00  INFO 95902 --- [qtp2101609336-214] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T11:59:46.959+07:00  INFO 95902 --- [qtp2101609336-214] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T12:00:06.586+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at 12 PM every day
2025-09-18T12:00:06.597+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-09-18T12:00:06.601+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 3 hour
2025-09-18T12:00:06.601+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T12:00:06.602+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-18T12:00:06.632+07:00  INFO 95902 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 18/09/2025@12:00:06+0700
2025-09-18T12:00:06.698+07:00  INFO 95902 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 18/09/2025@12:00:00+0700 to 18/09/2025@12:15:00+0700
2025-09-18T12:00:06.698+07:00  INFO 95902 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 18/09/2025@12:00:00+0700 to 18/09/2025@12:15:00+0700
2025-09-18T12:00:06.699+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T12:00:07.873+07:00  INFO 95902 --- [qtp2101609336-215] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T12:00:07.892+07:00  INFO 95902 --- [qtp2101609336-215] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T12:00:07.903+07:00  INFO 95902 --- [qtp2101609336-212] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T12:00:07.913+07:00  INFO 95902 --- [qtp2101609336-212] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T12:01:02.815+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T12:01:23.862+07:00  INFO 95902 --- [qtp2101609336-164] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T12:01:23.879+07:00  INFO 95902 --- [qtp2101609336-164] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T12:01:23.898+07:00  INFO 95902 --- [qtp2101609336-167] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T12:01:23.909+07:00  INFO 95902 --- [qtp2101609336-167] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T12:01:37.958+07:00  INFO 95902 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 0
2025-09-18T12:01:37.968+07:00  INFO 95902 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T12:01:59.874+07:00  INFO 95902 --- [qtp2101609336-167] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T12:01:59.878+07:00  INFO 95902 --- [qtp2101609336-215] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T12:01:59.899+07:00  INFO 95902 --- [qtp2101609336-215] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T12:01:59.899+07:00  INFO 95902 --- [qtp2101609336-167] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T12:02:06.011+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T12:02:41.861+07:00  INFO 95902 --- [qtp2101609336-215] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T12:02:41.862+07:00  INFO 95902 --- [qtp2101609336-164] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T12:02:41.887+07:00  INFO 95902 --- [qtp2101609336-215] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T12:02:41.887+07:00  INFO 95902 --- [qtp2101609336-164] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T12:03:02.110+07:00  INFO 95902 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T12:03:04.849+07:00  INFO 95902 --- [qtp2101609336-212] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T12:03:04.850+07:00  INFO 95902 --- [qtp2101609336-167] n.d.module.session.ClientSessionManager  : Add a client session id = node01cs9u1owkii1f12z7ha9hg5udx0, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T12:03:04.860+07:00  INFO 95902 --- [qtp2101609336-167] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T12:03:04.863+07:00  INFO 95902 --- [qtp2101609336-212] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T12:03:13.308+07:00  INFO 95902 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@c496c76{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-18T12:03:13.312+07:00  INFO 95902 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-18T12:03:13.312+07:00  INFO 95902 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-18T12:03:13.312+07:00  INFO 95902 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-18T12:03:13.314+07:00  INFO 95902 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-18T12:03:13.314+07:00  INFO 95902 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-18T12:03:13.314+07:00  INFO 95902 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-18T12:03:13.314+07:00  INFO 95902 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-18T12:03:13.314+07:00  INFO 95902 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-09-18T12:03:13.314+07:00  INFO 95902 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-09-18T12:03:13.314+07:00  INFO 95902 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-18T12:03:13.314+07:00  INFO 95902 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-09-18T12:03:13.314+07:00  INFO 95902 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-09-18T12:03:13.314+07:00  INFO 95902 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-18T12:03:13.314+07:00  INFO 95902 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-09-18T12:03:13.314+07:00  INFO 95902 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-09-18T12:03:13.362+07:00  INFO 95902 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T12:03:13.628+07:00  INFO 95902 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-18T12:03:13.635+07:00  INFO 95902 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-18T12:03:13.677+07:00  INFO 95902 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-18T12:03:13.692+07:00  INFO 95902 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-18T12:03:13.721+07:00  INFO 95902 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-18T12:03:13.723+07:00  INFO 95902 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-18T12:03:13.724+07:00  INFO 95902 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-18T12:03:13.724+07:00  INFO 95902 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-18T12:03:13.724+07:00  INFO 95902 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-18T12:03:13.724+07:00  INFO 95902 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-18T12:03:13.725+07:00  INFO 95902 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-18T12:03:13.725+07:00  INFO 95902 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-18T12:03:13.725+07:00  INFO 95902 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-18T12:03:13.725+07:00  INFO 95902 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-18T12:03:13.725+07:00  INFO 95902 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-18T12:03:13.737+07:00  INFO 95902 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@16908f89{STOPPING}[12.0.15,sto=0]
2025-09-18T12:03:13.743+07:00  INFO 95902 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-18T12:03:13.745+07:00  INFO 95902 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@49a2e86e{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.10759617939487991537/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@69959ea5{STOPPED}}
