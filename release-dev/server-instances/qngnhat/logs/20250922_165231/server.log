2025-09-22T16:52:32.054+07:00  INFO 46651 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 46651 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-22T16:52:32.054+07:00  INFO 46651 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-22T16:52:32.788+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:52:32.862+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 70 ms. Found 22 JPA repository interfaces.
2025-09-22T16:52:32.872+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:52:32.874+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-22T16:52:32.874+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:52:32.922+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 47 ms. Found 10 JPA repository interfaces.
2025-09-22T16:52:32.923+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:52:32.926+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-22T16:52:32.934+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:52:32.938+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-09-22T16:52:32.947+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:52:32.949+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-22T16:52:32.949+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:52:32.953+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-22T16:52:32.955+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:52:32.960+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 5 JPA repository interfaces.
2025-09-22T16:52:32.963+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:52:32.966+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-22T16:52:32.966+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:52:32.966+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-22T16:52:32.967+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:52:32.973+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 10 JPA repository interfaces.
2025-09-22T16:52:32.977+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:52:32.980+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-22T16:52:32.983+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:52:32.986+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-22T16:52:32.986+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:52:32.994+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-09-22T16:52:32.994+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:52:32.997+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-22T16:52:32.998+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:52:32.998+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-22T16:52:32.998+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:52:32.999+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-22T16:52:32.999+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:52:33.003+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-22T16:52:33.003+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:52:33.004+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-22T16:52:33.005+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:52:33.005+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-22T16:52:33.005+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:52:33.016+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 19 JPA repository interfaces.
2025-09-22T16:52:33.026+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:52:33.032+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 8 JPA repository interfaces.
2025-09-22T16:52:33.032+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:52:33.035+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-09-22T16:52:33.035+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:52:33.039+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-22T16:52:33.039+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:52:33.046+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 9 JPA repository interfaces.
2025-09-22T16:52:33.046+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:52:33.051+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-09-22T16:52:33.052+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:52:33.065+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 12 ms. Found 13 JPA repository interfaces.
2025-09-22T16:52:33.065+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:52:33.076+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 14 JPA repository interfaces.
2025-09-22T16:52:33.076+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:52:33.091+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 14 ms. Found 24 JPA repository interfaces.
2025-09-22T16:52:33.091+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:52:33.092+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-22T16:52:33.098+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:52:33.099+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-22T16:52:33.099+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:52:33.106+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-09-22T16:52:33.108+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:52:33.147+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 38 ms. Found 66 JPA repository interfaces.
2025-09-22T16:52:33.147+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:52:33.148+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-22T16:52:33.153+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-22T16:52:33.157+07:00  INFO 46651 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-09-22T16:52:33.410+07:00  INFO 46651 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-22T16:52:33.414+07:00  INFO 46651 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-22T16:52:33.864+07:00  WARN 46651 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-22T16:52:34.110+07:00  INFO 46651 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-22T16:52:34.112+07:00  INFO 46651 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-22T16:52:34.124+07:00  INFO 46651 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-22T16:52:34.124+07:00  INFO 46651 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1949 ms
2025-09-22T16:52:34.187+07:00  WARN 46651 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-22T16:52:34.187+07:00  INFO 46651 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-22T16:52:34.284+07:00  INFO 46651 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@2d44025b
2025-09-22T16:52:34.285+07:00  INFO 46651 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-22T16:52:34.290+07:00  WARN 46651 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-22T16:52:34.290+07:00  INFO 46651 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-22T16:52:34.298+07:00  INFO 46651 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@113b39e9
2025-09-22T16:52:34.298+07:00  INFO 46651 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-22T16:52:34.298+07:00  WARN 46651 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-22T16:52:34.298+07:00  INFO 46651 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-22T16:52:34.306+07:00  INFO 46651 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@60ee6f76
2025-09-22T16:52:34.307+07:00  INFO 46651 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-22T16:52:34.307+07:00  WARN 46651 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-22T16:52:34.307+07:00  INFO 46651 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-22T16:52:34.315+07:00  INFO 46651 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@659f5f32
2025-09-22T16:52:34.315+07:00  INFO 46651 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-22T16:52:34.315+07:00  WARN 46651 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-22T16:52:34.315+07:00  INFO 46651 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-22T16:52:34.324+07:00  INFO 46651 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@816272a
2025-09-22T16:52:34.324+07:00  INFO 46651 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-22T16:52:34.324+07:00  INFO 46651 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-22T16:52:34.370+07:00  INFO 46651 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-22T16:52:34.419+07:00  INFO 46651 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@21b05a8a{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.16339379005618276114/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@605a10fd{STARTED}}
2025-09-22T16:52:34.420+07:00  INFO 46651 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@21b05a8a{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.16339379005618276114/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@605a10fd{STARTED}}
2025-09-22T16:52:34.422+07:00  INFO 46651 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@39000894{STARTING}[12.0.15,sto=0] @3061ms
2025-09-22T16:52:34.477+07:00  INFO 46651 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-22T16:52:34.506+07:00  INFO 46651 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-22T16:52:34.523+07:00  INFO 46651 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-22T16:52:34.651+07:00  INFO 46651 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-22T16:52:34.678+07:00  WARN 46651 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-22T16:52:35.334+07:00  INFO 46651 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-22T16:52:35.343+07:00  INFO 46651 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@6ed68a2e] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-22T16:52:35.459+07:00  INFO 46651 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-22T16:52:35.666+07:00  INFO 46651 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.graphapi", "net.datatp.module.kpi", "net.datatp.module.monitor.activity", "net.datatp.module.wfms", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "net.datatp.module.okr", "cloud.datatp.module.fleet", "net.datatp.module.core.template", "cloud.datatp.gps", "cloud.datatp.module.pegasus.shipment", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "net.datatp.module.company.tmpl", "cloud.datatp.tms", "net.datatp.module.groovy", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "cloud.datatp.vendor", "net.datatp.module.resource", "net.datatp.module.hr", "net.datatp.module.company.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.core.print", "net.datatp.module.chatbot", "cloud.datatp.module.odoo", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-09-22T16:52:35.668+07:00  INFO 46651 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-22T16:52:35.675+07:00  INFO 46651 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-22T16:52:35.676+07:00  INFO 46651 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-22T16:52:35.712+07:00  INFO 46651 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-22T16:52:35.715+07:00  WARN 46651 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-22T16:52:38.238+07:00  INFO 46651 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-22T16:52:38.239+07:00  INFO 46651 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@2d6cd15f] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-22T16:52:38.413+07:00  WARN 46651 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-22T16:52:38.413+07:00  WARN 46651 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-22T16:52:38.419+07:00  WARN 46651 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-22T16:52:38.419+07:00  WARN 46651 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-22T16:52:38.434+07:00  WARN 46651 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-22T16:52:38.434+07:00  WARN 46651 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-22T16:52:38.819+07:00  INFO 46651 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-22T16:52:38.826+07:00  INFO 46651 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-22T16:52:38.827+07:00  INFO 46651 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-22T16:52:38.850+07:00  INFO 46651 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-22T16:52:38.853+07:00  WARN 46651 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-22T16:52:39.408+07:00  INFO 46651 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-22T16:52:39.409+07:00  INFO 46651 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@3300adc9] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-22T16:52:39.466+07:00  WARN 46651 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-22T16:52:39.466+07:00  WARN 46651 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-22T16:52:39.756+07:00  INFO 46651 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-22T16:52:39.786+07:00  INFO 46651 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-22T16:52:39.790+07:00  INFO 46651 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-22T16:52:39.791+07:00  INFO 46651 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T16:52:39.797+07:00  WARN 46651 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-22T16:52:39.977+07:00  INFO 46651 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-22T16:52:40.558+07:00  INFO 46651 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-22T16:52:40.562+07:00  INFO 46651 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-22T16:52:40.602+07:00  INFO 46651 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-22T16:52:40.653+07:00  INFO 46651 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-22T16:52:40.713+07:00  INFO 46651 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-22T16:52:40.746+07:00  INFO 46651 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-22T16:52:40.778+07:00  INFO 46651 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 602024008ms : this is harmless.
2025-09-22T16:52:40.791+07:00  INFO 46651 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-22T16:52:40.795+07:00  INFO 46651 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-22T16:52:40.808+07:00  INFO 46651 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 893529963ms : this is harmless.
2025-09-22T16:52:40.809+07:00  INFO 46651 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-22T16:52:40.825+07:00  INFO 46651 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-22T16:52:40.827+07:00  INFO 46651 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-22T16:52:43.297+07:00  INFO 46651 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-22T16:52:43.298+07:00  INFO 46651 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T16:52:43.299+07:00  WARN 46651 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-22T16:52:44.080+07:00  INFO 46651 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 22/09/2025@16:45:00+0700 to 22/09/2025@17:00:00+0700
2025-09-22T16:52:44.081+07:00  INFO 46651 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 22/09/2025@16:45:00+0700 to 22/09/2025@17:00:00+0700
2025-09-22T16:52:45.047+07:00  INFO 46651 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-09-22T16:52:45.048+07:00  INFO 46651 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T16:52:45.049+07:00  WARN 46651 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-22T16:52:45.490+07:00  INFO 46651 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-09-22T16:52:45.491+07:00  INFO 46651 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-09-22T16:52:45.491+07:00  INFO 46651 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-09-22T16:52:45.491+07:00  INFO 46651 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-09-22T16:52:45.491+07:00  INFO 46651 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-09-22T16:52:47.648+07:00  WARN 46651 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: b2431341-6d83-4ae6-8710-a35f0063787b

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-22T16:52:47.655+07:00  INFO 46651 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-09-22T16:52:47.994+07:00  INFO 46651 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-22T16:52:47.995+07:00  INFO 46651 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-22T16:52:47.995+07:00  INFO 46651 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-22T16:52:47.995+07:00  INFO 46651 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-22T16:52:47.995+07:00  INFO 46651 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-09-22T16:52:47.996+07:00  INFO 46651 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-09-22T16:52:47.996+07:00  INFO 46651 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-22T16:52:47.996+07:00  INFO 46651 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-09-22T16:52:47.996+07:00  INFO 46651 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-09-22T16:52:47.996+07:00  INFO 46651 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-22T16:52:47.996+07:00  INFO 46651 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-09-22T16:52:47.996+07:00  INFO 46651 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-09-22T16:52:47.999+07:00  INFO 46651 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-22T16:52:47.999+07:00  INFO 46651 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-22T16:52:47.999+07:00  INFO 46651 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-22T16:52:48.054+07:00  INFO 46651 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-22T16:52:48.054+07:00  INFO 46651 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-22T16:52:48.056+07:00  INFO 46651 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-09-22T16:52:48.067+07:00  INFO 46651 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@a317bdc{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-22T16:52:48.068+07:00  INFO 46651 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-09-22T16:52:48.069+07:00  INFO 46651 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-09-22T16:52:48.097+07:00  INFO 46651 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-09-22T16:52:48.097+07:00  INFO 46651 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-09-22T16:52:48.104+07:00  INFO 46651 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 16.374 seconds (process running for 16.742)
2025-09-22T16:52:52.890+07:00  INFO 46651 --- [qtp1728158149-39] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0pip2oido6f6zkktmmvks5bw41
2025-09-22T16:52:52.890+07:00  INFO 46651 --- [qtp1728158149-37] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0bngaxqif5mku1jynotmi44i2x0
2025-09-22T16:52:53.290+07:00  INFO 46651 --- [qtp1728158149-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0bngaxqif5mku1jynotmi44i2x0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:52:53.290+07:00  INFO 46651 --- [qtp1728158149-39] n.d.module.session.ClientSessionManager  : Add a client session id = node0pip2oido6f6zkktmmvks5bw41, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:52:54.013+07:00  INFO 46651 --- [qtp1728158149-37] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:52:54.028+07:00  INFO 46651 --- [qtp1728158149-39] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:53:02.040+07:00  INFO 46651 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T16:53:08.120+07:00  INFO 46651 --- [qtp1728158149-61] n.d.module.session.ClientSessionManager  : Add a client session id = node0bngaxqif5mku1jynotmi44i2x0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:53:08.121+07:00  INFO 46651 --- [qtp1728158149-41] n.d.module.session.ClientSessionManager  : Add a client session id = node0bngaxqif5mku1jynotmi44i2x0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:53:08.133+07:00  INFO 46651 --- [qtp1728158149-61] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:53:08.140+07:00  INFO 46651 --- [qtp1728158149-41] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:53:51.183+07:00  INFO 46651 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-09-22T16:53:51.204+07:00  INFO 46651 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T16:54:05.228+07:00  INFO 46651 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T16:54:15.188+07:00  INFO 46651 --- [qtp1728158149-39] n.d.module.session.ClientSessionManager  : Add a client session id = node0bngaxqif5mku1jynotmi44i2x0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:54:15.200+07:00  INFO 46651 --- [qtp1728158149-39] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:54:15.220+07:00  INFO 46651 --- [qtp1728158149-65] n.d.module.session.ClientSessionManager  : Add a client session id = node0bngaxqif5mku1jynotmi44i2x0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:54:15.231+07:00  INFO 46651 --- [qtp1728158149-65] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:54:20.076+07:00  INFO 46651 --- [qtp1728158149-62] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T16:54:20.076+07:00  INFO 46651 --- [qtp1728158149-63] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-22T16:54:20.167+07:00  INFO 46651 --- [qtp1728158149-63] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T16:54:20.171+07:00  INFO 46651 --- [qtp1728158149-62] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-22T16:55:06.324+07:00  INFO 46651 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T16:55:06.332+07:00  INFO 46651 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-22T16:55:13.675+07:00  INFO 46651 --- [qtp1728158149-41] n.d.module.session.ClientSessionManager  : Add a client session id = node0bngaxqif5mku1jynotmi44i2x0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:55:13.686+07:00  INFO 46651 --- [qtp1728158149-41] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:55:13.705+07:00  INFO 46651 --- [qtp1728158149-64] n.d.module.session.ClientSessionManager  : Add a client session id = node0bngaxqif5mku1jynotmi44i2x0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:55:13.718+07:00  INFO 46651 --- [qtp1728158149-64] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:55:30.682+07:00  INFO 46651 --- [qtp1728158149-39] n.d.module.session.ClientSessionManager  : Add a client session id = node0bngaxqif5mku1jynotmi44i2x0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:55:30.692+07:00  INFO 46651 --- [qtp1728158149-39] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:55:30.714+07:00  INFO 46651 --- [qtp1728158149-63] n.d.module.session.ClientSessionManager  : Add a client session id = node0bngaxqif5mku1jynotmi44i2x0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:55:30.724+07:00  INFO 46651 --- [qtp1728158149-63] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:55:35.097+07:00  INFO 46651 --- [qtp1728158149-39] n.d.module.session.ClientSessionManager  : Add a client session id = node0bngaxqif5mku1jynotmi44i2x0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:55:35.107+07:00  INFO 46651 --- [qtp1728158149-39] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:55:35.125+07:00  INFO 46651 --- [qtp1728158149-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0bngaxqif5mku1jynotmi44i2x0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:55:35.130+07:00  INFO 46651 --- [qtp1728158149-35] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:55:45.791+07:00  INFO 46651 --- [qtp1728158149-41] n.d.module.session.ClientSessionManager  : Add a client session id = node0bngaxqif5mku1jynotmi44i2x0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:55:45.804+07:00  INFO 46651 --- [qtp1728158149-41] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:55:45.833+07:00  INFO 46651 --- [qtp1728158149-73] n.d.module.session.ClientSessionManager  : Add a client session id = node0bngaxqif5mku1jynotmi44i2x0, token = 858f997ddf07ff57af7a0837d9dd5a42
2025-09-22T16:55:45.837+07:00  INFO 46651 --- [qtp1728158149-73] n.d.m.c.a.CompanyAuthenticationService   : User hanah.vnhph is logged in successfully system
2025-09-22T16:55:50.418+07:00  INFO 46651 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 0
2025-09-22T16:55:50.432+07:00  INFO 46651 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-22T16:56:04.460+07:00  INFO 46651 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-22T16:56:11.335+07:00  INFO 46651 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@a317bdc{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-22T16:56:11.337+07:00  INFO 46651 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-22T16:56:11.337+07:00  INFO 46651 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-22T16:56:11.337+07:00  INFO 46651 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-22T16:56:11.338+07:00  INFO 46651 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-22T16:56:11.338+07:00  INFO 46651 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-22T16:56:11.338+07:00  INFO 46651 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-22T16:56:11.338+07:00  INFO 46651 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-22T16:56:11.338+07:00  INFO 46651 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-09-22T16:56:11.338+07:00  INFO 46651 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-09-22T16:56:11.338+07:00  INFO 46651 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-22T16:56:11.338+07:00  INFO 46651 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-09-22T16:56:11.338+07:00  INFO 46651 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-09-22T16:56:11.338+07:00  INFO 46651 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-22T16:56:11.338+07:00  INFO 46651 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-09-22T16:56:11.338+07:00  INFO 46651 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-09-22T16:56:11.352+07:00  INFO 46651 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-22T16:56:11.470+07:00  INFO 46651 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-22T16:56:11.481+07:00  INFO 46651 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-22T16:56:11.503+07:00  INFO 46651 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-22T16:56:11.504+07:00  INFO 46651 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-22T16:56:11.505+07:00  INFO 46651 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-22T16:56:11.505+07:00  INFO 46651 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-22T16:56:11.506+07:00  INFO 46651 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-22T16:56:11.507+07:00  INFO 46651 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-22T16:56:11.507+07:00  INFO 46651 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-22T16:56:11.507+07:00  INFO 46651 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-22T16:56:11.507+07:00  INFO 46651 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-22T16:56:11.507+07:00  INFO 46651 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-22T16:56:11.508+07:00  INFO 46651 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-22T16:56:11.508+07:00  INFO 46651 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-22T16:56:11.508+07:00  INFO 46651 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-22T16:56:11.510+07:00  INFO 46651 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@39000894{STOPPING}[12.0.15,sto=0]
2025-09-22T16:56:11.512+07:00  INFO 46651 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-22T16:56:11.513+07:00  INFO 46651 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@21b05a8a{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.16339379005618276114/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@605a10fd{STOPPED}}
