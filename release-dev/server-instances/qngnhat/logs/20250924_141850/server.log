2025-09-24T14:18:51.462+07:00  INFO 66739 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 66739 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-24T14:18:51.462+07:00  INFO 66739 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-24T14:18:52.156+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:18:52.220+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 61 ms. Found 22 JPA repository interfaces.
2025-09-24T14:18:52.229+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:18:52.231+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-24T14:18:52.231+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:18:52.274+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 42 ms. Found 10 JPA repository interfaces.
2025-09-24T14:18:52.276+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:18:52.279+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-24T14:18:52.287+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:18:52.292+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-09-24T14:18:52.301+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:18:52.303+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-24T14:18:52.303+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:18:52.307+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-24T14:18:52.310+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:18:52.314+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 5 JPA repository interfaces.
2025-09-24T14:18:52.318+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:18:52.321+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-24T14:18:52.321+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:18:52.322+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-24T14:18:52.322+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:18:52.328+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 10 JPA repository interfaces.
2025-09-24T14:18:52.333+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:18:52.336+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-24T14:18:52.339+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:18:52.342+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-24T14:18:52.342+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:18:52.349+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-24T14:18:52.349+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:18:52.352+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-09-24T14:18:52.353+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:18:52.353+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-24T14:18:52.353+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:18:52.354+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-24T14:18:52.354+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:18:52.358+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-24T14:18:52.358+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:18:52.360+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-24T14:18:52.360+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:18:52.360+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-24T14:18:52.360+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:18:52.371+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 19 JPA repository interfaces.
2025-09-24T14:18:52.380+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:18:52.387+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 8 JPA repository interfaces.
2025-09-24T14:18:52.387+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:18:52.390+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-09-24T14:18:52.391+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:18:52.395+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-24T14:18:52.395+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:18:52.400+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-09-24T14:18:52.400+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:18:52.404+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-09-24T14:18:52.405+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:18:52.413+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 13 JPA repository interfaces.
2025-09-24T14:18:52.413+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:18:52.423+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 14 JPA repository interfaces.
2025-09-24T14:18:52.423+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:18:52.437+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 13 ms. Found 24 JPA repository interfaces.
2025-09-24T14:18:52.438+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:18:52.439+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-24T14:18:52.445+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:18:52.445+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-24T14:18:52.446+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:18:52.453+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-24T14:18:52.455+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:18:52.492+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 37 ms. Found 66 JPA repository interfaces.
2025-09-24T14:18:52.493+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:18:52.494+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-24T14:18:52.499+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:18:52.501+07:00  INFO 66739 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-24T14:18:52.815+07:00  INFO 66739 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-24T14:18:52.818+07:00  INFO 66739 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-24T14:18:53.087+07:00  WARN 66739 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-24T14:18:53.283+07:00  INFO 66739 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-24T14:18:53.285+07:00  INFO 66739 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-24T14:18:53.297+07:00  INFO 66739 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-24T14:18:53.297+07:00  INFO 66739 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1735 ms
2025-09-24T14:18:53.347+07:00  WARN 66739 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-24T14:18:53.347+07:00  INFO 66739 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-24T14:18:53.443+07:00  INFO 66739 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@20c4be8d
2025-09-24T14:18:53.444+07:00  INFO 66739 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-24T14:18:53.450+07:00  WARN 66739 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-24T14:18:53.450+07:00  INFO 66739 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-24T14:18:53.456+07:00  INFO 66739 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@21f8693e
2025-09-24T14:18:53.456+07:00  INFO 66739 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-24T14:18:53.456+07:00  WARN 66739 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-24T14:18:53.456+07:00  INFO 66739 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-24T14:18:53.468+07:00  INFO 66739 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@61b3de5b
2025-09-24T14:18:53.468+07:00  INFO 66739 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-24T14:18:53.469+07:00  WARN 66739 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-24T14:18:53.469+07:00  INFO 66739 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-24T14:18:53.474+07:00  INFO 66739 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@35408036
2025-09-24T14:18:53.474+07:00  INFO 66739 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-24T14:18:53.474+07:00  WARN 66739 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-24T14:18:53.474+07:00  INFO 66739 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-24T14:18:53.480+07:00  INFO 66739 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@5304ac92
2025-09-24T14:18:53.480+07:00  INFO 66739 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-24T14:18:53.480+07:00  INFO 66739 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-24T14:18:53.520+07:00  INFO 66739 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-24T14:18:53.570+07:00  INFO 66739 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@597f9d91{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.2895596014841438977/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@4f755506{STARTED}}
2025-09-24T14:18:53.570+07:00  INFO 66739 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@597f9d91{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.2895596014841438977/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@4f755506{STARTED}}
2025-09-24T14:18:53.572+07:00  INFO 66739 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@2c855eb7{STARTING}[12.0.15,sto=0] @2609ms
2025-09-24T14:18:53.627+07:00  INFO 66739 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-24T14:18:53.654+07:00  INFO 66739 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-24T14:18:53.669+07:00  INFO 66739 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-24T14:18:53.794+07:00  INFO 66739 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-24T14:18:53.834+07:00  WARN 66739 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-24T14:18:54.424+07:00  INFO 66739 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-24T14:18:54.432+07:00  INFO 66739 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@1f238bc9] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-24T14:18:54.577+07:00  INFO 66739 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-24T14:18:54.772+07:00  INFO 66739 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.graphapi", "net.datatp.module.kpi", "net.datatp.module.monitor.activity", "net.datatp.module.wfms", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "cloud.datatp.module.fleet", "net.datatp.module.okr", "net.datatp.module.core.template", "cloud.datatp.gps", "cloud.datatp.module.pegasus.shipment", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "net.datatp.module.groovy", "net.datatp.module.company.tmpl", "cloud.datatp.tms", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "cloud.datatp.vendor", "net.datatp.module.resource", "net.datatp.module.hr", "net.datatp.module.company.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "cloud.datatp.module.odoo", "net.datatp.module.core.print", "net.datatp.module.chatbot", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.zalo", "net.datatp.module.accounting" ]
2025-09-24T14:18:54.773+07:00  INFO 66739 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-24T14:18:54.779+07:00  INFO 66739 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-24T14:18:54.780+07:00  INFO 66739 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-24T14:18:54.804+07:00  INFO 66739 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-24T14:18:54.811+07:00  WARN 66739 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-24T14:18:56.814+07:00  INFO 66739 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-24T14:18:56.815+07:00  INFO 66739 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@40d9ca2e] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-24T14:18:56.999+07:00  WARN 66739 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-24T14:18:56.999+07:00  WARN 66739 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-24T14:18:57.020+07:00  WARN 66739 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-24T14:18:57.020+07:00  WARN 66739 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-24T14:18:57.033+07:00  WARN 66739 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-24T14:18:57.033+07:00  WARN 66739 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-24T14:18:57.542+07:00  INFO 66739 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-24T14:18:57.550+07:00  INFO 66739 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-24T14:18:57.551+07:00  INFO 66739 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-24T14:18:57.576+07:00  INFO 66739 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-24T14:18:57.578+07:00  WARN 66739 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-24T14:18:58.100+07:00  INFO 66739 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-24T14:18:58.101+07:00  INFO 66739 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@6cd19b58] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-24T14:18:58.171+07:00  WARN 66739 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-24T14:18:58.171+07:00  WARN 66739 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-24T14:18:58.526+07:00  INFO 66739 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-24T14:18:58.555+07:00  INFO 66739 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-24T14:18:58.559+07:00  INFO 66739 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-24T14:18:58.559+07:00  INFO 66739 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-24T14:18:58.566+07:00  WARN 66739 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-24T14:18:58.692+07:00  INFO 66739 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-24T14:18:59.131+07:00  INFO 66739 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-24T14:18:59.135+07:00  INFO 66739 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-24T14:18:59.170+07:00  INFO 66739 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-24T14:18:59.225+07:00  INFO 66739 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-24T14:18:59.291+07:00  INFO 66739 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-24T14:18:59.319+07:00  INFO 66739 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-24T14:18:59.350+07:00  INFO 66739 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 21387649ms : this is harmless.
2025-09-24T14:18:59.358+07:00  INFO 66739 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-24T14:18:59.361+07:00  INFO 66739 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-24T14:18:59.372+07:00  INFO 66739 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 21387639ms : this is harmless.
2025-09-24T14:18:59.374+07:00  INFO 66739 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-24T14:18:59.387+07:00  INFO 66739 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-24T14:18:59.388+07:00  INFO 66739 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-24T14:19:01.256+07:00  INFO 66739 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-24T14:19:01.257+07:00  INFO 66739 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-24T14:19:01.257+07:00  WARN 66739 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-24T14:19:01.547+07:00  INFO 66739 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 1 messages for session 24/09/2025@14:15:00+0700 to 24/09/2025@14:30:00+0700
2025-09-24T14:19:01.547+07:00  INFO 66739 --- [main] c.d.f.core.message.MessageQueueManager   : 📤 message : MAIL - inquiry-overdue-reminder - scheduled at 24/09/2025@14:28:46+0700
2025-09-24T14:19:01.547+07:00  INFO 66739 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 1 messages for session 24/09/2025@14:15:00+0700 to 24/09/2025@14:30:00+0700
2025-09-24T14:19:02.076+07:00  INFO 66739 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-09-24T14:19:02.076+07:00  INFO 66739 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-24T14:19:02.077+07:00  WARN 66739 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-24T14:19:02.351+07:00  INFO 66739 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-09-24T14:19:02.352+07:00  INFO 66739 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-09-24T14:19:02.352+07:00  INFO 66739 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-09-24T14:19:02.352+07:00  INFO 66739 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-09-24T14:19:02.352+07:00  INFO 66739 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-09-24T14:19:04.102+07:00  WARN 66739 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: be775526-e0ea-4f72-8f1b-0e99ecf5bfa4

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-24T14:19:04.105+07:00  INFO 66739 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-09-24T14:19:04.406+07:00  INFO 66739 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-24T14:19:04.406+07:00  INFO 66739 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-24T14:19:04.406+07:00  INFO 66739 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-24T14:19:04.406+07:00  INFO 66739 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-24T14:19:04.406+07:00  INFO 66739 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-09-24T14:19:04.406+07:00  INFO 66739 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-09-24T14:19:04.406+07:00  INFO 66739 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-24T14:19:04.406+07:00  INFO 66739 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-09-24T14:19:04.406+07:00  INFO 66739 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-09-24T14:19:04.406+07:00  INFO 66739 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-24T14:19:04.406+07:00  INFO 66739 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-09-24T14:19:04.406+07:00  INFO 66739 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-09-24T14:19:04.409+07:00  INFO 66739 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-24T14:19:04.409+07:00  INFO 66739 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-24T14:19:04.409+07:00  INFO 66739 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-24T14:19:04.527+07:00  INFO 66739 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-24T14:19:04.527+07:00  INFO 66739 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-24T14:19:04.528+07:00  INFO 66739 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-09-24T14:19:04.536+07:00  INFO 66739 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@5047a966{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-24T14:19:04.537+07:00  INFO 66739 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-09-24T14:19:04.538+07:00  INFO 66739 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-09-24T14:19:04.624+07:00  INFO 66739 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-09-24T14:19:04.624+07:00  INFO 66739 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-09-24T14:19:04.630+07:00  INFO 66739 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 13.443 seconds (process running for 13.668)
2025-09-24T14:19:41.011+07:00  INFO 66739 --- [qtp212402311-37] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01uhtwmuertknl10cnwaih86zz70
2025-09-24T14:19:41.011+07:00  INFO 66739 --- [qtp212402311-39] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01va1mwfr5kc15xj6gwwwbh47c1
2025-09-24T14:19:41.417+07:00  INFO 66739 --- [qtp212402311-37] n.d.module.session.ClientSessionManager  : Add a client session id = node01uhtwmuertknl10cnwaih86zz70, token = 5935f52c9fddb56e9b76246cabe26ea3
2025-09-24T14:19:41.418+07:00  INFO 66739 --- [qtp212402311-39] n.d.module.session.ClientSessionManager  : Add a client session id = node01va1mwfr5kc15xj6gwwwbh47c1, token = 5935f52c9fddb56e9b76246cabe26ea3
2025-09-24T14:19:41.837+07:00  INFO 66739 --- [qtp212402311-37] n.d.m.c.a.CompanyAuthenticationService   : User rio.vnsgn is logged in successfully system
2025-09-24T14:19:41.844+07:00  INFO 66739 --- [qtp212402311-39] n.d.m.c.a.CompanyAuthenticationService   : User rio.vnsgn is logged in successfully system
2025-09-24T14:20:06.556+07:00  INFO 66739 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T14:20:06.560+07:00  INFO 66739 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T14:20:07.629+07:00  INFO 66739 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-09-24T14:20:07.741+07:00  INFO 66739 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T14:21:03.834+07:00  INFO 66739 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T14:22:06.945+07:00  INFO 66739 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T14:22:06.987+07:00  INFO 66739 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-24T14:22:06.991+07:00  INFO 66739 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T14:23:03.096+07:00  INFO 66739 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T14:24:06.209+07:00  INFO 66739 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T14:24:11.236+07:00  INFO 66739 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-24T14:24:11.250+07:00  INFO 66739 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T14:25:02.327+07:00  INFO 66739 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T14:25:02.331+07:00  INFO 66739 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T14:26:05.449+07:00  INFO 66739 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T14:26:11.481+07:00  INFO 66739 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-24T14:26:11.490+07:00  INFO 66739 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T14:27:06.576+07:00  INFO 66739 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T14:28:04.695+07:00  INFO 66739 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T14:28:10.712+07:00  INFO 66739 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T14:28:10.719+07:00  INFO 66739 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T14:28:53.554+07:00  INFO 66739 --- [Scheduler-2113971262-1] n.d.m.session.AppHttpSessionListener     : The session node01va1mwfr5kc15xj6gwwwbh47c1 is destroyed.
2025-09-24T14:29:06.811+07:00  INFO 66739 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T14:29:42.927+07:00  INFO 66739 --- [qtp212402311-40] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-09-24T14:30:03.906+07:00  INFO 66739 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T14:30:03.908+07:00  INFO 66739 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T14:30:03.909+07:00  INFO 66739 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-24T14:30:03.912+07:00  INFO 66739 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 24/09/2025@14:30:03+0700
2025-09-24T14:30:03.938+07:00  INFO 66739 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 6 messages for session 24/09/2025@14:30:00+0700 to 24/09/2025@14:45:00+0700
2025-09-24T14:30:03.938+07:00  INFO 66739 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 📤 message : MAIL - inquiry-overdue-reminder - scheduled at 24/09/2025@14:35:53+0700
2025-09-24T14:30:03.938+07:00  INFO 66739 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 📤 message : MAIL - inquiry-overdue-reminder - scheduled at 24/09/2025@14:36:01+0700
2025-09-24T14:30:03.938+07:00  INFO 66739 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 📤 message : MAIL - inquiry-overdue-reminder - scheduled at 24/09/2025@14:36:08+0700
2025-09-24T14:30:03.939+07:00  INFO 66739 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 📤 message : MAIL - inquiry-overdue-reminder - scheduled at 24/09/2025@14:36:14+0700
2025-09-24T14:30:03.939+07:00  INFO 66739 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 📤 message : MAIL - inquiry-overdue-reminder - scheduled at 24/09/2025@14:36:20+0700
2025-09-24T14:30:03.939+07:00  INFO 66739 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 📤 message : MAIL - inquiry-overdue-reminder - scheduled at 24/09/2025@14:36:27+0700
2025-09-24T14:30:03.939+07:00  INFO 66739 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 6 messages for session 24/09/2025@14:30:00+0700 to 24/09/2025@14:45:00+0700
2025-09-24T14:30:10.985+07:00  INFO 66739 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 3
2025-09-24T14:30:10.996+07:00  INFO 66739 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T14:30:44.059+07:00  INFO 66739 --- [qtp212402311-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01uhtwmuertknl10cnwaih86zz70, token = 5935f52c9fddb56e9b76246cabe26ea3
2025-09-24T14:30:44.061+07:00  INFO 66739 --- [qtp212402311-40] n.d.module.session.ClientSessionManager  : Add a client session id = node01uhtwmuertknl10cnwaih86zz70, token = 5935f52c9fddb56e9b76246cabe26ea3
2025-09-24T14:30:44.082+07:00  INFO 66739 --- [qtp212402311-40] n.d.m.c.a.CompanyAuthenticationService   : User rio.vnsgn is logged in successfully system
2025-09-24T14:30:44.082+07:00  INFO 66739 --- [qtp212402311-36] n.d.m.c.a.CompanyAuthenticationService   : User rio.vnsgn is logged in successfully system
2025-09-24T14:31:06.089+07:00  INFO 66739 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T14:32:03.182+07:00  INFO 66739 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T14:32:10.233+07:00  INFO 66739 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-09-24T14:32:10.244+07:00  INFO 66739 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T14:33:06.330+07:00  INFO 66739 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T14:34:02.408+07:00  INFO 66739 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T14:34:09.444+07:00  INFO 66739 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-24T14:34:09.453+07:00  INFO 66739 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T14:35:05.542+07:00  INFO 66739 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T14:35:05.543+07:00  INFO 66739 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T14:36:06.634+07:00  INFO 66739 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T14:36:08.648+07:00  INFO 66739 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T14:36:08.654+07:00  INFO 66739 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T14:36:30.161+07:00  INFO 66739 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@5047a966{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-24T14:36:30.166+07:00  INFO 66739 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-24T14:36:30.166+07:00  INFO 66739 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-24T14:36:30.166+07:00  INFO 66739 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-24T14:36:30.167+07:00  INFO 66739 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-24T14:36:30.167+07:00  INFO 66739 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-24T14:36:30.167+07:00  INFO 66739 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-24T14:36:30.167+07:00  INFO 66739 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-24T14:36:30.167+07:00  INFO 66739 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-09-24T14:36:30.167+07:00  INFO 66739 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-09-24T14:36:30.168+07:00  INFO 66739 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-24T14:36:30.168+07:00  INFO 66739 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-09-24T14:36:30.168+07:00  INFO 66739 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-09-24T14:36:30.168+07:00  INFO 66739 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-24T14:36:30.168+07:00  INFO 66739 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-09-24T14:36:30.168+07:00  INFO 66739 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-09-24T14:36:30.186+07:00  INFO 66739 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T14:36:30.293+07:00  INFO 66739 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-24T14:36:30.299+07:00  INFO 66739 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-24T14:36:30.320+07:00  INFO 66739 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-24T14:36:30.321+07:00  INFO 66739 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-24T14:36:30.322+07:00  INFO 66739 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-24T14:36:30.323+07:00  INFO 66739 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-24T14:36:30.324+07:00  INFO 66739 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-24T14:36:30.324+07:00  INFO 66739 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-24T14:36:30.324+07:00  INFO 66739 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-24T14:36:30.324+07:00  INFO 66739 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-24T14:36:30.325+07:00  INFO 66739 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-24T14:36:30.325+07:00  INFO 66739 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-24T14:36:30.325+07:00  INFO 66739 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-24T14:36:30.325+07:00  INFO 66739 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-24T14:36:30.326+07:00  INFO 66739 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-24T14:36:30.331+07:00  INFO 66739 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@2c855eb7{STOPPING}[12.0.15,sto=0]
2025-09-24T14:36:30.334+07:00  INFO 66739 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-24T14:36:30.336+07:00  INFO 66739 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@597f9d91{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.2895596014841438977/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@4f755506{STOPPED}}
