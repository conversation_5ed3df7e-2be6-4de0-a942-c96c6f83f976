2025-09-24T15:29:51.959+07:00  INFO 75004 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 75004 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-24T15:29:51.960+07:00  INFO 75004 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-24T15:29:52.627+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:29:52.690+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 60 ms. Found 22 JPA repository interfaces.
2025-09-24T15:29:52.700+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:29:52.701+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-24T15:29:52.702+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:29:52.744+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 42 ms. Found 10 JPA repository interfaces.
2025-09-24T15:29:52.745+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:29:52.748+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-24T15:29:52.756+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:29:52.761+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-09-24T15:29:52.769+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:29:52.771+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-24T15:29:52.772+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:29:52.776+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-24T15:29:52.778+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:29:52.782+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-09-24T15:29:52.786+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:29:52.789+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-24T15:29:52.789+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:29:52.789+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-24T15:29:52.789+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:29:52.795+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 10 JPA repository interfaces.
2025-09-24T15:29:52.800+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:29:52.803+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-24T15:29:52.806+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:29:52.811+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-24T15:29:52.811+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:29:52.818+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-24T15:29:52.818+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:29:52.821+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-24T15:29:52.821+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:29:52.821+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-24T15:29:52.821+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:29:52.822+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-24T15:29:52.822+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:29:52.826+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-24T15:29:52.826+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:29:52.828+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-24T15:29:52.828+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:29:52.828+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-24T15:29:52.828+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:29:52.839+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 19 JPA repository interfaces.
2025-09-24T15:29:52.849+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:29:52.855+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 8 JPA repository interfaces.
2025-09-24T15:29:52.855+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:29:52.858+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-09-24T15:29:52.858+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:29:52.862+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-24T15:29:52.862+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:29:52.867+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-09-24T15:29:52.867+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:29:52.872+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-09-24T15:29:52.873+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:29:52.881+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 13 JPA repository interfaces.
2025-09-24T15:29:52.881+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:29:52.892+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 14 JPA repository interfaces.
2025-09-24T15:29:52.892+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:29:52.907+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 15 ms. Found 24 JPA repository interfaces.
2025-09-24T15:29:52.908+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:29:52.910+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-24T15:29:52.916+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:29:52.917+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-24T15:29:52.917+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:29:52.924+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-24T15:29:52.926+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:29:52.963+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 37 ms. Found 66 JPA repository interfaces.
2025-09-24T15:29:52.963+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:29:52.965+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-24T15:29:52.970+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:29:52.973+07:00  INFO 75004 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-09-24T15:29:53.210+07:00  INFO 75004 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-24T15:29:53.213+07:00  INFO 75004 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-24T15:29:53.478+07:00  WARN 75004 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-24T15:29:53.662+07:00  INFO 75004 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-24T15:29:53.664+07:00  INFO 75004 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-24T15:29:53.675+07:00  INFO 75004 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-24T15:29:53.675+07:00  INFO 75004 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1613 ms
2025-09-24T15:29:53.724+07:00  WARN 75004 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-24T15:29:53.725+07:00  INFO 75004 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-24T15:29:53.817+07:00  INFO 75004 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@1b13e4fb
2025-09-24T15:29:53.817+07:00  INFO 75004 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-24T15:29:53.822+07:00  WARN 75004 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-24T15:29:53.822+07:00  INFO 75004 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-24T15:29:53.828+07:00  INFO 75004 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@3bf92d96
2025-09-24T15:29:53.828+07:00  INFO 75004 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-24T15:29:53.828+07:00  WARN 75004 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-24T15:29:53.828+07:00  INFO 75004 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-24T15:29:53.836+07:00  INFO 75004 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@18f37d6e
2025-09-24T15:29:53.836+07:00  INFO 75004 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-24T15:29:53.837+07:00  WARN 75004 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-24T15:29:53.837+07:00  INFO 75004 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-24T15:29:53.844+07:00  INFO 75004 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@7a213820
2025-09-24T15:29:53.844+07:00  INFO 75004 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-24T15:29:53.844+07:00  WARN 75004 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-24T15:29:53.844+07:00  INFO 75004 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-24T15:29:53.852+07:00  INFO 75004 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@4d484961
2025-09-24T15:29:53.853+07:00  INFO 75004 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-24T15:29:53.853+07:00  INFO 75004 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-24T15:29:53.895+07:00  INFO 75004 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-24T15:29:53.954+07:00  INFO 75004 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@35eb95d3{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.9836632349724752376/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@3c320c8b{STARTED}}
2025-09-24T15:29:53.955+07:00  INFO 75004 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@35eb95d3{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.9836632349724752376/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@3c320c8b{STARTED}}
2025-09-24T15:29:53.956+07:00  INFO 75004 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@1a42da0a{STARTING}[12.0.15,sto=0] @2555ms
2025-09-24T15:29:54.010+07:00  INFO 75004 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-24T15:29:54.036+07:00  INFO 75004 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-24T15:29:54.053+07:00  INFO 75004 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-24T15:29:54.178+07:00  INFO 75004 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-24T15:29:54.211+07:00  WARN 75004 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-24T15:29:54.822+07:00  INFO 75004 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-24T15:29:54.829+07:00  INFO 75004 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@ac61487] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-24T15:29:54.973+07:00  INFO 75004 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-24T15:29:55.168+07:00  INFO 75004 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.monitor.activity", "net.datatp.module.wfms", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "net.datatp.module.okr", "cloud.datatp.module.fleet", "net.datatp.module.core.template", "cloud.datatp.gps", "cloud.datatp.module.pegasus.shipment", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "net.datatp.module.company.tmpl", "cloud.datatp.tms", "net.datatp.module.groovy", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "net.datatp.module.resource", "cloud.datatp.vendor", "net.datatp.module.hr", "net.datatp.module.company.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "cloud.datatp.module.odoo", "net.datatp.module.chatbot", "net.datatp.module.core.print", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-09-24T15:29:55.169+07:00  INFO 75004 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-24T15:29:55.175+07:00  INFO 75004 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-24T15:29:55.177+07:00  INFO 75004 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-24T15:29:55.203+07:00  INFO 75004 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-24T15:29:55.211+07:00  WARN 75004 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-24T15:29:57.288+07:00  INFO 75004 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-24T15:29:57.289+07:00  INFO 75004 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@7cd041d3] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-24T15:29:57.510+07:00  WARN 75004 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-24T15:29:57.510+07:00  WARN 75004 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-24T15:29:57.522+07:00  WARN 75004 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-24T15:29:57.522+07:00  WARN 75004 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-24T15:29:57.535+07:00  WARN 75004 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-24T15:29:57.535+07:00  WARN 75004 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-24T15:29:57.990+07:00  INFO 75004 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-24T15:29:57.996+07:00  INFO 75004 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-24T15:29:57.997+07:00  INFO 75004 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-24T15:29:58.019+07:00  INFO 75004 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-24T15:29:58.022+07:00  WARN 75004 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-24T15:29:58.560+07:00  INFO 75004 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-24T15:29:58.561+07:00  INFO 75004 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@4584e75f] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-24T15:29:58.632+07:00  WARN 75004 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-24T15:29:58.632+07:00  WARN 75004 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-24T15:29:58.979+07:00  INFO 75004 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-24T15:29:59.010+07:00  INFO 75004 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-24T15:29:59.014+07:00  INFO 75004 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-24T15:29:59.014+07:00  INFO 75004 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-24T15:29:59.020+07:00  WARN 75004 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-24T15:29:59.150+07:00  INFO 75004 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-24T15:29:59.604+07:00  INFO 75004 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-24T15:29:59.607+07:00  INFO 75004 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-24T15:29:59.641+07:00  INFO 75004 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-24T15:29:59.698+07:00  INFO 75004 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-24T15:29:59.799+07:00  INFO 75004 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-24T15:29:59.827+07:00  INFO 75004 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-24T15:29:59.848+07:00  INFO 75004 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 25653001ms : this is harmless.
2025-09-24T15:29:59.856+07:00  INFO 75004 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-24T15:29:59.859+07:00  INFO 75004 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-24T15:29:59.878+07:00  INFO 75004 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 25652991ms : this is harmless.
2025-09-24T15:29:59.879+07:00  INFO 75004 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-24T15:29:59.893+07:00  INFO 75004 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-24T15:29:59.894+07:00  INFO 75004 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-24T15:30:01.718+07:00  INFO 75004 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-24T15:30:01.718+07:00  INFO 75004 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-24T15:30:01.719+07:00  WARN 75004 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-24T15:30:02.003+07:00  INFO 75004 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 2 messages for session 24/09/2025@15:30:00+0700 to 24/09/2025@15:45:00+0700
2025-09-24T15:30:02.003+07:00  INFO 75004 --- [main] c.d.f.core.message.MessageQueueManager   : 📤 message : MAIL - inquiry-overdue-reminder - scheduled at 24/09/2025@15:30:35+0700
2025-09-24T15:30:02.003+07:00  INFO 75004 --- [main] c.d.f.core.message.MessageQueueManager   : 📤 message : MAIL - inquiry-overdue-reminder - scheduled at 24/09/2025@15:35:25+0700
2025-09-24T15:30:02.003+07:00  INFO 75004 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 2 messages for session 24/09/2025@15:30:00+0700 to 24/09/2025@15:45:00+0700
2025-09-24T15:30:02.554+07:00  INFO 75004 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-09-24T15:30:02.555+07:00  INFO 75004 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-24T15:30:02.555+07:00  WARN 75004 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-24T15:30:02.852+07:00  INFO 75004 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-09-24T15:30:02.852+07:00  INFO 75004 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-09-24T15:30:02.852+07:00  INFO 75004 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-09-24T15:30:02.852+07:00  INFO 75004 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-09-24T15:30:02.852+07:00  INFO 75004 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-09-24T15:30:04.565+07:00  WARN 75004 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 862559ad-61b0-42e9-9bb7-8d6e3cbb9963

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-24T15:30:04.568+07:00  INFO 75004 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-09-24T15:30:04.857+07:00  INFO 75004 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-24T15:30:04.857+07:00  INFO 75004 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-24T15:30:04.857+07:00  INFO 75004 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-24T15:30:04.857+07:00  INFO 75004 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-24T15:30:04.857+07:00  INFO 75004 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-09-24T15:30:04.857+07:00  INFO 75004 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-09-24T15:30:04.857+07:00  INFO 75004 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-24T15:30:04.857+07:00  INFO 75004 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-09-24T15:30:04.857+07:00  INFO 75004 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-09-24T15:30:04.857+07:00  INFO 75004 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-24T15:30:04.857+07:00  INFO 75004 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-09-24T15:30:04.857+07:00  INFO 75004 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-09-24T15:30:04.858+07:00  INFO 75004 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-24T15:30:04.858+07:00  INFO 75004 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-24T15:30:04.858+07:00  INFO 75004 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-24T15:30:04.927+07:00  INFO 75004 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-24T15:30:04.927+07:00  INFO 75004 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-24T15:30:04.929+07:00  INFO 75004 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-09-24T15:30:04.936+07:00  INFO 75004 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@e7fc6bb{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-24T15:30:04.937+07:00  INFO 75004 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-09-24T15:30:04.938+07:00  INFO 75004 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-09-24T15:30:04.970+07:00  INFO 75004 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-09-24T15:30:04.971+07:00  INFO 75004 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-09-24T15:30:04.976+07:00  INFO 75004 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 13.319 seconds (process running for 13.576)
2025-09-24T15:30:06.489+07:00  INFO 75004 --- [qtp1504482477-36] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-09-24T15:30:18.823+07:00  INFO 75004 --- [qtp1504482477-36] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01r2vvfgdr3hoajhfvl83awkww1
2025-09-24T15:30:18.823+07:00  INFO 75004 --- [qtp1504482477-40] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0fwyxpcavjd09h2ho6vj8uq900
2025-09-24T15:30:18.887+07:00  INFO 75004 --- [qtp1504482477-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01r2vvfgdr3hoajhfvl83awkww1, token = 2a9ec688fede8ee860dd8809db6a4091
2025-09-24T15:30:18.902+07:00  INFO 75004 --- [qtp1504482477-40] n.d.module.session.ClientSessionManager  : Add a client session id = node0fwyxpcavjd09h2ho6vj8uq900, token = 2a9ec688fede8ee860dd8809db6a4091
2025-09-24T15:30:19.275+07:00  INFO 75004 --- [qtp1504482477-36] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T15:30:19.281+07:00  INFO 75004 --- [qtp1504482477-40] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T15:31:06.964+07:00  INFO 75004 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:31:08.001+07:00  INFO 75004 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-24T15:31:08.028+07:00  INFO 75004 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T15:32:04.112+07:00  INFO 75004 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:32:27.487+07:00  INFO 75004 --- [qtp1504482477-40] n.d.module.session.ClientSessionManager  : Add a client session id = node0fwyxpcavjd09h2ho6vj8uq900, token = 2a9ec688fede8ee860dd8809db6a4091
2025-09-24T15:32:27.500+07:00  INFO 75004 --- [qtp1504482477-40] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T15:32:27.539+07:00  INFO 75004 --- [qtp1504482477-61] n.d.module.session.ClientSessionManager  : Add a client session id = node0fwyxpcavjd09h2ho6vj8uq900, token = 2a9ec688fede8ee860dd8809db6a4091
2025-09-24T15:32:27.585+07:00  INFO 75004 --- [qtp1504482477-61] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T15:32:31.133+07:00  INFO 75004 --- [qtp1504482477-61] n.d.module.session.ClientSessionManager  : Add a client session id = node0fwyxpcavjd09h2ho6vj8uq900, token = 2a9ec688fede8ee860dd8809db6a4091
2025-09-24T15:32:31.146+07:00  INFO 75004 --- [qtp1504482477-61] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T15:32:31.195+07:00  INFO 75004 --- [qtp1504482477-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0fwyxpcavjd09h2ho6vj8uq900, token = 2a9ec688fede8ee860dd8809db6a4091
2025-09-24T15:32:31.211+07:00  INFO 75004 --- [qtp1504482477-34] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T15:33:06.226+07:00  INFO 75004 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:33:07.304+07:00  INFO 75004 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-09-24T15:33:07.330+07:00  INFO 75004 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T15:33:23.307+07:00  INFO 75004 --- [qtp1504482477-61] n.d.module.session.ClientSessionManager  : Add a client session id = node0fwyxpcavjd09h2ho6vj8uq900, token = 2a9ec688fede8ee860dd8809db6a4091
2025-09-24T15:33:23.309+07:00  INFO 75004 --- [qtp1504482477-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0fwyxpcavjd09h2ho6vj8uq900, token = 2a9ec688fede8ee860dd8809db6a4091
2025-09-24T15:33:23.321+07:00  INFO 75004 --- [qtp1504482477-61] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T15:33:23.321+07:00  INFO 75004 --- [qtp1504482477-34] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T15:33:26.458+07:00  INFO 75004 --- [qtp1504482477-61] n.d.module.session.ClientSessionManager  : Add a client session id = node0fwyxpcavjd09h2ho6vj8uq900, token = 2a9ec688fede8ee860dd8809db6a4091
2025-09-24T15:33:26.470+07:00  INFO 75004 --- [qtp1504482477-61] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T15:33:26.485+07:00  INFO 75004 --- [qtp1504482477-41] n.d.module.session.ClientSessionManager  : Add a client session id = node0fwyxpcavjd09h2ho6vj8uq900, token = 2a9ec688fede8ee860dd8809db6a4091
2025-09-24T15:33:26.536+07:00  INFO 75004 --- [qtp1504482477-41] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T15:34:03.433+07:00  INFO 75004 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:35:06.534+07:00  INFO 75004 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T15:35:06.538+07:00  INFO 75004 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:35:11.581+07:00  INFO 75004 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-24T15:35:11.590+07:00  INFO 75004 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T15:36:02.686+07:00  INFO 75004 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:37:05.806+07:00  INFO 75004 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:37:11.848+07:00  INFO 75004 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-24T15:37:11.859+07:00  INFO 75004 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T15:38:06.945+07:00  INFO 75004 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:39:05.054+07:00  INFO 75004 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:39:11.060+07:00  INFO 75004 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T15:39:11.063+07:00  INFO 75004 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T15:39:53.910+07:00  INFO 75004 --- [Scheduler-1473083361-1] n.d.m.session.AppHttpSessionListener     : The session node01r2vvfgdr3hoajhfvl83awkww1 is destroyed.
2025-09-24T15:40:06.150+07:00  INFO 75004 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T15:40:06.151+07:00  INFO 75004 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:40:28.614+07:00  INFO 75004 --- [qtp1504482477-34] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-09-24T15:41:04.312+07:00  INFO 75004 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:41:11.357+07:00  INFO 75004 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-24T15:41:11.367+07:00  INFO 75004 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T15:41:29.686+07:00  INFO 75004 --- [qtp1504482477-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0fwyxpcavjd09h2ho6vj8uq900, token = 2a9ec688fede8ee860dd8809db6a4091
2025-09-24T15:41:29.695+07:00  INFO 75004 --- [qtp1504482477-34] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T15:41:29.730+07:00  INFO 75004 --- [qtp1504482477-68] n.d.module.session.ClientSessionManager  : Add a client session id = node0fwyxpcavjd09h2ho6vj8uq900, token = 2a9ec688fede8ee860dd8809db6a4091
2025-09-24T15:41:29.760+07:00  INFO 75004 --- [qtp1504482477-68] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T15:42:06.443+07:00  INFO 75004 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:42:56.862+07:00  INFO 75004 --- [qtp1504482477-68] n.d.module.session.ClientSessionManager  : Add a client session id = node0fwyxpcavjd09h2ho6vj8uq900, token = 2a9ec688fede8ee860dd8809db6a4091
2025-09-24T15:42:56.874+07:00  INFO 75004 --- [qtp1504482477-68] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T15:42:56.918+07:00  INFO 75004 --- [qtp1504482477-71] n.d.module.session.ClientSessionManager  : Add a client session id = node0fwyxpcavjd09h2ho6vj8uq900, token = 2a9ec688fede8ee860dd8809db6a4091
2025-09-24T15:42:56.961+07:00  INFO 75004 --- [qtp1504482477-71] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T15:43:03.572+07:00  INFO 75004 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:43:10.644+07:00  INFO 75004 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 1
2025-09-24T15:43:10.654+07:00  INFO 75004 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T15:44:01.755+07:00  INFO 75004 --- [qtp1504482477-71] n.d.module.session.ClientSessionManager  : Add a client session id = node0fwyxpcavjd09h2ho6vj8uq900, token = 2a9ec688fede8ee860dd8809db6a4091
2025-09-24T15:44:01.761+07:00  INFO 75004 --- [qtp1504482477-71] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T15:44:01.815+07:00  INFO 75004 --- [qtp1504482477-92] n.d.module.session.ClientSessionManager  : Add a client session id = node0fwyxpcavjd09h2ho6vj8uq900, token = 2a9ec688fede8ee860dd8809db6a4091
2025-09-24T15:44:01.833+07:00  INFO 75004 --- [qtp1504482477-92] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T15:44:06.748+07:00  INFO 75004 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:45:02.847+07:00  INFO 75004 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:45:02.850+07:00  INFO 75004 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-24T15:45:02.853+07:00  INFO 75004 --- [qtp1504482477-66] n.d.module.session.ClientSessionManager  : Add a client session id = node0fwyxpcavjd09h2ho6vj8uq900, token = 2a9ec688fede8ee860dd8809db6a4091
2025-09-24T15:45:02.856+07:00  INFO 75004 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 24/09/2025@15:45:02+0700
2025-09-24T15:45:02.875+07:00  INFO 75004 --- [qtp1504482477-72] n.d.module.session.ClientSessionManager  : Add a client session id = node0fwyxpcavjd09h2ho6vj8uq900, token = 2a9ec688fede8ee860dd8809db6a4091
2025-09-24T15:45:02.897+07:00  INFO 75004 --- [qtp1504482477-66] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T15:45:02.901+07:00  INFO 75004 --- [qtp1504482477-72] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T15:45:02.920+07:00  INFO 75004 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 24/09/2025@15:45:00+0700 to 24/09/2025@16:00:00+0700
2025-09-24T15:45:02.943+07:00  INFO 75004 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 24/09/2025@15:45:00+0700 to 24/09/2025@16:00:00+0700
2025-09-24T15:45:02.943+07:00  INFO 75004 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T15:45:09.985+07:00  INFO 75004 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-24T15:45:09.988+07:00  INFO 75004 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T15:45:34.646+07:00  INFO 75004 --- [qtp1504482477-72] n.d.module.session.ClientSessionManager  : Add a client session id = node0fwyxpcavjd09h2ho6vj8uq900, token = 2a9ec688fede8ee860dd8809db6a4091
2025-09-24T15:45:34.656+07:00  INFO 75004 --- [qtp1504482477-72] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T15:45:34.782+07:00  INFO 75004 --- [qtp1504482477-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0fwyxpcavjd09h2ho6vj8uq900, token = 2a9ec688fede8ee860dd8809db6a4091
2025-09-24T15:45:34.810+07:00  INFO 75004 --- [qtp1504482477-37] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T15:45:40.866+07:00  INFO 75004 --- [qtp1504482477-72] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph logout successfully 
2025-09-24T15:46:06.085+07:00  INFO 75004 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:46:07.500+07:00  INFO 75004 --- [qtp1504482477-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0fwyxpcavjd09h2ho6vj8uq900, token = 595025c2817cb490acbec03f16dabd18
2025-09-24T15:46:07.509+07:00  INFO 75004 --- [qtp1504482477-37] n.d.m.c.a.CompanyAuthenticationService   : User rio.vnsgn is logged in successfully system
2025-09-24T15:46:44.159+07:00  INFO 75004 --- [qtp1504482477-40] n.d.module.session.ClientSessionManager  : Add a client session id = node0fwyxpcavjd09h2ho6vj8uq900, token = 595025c2817cb490acbec03f16dabd18
2025-09-24T15:46:44.160+07:00  INFO 75004 --- [qtp1504482477-66] n.d.module.session.ClientSessionManager  : Add a client session id = node0fwyxpcavjd09h2ho6vj8uq900, token = 595025c2817cb490acbec03f16dabd18
2025-09-24T15:46:44.170+07:00  INFO 75004 --- [qtp1504482477-40] n.d.m.c.a.CompanyAuthenticationService   : User rio.vnsgn is logged in successfully system
2025-09-24T15:46:44.173+07:00  INFO 75004 --- [qtp1504482477-66] n.d.m.c.a.CompanyAuthenticationService   : User rio.vnsgn is logged in successfully system
2025-09-24T15:47:02.183+07:00  INFO 75004 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:47:09.228+07:00  INFO 75004 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 15, expire count 0
2025-09-24T15:47:09.252+07:00  INFO 75004 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 2, expire count 2
2025-09-24T15:48:05.359+07:00  INFO 75004 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:48:22.770+07:00  INFO 75004 --- [qtp1504482477-34] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-24T15:48:22.770+07:00  INFO 75004 --- [qtp1504482477-40] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-24T15:48:22.906+07:00  INFO 75004 --- [qtp1504482477-40] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 8 records
2025-09-24T15:48:22.906+07:00  INFO 75004 --- [qtp1504482477-34] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 8 records
2025-09-24T15:49:06.459+07:00  INFO 75004 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:49:08.498+07:00  INFO 75004 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-24T15:49:08.502+07:00  INFO 75004 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T15:50:04.587+07:00  INFO 75004 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:50:04.589+07:00  INFO 75004 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T15:51:06.665+07:00  INFO 75004 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:51:07.706+07:00  INFO 75004 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 1
2025-09-24T15:51:07.711+07:00  INFO 75004 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T15:52:03.801+07:00  INFO 75004 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:53:06.908+07:00  INFO 75004 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:53:11.931+07:00  INFO 75004 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-24T15:53:11.941+07:00  INFO 75004 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T15:53:46.638+07:00  INFO 75004 --- [qtp1504482477-68] n.d.module.session.ClientSessionManager  : Add a client session id = node0fwyxpcavjd09h2ho6vj8uq900, token = 595025c2817cb490acbec03f16dabd18
2025-09-24T15:53:46.640+07:00  INFO 75004 --- [qtp1504482477-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0fwyxpcavjd09h2ho6vj8uq900, token = 595025c2817cb490acbec03f16dabd18
2025-09-24T15:53:46.658+07:00  INFO 75004 --- [qtp1504482477-34] n.d.m.c.a.CompanyAuthenticationService   : User rio.vnsgn is logged in successfully system
2025-09-24T15:53:46.658+07:00  INFO 75004 --- [qtp1504482477-68] n.d.m.c.a.CompanyAuthenticationService   : User rio.vnsgn is logged in successfully system
2025-09-24T15:54:03.035+07:00  INFO 75004 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:55:06.133+07:00  INFO 75004 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:55:06.134+07:00  INFO 75004 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T15:55:08.642+07:00  INFO 75004 --- [qtp1504482477-40] n.d.module.session.ClientSessionManager  : Add a client session id = node0fwyxpcavjd09h2ho6vj8uq900, token = 595025c2817cb490acbec03f16dabd18
2025-09-24T15:55:08.643+07:00  INFO 75004 --- [qtp1504482477-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0fwyxpcavjd09h2ho6vj8uq900, token = 595025c2817cb490acbec03f16dabd18
2025-09-24T15:55:08.658+07:00  INFO 75004 --- [qtp1504482477-40] n.d.m.c.a.CompanyAuthenticationService   : User rio.vnsgn is logged in successfully system
2025-09-24T15:55:08.658+07:00  INFO 75004 --- [qtp1504482477-34] n.d.m.c.a.CompanyAuthenticationService   : User rio.vnsgn is logged in successfully system
2025-09-24T15:55:11.162+07:00  INFO 75004 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 1
2025-09-24T15:55:11.176+07:00  INFO 75004 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T15:55:39.626+07:00  INFO 75004 --- [qtp1504482477-40] n.d.module.session.ClientSessionManager  : Add a client session id = node0fwyxpcavjd09h2ho6vj8uq900, token = 595025c2817cb490acbec03f16dabd18
2025-09-24T15:55:39.640+07:00  INFO 75004 --- [qtp1504482477-40] n.d.m.c.a.CompanyAuthenticationService   : User rio.vnsgn is logged in successfully system
2025-09-24T15:55:39.663+07:00  INFO 75004 --- [qtp1504482477-100] n.d.module.session.ClientSessionManager  : Add a client session id = node0fwyxpcavjd09h2ho6vj8uq900, token = 595025c2817cb490acbec03f16dabd18
2025-09-24T15:55:39.677+07:00  INFO 75004 --- [qtp1504482477-100] n.d.m.c.a.CompanyAuthenticationService   : User rio.vnsgn is logged in successfully system
2025-09-24T15:56:00.626+07:00  INFO 75004 --- [qtp1504482477-66] n.d.module.session.ClientSessionManager  : Add a client session id = node0fwyxpcavjd09h2ho6vj8uq900, token = 595025c2817cb490acbec03f16dabd18
2025-09-24T15:56:00.627+07:00  INFO 75004 --- [qtp1504482477-92] n.d.module.session.ClientSessionManager  : Add a client session id = node0fwyxpcavjd09h2ho6vj8uq900, token = 595025c2817cb490acbec03f16dabd18
2025-09-24T15:56:00.635+07:00  INFO 75004 --- [qtp1504482477-92] n.d.m.c.a.CompanyAuthenticationService   : User rio.vnsgn is logged in successfully system
2025-09-24T15:56:00.635+07:00  INFO 75004 --- [qtp1504482477-66] n.d.m.c.a.CompanyAuthenticationService   : User rio.vnsgn is logged in successfully system
2025-09-24T15:56:02.256+07:00  INFO 75004 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:57:05.358+07:00  INFO 75004 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:57:11.397+07:00  INFO 75004 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 12
2025-09-24T15:57:11.409+07:00  INFO 75004 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T15:58:06.449+07:00  INFO 75004 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:59:04.557+07:00  INFO 75004 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:59:11.606+07:00  INFO 75004 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 1
2025-09-24T15:59:11.611+07:00  INFO 75004 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T16:00:06.709+07:00  INFO 75004 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T16:00:06.735+07:00  INFO 75004 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-24T16:00:06.744+07:00  INFO 75004 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 24/09/2025@16:00:06+0700
2025-09-24T16:00:06.760+07:00  INFO 75004 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 24/09/2025@16:00:00+0700 to 24/09/2025@16:15:00+0700
2025-09-24T16:00:06.761+07:00  INFO 75004 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 24/09/2025@16:00:00+0700 to 24/09/2025@16:15:00+0700
2025-09-24T16:00:06.761+07:00  INFO 75004 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T16:00:06.762+07:00  INFO 75004 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-09-24T16:01:04.178+07:00  INFO 75004 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T16:01:11.205+07:00  INFO 75004 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-24T16:01:11.223+07:00  INFO 75004 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T16:02:06.319+07:00  INFO 75004 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T16:03:03.422+07:00  INFO 75004 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T16:03:10.453+07:00  INFO 75004 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-24T16:03:10.464+07:00  INFO 75004 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T16:03:41.897+07:00  INFO 75004 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@e7fc6bb{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-24T16:03:41.899+07:00  INFO 75004 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-24T16:03:41.899+07:00  INFO 75004 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-24T16:03:41.899+07:00  INFO 75004 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-24T16:03:41.899+07:00  INFO 75004 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-24T16:03:41.900+07:00  INFO 75004 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-24T16:03:41.900+07:00  INFO 75004 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-24T16:03:41.900+07:00  INFO 75004 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-24T16:03:41.900+07:00  INFO 75004 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-09-24T16:03:41.900+07:00  INFO 75004 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-09-24T16:03:41.900+07:00  INFO 75004 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-24T16:03:41.900+07:00  INFO 75004 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-09-24T16:03:41.900+07:00  INFO 75004 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-09-24T16:03:41.900+07:00  INFO 75004 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-24T16:03:41.900+07:00  INFO 75004 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-09-24T16:03:41.900+07:00  INFO 75004 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-09-24T16:03:41.914+07:00  INFO 75004 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T16:03:41.974+07:00  INFO 75004 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-24T16:03:41.980+07:00  INFO 75004 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-24T16:03:41.997+07:00  INFO 75004 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-24T16:03:41.998+07:00  INFO 75004 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-24T16:03:41.999+07:00  INFO 75004 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-24T16:03:41.999+07:00  INFO 75004 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-24T16:03:42.001+07:00  INFO 75004 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-24T16:03:42.001+07:00  INFO 75004 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-24T16:03:42.001+07:00  INFO 75004 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-24T16:03:42.001+07:00  INFO 75004 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-24T16:03:42.001+07:00  INFO 75004 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-24T16:03:42.001+07:00  INFO 75004 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-24T16:03:42.002+07:00  INFO 75004 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-24T16:03:42.002+07:00  INFO 75004 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-24T16:03:42.002+07:00  INFO 75004 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-24T16:03:42.003+07:00  INFO 75004 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@1a42da0a{STOPPING}[12.0.15,sto=0]
2025-09-24T16:03:42.005+07:00  INFO 75004 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-24T16:03:42.007+07:00  INFO 75004 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@35eb95d3{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.9836632349724752376/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@3c320c8b{STOPPED}}
