2025-09-18T09:10:04.048+07:00  INFO 80237 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 80237 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-18T09:10:04.049+07:00  INFO 80237 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-18T09:10:04.799+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T09:10:04.864+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 61 ms. Found 22 JPA repository interfaces.
2025-09-18T09:10:04.873+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T09:10:04.875+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-18T09:10:04.875+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T09:10:04.919+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 44 ms. Found 10 JPA repository interfaces.
2025-09-18T09:10:04.920+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T09:10:04.924+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-18T09:10:04.932+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T09:10:04.937+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-09-18T09:10:04.945+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T09:10:04.947+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 2 JPA repository interfaces.
2025-09-18T09:10:04.948+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T09:10:04.951+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-18T09:10:04.954+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T09:10:04.958+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-09-18T09:10:04.961+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T09:10:04.964+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-18T09:10:04.964+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T09:10:04.964+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-18T09:10:04.964+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T09:10:04.970+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 10 JPA repository interfaces.
2025-09-18T09:10:04.975+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T09:10:04.978+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-18T09:10:04.981+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T09:10:04.985+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-18T09:10:04.985+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T09:10:04.992+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-18T09:10:04.992+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T09:10:04.995+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-18T09:10:04.995+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T09:10:04.996+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-18T09:10:04.996+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T09:10:04.996+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-18T09:10:04.997+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T09:10:05.001+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-18T09:10:05.001+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T09:10:05.002+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-18T09:10:05.002+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T09:10:05.002+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-18T09:10:05.003+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T09:10:05.013+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 19 JPA repository interfaces.
2025-09-18T09:10:05.023+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T09:10:05.028+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 8 JPA repository interfaces.
2025-09-18T09:10:05.029+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T09:10:05.032+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-09-18T09:10:05.032+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T09:10:05.035+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-18T09:10:05.036+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T09:10:05.041+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-09-18T09:10:05.042+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T09:10:05.046+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-09-18T09:10:05.047+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T09:10:05.055+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 13 JPA repository interfaces.
2025-09-18T09:10:05.055+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T09:10:05.064+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 14 JPA repository interfaces.
2025-09-18T09:10:05.065+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T09:10:05.080+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 15 ms. Found 24 JPA repository interfaces.
2025-09-18T09:10:05.080+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T09:10:05.082+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-18T09:10:05.087+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T09:10:05.088+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-18T09:10:05.088+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T09:10:05.095+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-18T09:10:05.097+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T09:10:05.133+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 35 ms. Found 66 JPA repository interfaces.
2025-09-18T09:10:05.133+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T09:10:05.134+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-18T09:10:05.139+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-18T09:10:05.142+07:00  INFO 80237 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-09-18T09:10:05.365+07:00  INFO 80237 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-18T09:10:05.369+07:00  INFO 80237 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-18T09:10:05.702+07:00  WARN 80237 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-18T09:10:05.965+07:00  INFO 80237 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-18T09:10:05.967+07:00  INFO 80237 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-18T09:10:05.982+07:00  INFO 80237 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-18T09:10:05.982+07:00  INFO 80237 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1828 ms
2025-09-18T09:10:06.051+07:00  WARN 80237 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-18T09:10:06.051+07:00  INFO 80237 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-18T09:10:06.152+07:00  INFO 80237 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@579b1d86
2025-09-18T09:10:06.152+07:00  INFO 80237 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-18T09:10:06.157+07:00  WARN 80237 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-18T09:10:06.157+07:00  INFO 80237 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-18T09:10:06.161+07:00  INFO 80237 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@4015c65b
2025-09-18T09:10:06.161+07:00  INFO 80237 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-18T09:10:06.161+07:00  WARN 80237 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-18T09:10:06.161+07:00  INFO 80237 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-18T09:10:06.169+07:00  INFO 80237 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@2ea3f905
2025-09-18T09:10:06.169+07:00  INFO 80237 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-18T09:10:06.169+07:00  WARN 80237 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-18T09:10:06.169+07:00  INFO 80237 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-18T09:10:06.179+07:00  INFO 80237 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@4ab8c3c0
2025-09-18T09:10:06.179+07:00  INFO 80237 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-18T09:10:06.179+07:00  WARN 80237 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-18T09:10:06.179+07:00  INFO 80237 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-18T09:10:06.184+07:00  INFO 80237 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@71a18feb
2025-09-18T09:10:06.184+07:00  INFO 80237 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-18T09:10:06.184+07:00  INFO 80237 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-18T09:10:06.237+07:00  INFO 80237 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-18T09:10:06.293+07:00  INFO 80237 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@37eca4c0{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.8720327060594479752/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@5c452eb5{STARTED}}
2025-09-18T09:10:06.294+07:00  INFO 80237 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@37eca4c0{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.8720327060594479752/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@5c452eb5{STARTED}}
2025-09-18T09:10:06.295+07:00  INFO 80237 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@7e7f72{STARTING}[12.0.15,sto=0] @2797ms
2025-09-18T09:10:06.356+07:00  INFO 80237 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-18T09:10:06.385+07:00  INFO 80237 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-18T09:10:06.401+07:00  INFO 80237 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-18T09:10:06.540+07:00  INFO 80237 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-18T09:10:06.581+07:00  WARN 80237 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-18T09:10:07.420+07:00  INFO 80237 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-18T09:10:07.431+07:00  INFO 80237 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@44c164e5] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-18T09:10:07.585+07:00  INFO 80237 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-18T09:10:07.807+07:00  INFO 80237 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.wfms", "net.datatp.module.monitor.activity", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "cloud.datatp.module.fleet", "net.datatp.module.okr", "net.datatp.module.core.template", "cloud.datatp.module.pegasus.shipment", "cloud.datatp.gps", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "net.datatp.module.company.tmpl", "net.datatp.module.groovy", "cloud.datatp.tms", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "net.datatp.module.resource", "cloud.datatp.vendor", "net.datatp.module.hr", "net.datatp.module.company.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.core.print", "net.datatp.module.chatbot", "cloud.datatp.module.odoo", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-09-18T09:10:07.810+07:00  INFO 80237 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-18T09:10:07.818+07:00  INFO 80237 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-18T09:10:07.819+07:00  INFO 80237 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-18T09:10:07.848+07:00  INFO 80237 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-18T09:10:07.857+07:00  WARN 80237 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-18T09:10:10.045+07:00  INFO 80237 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-18T09:10:10.046+07:00  INFO 80237 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@763eb4a4] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-18T09:10:10.217+07:00  WARN 80237 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-18T09:10:10.217+07:00  WARN 80237 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-18T09:10:10.225+07:00  WARN 80237 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-18T09:10:10.225+07:00  WARN 80237 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-18T09:10:10.239+07:00  WARN 80237 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-18T09:10:10.239+07:00  WARN 80237 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-18T09:10:10.606+07:00  INFO 80237 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-18T09:10:10.612+07:00  INFO 80237 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-18T09:10:10.613+07:00  INFO 80237 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-18T09:10:10.631+07:00  INFO 80237 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-18T09:10:10.633+07:00  WARN 80237 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-18T09:10:11.152+07:00  INFO 80237 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-18T09:10:11.152+07:00  INFO 80237 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@510191e] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-18T09:10:11.207+07:00  WARN 80237 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-18T09:10:11.208+07:00  WARN 80237 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-18T09:10:11.511+07:00  INFO 80237 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-18T09:10:11.541+07:00  INFO 80237 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-18T09:10:11.545+07:00  INFO 80237 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-18T09:10:11.545+07:00  INFO 80237 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T09:10:11.551+07:00  WARN 80237 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-18T09:10:11.680+07:00  INFO 80237 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-18T09:10:12.128+07:00  INFO 80237 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-18T09:10:12.131+07:00  INFO 80237 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-18T09:10:12.165+07:00  INFO 80237 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-18T09:10:12.202+07:00  INFO 80237 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-18T09:10:12.313+07:00  INFO 80237 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-18T09:10:12.341+07:00  INFO 80237 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-18T09:10:12.366+07:00  INFO 80237 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 228678412ms : this is harmless.
2025-09-18T09:10:12.375+07:00  INFO 80237 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-18T09:10:12.377+07:00  INFO 80237 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-18T09:10:12.391+07:00  INFO 80237 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 520184366ms : this is harmless.
2025-09-18T09:10:12.392+07:00  INFO 80237 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-18T09:10:12.414+07:00  INFO 80237 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-18T09:10:12.415+07:00  INFO 80237 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-18T09:10:14.283+07:00  INFO 80237 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-18T09:10:14.283+07:00  INFO 80237 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T09:10:14.284+07:00  WARN 80237 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-18T09:10:14.586+07:00  INFO 80237 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 18/09/2025@09:00:00+0700 to 18/09/2025@09:15:00+0700
2025-09-18T09:10:14.586+07:00  INFO 80237 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 18/09/2025@09:00:00+0700 to 18/09/2025@09:15:00+0700
2025-09-18T09:10:15.094+07:00  INFO 80237 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-09-18T09:10:15.094+07:00  INFO 80237 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T09:10:15.094+07:00  WARN 80237 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-18T09:10:15.361+07:00  INFO 80237 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-09-18T09:10:15.361+07:00  INFO 80237 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-09-18T09:10:15.361+07:00  INFO 80237 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-09-18T09:10:15.361+07:00  INFO 80237 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-09-18T09:10:15.361+07:00  INFO 80237 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-09-18T09:10:17.167+07:00  WARN 80237 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: eb47643a-a264-430b-bede-f9c6f14b0a0f

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-18T09:10:17.170+07:00  INFO 80237 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-09-18T09:10:17.472+07:00  INFO 80237 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-18T09:10:17.473+07:00  INFO 80237 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-18T09:10:17.473+07:00  INFO 80237 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-18T09:10:17.473+07:00  INFO 80237 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-18T09:10:17.473+07:00  INFO 80237 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-09-18T09:10:17.473+07:00  INFO 80237 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-09-18T09:10:17.473+07:00  INFO 80237 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-18T09:10:17.473+07:00  INFO 80237 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-09-18T09:10:17.473+07:00  INFO 80237 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-09-18T09:10:17.473+07:00  INFO 80237 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-18T09:10:17.473+07:00  INFO 80237 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-09-18T09:10:17.473+07:00  INFO 80237 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-09-18T09:10:17.476+07:00  INFO 80237 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-18T09:10:17.476+07:00  INFO 80237 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-18T09:10:17.476+07:00  INFO 80237 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-18T09:10:17.537+07:00  INFO 80237 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-18T09:10:17.537+07:00  INFO 80237 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-18T09:10:17.538+07:00  INFO 80237 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-09-18T09:10:17.545+07:00  INFO 80237 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@7ee183c1{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-18T09:10:17.546+07:00  INFO 80237 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-09-18T09:10:17.547+07:00  INFO 80237 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-09-18T09:10:17.586+07:00  INFO 80237 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-09-18T09:10:17.586+07:00  INFO 80237 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-09-18T09:10:17.592+07:00  INFO 80237 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 13.847 seconds (process running for 14.093)
2025-09-18T09:10:28.986+07:00  INFO 80237 --- [qtp828508529-36] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-09-18T09:11:06.579+07:00  INFO 80237 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T09:11:20.641+07:00  INFO 80237 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T09:11:20.668+07:00  INFO 80237 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T09:11:25.155+07:00  INFO 80237 --- [qtp828508529-40] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node025qmjuw0gnva1k141xb2brhrz0
2025-09-18T09:11:25.155+07:00  INFO 80237 --- [qtp828508529-35] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0jlmu79i0nvy71y1sjryy50ecs1
2025-09-18T09:11:25.238+07:00  INFO 80237 --- [qtp828508529-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0jlmu79i0nvy71y1sjryy50ecs1, token = 04a8376faab3659affa6f86510a7de2d
2025-09-18T09:11:25.238+07:00  INFO 80237 --- [qtp828508529-40] n.d.module.session.ClientSessionManager  : Add a client session id = node025qmjuw0gnva1k141xb2brhrz0, token = 04a8376faab3659affa6f86510a7de2d
2025-09-18T09:11:25.676+07:00  INFO 80237 --- [qtp828508529-40] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T09:11:25.683+07:00  INFO 80237 --- [qtp828508529-35] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T09:11:28.257+07:00  INFO 80237 --- [qtp828508529-40] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T09:11:28.257+07:00  INFO 80237 --- [qtp828508529-36] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T09:11:28.312+07:00  INFO 80237 --- [qtp828508529-40] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-18T09:11:28.323+07:00  INFO 80237 --- [qtp828508529-36] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-18T09:11:51.907+07:00  INFO 80237 --- [qtp828508529-61] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T09:11:51.914+07:00  INFO 80237 --- [qtp828508529-40] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T09:11:51.918+07:00  INFO 80237 --- [qtp828508529-61] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-18T09:11:51.919+07:00  INFO 80237 --- [qtp828508529-40] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-18T09:12:02.727+07:00  INFO 80237 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T09:12:09.513+07:00  INFO 80237 --- [qtp828508529-61] c.d.f.core.message.CRMMessageLogic       : 📅 Scheduled message [id=22071] [company=bee] type=MAIL for 18/09/2025@09:12:09+0700
2025-09-18T09:12:09.513+07:00  INFO 80237 --- [qtp828508529-61] c.d.f.core.message.MessageQueueManager   : Added message [22071] - scheduled at 18/09/2025@09:12:09+0700 - current session (18/09/2025@09:00:00+0700 to 18/09/2025@09:15:00+0700)

2025-09-18T09:12:20.104+07:00  INFO 80237 --- [qtp828508529-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T09:12:20.106+07:00  INFO 80237 --- [qtp828508529-60] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T09:12:20.109+07:00  INFO 80237 --- [qtp828508529-37] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-18T09:12:20.110+07:00  INFO 80237 --- [qtp828508529-60] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-18T09:13:05.829+07:00  INFO 80237 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T09:13:19.892+07:00  INFO 80237 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 15, expire count 0
2025-09-18T09:13:19.921+07:00  INFO 80237 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-18T09:14:06.988+07:00  INFO 80237 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T09:14:45.448+07:00  INFO 80237 --- [qtp828508529-73] n.d.module.session.ClientSessionManager  : Add a client session id = node0jlmu79i0nvy71y1sjryy50ecs1, token = 04a8376faab3659affa6f86510a7de2d
2025-09-18T09:14:45.450+07:00  INFO 80237 --- [qtp828508529-61] n.d.module.session.ClientSessionManager  : Add a client session id = node0jlmu79i0nvy71y1sjryy50ecs1, token = 04a8376faab3659affa6f86510a7de2d
2025-09-18T09:14:45.460+07:00  INFO 80237 --- [qtp828508529-61] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T09:14:45.460+07:00  INFO 80237 --- [qtp828508529-73] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T09:14:48.143+07:00  INFO 80237 --- [qtp828508529-60] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T09:14:48.143+07:00  INFO 80237 --- [qtp828508529-36] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T09:14:48.152+07:00  INFO 80237 --- [qtp828508529-60] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-18T09:14:48.152+07:00  INFO 80237 --- [qtp828508529-36] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-18T09:14:59.056+07:00  INFO 80237 --- [qtp828508529-37] c.d.f.core.message.CRMMessageLogic       : 📅 Scheduled message [id=22072] [company=bee] type=MAIL for 18/09/2025@09:14:59+0700
2025-09-18T09:14:59.057+07:00  INFO 80237 --- [qtp828508529-37] c.d.f.core.message.MessageQueueManager   : Added message [22072] - scheduled at 18/09/2025@09:14:59+0700 - current session (18/09/2025@09:00:00+0700 to 18/09/2025@09:15:00+0700)

2025-09-18T09:15:05.113+07:00  INFO 80237 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T09:15:05.117+07:00  INFO 80237 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T09:15:05.118+07:00  INFO 80237 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-18T09:15:05.123+07:00  INFO 80237 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 18/09/2025@09:15:05+0700
2025-09-18T09:15:05.133+07:00  INFO 80237 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 18/09/2025@09:15:00+0700 to 18/09/2025@09:30:00+0700
2025-09-18T09:15:05.133+07:00  INFO 80237 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 18/09/2025@09:15:00+0700 to 18/09/2025@09:30:00+0700
2025-09-18T09:15:22.189+07:00  INFO 80237 --- [qtp828508529-73] n.d.module.session.ClientSessionManager  : Add a client session id = node0jlmu79i0nvy71y1sjryy50ecs1, token = 04a8376faab3659affa6f86510a7de2d
2025-09-18T09:15:22.201+07:00  INFO 80237 --- [qtp828508529-73] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T09:15:22.310+07:00  INFO 80237 --- [qtp828508529-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0jlmu79i0nvy71y1sjryy50ecs1, token = 04a8376faab3659affa6f86510a7de2d
2025-09-18T09:15:22.330+07:00  INFO 80237 --- [qtp828508529-37] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T09:15:24.215+07:00  INFO 80237 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 16, expire count 0
2025-09-18T09:15:24.228+07:00  INFO 80237 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-18T09:15:41.103+07:00  INFO 80237 --- [qtp828508529-61] n.d.module.session.ClientSessionManager  : Add a client session id = node0jlmu79i0nvy71y1sjryy50ecs1, token = 04a8376faab3659affa6f86510a7de2d
2025-09-18T09:15:41.114+07:00  INFO 80237 --- [qtp828508529-61] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T09:15:41.131+07:00  INFO 80237 --- [qtp828508529-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0jlmu79i0nvy71y1sjryy50ecs1, token = 04a8376faab3659affa6f86510a7de2d
2025-09-18T09:15:41.168+07:00  INFO 80237 --- [qtp828508529-36] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T09:15:55.533+07:00  INFO 80237 --- [qtp828508529-36] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T09:15:55.533+07:00  INFO 80237 --- [qtp828508529-34] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T09:15:55.546+07:00  INFO 80237 --- [qtp828508529-36] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-18T09:15:55.546+07:00  INFO 80237 --- [qtp828508529-34] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-18T09:16:06.318+07:00  INFO 80237 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T09:16:06.693+07:00  INFO 80237 --- [qtp828508529-74] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T09:16:06.693+07:00  INFO 80237 --- [qtp828508529-68] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T09:16:06.797+07:00  INFO 80237 --- [qtp828508529-74] c.d.f.s.report.PerformanceReportLogic    : Retrieved 17 records
2025-09-18T09:16:06.797+07:00  INFO 80237 --- [qtp828508529-68] c.d.f.s.report.PerformanceReportLogic    : Retrieved 17 records
2025-09-18T09:16:06.810+07:00  INFO 80237 --- [qtp828508529-74] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T09:16:06.810+07:00  INFO 80237 --- [qtp828508529-68] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T09:16:07.321+07:00  INFO 80237 --- [qtp828508529-74] c.d.f.s.report.PerformanceReportLogic    : Retrieved 21 records
2025-09-18T09:16:07.321+07:00  INFO 80237 --- [qtp828508529-68] c.d.f.s.report.PerformanceReportLogic    : Retrieved 21 records
2025-09-18T09:16:16.185+07:00  INFO 80237 --- [qtp828508529-36] c.d.f.core.message.CRMMessageLogic       : 📅 Scheduled message [id=22073] [company=bee] type=MAIL for 18/09/2025@09:16:16+0700
2025-09-18T09:16:16.185+07:00  INFO 80237 --- [qtp828508529-36] c.d.f.core.message.MessageQueueManager   : Added message [22073] - scheduled at 18/09/2025@09:16:16+0700 - current session (18/09/2025@09:15:00+0700 to 18/09/2025@09:30:00+0700)

2025-09-18T09:16:51.632+07:00  INFO 80237 --- [qtp828508529-61] n.d.module.session.ClientSessionManager  : Add a client session id = node0jlmu79i0nvy71y1sjryy50ecs1, token = 04a8376faab3659affa6f86510a7de2d
2025-09-18T09:16:51.647+07:00  INFO 80237 --- [qtp828508529-61] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T09:16:51.684+07:00  INFO 80237 --- [qtp828508529-68] n.d.module.session.ClientSessionManager  : Add a client session id = node0jlmu79i0nvy71y1sjryy50ecs1, token = 04a8376faab3659affa6f86510a7de2d
2025-09-18T09:16:51.711+07:00  INFO 80237 --- [qtp828508529-68] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T09:17:00.459+07:00  INFO 80237 --- [qtp828508529-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0jlmu79i0nvy71y1sjryy50ecs1, token = 04a8376faab3659affa6f86510a7de2d
2025-09-18T09:17:00.474+07:00  INFO 80237 --- [qtp828508529-35] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T09:17:00.496+07:00  INFO 80237 --- [qtp828508529-70] n.d.module.session.ClientSessionManager  : Add a client session id = node0jlmu79i0nvy71y1sjryy50ecs1, token = 04a8376faab3659affa6f86510a7de2d
2025-09-18T09:17:00.522+07:00  INFO 80237 --- [qtp828508529-70] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T09:17:04.426+07:00  INFO 80237 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T09:17:13.161+07:00  INFO 80237 --- [qtp828508529-62] n.d.module.session.ClientSessionManager  : Add a client session id = node0jlmu79i0nvy71y1sjryy50ecs1, token = 04a8376faab3659affa6f86510a7de2d
2025-09-18T09:17:13.162+07:00  INFO 80237 --- [qtp828508529-74] n.d.module.session.ClientSessionManager  : Add a client session id = node0jlmu79i0nvy71y1sjryy50ecs1, token = 04a8376faab3659affa6f86510a7de2d
2025-09-18T09:17:13.177+07:00  INFO 80237 --- [qtp828508529-62] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T09:17:13.177+07:00  INFO 80237 --- [qtp828508529-74] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T09:17:24.483+07:00  INFO 80237 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 15, expire count 0
2025-09-18T09:17:24.494+07:00  INFO 80237 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-18T09:17:24.661+07:00  INFO 80237 --- [qtp828508529-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0jlmu79i0nvy71y1sjryy50ecs1, token = 04a8376faab3659affa6f86510a7de2d
2025-09-18T09:17:24.668+07:00  INFO 80237 --- [qtp828508529-36] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T09:17:24.704+07:00  INFO 80237 --- [qtp828508529-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0jlmu79i0nvy71y1sjryy50ecs1, token = 04a8376faab3659affa6f86510a7de2d
2025-09-18T09:17:24.711+07:00  INFO 80237 --- [qtp828508529-34] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T09:17:27.748+07:00  INFO 80237 --- [qtp828508529-34] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T09:17:27.755+07:00  INFO 80237 --- [qtp828508529-74] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T09:17:27.766+07:00  INFO 80237 --- [qtp828508529-74] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-18T09:17:27.766+07:00  INFO 80237 --- [qtp828508529-34] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-18T09:17:52.067+07:00  INFO 80237 --- [qtp828508529-73] c.d.f.core.message.CRMMessageLogic       : 📅 Scheduled message [id=22074] [company=bee] type=MAIL for 18/09/2025@09:17:52+0700
2025-09-18T09:17:52.068+07:00  INFO 80237 --- [qtp828508529-73] c.d.f.core.message.MessageQueueManager   : Added message [22074] - scheduled at 18/09/2025@09:17:52+0700 - current session (18/09/2025@09:15:00+0700 to 18/09/2025@09:30:00+0700)

2025-09-18T09:17:58.601+07:00  INFO 80237 --- [qtp828508529-68] c.d.f.core.partner.PartnerRequestLogic   : ------------------------Partner---------------------------

2025-09-18T09:17:58.602+07:00  INFO 80237 --- [qtp828508529-68] c.d.f.core.partner.PartnerRequestLogic   : --------------------------------------------------------

2025-09-18T09:17:58.607+07:00  INFO 80237 --- [qtp828508529-68] c.d.f.core.message.CRMMessageLogic       : 📅 Scheduled message [id=22075] [company=bee] type=MAIL for 18/09/2025@09:17:58+0700
2025-09-18T09:17:58.607+07:00  INFO 80237 --- [qtp828508529-68] c.d.f.core.message.MessageQueueManager   : Added message [22075] - scheduled at 18/09/2025@09:17:58+0700 - current session (18/09/2025@09:15:00+0700 to 18/09/2025@09:30:00+0700)

2025-09-18T09:18:06.556+07:00  INFO 80237 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T09:18:30.347+07:00  INFO 80237 --- [qtp828508529-61] n.d.module.session.ClientSessionManager  : Add a client session id = node0jlmu79i0nvy71y1sjryy50ecs1, token = 04a8376faab3659affa6f86510a7de2d
2025-09-18T09:18:30.367+07:00  INFO 80237 --- [qtp828508529-61] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T09:18:30.392+07:00  INFO 80237 --- [qtp828508529-41] n.d.module.session.ClientSessionManager  : Add a client session id = node0jlmu79i0nvy71y1sjryy50ecs1, token = 04a8376faab3659affa6f86510a7de2d
2025-09-18T09:18:30.399+07:00  INFO 80237 --- [qtp828508529-41] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T09:18:36.777+07:00  INFO 80237 --- [qtp828508529-61] n.d.module.session.ClientSessionManager  : Add a client session id = node0jlmu79i0nvy71y1sjryy50ecs1, token = 04a8376faab3659affa6f86510a7de2d
2025-09-18T09:18:36.785+07:00  INFO 80237 --- [qtp828508529-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0jlmu79i0nvy71y1sjryy50ecs1, token = 04a8376faab3659affa6f86510a7de2d
2025-09-18T09:18:36.816+07:00  INFO 80237 --- [qtp828508529-36] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T09:18:36.817+07:00  INFO 80237 --- [qtp828508529-61] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T09:18:50.009+07:00  INFO 80237 --- [qtp828508529-61] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T09:18:50.009+07:00  INFO 80237 --- [qtp828508529-40] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T09:18:50.022+07:00  INFO 80237 --- [qtp828508529-61] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-18T09:18:50.022+07:00  INFO 80237 --- [qtp828508529-40] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-18T09:19:03.653+07:00  INFO 80237 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T09:19:16.080+07:00  INFO 80237 --- [qtp828508529-41] n.d.module.session.ClientSessionManager  : Add a client session id = node0jlmu79i0nvy71y1sjryy50ecs1, token = 04a8376faab3659affa6f86510a7de2d
2025-09-18T09:19:16.081+07:00  INFO 80237 --- [qtp828508529-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0jlmu79i0nvy71y1sjryy50ecs1, token = 04a8376faab3659affa6f86510a7de2d
2025-09-18T09:19:16.093+07:00  INFO 80237 --- [qtp828508529-36] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T09:19:16.093+07:00  INFO 80237 --- [qtp828508529-41] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T09:19:19.003+07:00  INFO 80237 --- [qtp828508529-41] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T09:19:19.008+07:00  INFO 80237 --- [qtp828508529-36] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T09:19:19.013+07:00  INFO 80237 --- [qtp828508529-41] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-18T09:19:19.014+07:00  INFO 80237 --- [qtp828508529-36] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-18T09:19:23.709+07:00  INFO 80237 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-18T09:19:23.713+07:00  INFO 80237 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T09:19:42.066+07:00  INFO 80237 --- [qtp828508529-34] c.d.f.core.message.CRMMessageLogic       : 📅 Scheduled message [id=22076] [company=bee] type=MAIL for 18/09/2025@09:19:42+0700
2025-09-18T09:19:42.067+07:00  INFO 80237 --- [qtp828508529-34] c.d.f.core.message.MessageQueueManager   : Added message [22076] - scheduled at 18/09/2025@09:19:42+0700 - current session (18/09/2025@09:15:00+0700 to 18/09/2025@09:30:00+0700)

2025-09-18T09:19:45.823+07:00  INFO 80237 --- [qtp828508529-68] c.d.f.core.partner.PartnerRequestLogic   : ------------------------Partner---------------------------

2025-09-18T09:19:45.824+07:00  INFO 80237 --- [qtp828508529-68] c.d.f.core.partner.PartnerRequestLogic   : --------------------------------------------------------

2025-09-18T09:19:45.837+07:00  INFO 80237 --- [qtp828508529-68] c.d.f.core.message.CRMMessageLogic       : 📅 Scheduled message [id=22077] [company=bee] type=MAIL for 18/09/2025@09:19:45+0700
2025-09-18T09:19:45.837+07:00  INFO 80237 --- [qtp828508529-68] c.d.f.core.message.MessageQueueManager   : Added message [22077] - scheduled at 18/09/2025@09:19:45+0700 - current session (18/09/2025@09:15:00+0700 to 18/09/2025@09:30:00+0700)

2025-09-18T09:19:49.703+07:00  INFO 80237 --- [qtp828508529-35] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T09:19:49.703+07:00  INFO 80237 --- [qtp828508529-73] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T09:19:49.709+07:00  INFO 80237 --- [qtp828508529-73] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-18T09:19:49.709+07:00  INFO 80237 --- [qtp828508529-35] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-18T09:20:06.257+07:00  INFO 80237 --- [Scheduler-*********-1] n.d.m.session.AppHttpSessionListener     : The session node025qmjuw0gnva1k141xb2brhrz0 is destroyed.
2025-09-18T09:20:06.793+07:00  INFO 80237 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T09:20:06.794+07:00  INFO 80237 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T09:20:17.139+07:00  INFO 80237 --- [qtp828508529-68] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-09-18T09:20:20.064+07:00  INFO 80237 --- [qtp828508529-61] n.d.module.session.ClientSessionManager  : Add a client session id = node0jlmu79i0nvy71y1sjryy50ecs1, token = 04a8376faab3659affa6f86510a7de2d
2025-09-18T09:20:20.065+07:00  INFO 80237 --- [qtp828508529-68] n.d.module.session.ClientSessionManager  : Add a client session id = node0jlmu79i0nvy71y1sjryy50ecs1, token = 04a8376faab3659affa6f86510a7de2d
2025-09-18T09:20:20.078+07:00  INFO 80237 --- [qtp828508529-68] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T09:20:20.078+07:00  INFO 80237 --- [qtp828508529-61] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T09:20:38.521+07:00  INFO 80237 --- [qtp828508529-41] n.d.module.session.ClientSessionManager  : Add a client session id = node0jlmu79i0nvy71y1sjryy50ecs1, token = 04a8376faab3659affa6f86510a7de2d
2025-09-18T09:20:38.533+07:00  INFO 80237 --- [qtp828508529-68] n.d.module.session.ClientSessionManager  : Add a client session id = node0jlmu79i0nvy71y1sjryy50ecs1, token = 04a8376faab3659affa6f86510a7de2d
2025-09-18T09:20:38.553+07:00  INFO 80237 --- [qtp828508529-41] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T09:20:38.554+07:00  INFO 80237 --- [qtp828508529-68] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T09:20:45.489+07:00  INFO 80237 --- [qtp828508529-61] n.d.module.session.ClientSessionManager  : Add a client session id = node0jlmu79i0nvy71y1sjryy50ecs1, token = 04a8376faab3659affa6f86510a7de2d
2025-09-18T09:20:45.490+07:00  INFO 80237 --- [qtp828508529-74] n.d.module.session.ClientSessionManager  : Add a client session id = node0jlmu79i0nvy71y1sjryy50ecs1, token = 04a8376faab3659affa6f86510a7de2d
2025-09-18T09:20:45.502+07:00  INFO 80237 --- [qtp828508529-74] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T09:20:45.502+07:00  INFO 80237 --- [qtp828508529-61] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T09:20:54.729+07:00  INFO 80237 --- [qtp828508529-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0jlmu79i0nvy71y1sjryy50ecs1, token = 04a8376faab3659affa6f86510a7de2d
2025-09-18T09:20:54.730+07:00  INFO 80237 --- [qtp828508529-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0jlmu79i0nvy71y1sjryy50ecs1, token = 04a8376faab3659affa6f86510a7de2d
2025-09-18T09:20:54.739+07:00  INFO 80237 --- [qtp828508529-35] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T09:20:54.739+07:00  INFO 80237 --- [qtp828508529-34] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-18T09:21:02.934+07:00  INFO 80237 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T09:21:24.023+07:00  INFO 80237 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 18, expire count 0
2025-09-18T09:21:24.038+07:00  INFO 80237 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-18T09:22:06.122+07:00  INFO 80237 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T09:23:02.235+07:00  INFO 80237 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T09:23:23.281+07:00  INFO 80237 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T09:23:23.287+07:00  INFO 80237 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T09:24:05.368+07:00  INFO 80237 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T09:25:06.477+07:00  INFO 80237 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T09:25:06.481+07:00  INFO 80237 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T09:25:22.557+07:00  INFO 80237 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 8, expire count 1
2025-09-18T09:25:22.579+07:00  INFO 80237 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-18T09:26:04.656+07:00  INFO 80237 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T09:26:18.908+07:00  INFO 80237 --- [qtp828508529-37] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph logout successfully 
2025-09-18T09:26:21.340+07:00  INFO 80237 --- [qtp828508529-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0jlmu79i0nvy71y1sjryy50ecs1, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T09:26:21.345+07:00  INFO 80237 --- [qtp828508529-34] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T09:26:25.797+07:00  INFO 80237 --- [qtp828508529-36] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T09:26:25.797+07:00  INFO 80237 --- [qtp828508529-34] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T09:26:26.083+07:00  INFO 80237 --- [qtp828508529-36] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T09:26:26.084+07:00  INFO 80237 --- [qtp828508529-34] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T09:27:06.736+07:00  INFO 80237 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T09:27:21.783+07:00  INFO 80237 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 2
2025-09-18T09:27:21.793+07:00  INFO 80237 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T09:27:25.319+07:00  INFO 80237 --- [qtp828508529-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0jlmu79i0nvy71y1sjryy50ecs1, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T09:27:25.320+07:00  INFO 80237 --- [qtp828508529-74] n.d.module.session.ClientSessionManager  : Add a client session id = node0jlmu79i0nvy71y1sjryy50ecs1, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T09:27:25.335+07:00  INFO 80237 --- [qtp828508529-74] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T09:27:25.335+07:00  INFO 80237 --- [qtp828508529-36] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T09:27:29.291+07:00  INFO 80237 --- [qtp828508529-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T09:27:29.291+07:00  INFO 80237 --- [qtp828508529-34] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-18T09:27:29.382+07:00  INFO 80237 --- [qtp828508529-34] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T09:27:29.382+07:00  INFO 80237 --- [qtp828508529-37] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 57 records
2025-09-18T09:28:03.858+07:00  INFO 80237 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T09:29:06.955+07:00  INFO 80237 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T09:29:21.049+07:00  INFO 80237 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 16, expire count 1
2025-09-18T09:29:21.064+07:00  INFO 80237 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T09:30:03.143+07:00  INFO 80237 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T09:30:03.148+07:00  INFO 80237 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T09:30:03.149+07:00  INFO 80237 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-18T09:30:03.153+07:00  INFO 80237 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 18/09/2025@09:30:03+0700
2025-09-18T09:30:03.175+07:00  INFO 80237 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 18/09/2025@09:30:00+0700 to 18/09/2025@09:45:00+0700
2025-09-18T09:30:03.175+07:00  INFO 80237 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 18/09/2025@09:30:00+0700 to 18/09/2025@09:45:00+0700
2025-09-18T09:31:06.267+07:00  INFO 80237 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T09:31:20.318+07:00  INFO 80237 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 10
2025-09-18T09:31:20.323+07:00  INFO 80237 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T09:32:02.404+07:00  INFO 80237 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T09:33:05.520+07:00  INFO 80237 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T09:33:24.564+07:00  INFO 80237 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T09:33:24.578+07:00  INFO 80237 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-18T09:34:06.650+07:00  INFO 80237 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T09:35:04.739+07:00  INFO 80237 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T09:35:04.744+07:00  INFO 80237 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T09:35:23.774+07:00  INFO 80237 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 5
2025-09-18T09:35:23.785+07:00  INFO 80237 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T09:36:06.846+07:00  INFO 80237 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T09:37:03.943+07:00  INFO 80237 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T09:37:24.009+07:00  INFO 80237 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 8
2025-09-18T09:37:24.030+07:00  INFO 80237 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-18T09:38:04.917+07:00  INFO 80237 --- [qtp828508529-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0jlmu79i0nvy71y1sjryy50ecs1, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T09:38:04.932+07:00  INFO 80237 --- [qtp828508529-36] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T09:38:04.958+07:00  INFO 80237 --- [qtp828508529-147] n.d.module.session.ClientSessionManager  : Add a client session id = node0jlmu79i0nvy71y1sjryy50ecs1, token = 4a758e39123fe8b5a2364bcefc38f355
2025-09-18T09:38:04.961+07:00  INFO 80237 --- [qtp828508529-147] n.d.m.c.a.CompanyAuthenticationService   : User kai.vnhph is logged in successfully system
2025-09-18T09:38:06.121+07:00  INFO 80237 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T09:39:03.238+07:00  INFO 80237 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T09:39:24.303+07:00  INFO 80237 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 3
2025-09-18T09:39:24.311+07:00  INFO 80237 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-18T09:40:06.382+07:00  INFO 80237 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T09:40:06.384+07:00  INFO 80237 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T09:41:02.471+07:00  INFO 80237 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T09:41:23.511+07:00  INFO 80237 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-18T09:41:23.517+07:00  INFO 80237 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T09:42:05.592+07:00  INFO 80237 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T09:43:06.709+07:00  INFO 80237 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T09:43:22.756+07:00  INFO 80237 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T09:43:22.764+07:00  INFO 80237 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T09:44:04.838+07:00  INFO 80237 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T09:45:06.926+07:00  INFO 80237 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T09:45:06.930+07:00  INFO 80237 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-18T09:45:06.932+07:00  INFO 80237 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-18T09:45:06.934+07:00  INFO 80237 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 18/09/2025@09:45:06+0700
2025-09-18T09:45:06.961+07:00  INFO 80237 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 18/09/2025@09:45:00+0700 to 18/09/2025@10:00:00+0700
2025-09-18T09:45:06.962+07:00  INFO 80237 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 18/09/2025@09:45:00+0700 to 18/09/2025@10:00:00+0700
2025-09-18T09:45:22.002+07:00  INFO 80237 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-18T09:45:22.008+07:00  INFO 80237 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T09:46:04.065+07:00  INFO 80237 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-18T09:46:56.917+07:00  INFO 80237 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@7ee183c1{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-18T09:46:56.920+07:00  INFO 80237 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-18T09:46:56.921+07:00  INFO 80237 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-18T09:46:56.921+07:00  INFO 80237 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-18T09:46:56.921+07:00  INFO 80237 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-18T09:46:56.921+07:00  INFO 80237 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-18T09:46:56.921+07:00  INFO 80237 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-18T09:46:56.922+07:00  INFO 80237 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-18T09:46:56.922+07:00  INFO 80237 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-09-18T09:46:56.922+07:00  INFO 80237 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-09-18T09:46:56.922+07:00  INFO 80237 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-18T09:46:56.922+07:00  INFO 80237 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-09-18T09:46:56.922+07:00  INFO 80237 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-09-18T09:46:56.922+07:00  INFO 80237 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-18T09:46:56.922+07:00  INFO 80237 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-09-18T09:46:56.922+07:00  INFO 80237 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-09-18T09:46:56.975+07:00  INFO 80237 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-18T09:46:57.086+07:00  INFO 80237 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-18T09:46:57.096+07:00  INFO 80237 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-18T09:46:57.135+07:00  INFO 80237 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-18T09:46:57.155+07:00  INFO 80237 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-18T09:46:57.186+07:00  INFO 80237 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-18T09:46:57.190+07:00  INFO 80237 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-18T09:46:57.192+07:00  INFO 80237 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-18T09:46:57.192+07:00  INFO 80237 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-18T09:46:57.192+07:00  INFO 80237 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-18T09:46:57.192+07:00  INFO 80237 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-18T09:46:57.192+07:00  INFO 80237 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-18T09:46:57.193+07:00  INFO 80237 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-18T09:46:57.193+07:00  INFO 80237 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-18T09:46:57.193+07:00  INFO 80237 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-18T09:46:57.194+07:00  INFO 80237 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-18T09:46:57.201+07:00  INFO 80237 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@7e7f72{STOPPING}[12.0.15,sto=0]
2025-09-18T09:46:57.206+07:00  INFO 80237 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-18T09:46:57.208+07:00  INFO 80237 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@37eca4c0{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.8720327060594479752/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@5c452eb5{STOPPED}}
