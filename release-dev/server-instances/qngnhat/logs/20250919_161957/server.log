2025-09-19T16:19:58.274+07:00  INFO 94954 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 94954 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-19T16:19:58.275+07:00  INFO 94954 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-19T16:19:58.967+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:19:59.043+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 72 ms. Found 22 JPA repository interfaces.
2025-09-19T16:19:59.054+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:19:59.055+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-19T16:19:59.056+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:19:59.104+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 48 ms. Found 10 JPA repository interfaces.
2025-09-19T16:19:59.105+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:19:59.108+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-19T16:19:59.116+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:19:59.121+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-09-19T16:19:59.129+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:19:59.131+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 2 JPA repository interfaces.
2025-09-19T16:19:59.132+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:19:59.136+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-09-19T16:19:59.139+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:19:59.143+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-09-19T16:19:59.146+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:19:59.149+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-19T16:19:59.149+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:19:59.149+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-19T16:19:59.150+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:19:59.156+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 10 JPA repository interfaces.
2025-09-19T16:19:59.160+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:19:59.163+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-19T16:19:59.166+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:19:59.170+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-19T16:19:59.170+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:19:59.176+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-19T16:19:59.176+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:19:59.179+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-19T16:19:59.179+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:19:59.180+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-19T16:19:59.180+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:19:59.181+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-19T16:19:59.181+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:19:59.185+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-09-19T16:19:59.185+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:19:59.187+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-19T16:19:59.187+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:19:59.187+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-19T16:19:59.187+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:19:59.198+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 19 JPA repository interfaces.
2025-09-19T16:19:59.209+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:19:59.215+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 8 JPA repository interfaces.
2025-09-19T16:19:59.215+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:19:59.218+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-09-19T16:19:59.218+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:19:59.222+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-19T16:19:59.222+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:19:59.227+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-09-19T16:19:59.228+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:19:59.232+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-19T16:19:59.232+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:19:59.241+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 13 JPA repository interfaces.
2025-09-19T16:19:59.241+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:19:59.250+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 14 JPA repository interfaces.
2025-09-19T16:19:59.250+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:19:59.265+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 14 ms. Found 24 JPA repository interfaces.
2025-09-19T16:19:59.265+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:19:59.267+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-19T16:19:59.272+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:19:59.273+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-19T16:19:59.273+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:19:59.280+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-19T16:19:59.282+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:19:59.318+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 36 ms. Found 66 JPA repository interfaces.
2025-09-19T16:19:59.318+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:19:59.320+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-19T16:19:59.325+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:19:59.328+07:00  INFO 94954 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-09-19T16:19:59.527+07:00  INFO 94954 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-19T16:19:59.530+07:00  INFO 94954 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-19T16:19:59.791+07:00  WARN 94954 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-19T16:19:59.976+07:00  INFO 94954 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-19T16:19:59.978+07:00  INFO 94954 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-19T16:19:59.989+07:00  INFO 94954 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-19T16:19:59.989+07:00  INFO 94954 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1598 ms
2025-09-19T16:20:00.043+07:00  WARN 94954 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-19T16:20:00.043+07:00  INFO 94954 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-19T16:20:00.135+07:00  INFO 94954 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@105d2c27
2025-09-19T16:20:00.135+07:00  INFO 94954 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-19T16:20:00.140+07:00  WARN 94954 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-19T16:20:00.140+07:00  INFO 94954 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-19T16:20:00.144+07:00  INFO 94954 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@25767bd9
2025-09-19T16:20:00.145+07:00  INFO 94954 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-19T16:20:00.145+07:00  WARN 94954 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-19T16:20:00.145+07:00  INFO 94954 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-19T16:20:00.152+07:00  INFO 94954 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@66d0d5ee
2025-09-19T16:20:00.152+07:00  INFO 94954 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-19T16:20:00.153+07:00  WARN 94954 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-19T16:20:00.153+07:00  INFO 94954 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-19T16:20:00.160+07:00  INFO 94954 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@73c5b676
2025-09-19T16:20:00.160+07:00  INFO 94954 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-19T16:20:00.160+07:00  WARN 94954 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-19T16:20:00.161+07:00  INFO 94954 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-19T16:20:00.166+07:00  INFO 94954 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@b7526a9
2025-09-19T16:20:00.166+07:00  INFO 94954 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-19T16:20:00.166+07:00  INFO 94954 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-19T16:20:00.212+07:00  INFO 94954 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-19T16:20:00.266+07:00  INFO 94954 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@70b9a553{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.5832110692039497281/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@591dd039{STARTED}}
2025-09-19T16:20:00.267+07:00  INFO 94954 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@70b9a553{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.5832110692039497281/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@591dd039{STARTED}}
2025-09-19T16:20:00.268+07:00  INFO 94954 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@7d2d7a85{STARTING}[12.0.15,sto=0] @2581ms
2025-09-19T16:20:00.322+07:00  INFO 94954 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-19T16:20:00.347+07:00  INFO 94954 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-19T16:20:00.362+07:00  INFO 94954 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-19T16:20:00.480+07:00  INFO 94954 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-19T16:20:00.513+07:00  WARN 94954 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-19T16:20:01.112+07:00  INFO 94954 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-19T16:20:01.121+07:00  INFO 94954 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@57f28893] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-19T16:20:01.264+07:00  INFO 94954 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T16:20:01.467+07:00  INFO 94954 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.graphapi", "net.datatp.module.kpi", "net.datatp.module.monitor.activity", "net.datatp.module.wfms", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "net.datatp.module.okr", "cloud.datatp.module.fleet", "net.datatp.module.core.template", "cloud.datatp.module.pegasus.shipment", "cloud.datatp.gps", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "net.datatp.module.groovy", "cloud.datatp.tms", "net.datatp.module.company.tmpl", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "cloud.datatp.vendor", "net.datatp.module.resource", "net.datatp.module.company.hr", "net.datatp.module.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.chatbot", "net.datatp.module.core.print", "cloud.datatp.module.odoo", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-09-19T16:20:01.469+07:00  INFO 94954 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-19T16:20:01.475+07:00  INFO 94954 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-19T16:20:01.477+07:00  INFO 94954 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-19T16:20:01.505+07:00  INFO 94954 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-19T16:20:01.572+07:00  WARN 94954 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-19T16:20:03.563+07:00  INFO 94954 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-19T16:20:03.564+07:00  INFO 94954 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@1fc570ab] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-19T16:20:03.766+07:00  WARN 94954 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-19T16:20:03.766+07:00  WARN 94954 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-19T16:20:03.775+07:00  WARN 94954 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-19T16:20:03.775+07:00  WARN 94954 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-19T16:20:03.788+07:00  WARN 94954 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-19T16:20:03.788+07:00  WARN 94954 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-19T16:20:04.225+07:00  INFO 94954 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T16:20:04.230+07:00  INFO 94954 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-19T16:20:04.232+07:00  INFO 94954 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-19T16:20:04.252+07:00  INFO 94954 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-19T16:20:04.260+07:00  WARN 94954 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-19T16:20:04.786+07:00  INFO 94954 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-19T16:20:04.787+07:00  INFO 94954 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@29eed4f2] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-19T16:20:04.860+07:00  WARN 94954 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-19T16:20:04.860+07:00  WARN 94954 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-19T16:20:05.181+07:00  INFO 94954 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T16:20:05.211+07:00  INFO 94954 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-19T16:20:05.215+07:00  INFO 94954 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-19T16:20:05.215+07:00  INFO 94954 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T16:20:05.222+07:00  WARN 94954 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-19T16:20:05.349+07:00  INFO 94954 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-19T16:20:05.796+07:00  INFO 94954 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-19T16:20:05.799+07:00  INFO 94954 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-19T16:20:05.835+07:00  INFO 94954 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-19T16:20:05.875+07:00  INFO 94954 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-19T16:20:05.942+07:00  INFO 94954 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-19T16:20:05.970+07:00  INFO 94954 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-19T16:20:05.998+07:00  INFO 94954 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 340824757ms : this is harmless.
2025-09-19T16:20:06.009+07:00  INFO 94954 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-19T16:20:06.014+07:00  WARN 94954 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Attempting to allocate 100.0MB of offheap when there is only 99.0MB of free physical memory - some paging will therefore occur.
2025-09-19T16:20:06.014+07:00  INFO 94954 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-19T16:20:06.044+07:00  INFO 94954 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 632330717ms : this is harmless.
2025-09-19T16:20:06.046+07:00  INFO 94954 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-19T16:20:06.063+07:00  INFO 94954 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-19T16:20:06.064+07:00  INFO 94954 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-19T16:20:07.864+07:00  INFO 94954 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-19T16:20:07.864+07:00  INFO 94954 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T16:20:07.865+07:00  WARN 94954 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-19T16:20:08.154+07:00  INFO 94954 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 19/09/2025@16:15:00+0700 to 19/09/2025@16:30:00+0700
2025-09-19T16:20:08.154+07:00  INFO 94954 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 19/09/2025@16:15:00+0700 to 19/09/2025@16:30:00+0700
2025-09-19T16:20:08.735+07:00  INFO 94954 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-09-19T16:20:08.735+07:00  INFO 94954 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T16:20:08.736+07:00  WARN 94954 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-19T16:20:09.077+07:00  INFO 94954 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-09-19T16:20:09.078+07:00  INFO 94954 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-09-19T16:20:09.078+07:00  INFO 94954 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-09-19T16:20:09.078+07:00  INFO 94954 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-09-19T16:20:09.078+07:00  INFO 94954 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-09-19T16:20:11.168+07:00  WARN 94954 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 4a8ab770-408f-4e99-991f-8b0819bc2739

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-19T16:20:11.172+07:00  INFO 94954 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-09-19T16:20:11.498+07:00  INFO 94954 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-19T16:20:11.498+07:00  INFO 94954 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-19T16:20:11.499+07:00  INFO 94954 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-19T16:20:11.499+07:00  INFO 94954 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-19T16:20:11.499+07:00  INFO 94954 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-09-19T16:20:11.499+07:00  INFO 94954 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-09-19T16:20:11.499+07:00  INFO 94954 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-19T16:20:11.499+07:00  INFO 94954 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-09-19T16:20:11.499+07:00  INFO 94954 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-09-19T16:20:11.499+07:00  INFO 94954 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-19T16:20:11.499+07:00  INFO 94954 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-09-19T16:20:11.499+07:00  INFO 94954 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-09-19T16:20:11.503+07:00  INFO 94954 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-19T16:20:11.503+07:00  INFO 94954 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-19T16:20:11.503+07:00  INFO 94954 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-19T16:20:11.902+07:00  INFO 94954 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-19T16:20:11.902+07:00  INFO 94954 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-19T16:20:11.912+07:00  INFO 94954 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 10 ms
2025-09-19T16:20:11.926+07:00  INFO 94954 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@2cea0a65{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-19T16:20:11.927+07:00  INFO 94954 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-09-19T16:20:11.928+07:00  INFO 94954 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-09-19T16:20:11.991+07:00  INFO 94954 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-09-19T16:20:11.991+07:00  INFO 94954 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-09-19T16:20:11.999+07:00  INFO 94954 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 14.079 seconds (process running for 14.312)
2025-09-19T16:20:15.848+07:00  INFO 94954 --- [qtp704611578-37] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0dyb0v7pe5kn01ts4higsh7di0
2025-09-19T16:20:15.848+07:00  INFO 94954 --- [qtp704611578-39] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0jsqccmyzuysabh8esm4meo9g1
2025-09-19T16:20:16.119+07:00  INFO 94954 --- [qtp704611578-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0dyb0v7pe5kn01ts4higsh7di0, token = 5472f488fc76ba0d478370fd8ee5a916
2025-09-19T16:20:16.120+07:00  INFO 94954 --- [qtp704611578-39] n.d.module.session.ClientSessionManager  : Add a client session id = node0jsqccmyzuysabh8esm4meo9g1, token = 5472f488fc76ba0d478370fd8ee5a916
2025-09-19T16:20:16.591+07:00  INFO 94954 --- [qtp704611578-39] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-19T16:20:16.592+07:00  INFO 94954 --- [qtp704611578-37] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-19T16:20:20.643+07:00 ERROR 94954 --- [qtp704611578-41] n.d.m.monitor.call.EndpointCallContext   : Start call with component BookingService, method resendBFSOneIBooking, arguments
[ {
  "tenantId" : "default",
  "companyId" : 8,
  "companyParentId" : 4,
  "companyCode" : "beehph",
  "companyLabel" : "Bee HPH",
  "companyFullName" : "BEE LOGISTICS CORPORATION - HAI PHONG BRANCH OFFICE",
  "loginId" : "minhtv",
  "accountId" : 2588,
  "token" : "5472f488fc76ba0d478370fd8ee5a916",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node0jsqccmyzuysabh8esm4meo9g1",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : {
    "beehph" : {
      "companyId" : 8,
      "companyParentId" : 4,
      "companyCode" : "beehph",
      "companyLabel" : "Bee HPH",
      "companyFullName" : null
    }
  },
  "featurePermissions" : [ {
    "id" : 14474,
    "appId" : 3,
    "appModule" : "project",
    "appName" : "project",
    "companyId" : 8,
    "loginId" : "minhtv",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 2119,
    "appId" : 26,
    "appModule" : "okr",
    "appName" : "okr",
    "companyId" : 8,
    "loginId" : "minhtv",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 14168,
    "appId" : 7,
    "appModule" : "company",
    "appName" : "company-asset",
    "companyId" : 8,
    "loginId" : "minhtv",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 5691,
    "appId" : 64,
    "appModule" : "job-tracking",
    "appName" : "job-tracking",
    "companyId" : 8,
    "loginId" : "minhtv",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 6245,
    "appId" : 66,
    "appModule" : "tms",
    "appName" : "tms-bill-company",
    "companyId" : 8,
    "loginId" : "minhtv",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Read"
  }, {
    "id" : 11685,
    "appId" : 70,
    "appModule" : "hr",
    "appName" : "company-kpi",
    "companyId" : 8,
    "loginId" : "minhtv",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 16832,
    "appId" : 69,
    "appModule" : "hr",
    "appName" : "user-kpi",
    "companyId" : 8,
    "loginId" : "minhtv",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 13121,
    "appId" : 79,
    "appModule" : "company",
    "appName" : "user-asset",
    "companyId" : 8,
    "loginId" : "minhtv",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 2141,
    "appId" : 16,
    "appModule" : "logistics",
    "appName" : "user-logistics-sales",
    "companyId" : 8,
    "loginId" : "minhtv",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 14272,
    "appId" : 80,
    "appModule" : "logistics",
    "appName" : "company-logistics-sales",
    "companyId" : 8,
    "loginId" : "minhtv",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 8592,
    "appId" : 6,
    "appModule" : "company",
    "appName" : "company-space",
    "companyId" : 8,
    "loginId" : "minhtv",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 6958,
    "appId" : 2,
    "appModule" : "user",
    "appName" : "my-employee-space",
    "companyId" : 8,
    "loginId" : "minhtv",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 18314,
    "appId" : 88,
    "appModule" : "user",
    "appName" : "my-space",
    "companyId" : 8,
    "loginId" : "minhtv",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  } ],
  "attributes" : { },
  "clientId" : "default:minhtv"
}, {
  "id" : 220411,
  "editState" : "ORIGIN",
  "loadState" : "FromDB",
  "version" : 1,
  "createdBy" : "minhtv",
  "createdTime" : "09/07/2025@02:12:11+0000",
  "modifiedBy" : "minhtv",
  "modifiedTime" : "09/07/2025@02:12:14+0000",
  "storageState" : "ACTIVE",
  "companyId" : 8,
  "bookingDate" : "09/07/2025@02:12:11+0000",
  "bfsoneReference" : "HPIB-FI07010/25",
  "hawbNo" : "BSCN25070021",
  "shipmentType" : "FREE-HAND",
  "paymentTerm" : "PREPAID",
  "receiverAccountId" : 53498,
  "receiverBFSOneCode" : "CT1497",
  "receiverLabel" : "NGUYỄN THỊ NGÂN",
  "chargeType" : "SEA",
  "chargeId" : 88993,
  "inquiryId" : 223287,
  "inquiry" : {
    "id" : 223287,
    "uikey" : "223287",
    "editState" : "ORIGIN",
    "loadState" : "FromDB",
    "version" : 4,
    "createdBy" : "minhtv",
    "createdTime" : "07/07/2025@03:57:47+0000",
    "modifiedBy" : "minhtv",
    "modifiedTime" : "09/07/2025@02:12:14+0000",
    "storageState" : "ACTIVE",
    "companyId" : 8,
    "referenceCode" : "IS25070911633",
    "requestDate" : "09/07/2025@02:07:29+0000",
    "mode" : "SEA_FCL",
    "purpose" : "IMPORT",
    "typeOfService" : "SeaImpTransactions_FCL",
    "clientPartnerType" : "CUSTOMERS",
    "clientPartnerId" : 12575,
    "clientLabel" : "VINATEX INTL JSC - HCM HO -TKC HP",
    "attention" : "N/A",
    "salemanAccountId" : 2588,
    "salemanLabel" : "TRẦN VĂN MINH",
    "fromLocationCode" : "HKHKG",
    "fromLocationLabel" : "HONG KONG",
    "toLocationCode" : "VNHPH",
    "toLocationLabel" : "HAIPHONG, VIETNAM",
    "finalDestination" : "",
    "termOfService" : "PORT_TO_PORT",
    "incoterms" : "FOB",
    "estimatedTimeDeparture" : "09/07/2025@02:07:29+0000",
    "cargoReadyDate" : "09/07/2025@02:07:29+0000",
    "containerTypes" : "1x40DC",
    "packagingType" : "PK",
    "packageQty" : 0,
    "descOfGoods" : "",
    "commodity" : "GENERAL",
    "grossWeightKg" : 0.0,
    "volumeCbm" : 0.0,
    "chargeableWeight" : 0.0,
    "chargeableVolume" : 0.0,
    "termsAndConditions" : "Rates are applied for general, non-hazardous, non-special cargo.\n          Rates are subject to Peak Season Surcharge (PSS), General Rate Increase (GRI) whenever applicable, unless otherwise indicated herein.\n          Rates exclude import duty, loading & unloading cargo from truck…, unless otherwise indicated herein.\n          Rates exclude container detention, demurrage, container repair, storage charge, customs penalty, truck detention charge, if any.\n          Transit time indicated is based on carrier's publication, which may be subject to change with/without prior notice.",
    "note" : "The price quote is temporarily calculated in USD, will be converted into VND at exchange rate of Vietcombank on date of invoice.\n          - Rates are subjects to all arising charges during shipment handling process.\n          - Booking acceptance is subject to equipment and space availability at origin.\n          - All our business and services are always subject to the Standard Trading Conditions of VLA and a copy of which shall be supplied on demand. ",
    "containers" : [ {
      "id" : 183374,
      "uikey" : "183374",
      "editState" : "ORIGIN",
      "loadState" : "FromDB",
      "companyId" : 8,
      "containerType" : "40DC",
      "quantity" : 1,
      "cargoWeight" : 0.0,
      "totalCargoWeight" : 0.0,
      "cargoWeightUnit" : "KGM"
    } ]
  },
  "sellingRates" : [ {
    "group" : "SEA_FCL",
    "type" : "LOCAL_CHARGE",
    "target" : "ORIGIN",
    "payerPartnerId" : 12575,
    "payerPartnerCode" : null,
    "payerPartnerLabel" : "VINATEX INTL JSC - HCM HO -TKC HP",
    "code" : "S_TLTK",
    "name" : "LIQUIDATION FEE",
    "quantity" : 1.0,
    "unit" : "40´DC",
    "unitPrice" : 1.0,
    "currency" : "VND",
    "exchangeRate" : 0.0,
    "taxRate" : 0.08,
    "totalAmount" : 1.0,
    "domesticCurrency" : "VND",
    "domesticUnitPrice" : 0.0,
    "domesticTotalAmount" : 0.0,
    "note" : "Mr Khanh"
  }, {
    "group" : "SEA_FCL",
    "type" : "LOCAL_CHARGE",
    "target" : "ORIGIN",
    "payerPartnerId" : 12575,
    "payerPartnerCode" : null,
    "payerPartnerLabel" : "VINATEX INTL JSC - HCM HO -TKC HP",
    "code" : "S_LOLO",
    "name" : "LIFT ON/ LIFT OFF ",
    "quantity" : 1.0,
    "unit" : "40´DC",
    "unitPrice" : 1.0,
    "currency" : "VND",
    "exchangeRate" : 0.0,
    "taxRate" : 0.08,
    "totalAmount" : 1.0,
    "domesticCurrency" : "VND",
    "domesticUnitPrice" : 0.0,
    "domesticTotalAmount" : 0.0,
    "note" : null
  }, {
    "group" : "SEA_FCL",
    "type" : "LOCAL_CHARGE",
    "target" : "ORIGIN",
    "payerPartnerId" : 12575,
    "payerPartnerCode" : null,
    "payerPartnerLabel" : "VINATEX INTL JSC - HCM HO -TKC HP",
    "code" : "S_LSS",
    "name" : "LOW SULPHUR SURCHARGE",
    "quantity" : 1.0,
    "unit" : "40´DC",
    "unitPrice" : 270.0,
    "currency" : "USD",
    "exchangeRate" : 26250.0,
    "taxRate" : 0.08,
    "totalAmount" : 291.6,
    "domesticCurrency" : "VND",
    "domesticUnitPrice" : 7087500.0,
    "domesticTotalAmount" : 7654500.*********,
    "note" : "Rate: 291.60 x 26,250"
  }, {
    "group" : "SEA_FCL",
    "type" : "LOCAL_CHARGE",
    "target" : "ORIGIN",
    "payerPartnerId" : 12575,
    "payerPartnerCode" : null,
    "payerPartnerLabel" : "VINATEX INTL JSC - HCM HO -TKC HP",
    "code" : "S_TRUCK",
    "name" : "DOMESTIC TRUCKING FEE",
    "quantity" : 1.0,
    "unit" : "40´DC",
    "unitPrice" : 1.0,
    "currency" : "VND",
    "exchangeRate" : 0.0,
    "taxRate" : 0.08,
    "totalAmount" : 1.0,
    "domesticCurrency" : "VND",
    "domesticUnitPrice" : 0.0,
    "domesticTotalAmount" : 0.0,
    "note" : null
  }, {
    "group" : "SEA_FCL",
    "type" : "LOCAL_CHARGE",
    "target" : "ORIGIN",
    "payerPartnerId" : 12575,
    "payerPartnerCode" : null,
    "payerPartnerLabel" : "VINATEX INTL JSC - HCM HO -TKC HP",
    "code" : "S_CIC",
    "name" : "CIC",
    "quantity" : 1.0,
    "unit" : "40´DC",
    "unitPrice" : 240.0,
    "currency" : "USD",
    "exchangeRate" : 26250.0,
    "taxRate" : 0.08,
    "totalAmount" : 259.2,
    "domesticCurrency" : "VND",
    "domesticUnitPrice" : 6300000.0,
    "domesticTotalAmount" : 6804000.0,
    "note" : "Rate: 259.20 x 26,250"
  }, {
    "group" : "SEA_FCL",
    "type" : "LOCAL_CHARGE",
    "target" : "ORIGIN",
    "payerPartnerId" : 12575,
    "payerPartnerCode" : null,
    "payerPartnerLabel" : "VINATEX INTL JSC - HCM HO -TKC HP",
    "code" : "S_HAND",
    "name" : "HANDLING FEE ",
    "quantity" : 1.0,
    "unit" : "SHIPMENT",
    "unitPrice" : 10.0,
    "currency" : "USD",
    "exchangeRate" : 26250.0,
    "taxRate" : 0.08,
    "totalAmount" : 10.8,
    "domesticCurrency" : "VND",
    "domesticUnitPrice" : 262500.0,
    "domesticTotalAmount" : 283500.0,
    "note" : "Rate: 10.80 x 26,250"
  }, {
    "group" : "SEA_FCL",
    "type" : "LOCAL_CHARGE",
    "target" : "ORIGIN",
    "payerPartnerId" : 12575,
    "payerPartnerCode" : null,
    "payerPartnerLabel" : "VINATEX INTL JSC - HCM HO -TKC HP",
    "code" : "S_THC",
    "name" : "THC",
    "quantity" : 1.0,
    "unit" : "40´DC",
    "unitPrice" : 210.0,
    "currency" : "USD",
    "exchangeRate" : 26250.0,
    "taxRate" : 0.08,
    "totalAmount" : 226.8,
    "domesticCurrency" : "VND",
    "domesticUnitPrice" : 5512500.0,
    "domesticTotalAmount" : 5953500.0,
    "note" : "Rate: 226.80 x 26,250"
  }, {
    "group" : "SEA_FCL",
    "type" : "LOCAL_CHARGE",
    "target" : "ORIGIN",
    "payerPartnerId" : 12575,
    "payerPartnerCode" : null,
    "payerPartnerLabel" : "VINATEX INTL JSC - HCM HO -TKC HP",
    "code" : "S_DF",
    "name" : "DOC FEE",
    "quantity" : 1.0,
    "unit" : "SET",
    "unitPrice" : 30.0,
    "currency" : "USD",
    "exchangeRate" : 26250.0,
    "taxRate" : 0.08,
    "totalAmount" : 32.4,
    "domesticCurrency" : "VND",
    "domesticUnitPrice" : 787500.0,
    "domesticTotalAmount" : 850500.0,
    "note" : "Rate: 32.40 x 26,250"
  }, {
    "group" : "SEA_FCL",
    "type" : "LOCAL_CHARGE",
    "target" : "ORIGIN",
    "payerPartnerId" : 12575,
    "payerPartnerCode" : null,
    "payerPartnerLabel" : "VINATEX INTL JSC - HCM HO -TKC HP",
    "code" : "S_CLEAN",
    "name" : "CLEANING FEE",
    "quantity" : 1.0,
    "unit" : "40´DC",
    "unitPrice" : 10.0,
    "currency" : "USD",
    "exchangeRate" : 26250.0,
    "taxRate" : 0.08,
    "totalAmount" : 10.8,
    "domesticCurrency" : "VND",
    "domesticUnitPrice" : 262500.0,
    "domesticTotalAmount" : 283500.0,
    "note" : "Rate: 10.80 x 26,250"
  }, {
    "group" : "SEA_FCL",
    "type" : "SEAFREIGHT",
    "target" : "ORIGIN",
    "payerPartnerId" : 12575,
    "payerPartnerCode" : null,
    "payerPartnerLabel" : "VINATEX INTL JSC - HCM HO -TKC HP",
    "code" : "S_OF",
    "name" : "SEAFREIGHT",
    "quantity" : 1.0,
    "unit" : "40´DC",
    "unitPrice" : 1175.0,
    "currency" : "USD",
    "exchangeRate" : 0.0,
    "taxRate" : 0.0,
    "totalAmount" : 1175.0,
    "domesticCurrency" : "VND",
    "domesticUnitPrice" : 0.0,
    "domesticTotalAmount" : 0.0,
    "note" : "EVER CONFORM 0327-067S\n Surcharge:\nFOR SINGLE CONT, NẾU 2CONT/BILL, ADD 30$/40$\n Free Time:\n14 + 7\n"
  } ]
} ]
2025-09-19T16:20:20.650+07:00 ERROR 94954 --- [qtp704611578-41] n.d.m.monitor.call.EndpointCallService   : Unknown Error when handle the endpoint BookingService/resendBFSOneIBooking
2025-09-19T16:20:20.651+07:00 ERROR 94954 --- [qtp704611578-41] n.d.m.monitor.call.EndpointCallService   : Exception: 

net.datatp.util.error.RuntimeError: class java.util.HashMap cannot be cast to class net.datatp.util.ds.MapObject (java.util.HashMap is in module java.base of loader bootstrap; net.datatp.util.ds.MapObject is in unnamed module of loader app)
	at net.datatp.util.error.RuntimeError.UnknownError(RuntimeError.java:96)
	at cloud.datatp.fforwarder.sales.booking.BookingLogic.resendBFSOneIBooking(BookingLogic.java:236)
	at cloud.datatp.fforwarder.sales.booking.BookingService.resendBFSOneIBooking(BookingService.java:37)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at cloud.datatp.fforwarder.sales.booking.BookingService$$SpringCGLIB$$0.resendBFSOneIBooking(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)

2025-09-19T16:21:06.631+07:00  INFO 94954 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T16:21:14.707+07:00  INFO 94954 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 0
2025-09-19T16:21:14.756+07:00  INFO 94954 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T16:22:03.851+07:00  INFO 94954 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T16:22:16.700+07:00  INFO 94954 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@2cea0a65{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-19T16:22:16.702+07:00  INFO 94954 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-19T16:22:16.702+07:00  INFO 94954 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-19T16:22:16.702+07:00  INFO 94954 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-19T16:22:16.702+07:00  INFO 94954 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-19T16:22:16.702+07:00  INFO 94954 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-19T16:22:16.702+07:00  INFO 94954 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-19T16:22:16.702+07:00  INFO 94954 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-19T16:22:16.702+07:00  INFO 94954 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-09-19T16:22:16.702+07:00  INFO 94954 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-09-19T16:22:16.702+07:00  INFO 94954 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-19T16:22:16.702+07:00  INFO 94954 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-09-19T16:22:16.702+07:00  INFO 94954 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-09-19T16:22:16.702+07:00  INFO 94954 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-19T16:22:16.702+07:00  INFO 94954 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-09-19T16:22:16.702+07:00  INFO 94954 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-09-19T16:22:16.716+07:00  INFO 94954 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T16:22:16.801+07:00  INFO 94954 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-19T16:22:16.806+07:00  INFO 94954 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-19T16:22:16.828+07:00  INFO 94954 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T16:22:16.829+07:00  INFO 94954 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T16:22:16.832+07:00  INFO 94954 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T16:22:16.832+07:00  INFO 94954 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-19T16:22:16.904+07:00  INFO 94954 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-19T16:22:16.904+07:00  INFO 94954 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-19T16:22:16.905+07:00  INFO 94954 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-19T16:22:16.905+07:00  INFO 94954 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-19T16:22:16.906+07:00  INFO 94954 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-19T16:22:16.907+07:00  INFO 94954 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-19T16:22:16.907+07:00  INFO 94954 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-19T16:22:16.907+07:00  INFO 94954 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-19T16:22:16.908+07:00  INFO 94954 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-19T16:22:16.909+07:00  INFO 94954 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@7d2d7a85{STOPPING}[12.0.15,sto=0]
2025-09-19T16:22:16.915+07:00  INFO 94954 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-19T16:22:16.917+07:00  INFO 94954 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@70b9a553{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.5832110692039497281/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@591dd039{STOPPED}}
