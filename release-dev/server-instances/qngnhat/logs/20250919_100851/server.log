2025-09-19T10:08:52.607+07:00  INFO 50860 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 50860 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-19T10:08:52.608+07:00  INFO 50860 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-19T10:08:53.315+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:08:53.382+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 64 ms. Found 22 JPA repository interfaces.
2025-09-19T10:08:53.391+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:08:53.393+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-19T10:08:53.393+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:08:53.400+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 10 JPA repository interfaces.
2025-09-19T10:08:53.435+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:08:53.439+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 3 JPA repository interfaces.
2025-09-19T10:08:53.447+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:08:53.452+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-09-19T10:08:53.460+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:08:53.462+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-19T10:08:53.462+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:08:53.466+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-19T10:08:53.468+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:08:53.476+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-09-19T10:08:53.479+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:08:53.482+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-19T10:08:53.482+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:08:53.482+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-19T10:08:53.482+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:08:53.488+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 10 JPA repository interfaces.
2025-09-19T10:08:53.493+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:08:53.496+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-19T10:08:53.500+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:08:53.503+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-19T10:08:53.504+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:08:53.511+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-09-19T10:08:53.511+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:08:53.514+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-19T10:08:53.514+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:08:53.514+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-19T10:08:53.514+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:08:53.515+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-19T10:08:53.515+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:08:53.520+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-09-19T10:08:53.520+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:08:53.521+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-19T10:08:53.521+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:08:53.521+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-19T10:08:53.521+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:08:53.532+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 19 JPA repository interfaces.
2025-09-19T10:08:53.542+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:08:53.548+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 8 JPA repository interfaces.
2025-09-19T10:08:53.548+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:08:53.551+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-09-19T10:08:53.551+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:08:53.555+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-19T10:08:53.555+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:08:53.561+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-09-19T10:08:53.561+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:08:53.565+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-09-19T10:08:53.566+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:08:53.574+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 13 JPA repository interfaces.
2025-09-19T10:08:53.574+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:08:53.583+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 14 JPA repository interfaces.
2025-09-19T10:08:53.583+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:08:53.598+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 14 ms. Found 24 JPA repository interfaces.
2025-09-19T10:08:53.598+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:08:53.599+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-19T10:08:53.604+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:08:53.605+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-19T10:08:53.605+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:08:53.612+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-19T10:08:53.614+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:08:53.649+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 35 ms. Found 66 JPA repository interfaces.
2025-09-19T10:08:53.650+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:08:53.651+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-19T10:08:53.656+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:08:53.659+07:00  INFO 50860 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-09-19T10:08:53.870+07:00  INFO 50860 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-19T10:08:53.874+07:00  INFO 50860 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-19T10:08:54.168+07:00  WARN 50860 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-19T10:08:54.374+07:00  INFO 50860 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-19T10:08:54.376+07:00  INFO 50860 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-19T10:08:54.387+07:00  INFO 50860 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-19T10:08:54.387+07:00  INFO 50860 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1679 ms
2025-09-19T10:08:54.445+07:00  WARN 50860 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-19T10:08:54.446+07:00  INFO 50860 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-19T10:08:54.544+07:00  INFO 50860 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@1847da64
2025-09-19T10:08:54.545+07:00  INFO 50860 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-19T10:08:54.549+07:00  WARN 50860 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-19T10:08:54.549+07:00  INFO 50860 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-19T10:08:54.555+07:00  INFO 50860 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@41d34a11
2025-09-19T10:08:54.555+07:00  INFO 50860 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-19T10:08:54.555+07:00  WARN 50860 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-19T10:08:54.555+07:00  INFO 50860 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-19T10:08:54.561+07:00  INFO 50860 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@45b4d2ff
2025-09-19T10:08:54.561+07:00  INFO 50860 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-19T10:08:54.561+07:00  WARN 50860 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-19T10:08:54.561+07:00  INFO 50860 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-19T10:08:54.569+07:00  INFO 50860 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@488bf7cd
2025-09-19T10:08:54.569+07:00  INFO 50860 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-19T10:08:54.569+07:00  WARN 50860 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-19T10:08:54.569+07:00  INFO 50860 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-19T10:08:54.575+07:00  INFO 50860 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@1d4ee7d7
2025-09-19T10:08:54.576+07:00  INFO 50860 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-19T10:08:54.576+07:00  INFO 50860 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-19T10:08:54.629+07:00  INFO 50860 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-19T10:08:54.631+07:00  INFO 50860 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@2dba6013{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.11678029312027985455/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@4873d4cb{STARTED}}
2025-09-19T10:08:54.632+07:00  INFO 50860 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@2dba6013{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.11678029312027985455/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@4873d4cb{STARTED}}
2025-09-19T10:08:54.681+07:00  INFO 50860 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@7da4c956{STARTING}[12.0.15,sto=0] @2681ms
2025-09-19T10:08:54.734+07:00  INFO 50860 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-19T10:08:54.758+07:00  INFO 50860 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-19T10:08:54.773+07:00  INFO 50860 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-19T10:08:54.898+07:00  INFO 50860 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-19T10:08:54.930+07:00  WARN 50860 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-19T10:08:55.532+07:00  INFO 50860 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-19T10:08:55.540+07:00  INFO 50860 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@3046057a] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-19T10:08:55.732+07:00  INFO 50860 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T10:08:55.929+07:00  INFO 50860 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.graphapi", "net.datatp.module.kpi", "net.datatp.module.monitor.activity", "net.datatp.module.wfms", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "net.datatp.module.okr", "cloud.datatp.module.fleet", "net.datatp.module.core.template", "cloud.datatp.gps", "cloud.datatp.module.pegasus.shipment", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "net.datatp.module.company.tmpl", "cloud.datatp.tms", "net.datatp.module.groovy", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "cloud.datatp.vendor", "net.datatp.module.resource", "net.datatp.module.hr", "net.datatp.module.company.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "cloud.datatp.module.odoo", "net.datatp.module.chatbot", "net.datatp.module.core.print", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-09-19T10:08:55.931+07:00  INFO 50860 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-19T10:08:55.937+07:00  INFO 50860 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-19T10:08:55.939+07:00  INFO 50860 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-19T10:08:55.962+07:00  INFO 50860 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-19T10:08:55.970+07:00  WARN 50860 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-19T10:08:58.034+07:00  INFO 50860 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-19T10:08:58.035+07:00  INFO 50860 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@1cca908b] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-19T10:08:58.217+07:00  WARN 50860 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-19T10:08:58.217+07:00  WARN 50860 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-19T10:08:58.227+07:00  WARN 50860 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-19T10:08:58.227+07:00  WARN 50860 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-19T10:08:58.241+07:00  WARN 50860 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-19T10:08:58.241+07:00  WARN 50860 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-19T10:08:58.685+07:00  INFO 50860 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T10:08:58.691+07:00  INFO 50860 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-19T10:08:58.693+07:00  INFO 50860 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-19T10:08:58.715+07:00  INFO 50860 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-19T10:08:58.718+07:00  WARN 50860 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-19T10:08:59.234+07:00  INFO 50860 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-19T10:08:59.235+07:00  INFO 50860 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@7c769be5] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-19T10:08:59.305+07:00  WARN 50860 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-19T10:08:59.305+07:00  WARN 50860 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-19T10:08:59.648+07:00  INFO 50860 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T10:08:59.678+07:00  INFO 50860 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-19T10:08:59.682+07:00  INFO 50860 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-19T10:08:59.682+07:00  INFO 50860 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T10:08:59.688+07:00  WARN 50860 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-19T10:08:59.819+07:00  INFO 50860 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-19T10:09:00.286+07:00  INFO 50860 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-19T10:09:00.289+07:00  INFO 50860 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-19T10:09:00.322+07:00  INFO 50860 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-19T10:09:00.370+07:00  INFO 50860 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-19T10:09:00.435+07:00  INFO 50860 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-19T10:09:00.464+07:00  INFO 50860 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-19T10:09:00.488+07:00  INFO 50860 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 318605660ms : this is harmless.
2025-09-19T10:09:00.499+07:00  INFO 50860 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-19T10:09:00.512+07:00  INFO 50860 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-19T10:09:00.525+07:00  INFO 50860 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 610111616ms : this is harmless.
2025-09-19T10:09:00.526+07:00  INFO 50860 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-19T10:09:00.540+07:00  INFO 50860 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-19T10:09:00.540+07:00  INFO 50860 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-19T10:09:02.432+07:00  INFO 50860 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-19T10:09:02.432+07:00  INFO 50860 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T10:09:02.433+07:00  WARN 50860 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-19T10:09:02.725+07:00  INFO 50860 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 19/09/2025@10:00:00+0700 to 19/09/2025@10:15:00+0700
2025-09-19T10:09:02.725+07:00  INFO 50860 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 19/09/2025@10:00:00+0700 to 19/09/2025@10:15:00+0700
2025-09-19T10:09:03.270+07:00  INFO 50860 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-09-19T10:09:03.270+07:00  INFO 50860 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T10:09:03.271+07:00  WARN 50860 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-19T10:09:03.564+07:00  INFO 50860 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-09-19T10:09:03.564+07:00  INFO 50860 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-09-19T10:09:03.564+07:00  INFO 50860 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-09-19T10:09:03.564+07:00  INFO 50860 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-09-19T10:09:03.564+07:00  INFO 50860 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-09-19T10:09:05.329+07:00  WARN 50860 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 175eab14-5c21-4df5-b52f-e1b52378d8c1

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-19T10:09:05.333+07:00  INFO 50860 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-09-19T10:09:05.634+07:00  INFO 50860 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-19T10:09:05.634+07:00  INFO 50860 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-19T10:09:05.634+07:00  INFO 50860 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-19T10:09:05.634+07:00  INFO 50860 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-19T10:09:05.634+07:00  INFO 50860 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-09-19T10:09:05.634+07:00  INFO 50860 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-09-19T10:09:05.634+07:00  INFO 50860 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-19T10:09:05.634+07:00  INFO 50860 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-09-19T10:09:05.634+07:00  INFO 50860 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-09-19T10:09:05.634+07:00  INFO 50860 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-19T10:09:05.634+07:00  INFO 50860 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-09-19T10:09:05.634+07:00  INFO 50860 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-09-19T10:09:05.637+07:00  INFO 50860 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-19T10:09:05.638+07:00  INFO 50860 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-19T10:09:05.638+07:00  INFO 50860 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-19T10:09:05.707+07:00  INFO 50860 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-19T10:09:05.707+07:00  INFO 50860 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-19T10:09:05.709+07:00  INFO 50860 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-09-19T10:09:05.717+07:00  INFO 50860 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@32bbb6e{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-19T10:09:05.718+07:00  INFO 50860 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-09-19T10:09:05.719+07:00  INFO 50860 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-09-19T10:09:05.749+07:00  INFO 50860 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-09-19T10:09:05.749+07:00  INFO 50860 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-09-19T10:09:05.755+07:00  INFO 50860 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 13.438 seconds (process running for 13.756)
2025-09-19T10:09:15.403+07:00  INFO 50860 --- [qtp85102332-36] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-09-19T10:10:06.754+07:00  INFO 50860 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T10:10:06.758+07:00  INFO 50860 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-19T10:10:08.805+07:00  INFO 50860 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-19T10:10:08.824+07:00  INFO 50860 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T10:10:16.168+07:00  INFO 50860 --- [qtp85102332-40] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01069vvd87js066501vak3dgmq0
2025-09-19T10:10:16.168+07:00  INFO 50860 --- [qtp85102332-35] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node026emcvugymv7hvu1gol8vo1p1
2025-09-19T10:10:16.247+07:00  INFO 50860 --- [qtp85102332-35] n.d.module.session.ClientSessionManager  : Add a client session id = node026emcvugymv7hvu1gol8vo1p1, token = f6470a95190b9569562efcd1c65b8483
2025-09-19T10:10:16.248+07:00  INFO 50860 --- [qtp85102332-40] n.d.module.session.ClientSessionManager  : Add a client session id = node01069vvd87js066501vak3dgmq0, token = f6470a95190b9569562efcd1c65b8483
2025-09-19T10:10:16.740+07:00  INFO 50860 --- [qtp85102332-40] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:10:16.746+07:00  INFO 50860 --- [qtp85102332-35] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:11:04.926+07:00  INFO 50860 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T10:12:06.029+07:00  INFO 50860 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T10:12:08.065+07:00  INFO 50860 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-19T10:12:08.076+07:00  INFO 50860 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T10:13:04.166+07:00  INFO 50860 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T10:14:06.261+07:00  INFO 50860 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T10:14:12.306+07:00  INFO 50860 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-19T10:14:12.313+07:00  INFO 50860 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T10:14:13.476+07:00  INFO 50860 --- [qtp85102332-39] n.d.module.session.ClientSessionManager  : Add a client session id = node026emcvugymv7hvu1gol8vo1p1, token = f6470a95190b9569562efcd1c65b8483
2025-09-19T10:14:13.476+07:00  INFO 50860 --- [qtp85102332-36] n.d.module.session.ClientSessionManager  : Add a client session id = node026emcvugymv7hvu1gol8vo1p1, token = f6470a95190b9569562efcd1c65b8483
2025-09-19T10:14:13.486+07:00  INFO 50860 --- [qtp85102332-36] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:14:13.486+07:00  INFO 50860 --- [qtp85102332-39] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:14:43.640+07:00  INFO 50860 --- [qtp85102332-39] c.d.f.core.partner.CRMPartnerLogic       : --- Create account: bfsone code = CL000079_TEMP
2025-09-19T10:14:43.846+07:00  INFO 50860 --- [qtp85102332-39] c.d.f.core.message.CRMMessageLogic       : 📅 Scheduled message [id=22092] [company=bee] type=MAIL for 19/09/2025@10:14:43+0700
2025-09-19T10:14:43.846+07:00  INFO 50860 --- [qtp85102332-39] c.d.f.core.message.MessageQueueManager   : Added message [22092] - scheduled at 19/09/2025@10:14:43+0700 - current session (19/09/2025@10:00:00+0700 to 19/09/2025@10:15:00+0700)

2025-09-19T10:14:43.850+07:00  WARN 50860 --- [qtp85102332-39] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 0, SQLState: 23505
2025-09-19T10:14:43.850+07:00 ERROR 50860 --- [qtp85102332-39] o.h.engine.jdbc.spi.SqlExceptionHelper   : ERROR: duplicate key value violates unique constraint "lgc_forwarder_partner_request_partner_id"
  Detail: Key (partner_id)=(56630) already exists.
2025-09-19T10:14:43.862+07:00 ERROR 50860 --- [qtp85102332-39] n.d.m.monitor.call.EndpointCallContext   : Start call with component BFSOneCRMService, method createBFSOnePartner, arguments
[ {
  "tenantId" : "default",
  "companyId" : 4,
  "companyParentId" : null,
  "companyCode" : "bee",
  "companyLabel" : "Bee Corp",
  "companyFullName" : "BEE LOGISTICS CORPORATION",
  "loginId" : "dan",
  "accountId" : 3,
  "token" : "f6470a95190b9569562efcd1c65b8483",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node026emcvugymv7hvu1gol8vo1p1",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : {
    "bee-tw" : {
      "companyId" : 56,
      "companyParentId" : 4,
      "companyCode" : "bee-tw",
      "companyLabel" : "BEE TW",
      "companyFullName" : null
    },
    "bee" : {
      "companyId" : 4,
      "companyParentId" : 0,
      "companyCode" : "bee",
      "companyLabel" : "Bee Corp",
      "companyFullName" : null
    },
    "thudo" : {
      "companyId" : 53,
      "companyParentId" : 0,
      "companyCode" : "thudo",
      "companyLabel" : "THUDO",
      "companyFullName" : null
    },
    "datatp" : {
      "companyId" : 10,
      "companyParentId" : 1,
      "companyCode" : "datatp",
      "companyLabel" : "DataTP Cloud",
      "companyFullName" : null
    },
    "beehan" : {
      "companyId" : 16,
      "companyParentId" : 4,
      "companyCode" : "beehan",
      "companyLabel" : "Bee HAN",
      "companyFullName" : null
    },
    "beescs" : {
      "companyId" : 12,
      "companyParentId" : 1,
      "companyCode" : "beescs",
      "companyLabel" : "BEESCS",
      "companyFullName" : null
    },
    "beedad" : {
      "companyId" : 18,
      "companyParentId" : 4,
      "companyCode" : "beedad",
      "companyLabel" : "Bee DAD",
      "companyFullName" : null
    },
    "hps" : {
      "companyId" : 51,
      "companyParentId" : 0,
      "companyCode" : "hps",
      "companyLabel" : "HPS",
      "companyFullName" : null
    },
    "marine" : {
      "companyId" : 52,
      "companyParentId" : 0,
      "companyCode" : "marine",
      "companyLabel" : "MARINE",
      "companyFullName" : null
    },
    "bonds" : {
      "companyId" : 54,
      "companyParentId" : 4,
      "companyCode" : "bonds",
      "companyLabel" : "BONDS",
      "companyFullName" : null
    },
    "tiendat" : {
      "companyId" : 55,
      "companyParentId" : 4,
      "companyCode" : "tiendat",
      "companyLabel" : "TIENDAT",
      "companyFullName" : null
    },
    "beehcm" : {
      "companyId" : 17,
      "companyParentId" : 4,
      "companyCode" : "beehcm",
      "companyLabel" : "Bee HCM",
      "companyFullName" : null
    },
    "beehph" : {
      "companyId" : 8,
      "companyParentId" : 4,
      "companyCode" : "beehph",
      "companyLabel" : "Bee HPH",
      "companyFullName" : null
    }
  },
  "featurePermissions" : [ {
    "id" : 17253,
    "appId" : 69,
    "appModule" : "hr",
    "appName" : "user-kpi",
    "companyId" : 4,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 8429,
    "appId" : 64,
    "appModule" : "job-tracking",
    "appName" : "job-tracking",
    "companyId" : 4,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 14171,
    "appId" : 68,
    "appModule" : "document",
    "appName" : "company-document",
    "companyId" : 4,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 14964,
    "appId" : 81,
    "appModule" : "logistics",
    "appName" : "company-logistics-prices",
    "companyId" : 4,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 6232,
    "appId" : 18,
    "appModule" : "system",
    "appName" : "system",
    "companyId" : 4,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 6140,
    "appId" : 2,
    "appModule" : "user",
    "appName" : "my-employee-space",
    "companyId" : 4,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 1616,
    "appId" : 5,
    "appModule" : "admin",
    "appName" : "admin-space",
    "companyId" : 4,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 1618,
    "appId" : 17,
    "appModule" : "logistics",
    "appName" : "user-logistics-managements",
    "companyId" : 4,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 1622,
    "appId" : 8,
    "appModule" : "company",
    "appName" : "company-accounting",
    "companyId" : 4,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 1629,
    "appId" : 1,
    "appModule" : "user",
    "appName" : "dashboard",
    "companyId" : 4,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 1635,
    "appId" : 9,
    "appModule" : "company",
    "appName" : "company-hr",
    "companyId" : 4,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 1638,
    "appId" : 6,
    "appModule" : "company",
    "appName" : "company-space",
    "companyId" : 4,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 1641,
    "appId" : 26,
    "appModule" : "okr",
    "appName" : "okr",
    "companyId" : 4,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 1645,
    "appId" : 15,
    "appModule" : "logistics",
    "appName" : "user-logistics-prices",
    "companyId" : 4,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 1620,
    "appId" : 16,
    "appModule" : "logistics",
    "appName" : "user-logistics-sales",
    "companyId" : 4,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 1626,
    "appId" : 12,
    "appModule" : "spreadsheet",
    "appName" : "spreadsheet",
    "companyId" : 4,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 14279,
    "appId" : 80,
    "appModule" : "logistics",
    "appName" : "company-logistics-sales",
    "companyId" : 4,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 14834,
    "appId" : 79,
    "appModule" : "company",
    "appName" : "user-asset",
    "companyId" : 4,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 14932,
    "appId" : 70,
    "appModule" : "hr",
    "appName" : "company-kpi",
    "companyId" : 4,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 15069,
    "appId" : 3,
    "appModule" : "project",
    "appName" : "project",
    "companyId" : 4,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  } ],
  "attributes" : { },
  "clientId" : "default:dan"
}, {
  "editState" : "ORIGIN",
  "loadState" : "FromDB",
  "version" : 0,
  "storageState" : "ACTIVE",
  "partnerCode" : "CL000079_TEMP",
  "partnerCodeTemp" : "CL000079_TEMP",
  "status" : "NEW",
  "partnerGroup" : "COLOADERS",
  "category" : "TRUCKER",
  "name" : "TESCVCXVBXCVBXCVBXCV",
  "label" : "TEScvcxvbxcvbxcvbxcv",
  "localizedLabel" : "TEScvcxvbxcvbxcvbxcv",
  "personalContact" : "zxcvzx",
  "email" : "<EMAIL>",
  "cell" : "vzxcvzxcv",
  "taxCode" : "TEScvcxvbxcvbxcvbxcv",
  "source" : "WCA",
  "industryCode" : "I000",
  "industryLabel" : "  --none--  ",
  "countryId" : 244,
  "countryLabel" : "VIETNAM",
  "provinceId" : 163,
  "provinceLabel" : "Tỉnh Sóc Trăng",
  "kcnCode" : "41720",
  "kcnLabel" : "Khu công nghiệp An Hiệp",
  "address" : "xzcvzxcvzxcv",
  "localizedAddress" : "xzcvzxcvxzcvzxcv",
  "printCustomConfirmBillInfo" : "TESCVCXVBXCVBXCVBXCV\nxzcvzxcvzxcv\nzxcvzx\nTEL :vzxcvzxcv",
  "inputUsername" : "JESSE.VNHPH",
  "scope" : "Domestic",
  "groupName" : "NORMAL",
  "refund" : false,
  "requestSalemanAccountId" : 3,
  "requestSalemanLabel" : "LÊ NGỌC ĐÀN"
} ]
2025-09-19T10:14:43.866+07:00 ERROR 50860 --- [qtp85102332-39] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:118)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.springframework.dao.DataIntegrityViolationException: could not execute statement [ERROR: duplicate key value violates unique constraint "lgc_forwarder_partner_request_partner_id"
  Detail: Key (partner_id)=(56630) already exists.] [insert into lgc_forwarder_partner_request (approved_by_account_id,approved_by_label,approved_date,approved_note,bfsone_partner_code_temp,mail_cc,created_by,created_time,modified_by,modified_time,partner_id,partner_label,partner_name,request_by_account_id,request_by_label,request_date,status,storage_state,mail_to,version) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into lgc_forwarder_partner_request (approved_by_account_id,approved_by_label,approved_date,approved_note,bfsone_partner_code_temp,mail_cc,created_by,created_time,modified_by,modified_time,partner_id,partner_label,partner_name,request_by_account_id,request_by_label,request_date,status,storage_state,mail_to,version) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [lgc_forwarder_partner_request_partner_id]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:290)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:560)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:343)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:160)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:164)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy329.save(Unknown Source)
	at cloud.datatp.fforwarder.core.partner.PartnerRequestLogic.savePartnerRequest(PartnerRequestLogic.java:56)
	at cloud.datatp.fforwarder.sales.integration.BFSOneCRMLogic.createBFSPartner(BFSOneCRMLogic.java:223)
	at cloud.datatp.fforwarder.sales.integration.BFSOneCRMService.createBFSOnePartner(BFSOneCRMService.java:29)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at cloud.datatp.fforwarder.sales.integration.BFSOneCRMService$$SpringCGLIB$$0.createBFSOnePartner(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted
Caused by: org.hibernate.exception.ConstraintViolationException: could not execute statement [ERROR: duplicate key value violates unique constraint "lgc_forwarder_partner_request_partner_id"
  Detail: Key (partner_id)=(56630) already exists.] [insert into lgc_forwarder_partner_request (approved_by_account_id,approved_by_label,approved_date,approved_note,bfsone_partner_code_temp,mail_cc,created_by,created_time,modified_by,modified_time,partner_id,partner_label,partner_name,request_by_account_id,request_by_label,request_date,status,storage_state,mail_to,version) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]
	at org.hibernate.exception.internal.SQLStateConversionDelegate.convert(SQLStateConversionDelegate.java:97)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:197)
	at org.hibernate.id.insert.GetGeneratedKeysDelegate.performInsert(GetGeneratedKeysDelegate.java:107)
	at org.hibernate.engine.jdbc.mutation.internal.MutationExecutorPostInsertSingleTable.execute(MutationExecutorPostInsertSingleTable.java:100)
	at org.hibernate.persister.entity.mutation.InsertCoordinator.doStaticInserts(InsertCoordinator.java:175)
	at org.hibernate.persister.entity.mutation.InsertCoordinator.coordinateInsert(InsertCoordinator.java:113)
	at org.hibernate.persister.entity.AbstractEntityPersister.insert(AbstractEntityPersister.java:2868)
	at org.hibernate.action.internal.EntityIdentityInsertAction.execute(EntityIdentityInsertAction.java:81)
	at org.hibernate.engine.spi.ActionQueue.execute(ActionQueue.java:670)
	at org.hibernate.engine.spi.ActionQueue.addResolvedEntityInsertAction(ActionQueue.java:291)
	at org.hibernate.engine.spi.ActionQueue.addInsertAction(ActionQueue.java:272)
	at org.hibernate.engine.spi.ActionQueue.addAction(ActionQueue.java:322)
	at org.hibernate.event.internal.AbstractSaveEventListener.addInsertAction(AbstractSaveEventListener.java:386)
	at org.hibernate.event.internal.AbstractSaveEventListener.performSaveOrReplicate(AbstractSaveEventListener.java:300)
	at org.hibernate.event.internal.AbstractSaveEventListener.performSave(AbstractSaveEventListener.java:219)
	at org.hibernate.event.internal.AbstractSaveEventListener.saveWithGeneratedId(AbstractSaveEventListener.java:134)
	at org.hibernate.event.internal.DefaultPersistEventListener.entityIsTransient(DefaultPersistEventListener.java:175)
	at org.hibernate.event.internal.DefaultPersistEventListener.persist(DefaultPersistEventListener.java:93)
	at org.hibernate.event.internal.DefaultPersistEventListener.onPersist(DefaultPersistEventListener.java:77)
	at org.hibernate.event.internal.DefaultPersistEventListener.onPersist(DefaultPersistEventListener.java:54)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.firePersist(SessionImpl.java:754)
	at org.hibernate.internal.SessionImpl.persist(SessionImpl.java:738)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler.invoke(SharedEntityManagerCreator.java:320)
	at jdk.proxy2/jdk.proxy2.$Proxy181.persist(Unknown Source)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.save(SimpleJpaRepository.java:618)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:277)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:516)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:285)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:628)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:168)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:143)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	... 119 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: duplicate key value violates unique constraint "lgc_forwarder_partner_request_partner_id"
  Detail: Key (partner_id)=(56630) already exists.
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2725)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2412)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:371)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:502)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:419)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:194)
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:155)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:194)
	... 164 common frames omitted

2025-09-19T10:14:43.873+07:00  INFO 50860 --- [qtp85102332-39] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint BFSOneCRMService/createBFSOnePartner
2025-09-19T10:15:03.406+07:00  INFO 50860 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T10:15:03.410+07:00  INFO 50860 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-19T10:15:03.413+07:00  INFO 50860 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-19T10:15:03.422+07:00  INFO 50860 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 19/09/2025@10:15:03+0700
2025-09-19T10:15:03.443+07:00  INFO 50860 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 19/09/2025@10:15:00+0700 to 19/09/2025@10:30:00+0700
2025-09-19T10:15:03.443+07:00  INFO 50860 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 19/09/2025@10:15:00+0700 to 19/09/2025@10:30:00+0700
2025-09-19T10:15:31.017+07:00  INFO 50860 --- [qtp85102332-34] c.d.f.core.partner.CRMPartnerLogic       : --- Create account: bfsone code = CL000080_TEMP
2025-09-19T10:15:31.155+07:00  INFO 50860 --- [qtp85102332-34] c.d.f.core.message.CRMMessageLogic       : 📅 Scheduled message [id=22093] [company=bee] type=MAIL for 19/09/2025@10:15:31+0700
2025-09-19T10:15:31.155+07:00  INFO 50860 --- [qtp85102332-34] c.d.f.core.message.MessageQueueManager   : Added message [22093] - scheduled at 19/09/2025@10:15:31+0700 - current session (19/09/2025@10:15:00+0700 to 19/09/2025@10:30:00+0700)

2025-09-19T10:16:06.556+07:00  INFO 50860 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T10:16:12.605+07:00  INFO 50860 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 13, expire count 0
2025-09-19T10:16:12.617+07:00  INFO 50860 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T10:17:02.704+07:00  INFO 50860 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T10:18:05.807+07:00  INFO 50860 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T10:18:11.826+07:00  INFO 50860 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T10:18:11.834+07:00  INFO 50860 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T10:18:54.640+07:00  INFO 50860 --- [Scheduler-1390121014-1] n.d.m.session.AppHttpSessionListener     : The session node01069vvd87js066501vak3dgmq0 is destroyed.
2025-09-19T10:18:58.988+07:00  INFO 50860 --- [qtp85102332-36] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint CRMPartnerService/getCRMPartnerByCode
2025-09-19T10:19:00.015+07:00  INFO 50860 --- [qtp85102332-60] n.d.module.session.ClientSessionManager  : Add a client session id = node026emcvugymv7hvu1gol8vo1p1, token = f6470a95190b9569562efcd1c65b8483
2025-09-19T10:19:00.016+07:00  INFO 50860 --- [qtp85102332-34] n.d.module.session.ClientSessionManager  : Add a client session id = node026emcvugymv7hvu1gol8vo1p1, token = f6470a95190b9569562efcd1c65b8483
2025-09-19T10:19:00.026+07:00  INFO 50860 --- [qtp85102332-60] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:19:00.026+07:00  INFO 50860 --- [qtp85102332-34] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:19:06.920+07:00  INFO 50860 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T10:19:26.610+07:00  INFO 50860 --- [qtp85102332-67] c.d.f.core.message.CRMMessageLogic       : 📅 Scheduled message [id=22094] [company=bee] type=MAIL for 19/09/2025@10:19:26+0700
2025-09-19T10:19:26.611+07:00  INFO 50860 --- [qtp85102332-67] c.d.f.core.message.MessageQueueManager   : Added message [22094] - scheduled at 19/09/2025@10:19:26+0700 - current session (19/09/2025@10:15:00+0700 to 19/09/2025@10:30:00+0700)

2025-09-19T10:19:33.625+07:00  INFO 50860 --- [qtp85102332-34] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T10:19:33.625+07:00  INFO 50860 --- [qtp85102332-67] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T10:19:33.666+07:00  INFO 50860 --- [qtp85102332-67] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-19T10:19:33.666+07:00  INFO 50860 --- [qtp85102332-34] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-19T10:20:05.015+07:00  INFO 50860 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T10:20:05.019+07:00  INFO 50860 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-19T10:20:12.080+07:00  INFO 50860 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 14, expire count 1
2025-09-19T10:20:12.124+07:00  INFO 50860 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-19T10:21:06.221+07:00  INFO 50860 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T10:22:04.312+07:00  INFO 50860 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T10:22:11.368+07:00  INFO 50860 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-09-19T10:22:11.378+07:00  INFO 50860 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T10:22:26.825+07:00  INFO 50860 --- [qtp85102332-60] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0bi0o5d4uudnp12y7k0ct1ynql2
2025-09-19T10:22:26.964+07:00  INFO 50860 --- [qtp85102332-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0bi0o5d4uudnp12y7k0ct1ynql2, token = 81a42c51ff40141d14fa5ed2ad1ed2ee
2025-09-19T10:22:26.976+07:00  INFO 50860 --- [qtp85102332-60] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-19T10:23:06.455+07:00  INFO 50860 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T10:24:03.550+07:00  INFO 50860 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T10:24:10.600+07:00  INFO 50860 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 11, expire count 0
2025-09-19T10:24:10.615+07:00  INFO 50860 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T10:24:52.146+07:00  INFO 50860 --- [qtp85102332-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0bi0o5d4uudnp12y7k0ct1ynql2, token = 81a42c51ff40141d14fa5ed2ad1ed2ee
2025-09-19T10:24:52.160+07:00  INFO 50860 --- [qtp85102332-60] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-19T10:24:52.174+07:00  INFO 50860 --- [qtp85102332-62] n.d.module.session.ClientSessionManager  : Add a client session id = node0bi0o5d4uudnp12y7k0ct1ynql2, token = 81a42c51ff40141d14fa5ed2ad1ed2ee
2025-09-19T10:24:52.180+07:00  INFO 50860 --- [qtp85102332-62] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-19T10:24:52.889+07:00  INFO 50860 --- [qtp85102332-36] n.d.module.session.ClientSessionManager  : Add a client session id = node026emcvugymv7hvu1gol8vo1p1, token = f6470a95190b9569562efcd1c65b8483
2025-09-19T10:24:52.893+07:00  INFO 50860 --- [qtp85102332-36] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:24:52.911+07:00  INFO 50860 --- [qtp85102332-66] n.d.module.session.ClientSessionManager  : Add a client session id = node026emcvugymv7hvu1gol8vo1p1, token = f6470a95190b9569562efcd1c65b8483
2025-09-19T10:24:52.967+07:00  INFO 50860 --- [qtp85102332-66] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:25:06.726+07:00  INFO 50860 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-19T10:25:06.730+07:00  INFO 50860 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T10:25:16.957+07:00  INFO 50860 --- [qtp85102332-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0bi0o5d4uudnp12y7k0ct1ynql2, token = 81a42c51ff40141d14fa5ed2ad1ed2ee
2025-09-19T10:25:16.975+07:00  INFO 50860 --- [qtp85102332-36] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-19T10:25:17.048+07:00  INFO 50860 --- [qtp85102332-100] n.d.module.session.ClientSessionManager  : Add a client session id = node0bi0o5d4uudnp12y7k0ct1ynql2, token = 81a42c51ff40141d14fa5ed2ad1ed2ee
2025-09-19T10:25:17.070+07:00  INFO 50860 --- [qtp85102332-100] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-19T10:25:17.907+07:00  INFO 50860 --- [qtp85102332-91] n.d.module.session.ClientSessionManager  : Add a client session id = node026emcvugymv7hvu1gol8vo1p1, token = f6470a95190b9569562efcd1c65b8483
2025-09-19T10:25:17.907+07:00  INFO 50860 --- [qtp85102332-70] n.d.module.session.ClientSessionManager  : Add a client session id = node026emcvugymv7hvu1gol8vo1p1, token = f6470a95190b9569562efcd1c65b8483
2025-09-19T10:25:17.910+07:00  INFO 50860 --- [qtp85102332-91] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:25:17.910+07:00  INFO 50860 --- [qtp85102332-70] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:26:02.819+07:00  INFO 50860 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T10:26:09.890+07:00  INFO 50860 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 9, expire count 5
2025-09-19T10:26:09.909+07:00  INFO 50860 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-19T10:27:05.995+07:00  INFO 50860 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T10:28:02.102+07:00  INFO 50860 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T10:28:09.157+07:00  INFO 50860 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-19T10:28:09.162+07:00  INFO 50860 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T10:29:05.242+07:00  INFO 50860 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T10:30:06.343+07:00  INFO 50860 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 19/09/2025@10:30:06+0700
2025-09-19T10:30:06.377+07:00  INFO 50860 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 19/09/2025@10:30:00+0700 to 19/09/2025@10:45:00+0700
2025-09-19T10:30:06.377+07:00  INFO 50860 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 19/09/2025@10:30:00+0700 to 19/09/2025@10:45:00+0700
2025-09-19T10:30:06.378+07:00  INFO 50860 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-19T10:30:06.379+07:00  INFO 50860 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T10:30:06.379+07:00  INFO 50860 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-19T10:30:08.393+07:00  INFO 50860 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 10
2025-09-19T10:30:08.398+07:00  INFO 50860 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-19T10:31:04.485+07:00  INFO 50860 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T10:32:06.583+07:00  INFO 50860 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T10:32:12.636+07:00  INFO 50860 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-19T10:32:12.645+07:00  INFO 50860 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T10:33:03.723+07:00  INFO 50860 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T10:33:09.227+07:00  INFO 50860 --- [qtp85102332-101] n.d.module.session.ClientSessionManager  : Add a client session id = node0bi0o5d4uudnp12y7k0ct1ynql2, token = 81a42c51ff40141d14fa5ed2ad1ed2ee
2025-09-19T10:33:09.230+07:00  INFO 50860 --- [qtp85102332-99] n.d.module.session.ClientSessionManager  : Add a client session id = node0bi0o5d4uudnp12y7k0ct1ynql2, token = 81a42c51ff40141d14fa5ed2ad1ed2ee
2025-09-19T10:33:09.250+07:00  INFO 50860 --- [qtp85102332-101] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-19T10:33:09.250+07:00  INFO 50860 --- [qtp85102332-99] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-19T10:33:09.915+07:00  INFO 50860 --- [qtp85102332-62] n.d.module.session.ClientSessionManager  : Add a client session id = node026emcvugymv7hvu1gol8vo1p1, token = f6470a95190b9569562efcd1c65b8483
2025-09-19T10:33:09.933+07:00  INFO 50860 --- [qtp85102332-62] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:33:10.043+07:00  INFO 50860 --- [qtp85102332-108] n.d.module.session.ClientSessionManager  : Add a client session id = node026emcvugymv7hvu1gol8vo1p1, token = f6470a95190b9569562efcd1c65b8483
2025-09-19T10:33:10.063+07:00  INFO 50860 --- [qtp85102332-108] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:33:19.528+07:00  INFO 50860 --- [qtp85102332-108] n.d.module.session.ClientSessionManager  : Add a client session id = node0bi0o5d4uudnp12y7k0ct1ynql2, token = 81a42c51ff40141d14fa5ed2ad1ed2ee
2025-09-19T10:33:19.530+07:00  INFO 50860 --- [qtp85102332-40] n.d.module.session.ClientSessionManager  : Add a client session id = node0bi0o5d4uudnp12y7k0ct1ynql2, token = 81a42c51ff40141d14fa5ed2ad1ed2ee
2025-09-19T10:33:19.540+07:00  INFO 50860 --- [qtp85102332-108] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-19T10:33:19.540+07:00  INFO 50860 --- [qtp85102332-40] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-19T10:33:19.907+07:00  INFO 50860 --- [qtp85102332-99] n.d.module.session.ClientSessionManager  : Add a client session id = node026emcvugymv7hvu1gol8vo1p1, token = f6470a95190b9569562efcd1c65b8483
2025-09-19T10:33:19.923+07:00  INFO 50860 --- [qtp85102332-99] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:33:19.964+07:00  INFO 50860 --- [qtp85102332-83] n.d.module.session.ClientSessionManager  : Add a client session id = node026emcvugymv7hvu1gol8vo1p1, token = f6470a95190b9569562efcd1c65b8483
2025-09-19T10:33:19.971+07:00  INFO 50860 --- [qtp85102332-83] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:33:24.286+07:00  INFO 50860 --- [qtp85102332-108] n.d.module.session.ClientSessionManager  : Add a client session id = node0bi0o5d4uudnp12y7k0ct1ynql2, token = 81a42c51ff40141d14fa5ed2ad1ed2ee
2025-09-19T10:33:24.287+07:00  INFO 50860 --- [qtp85102332-99] n.d.module.session.ClientSessionManager  : Add a client session id = node0bi0o5d4uudnp12y7k0ct1ynql2, token = 81a42c51ff40141d14fa5ed2ad1ed2ee
2025-09-19T10:33:24.299+07:00  INFO 50860 --- [qtp85102332-99] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-19T10:33:24.299+07:00  INFO 50860 --- [qtp85102332-108] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-19T10:33:24.896+07:00  INFO 50860 --- [qtp85102332-108] n.d.module.session.ClientSessionManager  : Add a client session id = node026emcvugymv7hvu1gol8vo1p1, token = f6470a95190b9569562efcd1c65b8483
2025-09-19T10:33:24.898+07:00  INFO 50860 --- [qtp85102332-91] n.d.module.session.ClientSessionManager  : Add a client session id = node026emcvugymv7hvu1gol8vo1p1, token = f6470a95190b9569562efcd1c65b8483
2025-09-19T10:33:24.902+07:00  INFO 50860 --- [qtp85102332-108] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:33:24.918+07:00  INFO 50860 --- [qtp85102332-91] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:33:29.066+07:00  INFO 50860 --- [qtp85102332-91] n.d.module.session.ClientSessionManager  : Add a client session id = node0bi0o5d4uudnp12y7k0ct1ynql2, token = 81a42c51ff40141d14fa5ed2ad1ed2ee
2025-09-19T10:33:29.067+07:00  INFO 50860 --- [qtp85102332-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0bi0o5d4uudnp12y7k0ct1ynql2, token = 81a42c51ff40141d14fa5ed2ad1ed2ee
2025-09-19T10:33:29.088+07:00  INFO 50860 --- [qtp85102332-91] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-19T10:33:29.091+07:00  INFO 50860 --- [qtp85102332-36] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-19T10:33:29.893+07:00  INFO 50860 --- [qtp85102332-100] n.d.module.session.ClientSessionManager  : Add a client session id = node026emcvugymv7hvu1gol8vo1p1, token = f6470a95190b9569562efcd1c65b8483
2025-09-19T10:33:29.895+07:00  INFO 50860 --- [qtp85102332-83] n.d.module.session.ClientSessionManager  : Add a client session id = node026emcvugymv7hvu1gol8vo1p1, token = f6470a95190b9569562efcd1c65b8483
2025-09-19T10:33:29.903+07:00  INFO 50860 --- [qtp85102332-100] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:33:29.908+07:00  INFO 50860 --- [qtp85102332-83] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:34:00.644+07:00  INFO 50860 --- [qtp85102332-107] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-19T10:34:01.069+07:00  INFO 50860 --- [qtp85102332-107] c.d.f.core.partner.PartnerRequestLogic   : ------------------------Partner---------------------------

2025-09-19T10:34:01.070+07:00  INFO 50860 --- [qtp85102332-107] c.d.f.core.partner.PartnerRequestLogic   : --------------------------------------------------------

2025-09-19T10:34:01.088+07:00  INFO 50860 --- [qtp85102332-107] c.d.f.core.message.CRMMessageLogic       : 📅 Scheduled message [id=22095] [company=bee] type=MAIL for 19/09/2025@10:34:01+0700
2025-09-19T10:34:01.089+07:00  INFO 50860 --- [qtp85102332-107] c.d.f.core.message.MessageQueueManager   : Added message [22095] - scheduled at 19/09/2025@10:34:01+0700 - current session (19/09/2025@10:30:00+0700 to 19/09/2025@10:45:00+0700)

2025-09-19T10:34:06.830+07:00  INFO 50860 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T10:34:11.857+07:00  INFO 50860 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 10, expire count 9
2025-09-19T10:34:11.871+07:00  INFO 50860 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T10:35:02.968+07:00  INFO 50860 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T10:35:02.975+07:00  INFO 50860 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-19T10:36:06.091+07:00  INFO 50860 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T10:36:12.148+07:00  INFO 50860 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-09-19T10:36:12.155+07:00  INFO 50860 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T10:37:02.239+07:00  INFO 50860 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T10:37:15.611+07:00  INFO 50860 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@32bbb6e{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-19T10:37:15.614+07:00  INFO 50860 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-19T10:37:15.614+07:00  INFO 50860 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-19T10:37:15.614+07:00  INFO 50860 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-19T10:37:15.615+07:00  INFO 50860 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-19T10:37:15.615+07:00  INFO 50860 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-19T10:37:15.615+07:00  INFO 50860 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-19T10:37:15.615+07:00  INFO 50860 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-19T10:37:15.615+07:00  INFO 50860 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-09-19T10:37:15.615+07:00  INFO 50860 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-09-19T10:37:15.615+07:00  INFO 50860 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-19T10:37:15.615+07:00  INFO 50860 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-09-19T10:37:15.615+07:00  INFO 50860 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-09-19T10:37:15.615+07:00  INFO 50860 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-19T10:37:15.615+07:00  INFO 50860 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-09-19T10:37:15.615+07:00  INFO 50860 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-09-19T10:37:15.632+07:00  INFO 50860 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T10:37:15.720+07:00  INFO 50860 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-19T10:37:15.724+07:00  INFO 50860 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-19T10:37:15.762+07:00  INFO 50860 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T10:37:15.764+07:00  INFO 50860 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T10:37:15.765+07:00  INFO 50860 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T10:37:15.765+07:00  INFO 50860 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-19T10:37:15.766+07:00  INFO 50860 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-19T10:37:15.766+07:00  INFO 50860 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-19T10:37:15.766+07:00  INFO 50860 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-19T10:37:15.767+07:00  INFO 50860 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-19T10:37:15.767+07:00  INFO 50860 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-19T10:37:15.767+07:00  INFO 50860 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-19T10:37:15.767+07:00  INFO 50860 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-19T10:37:15.767+07:00  INFO 50860 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-19T10:37:15.767+07:00  INFO 50860 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-19T10:37:15.769+07:00  INFO 50860 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@7da4c956{STOPPING}[12.0.15,sto=0]
2025-09-19T10:37:15.771+07:00  INFO 50860 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-19T10:37:15.772+07:00  INFO 50860 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@2dba6013{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.11678029312027985455/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@4873d4cb{STOPPED}}
