2025-09-19T16:46:34.533+07:00  INFO 98279 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 98279 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-19T16:46:34.533+07:00  INFO 98279 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-19T16:46:35.208+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:46:35.273+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 62 ms. Found 22 JPA repository interfaces.
2025-09-19T16:46:35.282+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:46:35.283+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-19T16:46:35.284+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:46:35.328+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 44 ms. Found 10 JPA repository interfaces.
2025-09-19T16:46:35.329+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:46:35.332+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-19T16:46:35.340+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:46:35.345+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-09-19T16:46:35.353+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:46:35.355+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-19T16:46:35.355+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:46:35.358+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-19T16:46:35.361+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:46:35.365+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 5 JPA repository interfaces.
2025-09-19T16:46:35.369+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:46:35.375+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 3 JPA repository interfaces.
2025-09-19T16:46:35.375+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:46:35.376+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-19T16:46:35.376+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:46:35.382+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 10 JPA repository interfaces.
2025-09-19T16:46:35.387+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:46:35.389+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-19T16:46:35.392+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:46:35.396+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-19T16:46:35.396+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:46:35.404+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-09-19T16:46:35.404+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:46:35.407+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-09-19T16:46:35.408+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:46:35.408+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-19T16:46:35.408+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:46:35.409+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-19T16:46:35.409+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:46:35.413+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-19T16:46:35.413+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:46:35.414+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-19T16:46:35.414+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:46:35.414+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-19T16:46:35.415+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:46:35.425+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 19 JPA repository interfaces.
2025-09-19T16:46:35.435+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:46:35.441+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 8 JPA repository interfaces.
2025-09-19T16:46:35.441+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:46:35.444+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-09-19T16:46:35.445+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:46:35.449+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-09-19T16:46:35.449+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:46:35.454+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-09-19T16:46:35.455+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:46:35.458+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-19T16:46:35.459+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:46:35.468+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 13 JPA repository interfaces.
2025-09-19T16:46:35.469+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:46:35.478+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 14 JPA repository interfaces.
2025-09-19T16:46:35.479+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:46:35.493+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 13 ms. Found 24 JPA repository interfaces.
2025-09-19T16:46:35.493+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:46:35.494+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-19T16:46:35.499+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:46:35.500+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-19T16:46:35.500+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:46:35.507+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-19T16:46:35.509+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:46:35.546+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 37 ms. Found 66 JPA repository interfaces.
2025-09-19T16:46:35.547+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:46:35.548+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-19T16:46:35.552+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:46:35.556+07:00  INFO 98279 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-09-19T16:46:35.768+07:00  INFO 98279 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-19T16:46:35.772+07:00  INFO 98279 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-19T16:46:36.051+07:00  WARN 98279 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-19T16:46:36.243+07:00  INFO 98279 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-19T16:46:36.246+07:00  INFO 98279 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-19T16:46:36.257+07:00  INFO 98279 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-19T16:46:36.257+07:00  INFO 98279 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1625 ms
2025-09-19T16:46:36.307+07:00  WARN 98279 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-19T16:46:36.307+07:00  INFO 98279 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-19T16:46:36.408+07:00  INFO 98279 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@7ca46aaf
2025-09-19T16:46:36.410+07:00  INFO 98279 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-19T16:46:36.415+07:00  WARN 98279 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-19T16:46:36.415+07:00  INFO 98279 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-19T16:46:36.421+07:00  INFO 98279 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@7e49ab66
2025-09-19T16:46:36.421+07:00  INFO 98279 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-19T16:46:36.421+07:00  WARN 98279 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-19T16:46:36.421+07:00  INFO 98279 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-19T16:46:36.428+07:00  INFO 98279 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@67aedd28
2025-09-19T16:46:36.428+07:00  INFO 98279 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-19T16:46:36.428+07:00  WARN 98279 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-19T16:46:36.428+07:00  INFO 98279 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-19T16:46:36.436+07:00  INFO 98279 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@3bf92d96
2025-09-19T16:46:36.436+07:00  INFO 98279 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-19T16:46:36.436+07:00  WARN 98279 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-19T16:46:36.436+07:00  INFO 98279 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-19T16:46:36.443+07:00  INFO 98279 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@18f37d6e
2025-09-19T16:46:36.443+07:00  INFO 98279 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-19T16:46:36.443+07:00  INFO 98279 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-19T16:46:36.486+07:00  INFO 98279 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-19T16:46:36.537+07:00  INFO 98279 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@58fe7e22{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.4386514311541624229/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@597f9d91{STARTED}}
2025-09-19T16:46:36.538+07:00  INFO 98279 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@58fe7e22{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.4386514311541624229/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@597f9d91{STARTED}}
2025-09-19T16:46:36.540+07:00  INFO 98279 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@5cb3c9e6{STARTING}[12.0.15,sto=0] @2577ms
2025-09-19T16:46:36.592+07:00  INFO 98279 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-19T16:46:36.620+07:00  INFO 98279 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-19T16:46:36.635+07:00  INFO 98279 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-19T16:46:36.758+07:00  INFO 98279 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-19T16:46:36.782+07:00  WARN 98279 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-19T16:46:37.406+07:00  INFO 98279 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-19T16:46:37.415+07:00  INFO 98279 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@57f28893] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-19T16:46:37.556+07:00  INFO 98279 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T16:46:37.774+07:00  INFO 98279 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.graphapi", "net.datatp.module.kpi", "net.datatp.module.wfms", "net.datatp.module.monitor.activity", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "net.datatp.module.okr", "cloud.datatp.module.fleet", "net.datatp.module.core.template", "cloud.datatp.module.pegasus.shipment", "cloud.datatp.gps", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "cloud.datatp.tms", "net.datatp.module.groovy", "net.datatp.module.company.tmpl", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "net.datatp.module.resource", "cloud.datatp.vendor", "net.datatp.module.company.hr", "net.datatp.module.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "cloud.datatp.module.odoo", "net.datatp.module.chatbot", "net.datatp.module.core.print", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-09-19T16:46:37.777+07:00  INFO 98279 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-19T16:46:37.783+07:00  INFO 98279 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-19T16:46:37.784+07:00  INFO 98279 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-19T16:46:37.809+07:00  INFO 98279 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-19T16:46:37.813+07:00  WARN 98279 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-19T16:46:39.810+07:00  INFO 98279 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-19T16:46:39.811+07:00  INFO 98279 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@25205c9] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-19T16:46:39.981+07:00  WARN 98279 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-19T16:46:39.981+07:00  WARN 98279 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-19T16:46:39.988+07:00  WARN 98279 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-19T16:46:39.988+07:00  WARN 98279 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-19T16:46:40.001+07:00  WARN 98279 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-19T16:46:40.002+07:00  WARN 98279 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-19T16:46:40.456+07:00  INFO 98279 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T16:46:40.462+07:00  INFO 98279 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-19T16:46:40.463+07:00  INFO 98279 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-19T16:46:40.482+07:00  INFO 98279 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-19T16:46:40.484+07:00  WARN 98279 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-19T16:46:40.998+07:00  INFO 98279 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-19T16:46:40.998+07:00  INFO 98279 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@19f72243] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-19T16:46:41.050+07:00  WARN 98279 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-19T16:46:41.050+07:00  WARN 98279 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-19T16:46:41.526+07:00  INFO 98279 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T16:46:41.555+07:00  INFO 98279 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-19T16:46:41.560+07:00  INFO 98279 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-19T16:46:41.560+07:00  INFO 98279 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T16:46:41.566+07:00  WARN 98279 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-19T16:46:41.691+07:00  INFO 98279 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-19T16:46:42.135+07:00  INFO 98279 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-19T16:46:42.138+07:00  INFO 98279 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-19T16:46:42.172+07:00  INFO 98279 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-19T16:46:42.219+07:00  INFO 98279 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-19T16:46:42.275+07:00  INFO 98279 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-19T16:46:42.304+07:00  INFO 98279 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-19T16:46:42.342+07:00  INFO 98279 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 342467104ms : this is harmless.
2025-09-19T16:46:42.351+07:00  INFO 98279 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-19T16:46:42.354+07:00  INFO 98279 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-19T16:46:42.376+07:00  INFO 98279 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 633973064ms : this is harmless.
2025-09-19T16:46:42.378+07:00  INFO 98279 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-19T16:46:42.391+07:00  INFO 98279 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-19T16:46:42.391+07:00  INFO 98279 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-19T16:46:44.179+07:00  INFO 98279 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-19T16:46:44.179+07:00  INFO 98279 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T16:46:44.179+07:00  WARN 98279 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-19T16:46:44.453+07:00  INFO 98279 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 19/09/2025@16:45:00+0700 to 19/09/2025@17:00:00+0700
2025-09-19T16:46:44.453+07:00  INFO 98279 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 19/09/2025@16:45:00+0700 to 19/09/2025@17:00:00+0700
2025-09-19T16:46:44.992+07:00  INFO 98279 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-09-19T16:46:44.992+07:00  INFO 98279 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T16:46:44.993+07:00  WARN 98279 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-19T16:46:45.266+07:00  INFO 98279 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-09-19T16:46:45.266+07:00  INFO 98279 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-09-19T16:46:45.266+07:00  INFO 98279 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-09-19T16:46:45.266+07:00  INFO 98279 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-09-19T16:46:45.266+07:00  INFO 98279 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-09-19T16:46:47.140+07:00  WARN 98279 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: d042211c-0474-4a8d-ac99-0ff122cea884

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-19T16:46:47.143+07:00  INFO 98279 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-09-19T16:46:47.428+07:00  INFO 98279 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-19T16:46:47.428+07:00  INFO 98279 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-19T16:46:47.428+07:00  INFO 98279 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-19T16:46:47.428+07:00  INFO 98279 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-19T16:46:47.428+07:00  INFO 98279 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-09-19T16:46:47.428+07:00  INFO 98279 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-09-19T16:46:47.428+07:00  INFO 98279 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-19T16:46:47.428+07:00  INFO 98279 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-09-19T16:46:47.428+07:00  INFO 98279 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-09-19T16:46:47.428+07:00  INFO 98279 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-19T16:46:47.428+07:00  INFO 98279 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-09-19T16:46:47.428+07:00  INFO 98279 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-09-19T16:46:47.431+07:00  INFO 98279 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-19T16:46:47.431+07:00  INFO 98279 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-19T16:46:47.431+07:00  INFO 98279 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-19T16:46:47.498+07:00  INFO 98279 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-19T16:46:47.498+07:00  INFO 98279 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-19T16:46:47.500+07:00  INFO 98279 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-09-19T16:46:47.516+07:00  INFO 98279 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@47318922{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-19T16:46:47.517+07:00  INFO 98279 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-09-19T16:46:47.518+07:00  INFO 98279 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-09-19T16:46:47.541+07:00  INFO 98279 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-09-19T16:46:47.542+07:00  INFO 98279 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-09-19T16:46:47.547+07:00  INFO 98279 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 13.342 seconds (process running for 13.594)
2025-09-19T16:46:59.329+07:00  INFO 98279 --- [qtp22459165-35] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0mg5i03tgpoat10bhc90mhwui71
2025-09-19T16:46:59.329+07:00  INFO 98279 --- [qtp22459165-37] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01dbzblr080iix1c3lckretmvn90
2025-09-19T16:46:59.660+07:00  INFO 98279 --- [qtp22459165-37] n.d.module.session.ClientSessionManager  : Add a client session id = node01dbzblr080iix1c3lckretmvn90, token = 5472f488fc76ba0d478370fd8ee5a916
2025-09-19T16:46:59.661+07:00  INFO 98279 --- [qtp22459165-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0mg5i03tgpoat10bhc90mhwui71, token = 5472f488fc76ba0d478370fd8ee5a916
2025-09-19T16:47:00.186+07:00  INFO 98279 --- [qtp22459165-35] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-19T16:47:00.188+07:00  INFO 98279 --- [qtp22459165-37] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-19T16:47:06.469+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T16:47:50.633+07:00  INFO 98279 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 0
2025-09-19T16:47:50.674+07:00  INFO 98279 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-19T16:48:04.725+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T16:48:31.128+07:00  INFO 98279 --- [qtp22459165-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0mg5i03tgpoat10bhc90mhwui71, token = 5472f488fc76ba0d478370fd8ee5a916
2025-09-19T16:48:31.143+07:00  INFO 98279 --- [qtp22459165-35] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-19T16:48:31.169+07:00  INFO 98279 --- [qtp22459165-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0mg5i03tgpoat10bhc90mhwui71, token = 5472f488fc76ba0d478370fd8ee5a916
2025-09-19T16:48:31.175+07:00  INFO 98279 --- [qtp22459165-36] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-19T16:49:06.818+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T16:49:49.903+07:00  INFO 98279 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T16:49:49.926+07:00  INFO 98279 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T16:50:03.955+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-19T16:50:03.957+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T16:50:19.137+07:00  INFO 98279 --- [qtp22459165-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0mg5i03tgpoat10bhc90mhwui71, token = 5472f488fc76ba0d478370fd8ee5a916
2025-09-19T16:50:19.154+07:00  INFO 98279 --- [qtp22459165-35] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-19T16:50:19.163+07:00  INFO 98279 --- [qtp22459165-61] n.d.module.session.ClientSessionManager  : Add a client session id = node0mg5i03tgpoat10bhc90mhwui71, token = 5472f488fc76ba0d478370fd8ee5a916
2025-09-19T16:50:19.167+07:00  INFO 98279 --- [qtp22459165-61] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-19T16:51:06.077+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T16:51:54.197+07:00  INFO 98279 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-19T16:51:54.209+07:00  INFO 98279 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T16:52:03.236+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T16:53:06.342+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T16:53:54.441+07:00  INFO 98279 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-19T16:53:54.451+07:00  INFO 98279 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T16:54:02.470+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T16:55:05.571+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-19T16:55:05.575+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T16:55:53.657+07:00  INFO 98279 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T16:55:53.667+07:00  INFO 98279 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T16:56:06.689+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T16:57:04.781+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T16:57:36.505+07:00  INFO 98279 --- [Scheduler-549955869-1] n.d.m.session.AppHttpSessionListener     : The session node01dbzblr080iix1c3lckretmvn90 is destroyed.
2025-09-19T16:57:53.884+07:00  INFO 98279 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 3
2025-09-19T16:57:53.895+07:00  INFO 98279 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T16:58:06.911+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T16:58:16.552+07:00  INFO 98279 --- [qtp22459165-60] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint QuotationService/getSpecificQuotationById
2025-09-19T16:58:17.676+07:00  INFO 98279 --- [qtp22459165-61] n.d.module.session.ClientSessionManager  : Add a client session id = node0mg5i03tgpoat10bhc90mhwui71, token = 5472f488fc76ba0d478370fd8ee5a916
2025-09-19T16:58:17.690+07:00  INFO 98279 --- [qtp22459165-61] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-19T16:58:17.706+07:00  INFO 98279 --- [qtp22459165-71] n.d.module.session.ClientSessionManager  : Add a client session id = node0mg5i03tgpoat10bhc90mhwui71, token = 5472f488fc76ba0d478370fd8ee5a916
2025-09-19T16:58:17.716+07:00  INFO 98279 --- [qtp22459165-71] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-19T16:59:04.007+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T16:59:53.117+07:00  INFO 98279 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-19T16:59:53.133+07:00  INFO 98279 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T17:00:06.162+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-19T17:00:06.173+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-09-19T17:00:06.175+07:00  INFO 98279 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 19/09/2025@17:00:06+0700
2025-09-19T17:00:06.190+07:00  INFO 98279 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 19/09/2025@17:00:00+0700 to 19/09/2025@17:15:00+0700
2025-09-19T17:00:06.190+07:00  INFO 98279 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 19/09/2025@17:00:00+0700 to 19/09/2025@17:15:00+0700
2025-09-19T17:00:06.191+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-19T17:00:06.191+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T17:01:03.283+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T17:01:52.403+07:00  INFO 98279 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-09-19T17:01:52.410+07:00  INFO 98279 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T17:02:06.440+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T17:03:02.575+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T17:03:51.688+07:00  INFO 98279 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T17:03:51.700+07:00  INFO 98279 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T17:04:05.722+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T17:05:06.822+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T17:05:06.829+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-19T17:05:50.920+07:00  INFO 98279 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-19T17:05:50.930+07:00  INFO 98279 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T17:06:04.953+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T17:07:06.042+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T17:07:50.134+07:00  INFO 98279 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T17:07:50.140+07:00  INFO 98279 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T17:08:04.161+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T17:09:06.268+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T17:09:54.401+07:00  INFO 98279 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 5
2025-09-19T17:09:54.417+07:00  INFO 98279 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T17:10:03.430+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T17:10:03.434+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-19T17:11:06.544+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T17:11:53.624+07:00  INFO 98279 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-19T17:11:53.634+07:00  INFO 98279 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T17:12:02.652+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T17:13:05.736+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T17:13:53.854+07:00  INFO 98279 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-19T17:13:53.865+07:00  INFO 98279 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T17:14:06.886+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T17:15:04.996+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T17:15:05.003+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-19T17:15:05.005+07:00  INFO 98279 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 19/09/2025@17:15:05+0700
2025-09-19T17:15:05.042+07:00  INFO 98279 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 19/09/2025@17:15:00+0700 to 19/09/2025@17:30:00+0700
2025-09-19T17:15:05.042+07:00  INFO 98279 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 19/09/2025@17:15:00+0700 to 19/09/2025@17:30:00+0700
2025-09-19T17:15:05.042+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-19T17:15:54.181+07:00  INFO 98279 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-19T17:15:54.208+07:00  INFO 98279 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T17:16:06.224+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T17:17:04.315+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T17:17:53.411+07:00  INFO 98279 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T17:17:53.422+07:00  INFO 98279 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T17:18:06.444+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T17:19:03.576+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T17:19:52.700+07:00  INFO 98279 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-19T17:19:52.711+07:00  INFO 98279 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T17:20:06.726+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T17:20:06.729+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-19T17:21:02.829+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T17:21:51.925+07:00  INFO 98279 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T17:21:51.942+07:00  INFO 98279 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T17:22:05.972+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T17:23:02.079+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T17:23:51.192+07:00  INFO 98279 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-19T17:23:51.210+07:00  INFO 98279 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T17:24:05.231+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T17:25:06.330+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T17:25:06.337+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-19T17:25:50.423+07:00  INFO 98279 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-19T17:25:50.444+07:00  INFO 98279 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T17:26:04.468+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T17:27:06.604+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T17:27:49.730+07:00  INFO 98279 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-19T17:27:49.741+07:00  INFO 98279 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T17:28:03.766+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T17:29:03.063+07:00  INFO 98279 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@47318922{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-19T17:29:03.067+07:00  INFO 98279 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-19T17:29:03.067+07:00  INFO 98279 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-19T17:29:03.067+07:00  INFO 98279 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-19T17:29:03.067+07:00  INFO 98279 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-19T17:29:03.068+07:00  INFO 98279 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-19T17:29:03.068+07:00  INFO 98279 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T17:29:03.069+07:00  INFO 98279 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-19T17:29:03.069+07:00  INFO 98279 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-19T17:29:03.069+07:00  INFO 98279 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-09-19T17:29:03.069+07:00  INFO 98279 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-09-19T17:29:03.069+07:00  INFO 98279 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-19T17:29:03.069+07:00  INFO 98279 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-09-19T17:29:03.069+07:00  INFO 98279 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-09-19T17:29:03.069+07:00  INFO 98279 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-19T17:29:03.069+07:00  INFO 98279 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-09-19T17:29:03.069+07:00  INFO 98279 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-09-19T17:29:03.096+07:00  INFO 98279 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T17:29:03.195+07:00  INFO 98279 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-19T17:29:03.202+07:00  INFO 98279 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-19T17:29:03.227+07:00  INFO 98279 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T17:29:03.231+07:00  INFO 98279 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T17:29:03.233+07:00  INFO 98279 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T17:29:03.234+07:00  INFO 98279 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-19T17:29:03.236+07:00  INFO 98279 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-19T17:29:03.236+07:00  INFO 98279 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-19T17:29:03.236+07:00  INFO 98279 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-19T17:29:03.236+07:00  INFO 98279 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-19T17:29:03.236+07:00  INFO 98279 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-19T17:29:03.236+07:00  INFO 98279 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-19T17:29:03.237+07:00  INFO 98279 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-19T17:29:03.237+07:00  INFO 98279 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-19T17:29:03.237+07:00  INFO 98279 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-19T17:29:03.239+07:00  INFO 98279 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@5cb3c9e6{STOPPING}[12.0.15,sto=0]
2025-09-19T17:29:03.243+07:00  INFO 98279 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-19T17:29:03.245+07:00  INFO 98279 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@58fe7e22{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.4386514311541624229/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@597f9d91{STOPPED}}
