2025-09-19T16:22:28.967+07:00  INFO 95597 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 95597 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-19T16:22:28.968+07:00  INFO 95597 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-19T16:22:29.680+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:22:29.746+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 62 ms. Found 22 JPA repository interfaces.
2025-09-19T16:22:29.755+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:22:29.757+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-19T16:22:29.757+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:22:29.801+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 44 ms. Found 10 JPA repository interfaces.
2025-09-19T16:22:29.802+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:22:29.806+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 3 JPA repository interfaces.
2025-09-19T16:22:29.814+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:22:29.819+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-09-19T16:22:29.827+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:22:29.829+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-19T16:22:29.829+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:22:29.833+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-19T16:22:29.836+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:22:29.840+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 5 JPA repository interfaces.
2025-09-19T16:22:29.844+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:22:29.847+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-19T16:22:29.848+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:22:29.848+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-19T16:22:29.848+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:22:29.855+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 10 JPA repository interfaces.
2025-09-19T16:22:29.862+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:22:29.865+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-19T16:22:29.868+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:22:29.872+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-19T16:22:29.872+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:22:29.878+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-19T16:22:29.878+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:22:29.881+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-19T16:22:29.882+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:22:29.882+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-19T16:22:29.882+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:22:29.883+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-19T16:22:29.883+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:22:29.887+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-19T16:22:29.887+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:22:29.888+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-19T16:22:29.888+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:22:29.889+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-19T16:22:29.889+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:22:29.900+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 19 JPA repository interfaces.
2025-09-19T16:22:29.910+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:22:29.915+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 8 JPA repository interfaces.
2025-09-19T16:22:29.915+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:22:29.918+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-09-19T16:22:29.918+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:22:29.922+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-19T16:22:29.922+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:22:29.927+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-09-19T16:22:29.928+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:22:29.931+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-19T16:22:29.932+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:22:29.940+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 13 JPA repository interfaces.
2025-09-19T16:22:29.940+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:22:29.949+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 14 JPA repository interfaces.
2025-09-19T16:22:29.949+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:22:29.963+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 13 ms. Found 24 JPA repository interfaces.
2025-09-19T16:22:29.963+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:22:29.964+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-19T16:22:29.969+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:22:29.970+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-19T16:22:29.970+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:22:29.977+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-19T16:22:29.979+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:22:30.015+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 36 ms. Found 66 JPA repository interfaces.
2025-09-19T16:22:30.016+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:22:30.017+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-19T16:22:30.022+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T16:22:30.025+07:00  INFO 95597 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-09-19T16:22:30.247+07:00  INFO 95597 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-19T16:22:30.251+07:00  INFO 95597 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-19T16:22:30.533+07:00  WARN 95597 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-19T16:22:30.738+07:00  INFO 95597 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-19T16:22:30.740+07:00  INFO 95597 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-19T16:22:30.751+07:00  INFO 95597 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-19T16:22:30.751+07:00  INFO 95597 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1675 ms
2025-09-19T16:22:30.812+07:00  WARN 95597 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-19T16:22:30.812+07:00  INFO 95597 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-19T16:22:30.904+07:00  INFO 95597 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@3ec8c874
2025-09-19T16:22:30.905+07:00  INFO 95597 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-19T16:22:30.910+07:00  WARN 95597 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-19T16:22:30.910+07:00  INFO 95597 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-19T16:22:30.916+07:00  INFO 95597 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@5ccf1542
2025-09-19T16:22:30.917+07:00  INFO 95597 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-19T16:22:30.917+07:00  WARN 95597 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-19T16:22:30.917+07:00  INFO 95597 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-19T16:22:30.925+07:00  INFO 95597 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@7e49ab66
2025-09-19T16:22:30.926+07:00  INFO 95597 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-19T16:22:30.926+07:00  WARN 95597 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-19T16:22:30.926+07:00  INFO 95597 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-19T16:22:30.933+07:00  INFO 95597 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@67aedd28
2025-09-19T16:22:30.934+07:00  INFO 95597 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-19T16:22:30.934+07:00  WARN 95597 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-19T16:22:30.934+07:00  INFO 95597 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-19T16:22:30.942+07:00  INFO 95597 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@3bf92d96
2025-09-19T16:22:30.942+07:00  INFO 95597 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-19T16:22:30.942+07:00  INFO 95597 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-19T16:22:30.987+07:00  INFO 95597 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-19T16:22:31.037+07:00  INFO 95597 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@9b87a98{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.8778630082748066485/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@50ba10de{STARTED}}
2025-09-19T16:22:31.038+07:00  INFO 95597 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@9b87a98{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.8778630082748066485/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@50ba10de{STARTED}}
2025-09-19T16:22:31.040+07:00  INFO 95597 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@4f20ff8e{STARTING}[12.0.15,sto=0] @2604ms
2025-09-19T16:22:31.098+07:00  INFO 95597 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-19T16:22:31.125+07:00  INFO 95597 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-19T16:22:31.141+07:00  INFO 95597 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-19T16:22:31.264+07:00  INFO 95597 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-19T16:22:31.289+07:00  WARN 95597 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-19T16:22:31.909+07:00  INFO 95597 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-19T16:22:31.917+07:00  INFO 95597 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@12599bf3] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-19T16:22:32.041+07:00  INFO 95597 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T16:22:32.242+07:00  INFO 95597 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.graphapi", "net.datatp.module.kpi", "net.datatp.module.wfms", "net.datatp.module.monitor.activity", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "net.datatp.module.okr", "cloud.datatp.module.fleet", "net.datatp.module.core.template", "cloud.datatp.module.pegasus.shipment", "cloud.datatp.gps", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "cloud.datatp.tms", "net.datatp.module.groovy", "net.datatp.module.company.tmpl", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "cloud.datatp.vendor", "net.datatp.module.resource", "net.datatp.module.company.hr", "net.datatp.module.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.core.print", "net.datatp.module.chatbot", "cloud.datatp.module.odoo", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.zalo", "net.datatp.module.accounting" ]
2025-09-19T16:22:32.244+07:00  INFO 95597 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-19T16:22:32.250+07:00  INFO 95597 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-19T16:22:32.252+07:00  INFO 95597 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-19T16:22:32.277+07:00  INFO 95597 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-19T16:22:32.279+07:00  WARN 95597 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-19T16:22:34.270+07:00  INFO 95597 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-19T16:22:34.271+07:00  INFO 95597 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@66c718e] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-19T16:22:34.448+07:00  WARN 95597 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-19T16:22:34.448+07:00  WARN 95597 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-19T16:22:34.453+07:00  WARN 95597 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-19T16:22:34.453+07:00  WARN 95597 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-19T16:22:34.467+07:00  WARN 95597 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-19T16:22:34.467+07:00  WARN 95597 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-19T16:22:34.826+07:00  INFO 95597 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T16:22:34.832+07:00  INFO 95597 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-19T16:22:34.833+07:00  INFO 95597 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-19T16:22:34.853+07:00  INFO 95597 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-19T16:22:34.855+07:00  WARN 95597 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-19T16:22:35.377+07:00  INFO 95597 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-19T16:22:35.377+07:00  INFO 95597 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@5bad3e4] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-19T16:22:35.427+07:00  WARN 95597 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-19T16:22:35.428+07:00  WARN 95597 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-19T16:22:35.730+07:00  INFO 95597 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T16:22:35.759+07:00  INFO 95597 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-19T16:22:35.763+07:00  INFO 95597 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-19T16:22:35.764+07:00  INFO 95597 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T16:22:35.770+07:00  WARN 95597 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-19T16:22:35.897+07:00  INFO 95597 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-19T16:22:36.348+07:00  INFO 95597 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-19T16:22:36.351+07:00  INFO 95597 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-19T16:22:36.390+07:00  INFO 95597 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-19T16:22:36.430+07:00  INFO 95597 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-19T16:22:36.527+07:00  INFO 95597 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-19T16:22:36.563+07:00  INFO 95597 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-19T16:22:36.591+07:00  INFO 95597 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 341022357ms : this is harmless.
2025-09-19T16:22:36.601+07:00  INFO 95597 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-19T16:22:36.606+07:00  INFO 95597 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-19T16:22:36.642+07:00  INFO 95597 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 632528312ms : this is harmless.
2025-09-19T16:22:36.644+07:00  INFO 95597 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-19T16:22:36.659+07:00  INFO 95597 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-19T16:22:36.660+07:00  INFO 95597 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-19T16:22:38.511+07:00  INFO 95597 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-19T16:22:38.511+07:00  INFO 95597 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T16:22:38.513+07:00  WARN 95597 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-19T16:22:38.813+07:00  INFO 95597 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 19/09/2025@16:15:00+0700 to 19/09/2025@16:30:00+0700
2025-09-19T16:22:38.813+07:00  INFO 95597 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 19/09/2025@16:15:00+0700 to 19/09/2025@16:30:00+0700
2025-09-19T16:22:39.381+07:00  INFO 95597 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-09-19T16:22:39.381+07:00  INFO 95597 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T16:22:39.382+07:00  WARN 95597 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-19T16:22:39.667+07:00  INFO 95597 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-09-19T16:22:39.667+07:00  INFO 95597 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-09-19T16:22:39.667+07:00  INFO 95597 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-09-19T16:22:39.667+07:00  INFO 95597 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-09-19T16:22:39.667+07:00  INFO 95597 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-09-19T16:22:41.474+07:00  WARN 95597 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: bf74ad0b-c0ae-4ec1-bb66-be4f04d6d464

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-19T16:22:41.478+07:00  INFO 95597 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-09-19T16:22:41.783+07:00  INFO 95597 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-19T16:22:41.783+07:00  INFO 95597 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-19T16:22:41.783+07:00  INFO 95597 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-19T16:22:41.783+07:00  INFO 95597 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-19T16:22:41.783+07:00  INFO 95597 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-09-19T16:22:41.783+07:00  INFO 95597 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-09-19T16:22:41.783+07:00  INFO 95597 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-19T16:22:41.783+07:00  INFO 95597 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-09-19T16:22:41.783+07:00  INFO 95597 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-09-19T16:22:41.783+07:00  INFO 95597 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-19T16:22:41.783+07:00  INFO 95597 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-09-19T16:22:41.783+07:00  INFO 95597 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-09-19T16:22:41.787+07:00  INFO 95597 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-19T16:22:41.787+07:00  INFO 95597 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-19T16:22:41.787+07:00  INFO 95597 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-19T16:22:41.905+07:00  INFO 95597 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-19T16:22:41.905+07:00  INFO 95597 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-19T16:22:41.907+07:00  INFO 95597 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-09-19T16:22:41.916+07:00  INFO 95597 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@e3ec153{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-19T16:22:41.917+07:00  INFO 95597 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-09-19T16:22:41.918+07:00  INFO 95597 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-09-19T16:22:41.946+07:00  INFO 95597 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-09-19T16:22:41.946+07:00  INFO 95597 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-09-19T16:22:41.952+07:00  INFO 95597 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 13.286 seconds (process running for 13.516)
2025-09-19T16:22:59.131+07:00  INFO 95597 --- [qtp1828074475-37] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01ocddc8c209cp1c0awb979046w0
2025-09-19T16:22:59.131+07:00  INFO 95597 --- [qtp1828074475-39] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0j5j1yb71nlwsl3d9po0ooda61
2025-09-19T16:22:59.366+07:00  INFO 95597 --- [qtp1828074475-37] n.d.module.session.ClientSessionManager  : Add a client session id = node01ocddc8c209cp1c0awb979046w0, token = 5472f488fc76ba0d478370fd8ee5a916
2025-09-19T16:22:59.367+07:00  INFO 95597 --- [qtp1828074475-39] n.d.module.session.ClientSessionManager  : Add a client session id = node0j5j1yb71nlwsl3d9po0ooda61, token = 5472f488fc76ba0d478370fd8ee5a916
2025-09-19T16:22:59.809+07:00  INFO 95597 --- [qtp1828074475-39] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-19T16:22:59.815+07:00  INFO 95597 --- [qtp1828074475-37] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-19T16:23:02.829+07:00  INFO 95597 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T16:23:24.221+07:00  INFO 95597 --- [qtp1828074475-39] n.d.module.session.ClientSessionManager  : Add a client session id = node01ocddc8c209cp1c0awb979046w0, token = 5472f488fc76ba0d478370fd8ee5a916
2025-09-19T16:23:24.234+07:00  INFO 95597 --- [qtp1828074475-39] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-19T16:23:24.254+07:00  INFO 95597 --- [qtp1828074475-61] n.d.module.session.ClientSessionManager  : Add a client session id = node01ocddc8c209cp1c0awb979046w0, token = 5472f488fc76ba0d478370fd8ee5a916
2025-09-19T16:23:24.267+07:00  INFO 95597 --- [qtp1828074475-61] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-19T16:23:44.921+07:00  INFO 95597 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-09-19T16:23:44.939+07:00  INFO 95597 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T16:24:05.974+07:00  INFO 95597 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T16:25:02.073+07:00  INFO 95597 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T16:25:02.075+07:00  INFO 95597 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-19T16:25:44.170+07:00  INFO 95597 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-19T16:25:44.182+07:00  INFO 95597 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T16:26:05.221+07:00  INFO 95597 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T16:27:06.316+07:00  INFO 95597 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T16:27:48.563+07:00  INFO 95597 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 0
2025-09-19T16:27:48.592+07:00  INFO 95597 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-19T16:28:03.679+07:00  INFO 95597 --- [qtp1828074475-71] n.d.module.session.ClientSessionManager  : Add a client session id = node01ocddc8c209cp1c0awb979046w0, token = 5472f488fc76ba0d478370fd8ee5a916
2025-09-19T16:28:03.680+07:00  INFO 95597 --- [qtp1828074475-65] n.d.module.session.ClientSessionManager  : Add a client session id = node01ocddc8c209cp1c0awb979046w0, token = 5472f488fc76ba0d478370fd8ee5a916
2025-09-19T16:28:03.685+07:00  INFO 95597 --- [qtp1828074475-65] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-19T16:28:03.685+07:00  INFO 95597 --- [qtp1828074475-71] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-19T16:28:04.626+07:00  INFO 95597 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T16:28:37.701+07:00  INFO 95597 --- [qtp1828074475-39] n.d.module.session.ClientSessionManager  : Add a client session id = node01ocddc8c209cp1c0awb979046w0, token = 5472f488fc76ba0d478370fd8ee5a916
2025-09-19T16:28:37.710+07:00  INFO 95597 --- [qtp1828074475-39] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-19T16:28:37.729+07:00  INFO 95597 --- [qtp1828074475-66] n.d.module.session.ClientSessionManager  : Add a client session id = node01ocddc8c209cp1c0awb979046w0, token = 5472f488fc76ba0d478370fd8ee5a916
2025-09-19T16:28:37.735+07:00  INFO 95597 --- [qtp1828074475-66] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-19T16:29:06.734+07:00  INFO 95597 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T16:29:48.839+07:00  INFO 95597 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 8, expire count 0
2025-09-19T16:29:48.851+07:00  INFO 95597 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T16:30:03.882+07:00  INFO 95597 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T16:30:03.885+07:00  INFO 95597 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-19T16:30:03.889+07:00  INFO 95597 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-19T16:30:03.895+07:00  INFO 95597 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 19/09/2025@16:30:03+0700
2025-09-19T16:30:03.917+07:00  INFO 95597 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 19/09/2025@16:30:00+0700 to 19/09/2025@16:45:00+0700
2025-09-19T16:30:03.917+07:00  INFO 95597 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 19/09/2025@16:30:00+0700 to 19/09/2025@16:45:00+0700
2025-09-19T16:31:06.045+07:00  INFO 95597 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T16:31:48.132+07:00  INFO 95597 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-19T16:31:48.141+07:00  INFO 95597 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-19T16:32:03.178+07:00  INFO 95597 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T16:32:31.018+07:00  INFO 95597 --- [Scheduler-366054241-1] n.d.m.session.AppHttpSessionListener     : The session node0j5j1yb71nlwsl3d9po0ooda61 is destroyed.
2025-09-19T16:32:38.802+07:00  INFO 95597 --- [qtp1828074475-71] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-09-19T16:33:06.280+07:00  INFO 95597 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T16:33:40.117+07:00  INFO 95597 --- [qtp1828074475-71] n.d.module.session.ClientSessionManager  : Add a client session id = node01ocddc8c209cp1c0awb979046w0, token = 5472f488fc76ba0d478370fd8ee5a916
2025-09-19T16:33:40.153+07:00  INFO 95597 --- [qtp1828074475-71] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-19T16:33:40.169+07:00  INFO 95597 --- [qtp1828074475-41] n.d.module.session.ClientSessionManager  : Add a client session id = node01ocddc8c209cp1c0awb979046w0, token = 5472f488fc76ba0d478370fd8ee5a916
2025-09-19T16:33:40.186+07:00  INFO 95597 --- [qtp1828074475-41] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-19T16:33:48.443+07:00  INFO 95597 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 1
2025-09-19T16:33:48.462+07:00  INFO 95597 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T16:34:02.489+07:00  INFO 95597 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T16:35:05.606+07:00  INFO 95597 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T16:35:05.613+07:00  INFO 95597 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-19T16:35:47.676+07:00  INFO 95597 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-19T16:35:47.685+07:00  INFO 95597 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T16:36:06.712+07:00  INFO 95597 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T16:37:04.814+07:00  INFO 95597 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T16:37:46.908+07:00  INFO 95597 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-19T16:37:46.963+07:00  INFO 95597 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T16:38:06.995+07:00  INFO 95597 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T16:39:04.082+07:00  INFO 95597 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T16:39:46.171+07:00  INFO 95597 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 4
2025-09-19T16:39:46.178+07:00  INFO 95597 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T16:40:06.219+07:00  INFO 95597 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T16:40:06.225+07:00  INFO 95597 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-19T16:41:03.314+07:00  INFO 95597 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T16:41:45.406+07:00  INFO 95597 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-19T16:41:45.415+07:00  INFO 95597 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T16:42:06.443+07:00  INFO 95597 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T16:43:02.525+07:00  INFO 95597 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T16:43:44.604+07:00  INFO 95597 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 4
2025-09-19T16:43:44.616+07:00  INFO 95597 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T16:44:05.661+07:00  INFO 95597 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T16:45:06.777+07:00  INFO 95597 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T16:45:06.780+07:00  INFO 95597 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-19T16:45:06.780+07:00  INFO 95597 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 19/09/2025@16:45:06+0700
2025-09-19T16:45:06.803+07:00  INFO 95597 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 19/09/2025@16:45:00+0700 to 19/09/2025@17:00:00+0700
2025-09-19T16:45:06.803+07:00  INFO 95597 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 19/09/2025@16:45:00+0700 to 19/09/2025@17:00:00+0700
2025-09-19T16:45:06.803+07:00  INFO 95597 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-19T16:45:48.908+07:00  INFO 95597 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-19T16:45:48.925+07:00  INFO 95597 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T16:46:04.955+07:00  INFO 95597 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T16:46:21.460+07:00  INFO 95597 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@e3ec153{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-19T16:46:21.464+07:00  INFO 95597 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-19T16:46:21.464+07:00  INFO 95597 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-19T16:46:21.464+07:00  INFO 95597 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-19T16:46:21.464+07:00  INFO 95597 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-19T16:46:21.464+07:00  INFO 95597 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-19T16:46:21.464+07:00  INFO 95597 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-19T16:46:21.464+07:00  INFO 95597 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-19T16:46:21.464+07:00  INFO 95597 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-09-19T16:46:21.464+07:00  INFO 95597 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-09-19T16:46:21.464+07:00  INFO 95597 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-19T16:46:21.464+07:00  INFO 95597 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-09-19T16:46:21.464+07:00  INFO 95597 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-09-19T16:46:21.464+07:00  INFO 95597 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-19T16:46:21.464+07:00  INFO 95597 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-09-19T16:46:21.464+07:00  INFO 95597 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-09-19T16:46:21.482+07:00  INFO 95597 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T16:46:21.551+07:00  INFO 95597 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-19T16:46:21.558+07:00  INFO 95597 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-19T16:46:21.581+07:00  INFO 95597 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T16:46:21.583+07:00  INFO 95597 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T16:46:21.584+07:00  INFO 95597 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T16:46:21.584+07:00  INFO 95597 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-19T16:46:21.586+07:00  INFO 95597 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-19T16:46:21.586+07:00  INFO 95597 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-19T16:46:21.586+07:00  INFO 95597 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-19T16:46:21.586+07:00  INFO 95597 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-19T16:46:21.587+07:00  INFO 95597 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-19T16:46:21.587+07:00  INFO 95597 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-19T16:46:21.590+07:00  INFO 95597 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-19T16:46:21.590+07:00  INFO 95597 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-19T16:46:21.591+07:00  INFO 95597 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-19T16:46:21.593+07:00  INFO 95597 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@4f20ff8e{STOPPING}[12.0.15,sto=0]
2025-09-19T16:46:21.595+07:00  INFO 95597 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-19T16:46:21.596+07:00  INFO 95597 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@9b87a98{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.8778630082748066485/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@50ba10de{STOPPED}}
