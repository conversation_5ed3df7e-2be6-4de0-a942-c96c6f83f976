2025-09-19T17:40:55.561+07:00  INFO 5506 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 5506 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-19T17:40:55.563+07:00  INFO 5506 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-19T17:40:58.951+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:40:59.490+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 514 ms. Found 22 JPA repository interfaces.
2025-09-19T17:40:59.555+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:40:59.569+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 13 ms. Found 1 JPA repository interface.
2025-09-19T17:40:59.569+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:40:59.768+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 198 ms. Found 10 JPA repository interfaces.
2025-09-19T17:40:59.771+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:40:59.780+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 3 JPA repository interfaces.
2025-09-19T17:40:59.811+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:40:59.827+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 15 ms. Found 1 JPA repository interface.
2025-09-19T17:40:59.865+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:40:59.876+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 2 JPA repository interfaces.
2025-09-19T17:40:59.877+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:40:59.921+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 43 ms. Found 6 JPA repository interfaces.
2025-09-19T17:40:59.941+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:40:59.981+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 39 ms. Found 5 JPA repository interfaces.
2025-09-19T17:40:59.992+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:41:00.003+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 3 JPA repository interfaces.
2025-09-19T17:41:00.003+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:41:00.008+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 0 JPA repository interfaces.
2025-09-19T17:41:00.009+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:41:00.050+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 38 ms. Found 10 JPA repository interfaces.
2025-09-19T17:41:00.064+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:41:00.075+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 3 JPA repository interfaces.
2025-09-19T17:41:00.086+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:41:00.103+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 16 ms. Found 6 JPA repository interfaces.
2025-09-19T17:41:00.104+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:41:00.140+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 36 ms. Found 12 JPA repository interfaces.
2025-09-19T17:41:00.140+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:41:00.156+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 15 ms. Found 4 JPA repository interfaces.
2025-09-19T17:41:00.157+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:41:00.157+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-19T17:41:00.157+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:41:00.162+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-09-19T17:41:00.163+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:41:00.177+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 14 ms. Found 7 JPA repository interfaces.
2025-09-19T17:41:00.178+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:41:00.183+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 2 JPA repository interfaces.
2025-09-19T17:41:00.183+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:41:00.185+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-19T17:41:00.185+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:41:00.228+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 43 ms. Found 19 JPA repository interfaces.
2025-09-19T17:41:00.285+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:41:00.312+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 27 ms. Found 8 JPA repository interfaces.
2025-09-19T17:41:00.313+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:41:00.345+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 30 ms. Found 5 JPA repository interfaces.
2025-09-19T17:41:00.346+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:41:00.367+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 20 ms. Found 7 JPA repository interfaces.
2025-09-19T17:41:00.367+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:41:00.393+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 25 ms. Found 9 JPA repository interfaces.
2025-09-19T17:41:00.394+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:41:00.409+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 15 ms. Found 6 JPA repository interfaces.
2025-09-19T17:41:00.411+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:41:00.442+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 30 ms. Found 13 JPA repository interfaces.
2025-09-19T17:41:00.442+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:41:00.475+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 33 ms. Found 14 JPA repository interfaces.
2025-09-19T17:41:00.476+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:41:00.511+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 35 ms. Found 23 JPA repository interfaces.
2025-09-19T17:41:00.512+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:41:00.516+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 1 JPA repository interface.
2025-09-19T17:41:00.528+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:41:00.530+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 0 JPA repository interfaces.
2025-09-19T17:41:00.530+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:41:00.551+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 20 ms. Found 12 JPA repository interfaces.
2025-09-19T17:41:00.555+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:41:00.651+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 95 ms. Found 66 JPA repository interfaces.
2025-09-19T17:41:00.651+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:41:00.654+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-09-19T17:41:00.662+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:41:00.672+07:00  INFO 5506 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 4 JPA repository interfaces.
2025-09-19T17:41:01.110+07:00  INFO 5506 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-19T17:41:01.115+07:00  INFO 5506 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-19T17:41:01.637+07:00  WARN 5506 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-19T17:41:02.068+07:00  INFO 5506 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-19T17:41:02.072+07:00  INFO 5506 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-19T17:41:02.093+07:00  INFO 5506 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-19T17:41:02.093+07:00  INFO 5506 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 6054 ms
2025-09-19T17:41:02.194+07:00  WARN 5506 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-19T17:41:02.195+07:00  INFO 5506 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-19T17:41:02.398+07:00  INFO 5506 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@574f89bf
2025-09-19T17:41:02.399+07:00  INFO 5506 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-19T17:41:02.409+07:00  WARN 5506 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-19T17:41:02.409+07:00  INFO 5506 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-19T17:41:02.460+07:00  INFO 5506 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@13c0ced1
2025-09-19T17:41:02.460+07:00  INFO 5506 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-19T17:41:02.460+07:00  WARN 5506 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-19T17:41:02.460+07:00  INFO 5506 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-19T17:41:02.482+07:00  INFO 5506 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@3746766b
2025-09-19T17:41:02.482+07:00  INFO 5506 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-19T17:41:02.482+07:00  WARN 5506 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-19T17:41:02.482+07:00  INFO 5506 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-19T17:41:02.496+07:00  INFO 5506 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@12c1826
2025-09-19T17:41:02.497+07:00  INFO 5506 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-19T17:41:02.497+07:00  WARN 5506 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-19T17:41:02.497+07:00  INFO 5506 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-19T17:41:02.519+07:00  INFO 5506 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@6be8e6ee
2025-09-19T17:41:02.519+07:00  INFO 5506 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-19T17:41:02.519+07:00  INFO 5506 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-19T17:41:02.633+07:00  INFO 5506 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-19T17:41:02.639+07:00  INFO 5506 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@518e9141{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.10055278709606082049/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@42018e32{STARTED}}
2025-09-19T17:41:02.640+07:00  INFO 5506 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@518e9141{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.10055278709606082049/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@42018e32{STARTED}}
2025-09-19T17:41:02.642+07:00  INFO 5506 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@404cc16a{STARTING}[12.0.15,sto=0] @9328ms
2025-09-19T17:41:02.856+07:00  INFO 5506 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-19T17:41:02.893+07:00  INFO 5506 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-19T17:41:02.923+07:00  INFO 5506 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-19T17:41:03.209+07:00  INFO 5506 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-19T17:41:03.319+07:00  WARN 5506 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-19T17:41:04.435+07:00  INFO 5506 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-19T17:41:04.444+07:00  INFO 5506 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@5e7558ef] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-19T17:41:04.564+07:00  INFO 5506 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T17:41:04.758+07:00  INFO 5506 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.graphapi", "net.datatp.module.kpi", "net.datatp.module.wfms", "net.datatp.module.monitor.activity", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "net.datatp.module.okr", "cloud.datatp.module.fleet", "net.datatp.module.core.template", "cloud.datatp.gps", "cloud.datatp.module.pegasus.shipment", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "cloud.datatp.tms", "net.datatp.module.groovy", "net.datatp.module.company.tmpl", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "cloud.datatp.vendor", "net.datatp.module.resource", "net.datatp.module.company.hr", "net.datatp.module.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "cloud.datatp.module.odoo", "net.datatp.module.core.print", "net.datatp.module.chatbot", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.zalo", "net.datatp.module.accounting" ]
2025-09-19T17:41:04.760+07:00  INFO 5506 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-19T17:41:04.769+07:00  INFO 5506 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-19T17:41:04.771+07:00  INFO 5506 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-19T17:41:04.809+07:00  INFO 5506 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-19T17:41:04.826+07:00  WARN 5506 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-19T17:41:07.452+07:00  INFO 5506 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-19T17:41:07.454+07:00  INFO 5506 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@67d7f8bc] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-19T17:41:07.654+07:00  WARN 5506 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-19T17:41:07.654+07:00  WARN 5506 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-19T17:41:07.661+07:00  WARN 5506 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-19T17:41:07.661+07:00  WARN 5506 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-19T17:41:07.677+07:00  WARN 5506 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-19T17:41:07.677+07:00  WARN 5506 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-19T17:41:08.078+07:00  INFO 5506 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T17:41:08.086+07:00  INFO 5506 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-19T17:41:08.087+07:00  INFO 5506 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-19T17:41:08.107+07:00  INFO 5506 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-19T17:41:08.109+07:00  WARN 5506 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-19T17:41:08.864+07:00  INFO 5506 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-19T17:41:08.865+07:00  INFO 5506 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@14a447e9] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-19T17:41:08.924+07:00  WARN 5506 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-19T17:41:08.924+07:00  WARN 5506 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-19T17:41:09.463+07:00  INFO 5506 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T17:41:09.504+07:00  INFO 5506 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-19T17:41:09.510+07:00  INFO 5506 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-19T17:41:09.510+07:00  INFO 5506 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T17:41:09.519+07:00  WARN 5506 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-19T17:41:09.684+07:00  INFO 5506 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-19T17:41:10.192+07:00  INFO 5506 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-19T17:41:10.195+07:00  INFO 5506 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-19T17:41:10.234+07:00  INFO 5506 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-19T17:41:10.285+07:00  INFO 5506 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-19T17:41:10.379+07:00  INFO 5506 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-19T17:41:10.410+07:00  INFO 5506 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-19T17:41:10.445+07:00  INFO 5506 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 345567800ms : this is harmless.
2025-09-19T17:41:10.455+07:00  INFO 5506 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-19T17:41:10.459+07:00  INFO 5506 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-19T17:41:10.472+07:00  INFO 5506 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 637073753ms : this is harmless.
2025-09-19T17:41:10.474+07:00  INFO 5506 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-19T17:41:10.487+07:00  INFO 5506 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-19T17:41:10.488+07:00  INFO 5506 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-19T17:41:12.821+07:00  INFO 5506 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-19T17:41:12.821+07:00  INFO 5506 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T17:41:12.822+07:00  WARN 5506 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-19T17:41:13.157+07:00  INFO 5506 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 19/09/2025@17:30:00+0700 to 19/09/2025@17:45:00+0700
2025-09-19T17:41:13.157+07:00  INFO 5506 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 19/09/2025@17:30:00+0700 to 19/09/2025@17:45:00+0700
2025-09-19T17:41:13.269+07:00  WARN 5506 --- [main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'timeOffLogic': Unsatisfied dependency expressed through field 'remainingLogic': Error creating bean with name 'timeOffRemainingLogic': Unsatisfied dependency expressed through field 'employeeLogic': Error creating bean with name 'employeeReadLogic': Unsatisfied dependency expressed through field 'accountLogic': Error creating bean with name 'accountLogic': Unsatisfied dependency expressed through field 'plugins': Error creating bean with name 'communicationAccountServicePlugin': Unsatisfied dependency expressed through field 'service': Error creating bean with name 'CommunicationMessageService': Unsatisfied dependency expressed through field 'botService': Error creating bean with name 'BotService': Unsatisfied dependency expressed through method 'register' parameter 0: Error creating bean with name 'TMSBillBotJobTrackingRuleEventHandler': Unsatisfied dependency expressed through field 'customerLogic': Error creating bean with name 'TMSCustomerLogic': Unsatisfied dependency expressed through field 'partnerLogic': Error creating bean with name 'TMSPartnerLogic': Unsatisfied dependency expressed through field 'tmsBillLogic': Error creating bean with name 'TMSBillLogic': Unsatisfied dependency expressed through field 'vendorBillLogic': Error creating bean with name 'TMSVendorBillLogic': Unsatisfied dependency expressed through field 'trackingLogic': Error creating bean with name 'vehicleTripGoodsTrackingLogic': Unsatisfied dependency expressed through field 'vehicleLogic': Error creating bean with name 'vehicleLogic': Unsatisfied dependency expressed through field 'apiAuthorizationService': Error creating bean with name 'ApiAuthorizationService': Unsatisfied dependency expressed through method 'setPlugins' parameter 0: Error creating bean with name 'syncBFSOnePartnerCodeApiPlugin': Unsatisfied dependency expressed through field 'bfsOnePartnerService': Error creating bean with name 'CRMPartnerService': Unsatisfied dependency expressed through field 'crmPartnerLogic': Error creating bean with name 'CRMPartnerLogic': Unsatisfied dependency expressed through field 'crmMessageLogic': Error creating bean with name 'CRMMessageLogic': Unsatisfied dependency expressed through field 'plugins': Error creating bean with name 'taskCalendarMessagePlugin': Unsatisfied dependency expressed through field 'saleTaskReportLogic': Error creating bean with name 'taskCalendarLogic': Unsatisfied dependency expressed through field 'taskCalendarRepo': No qualifying bean of type 'cloud.datatp.fforwarder.sales.project.repository.TaskCalendarRepository' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@org.springframework.beans.factory.annotation.Autowired(required=true)}
2025-09-19T17:41:13.317+07:00  INFO 5506 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-19T17:41:13.321+07:00  INFO 5506 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-19T17:41:13.334+07:00  INFO 5506 --- [main] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T17:41:13.335+07:00  INFO 5506 --- [main] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T17:41:13.335+07:00  INFO 5506 --- [main] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T17:41:13.335+07:00  INFO 5506 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-19T17:41:13.337+07:00  INFO 5506 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-19T17:41:13.337+07:00  INFO 5506 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-19T17:41:13.337+07:00  INFO 5506 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-19T17:41:13.337+07:00  INFO 5506 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-19T17:41:13.338+07:00  INFO 5506 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-19T17:41:13.338+07:00  INFO 5506 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-19T17:41:13.338+07:00  INFO 5506 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-19T17:41:13.338+07:00  INFO 5506 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-19T17:41:13.339+07:00  INFO 5506 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-19T17:41:13.342+07:00  INFO 5506 --- [main] org.eclipse.jetty.server.Server          : Stopped oejs.Server@404cc16a{STOPPING}[12.0.15,sto=0]
2025-09-19T17:41:13.344+07:00  INFO 5506 --- [main] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@518e9141{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.10055278709606082049/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@42018e32{STOPPED}}
