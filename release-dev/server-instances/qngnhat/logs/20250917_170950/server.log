2025-09-17T17:09:50.670+07:00  INFO 61586 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 61586 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-17T17:09:50.671+07:00  INFO 61586 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-17T17:09:51.372+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T17:09:51.436+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 61 ms. Found 22 JPA repository interfaces.
2025-09-17T17:09:51.446+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T17:09:51.447+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-17T17:09:51.447+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T17:09:51.454+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 10 JPA repository interfaces.
2025-09-17T17:09:51.456+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T17:09:51.495+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 39 ms. Found 3 JPA repository interfaces.
2025-09-17T17:09:51.504+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T17:09:51.509+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-09-17T17:09:51.517+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T17:09:51.519+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-17T17:09:51.519+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T17:09:51.522+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-17T17:09:51.525+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T17:09:51.529+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-09-17T17:09:51.533+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T17:09:51.535+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-17T17:09:51.535+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T17:09:51.536+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-17T17:09:51.536+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T17:09:51.542+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 10 JPA repository interfaces.
2025-09-17T17:09:51.547+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T17:09:51.550+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-17T17:09:51.553+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T17:09:51.557+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-17T17:09:51.557+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T17:09:51.565+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-09-17T17:09:51.565+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T17:09:51.568+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-09-17T17:09:51.569+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T17:09:51.569+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-17T17:09:51.569+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T17:09:51.570+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-17T17:09:51.570+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T17:09:51.574+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-17T17:09:51.574+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T17:09:51.575+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-17T17:09:51.575+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T17:09:51.575+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-17T17:09:51.575+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T17:09:51.586+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 19 JPA repository interfaces.
2025-09-17T17:09:51.596+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T17:09:51.601+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 8 JPA repository interfaces.
2025-09-17T17:09:51.602+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T17:09:51.604+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-09-17T17:09:51.605+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T17:09:51.608+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-17T17:09:51.608+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T17:09:51.614+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-09-17T17:09:51.614+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T17:09:51.617+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-17T17:09:51.618+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T17:09:51.626+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 13 JPA repository interfaces.
2025-09-17T17:09:51.626+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T17:09:51.635+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 14 JPA repository interfaces.
2025-09-17T17:09:51.636+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T17:09:51.649+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 13 ms. Found 24 JPA repository interfaces.
2025-09-17T17:09:51.650+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T17:09:51.650+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-17T17:09:51.656+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T17:09:51.656+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-17T17:09:51.657+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T17:09:51.663+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-17T17:09:51.665+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T17:09:51.700+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 34 ms. Found 66 JPA repository interfaces.
2025-09-17T17:09:51.700+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T17:09:51.701+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-17T17:09:51.705+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T17:09:51.708+07:00  INFO 61586 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-17T17:09:51.903+07:00  INFO 61586 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-17T17:09:51.907+07:00  INFO 61586 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-17T17:09:52.252+07:00  WARN 61586 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-17T17:09:52.452+07:00  INFO 61586 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-17T17:09:52.454+07:00  INFO 61586 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-17T17:09:52.466+07:00  INFO 61586 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-17T17:09:52.466+07:00  INFO 61586 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1688 ms
2025-09-17T17:09:52.518+07:00  WARN 61586 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-17T17:09:52.518+07:00  INFO 61586 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-17T17:09:52.621+07:00  INFO 61586 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@579b1d86
2025-09-17T17:09:52.622+07:00  INFO 61586 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-17T17:09:52.627+07:00  WARN 61586 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-17T17:09:52.627+07:00  INFO 61586 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-17T17:09:52.634+07:00  INFO 61586 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@4015c65b
2025-09-17T17:09:52.634+07:00  INFO 61586 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-17T17:09:52.634+07:00  WARN 61586 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-17T17:09:52.634+07:00  INFO 61586 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-17T17:09:52.643+07:00  INFO 61586 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@2ea3f905
2025-09-17T17:09:52.643+07:00  INFO 61586 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-17T17:09:52.643+07:00  WARN 61586 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-17T17:09:52.643+07:00  INFO 61586 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-17T17:09:52.652+07:00  INFO 61586 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@4ab8c3c0
2025-09-17T17:09:52.652+07:00  INFO 61586 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-17T17:09:52.652+07:00  WARN 61586 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-17T17:09:52.652+07:00  INFO 61586 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-17T17:09:52.660+07:00  INFO 61586 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@71a18feb
2025-09-17T17:09:52.660+07:00  INFO 61586 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-17T17:09:52.661+07:00  INFO 61586 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-17T17:09:52.703+07:00  INFO 61586 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-17T17:09:52.705+07:00  INFO 61586 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@37eca4c0{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.7110507551069367228/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@5c452eb5{STARTED}}
2025-09-17T17:09:52.705+07:00  INFO 61586 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@37eca4c0{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.7110507551069367228/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@5c452eb5{STARTED}}
2025-09-17T17:09:52.707+07:00  INFO 61586 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@7e7f72{STARTING}[12.0.15,sto=0] @2569ms
2025-09-17T17:09:52.810+07:00  INFO 61586 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-17T17:09:52.837+07:00  INFO 61586 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-17T17:09:52.851+07:00  INFO 61586 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-17T17:09:52.975+07:00  INFO 61586 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-17T17:09:53.001+07:00  WARN 61586 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-17T17:09:53.648+07:00  INFO 61586 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-17T17:09:53.658+07:00  INFO 61586 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@2e27c2b2] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-17T17:09:53.778+07:00  INFO 61586 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-17T17:09:53.971+07:00  INFO 61586 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.wfms", "net.datatp.module.monitor.activity", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "cloud.datatp.module.fleet", "net.datatp.module.okr", "net.datatp.module.core.template", "cloud.datatp.module.pegasus.shipment", "cloud.datatp.gps", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "net.datatp.module.company.tmpl", "net.datatp.module.groovy", "cloud.datatp.tms", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "net.datatp.module.resource", "cloud.datatp.vendor", "net.datatp.module.hr", "net.datatp.module.company.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.core.print", "net.datatp.module.chatbot", "cloud.datatp.module.odoo", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-09-17T17:09:53.973+07:00  INFO 61586 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-17T17:09:53.979+07:00  INFO 61586 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-17T17:09:53.981+07:00  INFO 61586 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-17T17:09:54.006+07:00  INFO 61586 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-17T17:09:54.010+07:00  WARN 61586 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-17T17:09:56.241+07:00  INFO 61586 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-17T17:09:56.242+07:00  INFO 61586 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@669cfd24] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-17T17:09:56.593+07:00  WARN 61586 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-17T17:09:56.593+07:00  WARN 61586 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-17T17:09:56.603+07:00  WARN 61586 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-17T17:09:56.603+07:00  WARN 61586 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-17T17:09:56.619+07:00  WARN 61586 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-17T17:09:56.619+07:00  WARN 61586 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-17T17:09:57.016+07:00  INFO 61586 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-17T17:09:57.023+07:00  INFO 61586 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-17T17:09:57.025+07:00  INFO 61586 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-17T17:09:57.048+07:00  INFO 61586 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-17T17:09:57.050+07:00  WARN 61586 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-17T17:09:57.833+07:00  INFO 61586 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-17T17:09:57.834+07:00  INFO 61586 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@25ff2043] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-17T17:09:57.921+07:00  WARN 61586 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-17T17:09:57.921+07:00  WARN 61586 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-17T17:09:58.278+07:00  INFO 61586 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-17T17:09:58.318+07:00  INFO 61586 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-17T17:09:58.322+07:00  INFO 61586 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-17T17:09:58.322+07:00  INFO 61586 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T17:09:58.330+07:00  WARN 61586 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-17T17:09:58.490+07:00  INFO 61586 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-17T17:09:58.991+07:00  INFO 61586 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-17T17:09:58.994+07:00  INFO 61586 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-17T17:09:59.031+07:00  INFO 61586 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-17T17:09:59.082+07:00  INFO 61586 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-17T17:09:59.164+07:00  INFO 61586 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-17T17:09:59.198+07:00  INFO 61586 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-17T17:09:59.228+07:00  INFO 61586 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 170940738ms : this is harmless.
2025-09-17T17:09:59.237+07:00  INFO 61586 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-17T17:09:59.241+07:00  INFO 61586 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-17T17:09:59.255+07:00  INFO 61586 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 462446691ms : this is harmless.
2025-09-17T17:09:59.257+07:00  INFO 61586 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-17T17:09:59.271+07:00  INFO 61586 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-17T17:09:59.273+07:00  INFO 61586 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-17T17:10:01.919+07:00  INFO 61586 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-17T17:10:01.919+07:00  INFO 61586 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T17:10:01.920+07:00  WARN 61586 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-17T17:10:02.325+07:00  INFO 61586 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 1 messages for session 17/09/2025@17:00:00+0700 to 17/09/2025@17:15:00+0700
2025-09-17T17:10:02.325+07:00  INFO 61586 --- [main] c.d.f.core.message.MessageQueueManager   : 📤 message : MAIL - inquiry-overdue-reminder - scheduled at 17/09/2025@17:01:12+0700
2025-09-17T17:10:02.326+07:00  INFO 61586 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 1 messages for session 17/09/2025@17:00:00+0700 to 17/09/2025@17:15:00+0700
2025-09-17T17:10:02.973+07:00  INFO 61586 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-09-17T17:10:02.973+07:00  INFO 61586 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T17:10:02.974+07:00  WARN 61586 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-17T17:10:03.336+07:00  INFO 61586 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-09-17T17:10:03.336+07:00  INFO 61586 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-09-17T17:10:03.336+07:00  INFO 61586 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-09-17T17:10:03.336+07:00  INFO 61586 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-09-17T17:10:03.336+07:00  INFO 61586 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-09-17T17:10:05.378+07:00  WARN 61586 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 0eed95d2-0177-412b-962d-10150928b71e

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-17T17:10:05.381+07:00  INFO 61586 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-09-17T17:10:05.748+07:00  INFO 61586 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-17T17:10:05.748+07:00  INFO 61586 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-17T17:10:05.748+07:00  INFO 61586 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-17T17:10:05.748+07:00  INFO 61586 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-17T17:10:05.748+07:00  INFO 61586 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-09-17T17:10:05.748+07:00  INFO 61586 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-09-17T17:10:05.748+07:00  INFO 61586 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-17T17:10:05.748+07:00  INFO 61586 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-09-17T17:10:05.748+07:00  INFO 61586 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-09-17T17:10:05.748+07:00  INFO 61586 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-17T17:10:05.748+07:00  INFO 61586 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-09-17T17:10:05.748+07:00  INFO 61586 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-09-17T17:10:05.749+07:00  INFO 61586 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-17T17:10:05.750+07:00  INFO 61586 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-17T17:10:05.750+07:00  INFO 61586 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-17T17:10:05.821+07:00  INFO 61586 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-17T17:10:05.822+07:00  INFO 61586 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-17T17:10:05.823+07:00  INFO 61586 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-09-17T17:10:05.832+07:00  INFO 61586 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@697f8f0c{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-17T17:10:05.833+07:00  INFO 61586 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-09-17T17:10:05.834+07:00  INFO 61586 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-09-17T17:10:05.858+07:00  INFO 61586 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-09-17T17:10:05.858+07:00  INFO 61586 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-09-17T17:10:05.865+07:00  INFO 61586 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 15.475 seconds (process running for 15.73)
2025-09-17T17:10:34.971+07:00  INFO 61586 --- [qtp828508529-36] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-09-17T17:11:06.867+07:00  INFO 61586 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T17:11:08.932+07:00  INFO 61586 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T17:11:08.951+07:00  INFO 61586 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T17:11:37.743+07:00  INFO 61586 --- [qtp828508529-35] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0lbbnc3w77c301rpx3iylyqcjr1
2025-09-17T17:11:37.743+07:00  INFO 61586 --- [qtp828508529-39] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node015ldodvy6qyyv1jouguuxdzxln0
2025-09-17T17:11:37.827+07:00  INFO 61586 --- [qtp828508529-39] n.d.module.session.ClientSessionManager  : Add a client session id = node015ldodvy6qyyv1jouguuxdzxln0, token = 87f2fef0fc18442fa21c9aa3d559e717
2025-09-17T17:11:37.827+07:00  INFO 61586 --- [qtp828508529-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0lbbnc3w77c301rpx3iylyqcjr1, token = 87f2fef0fc18442fa21c9aa3d559e717
2025-09-17T17:11:38.419+07:00  INFO 61586 --- [qtp828508529-39] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-17T17:11:38.428+07:00  INFO 61586 --- [qtp828508529-35] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-17T17:12:05.046+07:00  INFO 61586 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T17:13:06.148+07:00  INFO 61586 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T17:13:08.166+07:00  INFO 61586 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-17T17:13:08.186+07:00  INFO 61586 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T17:13:11.828+07:00  INFO 61586 --- [qtp828508529-41] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le logout successfully 
2025-09-17T17:13:13.982+07:00  INFO 61586 --- [qtp828508529-35] n.d.module.session.ClientSessionManager  : Add a client session id = node015ldodvy6qyyv1jouguuxdzxln0, token = 9465b6a1242791ec35e8b3d75867622b
2025-09-17T17:13:13.988+07:00  INFO 61586 --- [qtp828508529-35] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-17T17:13:56.423+07:00  INFO 61586 --- [qtp828508529-36] n.d.module.session.ClientSessionManager  : Add a client session id = node015ldodvy6qyyv1jouguuxdzxln0, token = 9465b6a1242791ec35e8b3d75867622b
2025-09-17T17:13:56.424+07:00  INFO 61586 --- [qtp828508529-37] n.d.module.session.ClientSessionManager  : Add a client session id = node015ldodvy6qyyv1jouguuxdzxln0, token = 9465b6a1242791ec35e8b3d75867622b
2025-09-17T17:13:56.439+07:00  INFO 61586 --- [qtp828508529-37] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-17T17:13:56.439+07:00  INFO 61586 --- [qtp828508529-36] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-17T17:14:00.364+07:00  INFO 61586 --- [qtp828508529-63] n.d.module.session.ClientSessionManager  : Add a client session id = node015ldodvy6qyyv1jouguuxdzxln0, token = 9465b6a1242791ec35e8b3d75867622b
2025-09-17T17:14:00.365+07:00  INFO 61586 --- [qtp828508529-41] n.d.module.session.ClientSessionManager  : Add a client session id = node015ldodvy6qyyv1jouguuxdzxln0, token = 9465b6a1242791ec35e8b3d75867622b
2025-09-17T17:14:00.375+07:00  INFO 61586 --- [qtp828508529-41] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-17T17:14:00.378+07:00  INFO 61586 --- [qtp828508529-63] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-17T17:14:04.288+07:00  INFO 61586 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T17:14:19.281+07:00  INFO 61586 --- [qtp828508529-63] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-17T17:14:19.712+07:00  INFO 61586 --- [qtp828508529-63] c.d.f.core.partner.PartnerRequestLogic   : ------------------------Partner---------------------------

2025-09-17T17:14:19.713+07:00  INFO 61586 --- [qtp828508529-63] c.d.f.core.partner.PartnerRequestLogic   : --------------------------------------------------------

2025-09-17T17:14:19.734+07:00  INFO 61586 --- [qtp828508529-63] c.d.f.core.message.CRMMessageLogic       : 📅 Scheduled message [id=22068] [company=bee] type=MAIL for 17/09/2025@17:14:19+0700
2025-09-17T17:14:19.734+07:00  INFO 61586 --- [qtp828508529-63] c.d.f.core.message.MessageQueueManager   : Added message [22068] - scheduled at 17/09/2025@17:14:19+0700 - current session (17/09/2025@17:00:00+0700 to 17/09/2025@17:15:00+0700)

2025-09-17T17:15:06.388+07:00  INFO 61586 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T17:15:06.392+07:00  INFO 61586 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-17T17:15:06.399+07:00  INFO 61586 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T17:15:06.401+07:00  INFO 61586 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 17/09/2025@17:15:06+0700
2025-09-17T17:15:06.414+07:00  INFO 61586 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 1 messages for session 17/09/2025@17:15:00+0700 to 17/09/2025@17:30:00+0700
2025-09-17T17:15:06.414+07:00  INFO 61586 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 📤 message : MAIL - inquiry-overdue-reminder - scheduled at 17/09/2025@17:21:11+0700
2025-09-17T17:15:06.414+07:00  INFO 61586 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 1 messages for session 17/09/2025@17:15:00+0700 to 17/09/2025@17:30:00+0700
2025-09-17T17:15:12.478+07:00  INFO 61586 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 9, expire count 0
2025-09-17T17:15:12.482+07:00  INFO 61586 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T17:16:03.581+07:00  INFO 61586 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T17:17:06.692+07:00  INFO 61586 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T17:17:12.774+07:00  INFO 61586 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 16, expire count 0
2025-09-17T17:17:12.791+07:00  INFO 61586 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T17:18:02.875+07:00  INFO 61586 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T17:19:05.973+07:00  INFO 61586 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T17:19:11.983+07:00  INFO 61586 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T17:19:11.988+07:00  INFO 61586 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T17:19:52.734+07:00  INFO 61586 --- [Scheduler-102150834-1] n.d.m.session.AppHttpSessionListener     : The session node0lbbnc3w77c301rpx3iylyqcjr1 is destroyed.
2025-09-17T17:20:02.078+07:00  INFO 61586 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T17:20:02.079+07:00  INFO 61586 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T17:21:05.185+07:00  INFO 61586 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T17:21:12.227+07:00  INFO 61586 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-17T17:21:12.244+07:00  INFO 61586 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T17:21:42.607+07:00  INFO 61586 --- [qtp828508529-40] n.d.module.session.ClientSessionManager  : Add a client session id = node015ldodvy6qyyv1jouguuxdzxln0, token = 1a705962410ad977f64db1cd12df08a5
2025-09-17T17:21:42.620+07:00  INFO 61586 --- [qtp828508529-40] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-17T17:21:57.983+07:00  INFO 61586 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@697f8f0c{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-17T17:21:57.984+07:00  INFO 61586 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-17T17:21:57.984+07:00  INFO 61586 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-17T17:21:57.984+07:00  INFO 61586 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-17T17:21:57.984+07:00  INFO 61586 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-17T17:21:57.985+07:00  INFO 61586 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-17T17:21:57.985+07:00  INFO 61586 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-17T17:21:57.985+07:00  INFO 61586 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-17T17:21:57.985+07:00  INFO 61586 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-09-17T17:21:57.985+07:00  INFO 61586 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-09-17T17:21:57.985+07:00  INFO 61586 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-17T17:21:57.985+07:00  INFO 61586 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-09-17T17:21:57.985+07:00  INFO 61586 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-09-17T17:21:57.985+07:00  INFO 61586 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-17T17:21:57.985+07:00  INFO 61586 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-09-17T17:21:57.985+07:00  INFO 61586 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-09-17T17:21:58.001+07:00  INFO 61586 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T17:21:58.063+07:00  INFO 61586 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-17T17:21:58.068+07:00  INFO 61586 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-17T17:21:58.084+07:00  INFO 61586 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-17T17:21:58.084+07:00  INFO 61586 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-17T17:21:58.085+07:00  INFO 61586 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-17T17:21:58.085+07:00  INFO 61586 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-17T17:21:58.085+07:00  INFO 61586 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-17T17:21:58.085+07:00  INFO 61586 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-17T17:21:58.086+07:00  INFO 61586 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-17T17:21:58.086+07:00  INFO 61586 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-17T17:21:58.086+07:00  INFO 61586 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-17T17:21:58.086+07:00  INFO 61586 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-17T17:21:58.086+07:00  INFO 61586 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-17T17:21:58.086+07:00  INFO 61586 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-17T17:21:58.087+07:00  INFO 61586 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-17T17:21:58.087+07:00  INFO 61586 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@7e7f72{STOPPING}[12.0.15,sto=0]
2025-09-17T17:21:58.089+07:00  INFO 61586 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-17T17:21:58.090+07:00  INFO 61586 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@37eca4c0{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.7110507551069367228/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@5c452eb5{STOPPED}}
