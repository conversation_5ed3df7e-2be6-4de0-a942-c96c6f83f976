2025-09-19T10:44:22.784+07:00  INFO 55430 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 55430 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-19T10:44:22.785+07:00  INFO 55430 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-19T10:44:23.665+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:44:23.746+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 76 ms. Found 22 JPA repository interfaces.
2025-09-19T10:44:23.758+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:44:23.760+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-19T10:44:23.760+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:44:23.768+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 10 JPA repository interfaces.
2025-09-19T10:44:23.769+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:44:23.812+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 43 ms. Found 3 JPA repository interfaces.
2025-09-19T10:44:23.822+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:44:23.828+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 1 JPA repository interface.
2025-09-19T10:44:23.837+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:44:23.840+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 2 JPA repository interfaces.
2025-09-19T10:44:23.840+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:44:23.844+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-19T10:44:23.847+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:44:23.850+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-09-19T10:44:23.853+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:44:23.856+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-19T10:44:23.857+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:44:23.857+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-19T10:44:23.857+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:44:23.864+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 10 JPA repository interfaces.
2025-09-19T10:44:23.870+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:44:23.872+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-19T10:44:23.875+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:44:23.879+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-19T10:44:23.879+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:44:23.886+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-19T10:44:23.886+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:44:23.889+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-09-19T10:44:23.890+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:44:23.890+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-19T10:44:23.890+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:44:23.891+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-19T10:44:23.891+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:44:23.896+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-09-19T10:44:23.896+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:44:23.898+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-19T10:44:23.898+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:44:23.898+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-19T10:44:23.898+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:44:23.908+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 19 JPA repository interfaces.
2025-09-19T10:44:23.917+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:44:23.924+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 8 JPA repository interfaces.
2025-09-19T10:44:23.925+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:44:23.928+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-09-19T10:44:23.928+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:44:23.933+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-09-19T10:44:23.933+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:44:23.940+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 9 JPA repository interfaces.
2025-09-19T10:44:23.940+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:44:23.944+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-09-19T10:44:23.945+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:44:23.954+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 13 JPA repository interfaces.
2025-09-19T10:44:23.955+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:44:23.965+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 14 JPA repository interfaces.
2025-09-19T10:44:23.966+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:44:23.983+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 17 ms. Found 24 JPA repository interfaces.
2025-09-19T10:44:23.983+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:44:23.984+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-19T10:44:23.991+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:44:23.992+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-19T10:44:23.992+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:44:24.000+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-09-19T10:44:24.002+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:44:24.045+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 42 ms. Found 66 JPA repository interfaces.
2025-09-19T10:44:24.045+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:44:24.047+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-19T10:44:24.051+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T10:44:24.055+07:00  INFO 55430 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 4 JPA repository interfaces.
2025-09-19T10:44:24.284+07:00  INFO 55430 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-19T10:44:24.289+07:00  INFO 55430 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-19T10:44:24.577+07:00  WARN 55430 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-19T10:44:24.776+07:00  INFO 55430 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-19T10:44:24.778+07:00  INFO 55430 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-19T10:44:24.789+07:00  INFO 55430 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-19T10:44:24.790+07:00  INFO 55430 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1837 ms
2025-09-19T10:44:24.842+07:00  WARN 55430 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-19T10:44:24.843+07:00  INFO 55430 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-19T10:44:24.936+07:00  INFO 55430 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@28d48616
2025-09-19T10:44:24.937+07:00  INFO 55430 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-19T10:44:24.941+07:00  WARN 55430 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-19T10:44:24.941+07:00  INFO 55430 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-19T10:44:24.947+07:00  INFO 55430 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@327fd5c9
2025-09-19T10:44:24.947+07:00  INFO 55430 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-19T10:44:24.947+07:00  WARN 55430 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-19T10:44:24.947+07:00  INFO 55430 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-19T10:44:24.953+07:00  INFO 55430 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@316745d8
2025-09-19T10:44:24.954+07:00  INFO 55430 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-19T10:44:24.954+07:00  WARN 55430 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-19T10:44:24.954+07:00  INFO 55430 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-19T10:44:24.960+07:00  INFO 55430 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@122557f8
2025-09-19T10:44:24.960+07:00  INFO 55430 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-19T10:44:24.960+07:00  WARN 55430 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-19T10:44:24.960+07:00  INFO 55430 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-19T10:44:24.967+07:00  INFO 55430 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@24663d05
2025-09-19T10:44:24.967+07:00  INFO 55430 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-19T10:44:24.967+07:00  INFO 55430 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-19T10:44:25.008+07:00  INFO 55430 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-19T10:44:25.010+07:00  INFO 55430 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@4833bb83{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.3594301024522676656/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@d4594a8{STARTED}}
2025-09-19T10:44:25.010+07:00  INFO 55430 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@4833bb83{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.3594301024522676656/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@d4594a8{STARTED}}
2025-09-19T10:44:25.063+07:00  INFO 55430 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@7c943143{STARTING}[12.0.15,sto=0] @2910ms
2025-09-19T10:44:25.115+07:00  INFO 55430 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-19T10:44:25.142+07:00  INFO 55430 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-19T10:44:25.157+07:00  INFO 55430 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-19T10:44:25.281+07:00  INFO 55430 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-19T10:44:25.310+07:00  WARN 55430 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-19T10:44:25.916+07:00  INFO 55430 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-19T10:44:25.925+07:00  INFO 55430 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@3046057a] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-19T10:44:26.112+07:00  INFO 55430 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T10:44:26.304+07:00  INFO 55430 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.graphapi", "net.datatp.module.kpi", "net.datatp.module.monitor.activity", "net.datatp.module.wfms", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "cloud.datatp.module.fleet", "net.datatp.module.okr", "net.datatp.module.core.template", "cloud.datatp.gps", "cloud.datatp.module.pegasus.shipment", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "net.datatp.module.company.tmpl", "cloud.datatp.tms", "net.datatp.module.groovy", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "net.datatp.module.resource", "cloud.datatp.vendor", "net.datatp.module.hr", "net.datatp.module.company.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.chatbot", "cloud.datatp.module.odoo", "net.datatp.module.core.print", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-09-19T10:44:26.306+07:00  INFO 55430 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-19T10:44:26.312+07:00  INFO 55430 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-19T10:44:26.313+07:00  INFO 55430 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-19T10:44:26.338+07:00  INFO 55430 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-19T10:44:26.342+07:00  WARN 55430 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-19T10:44:28.518+07:00  INFO 55430 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-19T10:44:28.519+07:00  INFO 55430 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@42fb4038] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-19T10:44:28.694+07:00  WARN 55430 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-19T10:44:28.694+07:00  WARN 55430 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-19T10:44:28.700+07:00  WARN 55430 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-19T10:44:28.700+07:00  WARN 55430 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-19T10:44:28.715+07:00  WARN 55430 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-19T10:44:28.715+07:00  WARN 55430 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-19T10:44:29.170+07:00  INFO 55430 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T10:44:29.176+07:00  INFO 55430 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-19T10:44:29.177+07:00  INFO 55430 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-19T10:44:29.198+07:00  INFO 55430 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-19T10:44:29.201+07:00  WARN 55430 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-19T10:44:29.715+07:00  INFO 55430 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-19T10:44:29.715+07:00  INFO 55430 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@4cef7cec] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-19T10:44:29.766+07:00  WARN 55430 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-19T10:44:29.766+07:00  WARN 55430 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-19T10:44:30.093+07:00  INFO 55430 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T10:44:30.122+07:00  INFO 55430 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-19T10:44:30.126+07:00  INFO 55430 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-19T10:44:30.127+07:00  INFO 55430 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T10:44:30.134+07:00  WARN 55430 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-19T10:44:30.262+07:00  INFO 55430 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-19T10:44:30.715+07:00  INFO 55430 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-19T10:44:30.718+07:00  INFO 55430 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-19T10:44:30.752+07:00  INFO 55430 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-19T10:44:30.794+07:00  INFO 55430 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-19T10:44:30.848+07:00  INFO 55430 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-19T10:44:30.875+07:00  INFO 55430 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-19T10:44:30.905+07:00  INFO 55430 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 320736543ms : this is harmless.
2025-09-19T10:44:30.913+07:00  INFO 55430 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-19T10:44:30.916+07:00  INFO 55430 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-19T10:44:30.933+07:00  INFO 55430 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 612242498ms : this is harmless.
2025-09-19T10:44:30.935+07:00  INFO 55430 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-19T10:44:30.957+07:00  INFO 55430 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-19T10:44:30.958+07:00  INFO 55430 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-19T10:44:32.818+07:00  INFO 55430 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-19T10:44:32.818+07:00  INFO 55430 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T10:44:32.818+07:00  WARN 55430 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-19T10:44:33.138+07:00  INFO 55430 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 1 messages for session 19/09/2025@10:30:00+0700 to 19/09/2025@10:45:00+0700
2025-09-19T10:44:33.138+07:00  INFO 55430 --- [main] c.d.f.core.message.MessageQueueManager   : 📤 message : MAIL - bd-annual-conference-notification - scheduled at 19/09/2025@10:34:01+0700
2025-09-19T10:44:33.139+07:00  INFO 55430 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 1 messages for session 19/09/2025@10:30:00+0700 to 19/09/2025@10:45:00+0700
2025-09-19T10:44:33.744+07:00  INFO 55430 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-09-19T10:44:33.744+07:00  INFO 55430 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T10:44:33.745+07:00  WARN 55430 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-19T10:44:34.142+07:00  INFO 55430 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-09-19T10:44:34.143+07:00  INFO 55430 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-09-19T10:44:34.143+07:00  INFO 55430 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-09-19T10:44:34.143+07:00  INFO 55430 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-09-19T10:44:34.143+07:00  INFO 55430 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-09-19T10:44:36.789+07:00  WARN 55430 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 800d025e-cffe-4941-8f6f-d23fcfc5142a

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-19T10:44:36.793+07:00  INFO 55430 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-09-19T10:44:37.103+07:00  INFO 55430 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-19T10:44:37.103+07:00  INFO 55430 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-19T10:44:37.103+07:00  INFO 55430 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-19T10:44:37.103+07:00  INFO 55430 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-19T10:44:37.103+07:00  INFO 55430 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-09-19T10:44:37.103+07:00  INFO 55430 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-09-19T10:44:37.103+07:00  INFO 55430 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-19T10:44:37.103+07:00  INFO 55430 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-09-19T10:44:37.103+07:00  INFO 55430 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-09-19T10:44:37.103+07:00  INFO 55430 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-19T10:44:37.103+07:00  INFO 55430 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-09-19T10:44:37.103+07:00  INFO 55430 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-09-19T10:44:37.107+07:00  INFO 55430 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-19T10:44:37.107+07:00  INFO 55430 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-19T10:44:37.114+07:00  INFO 55430 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-19T10:44:37.212+07:00  INFO 55430 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-19T10:44:37.212+07:00  INFO 55430 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-19T10:44:37.214+07:00  INFO 55430 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-09-19T10:44:37.221+07:00  INFO 55430 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@9b1ec17{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-19T10:44:37.221+07:00  INFO 55430 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-09-19T10:44:37.222+07:00  INFO 55430 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-09-19T10:44:37.259+07:00  INFO 55430 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-09-19T10:44:37.259+07:00  INFO 55430 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-09-19T10:44:37.266+07:00  INFO 55430 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 14.858 seconds (process running for 15.113)
2025-09-19T10:45:05.173+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T10:45:05.177+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-19T10:45:05.179+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-19T10:45:05.181+07:00  INFO 55430 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 19/09/2025@10:45:05+0700
2025-09-19T10:45:05.189+07:00  INFO 55430 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 19/09/2025@10:45:00+0700 to 19/09/2025@11:00:00+0700
2025-09-19T10:45:05.189+07:00  INFO 55430 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 19/09/2025@10:45:00+0700 to 19/09/2025@11:00:00+0700
2025-09-19T10:45:14.839+07:00  INFO 55430 --- [qtp1*********-37] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node018p00inwwa6y71fo8akze71zfc0
2025-09-19T10:45:15.167+07:00  INFO 55430 --- [qtp1*********-37] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = a08ffe20f95eea3797243a4da3e47a27
2025-09-19T10:45:15.237+07:00  INFO 55430 --- [qtp1*********-37] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-09-19T10:45:19.498+07:00  INFO 55430 --- [qtp1*********-40] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le logout successfully 
2025-09-19T10:45:22.133+07:00  INFO 55430 --- [qtp1*********-62] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:45:22.142+07:00  INFO 55430 --- [qtp1*********-62] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:45:32.320+07:00  INFO 55430 --- [qtp1*********-36] c.d.f.core.integration.BFSOneApi         : Starting authentication process for user: JESSE.VNHPH
2025-09-19T10:45:32.626+07:00  INFO 55430 --- [qtp1*********-36] c.d.f.core.partner.PartnerRequestLogic   : ------------------------Partner---------------------------

2025-09-19T10:45:32.627+07:00  INFO 55430 --- [qtp1*********-36] c.d.f.core.partner.PartnerRequestLogic   : --------------------------------------------------------

2025-09-19T10:45:32.642+07:00  INFO 55430 --- [qtp1*********-36] c.d.f.core.message.CRMMessageLogic       : 📅 Scheduled message [id=22096] [company=bee-tw] type=MAIL for 19/09/2025@10:45:32+0700
2025-09-19T10:45:32.642+07:00  INFO 55430 --- [qtp1*********-36] c.d.f.core.message.MessageQueueManager   : Added message [22096] - scheduled at 19/09/2025@10:45:32+0700 - current session (19/09/2025@10:45:00+0700 to 19/09/2025@11:00:00+0700)

2025-09-19T10:45:39.628+07:00  INFO 55430 --- [qtp1*********-76] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T10:45:39.629+07:00  INFO 55430 --- [qtp1*********-64] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T10:45:39.646+07:00  INFO 55430 --- [qtp1*********-76] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-19T10:45:39.646+07:00  INFO 55430 --- [qtp1*********-64] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-19T10:45:40.283+07:00  INFO 55430 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 11, expire count 0
2025-09-19T10:45:40.292+07:00  INFO 55430 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T10:46:06.326+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T10:47:04.481+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T10:47:39.597+07:00  INFO 55430 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 12, expire count 0
2025-09-19T10:47:39.613+07:00  INFO 55430 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-19T10:48:01.980+07:00  INFO 55430 --- [qtp1*********-76] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:48:01.981+07:00  INFO 55430 --- [qtp1*********-63] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:48:01.990+07:00  INFO 55430 --- [qtp1*********-63] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:48:01.990+07:00  INFO 55430 --- [qtp1*********-76] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:48:06.662+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T10:48:23.984+07:00  INFO 55430 --- [qtp1*********-39] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:48:23.985+07:00  INFO 55430 --- [qtp1*********-36] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:48:24.005+07:00  INFO 55430 --- [qtp1*********-39] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:48:24.005+07:00  INFO 55430 --- [qtp1*********-36] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:48:30.945+07:00  INFO 55430 --- [qtp1*********-36] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:48:30.988+07:00  INFO 55430 --- [qtp1*********-36] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:48:31.005+07:00  INFO 55430 --- [qtp1*********-40] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:48:31.014+07:00  INFO 55430 --- [qtp1*********-40] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:48:42.008+07:00  INFO 55430 --- [qtp1*********-63] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T10:48:42.018+07:00  INFO 55430 --- [qtp1*********-64] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T10:48:42.027+07:00  INFO 55430 --- [qtp1*********-63] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-19T10:48:42.029+07:00  INFO 55430 --- [qtp1*********-64] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-19T10:49:03.766+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T10:49:42.995+07:00  INFO 55430 --- [qtp1*********-39] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:49:43.011+07:00  INFO 55430 --- [qtp1*********-39] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:49:43.019+07:00  INFO 55430 --- [qtp1*********-64] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:49:43.038+07:00  INFO 55430 --- [qtp1*********-64] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:49:43.854+07:00  INFO 55430 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 9, expire count 0
2025-09-19T10:49:43.862+07:00  INFO 55430 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T10:50:06.913+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T10:50:06.919+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-19T10:50:54.226+07:00  INFO 55430 --- [qtp1*********-39] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:50:54.233+07:00  INFO 55430 --- [qtp1*********-39] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:50:54.289+07:00  INFO 55430 --- [qtp1*********-71] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:50:54.324+07:00  INFO 55430 --- [qtp1*********-71] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:50:54.373+07:00 ERROR 55430 --- [qtp1*********-67] n.d.m.monitor.call.EndpointCallContext   : Start call with component EntityTaskService, method searchEntityTasks, arguments
[ {
  "tenantId" : "default",
  "companyId" : 56,
  "companyParentId" : 4,
  "companyCode" : "bee-tw",
  "companyLabel" : "BEE TW",
  "companyFullName" : "BEE TAIWAN",
  "loginId" : "dan",
  "accountId" : 3,
  "token" : "096a856d617dd1d2b9eb95ee56abcf26",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node018p00inwwa6y71fo8akze71zfc0",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : {
    "bee-tw" : {
      "companyId" : 56,
      "companyParentId" : 4,
      "companyCode" : "bee-tw",
      "companyLabel" : "BEE TW",
      "companyFullName" : null
    },
    "bee" : {
      "companyId" : 4,
      "companyParentId" : 0,
      "companyCode" : "bee",
      "companyLabel" : "Bee Corp",
      "companyFullName" : null
    },
    "thudo" : {
      "companyId" : 53,
      "companyParentId" : 0,
      "companyCode" : "thudo",
      "companyLabel" : "THUDO",
      "companyFullName" : null
    },
    "datatp" : {
      "companyId" : 10,
      "companyParentId" : 1,
      "companyCode" : "datatp",
      "companyLabel" : "DataTP Cloud",
      "companyFullName" : null
    },
    "beehan" : {
      "companyId" : 16,
      "companyParentId" : 4,
      "companyCode" : "beehan",
      "companyLabel" : "Bee HAN",
      "companyFullName" : null
    },
    "beescs" : {
      "companyId" : 12,
      "companyParentId" : 1,
      "companyCode" : "beescs",
      "companyLabel" : "BEESCS",
      "companyFullName" : null
    },
    "beedad" : {
      "companyId" : 18,
      "companyParentId" : 4,
      "companyCode" : "beedad",
      "companyLabel" : "Bee DAD",
      "companyFullName" : null
    },
    "hps" : {
      "companyId" : 51,
      "companyParentId" : 0,
      "companyCode" : "hps",
      "companyLabel" : "HPS",
      "companyFullName" : null
    },
    "marine" : {
      "companyId" : 52,
      "companyParentId" : 0,
      "companyCode" : "marine",
      "companyLabel" : "MARINE",
      "companyFullName" : null
    },
    "bonds" : {
      "companyId" : 54,
      "companyParentId" : 4,
      "companyCode" : "bonds",
      "companyLabel" : "BONDS",
      "companyFullName" : null
    },
    "tiendat" : {
      "companyId" : 55,
      "companyParentId" : 4,
      "companyCode" : "tiendat",
      "companyLabel" : "TIENDAT",
      "companyFullName" : null
    },
    "beehcm" : {
      "companyId" : 17,
      "companyParentId" : 4,
      "companyCode" : "beehcm",
      "companyLabel" : "Bee HCM",
      "companyFullName" : null
    },
    "beehph" : {
      "companyId" : 8,
      "companyParentId" : 4,
      "companyCode" : "beehph",
      "companyLabel" : "Bee HPH",
      "companyFullName" : null
    }
  },
  "featurePermissions" : [ {
    "id" : 15014,
    "appId" : 9,
    "appModule" : "company",
    "appName" : "company-hr",
    "companyId" : 56,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 14978,
    "appId" : 15,
    "appModule" : "logistics",
    "appName" : "user-logistics-prices",
    "companyId" : 56,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 14977,
    "appId" : 16,
    "appModule" : "logistics",
    "appName" : "user-logistics-sales",
    "companyId" : 56,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 14972,
    "appId" : 80,
    "appModule" : "logistics",
    "appName" : "company-logistics-sales",
    "companyId" : 56,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 14973,
    "appId" : 81,
    "appModule" : "logistics",
    "appName" : "company-logistics-prices",
    "companyId" : 56,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 14974,
    "appId" : 5,
    "appModule" : "admin",
    "appName" : "admin-space",
    "companyId" : 56,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 14975,
    "appId" : 6,
    "appModule" : "company",
    "appName" : "company-space",
    "companyId" : 56,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 14976,
    "appId" : 2,
    "appModule" : "user",
    "appName" : "my-employee-space",
    "companyId" : 56,
    "loginId" : "dan",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "All",
    "accessType" : "Employee",
    "capability" : "Admin"
  } ],
  "attributes" : { },
  "clientId" : "default:dan"
}, null, {
  "params" : {
    "accountId" : 3
  },
  "filters" : [ {
    "name" : "search",
    "filterType" : "String",
    "filterValue" : "",
    "required" : true
  } ],
  "optionFilters" : [ {
    "name" : "storageState",
    "filterType" : "NotSet",
    "required" : true,
    "multiple" : true,
    "options" : [ "ACTIVE", "ARCHIVED" ],
    "selectOptions" : [ "ACTIVE" ]
  } ],
  "rangeFilters" : [ {
    "name" : "createdTime",
    "filterType" : "NotSet",
    "required" : true
  }, {
    "name" : "modifiedTime",
    "filterType" : "NotSet",
    "required" : true
  } ],
  "orderBy" : {
    "fields" : [ "modifiedTime" ],
    "selectFields" : [ ],
    "sort" : "DESC"
  },
  "maxReturn" : 5000
} ]
2025-09-19T10:50:54.374+07:00 ERROR 55430 --- [qtp1*********-67] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:115)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Cannot invoke "net.datatp.module.data.db.entity.ICompany.getId()" because "company" is null
	at net.datatp.module.wfms.EntityTaskService.searchEntityTasks(EntityTaskService.java:34)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.wfms.EntityTaskService$$SpringCGLIB$$0.searchEntityTasks(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 93 common frames omitted

2025-09-19T10:50:54.386+07:00  INFO 55430 --- [qtp1*********-67] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint EntityTaskService/searchEntityTasks
2025-09-19T10:50:58.466+07:00  INFO 55430 --- [qtp1*********-71] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:50:58.472+07:00  INFO 55430 --- [qtp1*********-71] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:50:58.505+07:00  INFO 55430 --- [qtp1*********-68] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:50:58.513+07:00 ERROR 55430 --- [qtp1*********-76] n.d.m.monitor.call.EndpointCallContext   : Start call with component EntityTaskService, method searchEntityTasks, arguments
[ {
  "tenantId" : "default",
  "companyId" : null,
  "companyParentId" : null,
  "companyCode" : null,
  "companyLabel" : null,
  "companyFullName" : null,
  "loginId" : "dan",
  "accountId" : 3,
  "token" : "096a856d617dd1d2b9eb95ee56abcf26",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node018p00inwwa6y71fo8akze71zfc0",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : null,
  "featurePermissions" : null,
  "attributes" : { },
  "clientId" : "default:dan"
}, null, {
  "params" : {
    "accountId" : 3
  },
  "filters" : [ {
    "name" : "search",
    "filterType" : "String",
    "filterValue" : "",
    "required" : true
  } ],
  "optionFilters" : [ {
    "name" : "storageState",
    "filterType" : "NotSet",
    "required" : true,
    "multiple" : true,
    "options" : [ "ACTIVE", "ARCHIVED" ],
    "selectOptions" : [ "ACTIVE" ]
  } ],
  "rangeFilters" : [ {
    "name" : "createdTime",
    "filterType" : "NotSet",
    "required" : true
  }, {
    "name" : "modifiedTime",
    "filterType" : "NotSet",
    "required" : true
  } ],
  "orderBy" : {
    "fields" : [ "modifiedTime" ],
    "selectFields" : [ ],
    "sort" : "DESC"
  },
  "maxReturn" : 5000
} ]
2025-09-19T10:50:58.514+07:00 ERROR 55430 --- [qtp1*********-71] n.d.m.monitor.call.EndpointCallContext   : Start call with component EntityTaskService, method searchEntityTasks, arguments
[ {
  "tenantId" : "default",
  "companyId" : null,
  "companyParentId" : null,
  "companyCode" : null,
  "companyLabel" : null,
  "companyFullName" : null,
  "loginId" : "dan",
  "accountId" : 3,
  "token" : "096a856d617dd1d2b9eb95ee56abcf26",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node018p00inwwa6y71fo8akze71zfc0",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : {
    "bee-tw" : {
      "companyId" : 56,
      "companyParentId" : 4,
      "companyCode" : "bee-tw",
      "companyLabel" : "BEE TW",
      "companyFullName" : null
    },
    "bee" : {
      "companyId" : 4,
      "companyParentId" : 0,
      "companyCode" : "bee",
      "companyLabel" : "Bee Corp",
      "companyFullName" : null
    },
    "thudo" : {
      "companyId" : 53,
      "companyParentId" : 0,
      "companyCode" : "thudo",
      "companyLabel" : "THUDO",
      "companyFullName" : null
    },
    "datatp" : {
      "companyId" : 10,
      "companyParentId" : 1,
      "companyCode" : "datatp",
      "companyLabel" : "DataTP Cloud",
      "companyFullName" : null
    },
    "beehan" : {
      "companyId" : 16,
      "companyParentId" : 4,
      "companyCode" : "beehan",
      "companyLabel" : "Bee HAN",
      "companyFullName" : null
    },
    "beescs" : {
      "companyId" : 12,
      "companyParentId" : 1,
      "companyCode" : "beescs",
      "companyLabel" : "BEESCS",
      "companyFullName" : null
    },
    "beedad" : {
      "companyId" : 18,
      "companyParentId" : 4,
      "companyCode" : "beedad",
      "companyLabel" : "Bee DAD",
      "companyFullName" : null
    },
    "hps" : {
      "companyId" : 51,
      "companyParentId" : 0,
      "companyCode" : "hps",
      "companyLabel" : "HPS",
      "companyFullName" : null
    },
    "marine" : {
      "companyId" : 52,
      "companyParentId" : 0,
      "companyCode" : "marine",
      "companyLabel" : "MARINE",
      "companyFullName" : null
    },
    "bonds" : {
      "companyId" : 54,
      "companyParentId" : 4,
      "companyCode" : "bonds",
      "companyLabel" : "BONDS",
      "companyFullName" : null
    },
    "tiendat" : {
      "companyId" : 55,
      "companyParentId" : 4,
      "companyCode" : "tiendat",
      "companyLabel" : "TIENDAT",
      "companyFullName" : null
    },
    "beehcm" : {
      "companyId" : 17,
      "companyParentId" : 4,
      "companyCode" : "beehcm",
      "companyLabel" : "Bee HCM",
      "companyFullName" : null
    },
    "beehph" : {
      "companyId" : 8,
      "companyParentId" : 4,
      "companyCode" : "beehph",
      "companyLabel" : "Bee HPH",
      "companyFullName" : null
    }
  },
  "featurePermissions" : null,
  "attributes" : { },
  "clientId" : "default:dan"
}, null, {
  "params" : {
    "accountId" : 3
  },
  "filters" : [ {
    "name" : "search",
    "filterType" : "String",
    "filterValue" : "",
    "required" : true
  } ],
  "optionFilters" : [ {
    "name" : "storageState",
    "filterType" : "NotSet",
    "required" : true,
    "multiple" : true,
    "options" : [ "ACTIVE", "ARCHIVED" ],
    "selectOptions" : [ "ACTIVE" ]
  } ],
  "rangeFilters" : [ {
    "name" : "createdTime",
    "filterType" : "NotSet",
    "required" : true
  }, {
    "name" : "modifiedTime",
    "filterType" : "NotSet",
    "required" : true
  } ],
  "orderBy" : {
    "fields" : [ "modifiedTime" ],
    "selectFields" : [ ],
    "sort" : "DESC"
  },
  "maxReturn" : 5000
} ]
2025-09-19T10:50:58.514+07:00 ERROR 55430 --- [qtp1*********-76] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:115)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Cannot invoke "net.datatp.module.data.db.entity.ICompany.getId()" because "company" is null
	at net.datatp.module.wfms.EntityTaskService.searchEntityTasks(EntityTaskService.java:34)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.wfms.EntityTaskService$$SpringCGLIB$$0.searchEntityTasks(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted

2025-09-19T10:50:58.518+07:00  INFO 55430 --- [qtp1*********-76] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint EntityTaskService/searchEntityTasks
2025-09-19T10:50:58.523+07:00  INFO 55430 --- [qtp1*********-68] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:50:58.514+07:00 ERROR 55430 --- [qtp1*********-71] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:115)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Cannot invoke "net.datatp.module.data.db.entity.ICompany.getId()" because "company" is null
	at net.datatp.module.wfms.EntityTaskService.searchEntityTasks(EntityTaskService.java:34)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.wfms.EntityTaskService$$SpringCGLIB$$0.searchEntityTasks(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted

2025-09-19T10:50:58.524+07:00  INFO 55430 --- [qtp1*********-71] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint EntityTaskService/searchEntityTasks
2025-09-19T10:51:00.974+07:00  INFO 55430 --- [qtp1*********-39] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:51:00.988+07:00  INFO 55430 --- [qtp1*********-39] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:51:01.006+07:00  INFO 55430 --- [qtp1*********-41] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:51:01.011+07:00  INFO 55430 --- [qtp1*********-41] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:51:03.646+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T10:51:04.687+07:00  INFO 55430 --- [qtp1*********-70] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T10:51:04.687+07:00  INFO 55430 --- [qtp1*********-36] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T10:51:04.700+07:00  INFO 55430 --- [qtp1*********-70] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-19T10:51:04.700+07:00  INFO 55430 --- [qtp1*********-36] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-19T10:51:19.025+07:00  INFO 55430 --- [qtp1*********-37] c.d.f.core.message.CRMMessageLogic       : 📅 Scheduled message [id=22097] [company=bee-tw] type=MAIL for 19/09/2025@10:51:19+0700
2025-09-19T10:51:19.026+07:00  INFO 55430 --- [qtp1*********-37] c.d.f.core.message.MessageQueueManager   : Added message [22097] - scheduled at 19/09/2025@10:51:19+0700 - current session (19/09/2025@10:45:00+0700 to 19/09/2025@11:00:00+0700)

2025-09-19T10:51:23.640+07:00  INFO 55430 --- [qtp1*********-34] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T10:51:23.644+07:00  INFO 55430 --- [qtp1*********-34] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-19T10:51:23.645+07:00  INFO 55430 --- [qtp1*********-37] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T10:51:23.648+07:00  INFO 55430 --- [qtp1*********-37] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-19T10:51:43.746+07:00  INFO 55430 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 0
2025-09-19T10:51:43.757+07:00  INFO 55430 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 2
2025-09-19T10:52:06.803+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T10:52:08.327+07:00  INFO 55430 --- [qtp1*********-76] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:52:08.327+07:00  INFO 55430 --- [qtp1*********-63] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:52:08.340+07:00  INFO 55430 --- [qtp1*********-76] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:52:08.340+07:00  INFO 55430 --- [qtp1*********-63] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:52:21.977+07:00  INFO 55430 --- [qtp1*********-63] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:52:21.977+07:00  INFO 55430 --- [qtp1*********-41] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:52:21.989+07:00  INFO 55430 --- [qtp1*********-63] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:52:21.989+07:00  INFO 55430 --- [qtp1*********-41] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:53:02.917+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T10:53:44.013+07:00  INFO 55430 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 12, expire count 0
2025-09-19T10:53:44.026+07:00  INFO 55430 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-19T10:54:06.067+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T10:54:12.502+07:00  INFO 55430 --- [qtp1*********-41] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:54:12.509+07:00  INFO 55430 --- [qtp1*********-41] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:54:12.543+07:00  INFO 55430 --- [qtp1*********-85] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:54:12.570+07:00  INFO 55430 --- [qtp1*********-85] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:54:36.418+07:00  INFO 55430 --- [qtp1*********-36] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:54:36.419+07:00  INFO 55430 --- [qtp1*********-39] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:54:36.431+07:00  INFO 55430 --- [qtp1*********-36] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:54:36.431+07:00  INFO 55430 --- [qtp1*********-39] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:54:51.208+07:00  INFO 55430 --- [qtp1*********-68] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:54:51.209+07:00  INFO 55430 --- [qtp1*********-85] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:54:51.222+07:00  INFO 55430 --- [qtp1*********-85] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:54:51.222+07:00  INFO 55430 --- [qtp1*********-68] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:55:02.165+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T10:55:02.168+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-19T10:55:04.214+07:00  INFO 55430 --- [qtp1*********-39] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:55:04.220+07:00  INFO 55430 --- [qtp1*********-39] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:55:04.240+07:00  INFO 55430 --- [qtp1*********-69] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:55:04.250+07:00  INFO 55430 --- [qtp1*********-69] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:55:15.705+07:00  INFO 55430 --- [qtp1*********-63] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:55:15.707+07:00  INFO 55430 --- [qtp1*********-68] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:55:15.719+07:00  INFO 55430 --- [qtp1*********-68] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:55:15.719+07:00  INFO 55430 --- [qtp1*********-63] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:55:27.598+07:00  INFO 55430 --- [qtp1*********-64] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:55:27.598+07:00  INFO 55430 --- [qtp1*********-69] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:55:27.604+07:00  INFO 55430 --- [qtp1*********-64] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:55:27.606+07:00  INFO 55430 --- [qtp1*********-69] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:55:44.295+07:00  INFO 55430 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 11, expire count 10
2025-09-19T10:55:44.312+07:00  INFO 55430 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-19T10:55:54.678+07:00  INFO 55430 --- [qtp1*********-68] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T10:55:54.678+07:00  INFO 55430 --- [qtp1*********-85] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T10:55:55.242+07:00  INFO 55430 --- [qtp1*********-86] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T10:55:55.243+07:00  INFO 55430 --- [qtp1*********-63] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T10:55:55.659+07:00  INFO 55430 --- [qtp1*********-68] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T10:55:55.660+07:00  INFO 55430 --- [qtp1*********-85] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T10:56:05.357+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T10:56:10.717+07:00  INFO 55430 --- [qtp1*********-85] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:56:10.727+07:00  INFO 55430 --- [qtp1*********-85] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:56:10.740+07:00  INFO 55430 --- [qtp1*********-82] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:56:10.747+07:00  INFO 55430 --- [qtp1*********-82] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:56:22.281+07:00  INFO 55430 --- [qtp1*********-36] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T10:56:22.281+07:00  INFO 55430 --- [qtp1*********-71] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T10:56:22.554+07:00  INFO 55430 --- [qtp1*********-36] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T10:56:22.603+07:00  INFO 55430 --- [qtp1*********-71] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T10:56:23.029+07:00  INFO 55430 --- [qtp1*********-76] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T10:56:23.030+07:00  INFO 55430 --- [qtp1*********-63] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T10:57:06.441+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T10:57:34.680+07:00  INFO 55430 --- [qtp1*********-36] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:57:34.683+07:00  INFO 55430 --- [qtp1*********-69] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:57:34.694+07:00  INFO 55430 --- [qtp1*********-36] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:57:34.701+07:00  INFO 55430 --- [qtp1*********-69] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:57:43.541+07:00  INFO 55430 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 1
2025-09-19T10:57:43.560+07:00  INFO 55430 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T10:57:49.321+07:00  INFO 55430 --- [qtp1*********-85] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:57:49.335+07:00  INFO 55430 --- [qtp1*********-76] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:57:49.367+07:00  INFO 55430 --- [qtp1*********-85] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:57:49.368+07:00  INFO 55430 --- [qtp1*********-76] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:57:52.986+07:00  INFO 55430 --- [qtp1*********-76] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T10:57:52.986+07:00  INFO 55430 --- [qtp1*********-63] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T10:58:02.677+07:00  INFO 55430 --- [qtp1*********-85] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:58:02.678+07:00  INFO 55430 --- [qtp1*********-68] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:58:02.687+07:00  INFO 55430 --- [qtp1*********-85] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:58:02.687+07:00  INFO 55430 --- [qtp1*********-68] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:58:04.638+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T10:58:06.877+07:00  INFO 55430 --- [qtp1*********-69] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T10:58:06.877+07:00  INFO 55430 --- [qtp1*********-76] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T10:58:37.102+07:00  INFO 55430 --- [qtp1*********-69] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:58:37.103+07:00  INFO 55430 --- [qtp1*********-71] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:58:37.113+07:00  INFO 55430 --- [qtp1*********-71] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:58:37.116+07:00  INFO 55430 --- [qtp1*********-69] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:58:46.345+07:00  INFO 55430 --- [qtp1*********-69] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:58:46.347+07:00  INFO 55430 --- [qtp1*********-36] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:58:46.356+07:00  INFO 55430 --- [qtp1*********-69] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:58:46.356+07:00  INFO 55430 --- [qtp1*********-36] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:58:52.802+07:00  INFO 55430 --- [qtp1*********-85] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T10:58:52.802+07:00  INFO 55430 --- [qtp1*********-68] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T10:58:55.836+07:00  INFO 55430 --- [qtp1*********-73] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T10:58:55.836+07:00  INFO 55430 --- [qtp1*********-86] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T10:59:06.726+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T10:59:12.044+07:00  INFO 55430 --- [qtp1*********-76] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:59:12.046+07:00  INFO 55430 --- [qtp1*********-82] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:59:12.066+07:00  INFO 55430 --- [qtp1*********-76] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:59:12.066+07:00  INFO 55430 --- [qtp1*********-82] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:59:25.616+07:00  INFO 55430 --- [qtp1*********-36] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:59:25.619+07:00  INFO 55430 --- [qtp1*********-85] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:59:25.630+07:00  INFO 55430 --- [qtp1*********-36] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:59:25.631+07:00  INFO 55430 --- [qtp1*********-85] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:59:34.001+07:00  INFO 55430 --- [qtp1*********-71] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:59:34.031+07:00  INFO 55430 --- [qtp1*********-73] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T10:59:34.033+07:00  INFO 55430 --- [qtp1*********-71] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:59:34.039+07:00  INFO 55430 --- [qtp1*********-73] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T10:59:42.939+07:00  INFO 55430 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 8, expire count 0
2025-09-19T10:59:42.956+07:00  INFO 55430 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T11:00:03.994+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-19T11:00:03.996+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-19T11:00:03.998+07:00  INFO 55430 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 19/09/2025@11:00:03+0700
2025-09-19T11:00:04.015+07:00  INFO 55430 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 19/09/2025@11:00:00+0700 to 19/09/2025@11:15:00+0700
2025-09-19T11:00:04.016+07:00  INFO 55430 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 19/09/2025@11:00:00+0700 to 19/09/2025@11:15:00+0700
2025-09-19T11:00:04.020+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-09-19T11:00:04.021+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T11:01:06.121+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T11:01:11.317+07:00  INFO 55430 --- [qtp1*********-36] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T11:01:11.318+07:00  INFO 55430 --- [qtp1*********-76] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T11:01:11.325+07:00  INFO 55430 --- [qtp1*********-76] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T11:01:11.325+07:00  INFO 55430 --- [qtp1*********-36] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T11:01:15.712+07:00  INFO 55430 --- [qtp1*********-68] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T11:01:15.712+07:00  INFO 55430 --- [qtp1*********-76] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T11:01:17.284+07:00  INFO 55430 --- [qtp1*********-76] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T11:01:17.284+07:00  INFO 55430 --- [qtp1*********-68] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T11:01:18.106+07:00  INFO 55430 --- [qtp1*********-76] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T11:01:18.106+07:00  INFO 55430 --- [qtp1*********-68] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T11:01:29.513+07:00  INFO 55430 --- [qtp1*********-36] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T11:01:29.514+07:00  INFO 55430 --- [qtp1*********-68] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T11:01:29.518+07:00  INFO 55430 --- [qtp1*********-68] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T11:01:29.519+07:00  INFO 55430 --- [qtp1*********-36] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T11:01:42.180+07:00  INFO 55430 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 10
2025-09-19T11:01:42.193+07:00  INFO 55430 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T11:01:43.082+07:00  INFO 55430 --- [qtp1*********-36] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T11:01:43.082+07:00  INFO 55430 --- [qtp1*********-63] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T11:02:03.228+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T11:02:59.191+07:00  INFO 55430 --- [qtp1*********-73] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T11:02:59.193+07:00  INFO 55430 --- [qtp1*********-63] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T11:02:59.202+07:00  INFO 55430 --- [qtp1*********-73] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T11:02:59.202+07:00  INFO 55430 --- [qtp1*********-63] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T11:03:05.649+07:00  INFO 55430 --- [qtp1*********-63] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T11:03:05.656+07:00  INFO 55430 --- [qtp1*********-63] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T11:03:05.689+07:00  INFO 55430 --- [qtp1*********-121] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T11:03:05.698+07:00  INFO 55430 --- [qtp1*********-121] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T11:03:06.316+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T11:03:41.395+07:00  INFO 55430 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 9, expire count 5
2025-09-19T11:03:41.407+07:00  INFO 55430 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T11:03:57.395+07:00  INFO 55430 --- [qtp1*********-69] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T11:03:57.405+07:00  INFO 55430 --- [qtp1*********-69] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T11:03:57.420+07:00  INFO 55430 --- [qtp1*********-74] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T11:03:57.430+07:00  INFO 55430 --- [qtp1*********-74] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T11:04:01.987+07:00  INFO 55430 --- [qtp1*********-85] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T11:04:01.988+07:00  INFO 55430 --- [qtp1*********-40] n.d.module.session.ClientSessionManager  : Add a client session id = node018p00inwwa6y71fo8akze71zfc0, token = 096a856d617dd1d2b9eb95ee56abcf26
2025-09-19T11:04:01.992+07:00  INFO 55430 --- [qtp1*********-85] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T11:04:01.992+07:00  INFO 55430 --- [qtp1*********-40] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-09-19T11:04:02.450+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T11:04:38.216+07:00  INFO 55430 --- [qtp1*********-40] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T11:04:38.216+07:00  INFO 55430 --- [qtp1*********-63] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T11:04:38.365+07:00  INFO 55430 --- [qtp1*********-63] c.d.f.s.report.PerformanceReportLogic    : Retrieved 17 records
2025-09-19T11:04:38.365+07:00  INFO 55430 --- [qtp1*********-40] c.d.f.s.report.PerformanceReportLogic    : Retrieved 17 records
2025-09-19T11:04:38.383+07:00  INFO 55430 --- [qtp1*********-39] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T11:04:38.383+07:00  INFO 55430 --- [qtp1*********-120] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T11:04:38.851+07:00  INFO 55430 --- [qtp1*********-39] c.d.f.s.report.PerformanceReportLogic    : Retrieved 21 records
2025-09-19T11:04:38.851+07:00  INFO 55430 --- [qtp1*********-120] c.d.f.s.report.PerformanceReportLogic    : Retrieved 21 records
2025-09-19T11:05:05.543+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T11:05:05.547+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-19T11:05:40.635+07:00  INFO 55430 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 7
2025-09-19T11:05:40.648+07:00  INFO 55430 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T11:06:06.679+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T11:07:04.769+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T11:07:39.870+07:00  INFO 55430 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 3
2025-09-19T11:07:39.885+07:00  INFO 55430 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T11:08:06.917+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T11:09:04.022+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T11:09:44.099+07:00  INFO 55430 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-19T11:09:44.109+07:00  INFO 55430 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T11:10:06.148+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T11:10:06.150+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-19T11:11:03.250+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T11:11:43.314+07:00  INFO 55430 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 7
2025-09-19T11:11:43.321+07:00  INFO 55430 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T11:12:06.355+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T11:13:02.457+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T11:13:43.553+07:00  INFO 55430 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 4
2025-09-19T11:13:43.565+07:00  INFO 55430 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T11:14:05.602+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T11:15:06.708+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T11:15:06.713+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-19T11:15:06.713+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-19T11:15:06.718+07:00  INFO 55430 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 19/09/2025@11:15:06+0700
2025-09-19T11:15:06.749+07:00  INFO 55430 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 19/09/2025@11:15:00+0700 to 19/09/2025@11:30:00+0700
2025-09-19T11:15:06.749+07:00  INFO 55430 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 19/09/2025@11:15:00+0700 to 19/09/2025@11:30:00+0700
2025-09-19T11:15:43.837+07:00  INFO 55430 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 4
2025-09-19T11:15:43.851+07:00  INFO 55430 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T11:16:04.890+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T11:17:06.989+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T11:17:43.055+07:00  INFO 55430 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T11:17:43.060+07:00  INFO 55430 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T11:18:04.084+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T11:19:06.204+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T11:19:42.316+07:00  INFO 55430 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-19T11:19:42.325+07:00  INFO 55430 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T11:20:01.567+07:00  INFO 55430 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@9b1ec17{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-19T11:20:01.569+07:00  INFO 55430 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-19T11:20:01.570+07:00  INFO 55430 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-19T11:20:01.570+07:00  INFO 55430 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-19T11:20:01.570+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T11:20:01.571+07:00  INFO 55430 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-19T11:20:01.574+07:00  INFO 55430 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-19T11:20:01.575+07:00  INFO 55430 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-19T11:20:01.575+07:00  INFO 55430 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-19T11:20:01.575+07:00  INFO 55430 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-19T11:20:01.575+07:00  INFO 55430 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-09-19T11:20:01.575+07:00  INFO 55430 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-09-19T11:20:01.576+07:00  INFO 55430 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-19T11:20:01.576+07:00  INFO 55430 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-09-19T11:20:01.576+07:00  INFO 55430 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-09-19T11:20:01.576+07:00  INFO 55430 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-19T11:20:01.576+07:00  INFO 55430 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-09-19T11:20:01.576+07:00  INFO 55430 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-09-19T11:20:01.628+07:00  INFO 55430 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T11:20:01.785+07:00  INFO 55430 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-19T11:20:01.802+07:00  INFO 55430 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-19T11:20:01.851+07:00  INFO 55430 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T11:20:01.870+07:00  INFO 55430 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T11:20:01.901+07:00  INFO 55430 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T11:20:01.904+07:00  INFO 55430 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-19T11:20:01.907+07:00  INFO 55430 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-19T11:20:01.907+07:00  INFO 55430 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-19T11:20:01.907+07:00  INFO 55430 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-19T11:20:01.907+07:00  INFO 55430 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-19T11:20:01.907+07:00  INFO 55430 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-19T11:20:01.907+07:00  INFO 55430 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-19T11:20:01.908+07:00  INFO 55430 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-19T11:20:01.908+07:00  INFO 55430 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-19T11:20:01.908+07:00  INFO 55430 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-19T11:20:01.924+07:00  INFO 55430 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@7c943143{STOPPING}[12.0.15,sto=0]
2025-09-19T11:20:01.931+07:00  INFO 55430 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-19T11:20:01.934+07:00  INFO 55430 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@4833bb83{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.3594301024522676656/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@d4594a8{STOPPED}}
