2025-09-24T16:03:58.747+07:00  INFO 78709 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 78709 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-24T16:03:58.748+07:00  INFO 78709 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-24T16:03:59.651+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:03:59.718+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 64 ms. Found 22 JPA repository interfaces.
2025-09-24T16:03:59.728+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:03:59.729+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-24T16:03:59.729+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:03:59.775+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 45 ms. Found 10 JPA repository interfaces.
2025-09-24T16:03:59.776+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:03:59.779+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-24T16:03:59.788+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:03:59.792+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-09-24T16:03:59.801+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:03:59.804+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 2 JPA repository interfaces.
2025-09-24T16:03:59.804+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:03:59.808+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-24T16:03:59.811+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:03:59.816+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 5 JPA repository interfaces.
2025-09-24T16:03:59.820+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:03:59.823+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 3 JPA repository interfaces.
2025-09-24T16:03:59.824+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:03:59.824+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-24T16:03:59.824+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:03:59.832+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 10 JPA repository interfaces.
2025-09-24T16:03:59.837+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:03:59.839+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-24T16:03:59.842+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:03:59.846+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-24T16:03:59.846+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:03:59.854+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-09-24T16:03:59.854+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:03:59.857+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-24T16:03:59.857+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:03:59.857+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-24T16:03:59.857+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:03:59.858+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-24T16:03:59.858+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:03:59.863+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-09-24T16:03:59.863+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:03:59.864+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-24T16:03:59.864+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:03:59.865+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-24T16:03:59.865+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:03:59.875+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 19 JPA repository interfaces.
2025-09-24T16:03:59.886+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:03:59.892+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 8 JPA repository interfaces.
2025-09-24T16:03:59.892+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:03:59.895+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-09-24T16:03:59.895+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:03:59.899+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-24T16:03:59.899+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:03:59.905+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-09-24T16:03:59.906+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:03:59.909+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-24T16:03:59.910+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:03:59.918+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 13 JPA repository interfaces.
2025-09-24T16:03:59.919+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:03:59.928+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 14 JPA repository interfaces.
2025-09-24T16:03:59.929+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:03:59.945+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 16 ms. Found 24 JPA repository interfaces.
2025-09-24T16:03:59.945+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:03:59.947+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-24T16:03:59.953+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:03:59.955+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 0 JPA repository interfaces.
2025-09-24T16:03:59.955+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:03:59.962+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-09-24T16:03:59.965+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:04:00.004+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 39 ms. Found 66 JPA repository interfaces.
2025-09-24T16:04:00.004+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:04:00.006+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-24T16:04:00.012+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T16:04:00.016+07:00  INFO 78709 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-09-24T16:04:00.261+07:00  INFO 78709 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-24T16:04:00.265+07:00  INFO 78709 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-24T16:04:00.555+07:00  WARN 78709 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-24T16:04:00.762+07:00  INFO 78709 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-24T16:04:00.764+07:00  INFO 78709 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-24T16:04:00.785+07:00  INFO 78709 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-24T16:04:00.785+07:00  INFO 78709 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1781 ms
2025-09-24T16:04:00.833+07:00  WARN 78709 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-24T16:04:00.833+07:00  INFO 78709 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-24T16:04:00.930+07:00  INFO 78709 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@2d44025b
2025-09-24T16:04:00.930+07:00  INFO 78709 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-24T16:04:00.935+07:00  WARN 78709 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-24T16:04:00.935+07:00  INFO 78709 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-24T16:04:00.939+07:00  INFO 78709 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@113b39e9
2025-09-24T16:04:00.939+07:00  INFO 78709 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-24T16:04:00.940+07:00  WARN 78709 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-24T16:04:00.940+07:00  INFO 78709 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-24T16:04:00.948+07:00  INFO 78709 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@60ee6f76
2025-09-24T16:04:00.948+07:00  INFO 78709 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-24T16:04:00.948+07:00  WARN 78709 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-24T16:04:00.948+07:00  INFO 78709 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-24T16:04:00.956+07:00  INFO 78709 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@659f5f32
2025-09-24T16:04:00.956+07:00  INFO 78709 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-24T16:04:00.956+07:00  WARN 78709 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-24T16:04:00.956+07:00  INFO 78709 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-24T16:04:00.962+07:00  INFO 78709 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@816272a
2025-09-24T16:04:00.962+07:00  INFO 78709 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-24T16:04:00.962+07:00  INFO 78709 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-24T16:04:01.049+07:00  INFO 78709 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-24T16:04:01.052+07:00  INFO 78709 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@21b05a8a{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.13259238187056187266/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@605a10fd{STARTED}}
2025-09-24T16:04:01.053+07:00  INFO 78709 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@21b05a8a{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.13259238187056187266/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@605a10fd{STARTED}}
2025-09-24T16:04:01.054+07:00  INFO 78709 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@39000894{STARTING}[12.0.15,sto=0] @2997ms
2025-09-24T16:04:01.112+07:00  INFO 78709 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-24T16:04:01.203+07:00  INFO 78709 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-24T16:04:01.232+07:00  INFO 78709 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-24T16:04:01.391+07:00  INFO 78709 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-24T16:04:01.424+07:00  WARN 78709 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-24T16:04:02.028+07:00  INFO 78709 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-24T16:04:02.037+07:00  INFO 78709 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@336bb88c] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-24T16:04:02.174+07:00  INFO 78709 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-24T16:04:02.372+07:00  INFO 78709 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.graphapi", "net.datatp.module.kpi", "net.datatp.module.monitor.activity", "net.datatp.module.wfms", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "net.datatp.module.okr", "cloud.datatp.module.fleet", "net.datatp.module.core.template", "cloud.datatp.gps", "cloud.datatp.module.pegasus.shipment", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "net.datatp.module.company.tmpl", "cloud.datatp.tms", "net.datatp.module.groovy", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "cloud.datatp.vendor", "net.datatp.module.resource", "net.datatp.module.hr", "net.datatp.module.company.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.core.print", "net.datatp.module.chatbot", "cloud.datatp.module.odoo", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-09-24T16:04:02.374+07:00  INFO 78709 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-24T16:04:02.386+07:00  INFO 78709 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-24T16:04:02.387+07:00  INFO 78709 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-24T16:04:02.412+07:00  INFO 78709 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-24T16:04:02.419+07:00  WARN 78709 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-24T16:04:04.406+07:00  INFO 78709 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-24T16:04:04.407+07:00  INFO 78709 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@2e90cb11] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-24T16:04:04.616+07:00  WARN 78709 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-24T16:04:04.616+07:00  WARN 78709 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-24T16:04:04.629+07:00  WARN 78709 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-24T16:04:04.629+07:00  WARN 78709 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-24T16:04:04.642+07:00  WARN 78709 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-24T16:04:04.642+07:00  WARN 78709 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-24T16:04:05.085+07:00  INFO 78709 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-24T16:04:05.092+07:00  INFO 78709 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-24T16:04:05.093+07:00  INFO 78709 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-24T16:04:05.117+07:00  INFO 78709 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-24T16:04:05.121+07:00  WARN 78709 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-24T16:04:05.631+07:00  INFO 78709 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-24T16:04:05.632+07:00  INFO 78709 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@5a4e221] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-24T16:04:05.703+07:00  WARN 78709 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-24T16:04:05.703+07:00  WARN 78709 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-24T16:04:06.146+07:00  INFO 78709 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-24T16:04:06.177+07:00  INFO 78709 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-24T16:04:06.181+07:00  INFO 78709 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-24T16:04:06.181+07:00  INFO 78709 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-24T16:04:06.188+07:00  WARN 78709 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-24T16:04:06.321+07:00  INFO 78709 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-24T16:04:06.831+07:00  INFO 78709 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-24T16:04:06.834+07:00  INFO 78709 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-24T16:04:06.871+07:00  INFO 78709 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-24T16:04:06.915+07:00  INFO 78709 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-24T16:04:07.029+07:00  INFO 78709 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-24T16:04:07.057+07:00  INFO 78709 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-24T16:04:07.080+07:00  INFO 78709 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 27695071ms : this is harmless.
2025-09-24T16:04:07.090+07:00  INFO 78709 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-24T16:04:07.093+07:00  INFO 78709 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-24T16:04:07.114+07:00  INFO 78709 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 27695061ms : this is harmless.
2025-09-24T16:04:07.120+07:00  INFO 78709 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-24T16:04:07.133+07:00  INFO 78709 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-24T16:04:07.134+07:00  INFO 78709 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-24T16:04:09.196+07:00  INFO 78709 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-24T16:04:09.197+07:00  INFO 78709 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-24T16:04:09.198+07:00  WARN 78709 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-24T16:04:09.533+07:00  INFO 78709 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 24/09/2025@16:00:00+0700 to 24/09/2025@16:15:00+0700
2025-09-24T16:04:09.533+07:00  INFO 78709 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 24/09/2025@16:00:00+0700 to 24/09/2025@16:15:00+0700
2025-09-24T16:04:10.099+07:00  INFO 78709 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-09-24T16:04:10.099+07:00  INFO 78709 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-24T16:04:10.100+07:00  WARN 78709 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-24T16:04:10.410+07:00  INFO 78709 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-09-24T16:04:10.410+07:00  INFO 78709 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-09-24T16:04:10.410+07:00  INFO 78709 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-09-24T16:04:10.410+07:00  INFO 78709 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-09-24T16:04:10.410+07:00  INFO 78709 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-09-24T16:04:12.255+07:00  WARN 78709 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: c09201a3-be5b-4e2b-a976-275711d75b2f

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-24T16:04:12.259+07:00  INFO 78709 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-09-24T16:04:12.558+07:00  INFO 78709 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-24T16:04:12.558+07:00  INFO 78709 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-24T16:04:12.558+07:00  INFO 78709 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-24T16:04:12.558+07:00  INFO 78709 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-24T16:04:12.558+07:00  INFO 78709 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-09-24T16:04:12.558+07:00  INFO 78709 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-09-24T16:04:12.558+07:00  INFO 78709 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-24T16:04:12.558+07:00  INFO 78709 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-09-24T16:04:12.558+07:00  INFO 78709 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-09-24T16:04:12.558+07:00  INFO 78709 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-24T16:04:12.558+07:00  INFO 78709 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-09-24T16:04:12.558+07:00  INFO 78709 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-09-24T16:04:12.562+07:00  INFO 78709 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-24T16:04:12.562+07:00  INFO 78709 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-24T16:04:12.562+07:00  INFO 78709 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-24T16:04:12.643+07:00  INFO 78709 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-24T16:04:12.643+07:00  INFO 78709 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-24T16:04:12.645+07:00  INFO 78709 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-09-24T16:04:12.654+07:00  INFO 78709 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@59801dfc{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-24T16:04:12.654+07:00  INFO 78709 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-09-24T16:04:12.655+07:00  INFO 78709 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-09-24T16:04:12.691+07:00  INFO 78709 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-09-24T16:04:12.692+07:00  INFO 78709 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-09-24T16:04:12.699+07:00  INFO 78709 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 14.322 seconds (process running for 14.642)
2025-09-24T16:04:14.279+07:00  INFO 78709 --- [qtp1728158149-37] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0may4othfyfskj37my1vihy2h1
2025-09-24T16:04:14.279+07:00  INFO 78709 --- [qtp1728158149-35] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0ov64amn1e0mm1pffer0d30x120
2025-09-24T16:04:14.587+07:00  INFO 78709 --- [qtp1728158149-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0may4othfyfskj37my1vihy2h1, token = 595025c2817cb490acbec03f16dabd18
2025-09-24T16:04:14.601+07:00  INFO 78709 --- [qtp1728158149-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0ov64amn1e0mm1pffer0d30x120, token = 595025c2817cb490acbec03f16dabd18
2025-09-24T16:04:15.021+07:00  INFO 78709 --- [qtp1728158149-37] n.d.m.c.a.CompanyAuthenticationService   : User rio.vnsgn is logged in successfully system
2025-09-24T16:04:15.027+07:00  INFO 78709 --- [qtp1728158149-35] n.d.m.c.a.CompanyAuthenticationService   : User rio.vnsgn is logged in successfully system
2025-09-24T16:04:15.178+07:00 ERROR 78709 --- [qtp1728158149-37] n.d.m.monitor.call.EndpointCallContext   : Start call with component QuotationService, method searchSpecificQuotations, arguments
[ {
  "tenantId" : "default",
  "companyId" : 17,
  "companyParentId" : 4,
  "companyCode" : "beehcm",
  "companyLabel" : "Bee HCM",
  "companyFullName" : "BEE LOGISTICS CORPORATION - HO CHI MINH BRANCH OFFICE",
  "loginId" : "rio.vnsgn",
  "accountId" : 55989,
  "token" : "595025c2817cb490acbec03f16dabd18",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node0ov64amn1e0mm1pffer0d30x120",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : {
    "beehcm" : {
      "companyId" : 17,
      "companyParentId" : 4,
      "companyCode" : "beehcm",
      "companyLabel" : "Bee HCM",
      "companyFullName" : null
    }
  },
  "featurePermissions" : [ {
    "id" : 17605,
    "appId" : 3,
    "appModule" : "project",
    "appName" : "project",
    "companyId" : 17,
    "loginId" : "rio.vnsgn",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 12433,
    "appId" : 69,
    "appModule" : "hr",
    "appName" : "user-kpi",
    "companyId" : 17,
    "loginId" : "rio.vnsgn",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 13960,
    "appId" : 79,
    "appModule" : "company",
    "appName" : "user-asset",
    "companyId" : 17,
    "loginId" : "rio.vnsgn",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 11428,
    "appId" : 16,
    "appModule" : "logistics",
    "appName" : "user-logistics-sales",
    "companyId" : 17,
    "loginId" : "rio.vnsgn",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 14870,
    "appId" : 80,
    "appModule" : "logistics",
    "appName" : "company-logistics-sales",
    "companyId" : 17,
    "loginId" : "rio.vnsgn",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Group",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 14873,
    "appId" : 6,
    "appModule" : "company",
    "appName" : "company-space",
    "companyId" : 17,
    "loginId" : "rio.vnsgn",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Read"
  }, {
    "id" : 11427,
    "appId" : 2,
    "appModule" : "user",
    "appName" : "my-employee-space",
    "companyId" : 17,
    "loginId" : "rio.vnsgn",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  } ],
  "attributes" : { },
  "clientId" : "default:rio.vnsgn"
}, {
  "parentId" : 0,
  "id" : 17,
  "code" : "beehcm",
  "label" : "Bee HCM",
  "fullName" : "BEE LOGISTICS CORPORATION - HO CHI MINH BRANCH OFFICE"
}, {
  "params" : {
    "space" : "User",
    "companyId" : 17,
    "accessAccountId" : 55989
  },
  "filters" : [ {
    "name" : "search",
    "filterType" : "String",
    "filterValue" : "",
    "required" : true
  } ],
  "optionFilters" : [ {
    "name" : "storageState",
    "filterType" : "NotSet",
    "required" : true,
    "multiple" : true,
    "options" : [ "ACTIVE", "ARCHIVED" ],
    "selectOptions" : [ "ACTIVE" ]
  } ],
  "rangeFilters" : [ {
    "name" : "requestDate",
    "filterType" : "NotSet",
    "required" : true,
    "fromValue" : "01/07/2025@00:00:00+0700",
    "toValue" : "30/09/2025@23:59:59+0700"
  } ],
  "maxReturn" : 1000
} ]
2025-09-24T16:04:15.179+07:00 ERROR 78709 --- [qtp1728158149-37] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:118)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.springframework.jdbc.BadSqlGrammarException: PreparedStatementCallback; bad SQL grammar [
              WITH filtered_requests AS (
                  -- Existing lgc_price_inquiry_request data
                  SELECT
                    c.id,
                    c.company_id,
                    c.code,
                    c.request_date,
                    c.status,
                    c.mode,
                    c.purpose,
                    c.type_of_shipment,
                    c.service_mode,
                    c.client_partner_type,
                    c.client_partner_id,
                    c.client_label,
                    c.saleman_account_id,
                    c.saleman_label,
                    c.saleman_email,
                    c.saleman_phone,
                    c.saleman_job_title,
                    c.saleman_branch_name,
                    c.pricing_date,
                    c.pricing_account_id,
                    c.pricing_label,
                    c.term_of_service,
                    c.cargo_ready_date,
                    c.is_multi_route,
                    c.from_location_code,
                    c.from_location_label,
                    c.to_location_code,
                    c.to_location_label,
                    c.pickup_address,
                    c.delivery_address,
                    c.target_rate,
                    c.note,
                    c.feedback,
                    c.pricing_note,
                    c.total_new_prices_count,
                    c.total_analysis_prices_count,
                    c.step_tracking,
                    c.total_step_counting,
                    c.job_tracking_status,
                    c.mail_subject,
                    c.mail_to,
                    c.mail_cc,
                    -- ShipmentDetail fields
                    c.dimension_length,
                    c.dimension_width,
                    c.dimension_height,
                    c.stackable,
                    c.package_quantity,
                    c.volume_info,
                    c.volume_cbm,
                    c.gross_weight_kg,
                    c.report_volume,
                    c.report_volume_unit,
                    c.commodity,
                    c.desc_of_goods,
                    c.dg_liquid_cargo,
                    c.buy_insurance_request,
                    c.express_courier,
                    c.cross_border_trucking,
                    c.special_request_note,
                    c.free_time_terminal_request,
                    c.vn_border_gate_request,
                    -- Additional fields
                    inquiry_quotation.final_destination AS final_destination,
                    q.id AS quotation_id,
                    b.id AS booking_id,
                    'INQUIRY_REQUEST' AS type
                  FROM lgc_price_inquiry_request c
                  LEFT JOIN (
                      SELECT
                          reference_code,
                          final_destination,
                          company_id,
                          id,
                          ROW_NUMBER() OVER (PARTITION BY reference_code, company_id ORDER BY id DESC) AS rn
                      FROM lgc_sales_specific_service_inquiry
                      WHERE EXISTS (
                          SELECT 1
                          FROM lgc_sales_specific_quotation q
                          WHERE q.inquiry_id = lgc_sales_specific_service_inquiry.id
                      )
                  ) inquiry_quotation
                      ON inquiry_quotation.reference_code = c.code
                      AND inquiry_quotation.company_id = c.company_id
                      AND inquiry_quotation.rn = 1
                  LEFT JOIN lgc_sales_specific_quotation q
                      ON q.inquiry_id = inquiry_quotation.id
                  LEFT JOIN (
                      SELECT
                          reference_code,
                          company_id,
                          id,
                          ROW_NUMBER() OVER (PARTITION BY reference_code, company_id ORDER BY id DESC) AS rn
                      FROM lgc_sales_specific_service_inquiry
                      WHERE EXISTS (
                          SELECT 1
                          FROM lgc_sales_booking b
                          WHERE b.inquiry_id = lgc_sales_specific_service_inquiry.id
                      )
                  ) inquiry_booking
                      ON inquiry_booking.reference_code = c.code
                      AND inquiry_booking.company_id = c.company_id
                      AND inquiry_booking.rn = 1
                      AND (inquiry_booking.id != inquiry_quotation.id OR inquiry_quotation.id IS NULL)
                  LEFT JOIN lgc_sales_booking b
                      ON b.inquiry_id = inquiry_booking.id
                  WHERE c.storage_state IN (?)
                    -- AND  FILTER_BY_OPTION('c.status', 'status' , userParams, options) 
                     AND (c.request_date >= ? AND c.request_date <= ?)
                    -- q.is_favorite = :isFavorite
                    AND (
                      'System' = ?
                      OR ('Company' = ? AND c.company_id = ?)
                      OR ('User' = ? AND c.saleman_account_id = ?)
                    )

                  UNION ALL

                  -- Add lgc_price_bulk_cargo_inquiry_request data
                  SELECT
                    bc.id,
                    bc.company_id,
                    bc.code,
                    bc.request_date,
                    bc.status,
                    'Bulk Cargo' AS mode,                    -- Constant value                           -- Constant for bulk cargo
                    bc.purpose,                                -- BulkCargo has purpose field
                    NULL AS type_of_shipment,                -- Not available in bulk cargo
                    NULL AS service_mode,                     -- Not available in bulk cargo
                    'CUSTOMERS' AS client_partner_type,        -- Default value
                    bc.client_partner_id,
                    bc.client_label,
                    bc.saleman_account_id,
                    bc.saleman_label,
                    bc.saleman_email,
                    bc.saleman_phone,
                    NULL AS saleman_job_title,               -- Not available in bulk cargo
                    bc.saleman_branch_name,
                    bc.pricing_date,
                    bc.pricing_account_id,
                    bc.pricing_label,
                    bc.term_of_service,        -- From embedded BulkCargoShipmentDetail
                    NULL AS cargo_ready_date,                 -- Bulk cargo uses laydays_date instead
                    false AS is_multi_route,                   -- Default false
                    bc.from_location_code,
                    bc.from_location_label,
                    bc.to_location_code,
                    bc.to_location_label,
                    NULL AS pickup_address,                   -- Not available in bulk cargo
                    NULL AS delivery_address,                 -- Not available in bulk cargo
                    bc.target_rate,            -- From embedded BulkCargoShipmentDetail
                    bc.note,
                    bc.feedback,
                    bc.pricing_note,
                    NULL AS total_new_prices_count,            -- Not available in bulk cargo
                    NULL AS total_analysis_prices_count,       -- Not available in bulk cargo
                    NULL AS step_tracking,                    -- Not available in bulk cargo
                    NULL AS total_step_counting,               -- Not available in bulk cargo
                    NULL AS job_tracking_status,              -- Not available in bulk cargo
                    bc.mail_subject,
                    bc.mail_to,
                    bc.mail_cc,
                    -- ShipmentDetail fields (mostly N/A for bulk cargo)
                    NULL AS dimension_length,                 -- Bulk cargo doesn't have dimensions
                    NULL AS dimension_width,
                    NULL AS dimension_height,
                    bc.stackable,              -- BulkCargo has stackable
                    NULL AS package_quantity,                 -- Bulk cargo uses quantity instead
                    bc.volume AS volume_info,                      -- Bulk cargo doesn't use container info
                    NULL AS volume_cbm,   -- Map volume to volume_cbm
                    NULL AS gross_weight_kg,                  -- Bulk cargo uses quantity in metric tons
                    bc.volume AS report_volume, -- Use volume as report
                    'CBM' AS report_volume_unit, -- Use unit as report unit
                    bc.commodity,
                    bc.desc_of_goods,
                    false AS dg_liquid_cargo,                  -- Default false
                    false AS buy_insurance_request,            -- Default false
                    false AS express_courier,                  -- Default false
                    false AS cross_border_trucking,            -- Default false
                    bc.cargo_proceeding AS special_request_note, -- Map cargo_proceeding
                    NULL AS free_time_terminal_request,       -- Not available in bulk cargo
                    NULL AS vn_border_gate_request,           -- Not available in bulk cargo
                    -- Additional fields
                    NULL AS final_destination,                 -- Bulk cargo doesn't have this
                    NULL AS quotation_id,                      -- Bulk cargo doesn't have quotations
                    NULL AS booking_id,                        -- Bulk cargo doesn't have bookings
                    'BULK_CARGO_INQUIRY_REQUEST' AS type
                  FROM lgc_price_bulk_cargo_inquiry_request bc
                  WHERE bc.storage_state IN (?)
                     AND (bc.request_date >= ? AND bc.request_date <= ?)
                    AND (
                      'System' = ?
                      OR ('Company' = ? AND bc.company_id = ?)
                      OR ('User' = ? AND bc.saleman_account_id = ?)
                    )
              )
              SELECT r.*
              FROM filtered_requests r
              ORDER BY r.request_date DESC
            ]
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:112)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.springframework.jdbc.core.JdbcTemplate.translateException(JdbcTemplate.java:1556)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:677)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:723)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:748)
	at org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate.query(NamedParameterJdbcTemplate.java:178)
	at org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate.query(NamedParameterJdbcTemplate.java:186)
	at net.datatp.module.data.db.repository.DAOTemplate.sqlSelect(DAOTemplate.java:162)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at cloud.datatp.fforwarder.core.db.CRMDAOTemplatePrimary$$SpringCGLIB$$0.sqlSelect(<generated>)
	at cloud.datatp.fforwarder.core.db.CRMSqlQueryUnitManager$QueryContext.createSqlSelectView(CRMSqlQueryUnitManager.java:53)
	at cloud.datatp.fforwarder.core.db.CRMDaoService.searchDbRecords(CRMDaoService.java:109)
	at cloud.datatp.fforwarder.sales.quotation.SpecificQuotationLogic.searchSpecificQuotations(SpecificQuotationLogic.java:289)
	at cloud.datatp.fforwarder.sales.quotation.QuotationService.searchSpecificQuotations(QuotationService.java:74)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at cloud.datatp.fforwarder.sales.quotation.QuotationService$$SpringCGLIB$$0.searchSpecificQuotations(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 93 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: UNION types character varying and double precision cannot be matched
  Position: 8354
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2725)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2412)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:371)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:502)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:419)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:194)
	at org.postgresql.jdbc.PgPreparedStatement.executeQuery(PgPreparedStatement.java:137)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeQuery(ProxyPreparedStatement.java:52)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeQuery(HikariProxyPreparedStatement.java)
	at org.springframework.jdbc.core.JdbcTemplate$1.doInPreparedStatement(JdbcTemplate.java:732)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:658)
	... 122 common frames omitted

2025-09-24T16:04:15.186+07:00  INFO 78709 --- [qtp1728158149-37] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint QuotationService/searchSpecificQuotations
2025-09-24T16:04:15.195+07:00 ERROR 78709 --- [qtp1728158149-39] n.d.m.monitor.call.EndpointCallContext   : Start call with component QuotationService, method searchSpecificQuotations, arguments
[ {
  "tenantId" : "default",
  "companyId" : 17,
  "companyParentId" : 4,
  "companyCode" : "beehcm",
  "companyLabel" : "Bee HCM",
  "companyFullName" : "BEE LOGISTICS CORPORATION - HO CHI MINH BRANCH OFFICE",
  "loginId" : "rio.vnsgn",
  "accountId" : 55989,
  "token" : "595025c2817cb490acbec03f16dabd18",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node0ov64amn1e0mm1pffer0d30x120",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : {
    "beehcm" : {
      "companyId" : 17,
      "companyParentId" : 4,
      "companyCode" : "beehcm",
      "companyLabel" : "Bee HCM",
      "companyFullName" : null
    }
  },
  "featurePermissions" : [ {
    "id" : 17605,
    "appId" : 3,
    "appModule" : "project",
    "appName" : "project",
    "companyId" : 17,
    "loginId" : "rio.vnsgn",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 12433,
    "appId" : 69,
    "appModule" : "hr",
    "appName" : "user-kpi",
    "companyId" : 17,
    "loginId" : "rio.vnsgn",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 13960,
    "appId" : 79,
    "appModule" : "company",
    "appName" : "user-asset",
    "companyId" : 17,
    "loginId" : "rio.vnsgn",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 11428,
    "appId" : 16,
    "appModule" : "logistics",
    "appName" : "user-logistics-sales",
    "companyId" : 17,
    "loginId" : "rio.vnsgn",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 14870,
    "appId" : 80,
    "appModule" : "logistics",
    "appName" : "company-logistics-sales",
    "companyId" : 17,
    "loginId" : "rio.vnsgn",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Group",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 14873,
    "appId" : 6,
    "appModule" : "company",
    "appName" : "company-space",
    "companyId" : 17,
    "loginId" : "rio.vnsgn",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Read"
  }, {
    "id" : 11427,
    "appId" : 2,
    "appModule" : "user",
    "appName" : "my-employee-space",
    "companyId" : 17,
    "loginId" : "rio.vnsgn",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  } ],
  "attributes" : { },
  "clientId" : "default:rio.vnsgn"
}, {
  "parentId" : 0,
  "id" : 17,
  "code" : "beehcm",
  "label" : "Bee HCM",
  "fullName" : "BEE LOGISTICS CORPORATION - HO CHI MINH BRANCH OFFICE"
}, {
  "params" : {
    "space" : "User",
    "companyId" : 17,
    "accessAccountId" : 55989
  },
  "filters" : [ {
    "name" : "search",
    "filterType" : "String",
    "filterValue" : "",
    "required" : true
  } ],
  "optionFilters" : [ {
    "name" : "storageState",
    "filterType" : "NotSet",
    "required" : true,
    "multiple" : true,
    "options" : [ "ACTIVE", "ARCHIVED" ],
    "selectOptions" : [ "ACTIVE" ]
  } ],
  "rangeFilters" : [ {
    "name" : "requestDate",
    "filterType" : "NotSet",
    "required" : true,
    "fromValue" : "01/07/2025@00:00:00+0700",
    "toValue" : "30/09/2025@23:59:59+0700"
  } ],
  "maxReturn" : 1000
} ]
2025-09-24T16:04:15.196+07:00 ERROR 78709 --- [qtp1728158149-39] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:118)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.springframework.jdbc.BadSqlGrammarException: PreparedStatementCallback; bad SQL grammar [
              WITH filtered_requests AS (
                  -- Existing lgc_price_inquiry_request data
                  SELECT
                    c.id,
                    c.company_id,
                    c.code,
                    c.request_date,
                    c.status,
                    c.mode,
                    c.purpose,
                    c.type_of_shipment,
                    c.service_mode,
                    c.client_partner_type,
                    c.client_partner_id,
                    c.client_label,
                    c.saleman_account_id,
                    c.saleman_label,
                    c.saleman_email,
                    c.saleman_phone,
                    c.saleman_job_title,
                    c.saleman_branch_name,
                    c.pricing_date,
                    c.pricing_account_id,
                    c.pricing_label,
                    c.term_of_service,
                    c.cargo_ready_date,
                    c.is_multi_route,
                    c.from_location_code,
                    c.from_location_label,
                    c.to_location_code,
                    c.to_location_label,
                    c.pickup_address,
                    c.delivery_address,
                    c.target_rate,
                    c.note,
                    c.feedback,
                    c.pricing_note,
                    c.total_new_prices_count,
                    c.total_analysis_prices_count,
                    c.step_tracking,
                    c.total_step_counting,
                    c.job_tracking_status,
                    c.mail_subject,
                    c.mail_to,
                    c.mail_cc,
                    -- ShipmentDetail fields
                    c.dimension_length,
                    c.dimension_width,
                    c.dimension_height,
                    c.stackable,
                    c.package_quantity,
                    c.volume_info,
                    c.volume_cbm,
                    c.gross_weight_kg,
                    c.report_volume,
                    c.report_volume_unit,
                    c.commodity,
                    c.desc_of_goods,
                    c.dg_liquid_cargo,
                    c.buy_insurance_request,
                    c.express_courier,
                    c.cross_border_trucking,
                    c.special_request_note,
                    c.free_time_terminal_request,
                    c.vn_border_gate_request,
                    -- Additional fields
                    inquiry_quotation.final_destination AS final_destination,
                    q.id AS quotation_id,
                    b.id AS booking_id,
                    'INQUIRY_REQUEST' AS type
                  FROM lgc_price_inquiry_request c
                  LEFT JOIN (
                      SELECT
                          reference_code,
                          final_destination,
                          company_id,
                          id,
                          ROW_NUMBER() OVER (PARTITION BY reference_code, company_id ORDER BY id DESC) AS rn
                      FROM lgc_sales_specific_service_inquiry
                      WHERE EXISTS (
                          SELECT 1
                          FROM lgc_sales_specific_quotation q
                          WHERE q.inquiry_id = lgc_sales_specific_service_inquiry.id
                      )
                  ) inquiry_quotation
                      ON inquiry_quotation.reference_code = c.code
                      AND inquiry_quotation.company_id = c.company_id
                      AND inquiry_quotation.rn = 1
                  LEFT JOIN lgc_sales_specific_quotation q
                      ON q.inquiry_id = inquiry_quotation.id
                  LEFT JOIN (
                      SELECT
                          reference_code,
                          company_id,
                          id,
                          ROW_NUMBER() OVER (PARTITION BY reference_code, company_id ORDER BY id DESC) AS rn
                      FROM lgc_sales_specific_service_inquiry
                      WHERE EXISTS (
                          SELECT 1
                          FROM lgc_sales_booking b
                          WHERE b.inquiry_id = lgc_sales_specific_service_inquiry.id
                      )
                  ) inquiry_booking
                      ON inquiry_booking.reference_code = c.code
                      AND inquiry_booking.company_id = c.company_id
                      AND inquiry_booking.rn = 1
                      AND (inquiry_booking.id != inquiry_quotation.id OR inquiry_quotation.id IS NULL)
                  LEFT JOIN lgc_sales_booking b
                      ON b.inquiry_id = inquiry_booking.id
                  WHERE c.storage_state IN (?)
                    -- AND  FILTER_BY_OPTION('c.status', 'status' , userParams, options) 
                     AND (c.request_date >= ? AND c.request_date <= ?)
                    -- q.is_favorite = :isFavorite
                    AND (
                      'System' = ?
                      OR ('Company' = ? AND c.company_id = ?)
                      OR ('User' = ? AND c.saleman_account_id = ?)
                    )

                  UNION ALL

                  -- Add lgc_price_bulk_cargo_inquiry_request data
                  SELECT
                    bc.id,
                    bc.company_id,
                    bc.code,
                    bc.request_date,
                    bc.status,
                    'Bulk Cargo' AS mode,                    -- Constant value                           -- Constant for bulk cargo
                    bc.purpose,                                -- BulkCargo has purpose field
                    NULL AS type_of_shipment,                -- Not available in bulk cargo
                    NULL AS service_mode,                     -- Not available in bulk cargo
                    'CUSTOMERS' AS client_partner_type,        -- Default value
                    bc.client_partner_id,
                    bc.client_label,
                    bc.saleman_account_id,
                    bc.saleman_label,
                    bc.saleman_email,
                    bc.saleman_phone,
                    NULL AS saleman_job_title,               -- Not available in bulk cargo
                    bc.saleman_branch_name,
                    bc.pricing_date,
                    bc.pricing_account_id,
                    bc.pricing_label,
                    bc.term_of_service,        -- From embedded BulkCargoShipmentDetail
                    NULL AS cargo_ready_date,                 -- Bulk cargo uses laydays_date instead
                    false AS is_multi_route,                   -- Default false
                    bc.from_location_code,
                    bc.from_location_label,
                    bc.to_location_code,
                    bc.to_location_label,
                    NULL AS pickup_address,                   -- Not available in bulk cargo
                    NULL AS delivery_address,                 -- Not available in bulk cargo
                    bc.target_rate,            -- From embedded BulkCargoShipmentDetail
                    bc.note,
                    bc.feedback,
                    bc.pricing_note,
                    NULL AS total_new_prices_count,            -- Not available in bulk cargo
                    NULL AS total_analysis_prices_count,       -- Not available in bulk cargo
                    NULL AS step_tracking,                    -- Not available in bulk cargo
                    NULL AS total_step_counting,               -- Not available in bulk cargo
                    NULL AS job_tracking_status,              -- Not available in bulk cargo
                    bc.mail_subject,
                    bc.mail_to,
                    bc.mail_cc,
                    -- ShipmentDetail fields (mostly N/A for bulk cargo)
                    NULL AS dimension_length,                 -- Bulk cargo doesn't have dimensions
                    NULL AS dimension_width,
                    NULL AS dimension_height,
                    bc.stackable,              -- BulkCargo has stackable
                    NULL AS package_quantity,                 -- Bulk cargo uses quantity instead
                    bc.volume AS volume_info,                      -- Bulk cargo doesn't use container info
                    NULL AS volume_cbm,   -- Map volume to volume_cbm
                    NULL AS gross_weight_kg,                  -- Bulk cargo uses quantity in metric tons
                    bc.volume AS report_volume, -- Use volume as report
                    'CBM' AS report_volume_unit, -- Use unit as report unit
                    bc.commodity,
                    bc.desc_of_goods,
                    false AS dg_liquid_cargo,                  -- Default false
                    false AS buy_insurance_request,            -- Default false
                    false AS express_courier,                  -- Default false
                    false AS cross_border_trucking,            -- Default false
                    bc.cargo_proceeding AS special_request_note, -- Map cargo_proceeding
                    NULL AS free_time_terminal_request,       -- Not available in bulk cargo
                    NULL AS vn_border_gate_request,           -- Not available in bulk cargo
                    -- Additional fields
                    NULL AS final_destination,                 -- Bulk cargo doesn't have this
                    NULL AS quotation_id,                      -- Bulk cargo doesn't have quotations
                    NULL AS booking_id,                        -- Bulk cargo doesn't have bookings
                    'BULK_CARGO_INQUIRY_REQUEST' AS type
                  FROM lgc_price_bulk_cargo_inquiry_request bc
                  WHERE bc.storage_state IN (?)
                     AND (bc.request_date >= ? AND bc.request_date <= ?)
                    AND (
                      'System' = ?
                      OR ('Company' = ? AND bc.company_id = ?)
                      OR ('User' = ? AND bc.saleman_account_id = ?)
                    )
              )
              SELECT r.*
              FROM filtered_requests r
              ORDER BY r.request_date DESC
            ]
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:112)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.springframework.jdbc.core.JdbcTemplate.translateException(JdbcTemplate.java:1556)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:677)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:723)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:748)
	at org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate.query(NamedParameterJdbcTemplate.java:178)
	at org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate.query(NamedParameterJdbcTemplate.java:186)
	at net.datatp.module.data.db.repository.DAOTemplate.sqlSelect(DAOTemplate.java:162)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at cloud.datatp.fforwarder.core.db.CRMDAOTemplatePrimary$$SpringCGLIB$$0.sqlSelect(<generated>)
	at cloud.datatp.fforwarder.core.db.CRMSqlQueryUnitManager$QueryContext.createSqlSelectView(CRMSqlQueryUnitManager.java:53)
	at cloud.datatp.fforwarder.core.db.CRMDaoService.searchDbRecords(CRMDaoService.java:109)
	at cloud.datatp.fforwarder.sales.quotation.SpecificQuotationLogic.searchSpecificQuotations(SpecificQuotationLogic.java:289)
	at cloud.datatp.fforwarder.sales.quotation.QuotationService.searchSpecificQuotations(QuotationService.java:74)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at cloud.datatp.fforwarder.sales.quotation.QuotationService$$SpringCGLIB$$0.searchSpecificQuotations(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: UNION types character varying and double precision cannot be matched
  Position: 8354
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2725)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2412)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:371)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:502)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:419)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:194)
	at org.postgresql.jdbc.PgPreparedStatement.executeQuery(PgPreparedStatement.java:137)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeQuery(ProxyPreparedStatement.java:52)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeQuery(HikariProxyPreparedStatement.java)
	at org.springframework.jdbc.core.JdbcTemplate$1.doInPreparedStatement(JdbcTemplate.java:732)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:658)
	... 127 common frames omitted

2025-09-24T16:04:15.198+07:00  INFO 78709 --- [qtp1728158149-39] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint QuotationService/searchSpecificQuotations
2025-09-24T16:05:06.686+07:00  INFO 78709 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T16:05:06.693+07:00  INFO 78709 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T16:05:15.770+07:00  INFO 78709 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-24T16:05:15.802+07:00  INFO 78709 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T16:06:04.873+07:00  INFO 78709 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T16:06:53.020+07:00  INFO 78709 --- [qtp1728158149-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0ov64amn1e0mm1pffer0d30x120, token = 595025c2817cb490acbec03f16dabd18
2025-09-24T16:06:53.032+07:00  INFO 78709 --- [qtp1728158149-37] n.d.m.c.a.CompanyAuthenticationService   : User rio.vnsgn is logged in successfully system
2025-09-24T16:06:53.056+07:00  INFO 78709 --- [qtp1728158149-39] n.d.module.session.ClientSessionManager  : Add a client session id = node0ov64amn1e0mm1pffer0d30x120, token = 595025c2817cb490acbec03f16dabd18
2025-09-24T16:06:53.072+07:00  INFO 78709 --- [qtp1728158149-39] n.d.m.c.a.CompanyAuthenticationService   : User rio.vnsgn is logged in successfully system
2025-09-24T16:06:53.145+07:00 ERROR 78709 --- [qtp1728158149-61] n.d.m.monitor.call.EndpointCallContext   : Start call with component QuotationService, method searchSpecificQuotations, arguments
[ {
  "tenantId" : "default",
  "companyId" : 17,
  "companyParentId" : 4,
  "companyCode" : "beehcm",
  "companyLabel" : "Bee HCM",
  "companyFullName" : "BEE LOGISTICS CORPORATION - HO CHI MINH BRANCH OFFICE",
  "loginId" : "rio.vnsgn",
  "accountId" : 55989,
  "token" : "595025c2817cb490acbec03f16dabd18",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node0ov64amn1e0mm1pffer0d30x120",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : {
    "beehcm" : {
      "companyId" : 17,
      "companyParentId" : 4,
      "companyCode" : "beehcm",
      "companyLabel" : "Bee HCM",
      "companyFullName" : null
    }
  },
  "featurePermissions" : [ {
    "id" : 17605,
    "appId" : 3,
    "appModule" : "project",
    "appName" : "project",
    "companyId" : 17,
    "loginId" : "rio.vnsgn",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 12433,
    "appId" : 69,
    "appModule" : "hr",
    "appName" : "user-kpi",
    "companyId" : 17,
    "loginId" : "rio.vnsgn",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 13960,
    "appId" : 79,
    "appModule" : "company",
    "appName" : "user-asset",
    "companyId" : 17,
    "loginId" : "rio.vnsgn",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 11428,
    "appId" : 16,
    "appModule" : "logistics",
    "appName" : "user-logistics-sales",
    "companyId" : 17,
    "loginId" : "rio.vnsgn",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 14870,
    "appId" : 80,
    "appModule" : "logistics",
    "appName" : "company-logistics-sales",
    "companyId" : 17,
    "loginId" : "rio.vnsgn",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Group",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 14873,
    "appId" : 6,
    "appModule" : "company",
    "appName" : "company-space",
    "companyId" : 17,
    "loginId" : "rio.vnsgn",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Read"
  }, {
    "id" : 11427,
    "appId" : 2,
    "appModule" : "user",
    "appName" : "my-employee-space",
    "companyId" : 17,
    "loginId" : "rio.vnsgn",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  } ],
  "attributes" : { },
  "clientId" : "default:rio.vnsgn"
}, {
  "parentId" : 0,
  "id" : 17,
  "code" : "beehcm",
  "label" : "Bee HCM",
  "fullName" : "BEE LOGISTICS CORPORATION - HO CHI MINH BRANCH OFFICE"
}, {
  "params" : {
    "space" : "User",
    "companyId" : 17,
    "accessAccountId" : 55989
  },
  "filters" : [ {
    "name" : "search",
    "filterType" : "String",
    "filterValue" : "",
    "required" : true
  } ],
  "optionFilters" : [ {
    "name" : "storageState",
    "filterType" : "NotSet",
    "required" : true,
    "multiple" : true,
    "options" : [ "ACTIVE", "ARCHIVED" ],
    "selectOptions" : [ "ACTIVE" ]
  } ],
  "rangeFilters" : [ {
    "name" : "requestDate",
    "filterType" : "NotSet",
    "required" : true,
    "fromValue" : "01/07/2025@00:00:00+0700",
    "toValue" : "30/09/2025@23:59:59+0700"
  } ],
  "maxReturn" : 1000
} ]
2025-09-24T16:06:53.145+07:00 ERROR 78709 --- [qtp1728158149-61] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:118)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.springframework.jdbc.BadSqlGrammarException: PreparedStatementCallback; bad SQL grammar [
              WITH filtered_requests AS (
                  -- Existing lgc_price_inquiry_request data
                  SELECT
                    c.id,
                    c.company_id,
                    c.code,
                    c.request_date,
                    c.status,
                    c.mode,
                    c.purpose,
                    c.type_of_shipment,
                    c.service_mode,
                    c.client_partner_type,
                    c.client_partner_id,
                    c.client_label,
                    c.saleman_account_id,
                    c.saleman_label,
                    c.saleman_email,
                    c.saleman_phone,
                    c.saleman_job_title,
                    c.saleman_branch_name,
                    c.pricing_date,
                    c.pricing_account_id,
                    c.pricing_label,
                    c.term_of_service,
                    c.cargo_ready_date,
                    c.is_multi_route,
                    c.from_location_code,
                    c.from_location_label,
                    c.to_location_code,
                    c.to_location_label,
                    c.pickup_address,
                    c.delivery_address,
                    c.target_rate,
                    c.note,
                    c.feedback,
                    c.pricing_note,
                    c.total_new_prices_count,
                    c.total_analysis_prices_count,
                    c.step_tracking,
                    c.total_step_counting,
                    c.job_tracking_status,
                    c.mail_subject,
                    c.mail_to,
                    c.mail_cc,
                    -- ShipmentDetail fields
                    c.dimension_length,
                    c.dimension_width,
                    c.dimension_height,
                    c.stackable,
                    c.package_quantity,
                    c.volume_info,
                    c.volume_cbm,
                    c.gross_weight_kg,
                    c.report_volume,
                    c.report_volume_unit,
                    c.commodity,
                    c.desc_of_goods,
                    c.dg_liquid_cargo,
                    c.buy_insurance_request,
                    c.express_courier,
                    c.cross_border_trucking,
                    c.special_request_note,
                    c.free_time_terminal_request,
                    c.vn_border_gate_request,
                    -- Additional fields
                    inquiry_quotation.final_destination AS final_destination,
                    q.id AS quotation_id,
                    b.id AS booking_id,
                    'INQUIRY_REQUEST' AS type
                  FROM lgc_price_inquiry_request c
                  LEFT JOIN (
                      SELECT
                          reference_code,
                          final_destination,
                          company_id,
                          id,
                          ROW_NUMBER() OVER (PARTITION BY reference_code, company_id ORDER BY id DESC) AS rn
                      FROM lgc_sales_specific_service_inquiry
                      WHERE EXISTS (
                          SELECT 1
                          FROM lgc_sales_specific_quotation q
                          WHERE q.inquiry_id = lgc_sales_specific_service_inquiry.id
                      )
                  ) inquiry_quotation
                      ON inquiry_quotation.reference_code = c.code
                      AND inquiry_quotation.company_id = c.company_id
                      AND inquiry_quotation.rn = 1
                  LEFT JOIN lgc_sales_specific_quotation q
                      ON q.inquiry_id = inquiry_quotation.id
                  LEFT JOIN (
                      SELECT
                          reference_code,
                          company_id,
                          id,
                          ROW_NUMBER() OVER (PARTITION BY reference_code, company_id ORDER BY id DESC) AS rn
                      FROM lgc_sales_specific_service_inquiry
                      WHERE EXISTS (
                          SELECT 1
                          FROM lgc_sales_booking b
                          WHERE b.inquiry_id = lgc_sales_specific_service_inquiry.id
                      )
                  ) inquiry_booking
                      ON inquiry_booking.reference_code = c.code
                      AND inquiry_booking.company_id = c.company_id
                      AND inquiry_booking.rn = 1
                      AND (inquiry_booking.id != inquiry_quotation.id OR inquiry_quotation.id IS NULL)
                  LEFT JOIN lgc_sales_booking b
                      ON b.inquiry_id = inquiry_booking.id
                  WHERE c.storage_state IN (?)
                    -- AND  FILTER_BY_OPTION('c.status', 'status' , userParams, options) 
                     AND (c.request_date >= ? AND c.request_date <= ?)
                    -- q.is_favorite = :isFavorite
                    AND (
                      'System' = ?
                      OR ('Company' = ? AND c.company_id = ?)
                      OR ('User' = ? AND c.saleman_account_id = ?)
                    )

                  UNION ALL

                  -- Add lgc_price_bulk_cargo_inquiry_request data
                  SELECT
                    bc.id,
                    bc.company_id,
                    bc.code,
                    bc.request_date,
                    bc.status,
                    'Bulk Cargo' AS mode,                    -- Constant value                           -- Constant for bulk cargo
                    bc.purpose,                                -- BulkCargo has purpose field
                    NULL AS type_of_shipment,                -- Not available in bulk cargo
                    NULL AS service_mode,                     -- Not available in bulk cargo
                    'CUSTOMERS' AS client_partner_type,        -- Default value
                    bc.client_partner_id,
                    bc.client_label,
                    bc.saleman_account_id,
                    bc.saleman_label,
                    bc.saleman_email,
                    bc.saleman_phone,
                    NULL AS saleman_job_title,               -- Not available in bulk cargo
                    bc.saleman_branch_name,
                    bc.pricing_date,
                    bc.pricing_account_id,
                    bc.pricing_label,
                    bc.term_of_service,        -- From embedded BulkCargoShipmentDetail
                    NULL AS cargo_ready_date,                 -- Bulk cargo uses laydays_date instead
                    false AS is_multi_route,                   -- Default false
                    bc.from_location_code,
                    bc.from_location_label,
                    bc.to_location_code,
                    bc.to_location_label,
                    NULL AS pickup_address,                   -- Not available in bulk cargo
                    NULL AS delivery_address,                 -- Not available in bulk cargo
                    bc.target_rate,            -- From embedded BulkCargoShipmentDetail
                    bc.note,
                    bc.feedback,
                    bc.pricing_note,
                    NULL AS total_new_prices_count,            -- Not available in bulk cargo
                    NULL AS total_analysis_prices_count,       -- Not available in bulk cargo
                    NULL AS step_tracking,                    -- Not available in bulk cargo
                    NULL AS total_step_counting,               -- Not available in bulk cargo
                    NULL AS job_tracking_status,              -- Not available in bulk cargo
                    bc.mail_subject,
                    bc.mail_to,
                    bc.mail_cc,
                    -- ShipmentDetail fields (mostly N/A for bulk cargo)
                    NULL AS dimension_length,                 -- Bulk cargo doesn't have dimensions
                    NULL AS dimension_width,
                    NULL AS dimension_height,
                    bc.stackable,              -- BulkCargo has stackable
                    NULL AS package_quantity,                 -- Bulk cargo uses quantity instead
                    bc.volume AS volume_info,                      -- Bulk cargo doesn't use container info
                    NULL AS volume_cbm,   -- Map volume to volume_cbm
                    NULL AS gross_weight_kg,                  -- Bulk cargo uses quantity in metric tons
                    bc.volume AS report_volume, -- Use volume as report
                    'CBM' AS report_volume_unit, -- Use unit as report unit
                    bc.commodity,
                    bc.desc_of_goods,
                    false AS dg_liquid_cargo,                  -- Default false
                    false AS buy_insurance_request,            -- Default false
                    false AS express_courier,                  -- Default false
                    false AS cross_border_trucking,            -- Default false
                    bc.cargo_proceeding AS special_request_note, -- Map cargo_proceeding
                    NULL AS free_time_terminal_request,       -- Not available in bulk cargo
                    NULL AS vn_border_gate_request,           -- Not available in bulk cargo
                    -- Additional fields
                    NULL AS final_destination,                 -- Bulk cargo doesn't have this
                    NULL AS quotation_id,                      -- Bulk cargo doesn't have quotations
                    NULL AS booking_id,                        -- Bulk cargo doesn't have bookings
                    'BULK_CARGO_INQUIRY_REQUEST' AS type
                  FROM lgc_price_bulk_cargo_inquiry_request bc
                  WHERE bc.storage_state IN (?)
                     AND (bc.request_date >= ? AND bc.request_date <= ?)
                    AND (
                      'System' = ?
                      OR ('Company' = ? AND bc.company_id = ?)
                      OR ('User' = ? AND bc.saleman_account_id = ?)
                    )
              )
              SELECT r.*
              FROM filtered_requests r
              ORDER BY r.request_date DESC
            ]
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:112)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.springframework.jdbc.core.JdbcTemplate.translateException(JdbcTemplate.java:1556)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:677)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:723)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:748)
	at org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate.query(NamedParameterJdbcTemplate.java:178)
	at org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate.query(NamedParameterJdbcTemplate.java:186)
	at net.datatp.module.data.db.repository.DAOTemplate.sqlSelect(DAOTemplate.java:162)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at cloud.datatp.fforwarder.core.db.CRMDAOTemplatePrimary$$SpringCGLIB$$0.sqlSelect(<generated>)
	at cloud.datatp.fforwarder.core.db.CRMSqlQueryUnitManager$QueryContext.createSqlSelectView(CRMSqlQueryUnitManager.java:53)
	at cloud.datatp.fforwarder.core.db.CRMDaoService.searchDbRecords(CRMDaoService.java:109)
	at cloud.datatp.fforwarder.sales.quotation.SpecificQuotationLogic.searchSpecificQuotations(SpecificQuotationLogic.java:289)
	at cloud.datatp.fforwarder.sales.quotation.QuotationService.searchSpecificQuotations(QuotationService.java:74)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at cloud.datatp.fforwarder.sales.quotation.QuotationService$$SpringCGLIB$$0.searchSpecificQuotations(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: UNION types character varying and double precision cannot be matched
  Position: 8354
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2725)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2412)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:371)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:502)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:419)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:194)
	at org.postgresql.jdbc.PgPreparedStatement.executeQuery(PgPreparedStatement.java:137)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeQuery(ProxyPreparedStatement.java:52)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeQuery(HikariProxyPreparedStatement.java)
	at org.springframework.jdbc.core.JdbcTemplate$1.doInPreparedStatement(JdbcTemplate.java:732)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:658)
	... 127 common frames omitted

2025-09-24T16:06:53.150+07:00  INFO 78709 --- [qtp1728158149-61] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint QuotationService/searchSpecificQuotations
2025-09-24T16:06:53.170+07:00 ERROR 78709 --- [qtp1728158149-41] n.d.m.monitor.call.EndpointCallContext   : Start call with component QuotationService, method searchSpecificQuotations, arguments
[ {
  "tenantId" : "default",
  "companyId" : 17,
  "companyParentId" : 4,
  "companyCode" : "beehcm",
  "companyLabel" : "Bee HCM",
  "companyFullName" : "BEE LOGISTICS CORPORATION - HO CHI MINH BRANCH OFFICE",
  "loginId" : "rio.vnsgn",
  "accountId" : 55989,
  "token" : "595025c2817cb490acbec03f16dabd18",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node0ov64amn1e0mm1pffer0d30x120",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : {
    "beehcm" : {
      "companyId" : 17,
      "companyParentId" : 4,
      "companyCode" : "beehcm",
      "companyLabel" : "Bee HCM",
      "companyFullName" : null
    }
  },
  "featurePermissions" : [ {
    "id" : 17605,
    "appId" : 3,
    "appModule" : "project",
    "appName" : "project",
    "companyId" : 17,
    "loginId" : "rio.vnsgn",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 12433,
    "appId" : 69,
    "appModule" : "hr",
    "appName" : "user-kpi",
    "companyId" : 17,
    "loginId" : "rio.vnsgn",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 13960,
    "appId" : 79,
    "appModule" : "company",
    "appName" : "user-asset",
    "companyId" : 17,
    "loginId" : "rio.vnsgn",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 11428,
    "appId" : 16,
    "appModule" : "logistics",
    "appName" : "user-logistics-sales",
    "companyId" : 17,
    "loginId" : "rio.vnsgn",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Owner",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 14870,
    "appId" : 80,
    "appModule" : "logistics",
    "appName" : "company-logistics-sales",
    "companyId" : 17,
    "loginId" : "rio.vnsgn",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Group",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 14873,
    "appId" : 6,
    "appModule" : "company",
    "appName" : "company-space",
    "companyId" : 17,
    "loginId" : "rio.vnsgn",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Read"
  }, {
    "id" : 11427,
    "appId" : 2,
    "appModule" : "user",
    "appName" : "my-employee-space",
    "companyId" : 17,
    "loginId" : "rio.vnsgn",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  } ],
  "attributes" : { },
  "clientId" : "default:rio.vnsgn"
}, {
  "parentId" : 0,
  "id" : 17,
  "code" : "beehcm",
  "label" : "Bee HCM",
  "fullName" : "BEE LOGISTICS CORPORATION - HO CHI MINH BRANCH OFFICE"
}, {
  "params" : {
    "space" : "User",
    "companyId" : 17,
    "accessAccountId" : 55989
  },
  "filters" : [ {
    "name" : "search",
    "filterType" : "String",
    "filterValue" : "",
    "required" : true
  } ],
  "optionFilters" : [ {
    "name" : "storageState",
    "filterType" : "NotSet",
    "required" : true,
    "multiple" : true,
    "options" : [ "ACTIVE", "ARCHIVED" ],
    "selectOptions" : [ "ACTIVE" ]
  } ],
  "rangeFilters" : [ {
    "name" : "requestDate",
    "filterType" : "NotSet",
    "required" : true,
    "fromValue" : "01/07/2025@00:00:00+0700",
    "toValue" : "30/09/2025@23:59:59+0700"
  } ],
  "maxReturn" : 1000
} ]
2025-09-24T16:06:53.171+07:00 ERROR 78709 --- [qtp1728158149-41] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:118)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.springframework.jdbc.BadSqlGrammarException: PreparedStatementCallback; bad SQL grammar [
              WITH filtered_requests AS (
                  -- Existing lgc_price_inquiry_request data
                  SELECT
                    c.id,
                    c.company_id,
                    c.code,
                    c.request_date,
                    c.status,
                    c.mode,
                    c.purpose,
                    c.type_of_shipment,
                    c.service_mode,
                    c.client_partner_type,
                    c.client_partner_id,
                    c.client_label,
                    c.saleman_account_id,
                    c.saleman_label,
                    c.saleman_email,
                    c.saleman_phone,
                    c.saleman_job_title,
                    c.saleman_branch_name,
                    c.pricing_date,
                    c.pricing_account_id,
                    c.pricing_label,
                    c.term_of_service,
                    c.cargo_ready_date,
                    c.is_multi_route,
                    c.from_location_code,
                    c.from_location_label,
                    c.to_location_code,
                    c.to_location_label,
                    c.pickup_address,
                    c.delivery_address,
                    c.target_rate,
                    c.note,
                    c.feedback,
                    c.pricing_note,
                    c.total_new_prices_count,
                    c.total_analysis_prices_count,
                    c.step_tracking,
                    c.total_step_counting,
                    c.job_tracking_status,
                    c.mail_subject,
                    c.mail_to,
                    c.mail_cc,
                    -- ShipmentDetail fields
                    c.dimension_length,
                    c.dimension_width,
                    c.dimension_height,
                    c.stackable,
                    c.package_quantity,
                    c.volume_info,
                    c.volume_cbm,
                    c.gross_weight_kg,
                    c.report_volume,
                    c.report_volume_unit,
                    c.commodity,
                    c.desc_of_goods,
                    c.dg_liquid_cargo,
                    c.buy_insurance_request,
                    c.express_courier,
                    c.cross_border_trucking,
                    c.special_request_note,
                    c.free_time_terminal_request,
                    c.vn_border_gate_request,
                    -- Additional fields
                    inquiry_quotation.final_destination AS final_destination,
                    q.id AS quotation_id,
                    b.id AS booking_id,
                    'INQUIRY_REQUEST' AS type
                  FROM lgc_price_inquiry_request c
                  LEFT JOIN (
                      SELECT
                          reference_code,
                          final_destination,
                          company_id,
                          id,
                          ROW_NUMBER() OVER (PARTITION BY reference_code, company_id ORDER BY id DESC) AS rn
                      FROM lgc_sales_specific_service_inquiry
                      WHERE EXISTS (
                          SELECT 1
                          FROM lgc_sales_specific_quotation q
                          WHERE q.inquiry_id = lgc_sales_specific_service_inquiry.id
                      )
                  ) inquiry_quotation
                      ON inquiry_quotation.reference_code = c.code
                      AND inquiry_quotation.company_id = c.company_id
                      AND inquiry_quotation.rn = 1
                  LEFT JOIN lgc_sales_specific_quotation q
                      ON q.inquiry_id = inquiry_quotation.id
                  LEFT JOIN (
                      SELECT
                          reference_code,
                          company_id,
                          id,
                          ROW_NUMBER() OVER (PARTITION BY reference_code, company_id ORDER BY id DESC) AS rn
                      FROM lgc_sales_specific_service_inquiry
                      WHERE EXISTS (
                          SELECT 1
                          FROM lgc_sales_booking b
                          WHERE b.inquiry_id = lgc_sales_specific_service_inquiry.id
                      )
                  ) inquiry_booking
                      ON inquiry_booking.reference_code = c.code
                      AND inquiry_booking.company_id = c.company_id
                      AND inquiry_booking.rn = 1
                      AND (inquiry_booking.id != inquiry_quotation.id OR inquiry_quotation.id IS NULL)
                  LEFT JOIN lgc_sales_booking b
                      ON b.inquiry_id = inquiry_booking.id
                  WHERE c.storage_state IN (?)
                    -- AND  FILTER_BY_OPTION('c.status', 'status' , userParams, options) 
                     AND (c.request_date >= ? AND c.request_date <= ?)
                    -- q.is_favorite = :isFavorite
                    AND (
                      'System' = ?
                      OR ('Company' = ? AND c.company_id = ?)
                      OR ('User' = ? AND c.saleman_account_id = ?)
                    )

                  UNION ALL

                  -- Add lgc_price_bulk_cargo_inquiry_request data
                  SELECT
                    bc.id,
                    bc.company_id,
                    bc.code,
                    bc.request_date,
                    bc.status,
                    'Bulk Cargo' AS mode,                    -- Constant value                           -- Constant for bulk cargo
                    bc.purpose,                                -- BulkCargo has purpose field
                    NULL AS type_of_shipment,                -- Not available in bulk cargo
                    NULL AS service_mode,                     -- Not available in bulk cargo
                    'CUSTOMERS' AS client_partner_type,        -- Default value
                    bc.client_partner_id,
                    bc.client_label,
                    bc.saleman_account_id,
                    bc.saleman_label,
                    bc.saleman_email,
                    bc.saleman_phone,
                    NULL AS saleman_job_title,               -- Not available in bulk cargo
                    bc.saleman_branch_name,
                    bc.pricing_date,
                    bc.pricing_account_id,
                    bc.pricing_label,
                    bc.term_of_service,        -- From embedded BulkCargoShipmentDetail
                    NULL AS cargo_ready_date,                 -- Bulk cargo uses laydays_date instead
                    false AS is_multi_route,                   -- Default false
                    bc.from_location_code,
                    bc.from_location_label,
                    bc.to_location_code,
                    bc.to_location_label,
                    NULL AS pickup_address,                   -- Not available in bulk cargo
                    NULL AS delivery_address,                 -- Not available in bulk cargo
                    bc.target_rate,            -- From embedded BulkCargoShipmentDetail
                    bc.note,
                    bc.feedback,
                    bc.pricing_note,
                    NULL AS total_new_prices_count,            -- Not available in bulk cargo
                    NULL AS total_analysis_prices_count,       -- Not available in bulk cargo
                    NULL AS step_tracking,                    -- Not available in bulk cargo
                    NULL AS total_step_counting,               -- Not available in bulk cargo
                    NULL AS job_tracking_status,              -- Not available in bulk cargo
                    bc.mail_subject,
                    bc.mail_to,
                    bc.mail_cc,
                    -- ShipmentDetail fields (mostly N/A for bulk cargo)
                    NULL AS dimension_length,                 -- Bulk cargo doesn't have dimensions
                    NULL AS dimension_width,
                    NULL AS dimension_height,
                    bc.stackable,              -- BulkCargo has stackable
                    NULL AS package_quantity,                 -- Bulk cargo uses quantity instead
                    bc.volume AS volume_info,                      -- Bulk cargo doesn't use container info
                    NULL AS volume_cbm,   -- Map volume to volume_cbm
                    NULL AS gross_weight_kg,                  -- Bulk cargo uses quantity in metric tons
                    bc.volume AS report_volume, -- Use volume as report
                    'CBM' AS report_volume_unit, -- Use unit as report unit
                    bc.commodity,
                    bc.desc_of_goods,
                    false AS dg_liquid_cargo,                  -- Default false
                    false AS buy_insurance_request,            -- Default false
                    false AS express_courier,                  -- Default false
                    false AS cross_border_trucking,            -- Default false
                    bc.cargo_proceeding AS special_request_note, -- Map cargo_proceeding
                    NULL AS free_time_terminal_request,       -- Not available in bulk cargo
                    NULL AS vn_border_gate_request,           -- Not available in bulk cargo
                    -- Additional fields
                    NULL AS final_destination,                 -- Bulk cargo doesn't have this
                    NULL AS quotation_id,                      -- Bulk cargo doesn't have quotations
                    NULL AS booking_id,                        -- Bulk cargo doesn't have bookings
                    'BULK_CARGO_INQUIRY_REQUEST' AS type
                  FROM lgc_price_bulk_cargo_inquiry_request bc
                  WHERE bc.storage_state IN (?)
                     AND (bc.request_date >= ? AND bc.request_date <= ?)
                    AND (
                      'System' = ?
                      OR ('Company' = ? AND bc.company_id = ?)
                      OR ('User' = ? AND bc.saleman_account_id = ?)
                    )
              )
              SELECT r.*
              FROM filtered_requests r
              ORDER BY r.request_date DESC
            ]
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:112)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.springframework.jdbc.core.JdbcTemplate.translateException(JdbcTemplate.java:1556)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:677)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:723)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:748)
	at org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate.query(NamedParameterJdbcTemplate.java:178)
	at org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate.query(NamedParameterJdbcTemplate.java:186)
	at net.datatp.module.data.db.repository.DAOTemplate.sqlSelect(DAOTemplate.java:162)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at cloud.datatp.fforwarder.core.db.CRMDAOTemplatePrimary$$SpringCGLIB$$0.sqlSelect(<generated>)
	at cloud.datatp.fforwarder.core.db.CRMSqlQueryUnitManager$QueryContext.createSqlSelectView(CRMSqlQueryUnitManager.java:53)
	at cloud.datatp.fforwarder.core.db.CRMDaoService.searchDbRecords(CRMDaoService.java:109)
	at cloud.datatp.fforwarder.sales.quotation.SpecificQuotationLogic.searchSpecificQuotations(SpecificQuotationLogic.java:289)
	at cloud.datatp.fforwarder.sales.quotation.QuotationService.searchSpecificQuotations(QuotationService.java:74)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at cloud.datatp.fforwarder.sales.quotation.QuotationService$$SpringCGLIB$$0.searchSpecificQuotations(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 93 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: UNION types character varying and double precision cannot be matched
  Position: 8354
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2725)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2412)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:371)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:502)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:419)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:194)
	at org.postgresql.jdbc.PgPreparedStatement.executeQuery(PgPreparedStatement.java:137)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeQuery(ProxyPreparedStatement.java:52)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeQuery(HikariProxyPreparedStatement.java)
	at org.springframework.jdbc.core.JdbcTemplate$1.doInPreparedStatement(JdbcTemplate.java:732)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:658)
	... 122 common frames omitted

2025-09-24T16:06:53.174+07:00  INFO 78709 --- [qtp1728158149-41] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint QuotationService/searchSpecificQuotations
2025-09-24T16:07:06.983+07:00  INFO 78709 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T16:07:15.012+07:00  INFO 78709 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T16:07:15.024+07:00  INFO 78709 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T16:08:00.615+07:00  INFO 78709 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@59801dfc{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-24T16:08:00.617+07:00  INFO 78709 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-24T16:08:00.617+07:00  INFO 78709 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-24T16:08:00.617+07:00  INFO 78709 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-24T16:08:00.618+07:00  INFO 78709 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-24T16:08:00.618+07:00  INFO 78709 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-24T16:08:00.618+07:00  INFO 78709 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-24T16:08:00.618+07:00  INFO 78709 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-24T16:08:00.618+07:00  INFO 78709 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-09-24T16:08:00.618+07:00  INFO 78709 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-09-24T16:08:00.618+07:00  INFO 78709 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-24T16:08:00.618+07:00  INFO 78709 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-09-24T16:08:00.618+07:00  INFO 78709 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-09-24T16:08:00.618+07:00  INFO 78709 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-24T16:08:00.618+07:00  INFO 78709 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-09-24T16:08:00.618+07:00  INFO 78709 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-09-24T16:08:00.618+07:00  INFO 78709 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T16:08:00.634+07:00  INFO 78709 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T16:08:00.705+07:00  INFO 78709 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-24T16:08:00.711+07:00  INFO 78709 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-24T16:08:00.728+07:00  INFO 78709 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-24T16:08:00.729+07:00  INFO 78709 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-24T16:08:00.731+07:00  INFO 78709 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-24T16:08:00.731+07:00  INFO 78709 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-24T16:08:00.733+07:00  INFO 78709 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-24T16:08:00.733+07:00  INFO 78709 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-24T16:08:00.733+07:00  INFO 78709 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-24T16:08:00.733+07:00  INFO 78709 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-24T16:08:00.733+07:00  INFO 78709 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-24T16:08:00.734+07:00  INFO 78709 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-24T16:08:00.734+07:00  INFO 78709 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-24T16:08:00.734+07:00  INFO 78709 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-24T16:08:00.734+07:00  INFO 78709 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-24T16:08:00.736+07:00  INFO 78709 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@39000894{STOPPING}[12.0.15,sto=0]
2025-09-24T16:08:00.738+07:00  INFO 78709 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-24T16:08:00.739+07:00  INFO 78709 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@21b05a8a{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.13259238187056187266/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@605a10fd{STOPPED}}
