2025-09-19T17:29:16.285+07:00  INFO 2853 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 2853 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-19T17:29:16.286+07:00  INFO 2853 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-19T17:29:16.976+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:29:17.041+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 61 ms. Found 22 JPA repository interfaces.
2025-09-19T17:29:17.049+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:29:17.051+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-19T17:29:17.051+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:29:17.096+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 44 ms. Found 10 JPA repository interfaces.
2025-09-19T17:29:17.097+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:29:17.100+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-19T17:29:17.110+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:29:17.116+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-09-19T17:29:17.127+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:29:17.129+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-19T17:29:17.131+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:29:17.135+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-19T17:29:17.140+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:29:17.146+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 5 JPA repository interfaces.
2025-09-19T17:29:17.152+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:29:17.162+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 3 JPA repository interfaces.
2025-09-19T17:29:17.163+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:29:17.163+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-19T17:29:17.164+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:29:17.179+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 14 ms. Found 10 JPA repository interfaces.
2025-09-19T17:29:17.184+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:29:17.187+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 3 JPA repository interfaces.
2025-09-19T17:29:17.192+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:29:17.197+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-09-19T17:29:17.197+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:29:17.207+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 12 JPA repository interfaces.
2025-09-19T17:29:17.207+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:29:17.211+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-09-19T17:29:17.211+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:29:17.212+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-19T17:29:17.212+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:29:17.214+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-19T17:29:17.214+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:29:17.219+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 7 JPA repository interfaces.
2025-09-19T17:29:17.220+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:29:17.222+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 2 JPA repository interfaces.
2025-09-19T17:29:17.222+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:29:17.222+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-19T17:29:17.223+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:29:17.235+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 12 ms. Found 19 JPA repository interfaces.
2025-09-19T17:29:17.247+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:29:17.255+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 8 JPA repository interfaces.
2025-09-19T17:29:17.255+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:29:17.259+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-09-19T17:29:17.259+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:29:17.264+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-09-19T17:29:17.264+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:29:17.271+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 9 JPA repository interfaces.
2025-09-19T17:29:17.271+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:29:17.276+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-09-19T17:29:17.276+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:29:17.286+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 13 JPA repository interfaces.
2025-09-19T17:29:17.286+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:29:17.296+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 14 JPA repository interfaces.
2025-09-19T17:29:17.297+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:29:17.315+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 17 ms. Found 24 JPA repository interfaces.
2025-09-19T17:29:17.315+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:29:17.317+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-19T17:29:17.322+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:29:17.323+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-19T17:29:17.323+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:29:17.332+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 12 JPA repository interfaces.
2025-09-19T17:29:17.335+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:29:17.390+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 54 ms. Found 66 JPA repository interfaces.
2025-09-19T17:29:17.390+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:29:17.391+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-19T17:29:17.396+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-19T17:29:17.400+07:00  INFO 2853 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-09-19T17:29:17.604+07:00  INFO 2853 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-19T17:29:17.608+07:00  INFO 2853 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-19T17:29:17.891+07:00  WARN 2853 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-19T17:29:18.100+07:00  INFO 2853 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-19T17:29:18.103+07:00  INFO 2853 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-19T17:29:18.114+07:00  INFO 2853 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-19T17:29:18.115+07:00  INFO 2853 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1726 ms
2025-09-19T17:29:18.202+07:00  WARN 2853 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-19T17:29:18.202+07:00  INFO 2853 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-19T17:29:18.314+07:00  INFO 2853 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@2fbc704a
2025-09-19T17:29:18.315+07:00  INFO 2853 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-19T17:29:18.320+07:00  WARN 2853 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-19T17:29:18.320+07:00  INFO 2853 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-19T17:29:18.325+07:00  INFO 2853 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@3701acee
2025-09-19T17:29:18.326+07:00  INFO 2853 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-19T17:29:18.326+07:00  WARN 2853 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-19T17:29:18.326+07:00  INFO 2853 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-19T17:29:18.432+07:00  INFO 2853 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@467f97e
2025-09-19T17:29:18.432+07:00  INFO 2853 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-19T17:29:18.432+07:00  WARN 2853 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-19T17:29:18.432+07:00  INFO 2853 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-19T17:29:18.442+07:00  INFO 2853 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@107ac2b6
2025-09-19T17:29:18.442+07:00  INFO 2853 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-19T17:29:18.442+07:00  WARN 2853 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-19T17:29:18.442+07:00  INFO 2853 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-19T17:29:18.450+07:00  INFO 2853 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@198fe398
2025-09-19T17:29:18.450+07:00  INFO 2853 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-19T17:29:18.450+07:00  INFO 2853 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-19T17:29:18.494+07:00  INFO 2853 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-19T17:29:18.547+07:00  INFO 2853 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@1ba84d52{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.15915829572221899649/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@42bd4a4c{STARTED}}
2025-09-19T17:29:18.548+07:00  INFO 2853 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@1ba84d52{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.15915829572221899649/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@42bd4a4c{STARTED}}
2025-09-19T17:29:18.550+07:00  INFO 2853 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@391cc6cf{STARTING}[12.0.15,sto=0] @2904ms
2025-09-19T17:29:18.618+07:00  INFO 2853 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-19T17:29:18.648+07:00  INFO 2853 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-19T17:29:18.664+07:00  INFO 2853 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-19T17:29:18.796+07:00  INFO 2853 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-19T17:29:18.824+07:00  WARN 2853 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-19T17:29:19.471+07:00  INFO 2853 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-19T17:29:19.479+07:00  INFO 2853 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@39fa3fc5] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-19T17:29:19.615+07:00  INFO 2853 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T17:29:19.823+07:00  INFO 2853 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.monitor.activity", "net.datatp.module.wfms", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "cloud.datatp.module.fleet", "net.datatp.module.okr", "net.datatp.module.core.template", "cloud.datatp.module.pegasus.shipment", "cloud.datatp.gps", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "net.datatp.module.company.tmpl", "cloud.datatp.tms", "net.datatp.module.groovy", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "net.datatp.module.resource", "cloud.datatp.vendor", "net.datatp.module.company.hr", "net.datatp.module.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.chatbot", "cloud.datatp.module.odoo", "net.datatp.module.core.print", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-09-19T17:29:19.825+07:00  INFO 2853 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-19T17:29:19.831+07:00  INFO 2853 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-19T17:29:19.832+07:00  INFO 2853 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-19T17:29:19.859+07:00  INFO 2853 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-19T17:29:19.862+07:00  WARN 2853 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-19T17:29:22.211+07:00  INFO 2853 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-19T17:29:22.212+07:00  INFO 2853 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@67d7f8bc] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-19T17:29:22.378+07:00  WARN 2853 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-19T17:29:22.378+07:00  WARN 2853 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-19T17:29:22.385+07:00  WARN 2853 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-19T17:29:22.385+07:00  WARN 2853 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-19T17:29:22.398+07:00  WARN 2853 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-19T17:29:22.398+07:00  WARN 2853 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-19T17:29:22.769+07:00  INFO 2853 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T17:29:22.776+07:00  INFO 2853 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-19T17:29:22.777+07:00  INFO 2853 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-19T17:29:22.802+07:00  INFO 2853 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-19T17:29:22.804+07:00  WARN 2853 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-19T17:29:23.347+07:00  INFO 2853 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-19T17:29:23.348+07:00  INFO 2853 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@276535b3] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-19T17:29:23.405+07:00  WARN 2853 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-19T17:29:23.405+07:00  WARN 2853 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-19T17:29:23.750+07:00  INFO 2853 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T17:29:23.782+07:00  INFO 2853 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-19T17:29:23.787+07:00  INFO 2853 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-19T17:29:23.787+07:00  INFO 2853 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T17:29:23.794+07:00  WARN 2853 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-19T17:29:23.936+07:00  INFO 2853 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-19T17:29:24.406+07:00  INFO 2853 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-19T17:29:24.410+07:00  INFO 2853 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-19T17:29:24.445+07:00  INFO 2853 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-19T17:29:24.492+07:00  INFO 2853 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-19T17:29:24.584+07:00  INFO 2853 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-19T17:29:24.615+07:00  INFO 2853 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-19T17:29:24.647+07:00  INFO 2853 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 345028750ms : this is harmless.
2025-09-19T17:29:24.658+07:00  INFO 2853 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-19T17:29:24.661+07:00  INFO 2853 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-19T17:29:24.683+07:00  INFO 2853 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 636534707ms : this is harmless.
2025-09-19T17:29:24.684+07:00  INFO 2853 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-19T17:29:24.715+07:00  INFO 2853 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-19T17:29:24.716+07:00  INFO 2853 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-19T17:29:26.770+07:00  INFO 2853 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-19T17:29:26.770+07:00  INFO 2853 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T17:29:26.771+07:00  WARN 2853 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-19T17:29:27.098+07:00  INFO 2853 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 19/09/2025@17:15:00+0700 to 19/09/2025@17:30:00+0700
2025-09-19T17:29:27.098+07:00  INFO 2853 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 19/09/2025@17:15:00+0700 to 19/09/2025@17:30:00+0700
2025-09-19T17:29:27.708+07:00  INFO 2853 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-09-19T17:29:27.708+07:00  INFO 2853 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-19T17:29:27.709+07:00  WARN 2853 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-19T17:29:28.024+07:00  INFO 2853 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-09-19T17:29:28.024+07:00  INFO 2853 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-09-19T17:29:28.024+07:00  INFO 2853 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-09-19T17:29:28.024+07:00  INFO 2853 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-09-19T17:29:28.024+07:00  INFO 2853 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-09-19T17:29:30.397+07:00  WARN 2853 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 22697dc1-286b-4862-80e3-2ddb927b24a0

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-19T17:29:30.401+07:00  INFO 2853 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-09-19T17:29:30.717+07:00  INFO 2853 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-19T17:29:30.717+07:00  INFO 2853 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-19T17:29:30.717+07:00  INFO 2853 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-19T17:29:30.718+07:00  INFO 2853 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-19T17:29:30.718+07:00  INFO 2853 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-09-19T17:29:30.718+07:00  INFO 2853 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-09-19T17:29:30.718+07:00  INFO 2853 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-19T17:29:30.718+07:00  INFO 2853 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-09-19T17:29:30.718+07:00  INFO 2853 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-09-19T17:29:30.718+07:00  INFO 2853 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-19T17:29:30.718+07:00  INFO 2853 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-09-19T17:29:30.718+07:00  INFO 2853 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-09-19T17:29:30.719+07:00  INFO 2853 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-19T17:29:30.719+07:00  INFO 2853 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-19T17:29:30.719+07:00  INFO 2853 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-19T17:29:30.793+07:00  INFO 2853 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-19T17:29:30.793+07:00  INFO 2853 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-19T17:29:30.795+07:00  INFO 2853 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-09-19T17:29:30.815+07:00  INFO 2853 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@33630c98{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-19T17:29:30.816+07:00  INFO 2853 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-09-19T17:29:30.817+07:00  INFO 2853 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-09-19T17:29:30.922+07:00  INFO 2853 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-09-19T17:29:30.922+07:00  INFO 2853 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-09-19T17:29:30.929+07:00  INFO 2853 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 15.046 seconds (process running for 15.283)
2025-09-19T17:30:05.799+07:00  INFO 2853 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-19T17:30:05.805+07:00  INFO 2853 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T17:30:05.806+07:00  INFO 2853 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-19T17:30:05.808+07:00  INFO 2853 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 19/09/2025@17:30:05+0700
2025-09-19T17:30:05.815+07:00  INFO 2853 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 19/09/2025@17:30:00+0700 to 19/09/2025@17:45:00+0700
2025-09-19T17:30:05.816+07:00  INFO 2853 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 19/09/2025@17:30:00+0700 to 19/09/2025@17:45:00+0700
2025-09-19T17:30:19.096+07:00  INFO 2853 --- [qtp1306610776-36] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-09-19T17:30:28.797+07:00  INFO 2853 --- [qtp1306610776-41] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node018g8wwrrs53deaxpm2oduk5rn1
2025-09-19T17:30:28.797+07:00  INFO 2853 --- [qtp1306610776-35] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0nuq2o6yjsr96sgkvqed446yp0
2025-09-19T17:30:28.891+07:00  INFO 2853 --- [qtp1306610776-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0nuq2o6yjsr96sgkvqed446yp0, token = 5472f488fc76ba0d478370fd8ee5a916
2025-09-19T17:30:28.893+07:00  INFO 2853 --- [qtp1306610776-41] n.d.module.session.ClientSessionManager  : Add a client session id = node018g8wwrrs53deaxpm2oduk5rn1, token = 5472f488fc76ba0d478370fd8ee5a916
2025-09-19T17:30:29.040+07:00  INFO 2853 --- [qtp1306610776-35] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-19T17:30:29.070+07:00  INFO 2853 --- [qtp1306610776-41] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-19T17:30:33.911+07:00  INFO 2853 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-09-19T17:30:33.919+07:00  INFO 2853 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T17:30:57.714+07:00  INFO 2853 --- [qtp1306610776-39] n.d.module.session.ClientSessionManager  : Add a client session id = node018g8wwrrs53deaxpm2oduk5rn1, token = 5472f488fc76ba0d478370fd8ee5a916
2025-09-19T17:30:57.715+07:00  INFO 2853 --- [qtp1306610776-64] n.d.module.session.ClientSessionManager  : Add a client session id = node018g8wwrrs53deaxpm2oduk5rn1, token = 5472f488fc76ba0d478370fd8ee5a916
2025-09-19T17:30:57.722+07:00  INFO 2853 --- [qtp1306610776-39] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-19T17:30:57.725+07:00  INFO 2853 --- [qtp1306610776-64] n.d.m.c.a.CompanyAuthenticationService   : User minhtv is logged in successfully system
2025-09-19T17:31:06.976+07:00  INFO 2853 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T17:32:05.091+07:00  INFO 2853 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T17:32:33.170+07:00  INFO 2853 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-09-19T17:32:33.177+07:00  INFO 2853 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T17:33:06.246+07:00  INFO 2853 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T17:34:04.331+07:00  INFO 2853 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T17:34:37.411+07:00  INFO 2853 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-19T17:34:37.425+07:00  INFO 2853 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T17:35:06.480+07:00  INFO 2853 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T17:35:06.481+07:00  INFO 2853 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-19T17:36:03.582+07:00  INFO 2853 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T17:36:37.653+07:00  INFO 2853 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-09-19T17:36:37.667+07:00  INFO 2853 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T17:37:06.723+07:00  INFO 2853 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-19T17:37:19.085+07:00  INFO 2853 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@33630c98{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-19T17:37:19.086+07:00  INFO 2853 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-19T17:37:19.087+07:00  INFO 2853 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-19T17:37:19.087+07:00  INFO 2853 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-19T17:37:19.087+07:00  INFO 2853 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-19T17:37:19.087+07:00  INFO 2853 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-19T17:37:19.087+07:00  INFO 2853 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-19T17:37:19.087+07:00  INFO 2853 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-19T17:37:19.087+07:00  INFO 2853 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-09-19T17:37:19.087+07:00  INFO 2853 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-09-19T17:37:19.087+07:00  INFO 2853 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-19T17:37:19.087+07:00  INFO 2853 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-09-19T17:37:19.087+07:00  INFO 2853 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-09-19T17:37:19.087+07:00  INFO 2853 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-19T17:37:19.087+07:00  INFO 2853 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-09-19T17:37:19.087+07:00  INFO 2853 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-09-19T17:37:19.102+07:00  INFO 2853 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-19T17:37:19.211+07:00  INFO 2853 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-19T17:37:19.216+07:00  INFO 2853 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-19T17:37:19.240+07:00  INFO 2853 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T17:37:19.244+07:00  INFO 2853 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T17:37:19.245+07:00  INFO 2853 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-19T17:37:19.246+07:00  INFO 2853 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-19T17:37:19.247+07:00  INFO 2853 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-19T17:37:19.247+07:00  INFO 2853 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-19T17:37:19.247+07:00  INFO 2853 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-19T17:37:19.247+07:00  INFO 2853 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-19T17:37:19.248+07:00  INFO 2853 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-19T17:37:19.248+07:00  INFO 2853 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-19T17:37:19.248+07:00  INFO 2853 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-19T17:37:19.248+07:00  INFO 2853 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-19T17:37:19.248+07:00  INFO 2853 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-19T17:37:19.250+07:00  INFO 2853 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@391cc6cf{STOPPING}[12.0.15,sto=0]
2025-09-19T17:37:19.253+07:00  INFO 2853 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-19T17:37:19.254+07:00  INFO 2853 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@1ba84d52{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.15915829572221899649/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@42bd4a4c{STOPPED}}
