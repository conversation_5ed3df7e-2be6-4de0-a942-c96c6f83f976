2025-09-17T13:32:42.961+07:00  INFO 40177 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 40177 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-17T13:32:42.962+07:00  INFO 40177 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-17T13:32:43.779+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T13:32:43.865+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 82 ms. Found 22 JPA repository interfaces.
2025-09-17T13:32:43.876+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T13:32:43.877+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-17T13:32:43.877+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T13:32:43.938+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 60 ms. Found 10 JPA repository interfaces.
2025-09-17T13:32:43.939+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T13:32:43.943+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 3 JPA repository interfaces.
2025-09-17T13:32:43.952+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T13:32:43.958+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 1 JPA repository interface.
2025-09-17T13:32:43.967+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T13:32:43.969+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 2 JPA repository interfaces.
2025-09-17T13:32:43.970+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T13:32:43.973+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-17T13:32:43.976+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T13:32:43.980+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 5 JPA repository interfaces.
2025-09-17T13:32:43.984+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T13:32:43.986+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-17T13:32:43.987+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T13:32:43.987+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-17T13:32:43.987+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T13:32:43.994+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 10 JPA repository interfaces.
2025-09-17T13:32:43.998+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T13:32:44.001+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-17T13:32:44.004+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T13:32:44.007+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-17T13:32:44.008+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T13:32:44.014+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-17T13:32:44.015+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T13:32:44.017+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-17T13:32:44.018+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T13:32:44.018+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-17T13:32:44.018+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T13:32:44.019+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-17T13:32:44.019+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T13:32:44.023+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-09-17T13:32:44.023+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T13:32:44.025+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-17T13:32:44.025+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T13:32:44.025+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-17T13:32:44.025+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T13:32:44.036+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 19 JPA repository interfaces.
2025-09-17T13:32:44.045+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T13:32:44.051+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 8 JPA repository interfaces.
2025-09-17T13:32:44.051+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T13:32:44.055+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-09-17T13:32:44.055+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T13:32:44.059+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-09-17T13:32:44.059+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T13:32:44.065+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-09-17T13:32:44.065+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T13:32:44.068+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-17T13:32:44.069+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T13:32:44.077+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 13 JPA repository interfaces.
2025-09-17T13:32:44.077+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T13:32:44.086+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 14 JPA repository interfaces.
2025-09-17T13:32:44.086+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T13:32:44.101+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 14 ms. Found 24 JPA repository interfaces.
2025-09-17T13:32:44.101+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T13:32:44.102+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-17T13:32:44.107+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T13:32:44.108+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-17T13:32:44.108+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T13:32:44.116+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-09-17T13:32:44.118+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T13:32:44.155+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 36 ms. Found 66 JPA repository interfaces.
2025-09-17T13:32:44.155+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T13:32:44.156+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-17T13:32:44.160+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-17T13:32:44.165+07:00  INFO 40177 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 4 JPA repository interfaces.
2025-09-17T13:32:44.358+07:00  INFO 40177 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-17T13:32:44.362+07:00  INFO 40177 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-17T13:32:44.640+07:00  WARN 40177 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-17T13:32:44.849+07:00  INFO 40177 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-17T13:32:44.852+07:00  INFO 40177 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-17T13:32:44.864+07:00  INFO 40177 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-17T13:32:44.864+07:00  INFO 40177 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1803 ms
2025-09-17T13:32:44.915+07:00  WARN 40177 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-17T13:32:44.916+07:00  INFO 40177 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-17T13:32:45.082+07:00  INFO 40177 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@4b75d640
2025-09-17T13:32:45.083+07:00  INFO 40177 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-17T13:32:45.087+07:00  WARN 40177 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-17T13:32:45.088+07:00  INFO 40177 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-17T13:32:45.093+07:00  INFO 40177 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@398f6622
2025-09-17T13:32:45.093+07:00  INFO 40177 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-17T13:32:45.093+07:00  WARN 40177 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-17T13:32:45.093+07:00  INFO 40177 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-17T13:32:45.100+07:00  INFO 40177 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@9fe1e14
2025-09-17T13:32:45.100+07:00  INFO 40177 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-17T13:32:45.101+07:00  WARN 40177 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-17T13:32:45.101+07:00  INFO 40177 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-17T13:32:45.108+07:00  INFO 40177 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@2806c788
2025-09-17T13:32:45.108+07:00  INFO 40177 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-17T13:32:45.108+07:00  WARN 40177 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-17T13:32:45.108+07:00  INFO 40177 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-17T13:32:45.122+07:00  INFO 40177 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@718212ef
2025-09-17T13:32:45.122+07:00  INFO 40177 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-17T13:32:45.122+07:00  INFO 40177 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-17T13:32:45.164+07:00  INFO 40177 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-17T13:32:45.166+07:00  INFO 40177 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@ae5eeee{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.9892372898702647324/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@5796ae15{STARTED}}
2025-09-17T13:32:45.216+07:00  INFO 40177 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@ae5eeee{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.9892372898702647324/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@5796ae15{STARTED}}
2025-09-17T13:32:45.219+07:00  INFO 40177 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@5ca8157c{STARTING}[12.0.15,sto=0] @2899ms
2025-09-17T13:32:45.272+07:00  INFO 40177 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-17T13:32:45.297+07:00  INFO 40177 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-17T13:32:45.312+07:00  INFO 40177 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-17T13:32:45.433+07:00  INFO 40177 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-17T13:32:45.473+07:00  WARN 40177 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-17T13:32:46.082+07:00  INFO 40177 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-17T13:32:46.090+07:00  INFO 40177 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@76d28406] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-17T13:32:46.244+07:00  INFO 40177 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-17T13:32:46.455+07:00  INFO 40177 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.graphapi", "net.datatp.module.kpi", "net.datatp.module.monitor.activity", "net.datatp.module.wfms", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "cloud.datatp.module.fleet", "net.datatp.module.okr", "net.datatp.module.core.template", "cloud.datatp.gps", "cloud.datatp.module.pegasus.shipment", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "net.datatp.module.company.tmpl", "cloud.datatp.tms", "net.datatp.module.groovy", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "cloud.datatp.vendor", "net.datatp.module.resource", "net.datatp.module.hr", "net.datatp.module.company.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.chatbot", "net.datatp.module.core.print", "cloud.datatp.module.odoo", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.zalo", "net.datatp.module.accounting" ]
2025-09-17T13:32:46.457+07:00  INFO 40177 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-17T13:32:46.464+07:00  INFO 40177 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-17T13:32:46.465+07:00  INFO 40177 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-17T13:32:46.490+07:00  INFO 40177 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-17T13:32:46.499+07:00  WARN 40177 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-17T13:32:48.581+07:00  INFO 40177 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-17T13:32:48.583+07:00  INFO 40177 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@1aff44c8] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-17T13:32:48.801+07:00  WARN 40177 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-17T13:32:48.801+07:00  WARN 40177 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-17T13:32:48.812+07:00  WARN 40177 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-17T13:32:48.812+07:00  WARN 40177 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-17T13:32:48.825+07:00  WARN 40177 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-17T13:32:48.825+07:00  WARN 40177 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-17T13:32:49.274+07:00  INFO 40177 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-17T13:32:49.281+07:00  INFO 40177 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-17T13:32:49.283+07:00  INFO 40177 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-17T13:32:49.306+07:00  INFO 40177 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-17T13:32:49.311+07:00  WARN 40177 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-17T13:32:49.886+07:00  INFO 40177 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-17T13:32:49.887+07:00  INFO 40177 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@6827d46b] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-17T13:32:49.993+07:00  WARN 40177 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-17T13:32:49.993+07:00  WARN 40177 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-17T13:32:50.345+07:00  INFO 40177 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-17T13:32:50.383+07:00  INFO 40177 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-17T13:32:50.388+07:00  INFO 40177 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-17T13:32:50.388+07:00  INFO 40177 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T13:32:50.396+07:00  WARN 40177 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-17T13:32:50.551+07:00  INFO 40177 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-17T13:32:51.129+07:00  INFO 40177 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-17T13:32:51.133+07:00  INFO 40177 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-17T13:32:51.169+07:00  INFO 40177 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-17T13:32:51.214+07:00  INFO 40177 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-17T13:32:51.287+07:00  INFO 40177 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-17T13:32:51.315+07:00  INFO 40177 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-17T13:32:51.355+07:00  INFO 40177 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 151913532ms : this is harmless.
2025-09-17T13:32:51.364+07:00  INFO 40177 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-17T13:32:51.367+07:00  INFO 40177 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-17T13:32:51.381+07:00  INFO 40177 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 443419488ms : this is harmless.
2025-09-17T13:32:51.382+07:00  INFO 40177 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-17T13:32:51.396+07:00  INFO 40177 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-17T13:32:51.396+07:00  INFO 40177 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-17T13:32:53.315+07:00  INFO 40177 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-17T13:32:53.315+07:00  INFO 40177 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T13:32:53.316+07:00  WARN 40177 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-17T13:32:53.616+07:00  INFO 40177 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 17/09/2025@13:30:00+0700 to 17/09/2025@13:45:00+0700
2025-09-17T13:32:53.616+07:00  INFO 40177 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 17/09/2025@13:30:00+0700 to 17/09/2025@13:45:00+0700
2025-09-17T13:32:54.129+07:00  INFO 40177 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-09-17T13:32:54.129+07:00  INFO 40177 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T13:32:54.130+07:00  WARN 40177 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-17T13:32:54.409+07:00  INFO 40177 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-09-17T13:32:54.409+07:00  INFO 40177 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-09-17T13:32:54.409+07:00  INFO 40177 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-09-17T13:32:54.409+07:00  INFO 40177 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-09-17T13:32:54.409+07:00  INFO 40177 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-09-17T13:32:56.258+07:00  WARN 40177 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 6338daa3-1276-4bb0-a4ce-5aeefa60e0dc

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-17T13:32:56.262+07:00  INFO 40177 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-09-17T13:32:56.576+07:00  INFO 40177 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-17T13:32:56.577+07:00  INFO 40177 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-17T13:32:56.577+07:00  INFO 40177 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-17T13:32:56.577+07:00  INFO 40177 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-17T13:32:56.577+07:00  INFO 40177 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-09-17T13:32:56.577+07:00  INFO 40177 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-09-17T13:32:56.577+07:00  INFO 40177 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-17T13:32:56.577+07:00  INFO 40177 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-09-17T13:32:56.577+07:00  INFO 40177 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-09-17T13:32:56.577+07:00  INFO 40177 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-17T13:32:56.577+07:00  INFO 40177 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-09-17T13:32:56.577+07:00  INFO 40177 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-09-17T13:32:56.579+07:00  INFO 40177 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-17T13:32:56.580+07:00  INFO 40177 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-17T13:32:56.580+07:00  INFO 40177 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-17T13:32:56.647+07:00  INFO 40177 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-17T13:32:56.647+07:00  INFO 40177 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-17T13:32:56.649+07:00  INFO 40177 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-09-17T13:32:56.656+07:00  INFO 40177 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@38a26a2f{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-17T13:32:56.657+07:00  INFO 40177 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-09-17T13:32:56.658+07:00  INFO 40177 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-09-17T13:32:56.699+07:00  INFO 40177 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-09-17T13:32:56.699+07:00  INFO 40177 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-09-17T13:32:56.705+07:00  INFO 40177 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 14.012 seconds (process running for 14.385)
2025-09-17T13:33:03.605+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T13:33:33.791+07:00  INFO 40177 --- [qtp1830557131-36] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-09-17T13:33:59.754+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T13:33:59.774+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T13:34:06.788+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T13:35:02.904+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T13:35:02.906+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T13:35:26.879+07:00  INFO 40177 --- [qtp1830557131-41] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01y270lcbf91w6e6ewh1003y3b0
2025-09-17T13:35:26.879+07:00  INFO 40177 --- [qtp1830557131-60] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node05retmcp45wwu1hsv5q32wbbg71
2025-09-17T13:35:26.992+07:00  INFO 40177 --- [qtp1830557131-60] n.d.module.session.ClientSessionManager  : Add a client session id = node05retmcp45wwu1hsv5q32wbbg71, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T13:35:27.012+07:00  INFO 40177 --- [qtp1830557131-41] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T13:35:27.502+07:00  INFO 40177 --- [qtp1830557131-41] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T13:35:27.508+07:00  INFO 40177 --- [qtp1830557131-60] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T13:35:59.042+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 0
2025-09-17T13:35:59.056+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T13:36:06.069+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T13:36:56.144+07:00  INFO 40177 --- [qtp1830557131-60] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T13:36:56.145+07:00  INFO 40177 --- [qtp1830557131-41] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T13:36:56.158+07:00  INFO 40177 --- [qtp1830557131-41] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T13:36:56.158+07:00  INFO 40177 --- [qtp1830557131-60] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T13:37:02.162+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T13:37:52.345+07:00  INFO 40177 --- [qtp1830557131-62] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T13:37:52.346+07:00  INFO 40177 --- [qtp1830557131-40] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T13:37:52.351+07:00  INFO 40177 --- [qtp1830557131-62] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T13:37:52.352+07:00  INFO 40177 --- [qtp1830557131-40] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T13:38:03.301+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-17T13:38:03.306+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T13:38:05.316+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T13:39:06.406+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T13:40:03.526+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T13:40:03.534+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T13:40:04.540+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T13:40:04.541+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T13:41:06.616+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T13:41:25.014+07:00  INFO 40177 --- [qtp1830557131-37] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T13:41:25.015+07:00  INFO 40177 --- [qtp1830557131-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T13:41:25.028+07:00  INFO 40177 --- [qtp1830557131-37] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T13:41:25.028+07:00  INFO 40177 --- [qtp1830557131-36] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T13:41:27.387+07:00  INFO 40177 --- [qtp1830557131-60] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T13:41:27.406+07:00  INFO 40177 --- [qtp1830557131-60] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T13:41:27.420+07:00  INFO 40177 --- [qtp1830557131-34] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T13:41:27.427+07:00  INFO 40177 --- [qtp1830557131-34] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T13:41:38.248+07:00  INFO 40177 --- [qtp1830557131-60] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T13:41:38.248+07:00  INFO 40177 --- [qtp1830557131-64] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T13:41:38.361+07:00  INFO 40177 --- [qtp1830557131-64] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-17T13:41:38.361+07:00  INFO 40177 --- [qtp1830557131-60] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 2 records
2025-09-17T13:41:39.390+07:00  INFO 40177 --- [qtp1830557131-36] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T13:41:39.390+07:00  INFO 40177 --- [qtp1830557131-40] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T13:41:39.793+07:00  INFO 40177 --- [qtp1830557131-64] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T13:41:39.799+07:00  INFO 40177 --- [qtp1830557131-60] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T13:42:03.336+07:00  INFO 40177 --- [qtp1830557131-66] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T13:42:03.360+07:00  INFO 40177 --- [qtp1830557131-67] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T13:42:03.431+07:00  INFO 40177 --- [qtp1830557131-66] c.d.f.s.report.PerformanceReportLogic    : Retrieved 17 records
2025-09-17T13:42:03.431+07:00  INFO 40177 --- [qtp1830557131-67] c.d.f.s.report.PerformanceReportLogic    : Retrieved 17 records
2025-09-17T13:42:03.445+07:00  INFO 40177 --- [qtp1830557131-34] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T13:42:03.445+07:00  INFO 40177 --- [qtp1830557131-35] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T13:42:03.738+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 16, expire count 0
2025-09-17T13:42:03.764+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-17T13:42:03.766+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T13:42:03.907+07:00  INFO 40177 --- [qtp1830557131-34] c.d.f.s.report.PerformanceReportLogic    : Retrieved 21 records
2025-09-17T13:42:03.907+07:00  INFO 40177 --- [qtp1830557131-35] c.d.f.s.report.PerformanceReportLogic    : Retrieved 21 records
2025-09-17T13:42:45.186+07:00  INFO 40177 --- [Scheduler-885136436-1] n.d.m.session.AppHttpSessionListener     : The session node05retmcp45wwu1hsv5q32wbbg71 is destroyed.
2025-09-17T13:43:00.387+07:00  INFO 40177 --- [qtp1830557131-60] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint CRMPartnerService/searchBFSOnePartners
2025-09-17T13:43:00.387+07:00  INFO 40177 --- [qtp1830557131-64] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint CRMPartnerService/searchBFSOnePartners
2025-09-17T13:43:01.157+07:00  INFO 40177 --- [qtp1830557131-40] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T13:43:01.158+07:00  INFO 40177 --- [qtp1830557131-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T13:43:01.173+07:00  INFO 40177 --- [qtp1830557131-36] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T13:43:01.173+07:00  INFO 40177 --- [qtp1830557131-40] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T13:43:06.873+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T13:44:02.998+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 1
2025-09-17T13:44:03.001+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T13:44:03.001+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T13:45:06.113+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T13:45:06.113+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T13:45:06.113+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-17T13:45:06.114+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 17/09/2025@13:45:06+0700
2025-09-17T13:45:06.123+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 1 messages for session 17/09/2025@13:45:00+0700 to 17/09/2025@14:00:00+0700
2025-09-17T13:45:06.123+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 📤 message : MAIL - inquiry-overdue-reminder - scheduled at 17/09/2025@13:59:50+0700
2025-09-17T13:45:06.123+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 1 messages for session 17/09/2025@13:45:00+0700 to 17/09/2025@14:00:00+0700
2025-09-17T13:46:02.280+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 13, expire count 0
2025-09-17T13:46:02.295+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T13:46:02.296+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T13:47:05.405+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T13:47:46.925+07:00  INFO 40177 --- [qtp1830557131-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T13:47:46.940+07:00  INFO 40177 --- [qtp1830557131-36] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T13:47:46.951+07:00  INFO 40177 --- [qtp1830557131-35] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T13:47:46.966+07:00  INFO 40177 --- [qtp1830557131-35] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T13:48:01.498+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T13:48:01.504+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T13:48:06.510+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T13:48:47.228+07:00  INFO 40177 --- [qtp1830557131-40] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T13:48:47.241+07:00  INFO 40177 --- [qtp1830557131-40] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T13:48:47.255+07:00  INFO 40177 --- [qtp1830557131-72] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T13:48:47.263+07:00  INFO 40177 --- [qtp1830557131-72] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T13:48:53.713+07:00  INFO 40177 --- [qtp1830557131-60] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T13:48:53.714+07:00  INFO 40177 --- [qtp1830557131-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T13:48:53.725+07:00  INFO 40177 --- [qtp1830557131-60] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T13:48:53.727+07:00  INFO 40177 --- [qtp1830557131-36] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T13:49:04.616+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T13:50:00.726+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-17T13:50:00.737+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T13:50:06.746+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T13:50:06.747+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T13:51:03.847+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T13:51:59.948+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 8
2025-09-17T13:51:59.961+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T13:52:06.974+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T13:52:10.475+07:00  INFO 40177 --- [qtp1830557131-34] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T13:52:10.538+07:00  INFO 40177 --- [qtp1830557131-34] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T13:52:10.584+07:00  INFO 40177 --- [qtp1830557131-39] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T13:52:10.588+07:00  INFO 40177 --- [qtp1830557131-39] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T13:53:03.071+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T13:53:47.138+07:00  INFO 40177 --- [qtp1830557131-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T13:53:47.171+07:00  INFO 40177 --- [qtp1830557131-64] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T13:53:47.176+07:00  INFO 40177 --- [qtp1830557131-36] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T13:53:47.178+07:00  INFO 40177 --- [qtp1830557131-64] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T13:53:59.176+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 9, expire count 6
2025-09-17T13:53:59.199+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T13:54:06.215+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T13:55:02.325+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T13:55:02.327+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T13:55:15.863+07:00  INFO 40177 --- [qtp1830557131-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T13:55:15.865+07:00  INFO 40177 --- [qtp1830557131-72] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T13:55:15.876+07:00  INFO 40177 --- [qtp1830557131-72] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T13:55:15.875+07:00  INFO 40177 --- [qtp1830557131-36] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T13:55:21.793+07:00  INFO 40177 --- [qtp1830557131-60] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T13:55:21.794+07:00  INFO 40177 --- [qtp1830557131-72] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T13:55:21.799+07:00  INFO 40177 --- [qtp1830557131-60] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T13:55:21.799+07:00  INFO 40177 --- [qtp1830557131-72] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T13:55:26.008+07:00  INFO 40177 --- [qtp1830557131-64] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T13:55:26.010+07:00  INFO 40177 --- [qtp1830557131-40] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T13:55:26.025+07:00  INFO 40177 --- [qtp1830557131-64] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T13:55:26.030+07:00  INFO 40177 --- [qtp1830557131-40] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T13:56:03.517+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 9, expire count 1
2025-09-17T13:56:03.568+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T13:56:05.576+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T13:56:05.985+07:00  INFO 40177 --- [qtp1830557131-40] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T13:56:05.995+07:00  INFO 40177 --- [qtp1830557131-40] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T13:56:06.018+07:00  INFO 40177 --- [qtp1830557131-124] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T13:56:06.030+07:00  INFO 40177 --- [qtp1830557131-124] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T13:56:59.659+07:00  INFO 40177 --- [qtp1830557131-34] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T13:56:59.660+07:00  INFO 40177 --- [qtp1830557131-60] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T13:56:59.669+07:00  INFO 40177 --- [qtp1830557131-60] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T13:56:59.670+07:00  INFO 40177 --- [qtp1830557131-34] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T13:57:06.674+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T13:58:02.767+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T13:58:02.772+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T13:58:04.782+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T13:58:53.644+07:00  INFO 40177 --- [qtp1830557131-124] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T13:58:53.645+07:00  INFO 40177 --- [qtp1830557131-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T13:58:53.654+07:00  INFO 40177 --- [qtp1830557131-124] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T13:58:53.654+07:00  INFO 40177 --- [qtp1830557131-36] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T13:59:06.885+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T13:59:22.641+07:00  INFO 40177 --- [qtp1830557131-39] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T13:59:22.645+07:00  INFO 40177 --- [qtp1830557131-64] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T13:59:22.666+07:00  INFO 40177 --- [qtp1830557131-39] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T13:59:22.666+07:00  INFO 40177 --- [qtp1830557131-64] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T14:00:01.643+07:00  INFO 40177 --- [qtp1830557131-60] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T14:00:01.644+07:00  INFO 40177 --- [qtp1830557131-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T14:00:01.654+07:00  INFO 40177 --- [qtp1830557131-60] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T14:00:01.654+07:00  INFO 40177 --- [qtp1830557131-36] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T14:00:03.006+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 9, expire count 0
2025-09-17T14:00:03.018+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T14:00:04.027+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:00:04.028+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T14:00:04.028+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-17T14:00:04.035+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 17/09/2025@14:00:04+0700
2025-09-17T14:00:04.102+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 17/09/2025@14:00:00+0700 to 17/09/2025@14:15:00+0700
2025-09-17T14:00:04.102+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 17/09/2025@14:00:00+0700 to 17/09/2025@14:15:00+0700
2025-09-17T14:00:04.104+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at 14 PM every day
2025-09-17T14:00:04.104+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-09-17T14:00:05.612+07:00  INFO 40177 --- [qtp1830557131-64] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T14:00:05.612+07:00  INFO 40177 --- [qtp1830557131-124] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T14:00:05.621+07:00  INFO 40177 --- [qtp1830557131-124] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T14:00:05.621+07:00  INFO 40177 --- [qtp1830557131-64] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T14:00:11.601+07:00  INFO 40177 --- [qtp1830557131-60] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T14:00:11.613+07:00  INFO 40177 --- [qtp1830557131-60] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T14:00:11.663+07:00  INFO 40177 --- [qtp1830557131-127] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T14:00:11.668+07:00  INFO 40177 --- [qtp1830557131-127] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T14:00:56.446+07:00  INFO 40177 --- [qtp1830557131-39] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T14:00:56.448+07:00  INFO 40177 --- [qtp1830557131-64] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T14:00:56.458+07:00  INFO 40177 --- [qtp1830557131-39] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T14:00:56.458+07:00  INFO 40177 --- [qtp1830557131-64] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T14:01:06.202+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:02:03.306+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 11, expire count 0
2025-09-17T14:02:03.319+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T14:02:03.320+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:03:06.418+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:04:02.524+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-09-17T14:04:02.534+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T14:04:02.535+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:05:05.635+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T14:05:05.636+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:06:01.772+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 0
2025-09-17T14:06:01.785+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T14:06:06.790+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:07:04.897+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:08:00.986+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T14:08:00.987+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T14:08:06.990+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:09:04.075+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:10:00.211+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T14:10:00.219+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T14:10:06.223+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:10:06.224+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T14:11:03.392+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:11:59.574+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 7
2025-09-17T14:11:59.579+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T14:12:06.605+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:13:02.791+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:13:59.028+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 8
2025-09-17T14:13:59.044+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T14:14:06.064+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:15:02.235+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:15:02.236+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T14:15:02.237+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 17/09/2025@14:15:02+0700
2025-09-17T14:15:02.255+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 17/09/2025@14:15:00+0700 to 17/09/2025@14:30:00+0700
2025-09-17T14:15:02.257+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 17/09/2025@14:15:00+0700 to 17/09/2025@14:30:00+0700
2025-09-17T14:15:02.258+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-17T14:16:03.432+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-17T14:16:03.440+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T14:16:05.461+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:17:06.614+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:18:02.794+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T14:18:02.796+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T14:18:04.814+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:19:06.980+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:20:03.177+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T14:20:03.183+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T14:20:04.190+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:20:04.191+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T14:21:06.393+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:22:03.606+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T14:22:03.614+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T14:22:03.614+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:23:06.723+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:24:02.810+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-09-17T14:24:02.822+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T14:24:02.823+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:24:45.677+07:00  INFO 40177 --- [qtp1830557131-39] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T14:24:45.706+07:00  INFO 40177 --- [qtp1830557131-131] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T14:24:45.808+07:00  INFO 40177 --- [qtp1830557131-39] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T14:24:45.817+07:00  INFO 40177 --- [qtp1830557131-131] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T14:24:59.657+07:00  INFO 40177 --- [qtp1830557131-131] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T14:24:59.664+07:00  INFO 40177 --- [qtp1830557131-131] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T14:24:59.680+07:00  INFO 40177 --- [qtp1830557131-134] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T14:24:59.683+07:00  INFO 40177 --- [qtp1830557131-134] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T14:25:05.931+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:25:05.931+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T14:25:33.991+07:00  INFO 40177 --- [qtp1830557131-39] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T14:25:34.015+07:00  INFO 40177 --- [qtp1830557131-39] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T14:25:34.028+07:00  INFO 40177 --- [qtp1830557131-123] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T14:25:34.047+07:00  INFO 40177 --- [qtp1830557131-123] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T14:25:40.545+07:00  INFO 40177 --- [qtp1830557131-127] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T14:25:40.545+07:00  INFO 40177 --- [qtp1830557131-131] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T14:25:40.554+07:00  INFO 40177 --- [qtp1830557131-127] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T14:25:40.556+07:00  INFO 40177 --- [qtp1830557131-131] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T14:25:48.829+07:00  INFO 40177 --- [qtp1830557131-246] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T14:25:48.831+07:00  INFO 40177 --- [qtp1830557131-132] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T14:25:48.841+07:00  INFO 40177 --- [qtp1830557131-132] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T14:25:48.841+07:00  INFO 40177 --- [qtp1830557131-246] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T14:26:02.062+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 1
2025-09-17T14:26:02.072+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T14:26:02.072+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:26:11.124+07:00  INFO 40177 --- [qtp1830557131-132] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T14:26:11.125+07:00  INFO 40177 --- [qtp1830557131-39] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T14:26:11.133+07:00  INFO 40177 --- [qtp1830557131-39] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T14:26:11.134+07:00  INFO 40177 --- [qtp1830557131-132] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T14:26:21.542+07:00  INFO 40177 --- [qtp1830557131-246] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T14:26:21.543+07:00  INFO 40177 --- [qtp1830557131-127] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T14:26:21.567+07:00  INFO 40177 --- [qtp1830557131-127] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T14:26:21.568+07:00  INFO 40177 --- [qtp1830557131-246] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T14:27:05.189+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:27:19.894+07:00  INFO 40177 --- [qtp1830557131-131] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T14:27:19.915+07:00  INFO 40177 --- [qtp1830557131-131] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T14:27:19.937+07:00  INFO 40177 --- [qtp1830557131-205] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T14:27:19.942+07:00  INFO 40177 --- [qtp1830557131-205] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T14:27:25.040+07:00  INFO 40177 --- [qtp1830557131-246] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T14:27:25.040+07:00  INFO 40177 --- [qtp1830557131-39] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T14:27:25.046+07:00  INFO 40177 --- [qtp1830557131-246] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T14:27:25.047+07:00  INFO 40177 --- [qtp1830557131-39] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T14:27:41.393+07:00  INFO 40177 --- [qtp1830557131-39] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T14:27:41.402+07:00  INFO 40177 --- [qtp1830557131-39] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T14:27:41.452+07:00  INFO 40177 --- [qtp1830557131-250] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T14:27:41.459+07:00  INFO 40177 --- [qtp1830557131-250] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T14:28:01.299+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-17T14:28:01.311+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T14:28:04.564+07:00  INFO 40177 --- [qtp1830557131-123] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T14:28:04.564+07:00  INFO 40177 --- [qtp1830557131-243] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-17T14:28:06.317+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:29:04.415+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:30:00.537+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 0
2025-09-17T14:30:00.554+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T14:30:06.566+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:30:06.567+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T14:30:06.567+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-17T14:30:06.568+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 17/09/2025@14:30:06+0700
2025-09-17T14:30:06.581+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 17/09/2025@14:30:00+0700 to 17/09/2025@14:45:00+0700
2025-09-17T14:30:06.582+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 17/09/2025@14:30:00+0700 to 17/09/2025@14:45:00+0700
2025-09-17T14:31:03.690+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:31:59.786+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-09-17T14:31:59.797+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T14:32:06.811+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:33:02.906+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:33:59.039+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T14:33:59.055+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T14:34:06.066+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:35:02.163+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:35:02.165+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T14:36:03.273+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T14:36:03.282+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T14:36:05.290+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:37:06.382+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:38:03.495+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 4
2025-09-17T14:38:03.503+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T14:38:04.509+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:39:06.612+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:40:03.718+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 5
2025-09-17T14:40:03.734+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T14:40:03.736+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:40:03.737+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T14:41:06.833+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:42:02.909+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-17T14:42:02.918+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T14:42:02.919+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:43:06.017+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:44:02.115+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T14:44:02.122+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T14:44:02.122+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:45:05.215+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T14:45:05.222+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:45:05.223+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-17T14:45:05.229+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 17/09/2025@14:45:05+0700
2025-09-17T14:45:05.257+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 17/09/2025@14:45:00+0700 to 17/09/2025@15:00:00+0700
2025-09-17T14:45:05.257+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 17/09/2025@14:45:00+0700 to 17/09/2025@15:00:00+0700
2025-09-17T14:46:01.349+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T14:46:01.358+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T14:46:06.362+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:47:04.466+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:48:00.580+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T14:48:00.595+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T14:48:06.605+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:49:03.691+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:49:59.785+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T14:49:59.792+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T14:50:06.805+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:50:06.807+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T14:51:02.910+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:51:59.006+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T14:51:59.017+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T14:52:06.030+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:53:02.127+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:54:03.234+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T14:54:03.278+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T14:54:05.288+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:55:06.390+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:55:06.392+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T14:56:03.506+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-17T14:56:03.514+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T14:56:04.520+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:57:06.619+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:58:03.724+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T14:58:03.739+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T14:58:03.740+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T14:59:06.845+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:00:02.936+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T15:00:02.941+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T15:00:02.942+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:00:02.942+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T15:00:02.942+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-17T15:00:02.943+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 17/09/2025@15:00:02+0700
2025-09-17T15:00:02.972+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 2 messages for session 17/09/2025@15:00:00+0700 to 17/09/2025@15:15:00+0700
2025-09-17T15:00:02.972+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 📤 message : MAIL - inquiry-overdue-reminder - scheduled at 17/09/2025@15:00:21+0700
2025-09-17T15:00:02.972+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 📤 message : MAIL - inquiry-overdue-reminder - scheduled at 17/09/2025@15:02:06+0700
2025-09-17T15:00:02.973+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 2 messages for session 17/09/2025@15:00:00+0700 to 17/09/2025@15:15:00+0700
2025-09-17T15:00:02.973+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-09-17T15:00:02.977+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at 15 PM every day
2025-09-17T15:00:02.978+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 3 hour
2025-09-17T15:01:06.065+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:02:02.175+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-17T15:02:02.184+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T15:02:02.186+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:03:05.293+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:04:01.386+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T15:04:01.389+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T15:04:06.391+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:05:04.491+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T15:05:04.495+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:06:00.598+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T15:06:00.607+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T15:06:06.617+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:07:03.708+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:07:59.795+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T15:07:59.807+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T15:08:06.819+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:09:02.902+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:09:59.060+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T15:09:59.088+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T15:10:06.100+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:10:06.101+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T15:11:02.191+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:12:03.310+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-17T15:12:03.326+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T15:12:05.334+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:13:06.430+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:14:03.578+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T15:14:03.588+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T15:14:04.593+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:15:06.699+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-17T15:15:06.707+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 17/09/2025@15:15:06+0700
2025-09-17T15:15:06.737+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 1 messages for session 17/09/2025@15:15:00+0700 to 17/09/2025@15:30:00+0700
2025-09-17T15:15:06.737+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 📤 message : MAIL - inquiry-overdue-reminder - scheduled at 17/09/2025@15:18:38+0700
2025-09-17T15:15:06.737+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 1 messages for session 17/09/2025@15:15:00+0700 to 17/09/2025@15:30:00+0700
2025-09-17T15:15:06.738+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:15:06.738+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T15:16:02.842+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T15:16:02.852+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T15:16:03.858+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:17:06.951+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:18:03.004+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T15:18:03.010+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T15:18:03.011+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:19:06.127+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:20:02.218+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T15:20:02.230+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T15:20:02.230+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T15:20:02.230+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:21:05.330+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:22:01.432+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T15:22:01.439+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T15:22:06.443+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:23:04.559+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:24:00.659+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T15:24:00.665+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T15:24:06.675+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:25:03.773+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T15:25:03.776+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:25:59.875+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-17T15:25:59.888+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T15:26:06.894+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:27:02.982+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:27:59.065+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T15:27:59.069+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T15:28:06.081+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:29:02.176+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:30:03.287+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T15:30:03.297+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T15:30:05.309+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 17/09/2025@15:30:05+0700
2025-09-17T15:30:05.325+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 17/09/2025@15:30:00+0700 to 17/09/2025@15:45:00+0700
2025-09-17T15:30:05.326+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 17/09/2025@15:30:00+0700 to 17/09/2025@15:45:00+0700
2025-09-17T15:30:05.327+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-17T15:30:05.327+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T15:30:05.328+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:31:06.417+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:32:03.519+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-17T15:32:03.546+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T15:32:04.552+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:33:06.658+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:34:02.750+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T15:34:02.754+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T15:34:03.756+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:35:06.864+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T15:35:06.866+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:36:02.987+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T15:36:03.004+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T15:36:03.004+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:37:06.096+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:38:02.194+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T15:38:02.199+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T15:38:02.199+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:39:05.294+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:40:01.407+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T15:40:01.415+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T15:40:06.421+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T15:40:06.423+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:41:04.508+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:42:00.602+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-17T15:42:00.608+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T15:42:06.612+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:43:03.698+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:43:59.800+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T15:43:59.811+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T15:44:06.826+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:45:02.917+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-17T15:45:02.919+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 17/09/2025@15:45:02+0700
2025-09-17T15:45:02.952+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 1 messages for session 17/09/2025@15:45:00+0700 to 17/09/2025@16:00:00+0700
2025-09-17T15:45:02.952+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 📤 message : MAIL - inquiry-overdue-reminder - scheduled at 17/09/2025@15:52:24+0700
2025-09-17T15:45:02.952+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 1 messages for session 17/09/2025@15:45:00+0700 to 17/09/2025@16:00:00+0700
2025-09-17T15:45:02.953+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T15:45:02.953+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:45:59.056+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T15:45:59.063+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T15:46:06.078+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:47:02.167+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:48:03.280+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T15:48:03.295+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T15:48:05.306+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:49:06.401+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:50:03.537+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T15:50:03.546+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T15:50:04.552+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T15:50:04.553+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:51:06.657+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:52:03.780+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T15:52:03.798+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T15:52:03.799+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:53:06.891+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:54:02.982+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T15:54:02.991+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T15:54:02.992+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:55:06.087+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:55:06.091+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T15:56:02.215+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-17T15:56:02.225+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T15:56:02.226+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:57:05.328+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:58:01.420+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T15:58:01.442+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T15:58:06.448+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T15:59:04.552+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:00:00.661+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T16:00:00.669+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T16:00:06.678+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-09-17T16:00:06.679+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:00:06.679+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T16:00:06.679+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-17T16:00:06.680+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 17/09/2025@16:00:06+0700
2025-09-17T16:00:06.693+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 1 messages for session 17/09/2025@16:00:00+0700 to 17/09/2025@16:15:00+0700
2025-09-17T16:00:06.693+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 📤 message : MAIL - inquiry-overdue-reminder - scheduled at 17/09/2025@16:09:30+0700
2025-09-17T16:00:06.694+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 1 messages for session 17/09/2025@16:00:00+0700 to 17/09/2025@16:15:00+0700
2025-09-17T16:01:03.798+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:01:59.910+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T16:01:59.920+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T16:02:06.929+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:03:03.018+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:03:59.120+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T16:03:59.129+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T16:04:06.142+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:05:02.228+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:05:02.230+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T16:06:03.354+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T16:06:03.363+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T16:06:05.371+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:07:06.482+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:08:03.586+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T16:08:03.598+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T16:08:04.599+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:09:06.688+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:10:03.779+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T16:10:03.784+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T16:10:03.785+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:10:03.785+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T16:11:06.879+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:11:18.704+07:00  INFO 40177 --- [qtp1830557131-741] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T16:11:18.705+07:00  INFO 40177 --- [qtp1830557131-674] n.d.module.session.ClientSessionManager  : Add a client session id = node01y270lcbf91w6e6ewh1003y3b0, token = 922db1993aad84c19f2518d232fde1ff
2025-09-17T16:11:18.758+07:00  INFO 40177 --- [qtp1830557131-741] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T16:11:18.767+07:00  INFO 40177 --- [qtp1830557131-674] n.d.m.c.a.CompanyAuthenticationService   : User sandy.vnhph is logged in successfully system
2025-09-17T16:12:02.977+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 1
2025-09-17T16:12:02.990+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T16:12:02.991+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:13:06.090+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:14:02.192+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T16:14:02.198+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T16:14:02.199+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:15:05.296+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:15:05.298+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T16:15:05.298+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-17T16:15:05.298+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 17/09/2025@16:15:05+0700
2025-09-17T16:15:05.312+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 1 messages for session 17/09/2025@16:15:00+0700 to 17/09/2025@16:30:00+0700
2025-09-17T16:15:05.313+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 📤 message : MAIL - inquiry-overdue-reminder - scheduled at 17/09/2025@16:21:22+0700
2025-09-17T16:15:05.313+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 1 messages for session 17/09/2025@16:15:00+0700 to 17/09/2025@16:30:00+0700
2025-09-17T16:16:01.405+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-09-17T16:16:01.416+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T16:16:06.422+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:17:04.527+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:18:00.640+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T16:18:00.651+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T16:18:06.657+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:19:03.745+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:19:59.843+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T16:19:59.848+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T16:20:06.855+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:20:06.856+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T16:21:02.942+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:21:59.084+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 3
2025-09-17T16:21:59.091+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T16:22:06.102+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:23:02.195+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:24:03.296+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-17T16:24:03.306+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T16:24:05.314+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:25:06.398+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:25:06.402+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T16:26:03.515+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 2
2025-09-17T16:26:03.521+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T16:26:04.526+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:27:06.616+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:28:03.731+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T16:28:03.743+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T16:28:03.744+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:29:06.853+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:30:02.952+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T16:30:02.956+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T16:30:02.961+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:30:02.961+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T16:30:02.962+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-17T16:30:02.963+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 17/09/2025@16:30:02+0700
2025-09-17T16:30:03.006+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 3 messages for session 17/09/2025@16:30:00+0700 to 17/09/2025@16:45:00+0700
2025-09-17T16:30:03.006+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 📤 message : MAIL - inquiry-overdue-reminder - scheduled at 17/09/2025@16:39:54+0700
2025-09-17T16:30:03.007+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 📤 message : MAIL - inquiry-overdue-reminder - scheduled at 17/09/2025@16:40:18+0700
2025-09-17T16:30:03.007+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 📤 message : MAIL - inquiry-overdue-reminder - scheduled at 17/09/2025@16:40:29+0700
2025-09-17T16:30:03.007+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 3 messages for session 17/09/2025@16:30:00+0700 to 17/09/2025@16:45:00+0700
2025-09-17T16:31:06.111+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:32:02.224+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-17T16:32:02.233+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T16:32:02.234+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:33:05.335+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:34:01.432+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-17T16:34:01.434+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T16:34:06.439+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:35:04.541+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:35:04.542+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T16:36:00.637+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T16:36:00.645+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T16:36:06.656+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:37:03.748+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:37:59.821+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T16:37:59.822+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T16:38:06.831+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:39:02.925+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:39:59.021+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T16:39:59.032+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T16:40:06.042+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:40:06.043+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T16:41:02.132+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:42:03.227+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-17T16:42:03.230+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T16:42:05.236+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:43:06.322+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:44:03.440+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T16:44:03.448+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T16:44:04.452+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:45:06.543+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:45:06.547+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T16:45:06.549+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 17/09/2025@16:45:06+0700
2025-09-17T16:45:06.567+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 1 messages for session 17/09/2025@16:45:00+0700 to 17/09/2025@17:00:00+0700
2025-09-17T16:45:06.568+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 📤 message : MAIL - inquiry-overdue-reminder - scheduled at 17/09/2025@16:46:56+0700
2025-09-17T16:45:06.568+07:00  INFO 40177 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 1 messages for session 17/09/2025@16:45:00+0700 to 17/09/2025@17:00:00+0700
2025-09-17T16:45:06.568+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-17T16:46:03.687+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-17T16:46:03.694+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T16:46:03.695+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:47:06.801+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:48:02.892+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T16:48:02.896+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T16:48:02.897+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:49:06.004+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:50:02.104+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T16:50:02.115+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T16:50:02.116+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:50:02.117+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T16:51:05.230+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:52:01.322+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T16:52:01.335+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T16:52:06.339+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:53:04.438+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:54:00.605+07:00  INFO 40177 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-17T16:54:00.614+07:00  INFO 40177 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T16:54:06.627+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:55:03.705+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-17T16:55:03.706+07:00  INFO 40177 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-17T16:55:13.746+07:00  INFO 40177 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@38a26a2f{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-17T16:55:13.747+07:00  INFO 40177 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-17T16:55:13.747+07:00  INFO 40177 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-17T16:55:13.747+07:00  INFO 40177 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-17T16:55:13.747+07:00  INFO 40177 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-17T16:55:13.747+07:00  INFO 40177 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-17T16:55:13.747+07:00  INFO 40177 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-17T16:55:13.747+07:00  INFO 40177 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-17T16:55:13.747+07:00  INFO 40177 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-09-17T16:55:13.747+07:00  INFO 40177 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-09-17T16:55:13.747+07:00  INFO 40177 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-17T16:55:13.748+07:00  INFO 40177 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-09-17T16:55:13.748+07:00  INFO 40177 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-09-17T16:55:13.748+07:00  INFO 40177 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-17T16:55:13.748+07:00  INFO 40177 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-09-17T16:55:13.748+07:00  INFO 40177 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-09-17T16:55:13.764+07:00  INFO 40177 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-17T16:55:13.822+07:00  INFO 40177 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-17T16:55:13.840+07:00  INFO 40177 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-17T16:55:13.862+07:00  INFO 40177 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-17T16:55:13.864+07:00  INFO 40177 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-17T16:55:13.866+07:00  INFO 40177 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-17T16:55:13.866+07:00  INFO 40177 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-17T16:55:13.868+07:00  INFO 40177 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-17T16:55:13.868+07:00  INFO 40177 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-17T16:55:13.868+07:00  INFO 40177 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-17T16:55:13.868+07:00  INFO 40177 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-17T16:55:13.868+07:00  INFO 40177 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-17T16:55:13.868+07:00  INFO 40177 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-17T16:55:13.868+07:00  INFO 40177 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-17T16:55:13.868+07:00  INFO 40177 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-17T16:55:13.869+07:00  INFO 40177 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-17T16:55:13.870+07:00  INFO 40177 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@5ca8157c{STOPPING}[12.0.15,sto=0]
2025-09-17T16:55:13.875+07:00  INFO 40177 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-17T16:55:13.877+07:00  INFO 40177 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@ae5eeee{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.9892372898702647324/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@5796ae15{STOPPED}}
