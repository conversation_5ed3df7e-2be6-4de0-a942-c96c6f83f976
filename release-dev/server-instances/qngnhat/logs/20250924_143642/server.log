2025-09-24T14:36:43.287+07:00  INFO 68713 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 68713 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-24T14:36:43.288+07:00  INFO 68713 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-24T14:36:44.109+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:36:44.233+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 119 ms. Found 22 JPA repository interfaces.
2025-09-24T14:36:44.266+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:36:44.270+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 1 JPA repository interface.
2025-09-24T14:36:44.270+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:36:44.349+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 78 ms. Found 10 JPA repository interfaces.
2025-09-24T14:36:44.350+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:36:44.354+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 3 JPA repository interfaces.
2025-09-24T14:36:44.363+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:36:44.372+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 1 JPA repository interface.
2025-09-24T14:36:44.381+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:36:44.383+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 2 JPA repository interfaces.
2025-09-24T14:36:44.383+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:36:44.389+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 6 JPA repository interfaces.
2025-09-24T14:36:44.392+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:36:44.397+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 5 JPA repository interfaces.
2025-09-24T14:36:44.402+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:36:44.405+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 3 JPA repository interfaces.
2025-09-24T14:36:44.406+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:36:44.406+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-24T14:36:44.407+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:36:44.414+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 10 JPA repository interfaces.
2025-09-24T14:36:44.419+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:36:44.423+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 3 JPA repository interfaces.
2025-09-24T14:36:44.429+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:36:44.433+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-09-24T14:36:44.434+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:36:44.444+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 12 JPA repository interfaces.
2025-09-24T14:36:44.444+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:36:44.447+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-09-24T14:36:44.448+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:36:44.448+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-24T14:36:44.448+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:36:44.449+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-24T14:36:44.450+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:36:44.456+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 7 JPA repository interfaces.
2025-09-24T14:36:44.456+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:36:44.459+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 2 JPA repository interfaces.
2025-09-24T14:36:44.459+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:36:44.459+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-24T14:36:44.459+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:36:44.473+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 13 ms. Found 19 JPA repository interfaces.
2025-09-24T14:36:44.483+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:36:44.491+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 8 JPA repository interfaces.
2025-09-24T14:36:44.492+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:36:44.495+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-09-24T14:36:44.496+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:36:44.501+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 7 JPA repository interfaces.
2025-09-24T14:36:44.502+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:36:44.508+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 9 JPA repository interfaces.
2025-09-24T14:36:44.509+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:36:44.513+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-09-24T14:36:44.514+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:36:44.524+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 13 JPA repository interfaces.
2025-09-24T14:36:44.524+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:36:44.535+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 14 JPA repository interfaces.
2025-09-24T14:36:44.536+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:36:44.554+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 17 ms. Found 24 JPA repository interfaces.
2025-09-24T14:36:44.555+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:36:44.556+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-24T14:36:44.562+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:36:44.563+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-24T14:36:44.564+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:36:44.573+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 12 JPA repository interfaces.
2025-09-24T14:36:44.575+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:36:44.614+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 39 ms. Found 66 JPA repository interfaces.
2025-09-24T14:36:44.615+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:36:44.616+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-24T14:36:44.621+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T14:36:44.625+07:00  INFO 68713 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-09-24T14:36:44.828+07:00  INFO 68713 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-24T14:36:44.831+07:00  INFO 68713 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-24T14:36:45.116+07:00  WARN 68713 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-24T14:36:45.349+07:00  INFO 68713 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-24T14:36:45.352+07:00  INFO 68713 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-24T14:36:45.364+07:00  INFO 68713 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-24T14:36:45.364+07:00  INFO 68713 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1969 ms
2025-09-24T14:36:45.446+07:00  WARN 68713 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-24T14:36:45.446+07:00  INFO 68713 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-24T14:36:45.569+07:00  INFO 68713 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@5a318745
2025-09-24T14:36:45.569+07:00  INFO 68713 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-24T14:36:45.575+07:00  WARN 68713 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-24T14:36:45.575+07:00  INFO 68713 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-24T14:36:45.581+07:00  INFO 68713 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@6e057374
2025-09-24T14:36:45.581+07:00  INFO 68713 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-24T14:36:45.581+07:00  WARN 68713 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-24T14:36:45.581+07:00  INFO 68713 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-24T14:36:45.589+07:00  INFO 68713 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@511e0453
2025-09-24T14:36:45.589+07:00  INFO 68713 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-24T14:36:45.589+07:00  WARN 68713 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-24T14:36:45.589+07:00  INFO 68713 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-24T14:36:45.598+07:00  INFO 68713 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@130901fd
2025-09-24T14:36:45.598+07:00  INFO 68713 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-24T14:36:45.599+07:00  WARN 68713 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-24T14:36:45.599+07:00  INFO 68713 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-24T14:36:45.606+07:00  INFO 68713 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@73ec417b
2025-09-24T14:36:45.606+07:00  INFO 68713 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-24T14:36:45.606+07:00  INFO 68713 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-24T14:36:45.656+07:00  INFO 68713 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-24T14:36:45.707+07:00  INFO 68713 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@10ecdd85{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.7154401700365241383/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@47ffce5d{STARTED}}
2025-09-24T14:36:45.708+07:00  INFO 68713 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@10ecdd85{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.7154401700365241383/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@47ffce5d{STARTED}}
2025-09-24T14:36:45.710+07:00  INFO 68713 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@4f5080ea{STARTING}[12.0.15,sto=0] @3001ms
2025-09-24T14:36:45.769+07:00  INFO 68713 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-24T14:36:45.797+07:00  INFO 68713 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-24T14:36:45.813+07:00  INFO 68713 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-24T14:36:45.941+07:00  INFO 68713 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-24T14:36:45.982+07:00  WARN 68713 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-24T14:36:46.671+07:00  INFO 68713 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-24T14:36:46.680+07:00  INFO 68713 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@7d8a546d] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-24T14:36:46.806+07:00  INFO 68713 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-24T14:36:47.023+07:00  INFO 68713 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.graphapi", "net.datatp.module.kpi", "net.datatp.module.monitor.activity", "net.datatp.module.wfms", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "net.datatp.module.okr", "cloud.datatp.module.fleet", "net.datatp.module.core.template", "cloud.datatp.gps", "cloud.datatp.module.pegasus.shipment", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "cloud.datatp.tms", "net.datatp.module.company.tmpl", "net.datatp.module.groovy", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "cloud.datatp.vendor", "net.datatp.module.resource", "net.datatp.module.hr", "net.datatp.module.company.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.chatbot", "cloud.datatp.module.odoo", "net.datatp.module.core.print", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-09-24T14:36:47.025+07:00  INFO 68713 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-24T14:36:47.031+07:00  INFO 68713 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-24T14:36:47.032+07:00  INFO 68713 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-24T14:36:47.058+07:00  INFO 68713 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-24T14:36:47.063+07:00  WARN 68713 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-24T14:36:49.191+07:00  INFO 68713 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-24T14:36:49.192+07:00  INFO 68713 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@8d342b1] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-24T14:36:49.361+07:00  WARN 68713 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-24T14:36:49.361+07:00  WARN 68713 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-24T14:36:49.369+07:00  WARN 68713 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-24T14:36:49.369+07:00  WARN 68713 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-24T14:36:49.383+07:00  WARN 68713 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-24T14:36:49.383+07:00  WARN 68713 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-24T14:36:49.746+07:00  INFO 68713 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-24T14:36:49.752+07:00  INFO 68713 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-24T14:36:49.753+07:00  INFO 68713 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-24T14:36:49.774+07:00  INFO 68713 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-24T14:36:49.776+07:00  WARN 68713 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-24T14:36:50.305+07:00  INFO 68713 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-24T14:36:50.306+07:00  INFO 68713 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@48b29d08] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-24T14:36:50.362+07:00  WARN 68713 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-24T14:36:50.362+07:00  WARN 68713 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-24T14:36:50.860+07:00  INFO 68713 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-24T14:36:50.891+07:00  INFO 68713 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-24T14:36:50.895+07:00  INFO 68713 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-24T14:36:50.895+07:00  INFO 68713 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-24T14:36:50.902+07:00  WARN 68713 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-24T14:36:51.041+07:00  INFO 68713 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-24T14:36:51.533+07:00  INFO 68713 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-24T14:36:51.537+07:00  INFO 68713 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-24T14:36:51.576+07:00  INFO 68713 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-24T14:36:51.624+07:00  INFO 68713 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-24T14:36:51.695+07:00  INFO 68713 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-24T14:36:51.726+07:00  INFO 68713 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-24T14:36:51.750+07:00  INFO 68713 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 22463390ms : this is harmless.
2025-09-24T14:36:51.761+07:00  INFO 68713 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-24T14:36:51.764+07:00  INFO 68713 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-24T14:36:51.779+07:00  INFO 68713 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 22463381ms : this is harmless.
2025-09-24T14:36:51.781+07:00  INFO 68713 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-24T14:36:51.794+07:00  INFO 68713 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-24T14:36:51.795+07:00  INFO 68713 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-24T14:36:53.708+07:00  INFO 68713 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-24T14:36:53.708+07:00  INFO 68713 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-24T14:36:53.708+07:00  WARN 68713 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-24T14:36:54.011+07:00  INFO 68713 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 6 messages for session 24/09/2025@14:30:00+0700 to 24/09/2025@14:45:00+0700
2025-09-24T14:36:54.011+07:00  INFO 68713 --- [main] c.d.f.core.message.MessageQueueManager   : 📤 message : MAIL - inquiry-overdue-reminder - scheduled at 24/09/2025@14:35:53+0700
2025-09-24T14:36:54.011+07:00  INFO 68713 --- [main] c.d.f.core.message.MessageQueueManager   : 📤 message : MAIL - inquiry-overdue-reminder - scheduled at 24/09/2025@14:36:01+0700
2025-09-24T14:36:54.011+07:00  INFO 68713 --- [main] c.d.f.core.message.MessageQueueManager   : 📤 message : MAIL - inquiry-overdue-reminder - scheduled at 24/09/2025@14:36:08+0700
2025-09-24T14:36:54.011+07:00  INFO 68713 --- [main] c.d.f.core.message.MessageQueueManager   : 📤 message : MAIL - inquiry-overdue-reminder - scheduled at 24/09/2025@14:36:14+0700
2025-09-24T14:36:54.011+07:00  INFO 68713 --- [main] c.d.f.core.message.MessageQueueManager   : 📤 message : MAIL - inquiry-overdue-reminder - scheduled at 24/09/2025@14:36:20+0700
2025-09-24T14:36:54.011+07:00  INFO 68713 --- [main] c.d.f.core.message.MessageQueueManager   : 📤 message : MAIL - inquiry-overdue-reminder - scheduled at 24/09/2025@14:36:27+0700
2025-09-24T14:36:54.011+07:00  INFO 68713 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 6 messages for session 24/09/2025@14:30:00+0700 to 24/09/2025@14:45:00+0700
2025-09-24T14:36:54.583+07:00  INFO 68713 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-09-24T14:36:54.584+07:00  INFO 68713 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-24T14:36:54.584+07:00  WARN 68713 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-24T14:36:54.876+07:00  INFO 68713 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-09-24T14:36:54.876+07:00  INFO 68713 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-09-24T14:36:54.876+07:00  INFO 68713 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-09-24T14:36:54.876+07:00  INFO 68713 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-09-24T14:36:54.876+07:00  INFO 68713 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-09-24T14:36:56.752+07:00  WARN 68713 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 426be696-e767-441a-8e06-89872f8f29b8

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-24T14:36:56.756+07:00  INFO 68713 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-09-24T14:36:57.074+07:00  INFO 68713 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-24T14:36:57.074+07:00  INFO 68713 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-24T14:36:57.074+07:00  INFO 68713 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-24T14:36:57.074+07:00  INFO 68713 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-24T14:36:57.074+07:00  INFO 68713 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-09-24T14:36:57.074+07:00  INFO 68713 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-09-24T14:36:57.074+07:00  INFO 68713 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-24T14:36:57.074+07:00  INFO 68713 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-09-24T14:36:57.074+07:00  INFO 68713 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-09-24T14:36:57.074+07:00  INFO 68713 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-24T14:36:57.074+07:00  INFO 68713 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-09-24T14:36:57.074+07:00  INFO 68713 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-09-24T14:36:57.077+07:00  INFO 68713 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-24T14:36:57.077+07:00  INFO 68713 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-24T14:36:57.077+07:00  INFO 68713 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-24T14:36:57.145+07:00  INFO 68713 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-24T14:36:57.146+07:00  INFO 68713 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-24T14:36:57.147+07:00  INFO 68713 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-09-24T14:36:57.155+07:00  INFO 68713 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@359014ce{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-24T14:36:57.156+07:00  INFO 68713 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-09-24T14:36:57.156+07:00  INFO 68713 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-09-24T14:36:57.184+07:00  INFO 68713 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-09-24T14:36:57.184+07:00  INFO 68713 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-09-24T14:36:57.190+07:00  INFO 68713 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 14.218 seconds (process running for 14.484)
2025-09-24T14:37:01.342+07:00  INFO 68713 --- [qtp1987166253-39] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01ets85pvctm3pij1jtwzckkzh0
2025-09-24T14:37:01.342+07:00  INFO 68713 --- [qtp1987166253-37] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node012trpgs0ip3ke1drlf80kz1cpv1
2025-09-24T14:37:01.652+07:00  INFO 68713 --- [qtp1987166253-39] n.d.module.session.ClientSessionManager  : Add a client session id = node01ets85pvctm3pij1jtwzckkzh0, token = 5935f52c9fddb56e9b76246cabe26ea3
2025-09-24T14:37:01.652+07:00  INFO 68713 --- [qtp1987166253-37] n.d.module.session.ClientSessionManager  : Add a client session id = node012trpgs0ip3ke1drlf80kz1cpv1, token = 5935f52c9fddb56e9b76246cabe26ea3
2025-09-24T14:37:02.091+07:00  INFO 68713 --- [qtp1987166253-37] n.d.m.c.a.CompanyAuthenticationService   : User rio.vnsgn is logged in successfully system
2025-09-24T14:37:02.099+07:00  INFO 68713 --- [qtp1987166253-39] n.d.m.c.a.CompanyAuthenticationService   : User rio.vnsgn is logged in successfully system
2025-09-24T14:37:04.096+07:00  INFO 68713 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T14:38:00.258+07:00  INFO 68713 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-09-24T14:38:00.299+07:00  INFO 68713 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T14:38:06.307+07:00  INFO 68713 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T14:39:03.413+07:00  INFO 68713 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T14:39:59.528+07:00  INFO 68713 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-24T14:39:59.539+07:00  INFO 68713 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T14:40:06.556+07:00  INFO 68713 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T14:40:06.558+07:00  INFO 68713 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T14:41:02.654+07:00  INFO 68713 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T14:42:03.783+07:00  INFO 68713 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-24T14:42:03.797+07:00  INFO 68713 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T14:42:05.805+07:00  INFO 68713 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T14:43:06.891+07:00  INFO 68713 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T14:44:04.026+07:00  INFO 68713 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-24T14:44:04.034+07:00  INFO 68713 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T14:44:05.040+07:00  INFO 68713 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T14:45:06.150+07:00  INFO 68713 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-24T14:45:06.161+07:00  INFO 68713 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 24/09/2025@14:45:06+0700
2025-09-24T14:45:06.182+07:00  INFO 68713 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 24/09/2025@14:45:00+0700 to 24/09/2025@15:00:00+0700
2025-09-24T14:45:06.183+07:00  INFO 68713 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 24/09/2025@14:45:00+0700 to 24/09/2025@15:00:00+0700
2025-09-24T14:45:06.184+07:00  INFO 68713 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T14:45:06.184+07:00  INFO 68713 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T14:46:03.303+07:00  INFO 68713 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-24T14:46:03.306+07:00  INFO 68713 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T14:46:04.311+07:00  INFO 68713 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T14:47:06.418+07:00  INFO 68713 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T14:47:45.665+07:00  INFO 68713 --- [Scheduler-661316124-1] n.d.m.session.AppHttpSessionListener     : The session node01ets85pvctm3pij1jtwzckkzh0 is destroyed.
2025-09-24T14:48:03.152+07:00  INFO 68713 --- [qtp1987166253-40] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-09-24T14:48:03.537+07:00  INFO 68713 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 3
2025-09-24T14:48:03.544+07:00  INFO 68713 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T14:48:03.545+07:00  INFO 68713 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T14:49:04.139+07:00  INFO 68713 --- [qtp1987166253-62] n.d.module.session.ClientSessionManager  : Add a client session id = node012trpgs0ip3ke1drlf80kz1cpv1, token = 5935f52c9fddb56e9b76246cabe26ea3
2025-09-24T14:49:04.140+07:00  INFO 68713 --- [qtp1987166253-40] n.d.module.session.ClientSessionManager  : Add a client session id = node012trpgs0ip3ke1drlf80kz1cpv1, token = 5935f52c9fddb56e9b76246cabe26ea3
2025-09-24T14:49:04.152+07:00  INFO 68713 --- [qtp1987166253-40] n.d.m.c.a.CompanyAuthenticationService   : User rio.vnsgn is logged in successfully system
2025-09-24T14:49:04.152+07:00  INFO 68713 --- [qtp1987166253-62] n.d.m.c.a.CompanyAuthenticationService   : User rio.vnsgn is logged in successfully system
2025-09-24T14:49:06.648+07:00  INFO 68713 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T14:50:02.789+07:00  INFO 68713 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-09-24T14:50:02.799+07:00  INFO 68713 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T14:50:02.800+07:00  INFO 68713 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T14:50:02.800+07:00  INFO 68713 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T14:51:05.997+07:00  INFO 68713 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T14:52:02.153+07:00  INFO 68713 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-24T14:52:02.163+07:00  INFO 68713 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T14:52:02.165+07:00  INFO 68713 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T14:53:05.345+07:00  INFO 68713 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T14:54:01.515+07:00  INFO 68713 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T14:54:01.518+07:00  INFO 68713 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T14:54:06.524+07:00  INFO 68713 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T14:55:04.705+07:00  INFO 68713 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T14:55:04.707+07:00  INFO 68713 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T14:56:00.881+07:00  INFO 68713 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-24T14:56:00.893+07:00  INFO 68713 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T14:56:06.899+07:00  INFO 68713 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T14:57:04.061+07:00  INFO 68713 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T14:58:00.233+07:00  INFO 68713 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T14:58:00.239+07:00  INFO 68713 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T14:58:06.257+07:00  INFO 68713 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T14:59:03.427+07:00  INFO 68713 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T14:59:59.568+07:00  INFO 68713 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 4
2025-09-24T14:59:59.581+07:00  INFO 68713 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T15:00:06.604+07:00  INFO 68713 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:00:06.604+07:00  INFO 68713 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T15:00:06.606+07:00  INFO 68713 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at 15 PM every day
2025-09-24T15:00:06.607+07:00  INFO 68713 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-09-24T15:00:06.607+07:00  INFO 68713 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 3 hour
2025-09-24T15:00:06.607+07:00  INFO 68713 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 24/09/2025@15:00:06+0700
2025-09-24T15:00:06.616+07:00  INFO 68713 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 24/09/2025@15:00:00+0700 to 24/09/2025@15:15:00+0700
2025-09-24T15:00:06.617+07:00  INFO 68713 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 24/09/2025@15:00:00+0700 to 24/09/2025@15:15:00+0700
2025-09-24T15:00:06.617+07:00  INFO 68713 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-24T15:01:02.710+07:00  INFO 68713 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:02:03.846+07:00  INFO 68713 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-24T15:02:03.857+07:00  INFO 68713 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T15:02:05.867+07:00  INFO 68713 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:03:06.972+07:00  INFO 68713 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:04:04.078+07:00  INFO 68713 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-24T15:04:04.086+07:00  INFO 68713 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T15:04:05.092+07:00  INFO 68713 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:05:06.198+07:00  INFO 68713 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:05:06.202+07:00  INFO 68713 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T15:06:03.337+07:00  INFO 68713 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-24T15:06:03.350+07:00  INFO 68713 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T15:06:04.356+07:00  INFO 68713 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:06:14.317+07:00  INFO 68713 --- [qtp1987166253-40] n.d.module.session.ClientSessionManager  : Add a client session id = node012trpgs0ip3ke1drlf80kz1cpv1, token = 5935f52c9fddb56e9b76246cabe26ea3
2025-09-24T15:06:14.328+07:00  INFO 68713 --- [qtp1987166253-40] n.d.m.c.a.CompanyAuthenticationService   : User rio.vnsgn is logged in successfully system
2025-09-24T15:06:14.340+07:00  INFO 68713 --- [qtp1987166253-109] n.d.module.session.ClientSessionManager  : Add a client session id = node012trpgs0ip3ke1drlf80kz1cpv1, token = 5935f52c9fddb56e9b76246cabe26ea3
2025-09-24T15:06:14.349+07:00  INFO 68713 --- [qtp1987166253-109] n.d.m.c.a.CompanyAuthenticationService   : User rio.vnsgn is logged in successfully system
2025-09-24T15:06:33.497+07:00  INFO 68713 --- [qtp1987166253-109] n.d.module.session.ClientSessionManager  : Add a client session id = node012trpgs0ip3ke1drlf80kz1cpv1, token = 5935f52c9fddb56e9b76246cabe26ea3
2025-09-24T15:06:33.528+07:00  INFO 68713 --- [qtp1987166253-109] n.d.m.c.a.CompanyAuthenticationService   : User rio.vnsgn is logged in successfully system
2025-09-24T15:06:33.591+07:00  INFO 68713 --- [qtp1987166253-113] n.d.module.session.ClientSessionManager  : Add a client session id = node012trpgs0ip3ke1drlf80kz1cpv1, token = 5935f52c9fddb56e9b76246cabe26ea3
2025-09-24T15:06:33.624+07:00  INFO 68713 --- [qtp1987166253-113] n.d.m.c.a.CompanyAuthenticationService   : User rio.vnsgn is logged in successfully system
2025-09-24T15:06:37.948+07:00  INFO 68713 --- [qtp1987166253-109] n.d.module.session.ClientSessionManager  : Add a client session id = node012trpgs0ip3ke1drlf80kz1cpv1, token = 5935f52c9fddb56e9b76246cabe26ea3
2025-09-24T15:06:37.957+07:00  INFO 68713 --- [qtp1987166253-109] n.d.m.c.a.CompanyAuthenticationService   : User rio.vnsgn is logged in successfully system
2025-09-24T15:06:38.030+07:00  INFO 68713 --- [qtp1987166253-66] n.d.module.session.ClientSessionManager  : Add a client session id = node012trpgs0ip3ke1drlf80kz1cpv1, token = 5935f52c9fddb56e9b76246cabe26ea3
2025-09-24T15:06:38.097+07:00  INFO 68713 --- [qtp1987166253-66] n.d.m.c.a.CompanyAuthenticationService   : User rio.vnsgn is logged in successfully system
2025-09-24T15:06:55.288+07:00  INFO 68713 --- [qtp1987166253-109] n.d.module.session.ClientSessionManager  : Add a client session id = node012trpgs0ip3ke1drlf80kz1cpv1, token = 5935f52c9fddb56e9b76246cabe26ea3
2025-09-24T15:06:55.298+07:00  INFO 68713 --- [qtp1987166253-109] n.d.m.c.a.CompanyAuthenticationService   : User rio.vnsgn is logged in successfully system
2025-09-24T15:06:55.337+07:00  INFO 68713 --- [qtp1987166253-85] n.d.module.session.ClientSessionManager  : Add a client session id = node012trpgs0ip3ke1drlf80kz1cpv1, token = 5935f52c9fddb56e9b76246cabe26ea3
2025-09-24T15:06:55.358+07:00  INFO 68713 --- [qtp1987166253-85] n.d.m.c.a.CompanyAuthenticationService   : User rio.vnsgn is logged in successfully system
2025-09-24T15:07:03.310+07:00  INFO 68713 --- [qtp1987166253-40] n.d.module.session.ClientSessionManager  : Add a client session id = node012trpgs0ip3ke1drlf80kz1cpv1, token = 5935f52c9fddb56e9b76246cabe26ea3
2025-09-24T15:07:03.312+07:00  INFO 68713 --- [qtp1987166253-109] n.d.module.session.ClientSessionManager  : Add a client session id = node012trpgs0ip3ke1drlf80kz1cpv1, token = 5935f52c9fddb56e9b76246cabe26ea3
2025-09-24T15:07:03.444+07:00  INFO 68713 --- [qtp1987166253-109] n.d.m.c.a.CompanyAuthenticationService   : User rio.vnsgn is logged in successfully system
2025-09-24T15:07:03.568+07:00  INFO 68713 --- [qtp1987166253-40] n.d.m.c.a.CompanyAuthenticationService   : User rio.vnsgn is logged in successfully system
2025-09-24T15:07:06.466+07:00  INFO 68713 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:07:41.354+07:00  INFO 68713 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@359014ce{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-24T15:07:41.356+07:00  INFO 68713 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-24T15:07:41.356+07:00  INFO 68713 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-24T15:07:41.356+07:00  INFO 68713 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-24T15:07:41.356+07:00  INFO 68713 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-24T15:07:41.356+07:00  INFO 68713 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-24T15:07:41.356+07:00  INFO 68713 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-24T15:07:41.356+07:00  INFO 68713 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-24T15:07:41.356+07:00  INFO 68713 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-09-24T15:07:41.356+07:00  INFO 68713 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-09-24T15:07:41.356+07:00  INFO 68713 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-24T15:07:41.356+07:00  INFO 68713 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-09-24T15:07:41.356+07:00  INFO 68713 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-09-24T15:07:41.356+07:00  INFO 68713 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-24T15:07:41.356+07:00  INFO 68713 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-09-24T15:07:41.356+07:00  INFO 68713 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-09-24T15:07:41.370+07:00  INFO 68713 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T15:07:41.461+07:00  INFO 68713 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-24T15:07:41.470+07:00  INFO 68713 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-24T15:07:41.493+07:00  INFO 68713 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-24T15:07:41.499+07:00  INFO 68713 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-24T15:07:41.501+07:00  INFO 68713 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-24T15:07:41.502+07:00  INFO 68713 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-24T15:07:41.503+07:00  INFO 68713 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-24T15:07:41.503+07:00  INFO 68713 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-24T15:07:41.504+07:00  INFO 68713 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-24T15:07:41.504+07:00  INFO 68713 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-24T15:07:41.504+07:00  INFO 68713 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-24T15:07:41.504+07:00  INFO 68713 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-24T15:07:41.504+07:00  INFO 68713 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-24T15:07:41.505+07:00  INFO 68713 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-24T15:07:41.505+07:00  INFO 68713 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-24T15:07:41.506+07:00  INFO 68713 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@4f5080ea{STOPPING}[12.0.15,sto=0]
2025-09-24T15:07:41.510+07:00  INFO 68713 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-24T15:07:41.512+07:00  INFO 68713 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@10ecdd85{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.7154401700365241383/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@47ffce5d{STOPPED}}
