2025-09-24T15:10:11.732+07:00  INFO 72540 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 72540 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-09-24T15:10:11.732+07:00  INFO 72540 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-09-24T15:10:12.413+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:10:12.477+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 61 ms. Found 22 JPA repository interfaces.
2025-09-24T15:10:12.486+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:10:12.488+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-24T15:10:12.488+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:10:12.535+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 47 ms. Found 10 JPA repository interfaces.
2025-09-24T15:10:12.536+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:10:12.539+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-24T15:10:12.548+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:10:12.553+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-09-24T15:10:12.561+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:10:12.564+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 2 JPA repository interfaces.
2025-09-24T15:10:12.564+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:10:12.568+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-09-24T15:10:12.571+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:10:12.575+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-09-24T15:10:12.579+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:10:12.582+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-24T15:10:12.582+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:10:12.583+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-24T15:10:12.583+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:10:12.589+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 10 JPA repository interfaces.
2025-09-24T15:10:12.594+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:10:12.597+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-09-24T15:10:12.600+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:10:12.604+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-09-24T15:10:12.604+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:10:12.611+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-09-24T15:10:12.611+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:10:12.614+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-24T15:10:12.614+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:10:12.615+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-24T15:10:12.615+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:10:12.616+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-09-24T15:10:12.616+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:10:12.620+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-24T15:10:12.620+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:10:12.621+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-09-24T15:10:12.621+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:10:12.621+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-24T15:10:12.621+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:10:12.632+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 19 JPA repository interfaces.
2025-09-24T15:10:12.641+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:10:12.647+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 8 JPA repository interfaces.
2025-09-24T15:10:12.647+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:10:12.650+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-09-24T15:10:12.650+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:10:12.654+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-09-24T15:10:12.654+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:10:12.659+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 9 JPA repository interfaces.
2025-09-24T15:10:12.659+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:10:12.664+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 6 JPA repository interfaces.
2025-09-24T15:10:12.665+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:10:12.673+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 13 JPA repository interfaces.
2025-09-24T15:10:12.674+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:10:12.683+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 14 JPA repository interfaces.
2025-09-24T15:10:12.684+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:10:12.698+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 14 ms. Found 24 JPA repository interfaces.
2025-09-24T15:10:12.699+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:10:12.700+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-24T15:10:12.706+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:10:12.706+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-09-24T15:10:12.706+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:10:12.713+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-09-24T15:10:12.715+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:10:12.751+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 36 ms. Found 66 JPA repository interfaces.
2025-09-24T15:10:12.752+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:10:12.753+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-09-24T15:10:12.758+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-24T15:10:12.760+07:00  INFO 72540 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-09-24T15:10:12.981+07:00  INFO 72540 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-09-24T15:10:12.985+07:00  INFO 72540 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-09-24T15:10:13.262+07:00  WARN 72540 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-09-24T15:10:13.458+07:00  INFO 72540 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-09-24T15:10:13.460+07:00  INFO 72540 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-09-24T15:10:13.472+07:00  INFO 72540 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-09-24T15:10:13.472+07:00  INFO 72540 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1643 ms
2025-09-24T15:10:13.523+07:00  WARN 72540 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-24T15:10:13.523+07:00  INFO 72540 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-09-24T15:10:13.618+07:00  INFO 72540 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@1b13e4fb
2025-09-24T15:10:13.619+07:00  INFO 72540 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-09-24T15:10:13.624+07:00  WARN 72540 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-24T15:10:13.624+07:00  INFO 72540 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-24T15:10:13.629+07:00  INFO 72540 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@3bf92d96
2025-09-24T15:10:13.629+07:00  INFO 72540 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-24T15:10:13.629+07:00  WARN 72540 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-24T15:10:13.629+07:00  INFO 72540 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-09-24T15:10:13.634+07:00  INFO 72540 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@18f37d6e
2025-09-24T15:10:13.634+07:00  INFO 72540 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-09-24T15:10:13.634+07:00  WARN 72540 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-24T15:10:13.634+07:00  INFO 72540 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-09-24T15:10:13.640+07:00  INFO 72540 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@7a213820
2025-09-24T15:10:13.640+07:00  INFO 72540 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-09-24T15:10:13.640+07:00  WARN 72540 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-09-24T15:10:13.640+07:00  INFO 72540 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-09-24T15:10:13.651+07:00  INFO 72540 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@4d484961
2025-09-24T15:10:13.651+07:00  INFO 72540 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-09-24T15:10:13.651+07:00  INFO 72540 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '**********************************************', '***********************************************', '**********************************************'
2025-09-24T15:10:13.694+07:00  INFO 72540 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-09-24T15:10:13.741+07:00  INFO 72540 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@35eb95d3{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.17400736764032479424/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@3c320c8b{STARTED}}
2025-09-24T15:10:13.741+07:00  INFO 72540 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@35eb95d3{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.17400736764032479424/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@3c320c8b{STARTED}}
2025-09-24T15:10:13.743+07:00  INFO 72540 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@1a42da0a{STARTING}[12.0.15,sto=0] @2524ms
2025-09-24T15:10:13.795+07:00  INFO 72540 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-24T15:10:13.821+07:00  INFO 72540 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-09-24T15:10:13.836+07:00  INFO 72540 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-24T15:10:13.957+07:00  INFO 72540 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-24T15:10:14.032+07:00  WARN 72540 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-24T15:10:14.649+07:00  INFO 72540 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-24T15:10:14.660+07:00  INFO 72540 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@443641bf] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-24T15:10:14.787+07:00  INFO 72540 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-24T15:10:14.980+07:00  INFO 72540 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.monitor.activity", "net.datatp.module.wfms", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "net.datatp.module.okr", "cloud.datatp.module.fleet", "net.datatp.module.core.template", "cloud.datatp.gps", "cloud.datatp.module.pegasus.shipment", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "net.datatp.module.company.tmpl", "cloud.datatp.tms", "net.datatp.module.groovy", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "net.datatp.module.resource", "cloud.datatp.vendor", "net.datatp.module.hr", "net.datatp.module.company.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "cloud.datatp.module.odoo", "net.datatp.module.chatbot", "net.datatp.module.core.print", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-09-24T15:10:14.982+07:00  INFO 72540 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-09-24T15:10:14.988+07:00  INFO 72540 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-24T15:10:14.990+07:00  INFO 72540 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-24T15:10:15.014+07:00  INFO 72540 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-24T15:10:15.020+07:00  WARN 72540 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-24T15:10:17.030+07:00  INFO 72540 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-24T15:10:17.031+07:00  INFO 72540 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@356bbed2] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-24T15:10:17.220+07:00  WARN 72540 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-24T15:10:17.221+07:00  WARN 72540 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-24T15:10:17.235+07:00  WARN 72540 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-09-24T15:10:17.235+07:00  WARN 72540 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-09-24T15:10:17.248+07:00  WARN 72540 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-24T15:10:17.248+07:00  WARN 72540 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-09-24T15:10:17.653+07:00  INFO 72540 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-24T15:10:17.659+07:00  INFO 72540 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-24T15:10:17.660+07:00  INFO 72540 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-09-24T15:10:17.682+07:00  INFO 72540 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-09-24T15:10:17.684+07:00  WARN 72540 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-09-24T15:10:18.196+07:00  INFO 72540 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-09-24T15:10:18.197+07:00  INFO 72540 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@72f2c2a2] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-09-24T15:10:18.264+07:00  WARN 72540 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-09-24T15:10:18.264+07:00  WARN 72540 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-09-24T15:10:18.580+07:00  INFO 72540 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-24T15:10:18.610+07:00  INFO 72540 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-09-24T15:10:18.614+07:00  INFO 72540 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-09-24T15:10:18.614+07:00  INFO 72540 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-24T15:10:18.620+07:00  WARN 72540 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-24T15:10:18.745+07:00  INFO 72540 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-09-24T15:10:19.177+07:00  INFO 72540 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-24T15:10:19.180+07:00  INFO 72540 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-09-24T15:10:19.214+07:00  INFO 72540 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-09-24T15:10:19.252+07:00  INFO 72540 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-09-24T15:10:19.313+07:00  INFO 72540 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-09-24T15:10:19.351+07:00  INFO 72540 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-24T15:10:19.382+07:00  INFO 72540 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 24469968ms : this is harmless.
2025-09-24T15:10:19.391+07:00  INFO 72540 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-09-24T15:10:19.394+07:00  INFO 72540 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-09-24T15:10:19.406+07:00  INFO 72540 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 24469956ms : this is harmless.
2025-09-24T15:10:19.408+07:00  INFO 72540 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-09-24T15:10:19.421+07:00  INFO 72540 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-09-24T15:10:19.422+07:00  INFO 72540 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-09-24T15:10:21.318+07:00  INFO 72540 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-09-24T15:10:21.318+07:00  INFO 72540 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-24T15:10:21.319+07:00  WARN 72540 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-24T15:10:21.615+07:00  INFO 72540 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 24/09/2025@15:00:00+0700 to 24/09/2025@15:15:00+0700
2025-09-24T15:10:21.616+07:00  INFO 72540 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 24/09/2025@15:00:00+0700 to 24/09/2025@15:15:00+0700
2025-09-24T15:10:22.186+07:00  INFO 72540 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-09-24T15:10:22.186+07:00  INFO 72540 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-24T15:10:22.187+07:00  WARN 72540 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-09-24T15:10:22.469+07:00  INFO 72540 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-09-24T15:10:22.469+07:00  INFO 72540 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-09-24T15:10:22.469+07:00  INFO 72540 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-09-24T15:10:22.469+07:00  INFO 72540 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-09-24T15:10:22.469+07:00  INFO 72540 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-09-24T15:10:24.180+07:00  WARN 72540 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: f830cc8e-9cbc-479c-8372-7a84350612be

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-09-24T15:10:24.184+07:00  INFO 72540 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-09-24T15:10:24.477+07:00  INFO 72540 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-24T15:10:24.477+07:00  INFO 72540 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-09-24T15:10:24.478+07:00  INFO 72540 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-09-24T15:10:24.478+07:00  INFO 72540 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-24T15:10:24.478+07:00  INFO 72540 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-09-24T15:10:24.478+07:00  INFO 72540 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-09-24T15:10:24.478+07:00  INFO 72540 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-24T15:10:24.478+07:00  INFO 72540 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-09-24T15:10:24.478+07:00  INFO 72540 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-09-24T15:10:24.478+07:00  INFO 72540 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-24T15:10:24.478+07:00  INFO 72540 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-09-24T15:10:24.478+07:00  INFO 72540 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-09-24T15:10:24.480+07:00  INFO 72540 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-24T15:10:24.480+07:00  INFO 72540 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-24T15:10:24.480+07:00  INFO 72540 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-24T15:10:24.550+07:00  INFO 72540 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-24T15:10:24.550+07:00  INFO 72540 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-24T15:10:24.552+07:00  INFO 72540 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-09-24T15:10:24.559+07:00  INFO 72540 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@591fea87{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-24T15:10:24.560+07:00  INFO 72540 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-09-24T15:10:24.561+07:00  INFO 72540 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-09-24T15:10:24.587+07:00  INFO 72540 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-09-24T15:10:24.587+07:00  INFO 72540 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-09-24T15:10:24.593+07:00  INFO 72540 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 13.123 seconds (process running for 13.374)
2025-09-24T15:10:26.282+07:00  INFO 72540 --- [qtp1504482477-37] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node05esi31pb4oc7112ftl8wv96w21
2025-09-24T15:10:26.282+07:00  INFO 72540 --- [qtp1504482477-35] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0ddu3pwuonj941roeax7v9atig0
2025-09-24T15:10:26.518+07:00  INFO 72540 --- [qtp1504482477-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0ddu3pwuonj941roeax7v9atig0, token = 5935f52c9fddb56e9b76246cabe26ea3
2025-09-24T15:10:26.518+07:00  INFO 72540 --- [qtp1504482477-37] n.d.module.session.ClientSessionManager  : Add a client session id = node05esi31pb4oc7112ftl8wv96w21, token = 5935f52c9fddb56e9b76246cabe26ea3
2025-09-24T15:10:26.915+07:00  INFO 72540 --- [qtp1504482477-37] n.d.m.c.a.CompanyAuthenticationService   : User rio.vnsgn is logged in successfully system
2025-09-24T15:10:26.922+07:00  INFO 72540 --- [qtp1504482477-35] n.d.m.c.a.CompanyAuthenticationService   : User rio.vnsgn is logged in successfully system
2025-09-24T15:11:06.572+07:00  INFO 72540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:11:27.605+07:00  INFO 72540 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T15:11:27.618+07:00  INFO 72540 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T15:12:02.681+07:00  INFO 72540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:13:05.785+07:00  INFO 72540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:13:26.868+07:00  INFO 72540 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-09-24T15:13:26.894+07:00  INFO 72540 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T15:14:06.963+07:00  INFO 72540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:15:05.067+07:00  INFO 72540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:15:05.090+07:00  INFO 72540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-09-24T15:15:05.095+07:00  INFO 72540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T15:15:05.106+07:00  INFO 72540 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 24/09/2025@15:15:05+0700
2025-09-24T15:15:05.126+07:00  INFO 72540 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 24/09/2025@15:15:00+0700 to 24/09/2025@15:30:00+0700
2025-09-24T15:15:05.126+07:00  INFO 72540 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 24/09/2025@15:15:00+0700 to 24/09/2025@15:30:00+0700
2025-09-24T15:15:31.238+07:00  INFO 72540 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-09-24T15:15:31.250+07:00  INFO 72540 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T15:16:06.314+07:00  INFO 72540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:16:18.644+07:00  INFO 72540 --- [qtp1504482477-40] n.d.module.session.ClientSessionManager  : Add a client session id = node05esi31pb4oc7112ftl8wv96w21, token = 5935f52c9fddb56e9b76246cabe26ea3
2025-09-24T15:16:18.655+07:00  INFO 72540 --- [qtp1504482477-40] n.d.m.c.a.CompanyAuthenticationService   : User rio.vnsgn is logged in successfully system
2025-09-24T15:16:18.666+07:00  INFO 72540 --- [qtp1504482477-71] n.d.module.session.ClientSessionManager  : Add a client session id = node05esi31pb4oc7112ftl8wv96w21, token = 5935f52c9fddb56e9b76246cabe26ea3
2025-09-24T15:16:18.670+07:00  INFO 72540 --- [qtp1504482477-71] n.d.m.c.a.CompanyAuthenticationService   : User rio.vnsgn is logged in successfully system
2025-09-24T15:16:58.677+07:00  INFO 72540 --- [qtp1504482477-41] n.d.module.session.ClientSessionManager  : Add a client session id = node05esi31pb4oc7112ftl8wv96w21, token = 5935f52c9fddb56e9b76246cabe26ea3
2025-09-24T15:16:58.686+07:00  INFO 72540 --- [qtp1504482477-72] n.d.module.session.ClientSessionManager  : Add a client session id = node05esi31pb4oc7112ftl8wv96w21, token = 5935f52c9fddb56e9b76246cabe26ea3
2025-09-24T15:16:58.698+07:00  INFO 72540 --- [qtp1504482477-72] n.d.m.c.a.CompanyAuthenticationService   : User rio.vnsgn is logged in successfully system
2025-09-24T15:16:58.698+07:00  INFO 72540 --- [qtp1504482477-41] n.d.m.c.a.CompanyAuthenticationService   : User rio.vnsgn is logged in successfully system
2025-09-24T15:17:04.417+07:00  INFO 72540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:17:20.776+07:00  INFO 72540 --- [qtp1504482477-40] n.d.m.c.a.CompanyAuthenticationService   : User rio.vnsgn logout successfully 
2025-09-24T15:17:23.954+07:00  INFO 72540 --- [qtp1504482477-72] n.d.module.session.ClientSessionManager  : Add a client session id = node05esi31pb4oc7112ftl8wv96w21, token = 2a9ec688fede8ee860dd8809db6a4091
2025-09-24T15:17:23.960+07:00  INFO 72540 --- [qtp1504482477-72] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T15:17:31.468+07:00  INFO 72540 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-09-24T15:17:31.473+07:00  INFO 72540 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T15:17:54.111+07:00  INFO 72540 --- [qtp1504482477-35] n.d.module.session.ClientSessionManager  : Add a client session id = node05esi31pb4oc7112ftl8wv96w21, token = 2a9ec688fede8ee860dd8809db6a4091
2025-09-24T15:17:54.117+07:00  INFO 72540 --- [qtp1504482477-35] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T15:17:54.130+07:00  INFO 72540 --- [qtp1504482477-63] n.d.module.session.ClientSessionManager  : Add a client session id = node05esi31pb4oc7112ftl8wv96w21, token = 2a9ec688fede8ee860dd8809db6a4091
2025-09-24T15:17:54.134+07:00  INFO 72540 --- [qtp1504482477-63] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T15:18:06.537+07:00  INFO 72540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:18:35.577+07:00  INFO 72540 --- [qtp1504482477-41] n.d.module.session.ClientSessionManager  : Add a client session id = node05esi31pb4oc7112ftl8wv96w21, token = 2a9ec688fede8ee860dd8809db6a4091
2025-09-24T15:18:35.579+07:00  INFO 72540 --- [qtp1504482477-63] n.d.module.session.ClientSessionManager  : Add a client session id = node05esi31pb4oc7112ftl8wv96w21, token = 2a9ec688fede8ee860dd8809db6a4091
2025-09-24T15:18:35.588+07:00  INFO 72540 --- [qtp1504482477-41] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T15:18:35.588+07:00  INFO 72540 --- [qtp1504482477-63] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T15:19:03.628+07:00  INFO 72540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:19:30.714+07:00  INFO 72540 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 10, expire count 0
2025-09-24T15:19:30.722+07:00  INFO 72540 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T15:19:34.585+07:00  INFO 72540 --- [qtp1504482477-72] n.d.module.session.ClientSessionManager  : Add a client session id = node05esi31pb4oc7112ftl8wv96w21, token = 2a9ec688fede8ee860dd8809db6a4091
2025-09-24T15:19:34.597+07:00  INFO 72540 --- [qtp1504482477-72] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T15:19:34.619+07:00  INFO 72540 --- [qtp1504482477-74] n.d.module.session.ClientSessionManager  : Add a client session id = node05esi31pb4oc7112ftl8wv96w21, token = 2a9ec688fede8ee860dd8809db6a4091
2025-09-24T15:19:34.661+07:00  INFO 72540 --- [qtp1504482477-74] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T15:19:50.586+07:00  INFO 72540 --- [qtp1504482477-71] n.d.module.session.ClientSessionManager  : Add a client session id = node05esi31pb4oc7112ftl8wv96w21, token = 2a9ec688fede8ee860dd8809db6a4091
2025-09-24T15:19:50.590+07:00  INFO 72540 --- [qtp1504482477-35] n.d.module.session.ClientSessionManager  : Add a client session id = node05esi31pb4oc7112ftl8wv96w21, token = 2a9ec688fede8ee860dd8809db6a4091
2025-09-24T15:19:50.612+07:00  INFO 72540 --- [qtp1504482477-35] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T15:19:50.612+07:00  INFO 72540 --- [qtp1504482477-71] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T15:20:06.797+07:00  INFO 72540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:20:06.799+07:00  INFO 72540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T15:20:33.426+07:00  INFO 72540 --- [qtp1504482477-72] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-24T15:20:33.426+07:00  INFO 72540 --- [qtp1504482477-71] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-09-24T15:20:33.530+07:00  INFO 72540 --- [qtp1504482477-72] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-24T15:20:33.530+07:00  INFO 72540 --- [qtp1504482477-71] c.d.f.sales.partner.PartnerReportLogic   : Retrieved 0 records
2025-09-24T15:21:02.883+07:00  INFO 72540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:21:13.709+07:00  INFO 72540 --- [Scheduler-1473083361-1] n.d.m.session.AppHttpSessionListener     : The session node0ddu3pwuonj941roeax7v9atig0 is destroyed.
2025-09-24T15:21:30.966+07:00  INFO 72540 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 10, expire count 0
2025-09-24T15:21:30.987+07:00  INFO 72540 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-09-24T15:22:06.042+07:00  INFO 72540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:22:54.924+07:00  INFO 72540 --- [qtp1504482477-71] n.d.module.session.ClientSessionManager  : Add a client session id = node05esi31pb4oc7112ftl8wv96w21, token = 2a9ec688fede8ee860dd8809db6a4091
2025-09-24T15:22:54.938+07:00  INFO 72540 --- [qtp1504482477-71] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T15:22:54.956+07:00  INFO 72540 --- [qtp1504482477-39] n.d.module.session.ClientSessionManager  : Add a client session id = node05esi31pb4oc7112ftl8wv96w21, token = 2a9ec688fede8ee860dd8809db6a4091
2025-09-24T15:22:54.962+07:00  INFO 72540 --- [qtp1504482477-39] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T15:23:02.138+07:00  INFO 72540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:23:30.199+07:00  INFO 72540 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-09-24T15:23:30.207+07:00  INFO 72540 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T15:24:05.270+07:00  INFO 72540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:24:09.590+07:00  INFO 72540 --- [qtp1504482477-71] n.d.module.session.ClientSessionManager  : Add a client session id = node05esi31pb4oc7112ftl8wv96w21, token = 2a9ec688fede8ee860dd8809db6a4091
2025-09-24T15:24:09.591+07:00  INFO 72540 --- [qtp1504482477-63] n.d.module.session.ClientSessionManager  : Add a client session id = node05esi31pb4oc7112ftl8wv96w21, token = 2a9ec688fede8ee860dd8809db6a4091
2025-09-24T15:24:09.594+07:00  INFO 72540 --- [qtp1504482477-71] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T15:24:09.596+07:00  INFO 72540 --- [qtp1504482477-63] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T15:24:20.959+07:00  INFO 72540 --- [qtp1504482477-40] n.d.module.session.ClientSessionManager  : Add a client session id = node05esi31pb4oc7112ftl8wv96w21, token = 2a9ec688fede8ee860dd8809db6a4091
2025-09-24T15:24:20.988+07:00  INFO 72540 --- [qtp1504482477-40] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T15:24:21.009+07:00  INFO 72540 --- [qtp1504482477-65] n.d.module.session.ClientSessionManager  : Add a client session id = node05esi31pb4oc7112ftl8wv96w21, token = 2a9ec688fede8ee860dd8809db6a4091
2025-09-24T15:24:21.027+07:00  INFO 72540 --- [qtp1504482477-65] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T15:24:21.530+07:00  INFO 72540 --- [qtp1504482477-63] n.d.module.session.ClientSessionManager  : Add a client session id = node05esi31pb4oc7112ftl8wv96w21, token = 2a9ec688fede8ee860dd8809db6a4091
2025-09-24T15:24:21.537+07:00  INFO 72540 --- [qtp1504482477-63] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T15:24:21.568+07:00  INFO 72540 --- [qtp1504482477-66] n.d.module.session.ClientSessionManager  : Add a client session id = node05esi31pb4oc7112ftl8wv96w21, token = 2a9ec688fede8ee860dd8809db6a4091
2025-09-24T15:24:21.574+07:00  INFO 72540 --- [qtp1504482477-66] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T15:24:33.604+07:00  INFO 72540 --- [qtp1504482477-71] n.d.module.session.ClientSessionManager  : Add a client session id = node05esi31pb4oc7112ftl8wv96w21, token = 2a9ec688fede8ee860dd8809db6a4091
2025-09-24T15:24:33.620+07:00  INFO 72540 --- [qtp1504482477-71] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T15:24:33.639+07:00  INFO 72540 --- [qtp1504482477-80] n.d.module.session.ClientSessionManager  : Add a client session id = node05esi31pb4oc7112ftl8wv96w21, token = 2a9ec688fede8ee860dd8809db6a4091
2025-09-24T15:24:33.643+07:00  INFO 72540 --- [qtp1504482477-80] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T15:24:34.536+07:00  INFO 72540 --- [qtp1504482477-80] n.d.module.session.ClientSessionManager  : Add a client session id = node05esi31pb4oc7112ftl8wv96w21, token = 2a9ec688fede8ee860dd8809db6a4091
2025-09-24T15:24:34.551+07:00  INFO 72540 --- [qtp1504482477-80] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T15:24:34.552+07:00  INFO 72540 --- [qtp1504482477-66] n.d.module.session.ClientSessionManager  : Add a client session id = node05esi31pb4oc7112ftl8wv96w21, token = 2a9ec688fede8ee860dd8809db6a4091
2025-09-24T15:24:34.565+07:00  INFO 72540 --- [qtp1504482477-66] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T15:25:06.306+07:00  INFO 72540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:25:06.310+07:00  INFO 72540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-09-24T15:25:29.393+07:00  INFO 72540 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 9, expire count 1
2025-09-24T15:25:29.415+07:00  INFO 72540 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-09-24T15:26:04.472+07:00  INFO 72540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:27:05.169+07:00  INFO 72540 --- [qtp1504482477-71] n.d.module.session.ClientSessionManager  : Add a client session id = node05esi31pb4oc7112ftl8wv96w21, token = 2a9ec688fede8ee860dd8809db6a4091
2025-09-24T15:27:05.171+07:00  INFO 72540 --- [qtp1504482477-63] n.d.module.session.ClientSessionManager  : Add a client session id = node05esi31pb4oc7112ftl8wv96w21, token = 2a9ec688fede8ee860dd8809db6a4091
2025-09-24T15:27:05.186+07:00  INFO 72540 --- [qtp1504482477-71] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T15:27:05.186+07:00  INFO 72540 --- [qtp1504482477-63] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T15:27:06.536+07:00  INFO 72540 --- [qtp1504482477-71] n.d.module.session.ClientSessionManager  : Add a client session id = node05esi31pb4oc7112ftl8wv96w21, token = 2a9ec688fede8ee860dd8809db6a4091
2025-09-24T15:27:06.537+07:00  INFO 72540 --- [qtp1504482477-72] n.d.module.session.ClientSessionManager  : Add a client session id = node05esi31pb4oc7112ftl8wv96w21, token = 2a9ec688fede8ee860dd8809db6a4091
2025-09-24T15:27:06.543+07:00  INFO 72540 --- [qtp1504482477-72] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T15:27:06.544+07:00  INFO 72540 --- [qtp1504482477-71] n.d.m.c.a.CompanyAuthenticationService   : User jack.vnhph is logged in successfully system
2025-09-24T15:27:06.577+07:00  INFO 72540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:27:28.616+07:00  INFO 72540 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 11
2025-09-24T15:27:28.619+07:00  INFO 72540 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T15:28:03.677+07:00  INFO 72540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:29:06.826+07:00  INFO 72540 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-09-24T15:29:27.895+07:00  INFO 72540 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 11, expire count 2
2025-09-24T15:29:27.911+07:00  INFO 72540 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-09-24T15:29:39.800+07:00  INFO 72540 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@591fea87{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-09-24T15:29:39.801+07:00  INFO 72540 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-09-24T15:29:39.801+07:00  INFO 72540 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-09-24T15:29:39.801+07:00  INFO 72540 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-09-24T15:29:39.801+07:00  INFO 72540 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-09-24T15:29:39.802+07:00  INFO 72540 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-09-24T15:29:39.802+07:00  INFO 72540 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-09-24T15:29:39.802+07:00  INFO 72540 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-09-24T15:29:39.802+07:00  INFO 72540 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-09-24T15:29:39.802+07:00  INFO 72540 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-09-24T15:29:39.802+07:00  INFO 72540 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-09-24T15:29:39.802+07:00  INFO 72540 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-09-24T15:29:39.802+07:00  INFO 72540 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-09-24T15:29:39.802+07:00  INFO 72540 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-09-24T15:29:39.802+07:00  INFO 72540 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-09-24T15:29:39.802+07:00  INFO 72540 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-09-24T15:29:39.818+07:00  INFO 72540 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-09-24T15:29:39.904+07:00  INFO 72540 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-09-24T15:29:39.910+07:00  INFO 72540 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-09-24T15:29:39.935+07:00  INFO 72540 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-24T15:29:39.936+07:00  INFO 72540 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-24T15:29:39.938+07:00  INFO 72540 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-24T15:29:39.938+07:00  INFO 72540 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-24T15:29:39.939+07:00  INFO 72540 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-24T15:29:39.939+07:00  INFO 72540 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-09-24T15:29:39.940+07:00  INFO 72540 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-09-24T15:29:39.940+07:00  INFO 72540 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-09-24T15:29:39.940+07:00  INFO 72540 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-09-24T15:29:39.940+07:00  INFO 72540 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-09-24T15:29:39.942+07:00  INFO 72540 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-09-24T15:29:39.942+07:00  INFO 72540 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-09-24T15:29:39.942+07:00  INFO 72540 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-09-24T15:29:39.944+07:00  INFO 72540 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@1a42da0a{STOPPING}[12.0.15,sto=0]
2025-09-24T15:29:39.946+07:00  INFO 72540 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-09-24T15:29:39.947+07:00  INFO 72540 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@35eb95d3{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.17400736764032479424/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@3c320c8b{STOPPED}}
