---
sidebar_position: 1
sidebar: false
hide_table_of_contents: true
displayed_sidebar: userSidebar
---

# Task Request - Công cụ request lỗi trên phần mềm.

> **Mục đích:** Công cụ gửi yêu cầu hỗ trợ và báo lỗi trên phần mềm một cách có hệ thống.

## 🚀 Cách truy cập

**Bước 1:** Vào module nghiệp vụ
- Góc trái màn hình → Click vào logo tên công ty
- Chọn **User** → **Project** → **Task Requests**

![Cá<PERSON> truy cập](./img/access.gif)

---

## 👤 Phần 1: Người gửi yêu cầu

### Tạo yêu cầu mới

**Bước 1:** Tạo request
- Click nút **"New Request"** ở góc phải màn hình

![Tạo request mới](./img/Picture1.png)

**Bước 2:** Điền thông tin yêu cầu
- <PERSON><PERSON><PERSON> thoại "New Task Request" sẽ xuất hiện

![Form tạo request](./img/Picture2.png)

### 📝 Hướng dẫn điền thông tin

#### Các trường bắt buộc:

| Trường thông tin | Mô tả | Ví dụ |
|------------------|-------|-------|
| **Request Type** | Loại yêu cầu | File Access (mở khóa file)<br/>Invoice Correction (điều chỉnh hóa đơn) |
| **Approved** | Người xử lý yêu cầu | Ms Quyên kế toán |
| **Reference Note** | Số tham chiếu | Số FILE hoặc số HBL |
| **Label** | Tiêu đề ngắn gọn | "XIN MỞ FILE DO ABC..." |
| **Send Email (Cc)** | Người liên quan | Trưởng bộ phận, người liên quan khác |
| **Description** | Mô tả chi tiết | "Khách hàng ABC thay đổi địa chỉ..." |

#### Minh họa điền form:

![Loại yêu cầu](./img/Picture3.png)
*Chọn loại yêu cầu*

![Người xử lý](./img/Picture4.png)
*Chọn người xử lý*

![Ví dụ hoàn chỉnh](./img/Picture5.png)
*Ví dụ form đã điền đầy đủ*

**Bước 3:** Lưu và gửi
- Click **"Save"** để gửi yêu cầu

### 📊 Quản lý yêu cầu đã gửi

- Tất cả request sẽ hiển thị trong bảng tổng hợp
- **Lưu ý:** Để xóa request, vui lòng liên hệ IT

![Danh sách request](./img/Picture6.png)

#### 🔐 Phân quyền xem:
Mỗi tài khoản chỉ thấy được:
- Request mình đã gửi
- Request được gửi cho mình
- Request mình được CC

### 📧 Thông báo phản hồi

Khi có phản hồi, hệ thống sẽ gửi email thông báo đến:
- Người gửi request
- Những người được CC

![Email thông báo](./img/Picture7.png)

---

## 👥 Phần 2: Người nhận và xử lý yêu cầu

### 📨 Nhận thông báo

Khi có request mới, người xử lý sẽ nhận được email thông báo từ hệ thống.

![Email thông báo cho người xử lý](./img/Picture7.png)

### ⚡ Cách xử lý yêu cầu

**Tùy chọn phản hồi:**
- ✅ **Approved** (Chấp thuận)
- ❌ **Reject** (Từ chối)

**Nơi có thể phản hồi:**
- Trực tiếp trên email
- Hoặc trên hệ thống CRM

### 📈 Tính năng nâng cao

#### 📋 Export Excel
- Tổng hợp nhiều request thành file Excel
- Hỗ trợ xử lý hàng loạt

#### 🏷️ Đánh giá mức độ ưu tiên
Sau khi xử lý, người nhận sẽ đánh giá mức độ của request:

| Mức độ | Ý nghĩa |
|--------|---------|
| 🟢 **Low** | Ưu tiên thấp |
| 🟡 **Medium** | Ưu tiên trung bình |
| 🔴 **High** | Ưu tiên cao |

---

## 💡 Lưu ý quan trọng

- ✓ Điền đầy đủ thông tin để xử lý nhanh chóng
- ✓ CC đúng người liên quan
- ✓ Mô tả rõ ràng vấn đề cần hỗ trợ
- ✓ Liên hệ IT nếu cần xóa request