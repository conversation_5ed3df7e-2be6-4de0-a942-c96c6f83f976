### Partner - Partner Request - Permission.

1. Partner - Partner Request
- PIC: Dan + Nhat
- Task:
  + Sales request tạo partner => PIC Approve => Create Partner in BFS => Change Partner status (Validate)
  + Implement phân quyền: setup user như sau:
    + Quyền view/ edit/ approve
    + Scope Self only |COMPANY_ONLY | GROUP_ALL | NONE
    + Resources Customer/ Agent/ Coloader/ Agent Potential/ Customer Lead. +
  + Implement Partner Request:
    + Mà<PERSON> hình list request của user thực hiện. (cancel / resend)
    + Mà<PERSON> hình list request cho user approve. (active approve/ reject)
    + Gửi mail thông báo đến người liên quan.
  + Cập nhật lại Api Partner, bổ sung thông tin (đầy đủ hơn) theo api a Quý cập nhật.
  + Documentation.

1. Nhat + Dan
  - [IN_PROGRESS] Partner Request (AGENT|CUSTOMER|COLOADER)
    + Tạo Agent trong Agent List => Tạo Partner Request với status = NEW.

    + <PERSON><PERSON> sung màn hình Partner Request List trong menu UI Partners.
      * space = User: Hiển thị danh sách các request do user tạo. User có thể Cancel Request khi Request ở trạng thái NEW.
      * space = Company: Hiển thị danh sách tất cả các request.
      * space = System: Hiển thị danh sách tất cả các request, Approve/Reject Request ở đây.

    + Bổ sung Btn Approve/Reject/Cancel trong màn hình Partner Request:
      * Approve: Hiển thị form Partner Request. Submit form => cập nhật status = APPROVED => gọi API tạo BFSOne Partner. => Gửi mail thông báo cho người liên quan.
      * Reject: Hiển thị form Partner Request. Submit form => cập nhật status = REJECTED => Gửi mail thông báo cho người liên quan.
        Đối với Partner Request bị Reject, User có thể tiếp gửi lại Request ở màn hình Request List.
      * Cancel: cập nhật Partner Request status = CANCELLED.

    + Cập nhật document hướng dẫn tính năng Request Partner
