---
sidebar_position: 2
hide_table_of_contents: true
displayed_sidebar: developerSidebar
---

# DataTP CRM Backlog

## Tasks

**Sprint Rules:**

- Sprint duration: 1 week (Monday-Sunday)
- Daily status updates required
- Incomplete tasks return to backlog for next sprint prioritization
- <PERSON><PERSON><PERSON> tasks làm đi làm lại, là<PERSON> hỏng không note vào backlog mà tự giác fix, sửa.

## Current Sprint - Tasks.

I. CRM Maintenance:

2. An.

3. Dan.
- MSA Python: replace pyodbc -> sqlalchemy
  Cập nhật logic, service liên quan.

- Review code:
  + rename SaleDailyTask -> TaskCalendar

II. Feature:

1. Partner - Partner Request
- PIC: Dan + Nhat
- Task:
  + Sales request tạo partner => PIC Approve => Create Partner in BFS => Change Partner status (Validate)
  + Implement phân quyền: setup user như sau:
    + Quyền: view/ edit/ approve
    + Scope: Self only | COMPANY_ONLY | GROUP_ALL | NONE
    + Resources: Customer/ Agent/ Coloader/ Agent Potential/ Customer Lead. +
  + Implement Partner Request:
    + <PERSON><PERSON><PERSON> hình list request của user thực hiện. (cancel / resend)
    + <PERSON><PERSON><PERSON> hình list request cho user approve. (active approve/ reject)
    + Gửi mail thông báo đến người liên quan.
  + Cập nhật lại Api Partner, bổ sung thông tin (đầy đủ hơn) theo api a Quý cập nhật.
  + Documentation.

_Chi tiết task, mô tả xem ở feature_

## Backlog
1. [Nhat] - Resend Internal Booking:
  + Tạo mới InquiryRequest + Booking + BookingInquiry + BookingCharge dựa theo Booking cũ.
  + Tạo mới Quotation + QuotationBooking + QuotationCharge dựa theo Booking cũ.
  + API tạo BFSOne Internal Booking.

2. [Nhat] + [An]
  - Tìm kiếm inquiry trên Quotation WorkBoard: mặc dù mỗi inquiry đã có mã Ref, nhưng sale vẫn chưa tìm dc inquiry để nhập feedback
    => Chỉnh sửa Quotation Workboard, hiển thị BulkCargoInquiryRequest lên màn hình Quotation Workboard
  - Feedback của sale trên CRM phải có thông báo qua email.	mỗi khi sale nhập feedback trên CRM thì đồng thời 1 email sẽ gửi nội dung đó cho BBT
  - Các lô hàng freehand của BBT hoặc hàng của agent, vp nước ngoài, ko có link mail trong hệ thống. Khi BBT tạo inquiry cho những đối tượng này và báo giá thì ko cần gửi mail cho chính BBT nữa.
    => Bổ sung thêm trường Customer Email, nếu có Customer Email, gửi email cho Customer Email thay vì Saleman Email.

5. [An] - Feedback Inquiry hàng rời.
```
a note lại 1 số những sửa đổi như này nhé:
8. thêm 1 cột đặt thời gian remind sale feedback, lặp remind (theo từng giờ, theo ngày)
9. thêm 1 cột yêu cầu deadline sale phải phản hồi, nội dung này sẽ đi kèm khi mà a báo giá trên CRM
10. tạo hệ thống chấm điểm feedback của sale, để đánh giá sự tích cực phản hồi của sale
11. lọc các inquiry theo tiêu chí hàng nhập, xuất, theo khu vực địa lý
****Các báo cáo cần tạo:
1. tỷ trọng các inquiry theo văn phòng theo tháng, lọc theo ngày nhập inquiry
2. tỷ trọng các loại inquiry : FIXED, NM, NR, NF, checking, tính từ inquiry đầu tiên lọc tới ngày cuối tháng báo cáo và trừ đi những inquiry DROPPED tới ngày cuối cùng của tháng trước.
3. tỷ trọng hàng nhập theo vùng, hàng xuất theo vùng, lọc theo ngày nhập inquiry
```

6. [An] - Feedback Inquiry, Bảng giá Trucking

4. Req by Mr. Minh Sale/ Mr Hai BD - Chỉnh sửa lại UI Task Calendar, Task Customer.
  ```
    Excel báo cáo performance sale.
      Cào bằng, tổng hợp dữ liệu toàn bộ sales export ra file excel.
  ```

4. Dashboard Sale - Bản đồ khách hàng.
- Idea:
    Nhìn được xem BEE phủ được bao % ở các khu vực rồi.
    ```
    ý a là ví du Đình Vũ có 1000 công ty ( đếm Customer và LEAD )
      Bee đang làm dc 500 rồi ( Customers )
      và 500 LEAD ( chưa win )
      -> phủ được 50%
    ```

6. [An] - Tạo Agents bằng file excel.
  - Lúc làm nhắn Đàn để lấy template.

2. Thông báo những giá mới được update/đang promote. #pending
  - Theo dõi lịch sử search giá, gửi báo giá theo các tuyến của saleman, theo dõi lịch sử cập nhật, chỉnh sửa giá của pricing.
    Từ đó, gửi lại thông tin giá cho salesman (giá tăng, giá giảm so với giá gần nhất, người cập nhật, số lượng cập nhật, chi tiết một số giá mẫu, ...)
  - Ở màn hình tìm kiếm giá:
    - Từng line giá nên có icon theo chiều hướng lên/ xuống để thể hiện giá đó đang tăng hay giảm so với lần cập nhật trước hoặc trung bình cộng của kỳ trước (theo rule setup).
    - Viết cron cập nhật vào 8h hàng ngày.
      - Tạo thêm trường ở bảng giá FCL để thể hiện giá tăng/ giảm (%) so với lần cập nhật trước.
      - Cron sẽ chạy vào 8h hàng ngày, check các giá FCL mới nhất, nếu giá > giá cũ thì update field trên.

  - Tạo thêm một màn hình tổng quan về việc cập nhật giá theo các tuyến của pricing (tuyến nào, giá nào, ngày cập nhật, ai cập nhật, ...)
    để sales có thể theo dõi các giá mới cập nhật trên phần mềm.

5. Partner Lost. `PENDING`
    ```
    ví dụ : có 1 customers ( gọi là CS ) nhưng k làm với BEE nữa.
    Giờ các sale tra thấy quá thời gian -> Auto tiếp cận. Như cái CRM Cũ thì có MST rồi k tạo đc LEAD hay gì
    nên cũng k thể biết là sale tiếp cận bi trùng k ấy e.
    ```
    => Lead/ CS nếu quá hạn thì để có thể xem được (có thể set để TBP xem) => xong tự allocate cho từng sales phù hợp, hoặc sales tự req để xin.

-----------------------------------------------
### Planning:
1. Implement DataTPServiceClient -> ServiceCall
- Enhance service để các app, module khác sử dụng gửi mail/ zalo qua api. (Thay vì gọi thẳng service như hiện tại)
- Tách logic, code, lib để communication như một microservice độc lập.

2. Keycloak SSO cho DataTP CRM.
 - Ngiên cứu, tích hợp vào app DataTP CRM, discuss với Đạt Lương.

3. Micro Frontend cho DataTP CRM.

4. Mail ticket: Nghiên cứu, tìm giải pháp, case study.



