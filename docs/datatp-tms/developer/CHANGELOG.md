---
sidebar_position: 2
---

# Changelog

All notable changes to this project will be documented in this file.

### [Unreleased]

1. Tasks:
   [Quan]
      - Tạo TMSHouseBill khi verify truck, cost BFSOne.
      - Clean code findBFSOnePartner tại TMSCustomer, TMSCarrier, TMSAgent.
      - Tại màn hình TMSBill thêm nút lọc các Bill chưa tạo VendorBill.
   [Chien]
      - Fix bugs input address màn hình VehicleTripGoodsTrackingList
      - Cập nhật màn VendorBill cho phép lọc nhanh các lô chưa nhập cont/thông tin xe/Cost

### [R20250915]

-[Chi<PERSON>]
L<PERSON>u lại bảng báo cáo tạm thời cho thầu phụ,
Fix public view báo cáo cho thầu phụ
Thêm báo cáo Fleet App
Fix chức năng att TMSBill, show file att sang app VehicleTripGoodsTrackingList

### [R20250909]

1. Tasks: -[Quan]:
   <PERSON><PERSON><PERSON> màn hình hiển thị TMSBill lọc theo trạng thái đã đẩy/ ch<PERSON>a đẩy cước, thông tin xe sang BFSOne.
   Thêm tab màn hình theo dõi tiến độ push thanh toán cước, thông tin xe sang BFSOne -[Chien]
   Thêm public view báo cáo cho thầu phụ
   Fix màn hình VehicleTripGoodsTrackingList, TMSBillList

### [R20250907]

1. Tasks: -[Chien]:
   Vehicle Fleet thêm trường containerShippingSupport, truckShippingSupport phân loại thầu phụ hỗ trợ các phương thức vận tải
   Chức năng Send Vendor cho phép chọn phương thức vận tải TRUCK/CONATAINER và đánh dấu dòng Mark Color các file push cho Điều Vận
   Viết chức năng send thông tin xe cho khách hàng cho màn VehicleTripGoodsTrackingList

- [Phương]
  Clean code và cập nhật thêm/sửa/xóa màn VehicleList
  Hoàn thành template hbs form mail khách hàng

### [R20250904]

1. Tasks: -[Chien]:
   Thêm trường phân loại lô hàng đường ngắn/đường dài
   Màn hình VehicleTripGoodsTrackingList cho phép lọc nhanh theo loại tuyến đường ngắn/dài/chưa phân loại
   Đổi màu dòng màn VehicleTripGoodsTrackingList
   Clear code groovy TMSBillSql
   Fix lại WInput componentWillUnmount

### [R20250902]

1. Tasks: -[Chien]:
   Thêm chức năng Assign Bill cho PIC có thể Assign lô hàng cho người dùng khác
   Update giao diện TMSVendorBillList
   Fix bugs Search TMSBill

### [R20250828]

1. Tasks: -[Chien]:
   Fix bugs BFSOne Cost template merge tính toán sai
   Cập nhật chức năng Check log TMSBillList kiểm tra thông tin khi thầu phụ input data
   Update chức năng focus/unfocus màn VehicleTripGoodsTrackingList

### [R20250828]

1. Tasks:

- [Quan]: Viết script remove Employee và init dữ liệu PartnerAccount, phân quyền cho các Thầu Phụ.
- [Chien]:
  Update from nhập thông tin xe từ màn hình list
  Template Truck format lại điểm đi - điểm đến, remove text cấp Xã, Phường, Thị Trấn, Tỉnh, Thành Phố
  Cập nhật lại phân quyền TMSBillList

2. Scripts:

- server:migrate:run --script tms/MigratePartnerAccount.groovy --company beehph

### [R20250827]

1. Tasks: -[Chien]:
   Fix bugs BFSOne Cost template merge tính toán sai
   Cập nhật chức năng Check log TMSBillList kiểm tra thông tin khi thầu phụ input data
   Update chức năng focus/unfocus màn VehicleTripGoodsTrackingList

### [R20250826]

1. Tasks: -[Quan]: Check cost và truck tự động. -[Chien]:
   Fix bugs BFSOne Costing template
   Cập nhật biểu đồ báo cáo cho từng thầu phụ

### [R20250822]

1. Tasks: -[Quan]: Tạo App TMS Reconcile. - Thêm chức năng lấy dữ liệu BFSOnePartner khi tạo Vehicle Fleet. -[Chien]:
   -Thêm chức năng cảnh báo màn TMSBillList khi thầu phụ update thông tin container, xe, costing
   -Fix bugs BFSOne Costing template

2. Scripts:

- server:migrate:run --script tms/CreateAppTMSReconcile.groovy
- migrate:run --script tms/AlterTMSTable.groovy

### [R20250821]

1. Tasks: -[Quan]: Tạo tài khoản cho thầu phụ. -[Chien]: Update UIVendorBillList, thông báo zalo/mail cho PIC khi có thay đổi thông tin xe, cost trên Vendor bill
2. Scripts:

- server:migrate:run --script tms/MigrationAccountVendor.groovy --company beehph

### [R20250820]

1. Tasks: -[Quan]: - Chỉnh sửa chức năng tạo mới TMS Customer, TMS Agent, TMS Carrier lấy dữ liệu từ BFSOnePartner. - Làm màn hình hiển thị TMSBill cho Sale.

   -[Chien]:

   - Update VehicleTripGoodsTrackingList thêm cảnh báo khi CUS thay đổi thời gian kế hoạch so với ban đầu
   - Fix bugs TMSBillFee lỗi nhập chi phí, Verify hbl thêm điều kiện check số file
   - Update API BFSOne push thông tin xe và cost
   - Fix bugs VehicleTripGoodsTrackingList

2. Scripts:

- migrate:run --script tms/ChangeFieldName.groovy
- migrate:run --script tms/AlterTMSTable.groovy

### [R20250818]

1. -[Chien]:
   - Fix bugs VehicleTripGoodsTrackingList và template confirm thanh toán + thông tin xe khi merge chưa đóng hết file
   - Kết nối BFSOneAPI push thông tin xe.

### [R20250814]

1. -[Quan]: - Cập nhật TMSPartnerAddress, sử dụng goong API tính toán địa chỉ khi chưa có trên hệ thống - Thêm cột InvoiceInformation lưu thông tin thanh toán theo yêu cầu BEEHCM màn VehicleTripGoodsTrackingList -[Chien]: - Fix bugs Sync bill từ BFSOne - Dựng API BFSOne push cost và thông tin xe

### [R20250813]

1. -[Quan]: - Migrate loginId sang accountId của các entity dùng bên TMS. - Thêm 1 cột hiển thị biểu tượng ngôi sao để thêm/xóa record Goods Tracking vào danh sách FOCUS. -[Chien]:

   - Cập nhật chức năng màn hình VehicleTripGoodsTrackingList:
     - Điều chỉnh input nhóm cột doanh thu/chi phí
     - Cho phép quản lý phân bổ chủ động các bill tracking có các điều vận
     - Cột RefFileNo khi kết thúc input tự động check các file trùng lặp, show các file trùng lặp nếu có

2. Script:

- migrate:run --script tms/MigrationLoginIdField.groovy

### [R20250811]

1. -[Quan]: - Sử dụng goong API tính toán địa chỉ - chuẩn hóa địa chỉ khi input điểm giao/nhận hàng TMSBillList
2. -[Chien]:
   - Viết báo cáo tuyến đường theo ngày/tháng theo thầu phụ, config target chuyến cho từng thầu
   - Cảnh báo trên báo cáo các thầu phụ ko đạt target

### [R20250807]

1. Tasks: -[Quan]: - Migrate và remove 2 field owner_login_id, owner_login_label bảng lgc_fleet_vehicle_fleet. - Init dữ liệu kho cảng cho BeeHCM.

2. -[Chien]:

- Update bảng VehicleTripGoodsTrackingList input phí lift on lift off và thêm trường phân loại bill request điều vận xử lý thông tin xe.
- Fix bugs kiểm tra thông tin bill khi export template BFSOne.

2. Script:

- migrate:run --script tms/MigrationVehicleGoodsTracking.groovy
- migrate:run --script tms/MigrationVehicleFleet.groovy
- server:migrate:run --script tms/MigrateAddressPortWarehouse.groovy

### [R20250805]

- [Quan]: - Remove join location, account tại các query TMS và thực hiện load các thông tin location, account tại hàm search. - [Quan]: Fix bug search TMS Partner Address.
- [Chien]: - Fix bugs input VehicleTripGoodsTrackingList - Fix bugs webhook clientContext không nhận company id

### [R20250804]

- [Chien]: - Thêm cột và cập nhật giao diện cho VehicleTripGoodsTrackingList - Thêm chức năng gửi mail và in biên bản cho VehicleTripGoodsTrackingList

### [R20250731]

- [Chien]: - Cập nhật VehicleTripGoodsTracking chia cung đường - Cập nhật TMSBill cho phép đánh dấu các chuyến chạy ghép và thêm báo cáo tổng file ghép chuyến

### [R20250727]

1. Tasks:

- [Quan]: - Lưu lại người export excel fee template và vehicle info template - Report tuyến đường dạng bảng
- [Chien]: - Cập nhật, fix bugs tms house-bill

### [R20250724]

1. Tasks:

- [Chien] - Cập nhật biểu đồ báo cáo tuyến đường TMSBill - Fix bugs BotEvent không hoạt động(Ko gửi tin nhắn zalo/mail)

### [R20250721]

### [R20250717]

1. Tasks:

- [Quan]:
  - Implement TMSConfigRoute, Zone, ZoneMapping.
  - Fix bug Vehicle Info Template.

### [R20250718]

1. Tasks:

- [Quan]: - Cập nhật địa chỉ kho cho tmsbill
- [Chien]:
  - Cập nhật báo cáo biểu diễn trên biểu đồ số chuyến theo tuyến đường cho từng thầu phụ dựa trên tms-bill
  - Config target số chuyến cho thầu phụ theo tuần/tháng và biểu diễn trên đồ thị

2. Script:

- migrate:run --script tms/DropTable.groovy
- server:migrate:run --company beehph --script tms/InitConfigRoute.groovy
- server:migrate:run --script tms/MigrationBillInvAddress.groovy

### [R20250716]

1. Tasks:

- [Quan] - Implement TMSConfigRoute, Zone, ZoneMapping.
- [Chien] - Câp nhật Zone, Zone Mapping Entity - Fix bugs BFSOne template

2. Script

- migrate:run --script tms/DropZoneTable.groovy
- server:migrate:run --script tms/MigrationLocation.groovy

### [R20250714]

1. Tasks:

- [Quan] - Cập nhập địa chỉ mới cho location kho/bãi
- [Chien] - Câp nhật Zone, Zone Mapping Entity - Fix bugs BFSOne template

2. Script

- migrate:run --script tms/DropZoneTable.groovy
- server:migrate:run --script tms/MigrationLocation.groovy

### [R20250714]

1. Tasks:

- [Chien] - Fix bugs TMSBillList - Màn TMSBillList thêm chức năng import/export file JSON để xử lỗi khi người dùng gặp các vấn đề lưu không thành công

### [R20250710]

1. Tasks:

- [Chien] - Cập nhật màn hình TMSVendorBill, tối ưu nhập thông tin

- [Quan] - Thêm màn hình TMSPartnerAddressList tổng hợp địa chỉ config của khách hàng. - Init dữ liêu thầu phụ cho văn phòng HCM
- [Chien] - Cập nhật dữ liệu location, subdistrict của Việt Nam sau sấp nhập.
  - Dữ liệu location, subdistrict cũ để về chế độ INACTIVE
  - Init location, subdistrict theo dữ liệu nguồn mới nhất lấy từ trang:
    https://thuvienphapluat.vn/phap-luat/ho-tro-phap-luat/danh-sach-3321-xa-phuong-dac-khu-chinh-thuc-cua-34-tinh-thanh-viet-nam-tra-cuu-3321-xa-phuong-dac-k-726625-221776.html
  - Điều chỉnh màn TMSParnerAddress, cảnh báo các địa chỉ cũ liên kết đến các location đã INACTIVE
  - Màn TMSBillList cảnh báo các địa chỉ giao/nhận của khách hàng đã là địa chỉ cũ khi người dùng input theo dữ liệu nguồn TMSPartnerAddress

2. Migrate script:

- server:migrate:run --script tms/MigrationHCMVehicleInfo.groovy --company beehcm
- server:migrate:run --script location/InitSubdistric.groovy

- server:migrate:run --script tms/MigrationHCMVehicleInfo.groovy --company beehcm

### [R20250707]

1. Tasks:

- [Quan] - Thêm màn InvoiceReconcileList vào module Vendor Bill, phân quyền cho thầu phụ. Tại màn InvoiceReconcileList của cus thêm nút hiển thị danh sách các InvoiceReconcile được tạo từ thầu phụ mà chưa có cus nào nhận. Thêm nút confirm để cus nhận yêu cầu thanh toán từ thầu phụ.

- [Chien] Cập nhật TMSHouseBill : Cho phép mở file mới bằng cách kéo dữ liệu từ BFSOne về TMS hoặc copy các lô đã tạo
- [Chien] Đồng bộ các chức năng hiện có trên TMSBill sang TMSHouseBill

2. Migrate script:
