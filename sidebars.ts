import type { SidebarsConfig } from '@docusaurus/plugin-content-docs';
const sidebars: SidebarsConfig = {
  userSidebar: [
    {
      type: 'link',
      label: 'System',
      href: '/docs/shared/user/system',
    },
    {
      type: 'link',
      label: 'Car/Meeting Schedule',
      href: '/docs/shared/user/asset',
    },
    {
      type: 'category',
      label: 'Projects',
      items: [
        {
          type: 'link',
          label: 'Task Request',
          href: '/docs/datatp-project/user/task-request',
        },
      ]
    },
    {
      type: 'category',
      label: 'CRM',
      items: [
        {
          type: 'link',
          label: 'Overview',
          href: '/docs/datatp-crm/user/intro',
        },
        {
          type: 'link',
          label: 'Pricing Tools',
          href: '/docs/datatp-crm/user/pricing-tools',
        },
        {
          type: 'link',
          label: 'CRM',
          href: '/docs/datatp-crm/user/crm/overview',
        },
        {
          type: 'link',
          label: 'Dashboard',
          href: '/docs/datatp-crm/user/crm/dashboard',
        }
      ]
    },
    {
      type: 'category',
      label: 'HRM',
      items: [
        {
          type: 'link',
          label: 'OKR',
          href: '/docs/datatp-hr/user/okr',
        },
        {
          type: 'link',
          label: 'KPI',
          href: '/docs/datatp-hr/user/kpi',
        },
      ]
    },
    {
      type: 'category',
      label: 'TMS',
      items: [
        {
          type: 'link',
          label: 'Vendor App',
          href: '/docs/datatp-tms/user/vendor-bill',
        },
        {
          type: 'link',
          label: 'TMS Bill',
          href: '/docs/datatp-tms/user/tms-bill',
        },
        {
          type: 'link',
          label: 'Fleet',
          href: '/docs/datatp-tms/user/fleet',
        },
      ]
    },

  ],

  developerSidebar: [
    {
      type: 'link',
      label: 'Setup',
      href: '/docs/shared/developer/SETUP',
    },
    {
      type: 'link',
      label: 'Changelog',
      href: '/docs/shared/developer/CHANGELOG',
    },
    {
      type: 'link',
      label: 'Backlog',
      href: '/docs/shared/developer/BACKLOG',
    },
    {
      type: 'category',
      label: 'CRM',
      items: [
        {
          type: 'autogenerated',
          dirName: 'datatp-crm/developer',
        }
      ]
    },
    {
      type: 'category',
      label: 'Document IE',
      items: [
        {
          type: 'autogenerated',
          dirName: 'document-ie/developer',
        }
      ]
    },
    {
      type: 'category',
      label: 'HRM',
      items: [
        {
          type: 'autogenerated',
          dirName: 'datatp-hr/developer',
        }
      ]
    },
  ],

};
export default sidebars;
